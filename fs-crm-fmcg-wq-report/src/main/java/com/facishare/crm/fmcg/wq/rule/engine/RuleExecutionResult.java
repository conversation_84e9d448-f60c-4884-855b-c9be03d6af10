package com.facishare.crm.fmcg.wq.rule.engine;

import com.facishare.crm.fmcg.wq.constants.DataReport2PublicFieldsConstants;
import com.facishare.crm.fmcg.wq.rule.template.RuleTemplate;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 规则执行结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleExecutionResult {

    /**
     * 规则ID
     */
    private String ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 是否执行成功
     */
    private boolean success;

    /**
     * 执行结果消息
     */
    private String message;

    /**
     * 条件执行结果列表
     */
    private List<ConditionExecutionResult> conditionResults;

    /**
     * 执行时间（毫秒）
     */
    private long executionTime;

    /**
     * 标准数据（标准对象的数据）
     */
    private IObjectData standardData;
    /**
     * 标准主数据
     */
    private IObjectData standardDataMain;

    /**
     * 报告数据（聚合后的数据）
     */
    private IObjectData reportData;

    /**
     * 过滤后的数据列表
     */
    private List<IObjectData> filteredDataList;

    /**
     * 匹配后的数据列表
     */
    private List<IObjectData> matchedDataList;

    /**
     * 是否陈列
     */
    private Boolean isDisplay;
    public boolean hasDisplay(){
        //if filterDataList is not empty, return true
        if(filteredDataList!=null && !filteredDataList.isEmpty()){
            return true;
        }
        return isDisplay!=null && isDisplay;
    }

//
//    /**
//     * 达成规则模板
//     */
//   private RuleTemplate achievementRuleTemplate;

    /**
     * 获取标准数据的特定字段值
     * 
     * @param fieldName 字段名
     * @return 字段值
     */
    public Object getStandardField(String fieldName) {
        return standardData != null ? standardData.get(fieldName) : null;
    }

    /**
     * 获取报告数据的特定字段值
     * 
     * @param fieldName 字段名
     * @return 字段值
     */
    public Object getReportField(String fieldName) {
        return reportData != null ? reportData.get(fieldName) : null;
    }
    /**
     * 是否是有子条件
     */
    public boolean hasSubConditions() {
        return !Optional.ofNullable(standardData)
                .map(data -> data.get(DataReport2PublicFieldsConstants.SUB_CONDITIONS, List.class))
                .orElse(Collections.emptyList())
                .isEmpty();
    }
}