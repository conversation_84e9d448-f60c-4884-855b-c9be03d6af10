package com.facishare.crm.fmcg.wq.rule.examples;

import com.facishare.crm.fmcg.wq.constants.ProductItemStandardDetailFields;
import com.facishare.crm.fmcg.wq.rule.generator.RuleMappingFactory;
import com.facishare.crm.fmcg.wq.rule.generator.StandardRuleConfigAdapter;
import com.facishare.crm.fmcg.wq.rule.model.mapping.BaseRuleTemplateConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantRuleRuntimeConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantStandardRuleTemplate;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.FieldMappingConfigField;
import com.facishare.crm.fmcg.wq.rule.service.RuleMappingService;
import com.facishare.crm.fmcg.wq.rule.util.RuleMappingValidator;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Example class demonstrating how to use the rule mapping system
 */
@Slf4j
@Component
public class RuleMappingExample {

    @Autowired
    private RuleMappingFactory ruleMappingFactory;
    
    @Autowired
    private RuleMappingService ruleMappingService;
    
    @Autowired
    private StandardRuleConfigAdapter standardRuleConfigAdapter;

    /**
     * Run all examples
     */
    public void runExamples(String tenantId) {
        log.info("Starting rule mapping examples");
        
        // Example 1: Creating a standard rule mapping
        TenantRuleRuntimeConfig standardMapping = createStandardRuleMapping(tenantId);
        if (standardMapping != null) {
            log.info("Example 1: Standard rule mapping created successfully");
        }
        
        // Example 2: Creating a custom rule mapping
        TenantRuleRuntimeConfig customMapping = createCustomRuleMapping(tenantId);
        if (customMapping != null) {
            log.info("Example 2: Custom rule mapping created successfully");
        }
        
        // Example 3: Converting data with rule mapping
        IObjectData standardData = convertDataExample(standardMapping);
        if (standardData != null) {
            log.info("Example 3: Data converted successfully");
            log.info("Converted data: {}", standardData);
        }
        
        // Example 4: Validation example
        boolean validationResult = validateRuleMappingExample(standardMapping);
        log.info("Example 4: Validation result: {}", validationResult);
        
        // Example 5: Generating field mappings from rule config
        List<FieldMappingConfigField> fieldMappings = generateFieldMappingsExample(tenantId);
        if (fieldMappings != null) {
            log.info("Example 5: Field mappings generated successfully");
            log.info("Number of field mappings: {}", fieldMappings.size());
        }
        
        log.info("Rule mapping examples completed");
    }
    
    /**
     * Example 1: Creating a standard rule mapping
     */
    private TenantRuleRuntimeConfig createStandardRuleMapping(String tenantId) {
        try {
            // Create a rule mapping for product items
            String sourceObjectApiName = "custom_product";
            TenantRuleRuntimeConfig mapping = ruleMappingFactory.createProductItemStandardRuleMapping(tenantId,sourceObjectApiName);
            
            log.info("Created standard rule mapping: source={}, standard={}, fields={}",
                    mapping.getSourceObjectApiName(),
                    mapping.getStandardObjectApiName(),
                    mapping.getFieldMappings().size());
            
            return mapping;
        } catch (Exception e) {
            log.error("Error creating standard rule mapping", e);
            return null;
        }
    }
    
    /**
     * Example 2: Creating a custom rule mapping
     */
    private TenantRuleRuntimeConfig createCustomRuleMapping(String tenantId) {
        try {
            // Define custom field mappings
            Map<String, String> fieldMappings = new HashMap<>();
            fieldMappings.put("name", "product_name");
            fieldMappings.put("sku", "product_code");
            fieldMappings.put("price", "unit_price");
            fieldMappings.put("quantity", "stock_quantity");
            
            // Create a custom rule mapping
            String sourceObjectApiName = "external_product";
            String standardObjectApiName = ProductItemStandardDetailFields.API_NAME;
            
            TenantRuleRuntimeConfig mapping = ruleMappingService.createCustomRuleMapping(
                tenantId,
                sourceObjectApiName,
                standardObjectApiName,
                fieldMappings
            );
            
            log.info("Created custom rule mapping: source={}, standard={}, fields={}",
                    mapping.getSourceObjectApiName(),
                    mapping.getStandardObjectApiName(),
                    mapping.getFieldMappings().size());
            
            return mapping;
        } catch (Exception e) {
            log.error("Error creating custom rule mapping", e);
            return null;
        }
    }
    
    /**
     * Example 3: Converting data with rule mapping
     */
    private IObjectData convertDataExample(TenantRuleRuntimeConfig mapping) {
        try {
            // Create sample source data
            IObjectData sourceData = new ObjectData();
            sourceData.set("product_name", "Test Product");
            sourceData.set("product_code", "TP-001");
            sourceData.set("unit_price", new BigDecimal("99.99"));
            sourceData.set("stock_quantity", 100);
            sourceData.set("is_active", true);
            sourceData.setId("1234567890");
            Map<String,IObjectData> iObjectDataMap = new HashMap<>();
            iObjectDataMap.put(sourceData.getId(), sourceData);
            // Convert data using the mapping
            // List<IObjectData> standardData = ruleMappingService.convertToStandardFormat(
            //         User.systemUser(tenantId),
            //         iObjectDataMap
            // );
            
            // return standardData.get(0);
            return null;
        } catch (Exception e) {
            log.error("Error converting data", e);
            return null;
        }
    }
    
    /**
     * Example 4: Validation example
     */
    private boolean validateRuleMappingExample(TenantRuleRuntimeConfig mapping) {
        try {
            // Validate the mapping
            RuleMappingValidator.ValidationResult result = RuleMappingValidator.validateMapping(mapping);
            
            if (!result.isValid()) {
                log.error("Validation errors: {}", result.getErrors());
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("Error validating rule mapping", e);
            return false;
        }
    }
    
    /**
     * Example 5: Generating field mappings from rule config
     */
    private List<FieldMappingConfigField> generateFieldMappingsExample(String tenantId) {
        try {
            // Get a standard rule configuration
            BaseRuleTemplateConfig ruleConfig =
                standardRuleConfigAdapter.getBaseRuleTemplateConfigCopy(ProductItemStandardDetailFields.API_NAME);
            
            // Generate field mappings
            String sourceObjectApiName = "external_product";
            TenantStandardRuleTemplate tenantStandardRuleTemplate = standardRuleConfigAdapter.getTenantStandardRuleTemplate(tenantId, ruleConfig);
//            List<RuleFieldConditionConfig> fieldMappings =
//                standardRuleConfigAdapter.getRuleFieldsFromTenantStandardRuleTemplate(tenantStandardRuleTemplate,sourceObjectApiName);
            return null;
        } catch (Exception e) {
            log.error("Error generating field mappings", e);
            return null;
        }
    }
    
    /**
     * Example method for usage in application
     */
    public static void main(String[] args) {
        log.info("This is an example class. In a real application, this would be called by a Spring component.");
        log.info("Please refer to the README.md file for more information on how to use the rule mapping system.");
    }
} 