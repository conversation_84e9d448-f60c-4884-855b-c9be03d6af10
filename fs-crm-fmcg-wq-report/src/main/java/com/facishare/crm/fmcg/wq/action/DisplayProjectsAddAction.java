package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.rule.util.CheckStandardDetailArgsUtils;
import com.facishare.crm.fmcg.wq.rule.util.FieldMappingConfigManager;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DisplayProjectsAddAction extends StandardAddAction {
    FieldMappingConfigManager fieldMappingConfigManager = SpringUtil.getContext().getBean(FieldMappingConfigManager.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String tenantId = actionContext.getTenantId();
    }
}
