package com.facishare.crm.fmcg.wq.report.model;

import com.facishare.crm.fmcg.wq.constants.DisplayDistrAchSummaryFields;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/03/21/ 11:23
 **/
@AllArgsConstructor
@Getter
public enum ReportType {
    // 陈列
    DISPLAY("0",DisplayDistrAchSummaryFields.RECORD_DISPLAY,"陈列报告"),
    // 铺货
    DISTRIBUTION("1",DisplayDistrAchSummaryFields.RECORD_DISTRIBUTION,"铺货报告"),
    // 举证
    ACTIVITY("2", DisplayDistrAchSummaryFields.RECORD_ACTIVITY,"活动举证");

    public final String code;
    public final String record;
    public final String desc;
}
