package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.DisplayTypeStandardsFields;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeFields;
import com.facishare.crm.fmcg.wq.dao.DataReportStandardDao;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class SuccessfulStoreRangeWebDetailController extends StandardWebDetailController {

    private static final Logger logger = LoggerFactory.getLogger(SuccessfulStoreRangeWebDetailController.class);
   //DataReportStandardDao
   private DataReportStandardDao dataReportStandardDao = SpringUtil.getContext().getBean(DataReportStandardDao.class);


    @Override
    protected Result after(Arg arg, Result result) {
        //修改结果 根据从对象填充 对象list
        result = super.after(arg, result);
        //取从对象陈列形式标准 DisplayTypeStandardsFields 里的陈列形式聚合
        //todo 暂时去掉 因为可能超过20 后期加回来需要给简爱 开灰度
//        List<String> displayTypeStandardsIds = Optional.ofNullable(result.getData().get(SuccessfulStoreRangeFields.DISPLAY_TYPE))
//                .filter(o -> o != null)
//                .map(o -> ((List<String>) o))
//                .orElse(Lists.newArrayList());
//        //查询从对象的陈列形式标准 DisplayTypeStandardsFields 里的陈列形式聚合
//        Map<String, Map<String, IObjectData>> mainAndDetailObjectData = dataReportStandardDao.getMainAndDetailObjectData(controllerContext.getUser(), SuccessfulStoreRangeFields.API_NAME, arg.getObjectDataId());
//        if (MapUtils.isNotEmpty(mainAndDetailObjectData)) {
//            //取从对象的陈列形式标准 DisplayTypeStandardsFields 里的陈列形式聚合
//            Map<String, IObjectData> detailObjectData = mainAndDetailObjectData.get(DisplayTypeStandardsFields.API_NAME);
//            if (MapUtils.isNotEmpty(detailObjectData)) {
//                //取从对象的陈列形式标准 DisplayTypeStandardsFields 里的陈列形式聚合
//                for (Map.Entry<String, IObjectData> entry : detailObjectData.entrySet()) {
//                    IObjectData detail = entry.getValue();
//                    Optional<String> displayForm = Optional.ofNullable(detail.get(DisplayTypeStandardsFields.DISPLAY_FORM)).map(o -> String.valueOf(o));
//                    if (displayForm.isPresent()) {
//                        displayTypeStandardsIds.add(displayForm.get());
//                    }
//                }
//            }
//        }
//        //重新赋值
//        result.getData().put(SuccessfulStoreRangeFields.DISPLAY_TYPE, displayTypeStandardsIds.stream().distinct().collect(Collectors.toList()));
        return result;
    }

}
