package com.facishare.crm.fmcg.wq.report.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;

import java.util.*;
import java.util.stream.Collectors;

import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.crm.fmcg.wq.constants.DistributionProductsAchievedFields;
import com.facishare.crm.fmcg.wq.dao.DataReportStandardDao;
import com.facishare.crm.fmcg.wq.report.model.DisplayReportDetail;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 * @Date 2025/03/27/ 16:55
 **/
@Slf4j
@Service
public class DistributionReportDetailService {

    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected DataReportStandardDao dataReportStandardDao;

    public List<DisplayReportDetail.DistributionInfo> buildReportData(User systemUser, String reportSummaryId) {
        List<DisplayReportDetail.DistributionInfo> result = Lists.newArrayList();
        IObjectDescribe describe = serviceFacade.findObject(systemUser.getTenantId(),
                DistributionProductsAchievedFields.API_NAME);
        List<IObjectData> mustDistributionList = getDistributionList(systemUser, reportSummaryId, describe);
        Map<String, String> distributionColorMap = buildDistributionColorMap(describe);
        if (CollectionUtils.isEmpty(mustDistributionList)) {
            return result;
        }
        // 根据陈列项目分组
        Map<String, List<IObjectData>> distributionMap = Maps.newHashMap();
        List<IObjectData> noDisplayFormatList = Lists.newArrayList();
        for (IObjectData item : mustDistributionList) {
            String displayFormat = (String) item.get(DistributionProductsAchievedFields.DISPLAY_FORMAT);
            if (StringUtils.isNotEmpty(displayFormat)) {
                distributionMap.computeIfAbsent(displayFormat, k -> Lists.newArrayList()).add(item);
            } else {
                noDisplayFormatList.add(item);
            }
        }
        distributionMap.forEach((key, value) -> {
            if(CollectionUtils.isNotEmpty(value)) {
                result.add(buildDistributionInfo(value, distributionColorMap));
            }
        });
        if(CollectionUtils.isNotEmpty(noDisplayFormatList)) {
            result.add(buildDistributionInfo(noDisplayFormatList, distributionColorMap));
        }
        return result;
    }

    private DisplayReportDetail.DistributionInfo buildDistributionInfo(List<IObjectData> items,Map<String, String> distributionColorMap) {
        if(CollectionUtils.isEmpty(items)){
            return null;
        }
        DisplayReportDetail.DistributionInfo distributionInfo = new DisplayReportDetail.DistributionInfo();
        distributionInfo.setDisplayName((String) items.get(0).get(DistributionProductsAchievedFields.DISPLAY_FORMAT_NAME));
        distributionInfo.setProductCategoryInfos(buildProductCategoryInfos(items, distributionColorMap));
        return distributionInfo;
    }

    private List<DisplayReportDetail.ProductCategoryInfo> buildProductCategoryInfos(List<IObjectData> items,
            Map<String, String> distributionColorMap) {
        List<DisplayReportDetail.ProductCategoryInfo> productCategoryInfos = Lists.newArrayList();
        items.forEach(item -> {
            DisplayReportDetail.ProductCategoryInfo productCategoryInfo = new DisplayReportDetail.ProductCategoryInfo();
            // 产品分类id
            String productCategoryId = item.get(DistributionProductsAchievedFields.PRODUCT_CATEGORY, String.class);
            // 产品分类ids
            List<String> productCategoryIds = item.get(DistributionProductsAchievedFields.PRODUCT_CATEGORIES, List.class);
            //赋值 名称 优先 id 在 ids名称
            if (CollectionUtils.isNotEmpty(productCategoryIds)) {
                String categoryName = item.get(DistributionProductsAchievedFields.PRODUCT_CATEGORIES_NAME, String.class);
                //[{"name":"化妆品","_id":"61c95c515e781a000103d82b"},{"name":"饮品","_id":"61c95c515e781a000103d82a"}]
                if (StringUtils.isNotEmpty(categoryName)) {
                    categoryName = JSON.parseArray(categoryName).stream().map(o -> (String) ((Map) o).get("name")).collect(Collectors.joining(","));
                }
                productCategoryInfo.setCategoryName(categoryName);
            }else  if (StringUtils.isNotEmpty(productCategoryId)) {
                productCategoryInfo.setCategoryName(item.get(DistributionProductsAchievedFields.PRODUCT_CATEGORY_NAME, String.class));
            }
            productCategoryInfo.setCategoryIds(productCategoryIds);
            String ruleDesc = "必售"; //ignoreI18n
            List<String> standardList = (List<String>) item.get(DistributionProductsAchievedFields.REQUIRED_STANDARD);
            if (CollectionUtils.isNotEmpty(standardList) && standardList.size() > 1) {
                String standardValue = (String) item.get(DistributionProductsAchievedFields.STANDARD_VALUE);
                if (StringUtils.isNotEmpty(standardValue)) {
                    ruleDesc = standardList.size() + "选" + standardValue + ruleDesc; //ignoreI18n
                }
            }
            productCategoryInfo.setDistributionRuleDesc(ruleDesc);
            productCategoryInfo.setDistributionMsg((String) item.get(DistributionProductsAchievedFields.STATUS_NAME));
            if (StringUtils.isNotEmpty(productCategoryInfo.getDistributionMsg())) {
                productCategoryInfo.setDistributionColor(distributionColorMap.get(productCategoryInfo.getDistributionMsg()));
                if(DistributionProductsAchievedFields.STATUS_Options_0.equals(item.get(DistributionProductsAchievedFields.STATUS))){
                    productCategoryInfo.setIsFillCurrentRow(1);
                }
            }
            productCategoryInfo.setProductInfos(buildProductInfos(item));
            productCategoryInfos.add(productCategoryInfo);
        });
        return productCategoryInfos;
    }

    private List<DisplayReportDetail.ProductInfo> buildProductInfos(IObjectData item) {

        List<DisplayReportDetail.ProductInfo> productInfos = Lists.newArrayList();
        // 获取实际产品
        List<String> actualProducts = (List<String>) item.get(DistributionProductsAchievedFields.ACTUAL_STANDARD);
        List<String> actualIdList = Optional.ofNullable(actualProducts).orElse(Lists.newArrayList());

        // 获取要求产品
        List<HashMap> requiredProducts = (List<HashMap>) item.get(DistributionProductsAchievedFields.REQUIRED_STANDARD_NAME);
        Map<String, String> requiredNameMap = Optional.ofNullable(requiredProducts).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(k1 -> (String) k1.get("_id"), k2 -> (String) k2.get("name")));
        requiredNameMap.forEach((k, v) -> {
            DisplayReportDetail.ProductInfo productInfo = new DisplayReportDetail.ProductInfo();
            productInfo.setProductId(k);
            productInfo.setProductName(v);
            productInfo.setIsDistribution(actualIdList.contains(k) ? 1 : 0);
            productInfos.add(productInfo);
        });
        return productInfos;
    }

    private List<IObjectData> getDistributionList(User systemUser, String reportSummaryId, IObjectDescribe describe) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(DistributionProductsAchievedFields.RELATED_REPORT, reportSummaryId)
                .build();
        List<IObjectData> resultList = dataReportStandardDao.getAllIObjectDataListByQuery(systemUser, searchQuery,
                DistributionProductsAchievedFields.API_NAME);
        if (CollectionUtils.isNotEmpty(resultList)) {

            serviceFacade.fillSelectLabelInfo(describe, resultList);
            serviceFacade.fillObjectDataWithRefObject(describe, resultList, systemUser);
        }
        return resultList;
    }

    private Map<String, String> buildDistributionColorMap(IObjectDescribe describe) {
        return ObjectUtils.getValueAndColorByDesc(
                describe.getFieldDescribeMap().get(DistributionProductsAchievedFields.STATUS));
    }
}
