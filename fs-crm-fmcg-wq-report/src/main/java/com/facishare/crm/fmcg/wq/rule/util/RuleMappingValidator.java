package com.facishare.crm.fmcg.wq.rule.util;

import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantRuleRuntimeConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantStandardRuleTemplate;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.FieldMappingConfigField;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleConditionGroup;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleFieldConditionConfig;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Utility class for validating rule mapping configurations
 */
@Slf4j
public class RuleMappingValidator {

    /**
     * Validate a rule mapping configuration
     *
     * @param config Rule mapping configuration to validate
     * @return Validation result
     */
    public static ValidationResult validateMapping(TenantRuleRuntimeConfig config) {
        ValidationResult result = ValidationResult.builder()
                .valid(true)
                .build();

        List<String> errors = new ArrayList<>();

        // Check basic properties
        if (StringUtils.isBlank(config.getSourceObjectApiName())) {
            errors.add("Source object API name is required");
        }

        if (StringUtils.isBlank(config.getStandardObjectApiName())) {
            errors.add("Standard object API name is required");
        }

        // Check field mappings
        List<FieldMappingConfigField> fieldMappings = config.getFieldMappings();
        if (CollectionUtils.isEmpty(fieldMappings)) {
            errors.add("At least one field mapping is required");
        } else {
            Set<String> targetFieldNames = new HashSet<>();
            for (FieldMappingConfigField field : fieldMappings) {
                if (StringUtils.isBlank(field.getSourceField())) {
                    errors.add("Source field is required for field mapping: " + field.getTargetField());
                }
                if (StringUtils.isBlank(field.getTargetField())) {
                    errors.add("Target field is required for field mapping: " + field.getSourceField());
                }
                if (!targetFieldNames.add(field.getTargetField())) {
                    errors.add("Duplicate target field detected: " + field.getTargetField());
                }
            }
        }

        // Check rule generation config
        TenantStandardRuleTemplate ruleConfig = config.getTenantStandardRuleTemplate();
        if (ruleConfig == null) {
            errors.add("Rule generation config is required");
        } else {
            // Validate filter rules
            if (ruleConfig.getTenantFilterRules() != null) {
                validateConditionGroup(ruleConfig.getTenantFilterRules(), "filter rule", errors);
            }

            // Validate match rules
            if (ruleConfig.getTenantMatchRules() != null) {
                validateConditionGroup(ruleConfig.getTenantMatchRules(), "match rule", errors);
            }

            // Validate achievement rules
            if (ruleConfig.getTenantAchievementRules() != null) {
                validateConditionGroup(ruleConfig.getTenantAchievementRules(), "achievement rule", errors);
            }
        }
        Set<String> requiredFields = new HashSet<>();
        if (ruleConfig != null) {
            if (ruleConfig.getTenantFilterRules() != null) {
                requiredFields.addAll(extractFieldsFromConditionGroup(ruleConfig.getTenantFilterRules()));
            }
            if (ruleConfig.getTenantMatchRules() != null) {
                requiredFields.addAll(extractFieldsFromConditionGroup(ruleConfig.getTenantMatchRules()));
            }
            if (ruleConfig.getTenantAchievementRules() != null) {
                requiredFields.addAll(extractFieldsFromConditionGroup(ruleConfig.getTenantAchievementRules()));
            }
        }
        // Check if all required fields are present in the field mappings
        for (String field : requiredFields) {
            boolean found = false;
            for (FieldMappingConfigField mapping : fieldMappings) {
                if (field.equals(mapping.getSourceField()) || field.equals(mapping.getTargetField())) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                errors.add("Required field not found in field mappings: " + field);
            }
        }
        
        
        if (!errors.isEmpty()) {
            result.setValid(false);
            result.setErrors(errors);
        }

        return result;
    }
    /**
     * Extracts field names from a condition group, including all nested conditions and groups.
     *
     * @param group The condition group from which to extract field names.
     * @return A set of field names used in the condition group.
     */
    private static Set<String> extractFieldsFromConditionGroup(RuleConditionGroup group) {
        Set<String> fields = new HashSet<>();
        if (group != null) {
            // Extract fields from direct conditions
            if (CollectionUtils.isNotEmpty(group.getConditions())) {
                for (RuleFieldConditionConfig condition : group.getConditions()) {
                    if (StringUtils.isNotBlank(condition.getTargetField())) {
                        fields.add(condition.getTargetField());
                    }
                    if (StringUtils.isNotBlank(condition.getTargetField())) {
                        fields.add(condition.getTargetApiName());
                    }
                }
            }
            // Recursively extract fields from nested groups
            if (CollectionUtils.isNotEmpty(group.getSubGroups())) {
                for (RuleConditionGroup subGroup : group.getSubGroups()) {
                    fields.addAll(extractFieldsFromConditionGroup(subGroup));
                }
            }
        }
        return fields;
    }

    /**
     * Validate a condition group
     *
     * @param group Condition group to validate
     * @param context Context for error messages
     * @param errors List to add errors to
     */
    private static void validateConditionGroup(RuleConditionGroup group, String context, List<String> errors) {
        if (group == null) {
            errors.add("Condition group is required for " + context);
            return;
        }

        if (group.getOperator() == null) {
            errors.add("Logic operator is required for condition group in " + context);
        }

        if (CollectionUtils.isEmpty(group.getConditions()) && CollectionUtils.isEmpty(group.getSubGroups())) {
            errors.add("At least one condition or nested group is required in condition group for " + context);
        }

        // Validate nested conditions
        if (CollectionUtils.isNotEmpty(group.getConditions())) {
            for (RuleFieldConditionConfig condition : group.getConditions()) {
                validateFieldCondition(condition, context, errors);
            }
        }

        // Validate nested groups
        if (CollectionUtils.isNotEmpty(group.getSubGroups())) {
            for (RuleConditionGroup nestedGroup : group.getSubGroups()) {
                validateConditionGroup(nestedGroup, context, errors);
            }
        }
    }

    /**
     * Validate a field condition
     *
     * @param condition Field condition to validate
     * @param context Context for error messages
     * @param errors List to add errors to
     */
    private static void validateFieldCondition(RuleFieldConditionConfig condition, String context, List<String> errors) {
        if (condition == null) {
            errors.add("Field condition is required for " + context);
            return;
        }

        if (StringUtils.isBlank(condition.getTargetField())) {
            errors.add("Field is required for field condition in " + context);
        }

        if (StringUtils.isBlank(condition.getOperator())) {
            errors.add("Operator is required for field condition in " + context);
        }
    }

    /**
     * Validation result
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationResult {
        private boolean valid;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
    }
} 