package com.facishare.crm.fmcg.wq.rule.model.mapping.base;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Rule Condition Group
 * <p>
 * Defines a group of conditions that should be evaluated together.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleConditionGroup {
    /**
     * Logical operator to apply between conditions (AND/OR)
     */
    private LogicalOperator operator = LogicalOperator.AND;

    /**
     * List of field-based conditions
     */
    private List<RuleFieldConditionConfig> conditions;

    /**
     * Nested condition groups for complex logic
     */
    private List<RuleConditionGroup> subGroups;

    /**
     * Create a simple condition group with AND logic
     *
     * @param conditions List of conditions to include
     * @return The configured condition group
     */
    public static RuleConditionGroup createAndGroup(List<RuleFieldConditionConfig> conditions) {
        return RuleConditionGroup.builder()
                .operator(LogicalOperator.AND)
                .conditions(conditions)
                .build();
    }

    /**
     * Create a simple condition group with OR logic
     *
     * @param conditions List of conditions to include
     * @return The configured condition group
     */
    public static RuleConditionGroup createOrGroup(List<RuleFieldConditionConfig> conditions) {
        return RuleConditionGroup.builder()
                .operator(LogicalOperator.OR)
                .conditions(conditions)
                .build();
    }
} 