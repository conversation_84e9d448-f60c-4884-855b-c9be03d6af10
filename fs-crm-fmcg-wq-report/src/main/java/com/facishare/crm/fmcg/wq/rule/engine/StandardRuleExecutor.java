package com.facishare.crm.fmcg.wq.rule.engine;

import com.facishare.crm.fmcg.wq.rule.model.mapping.base.LogicalOperator;
import com.facishare.crm.fmcg.wq.rule.template.ConditionTemplate;
import com.facishare.crm.fmcg.wq.rule.template.RuleTemplate;
import com.facishare.crm.fmcg.wq.rule.template.impl.SimpleCondition;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * 标准规则执行器
 * 处理标准类型的规则模板执行
 */
@Slf4j
@Component
public class StandardRuleExecutor implements RuleExecutor {

    @Override
    public boolean supports(RuleTemplate template) {
        // 默认支持所有标准类型规则
        return true;
    }

    @Override
    public boolean evaluate(RuleTemplate template, IObjectData data) {
        if (template.getConditions() == null || template.getConditions().isEmpty()) {
            return true; // 没有条件意味着成功
        }
        
        if (LogicalOperator.AND == template.getLogicalOperator()) {
            return template.getConditions().stream()
                .allMatch(condition -> executeCondition(condition, data).isSuccess());
        } else {
            return template.getConditions().stream()
                .anyMatch(condition -> executeCondition(condition, data).isSuccess());
        }
    }
    
    /**
     * 执行单个条件
     */
    private ConditionExecutionResult executeCondition(ConditionTemplate condition, IObjectData data) {
        try {
            boolean result = condition.evaluate(data);
            return ConditionExecutionResult.builder()
                .conditionId(condition.getConditionId())
                .conditionName(condition.getConditionName())
                .success(result)
                .actualValue(extractActualValue(condition, data))
                .expectedValue(getConditionValue(condition))
                .build();
                
        } catch (Exception e) {
            log.error("Error executing condition: {}", condition.getConditionId(), e);
            return ConditionExecutionResult.builder()
                .conditionId(condition.getConditionId())
                .conditionName(condition.getConditionName())
                .success(false)
                .message(e.getMessage())
                .build();
        }
    }
    
    /**
     * 提取条件的实际值
     */
    private Object extractActualValue(ConditionTemplate condition, IObjectData data) {
        try {
            String field = getConditionField(condition);
            return field != null ? data.get(field) : null;
        } catch (Exception e) {
            log.error("Error extracting actual value for condition: {}", condition.getConditionId(), e);
            return null;
        }
    }
    
    /**
     * 获取条件的字段名
     */
    private String getConditionField(ConditionTemplate condition) {
        if (condition instanceof SimpleCondition) {
            return ((SimpleCondition) condition).getField();
        }
        return null;
    }
    
    /**
     * 获取条件的比较值
     */
    private Object getConditionValue(ConditionTemplate condition) {
        if (condition instanceof SimpleCondition) {
            return ((SimpleCondition) condition).getValue();
        }
        return null;
    }
    
    /**
     * 合并多个条件执行结果
     */
    protected ConditionExecutionResult mergeConditionResults(ConditionTemplate condition, 
                                                          List<ConditionExecutionResult> results) {
        if (results.isEmpty()) {
            return ConditionExecutionResult.builder()
                .conditionId(condition.getConditionId())
                .conditionName(condition.getConditionName())
                .success(false)
                .message("No data available for condition evaluation")
                .build();
        }
        
        // Count successes and failures
        long successCount = results.stream()
            .filter(ConditionExecutionResult::isSuccess)
            .count();
        
        // Merge actual values
        List<Object> actualValues = results.stream()
            .map(ConditionExecutionResult::getActualValue)
            .collect(Collectors.toList());
        
        // Merge error messages
        List<String> errorMessages = results.stream()
            .map(ConditionExecutionResult::getMessage)
            .filter(message -> message != null && !message.isEmpty())
            .collect(Collectors.toList());
        
        return ConditionExecutionResult.builder()
            .conditionId(condition.getConditionId())
            .conditionName(condition.getConditionName())
            .success(successCount > 0)
            .actualValue(actualValues)
            .expectedValue(getConditionValue(condition))
            .message(String.join("; ", errorMessages))
            .build();
    }
} 