package com.facishare.crm.fmcg.wq.api.success;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface AllDataList {
    @Data
    @ToString
    class Arg implements Serializable {

        @NotEmpty(message = "数据为空，请填写数据")
        private int maxSize;
    }

    @Data
    @ToString
    class Result implements Serializable {
        private List<Map<String,Object>> data ;
    }
}
