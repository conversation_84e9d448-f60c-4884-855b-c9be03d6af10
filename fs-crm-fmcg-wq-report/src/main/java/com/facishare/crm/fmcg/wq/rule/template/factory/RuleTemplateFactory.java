package com.facishare.crm.fmcg.wq.rule.template.factory;

import com.facishare.crm.fmcg.wq.rule.model.mapping.base.LogicalOperator;
import com.facishare.crm.fmcg.wq.rule.template.ConditionTemplate;
import com.facishare.crm.fmcg.wq.rule.template.RuleTemplate;
import com.facishare.crm.fmcg.wq.rule.template.impl.CompositeCondition;
import com.facishare.crm.fmcg.wq.rule.template.impl.SimpleCondition;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * 规则模板工厂
 * 负责创建和管理规则模板
 */
@Slf4j
@Component
public class RuleTemplateFactory {

    /**
     * 规则模板缓存
     */
    private final Map<String, RuleTemplate> templateCache = new ConcurrentHashMap<>();

    /**
     * 创建规则模板
     * @param templateId 模板ID
     * @param templateName 模板名称
     * @param description 描述
     * @param ruleType 规则类型
     * @return 规则模板
     */
    public RuleTemplate createTemplate(String templateId, String templateName, String description, 
                                        RuleTemplate.RuleType ruleType) {
        RuleTemplate template = new RuleTemplate();
        template.setTemplateId(templateId);
        template.setTemplateName(templateName);
        template.setDescription(description);
        template.setRuleType(ruleType);
        template.setConditions(new ArrayList<>());
        template.setLogicalOperator(LogicalOperator.AND);
        
        // 缓存模板
        templateCache.put(templateId, template);
        return template;
    }
    
    /**
     * 创建简单条件
     * @param field 目标字段
     * @param operator 操作符
     * @param value 比较值
     * @return 条件
     */
    public SimpleCondition createSimpleCondition(String field, String operator, Object value) {
        return SimpleCondition.builder()
                .conditionId(UUID.randomUUID().toString())
                .conditionName(field + " " + operator)
                .field(field)
                .operator(operator)
                .value(value)
                .conditionType("SIMPLE")
                .build();
    }
    
    /**
     * 创建组合条件
     * @param conditionName 条件名称
     * @param logicalOperator 逻辑运算符
     * @return 组合条件
     */
    public CompositeCondition createCompositeCondition(String conditionName, LogicalOperator logicalOperator) {
        return CompositeCondition.builder()
                .conditionId(UUID.randomUUID().toString())
                .conditionName(conditionName)
                .logicalOperator(logicalOperator)
                .build();
    }
    
    /**
     * 添加条件到规则模板
     * @param template 规则模板
     * @param condition 条件
     */
    public void addCondition(RuleTemplate template, ConditionTemplate condition) {
        if (template.getConditions() == null) {
            template.setConditions(new ArrayList<>());
        }
        template.getConditions().add(condition);
        
        // 更新缓存
        templateCache.put(template.getTemplateId(), template);
    }
    
    /**
     * 根据ID获取规则模板
     * @param templateId 模板ID
     * @return 规则模板
     */
    public RuleTemplate getTemplate(String templateId) {
        return templateCache.get(templateId);
    }
    
    /**
     * 删除规则模板
     * @param templateId 模板ID
     */
    public void removeTemplate(String templateId) {
        templateCache.remove(templateId);
    }
    
    /**
     * 获取所有规则模板
     * @return 规则模板列表
     */
    public List<RuleTemplate> getAllTemplates() {
        return new ArrayList<>(templateCache.values());
    }
} 