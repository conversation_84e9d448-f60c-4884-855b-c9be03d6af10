package com.facishare.crm.fmcg.wq.rule.engine;

import com.facishare.crm.fmcg.wq.rule.template.RuleTemplate;
import com.facishare.paas.metadata.api.IObjectData;

/**
 * 规则执行器接口
 * 定义规则执行的标准接口，支持不同类型的规则执行策略
 */
public interface RuleExecutor {

    /**
     * 判断是否支持该类型的规则模板
     * @param template 规则模板
     * @return 是否支持
     */
    boolean supports(RuleTemplate template);
    
    /**
     * 评估规则模板
     * @param template 规则模板
     * @param data 待评估数据
     * @return 评估结果
     */
    boolean evaluate(RuleTemplate template, IObjectData data);
} 