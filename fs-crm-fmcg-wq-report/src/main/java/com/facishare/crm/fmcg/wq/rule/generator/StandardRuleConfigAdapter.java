package com.facishare.crm.fmcg.wq.rule.generator;

import com.facishare.crm.fmcg.wq.constants.DataReport2PublicFieldsConstants;
import com.facishare.crm.fmcg.wq.constants.DisplayFormFields;
import com.facishare.crm.fmcg.wq.constants.DisplayProjectsFields;
import com.facishare.crm.fmcg.wq.constants.MaterialStandardDetailsFields;
import com.facishare.crm.fmcg.wq.constants.MustDistributeProductsFields;
import com.facishare.crm.fmcg.wq.constants.ProductItemStandardDetailFields;
import com.facishare.crm.fmcg.wq.constants.ProjectStandardsFields;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.rule.model.mapping.BaseRuleTemplateConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantFieldCustomConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantRuleRuntimeConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantStandardRuleTemplate;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.AggregationConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.AggregationType;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.FieldMappingConfigField;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.LogicalOperator;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.MappingConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.MappingType;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleConditionGroup;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleFieldConditionConfig;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 */
@Slf4j
@Component
public class StandardRuleConfigAdapter {
    @Autowired
    private BaseDao baseDao;

//    @Autowired
//    private RuleGenerator ruleGenerator;

    // 存储标准对象API名称到规则生成配置的映射
    private Map<String, BaseRuleTemplateConfig> baseStandardRuleConfigMap;

    @PostConstruct
    public void init() {
        baseStandardRuleConfigMap = new HashMap<>();
        initBaseStandardRuleConfigs();
    }

    /**
     * 初始化标准规则配置
     */
    private void initBaseStandardRuleConfigs() {
        // 产品项目标准
        initProductItemStandardConfig();

        // 物料标准
        initMaterialStandardConfig();
        // 项目标准
        initProjectStandardsConfig();
        // 必分销品
        initDistributionProductConfig();
    }
    /**
     * 初始化项目标准配置
     */
    private void initProjectStandardsConfig() {
        String apiName = ProjectStandardsFields.API_NAME;
        // 创建规则生成配置
        BaseRuleTemplateConfig config = new BaseRuleTemplateConfig();
        config.setTemplateId(apiName);
        config.setPriority(1);
        config.setDescription("项目标准规则配置"); //ignoreI18n

        // 1.创建过滤规则组（添加层级和陈列形式条件）
        RuleConditionGroup filterRules = new RuleConditionGroup();
        filterRules.setOperator(LogicalOperator.AND);

        List<RuleFieldConditionConfig> filterConditions = new ArrayList<>();

        // 新增层级条件
        RuleFieldConditionConfig levelCondition = createPicklistCondition(
                apiName, DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, "=");
        levelCondition.setTargetFieldName("层级"); //ignoreI18n

        // 新增陈列形式条件
        RuleFieldConditionConfig displayFormatCondition = createLookupCondition(
                apiName, DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, "=", DisplayFormFields.API_NAME);
        displayFormatCondition.setTargetFieldName("陈列形式"); //ignoreI18n

        filterConditions.add(levelCondition);
        filterConditions.add(displayFormatCondition);
        filterRules.setConditions(filterConditions);
        config.setFilterRules(filterRules);

        // 2.创建匹配规则组（设置类型、项目类型条件）
        //无

        // 3.创建达成规则组（添加数量标准条件）
        RuleConditionGroup achievementRules = new RuleConditionGroup();
        achievementRules.setOperator(LogicalOperator.OR);

        List<RuleFieldConditionConfig> achievementConditions = new ArrayList<>();

        // 数量标准条件
        RuleFieldConditionConfig quantityCondition = createNumberCondition(
                apiName, DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY, ">=");
        quantityCondition.setTargetFieldName("数量"); //ignoreI18n

        // 创建映射配置
        MappingConfig keyMappingConfig =
                MappingConfig.createPrefixFieldKeyMapping(ProjectStandardsFields.QUANTITY, DisplayProjectsFields.API_NAME, null, null, null);
        quantityCondition.setKeyMappingConfig(keyMappingConfig);

        // 聚合规则 sum
        quantityCondition.setAggregationConfig(AggregationConfig.builder()
                .type(AggregationType.SUM).build());

        quantityCondition.setExpectValueField(Boolean.TRUE);
        achievementConditions.add(quantityCondition);
        achievementRules.setConditions(achievementConditions);
        config.setAchievementRules(achievementRules);
        // 冗余字段
        List<RuleFieldConditionConfig> onlyFormatFields = new ArrayList<>();
        // 陈列照片
        RuleFieldConditionConfig displayPhotoConfig = new RuleFieldConditionConfig();
        displayPhotoConfig.setTargetField(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO);
        displayPhotoConfig.setSourceFieldType(IFieldType.IMAGE);
        displayPhotoConfig.setTargetApiName(apiName);
        displayPhotoConfig.setTargetFieldName("照片"); //ignoreI18n
        onlyFormatFields.add(displayPhotoConfig);

        config.setOnlyFormatFields(onlyFormatFields);
        // 存储配置和必填字段
        baseStandardRuleConfigMap.put(config.getTemplateId(), config);
    }

    /**
     * 初始化产品项目标准配置
     *
     * @param projectIds 项目ID列表
     */
    private void initProductItemStandardConfig() {
        String apiName = ProductItemStandardDetailFields.API_NAME;
        // 创建规则生成配置
        BaseRuleTemplateConfig config = new BaseRuleTemplateConfig();
        config.setTemplateId(apiName);
        config.setPriority(1);
        config.setDescription("产品项目标准规则配置"); //ignoreI18n

        // 1.创建过滤规则组（添加层数和陈列条件）
        RuleConditionGroup filterRules = new RuleConditionGroup();
        filterRules.setOperator(LogicalOperator.AND);

        List<RuleFieldConditionConfig> filterConditions = new ArrayList<>();

        // 新增层数条件
        RuleFieldConditionConfig layerCondition = createNumberCondition(
                apiName, DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, "=");

        // 新增陈列形式条件
        RuleFieldConditionConfig displayCondition = createLookupCondition(
                apiName, DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, "=", DisplayFormFields.API_NAME);

        filterConditions.add(layerCondition);
        filterConditions.add(displayCondition);
        filterRules.setConditions(filterConditions);
        config.setFilterRules(filterRules);

        // 2.创建匹配规则组（保留产品和分类条件）
        RuleConditionGroup matchRules = new RuleConditionGroup();
        matchRules.setOperator(LogicalOperator.OR);

        List<RuleFieldConditionConfig> matchConditions = new ArrayList<>();
        // 产品名称条件
        RuleFieldConditionConfig productNameCondition = createLookupCondition(
                apiName, ProductItemStandardDetailFields.PRODUCT_NAME, "in", "ProductObj");
        productNameCondition.setTargetFieldName("产品"); //ignoreI18n
        // 产品分类条件
        RuleFieldConditionConfig productCatCondition = createLookupCondition(
                apiName, ProductItemStandardDetailFields.PRODUCT_CATEGORIZATION, "in", "ProductCategoryObj");
        productCatCondition.setTargetFieldName("产品分类"); //ignoreI18n

        matchConditions.add(productNameCondition);
        matchConditions.add(productCatCondition);
        matchRules.setConditions(matchConditions);
        config.setMatchRules(matchRules);

        // 3.创建达成规则组（添加项目标准条件）
        RuleConditionGroup achievementRules = new RuleConditionGroup();
        achievementRules.setOperator(LogicalOperator.OR);

        List<RuleFieldConditionConfig> achievementConditions = new ArrayList<>();
        // 项目标准条件
        // 项目标准条件是个list, 取项目数据 生成标准值
        RuleFieldConditionConfig projectStdCondition = createNumberCondition(
                apiName,  DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY, ">=");
        projectStdCondition.setTargetFieldName("数量"); //ignoreI18n
        MappingConfig keyMappingConfig =
                MappingConfig.createPrefixFieldKeyMapping(ProductItemStandardDetailFields.PROJECT_STANDARDS, DisplayProjectsFields.API_NAME, null, null,null);
        projectStdCondition.setKeyMappingConfig(keyMappingConfig);
        // 聚合规则 sum
        projectStdCondition.setAggregationConfig(AggregationConfig.builder()
                .type(AggregationType.SUM).build());
        projectStdCondition.setExpectValueField(Boolean.TRUE);
        achievementConditions.add(projectStdCondition);

        achievementRules.setConditions(achievementConditions);
        config.setAchievementRules(achievementRules);
        // 冗余字段
        List<RuleFieldConditionConfig> onlyFormatFields = new ArrayList<>();
        // 陈列照片
        RuleFieldConditionConfig displayPhotoConfig = new RuleFieldConditionConfig();
        displayPhotoConfig.setTargetField(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO);
        displayPhotoConfig.setSourceFieldType(IFieldType.IMAGE);
        displayPhotoConfig.setTargetApiName(apiName);
        displayPhotoConfig.setTargetFieldName("照片"); //ignoreI18n
        onlyFormatFields.add(displayPhotoConfig);

        config.setOnlyFormatFields(onlyFormatFields);

        // 存储配置和必填字段
        baseStandardRuleConfigMap.put(config.getTemplateId(), config);
    }

    /**
     * 初始化物料标准配置
     */
    private void initMaterialStandardConfig() {
        String apiName = MaterialStandardDetailsFields.API_NAME;

        // 创建规则生成配置
        BaseRuleTemplateConfig config = new BaseRuleTemplateConfig();
        config.setTemplateId(apiName);
        config.setPriority(1);
        config.setDescription("物料标准规则配置"); //ignoreI18n

        // 1.创建过滤规则组
        RuleConditionGroup filterRules = new RuleConditionGroup();
        filterRules.setOperator(LogicalOperator.AND);

        List<RuleFieldConditionConfig> filterConditions = new ArrayList<>();

        // 层数条件
        RuleFieldConditionConfig layerCondition = createNumberCondition(
                apiName, DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, "=");
        layerCondition.setTargetFieldName("层级"); //ignoreI18n
        filterConditions.add(layerCondition);

        // 陈列形式条件
        RuleFieldConditionConfig displayTypeCondition = createLookupCondition(
                apiName, DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, "=", DisplayFormFields.API_NAME);
        displayTypeCondition.setTargetFieldName("陈列形式"); //ignoreI18n
        filterConditions.add(displayTypeCondition);
        filterRules.setConditions(filterConditions);
        config.setFilterRules(filterRules);

        // 2.创建匹配规则组
        RuleConditionGroup matchRules = new RuleConditionGroup();
        matchRules.setOperator(LogicalOperator.OR);

        List<RuleFieldConditionConfig> matchConditions = new ArrayList<>();

        // 物料名称条件
        RuleFieldConditionConfig materialNameCondition = createLookupCondition(
                apiName, MaterialStandardDetailsFields.MATERIAL_NAMES, "in","MaterialObj");
        materialNameCondition.setTargetFieldName("物料"); //ignoreI18n
        matchConditions.add(materialNameCondition);
        // 物料分类条件
        RuleFieldConditionConfig materialCategoryCondition = createLookupCondition(
                apiName, MaterialStandardDetailsFields.MATERIAL_CATEGORY, "in", "MaterialObj");
        materialCategoryCondition.setTargetFieldName("物料分类"); //ignoreI18n
        matchConditions.add(materialCategoryCondition);
        matchRules.setConditions(matchConditions);
        config.setMatchRules(matchRules);

        // 3.创建达成规则组
        RuleConditionGroup achievementRules = new RuleConditionGroup();
        achievementRules.setOperator(LogicalOperator.OR);

        List<RuleFieldConditionConfig> achievementConditions = new ArrayList<>();

        // 物料数量条件
        RuleFieldConditionConfig materialQtyCondition = createNumberCondition(
                apiName,  DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY, ">=");
        materialQtyCondition.setTargetFieldName("物料数量"); //ignoreI18n

        // 添加映射配置
        MappingConfig keyMappingConfig = MappingConfig.createPrefixFieldKeyMapping(
                MaterialStandardDetailsFields.MATERIAL_QUANTITY, DisplayProjectsFields.API_NAME, null, null, null);
        materialQtyCondition.setKeyMappingConfig(keyMappingConfig);
        // 聚合规则 sum
        materialQtyCondition.setAggregationConfig(AggregationConfig.builder().type(AggregationType.SUM).build());
        materialQtyCondition.setExpectValueField(Boolean.TRUE);
        achievementConditions.add(materialQtyCondition);
        achievementRules.setConditions(achievementConditions);
        config.setAchievementRules(achievementRules);

        // 冗余字段
        List<RuleFieldConditionConfig> onlyFormatFields = new ArrayList<>();
        // 陈列照片
        RuleFieldConditionConfig displayPhotoConfig = new RuleFieldConditionConfig();
        displayPhotoConfig.setTargetField(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO);
        displayPhotoConfig.setSourceFieldType(IFieldType.IMAGE);
        displayPhotoConfig.setTargetApiName(apiName);
        displayPhotoConfig.setTargetFieldName("照片"); //ignoreI18n
        onlyFormatFields.add(displayPhotoConfig);

        config.setOnlyFormatFields(onlyFormatFields);

        // 存储配置
        baseStandardRuleConfigMap.put(config.getTemplateId(), config);
    }


    /**
     * 初始化必分销品配置
     */
    private void initDistributionProductConfig() {
        String apiName = MustDistributeProductsFields.API_NAME;

        // 创建规则生成配置
        BaseRuleTemplateConfig config = new BaseRuleTemplateConfig();
        config.setTemplateId(apiName);
        config.setPriority(1);
        config.setDescription("必分销品规则配置"); //ignoreI18n

        // 1.创建过滤规则组
        RuleConditionGroup filterRules = new RuleConditionGroup();
        filterRules.setOperator(LogicalOperator.AND);

        List<RuleFieldConditionConfig> filterConditions = new ArrayList<>();
        // 层数条件
        RuleFieldConditionConfig layerCondition = createNumberCondition(
                apiName, DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, "=");
        layerCondition.setTargetFieldName("层级"); //ignoreI18n
        filterConditions.add(layerCondition);

        // 陈列形式条件
        RuleFieldConditionConfig displayFormCondition = createLookupCondition(
                apiName, DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, "=", DisplayFormFields.API_NAME);
        displayFormCondition.setTargetFieldName("陈列形式"); //ignoreI18n
        filterConditions.add(displayFormCondition);
        filterRules.setConditions(filterConditions);
        config.setFilterRules(filterRules);

        // 2.创建匹配规则组
        RuleConditionGroup matchRules = new RuleConditionGroup();
        matchRules.setOperator(LogicalOperator.OR);

        List<RuleFieldConditionConfig> matchConditions = new ArrayList<>();
        // 产品条件
        RuleFieldConditionConfig productCondition = createLookupCondition(
                apiName, MustDistributeProductsFields.PRODUCT, "=", "ProductObj");
        productCondition.setTargetFieldName("产品"); //ignoreI18n
        matchConditions.add(productCondition);

        // 产品多选条件
        RuleFieldConditionConfig productMultiSelectCondition = createLookupCondition(
                apiName, MustDistributeProductsFields.PRODUCT_MULTIPLE, "in", "ProductObj");
        productMultiSelectCondition.setTargetFieldName("产品列表"); //ignoreI18n
        matchConditions.add(productMultiSelectCondition);

        // 产品分类条件
//        RuleFieldConditionConfig productCategoryCondition = createLookupCondition(
//                apiName, MustDistributeProductsFields.PRODUCT_CATEGORY, "in", "ProductCategoryObj");
//        productCategoryCondition.setTargetFieldName("产品分类列表"); //ignoreI18n
//        matchConditions.add(productCategoryCondition);

        matchRules.setConditions(matchConditions);
        config.setMatchRules(matchRules);

        // 3.创建达成规则组
        RuleConditionGroup achievementRules = new RuleConditionGroup();
        achievementRules.setOperator(LogicalOperator.OR);

        List<RuleFieldConditionConfig> achievementConditions = new ArrayList<>();

        // 最小上报产品条件
        RuleFieldConditionConfig minReportCondition = createLookupCondition(
                apiName, MustDistributeProductsFields.MIN_REPORT_PRODUCT, ">=", "ProductObj");
        minReportCondition.setTargetFieldName("最小上报SKU数"); //ignoreI18n
        minReportCondition.setExpectValueField(Boolean.TRUE);
        // 新建聚合规则
        minReportCondition.setAggregationConfig(AggregationConfig.builder().type(AggregationType.DISTINCT_COUNT).build());
        achievementConditions.add(minReportCondition);
        achievementRules.setConditions(achievementConditions);
        config.setAchievementRules(achievementRules);
        // 冗余字段
        List<RuleFieldConditionConfig> onlyFormatFields = new ArrayList<>();
        // 陈列照片
        RuleFieldConditionConfig displayPhotoConfig = new RuleFieldConditionConfig();
        displayPhotoConfig.setTargetField(DataReport2PublicFieldsConstants.DisplayTypeStandardsFields_Virtual_DISPLAY_PHOTO);
        displayPhotoConfig.setSourceFieldType(IFieldType.IMAGE);
        displayPhotoConfig.setTargetApiName(apiName);
        displayPhotoConfig.setTargetFieldName("照片"); //ignoreI18n
        onlyFormatFields.add(displayPhotoConfig);

        config.setOnlyFormatFields(onlyFormatFields);
        // 存储配置
        baseStandardRuleConfigMap.put(config.getTemplateId(), config);
    }


    /**
     * 创建Lookup类型的条件
     *
     * @param apiName   源API名称
     * @param field     字段名
     * @param targetApi 目标API名称
     * @return 字段条件配置
     */
    private RuleFieldConditionConfig createLookupCondition(String apiName, String field, String operator, String targetApi) {
        RuleFieldConditionConfig condition = new RuleFieldConditionConfig();
        condition.setTargetApiName(apiName);
        condition.setTargetField(field);
        condition.setOperator(operator);
        condition.setSourceLookUpApiName(targetApi);
        condition.setSourceFieldType(IFieldType.OBJECT_REFERENCE);
        return condition;
    }

    /**
     * 创建Number类型的条件
     *
     * @param apiName  API名称
     * @param field    字段名
     * @param operator 操作符
     * @return 字段条件配置
     */
    private RuleFieldConditionConfig createNumberCondition(String apiName, String field, String operator) {
        RuleFieldConditionConfig condition = new RuleFieldConditionConfig();
        condition.setTargetApiName(apiName);
        condition.setTargetField(field);
        condition.setOperator(operator);
        condition.setSourceFieldType(IFieldType.NUMBER);
        return condition;
    }

    /**
     * 创建Picklist类型的条件
     *
     * @param apiName  API名称
     * @param field    字段名
     * @param operator 操作符
     * @return 字段条件配置
     */
    private RuleFieldConditionConfig createPicklistCondition(String apiName, String field, String operator) {
        RuleFieldConditionConfig condition = new RuleFieldConditionConfig();
        condition.setTargetApiName(apiName);
        condition.setTargetField(field);
        condition.setOperator(operator);
        return condition;
    }

    /**
     * 创建Text类型的条件
     *
     * @param apiName  API名称
     * @param field    字段名
     * @param operator 操作符
     * @return 字段条件配置
     */
    private RuleFieldConditionConfig createTextCondition(String apiName, String field, String operator) {
        RuleFieldConditionConfig condition = new RuleFieldConditionConfig();
        condition.setTargetApiName(apiName);
        condition.setTargetField(field);
        condition.setOperator(operator);
        condition.setSourceFieldType(IFieldType.TEXT);
        return condition;
    }

    /**
     * Get a copy of the base standard rule configuration for a standard object
     *
     * @param standardObjectApiName Standard object API name
     * @return A copy of the base standard rule configuration
     */
    public BaseRuleTemplateConfig getBaseRuleTemplateConfigCopy(String standardObjectApiName) {
        BaseRuleTemplateConfig cachedConfig = baseStandardRuleConfigMap.get(standardObjectApiName);
        if (cachedConfig == null) {
            return null;
        }
        return cachedConfig.copy();
    }
    /**
     * 从基础规则配置创建租户特定的规则模板
     *
     * @param tenantId 租户ID
     * @param baseConfig 基础规则配置
     * @return 租户特定的规则模板
     */
    public TenantStandardRuleTemplate getTenantStandardRuleTemplate(String tenantId, BaseRuleTemplateConfig baseConfig) {
        return getTenantStandardRuleTemplate(tenantId, baseConfig, null);
    }

    /**
     * 从基础规则配置创建租户特定的规则模板
     *
     * @param tenantId 租户ID
     * @param baseConfig 基础规则配置
     * @param dataListMap 预先查询的数据列表映射，用于替代实时数据库查询
     * @return 租户特定的规则模板
     */
    public TenantStandardRuleTemplate getTenantStandardRuleTemplate(String tenantId, BaseRuleTemplateConfig baseConfig, Map<String, List<Map<String, Object>>> dataListMap) {
        if (baseConfig == null) {
            return null;
        }

        // Create base tenant template
        TenantStandardRuleTemplate tenantTemplate = TenantStandardRuleTemplate.builder()
                .tenantId(tenantId)
                .baseConfig(baseConfig)
                .build();

        // Process filter rules with fields that require data from the database
        if (baseConfig.getFilterRules() != null) {
            RuleConditionGroup tenantFilterRules = processRuleConditionGroup(tenantId, baseConfig.getFilterRules(), dataListMap);
            tenantTemplate.setTenantFilterRules(tenantFilterRules);
        }

        // Process match rules with fields that require data from the database
        if (baseConfig.getMatchRules() != null) {
            RuleConditionGroup tenantMatchRules = processRuleConditionGroup(tenantId, baseConfig.getMatchRules(), dataListMap);
            tenantTemplate.setTenantMatchRules(tenantMatchRules);
        }

        // Process achievement rules with fields that require data from the database
        if (baseConfig.getAchievementRules() != null) {
            RuleConditionGroup tenantAchievementRules = processRuleConditionGroup(tenantId, baseConfig.getAchievementRules(), dataListMap);
            tenantTemplate.setTenantAchievementRules(tenantAchievementRules);
        }
        // Process only format fields with fields that require data from the database
        if (baseConfig.getOnlyFormatFields()!= null) {
            RuleConditionGroup ruleConditionGroup = new RuleConditionGroup();
            ruleConditionGroup.setConditions(baseConfig.getOnlyFormatFields());
            RuleConditionGroup tenantRuleConditionGroup = processRuleConditionGroup(tenantId, ruleConditionGroup, dataListMap);
            tenantTemplate.setOnlyFormatFields(tenantRuleConditionGroup.getConditions());
        }

        // Set priority
        tenantTemplate.setTenantPriority(baseConfig.getPriority());

        return tenantTemplate;
    }

    /**
     * 处理规则条件组，将其转换为租户特定的条件
     *
     * @param tenantId 租户ID
     * @param conditionGroup 要处理的条件组
     * @param dataListMap 预先查询的数据列表映射，用于替代实时数据库查询
     * @return 处理后的条件组
     */
    private RuleConditionGroup processRuleConditionGroup(String tenantId, RuleConditionGroup conditionGroup, Map<String, List<Map<String, Object>>> dataListMap) {
        if (conditionGroup == null) {
            return null;
        }

        RuleConditionGroup processedGroup = new RuleConditionGroup();
        processedGroup.setOperator(conditionGroup.getOperator());

        // Process field conditions
        if (conditionGroup.getConditions() != null && !conditionGroup.getConditions().isEmpty()) {
            List<RuleFieldConditionConfig> processedConditions = new java.util.ArrayList<>();

            for (RuleFieldConditionConfig condition : conditionGroup.getConditions()) {
                if (condition.getKeyMappingConfig() != null &&
                    condition.getKeyMappingConfig().getMappingType() == MappingType.FIELD_VALUE_PREFIX &&
                    condition.getKeyMappingConfig().getPrefixObjApiName() != null) {

                    // This is a condition that requires data from the database
                    String prefixFieldFromApiName = condition.getKeyMappingConfig().getPrefixObjApiName();

                    // 使用预先查询的数据而不是实时查询数据库
                    List<Map<String, Object>> dataList = null;
                    if (dataListMap != null) {
                        dataList = dataListMap.get(prefixFieldFromApiName);
                    }

                    // 如果预先查询的数据为空，则回退到数据库查询
                    if (dataList == null || dataList.isEmpty()) {
                        log.info("No pre-fetched data for {}, falling back to database query", prefixFieldFromApiName);
                        // Query data from the database
                        List<IObjectData> dbDataList = baseDao.getQueryDataListByQuery(
                                User.systemUser(tenantId),
                                SearchQuery.builder().build(),
                                prefixFieldFromApiName,
                                true).getData();

                        // 创建一个新条件为每个数据项
                        for (IObjectData data : dbDataList) {
                            String id = data.getId();
                            String name = data.getName();
                            String prefixedFieldApiName = id;

                            // 创建一个基于原始条件的新条件
                            RuleFieldConditionConfig newCondition = new RuleFieldConditionConfig();
                            newCondition.setTargetApiName(condition.getTargetApiName());
                            newCondition.setTargetField(prefixedFieldApiName);
                            newCondition.setSourceFieldType(condition.getSourceFieldType());
                            newCondition.setOperator(condition.getOperator());
                            newCondition.setSourceLookUpApiName(condition.getSourceLookUpApiName());
                            newCondition.setSupportFields(condition.getSupportFields());
                            newCondition.setTargetFieldName(name);
                            newCondition.setAggregationConfig(condition.getAggregationConfig());
                            if(condition.getValueMappingConfig() != null){
                                newCondition.setValueMappingConfig(condition.getValueMappingConfig().copy());
                            }
                            if(condition.getKeyMappingConfig() != null){
                                newCondition.setKeyMappingConfig(condition.getKeyMappingConfig().copy());
                                newCondition.getKeyMappingConfig().setPrefixedObjDataId(id);
                                newCondition.getKeyMappingConfig().setPrefixedFieldDataName(name);
                                newCondition.getKeyMappingConfig().setPrefixedFieldApiName(prefixedFieldApiName);
                            }
                            processedConditions.add(newCondition);
                        }
                    } else {
                        // 使用预先查询的数据
                        for (Map<String, Object> data : dataList) {
                            String id = data.get("id") != null ? data.get("id").toString() : "";
                            String name = data.get("name") != null ? data.get("name").toString() : "";
                            String prefixedFieldApiName = id;

                            // 创建一个基于原始条件的新条件
                            RuleFieldConditionConfig newCondition = new RuleFieldConditionConfig();
                            newCondition.setTargetApiName(condition.getTargetApiName());
                            newCondition.setTargetField(prefixedFieldApiName);
                            newCondition.setSourceFieldType(condition.getSourceFieldType());
                            newCondition.setOperator(condition.getOperator());
                            newCondition.setSourceLookUpApiName(condition.getSourceLookUpApiName());
                            newCondition.setSupportFields(condition.getSupportFields());
                            newCondition.setTargetFieldName(name);
                            newCondition.setAggregationConfig(condition.getAggregationConfig());
                            newCondition.setValueMappingConfig(condition.getValueMappingConfig());
                            if(condition.getValueMappingConfig() != null){
                                newCondition.setValueMappingConfig(condition.getValueMappingConfig());
                            }
                            if(condition.getKeyMappingConfig() != null){
                                newCondition.setKeyMappingConfig(condition.getKeyMappingConfig().copy());
                                newCondition.getKeyMappingConfig().setPrefixedObjDataId(id);
                                newCondition.getKeyMappingConfig().setPrefixedFieldDataName(name);
                                newCondition.getKeyMappingConfig().setPrefixedFieldApiName(prefixedFieldApiName);
                            }
                            processedConditions.add(newCondition);
                        }
                    }
                } else {
                    // This is a regular condition, just add it as is
                    processedConditions.add(condition);
                }
            }

            processedGroup.setConditions(processedConditions);
        }

        // Process nested groups recursively
        if (conditionGroup.getSubGroups() != null && !conditionGroup.getSubGroups().isEmpty()) {
            List<RuleConditionGroup> processedGroups = new java.util.ArrayList<>();

            for (RuleConditionGroup nestedGroup : conditionGroup.getSubGroups()) {
                RuleConditionGroup processedNestedGroup = processRuleConditionGroup(tenantId, nestedGroup, dataListMap);
                if (processedNestedGroup != null) {
                    processedGroups.add(processedNestedGroup);
                }
            }

            processedGroup.setSubGroups(processedGroups);
        }

        return processedGroup;
    }

    /**
     * Get the list of required fields for a rule generation configuration
     *
     * @param ruleGenerationConfig Rule generation configuration
     * @return List of required field names
     */
    public List<String> getRequiredFields(TenantStandardRuleTemplate ruleGenerationConfig) {
        Set<String> requiredFields = new HashSet<>();
        if (ruleGenerationConfig.getTenantFilterRules() != null) {
            for (RuleFieldConditionConfig condition : ruleGenerationConfig.getTenantFilterRules().getConditions()) {
                requiredFields.add(condition.getTargetField());
            }
        }
        if (ruleGenerationConfig.getTenantMatchRules() != null) {
            for (RuleFieldConditionConfig condition : ruleGenerationConfig.getTenantMatchRules().getConditions()) {
                requiredFields.add(condition.getTargetField());
            }
        }
        if (ruleGenerationConfig.getTenantAchievementRules() != null) {
            for (RuleFieldConditionConfig condition : ruleGenerationConfig.getTenantAchievementRules().getConditions()) {
                requiredFields.add(condition.getTargetField());
            }
        }

        return new ArrayList<>(requiredFields);
    }

    /**
     * Generate field mappings from a tenant rule configuration
     *
     * @param tenantId Tenant ID
     * @param tenantRuleConfig Tenant rule generation configuration
     * @param sourceObjectApiName Source object API name
     * @return List of field mapping configurations
     */
    public List<RuleFieldConditionConfig> getRuleFieldsFromTenantStandardRuleTemplate(
            TenantStandardRuleTemplate tenantRuleConfig,String standardObjectApiName) {

        List<RuleFieldConditionConfig> fieldMappings = new ArrayList<>();
        Set<String> uniqueTargetFields = new HashSet<>();

        if (tenantRuleConfig.getTenantFilterRules() != null) {
            for (RuleFieldConditionConfig condition : tenantRuleConfig.getTenantFilterRules().getConditions()) {
                if (uniqueTargetFields.add(condition.getTargetField())) {
                    fieldMappings.add(condition);
                }
            }
        }
        if (tenantRuleConfig.getTenantMatchRules() != null) {
            for (RuleFieldConditionConfig condition : tenantRuleConfig.getTenantMatchRules().getConditions()) {
                if (uniqueTargetFields.add(condition.getTargetField())) {
                    fieldMappings.add(condition);
                }
            }
        }
        if (tenantRuleConfig.getTenantAchievementRules() != null) {
            for (RuleFieldConditionConfig condition : tenantRuleConfig.getTenantAchievementRules().getConditions()) {
                if (uniqueTargetFields.add(condition.getTargetField())) {
                    fieldMappings.add(condition);
                }
            }
        }
        if (tenantRuleConfig.getOnlyFormatFields() != null) {
            for (RuleFieldConditionConfig condition : tenantRuleConfig.getOnlyFormatFields()) {
                if (uniqueTargetFields.add(condition.getTargetField())) {
                    fieldMappings.add(condition);
                }
            }
        }
        return fieldMappings;

    }

//    /**
//     * 为了保持向后兼容，提供从 BaseRuleGenerationConfig 生成字段映射的方法
//     *
//     * @param tenantId Tenant ID
//     * @param baseRuleConfig Base rule generation configuration
//     * @param sourceObjectApiName Source object API name
//     * @return List of field mapping configurations
//     */
//    public List<FieldMappingConfigField> getRuleFieldsFromTenantStandardRuleTemplate(
//            String tenantId,
//            BaseRuleTemplateConfig baseRuleConfig,
//            String sourceObjectApiName) {
//
//        // 创建临时的 TenantRuleGenerationConfig
//        TenantStandardRuleTemplate tenantRuleConfig = com.facishare.crm.fmcg.wq.rule.model.mapping.TenantStandardRuleTemplate.fromBaseConfig(tenantId, baseRuleConfig);
//
//        // 使用 TenantRuleGenerationConfig 生成字段映射
//        return getRuleFieldsFromTenantStandardRuleTemplate(tenantId, tenantRuleConfig, sourceObjectApiName);
//    }
//
//    /**
//     * 为了保持向后兼容，提供从 RuleGenerationConfig 生成字段映射的方法
//     *
//     * @param tenantId Tenant ID
//     * @param ruleGenerationConfig Rule generation configuration
//     * @param sourceObjectApiName Source object API name
//     * @return List of field mapping configurations
//     */
//    public List<FieldMappingConfigField> getRuleFieldsFromTenantStandardRuleTemplate(
//            String tenantId,
//            TenantStandardRuleTemplate ruleGenerationConfig,
//            String sourceObjectApiName) {
//
//        // 创建临时的 BaseRuleGenerationConfig
//        BaseRuleTemplateConfig baseRuleConfig = BaseRuleTemplateConfig.builder()
//                .templateId(ruleGenerationConfig.getTemplateId())
//                .filterRules(ruleGenerationConfig.getFilterRules())
//                .matchRules(ruleGenerationConfig.getMatchRules())
//                .achievementRules(ruleGenerationConfig.getAchievementRules())
//                .priority(ruleGenerationConfig.getPriority())
//                .description(ruleGenerationConfig.getDescription())
//                .build();
//
//        // 使用 BaseRuleGenerationConfig 生成字段映射
//        return getRuleFieldsFromTenantStandardRuleTemplate(tenantId, baseRuleConfig, sourceObjectApiName);
//    }



    /**
     * Process a field condition to extract field mapping
     *
     * @param condition           Field condition
     * @param processedFields     Set of already processed fields
     * @param fieldMappings       List to add field mappings to
     * @param sourceObjectApiName Source object API name
     */


    /**
     * Find a matching source field in the source object
     *
     * @param targetField         Target field name
     * @param sourceObjectApiName Source object API name
     * @return Suggested source field name, or null if no match
     */
    public String findMatchingSourceField(String targetField, String sourceObjectApiName) {
        // Implementation will depend on how field mapping is typically done
        // Here's a simple implementation looking for exact matches or similar names

        // This is just a placeholder - in a real implementation, you would:
        // 1. Look up the fields available in the source object
        // 2. Look for matching or similar names
        // 3. Consider field types and other metadata

        // For now, we'll use the same name as a suggestion
        return null;
    }

    /**
     * 验证映射配置
     *
     * @param mappingConfig 映射配置
     * @return 验证结果
     */
    public ValidationResult validateMappingConfig(TenantRuleRuntimeConfig mappingConfig) {
        if (mappingConfig == null || mappingConfig.getTenantFieldCustomConfig() == null || mappingConfig.getTenantStandardRuleTemplate() == null) {
            List<String> errors = new ArrayList<>();
            errors.add("Missing field mapping config or tenant rule config");
            return new ValidationResult(false, errors);
        }

        TenantFieldCustomConfig tenantFieldCustomConfig = mappingConfig.getTenantFieldCustomConfig();
        TenantStandardRuleTemplate tenantStandardRuleTemplate = mappingConfig.getTenantStandardRuleTemplate();

        // 获取必填字段
        List<String> requiredFields = getRequiredFields(tenantStandardRuleTemplate);

        // 获取已映射的字段
        Set<String> mappedFields = tenantFieldCustomConfig.getFieldMappings().stream()
                .map(FieldMappingConfigField::getTargetField)
                .collect(Collectors.toSet());

        // 检查是否所有必填字段都已映射
        List<String> missingFields = requiredFields.stream()
                .filter(field -> !mappedFields.contains(field))
                .collect(Collectors.toList());

        return new ValidationResult(missingFields.isEmpty(), missingFields);
    }
//
//    /**
//     * 创建完整的 RuleMappingConfig
//     *
//     * @param tenantId 租户 ID
//     * @param sourceObjectApiName 源对象 API 名称
//     * @param standardObjectApiName 标准对象 API 名称
//     * @return 完整的规则映射配置
//     */
//    public TenantRuleRuntimeConfig createRuleMappingConfig(
//            String tenantId,
//            String sourceObjectApiName,
//            String standardObjectApiName) {
//
//        // 从缓存获取基础规则配置
//        BaseRuleTemplateConfig baseRuleConfig = getBaseRuleTemplateConfigCopy(standardObjectApiName);
//        if (baseRuleConfig == null) {
//            log.warn("No standard rule configuration found for {}", standardObjectApiName);
//            return null;
//        }
//
//        // 创建租户规则配置
//        TenantStandardRuleTemplate tenantRuleConfig = com.facishare.crm.fmcg.wq.rule.model.mapping.TenantStandardRuleTemplate.fromBaseConfig(tenantId, baseRuleConfig);
//
//        // 生成字段映射
//        List<FieldMappingConfigField> fieldMappings =
//                getRuleFieldsFromTenantStandardRuleTemplate(tenantId, tenantRuleConfig, sourceObjectApiName);
//
//        // 创建租户字段映射配置
//        TenantFieldCustomConfig fieldMappingConfig = TenantFieldCustomConfig.builder()
//                .configId(UUID.randomUUID().toString())
//                .tenantId(tenantId)
//                .sourceObjectApiName(sourceObjectApiName)
//                .standardObjectApiName(standardObjectApiName)
//                .fieldMappings(fieldMappings)
//                .build();
//
//        // 创建并返回完整配置
//        return TenantRuleRuntimeConfig.builder()
//                .tenantFieldCustomConfig(fieldMappingConfig)
//                .tenantStandardRuleTemplate(tenantRuleConfig)
//                .build();
//    }

    /**
     * Validation result for mapping configuration validation
     */
    @Getter
    public static class ValidationResult {
        private final boolean isValid;
        private final List<String> missingFields;

        public ValidationResult(boolean isValid, List<String> missingFields) {
            this.isValid = isValid;
            this.missingFields = missingFields;
        }

    }
}
