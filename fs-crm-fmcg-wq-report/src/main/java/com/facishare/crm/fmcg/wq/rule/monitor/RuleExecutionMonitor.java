package com.facishare.crm.fmcg.wq.rule.monitor;

import com.facishare.crm.fmcg.wq.rule.engine.RuleExecutionResult;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.DoubleSummaryStatistics;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * 规则执行监控器
 * 收集规则执行的性能和业务指标
 */
@Slf4j
@Component
public class RuleExecutionMonitor {

    /**
     * 执行计数器
     */
    private final AtomicLong totalExecutions = new AtomicLong(0);
    
    /**
     * 成功计数器
     */
    private final AtomicLong successExecutions = new AtomicLong(0);
    
    /**
     * 失败计数器
     */
    private final AtomicLong failedExecutions = new AtomicLong(0);
    
    /**
     * 缓存命中计数器
     */
    private final AtomicLong cacheHits = new AtomicLong(0);
    
    /**
     * 缓存未命中计数器
     */
    private final AtomicLong cacheMisses = new AtomicLong(0);
    
    /**
     * 规则执行计数
     * key: 规则ID
     * value: 执行统计
     */
    private final Map<String, RuleExecutionStat> ruleStats = new ConcurrentHashMap<>();
    
    /**
     * 执行错误日志
     */
    private final List<ExecutionError> recentErrors = new ArrayList<>();
    
    /**
     * 最大错误日志数量
     */
    private static final int MAX_ERROR_LOGS = 100;
    
    /**
     * 记录执行结果
     * @param result 执行结果
     * @param isCacheHit 是否缓存命中
     */
    public void recordExecution(RuleExecutionResult result, boolean isCacheHit) {
        totalExecutions.incrementAndGet();
        
        if (result.isSuccess()) {
            successExecutions.incrementAndGet();
        } else {
            failedExecutions.incrementAndGet();
        }
        
        if (isCacheHit) {
            cacheHits.incrementAndGet();
        } else {
            cacheMisses.incrementAndGet();
        }
        
        // 记录规则统计
        RuleExecutionStat stat = ruleStats.computeIfAbsent(result.getRuleId(), id -> new RuleExecutionStat());
        stat.recordExecution(result);
        
        // 记录错误
        if (!result.isSuccess() && result.getMessage() != null) {
            recordError(result);
        }
    }
    
    /**
     * 记录执行错误
     * @param result 执行结果
     */
    private synchronized void recordError(RuleExecutionResult result) {
        ExecutionError error = new ExecutionError();
        error.setRuleId(result.getRuleId());
        error.setRuleName(result.getRuleName());
        error.setErrorMessage(result.getMessage());
        error.setTimestamp(LocalDateTime.now());
        
        recentErrors.add(0, error);
        
        // 保持错误日志不超过最大数量
        if (recentErrors.size() > MAX_ERROR_LOGS) {
            recentErrors.remove(recentErrors.size() - 1);
        }
    }
    
    /**
     * 获取总执行次数
     */
    public long getTotalExecutions() {
        return totalExecutions.get();
    }
    
    /**
     * 获取成功执行次数
     */
    public long getSuccessExecutions() {
        return successExecutions.get();
    }
    
    /**
     * 获取失败执行次数
     */
    public long getFailedExecutions() {
        return failedExecutions.get();
    }
    
    /**
     * 获取缓存命中次数
     */
    public long getCacheHits() {
        return cacheHits.get();
    }
    
    /**
     * 获取缓存未命中次数
     */
    public long getCacheMisses() {
        return cacheMisses.get();
    }
    
    /**
     * 获取缓存命中率
     */
    public double getCacheHitRate() {
        long hits = cacheHits.get();
        long total = hits + cacheMisses.get();
        return total > 0 ? (double) hits / total : 0;
    }
    
    /**
     * 获取规则执行成功率
     */
    public double getSuccessRate() {
        long success = successExecutions.get();
        long total = success + failedExecutions.get();
        return total > 0 ? (double) success / total : 0;
    }
    
    /**
     * 获取最近错误
     */
    public synchronized List<ExecutionError> getRecentErrors() {
        return new ArrayList<>(recentErrors);
    }
    
    /**
     * 获取规则统计
     */
    public Map<String, RuleExecutionStat> getRuleStats() {
        return ruleStats;
    }
    
    /**
     * 重置所有统计
     */
    public synchronized void reset() {
        totalExecutions.set(0);
        successExecutions.set(0);
        failedExecutions.set(0);
        cacheHits.set(0);
        cacheMisses.set(0);
        ruleStats.clear();
        recentErrors.clear();
    }
    
    /**
     * 规则执行统计
     */
    @Data
    public static class RuleExecutionStat {
        /**
         * 执行总次数
         */
        private final AtomicLong executions = new AtomicLong(0);
        
        /**
         * 成功次数
         */
        private final AtomicLong successes = new AtomicLong(0);
        
        /**
         * 失败次数
         */
        private final AtomicLong failures = new AtomicLong(0);
        
        /**
         * 执行时间统计
         */
        private final List<Long> executionTimes = new ArrayList<>();
        
        /**
         * 记录执行结果
         * @param result 执行结果
         */
        public synchronized void recordExecution(RuleExecutionResult result) {
            executions.incrementAndGet();
            
            if (result.isSuccess()) {
                successes.incrementAndGet();
            } else {
                failures.incrementAndGet();
            }
            
            executionTimes.add(result.getExecutionTime());
            
            // 限制统计样本数量，避免内存占用过大
            if (executionTimes.size() > 1000) {
                executionTimes.remove(0);
            }
        }
        
        /**
         * 获取平均执行时间
         */
        public double getAverageExecutionTime() {
            synchronized(this) {
                return executionTimes.stream()
                    .mapToLong(Long::valueOf)
                    .average()
                    .orElse(0);
            }
        }
        
        /**
         * 获取执行时间统计
         */
        public DoubleSummaryStatistics getExecutionTimeStats() {
            synchronized(this) {
                return executionTimes.stream()
                    .mapToDouble(Long::doubleValue)
                    .summaryStatistics();
            }
        }
        
        /**
         * 获取成功率
         */
        public double getSuccessRate() {
            long success = successes.get();
            long total = executions.get();
            return total > 0 ? (double) success / total : 0;
        }
    }
    
    /**
     * 执行错误日志
     */
    @Data
    public static class ExecutionError {
        /**
         * 规则ID
         */
        private String ruleId;
        
        /**
         * 规则名称
         */
        private String ruleName;
        
        /**
         * 错误消息
         */
        private String errorMessage;
        
        /**
         * 时间戳
         */
        private LocalDateTime timestamp;
    }
} 