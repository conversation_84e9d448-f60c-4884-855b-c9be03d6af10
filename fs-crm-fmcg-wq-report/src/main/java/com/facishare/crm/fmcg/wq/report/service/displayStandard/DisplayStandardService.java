package com.facishare.crm.fmcg.wq.report.service.displayStandard;

import java.util.*;
import java.util.stream.Collectors;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.DisplayProjectAchievementFields;
import com.facishare.crm.fmcg.wq.dao.DataReportStandardDao;
import com.facishare.crm.fmcg.wq.report.model.DisplaySimpleInfo;
import com.facishare.crm.fmcg.wq.report.model.DisplayReportDetail;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class DisplayStandardService {

    @Autowired
    protected DataReportStandardDao dataReportStandardDao;

    @Autowired
    protected ServiceFacade serviceFacade;
    abstract String getDisplayStandardKey();

    public abstract List<DisplayReportDetail.ProjectDetailInfo> buildProjectInfo(User systemUser, List<IObjectData> dataList,Map<String,String> displayProjectValueAndColorMap);

    /**
     * 判断是否隐藏 品类、层级、规则
     */
    protected void buildProjectDetailInfoHeadInfo(DisplayReportDetail.ProjectDetailInfo projectDetailInfo){
        if(Objects.nonNull(projectDetailInfo)){
            for (DisplayReportDetail.ProjectBasicsInfo projectBasicsInfo : projectDetailInfo.getProjectBasicsInfos()) {
                if(Objects.nonNull(projectBasicsInfo.getLevel())){
                    projectDetailInfo.setIsShowLevel(1);
                }
                if(Objects.nonNull(projectBasicsInfo.getCategoryName()) || Objects.nonNull(projectBasicsInfo.getProductName())){
                    projectDetailInfo.setIsShowRule(1);
                }
            }
        }
    }

    protected DisplayReportDetail.ProjectBasicsInfo buildBaseProjectDetailInfo(IObjectData item, String productName, String categoryName,Map<String,String> displayProjectValueAndColorMap) {
        DisplayReportDetail.ProjectBasicsInfo projectBasicsInfo = new DisplayReportDetail.ProjectBasicsInfo();
        projectBasicsInfo.setProductName(productName);
        projectBasicsInfo.setCategoryName(categoryName);
        projectBasicsInfo.setProjectName((String)item.get(DisplayProjectAchievementFields.PROJECT_NAME__R));
        String level = (String)item.get(DisplayProjectAchievementFields.LEVEL);
        projectBasicsInfo.setLevel(Objects.isNull(level) || level.compareTo("0") <= 0 ? null : level);
        //如果 有不达标值 显示为 <不达标,>=达标值
//        if(Objects.nonNull(item.get(DisplayProjectAchievementFields.UNMET_PROJECT_COUNT__C))){
//            projectBasicsInfo.setStandard("<"+item.get(DisplayProjectAchievementFields.UNMET_PROJECT_COUNT__C)+",>="+item.get(DisplayProjectAchievementFields.REQUIRED_PROJECT_QUANTITY));
//        }else {
            projectBasicsInfo.setStandard((String)item.get(DisplayProjectAchievementFields.REQUIRED_PROJECT_QUANTITY));
//        }
        projectBasicsInfo.setActual((String)item.get(DisplayProjectAchievementFields.ACTUAL_PROJECT_QUANTITY));
        projectBasicsInfo.setDiff((String)item.get(DisplayProjectAchievementFields.DIFFERENCE_QUANTITY));
        projectBasicsInfo.setReachText((String)item.get(DisplayProjectAchievementFields.ACHIEVED_RESULTS__R));
        if(Objects.nonNull(displayProjectValueAndColorMap) && Objects.nonNull(projectBasicsInfo.getReachText())){
            projectBasicsInfo.setReachColor(displayProjectValueAndColorMap.get(projectBasicsInfo.getReachText()));
        }
        return projectBasicsInfo;
    }

    /**
     * 构建父子关系
     */
    protected Map<String,DisplaySimpleInfo> buildDisplaySimpleInfo(List<IObjectData> dataList,List<String> existIds) {
        Map<String,DisplaySimpleInfo> result = Maps.newHashMap();
        List<DisplaySimpleInfo> datas = Lists.newArrayList();

        if (dataList == null || dataList.isEmpty()) {
            return result;
        }
        // 构建一个映射，方便查找子节点
        Map<String, List<IObjectData>> childrenMap = dataList.stream()
                .collect(Collectors.groupingBy(item -> {
                    String parentId = item.get(DisplayProjectAchievementFields.CONDITION_PARENT_ID,String.class);
                    return parentId == null || !existIds.contains(parentId) ? "ROOT" : parentId;
                }));

        // 找到根节点并开始构建树
        List<IObjectData> rootNodes = childrenMap.getOrDefault("ROOT", new ArrayList<>());

        for (IObjectData rootNode : rootNodes) {
            DisplaySimpleInfo displayInfo = new DisplaySimpleInfo();
            String nodeId = String.valueOf(rootNode.get(BaseField.id.getApiName()));
            // 设置根节点的属性
            displayInfo.setDataId(nodeId);
            displayInfo.setChildInfos(buildChildren(nodeId, childrenMap, new ArrayList<>()));
            datas.add(displayInfo);
        }

        return datas.stream().collect(Collectors.toMap(DisplaySimpleInfo::getDataId, k2->k2,(k1, k2)->k1));
    }

    // 递归构建子节点，增加路径检测防止循环引用
    private List<DisplaySimpleInfo> buildChildren(String parentId, Map<String, List<IObjectData>> childrenMap, List<String> path) {
        List<DisplaySimpleInfo> children = new ArrayList<>();

        // 防止循环引用
        if (path.contains(parentId)) {
            return children;
        }

        List<String> newPath = new ArrayList<>(path);
        newPath.add(parentId);
        List<IObjectData> childNodes = childrenMap.getOrDefault(parentId, new ArrayList<>());
        // 按照层级排序
        childNodes.sort(Comparator.comparing(item -> String.valueOf(item.get(DisplayProjectAchievementFields.LEVEL))));
        for (IObjectData childNode : childNodes) {
            DisplaySimpleInfo childInfo = new DisplaySimpleInfo();
            String nodeId = String.valueOf(childNode.get(BaseField.id.getApiName()));
            // 设置子节点的属性
            childInfo.setDataId(nodeId);
            childInfo.setChildInfos(buildChildren(nodeId, childrenMap, newPath));
            children.add(childInfo);
        }

        return children;
    }

}
