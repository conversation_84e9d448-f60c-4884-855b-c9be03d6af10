package com.facishare.crm.fmcg.wq.rule.template.impl;

import com.facishare.crm.fmcg.wq.rule.model.mapping.base.LogicalOperator;
import com.facishare.crm.fmcg.wq.rule.template.ConditionTemplate;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.ArrayList;
import java.util.List;

import lombok.Builder;
import lombok.Data;

/**
 * 组合条件实现
 * 通过组合多个条件，实现复杂的条件判断逻辑
 */
@Data
@Builder
public class CompositeCondition implements ConditionTemplate {

    /**
     * 条件ID
     */
    private String conditionId;

    /**
     * 条件名称
     */
    private String conditionName;

    /**
     * 条件类型，组合条件固定为GROUP
     */
    private final String conditionType = "GROUP";

    /**
     * 条件描述
     */
    private String description;

    /**
     * 逻辑运算符
     */
    private LogicalOperator logicalOperator = LogicalOperator.AND;

    /**
     * 子条件列表
     */
    @Builder.Default
    private List<ConditionTemplate> subConditions = new ArrayList<>();

    /**
     * 添加子条件
     * @param condition 条件
     */
    public void addCondition(ConditionTemplate condition) {
        if (subConditions == null) {
            subConditions = new ArrayList<>();
        }
        subConditions.add(condition);
    }

    /**
     * 移除子条件
     * @param condition 条件
     */
    public void removeCondition(ConditionTemplate condition) {
        if (subConditions != null) {
            subConditions.remove(condition);
        }
    }

    @Override
    public boolean evaluate(IObjectData data) {
        if (subConditions == null || subConditions.isEmpty()) {
            return true; // 没有条件意味着成功
        }

        if (logicalOperator == LogicalOperator.AND) {
            return subConditions.stream().allMatch(c -> c.evaluate(data));
        } else {
            return subConditions.stream().anyMatch(c -> c.evaluate(data));
        }
    }
} 