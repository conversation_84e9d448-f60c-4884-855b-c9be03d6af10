package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.crm.fmcg.wq.util.StandardFieldMappingUtil;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectMany;
import com.facishare.paas.metadata.impl.describe.SelectManyFieldDescribe;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 数据上报标准的DAO
 * @author: zhangsm
 * @create: 2025-03-07 17:45
 **/
@Component
public class DataReportStandardDao extends AbstractDao {

//    //标准主对象和 子对象之间的一些映射关系存储
//    private Map<String, Map<String, String>> standardMainIdAndDetailIdMap = new HashMap<>();
//    {
//    Map<String, String> successfulStoreRangeMap = new HashMap<>();
//       //标准详情ApiName
//       successfulStoreRangeMap.put(ProjectStandardsFields.API_NAME, ProjectStandardsFields.API_NAME);
//       //层级
//       successfulStoreRangeMap.put(
//               DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, ProjectStandardsFields.DISPLAY_LEVEL);
//       //陈列形式
//       successfulStoreRangeMap.put(
//               DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, ProjectStandardsFields.DISPLAY_FORMAT);
//       //产品
//       successfulStoreRangeMap.put(
//               ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS, ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS);
//       //物料
//       successfulStoreRangeMap.put(
//               ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS, ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS);
//       standardMainIdAndDetailIdMap.put(SuccessfulStoreRangeFields.API_NAME, successfulStoreRangeMap);
//
//
//       Map<String, String> tpmActivityAgreementMap = new HashMap<>();
//       //标准详情ApiName
//       tpmActivityAgreementMap.put(ProjectStandardsFields.API_NAME, TPMActivityAgreementDetailFields.API_NAME);
//       //层级
//        // tpmActivityAgreementMap.put(
//        //         DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL,
//        //         TPMActivityAgreementDetailFields.DISPLAY_LEVEL);
//       //陈列形式
//       tpmActivityAgreementMap.put(
//               DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, TPMActivityAgreementDetailFields.DISPLAY_FORM_ID);
//       //产品
//       tpmActivityAgreementMap.put(
//               ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS, TPMActivityAgreementDetailFields.PRODUCT_ITEM_STANDARD_ID);
//       //物料
//       tpmActivityAgreementMap.put(
//               ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS, TPMActivityAgreementDetailFields.MATERIAL_STANDARD_REQUIREM_ID);
//       standardMainIdAndDetailIdMap.put(TPMActivityAgreementFields.API_NAME, tpmActivityAgreementMap);
//
//
//       Map<String, String> tpmActivityMap = new HashMap<>();
//       //标准详情ApiName
//       tpmActivityMap.put(ProjectStandardsFields.API_NAME, TPMActivityDetailFields.API_NAME);
//       //层级
//       // tpmActivityMap.put(
//       //         DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL,
//       //         TPMActivityDetailFields.DISPLAY_LEVEL);
//       //陈列形式
//       tpmActivityMap.put(
//               DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, TPMActivityDetailFields.DISPLAY_FORM_ID);
//       //产品
//       tpmActivityMap.put(
//               ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS, TPMActivityDetailFields.PRODUCT_ITEM_STANDARD_ID);
//       //物料
//       tpmActivityMap.put(
//               ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS, TPMActivityDetailFields.MATERIAL_STANDARD_REQUIREM_ID);
//       standardMainIdAndDetailIdMap.put(TPMActivityFields.API_NAME, tpmActivityMap);
//    }

    /**
     * 查询数据上报标准 根据主对象ID查询
     *
     * @param user         用户信息
     * @param standardMainId 主对象ID
     * @param recordType
     * @return 数据上报标准
     */
    public Map<String, Map<String, IObjectData>> getByMainObjectId(User user, String standardMainApiName, String standardMainId, String recordType) {
        if (user == null || StringUtils.isBlank(standardMainId)) {
            return Collections.emptyMap();
        }

        Map<String, Map<String, IObjectData>> mainAndDetailObjectData = getMainAndDetailObjectData(user, standardMainApiName, standardMainId);
        if (mainAndDetailObjectData == null || mainAndDetailObjectData.isEmpty()) {
            return Collections.emptyMap();
        }

        String projectStandardsApiName = StandardFieldMappingUtil.getFieldMappingByMainApiName(standardMainApiName).get(ProjectStandardsFields.API_NAME);
        Map<String, IObjectData> projectStandards = mainAndDetailObjectData.get(projectStandardsApiName);
        if (projectStandards == null || projectStandards.isEmpty()) {
            return mainAndDetailObjectData;
        }

        Map<String/**项目id */, String/**产品标准id */> projectIdAndProductItemStandardIdMap = new HashMap<>();
        Map<String/**项目id */, String/**物料标准id */> projectIdAndMaterialStandardIdMap = new HashMap<>();
        Map<String, IObjectData> successfulStoreRangeMap = mainAndDetailObjectData.get(standardMainApiName);
        if (successfulStoreRangeMap == null || successfulStoreRangeMap.isEmpty()) {
            return mainAndDetailObjectData;
        }

        String successfulStoreRangeId = successfulStoreRangeMap.values().iterator().next().getId();
        if (StringUtils.isBlank(successfulStoreRangeId)) {
            return mainAndDetailObjectData;
        }
        // Extracting Product Item Standard IDs and Material Standard Requirement IDs
        List<String> productItemStandardIds = new ArrayList<>();
        List<String> materialStandardRequirementIds = new ArrayList<>();
        
        for (IObjectData projectStandard : projectStandards.values()) {
            if (projectStandard == null) {
                continue;
            }
            // 规则组id 设置
            String ruleGroupId = projectStandard.getId();
            String ruleGroupApiName = projectStandard.getDescribeApiName();
            projectStandard.set(DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_ID, ruleGroupId);
            if (projectStandardsApiName.equals(ruleGroupApiName)) {
                projectStandard.set(DataReport2PublicFieldsConstants.PROJECT_STANDARD_SETTING_TYPE, projectStandard.get(ProjectStandardsFields.SETTING_TYPE));
            }else{
                projectStandard.set(DataReport2PublicFieldsConstants.PROJECT_STANDARD_SETTING_TYPE, "-1");
            }
            projectStandard.set(DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_APINAME, ruleGroupApiName);
            // 设置层级和陈列形式 兼容空
            String displayLevel =  projectStandard.get(StandardFieldMappingUtil.getFieldMappingByMainApiName(standardMainApiName).getOrDefault(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, ""), String.class);
            String displayFormat = projectStandard.get(StandardFieldMappingUtil.getFieldMappingByMainApiName(standardMainApiName).getOrDefault(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, ""), String.class);
            
            if (displayLevel != null) {
                projectStandard.set(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, displayLevel);
            }
            if (displayFormat != null) {
                projectStandard.set(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, displayFormat);
            }

            // 收集产品和材料标准ID
            String productItemId = projectStandard.get(StandardFieldMappingUtil.getFieldMappingByMainApiName(standardMainApiName).getOrDefault(ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS, ""), String.class);
            if (StringUtils.isNotBlank(productItemId)) {
                productItemStandardIds.add(productItemId);
                projectIdAndProductItemStandardIdMap.put( projectStandard.getId(),productItemId);
            }
            
            String materialId = projectStandard.get(StandardFieldMappingUtil.getFieldMappingByMainApiName(standardMainApiName).getOrDefault(ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS, ""), String.class);
            if (StringUtils.isNotBlank(materialId)) {
                materialStandardRequirementIds.add(materialId);
                projectIdAndMaterialStandardIdMap.put( projectStandard.getId(),materialId);
            }
            // 量词 项目 字段 活动的不需要 数据从tpm结果取
            if(projectStandardsApiName.equals(ruleGroupApiName)){
                // 量词 项目 字段
                String quantityField = projectStandard.get(ProjectStandardsFields.DISPLAY_PROJECT, String.class);
                List subConditions = projectStandard.get(DataReport2PublicFieldsConstants.SUB_CONDITIONS, List.class);
                if (CollectionUtils.isNotEmpty(subConditions)) {
                    // 有子的话
                    projectStandard.set(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY,
                            DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_VALUE);
                    projectStandard.set(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_VALUE,
                            projectStandard.get(ProjectStandardsFields.QUANTITY));
                } else {
                    if (StringUtils.isNotBlank(quantityField)) {
                        projectStandard.set(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY,
                                quantityField);
                        projectStandard.set(quantityField, projectStandard.get(ProjectStandardsFields.QUANTITY));
                    }
                }
            }
        }

        // 只有在有产品标准ID时才获取产品标准数据
        if (CollectionUtils.isNotEmpty(productItemStandardIds)) {
            // Fetching Product Item Standard Objects
            List<IObjectData> productItemStandardMainData = getByIds(user.getTenantId(), ProductItemStandardFields.API_NAME, productItemStandardIds);
            if (CollectionUtils.isNotEmpty(productItemStandardMainData)) {
                mainAndDetailObjectData.put(ProductItemStandardFields.API_NAME, 
                    productItemStandardMainData.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(IObjectData::getId, data -> data, (v1, v2) -> v1)));
                
                // Fetching Product Item Standard Detail Objects based on conditions
                List<IObjectData> productItemStandardDetailData = getAllIObjectDataListByQuery(user, 
                    SearchQuery.builder().in(ProductItemStandardDetailFields.PRODUCT_ITEM, productItemStandardIds).build(),
                    ProductItemStandardDetailFields.API_NAME);
                
                if (CollectionUtils.isNotEmpty(productItemStandardDetailData)) {
                    mainAndDetailObjectData.put(ProductItemStandardDetailFields.API_NAME, 
                        productItemStandardDetailData.stream()
                            .filter(Objects::nonNull)
                            .collect(Collectors.toMap(IObjectData::getId, data -> data, (v1, v2) -> v1)));
                    
                    processProductItemStandardDetails(standardMainApiName,mainAndDetailObjectData, projectIdAndProductItemStandardIdMap);
                }
            }
        }

        // 只有在有材料标准ID时才获取材料标准数据
        if (CollectionUtils.isNotEmpty(materialStandardRequirementIds)) {
            // Fetching Material Standard Requirement Objects
            List<IObjectData> materialStandardRequirementMainData = getByIds(user.getTenantId(), MaterialStandardRequiremFields.API_NAME, materialStandardRequirementIds);
            if (CollectionUtils.isNotEmpty(materialStandardRequirementMainData)) {
                mainAndDetailObjectData.put(MaterialStandardRequiremFields.API_NAME, 
                    materialStandardRequirementMainData.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(IObjectData::getId, data -> data, (v1, v2) -> v1)));
                
                // Fetching Material Standard Detail Objects based on conditions
                List<IObjectData> materialStandardDetailData = getAllIObjectDataListByQuery(user, 
                    SearchQuery.builder().in(MaterialStandardDetailsFields.MATERIAL_STANDARD, materialStandardRequirementIds).build(), 
                    MaterialStandardDetailsFields.API_NAME);
                //补充物料分类描述
                IObjectDescribe materialStandardDetailDescribe =  serviceFacade.findObject(user.getTenantId(), MaterialStandardDetailsFields.API_NAME);
                IFieldDescribe fieldDescribe = materialStandardDetailDescribe.getFieldDescribe(MaterialStandardDetailsFields.MATERIAL_CATEGORY);
                FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
                SelectManyFieldDescribe selectManyFieldDescribe = new SelectManyFieldDescribe(fieldDescribeExt.toMap());
                List<ISelectOption> selectOptions = selectManyFieldDescribe.getSelectOptions();
                Map<String, String> valueAndLabel = selectOptions.stream().collect(Collectors.toMap(ISelectOption::getValue, ISelectOption::getLabel));
                materialStandardDetailData.forEach(data -> {
                    data.set(DataReport2PublicFieldsConstants.MATERIAL_CATEGORY_DESC, valueAndLabel);
                });
                if (CollectionUtils.isNotEmpty(materialStandardDetailData)) {
                    mainAndDetailObjectData.put(MaterialStandardDetailsFields.API_NAME, 
                        materialStandardDetailData.stream()
                            .filter(Objects::nonNull)
                            .collect(Collectors.toMap(IObjectData::getId, data -> data, (v1, v2) -> v1)));
                    
                    processMaterialStandardDetails(standardMainApiName,mainAndDetailObjectData, projectIdAndMaterialStandardIdMap);
                }
            }
        }
        
        return mainAndDetailObjectData;
    }

    /**
     * 处理产品标准详情数据
     *
     * @param productItemStandardDetailData        产品标准详情数据
     * @param standardMainApiName
     * @param mainAndDetailObjectData              主和详情对象数据
     * @param projectIdAndProductItemStandardIdMap 项目id 到产品标准id的映射
     */
    private void processProductItemStandardDetails(String standardMainApiName, Map<String, Map<String, IObjectData>> mainAndDetailObjectData, Map<String, String> projectIdAndProductItemStandardIdMap) {
        Map<String, IObjectData> productItemStandardMap = mainAndDetailObjectData.get(ProductItemStandardFields.API_NAME);
        Map<String, IObjectData> projectStandardMap = mainAndDetailObjectData.get(StandardFieldMappingUtil.getFieldMappingByMainApiName(standardMainApiName).get(ProjectStandardsFields.API_NAME));
        Map<String, IObjectData> productItemStandardDetailMap = mainAndDetailObjectData.get(ProductItemStandardDetailFields.API_NAME);

        if (productItemStandardMap == null || projectStandardMap == null || productItemStandardDetailMap == null) {
            return;
        }
        // 通用本身虚拟字段设置 重复的数据 不受上级迎新啊个的
        productItemStandardDetailMap.values().stream().filter(Objects::nonNull).forEach(
                data->{
                    //设置量词 项目 字段
                    String quantityField = data.get(ProductItemStandardDetailFields.DISPLAY_PROJECT, String.class);
                    List subConditions = data.get(DataReport2PublicFieldsConstants.SUB_CONDITIONS, List.class);
                    if (CollectionUtils.isNotEmpty(subConditions)) {
                        // 有子的话
                        data.set(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY, DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_VALUE);
                        data.set(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_VALUE, data.get(ProductItemStandardDetailFields.PROJECT_STANDARDS));
                    } else {
                        if (StringUtils.isNotBlank(quantityField)) {
                            data.set(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY, quantityField);
                            data.set(quantityField, data.get(ProductItemStandardDetailFields.PROJECT_STANDARDS));
                        }
                    }
                }
        );
        // 按照productStandardId对productItemStandardDetailMap进行分组
        Map<String, List<IObjectData>> productStandardDetailGroups = productItemStandardDetailMap.values().stream()
                .filter(detail-> Objects.nonNull(detail)
                    && StringUtils.isNotBlank(detail.get(ProductItemStandardDetailFields.PRODUCT_ITEM, String.class)))
                .collect(Collectors.groupingBy(detail ->
                        detail.get(ProductItemStandardDetailFields.PRODUCT_ITEM, String.class)));

        // 为每个detail数据创建深拷贝并构建新的映射
        Map<String, IObjectData> newDetailMap = new HashMap<>();
        // 遍历itemIdAndProductItemStandardIdMap
        for (Map.Entry<String, String> entry : projectIdAndProductItemStandardIdMap.entrySet()) {
            String projectId = entry.getKey(); // 项目标准id
            String productStandardId = entry.getValue(); // 产品标准id

            // 获取该productStandardId下的所有detail数据
            List<IObjectData> detailList = productStandardDetailGroups.get(productStandardId);
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }

            for (IObjectData detail : detailList) {
                // 创建新的IObjectData实例作为深拷贝
                IObjectData detailCopy = ObjectDataExt.of(detail).copy();

                // 生成新的detailId，使用项目id + 当前id的组合
                String originalId = detail.getId();
                String newDetailId = StringUtils.joinWith("_",projectId,productStandardId,  originalId);
                detailCopy.setId(newDetailId);

                // 设置关联关系
                detailCopy.set(ProductItemStandardDetailFields.PRODUCT_ITEM, productStandardId);
                // 设置关联的标准详情id
                detailCopy.set(DataReport2PublicFieldsConstants.STANDARD_DETAIL_ID, newDetailId);
                // 产品或物料的id
                detailCopy.set(DataReport2PublicFieldsConstants.PRODUCT_OR_MATERIAL_STANDARD_FIELD, productStandardId);
                //项目的id
                detailCopy.set(DataReport2PublicFieldsConstants.PROJECT_STANDARD_FIELD, projectId);

                // 获取主数据并设置相关字段
                IObjectData productItemStandard = productItemStandardMap.get(productStandardId);
                if (productItemStandard != null) {
                    String waysAchieveStandard = productItemStandard.get(ProductItemStandardFields.WAYS_ACHIEVE_STANDARD, String.class);
                    if (StringUtils.isNotBlank(waysAchieveStandard)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.ACHIEVEMENT_WAY, waysAchieveStandard);
                    }
                }

                // 获取项目标准数据并设置相关字段
                IObjectData projectStandard = projectStandardMap.get(projectId);
                if (projectStandard != null) {
                    String displayFormat = projectStandard.get(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, String.class);
                    if (StringUtils.isNotBlank(displayFormat)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, displayFormat);
                    }

                    String displayLevel = projectStandard.get(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, String.class);
                    if (StringUtils.isNotBlank(displayLevel)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, displayLevel);
                    }

                    String ruleGroupId = projectStandard.get(DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_ID, String.class);
                    if (StringUtils.isNotBlank(ruleGroupId)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_ID, ruleGroupId);
                    }
                    String ruleGroupApiName = projectStandard.get(DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_APINAME, String.class);
                    if (StringUtils.isNotBlank(ruleGroupApiName)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_APINAME, ruleGroupApiName);
                    }
                    //设置settingType
                    String settingType = projectStandard.get(ProjectStandardsFields.SETTING_TYPE, String.class);
                    if (StringUtils.isNotBlank(settingType)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.PROJECT_STANDARD_SETTING_TYPE, settingType);
                    }
                }

                // 将深拷贝的数据放入新map
                newDetailMap.put(newDetailId, detailCopy);
            }

        }
        // 如果有数据，则放入mainAndDetailObjectData
        if (!newDetailMap.isEmpty()) {
            String newKey = ProductItemStandardDetailFields.API_NAME + DataReport2PublicFieldsConstants.DATA_REPORT_STANDARD_DETAIL_SUFFIX;
            mainAndDetailObjectData.put(newKey, newDetailMap);
        }

    }

    /**
     * 处理材料标准详情数据
     *
     * @param materialStandardDetailData 材料标准详情数据
     * @param childToParentMap           子对象到父对象的映射
     * @param standardMainApiName
     * @param mainAndDetailObjectData    主和详情对象数据
     */
    private void processMaterialStandardDetails(String standardMainApiName, Map<String, Map<String, IObjectData>> mainAndDetailObjectData,
                                                Map<String, String> projectIdAndMaterialStandardIdMap) {
        Map<String, IObjectData> materialStandardMap = mainAndDetailObjectData.get(MaterialStandardRequiremFields.API_NAME);
        Map<String, IObjectData> projectStandardMap = mainAndDetailObjectData.get(StandardFieldMappingUtil.getFieldMappingByMainApiName(standardMainApiName).get(ProjectStandardsFields.API_NAME));
        Map<String, IObjectData> materialStandardDetailMap = mainAndDetailObjectData.get(MaterialStandardDetailsFields.API_NAME);

        if (materialStandardMap == null || projectStandardMap == null || materialStandardDetailMap == null) {
            return;
        }

        // 通用本身虚拟字段设置
        materialStandardDetailMap.values().stream().filter(Objects::nonNull).forEach(
                data -> {
                    String quantityField = data.get(MaterialStandardDetailsFields.DISPLAY_PROJECT, String.class);
                    List subConditions = data.get(DataReport2PublicFieldsConstants.SUB_CONDITIONS, List.class);
                    if (CollectionUtils.isNotEmpty(subConditions)) {
                        data.set(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY, DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_VALUE);
                        data.set(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_VALUE, data.get(MaterialStandardDetailsFields.MATERIAL_QUANTITY));
                    } else {
                        if (StringUtils.isNotBlank(quantityField)) {
                            data.set(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY, quantityField);
                            data.set(quantityField, data.get(MaterialStandardDetailsFields.MATERIAL_QUANTITY));
                        }
                    }
                }
        );

        // 按照materialStandardId对materialStandardDetailMap进行分组
        Map<String, List<IObjectData>> materialStandardDetailGroups = materialStandardDetailMap.values().stream()
                .filter(detail -> Objects.nonNull(detail)
                        && StringUtils.isNotBlank(detail.get(MaterialStandardDetailsFields.MATERIAL_STANDARD, String.class)))
                .collect(Collectors.groupingBy(detail ->
                        detail.get(MaterialStandardDetailsFields.MATERIAL_STANDARD, String.class)));

        // 为每个detail数据创建深拷贝并构建新的映射
        Map<String, IObjectData> newDetailMap = new HashMap<>();
        // 遍历itemIdAndMaterialStandardIdMap
        for (Map.Entry<String, String> entry : projectIdAndMaterialStandardIdMap.entrySet()) {
            String projectId = entry.getKey(); // 项目标准id
            String materialStandardId = entry.getValue(); // 材料标准id

            // 获取该materialStandardId下的所有detail数据
            List<IObjectData> detailList = materialStandardDetailGroups.get(materialStandardId);
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }

            for (IObjectData detail : detailList) {
                // 创建新的IObjectData实例作为深拷贝
                IObjectData detailCopy = ObjectDataExt.of(detail).copy();

                // 生成新的detailId
                String originalId = detail.getId();
                String newDetailId = StringUtils.joinWith("_",projectId,materialStandardId,originalId);
                detailCopy.setId(newDetailId);

                // 设置关联关系
                detailCopy.set(MaterialStandardDetailsFields.MATERIAL_STANDARD, materialStandardId);
                // 设置关联的标准详情id
                detailCopy.set(DataReport2PublicFieldsConstants.STANDARD_DETAIL_ID, newDetailId);
                // 设置标准字段类型
                detailCopy.set(DataReport2PublicFieldsConstants.PRODUCT_OR_MATERIAL_STANDARD_FIELD, materialStandardId);
                detailCopy.set(DataReport2PublicFieldsConstants.PROJECT_STANDARD_FIELD, projectId);
                // 获取主数据并设置相关字段
                IObjectData materialStandard = materialStandardMap.get(materialStandardId);
                if (materialStandard != null) {
                    String waysAchieveStandard = materialStandard.get(MaterialStandardRequiremFields.WAYS_ACHIEVE_STANDARD, String.class);
                    if (StringUtils.isNotBlank(waysAchieveStandard)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.ACHIEVEMENT_WAY, waysAchieveStandard);
                    }
                }

                // 获取项目标准数据并设置相关字段
                IObjectData projectStandard = projectStandardMap.get(projectId);
                if (projectStandard != null) {
                    String displayFormat = projectStandard.get(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, String.class);
                    if (StringUtils.isNotBlank(displayFormat)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, displayFormat);
                    }

                    String displayLevel = projectStandard.get(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, String.class);
                    if (StringUtils.isNotBlank(displayLevel)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, displayLevel);
                    }

                    String ruleGroupId = projectStandard.get(DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_ID, String.class);
                    if (StringUtils.isNotBlank(ruleGroupId)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_ID, ruleGroupId);
                    }
                    String ruleGroupApiName = projectStandard.get(DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_APINAME, String.class);
                    if (StringUtils.isNotBlank(ruleGroupApiName)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.RULE_GROUP_DATA_DETAIL_APINAME, ruleGroupApiName);
                    }

                    String settingType = projectStandard.get(ProjectStandardsFields.SETTING_TYPE, String.class);
                    if (StringUtils.isNotBlank(settingType)) {
                        detailCopy.set(DataReport2PublicFieldsConstants.PROJECT_STANDARD_SETTING_TYPE, settingType);
                    }
                }

                // 将深拷贝的数据放入新map
                newDetailMap.put(newDetailId, detailCopy);
            }

        }
        // 如果有数据，则放入mainAndDetailObjectData
        if (!newDetailMap.isEmpty()) {
            String newKey = MaterialStandardDetailsFields.API_NAME + DataReport2PublicFieldsConstants.DATA_REPORT_STANDARD_DETAIL_SUFFIX;
            mainAndDetailObjectData.put(newKey, newDetailMap);
        }
    }


    /**
     * 通过陈列形式Id 汇总报告Id 查询陈列项目达成对象
     */
    public List<IObjectData> getDisplayProjectAchievementByReportId(User user, String reportId){
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(DisplayProjectAchievementFields.RELATED_REPORT, reportId)
                .build();
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(),
                DisplayProjectAchievementFields.API_NAME);
        List<IObjectData> dataList = getAllIObjectDataListByQuery(user, searchQuery, DisplayProjectAchievementFields.API_NAME);
        serviceFacade.fillSelectLabelInfo(describe, dataList);
        serviceFacade.fillObjectDataWithRefObject(describe, dataList, user);
        return dataList;
    }

    /**
     * 通过成列铺货达成查询必分销
     */
    public List<IObjectData> getDistributionByDisplaySummaryId(User user, String displaySummaryId){
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(DistributionProductsAchievedFields.RELATED_REPORT, displaySummaryId)
                .build();
        return getAllIObjectDataListByQuery(user, searchQuery, DistributionProductsAchievedFields.API_NAME);
    }

    /**
     * 通过陈列铺货达成汇总id 查询陈列形式达成总结
     */
    public List<IObjectData> getDisShapeSumByDispDistrAchSummaryId(User user, String displayDistrAchSummaryId,IObjectDescribe describe) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(SummaryDisplayAchievementFields.RELATED_REPORT, displayDistrAchSummaryId)
                .build();
        List<String> receiveFields = Lists.newArrayList(SummaryDisplayAchievementFields.DISPLAY_PHOTO, SummaryDisplayAchievementFields.RELATED_DISPLAY_ACHIEVEMENT,
                SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY,SummaryDisplayAchievementFields.SUMMARY_ISSUES,SummaryDisplayAchievementFields.ACHIEVED_RESULTS);
        List<IObjectData> resultList = getAllQueryDataListByQueryWithFields(user, searchQuery,SummaryDisplayAchievementFields.API_NAME, receiveFields).getData();
        serviceFacade.fillObjectDataWithRefObject(describe, resultList, user);
        serviceFacade.fillSelectLabelInfo(describe, resultList);

        IActionContext ctx = ActionContextExt.of(user).getContext();
        infraServiceFacade.getFileStoreService().generateNPathSignedUrl(ctx,
                AppFrameworkConfig.signedUrlFieldTypes(ctx.getEnterpriseId()), describe, resultList);
        return resultList;
    }

    /**
     * 通过举证id查询举证图片
     * @param systemUser
     * @param businessId
     * @return
     */
    public Map<String, IObjectData> getActivityProofDisplayImgIdAndBusinessSubObjMap(User systemUser, String businessId) {
        SearchQuery searchQuery = SearchQuery.builder().eq(TPMActivityProofDisplayImgFields.ACTIVITY_PROOF_ID, businessId).build();
        List<IObjectData> dataList = getAllIObjectDataListByQuery(systemUser, searchQuery, TPMActivityProofDisplayImgFields.API_NAME);
        //key 是陈列形式id value 是id
        return dataList.stream().filter(item -> Objects.nonNull(item.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID))).collect(Collectors.toMap(item -> String.valueOf(item.get(TPMActivityProofDisplayImgFields.DISPLAY_FORM_ID)), item -> item));
    }
}
