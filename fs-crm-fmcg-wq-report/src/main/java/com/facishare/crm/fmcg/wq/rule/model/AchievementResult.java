package com.facishare.crm.fmcg.wq.rule.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;

/**
 * Achievement result model
 * Contains all types of achievement result data
 */
@Data
public class AchievementResult {
    /**
     * Whether the achievement result is successful or not.
     */
    private boolean success = true;
    /**
     * Error message if the achievement result is not successful.
     */
    private String error;

    /**
     * Display project achievement results
     */
    private List<IObjectData> totalProjectAchievements = new ArrayList<>();

    /**
     * Product project achievement results
     */
    private List<IObjectData> productProjectAchievements = new ArrayList<>();
    
    /**
     * Material project achievement results
     */
    private List<IObjectData> materialProjectAchievements = new ArrayList<>();
    
    /**
     * Overall project achievement results
     */
    private List<IObjectData> overallProjectAchievements = new ArrayList<>();
    
    /**
     * Display form summary achievements
     */
    private List<IObjectData> summaryDisplayAchievements = new ArrayList<>();

    /**
     * Distribution product achievements
     */
    private List<IObjectData> distributionProductsAchievements = new ArrayList<>();

    /**
     * Display distribution achievement summary
     */
    private IObjectData displayDistrAchSummary;
    
    /**
     * Get immutable view of display project achievements
     * @return Unmodifiable list of display project achievements
     */
    public List<IObjectData> getTotalProjectAchievements() {
        return totalProjectAchievements;
    }
    
    /**
     * Get immutable view of summary display achievements
     * @return Unmodifiable list of summary display achievements
     */
    public List<IObjectData> getSummaryDisplayAchievements() {
        return summaryDisplayAchievements;
    }
    
    /**
     * Check if the result has any achievements
     * @return true if there are any achievements, false otherwise
     */
    public boolean hasAchievements() {
        return !totalProjectAchievements.isEmpty()
            || !summaryDisplayAchievements.isEmpty()
            || !distributionProductsAchievements.isEmpty();
    }
    
    /**
     * Get total achievement count
     * @return Total number of achievements
     */
    public int getTotalAchievementCount() {
        return totalProjectAchievements.size()
            + summaryDisplayAchievements.size() 
            + distributionProductsAchievements.size();
    }
    
    /**
     * Safely get display distribution achievement summary
     * @return Optional containing the summary if present, empty otherwise
     */
    public Optional<IObjectData> getOptionalDisplayDistrAchSummary() {
        return Optional.ofNullable(displayDistrAchSummary);
    }
}