package com.facishare.crm.fmcg.wq.rule.template;

import com.facishare.crm.fmcg.wq.rule.model.mapping.base.LogicalOperator;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 规则模板基类
 * 定义规则模板的基本结构，包含规则ID、名称、条件列表等
 */
@Data
public class RuleTemplate {

    /**
     * 规则模板ID
     */
    private String templateId;
    

    /**
     * 规则模板名称
     */
    private String templateName;

    /**
     * 规则模板描述
     */
    private String description;

    /**
     * 规则条件列表
     */
    private List<ConditionTemplate> conditions;

    /**
     * The logical operator for combining conditions
     */
    private LogicalOperator logicalOperator = LogicalOperator.AND;

    /**
     * 规则类型（用于区分不同业务场景的规则）
     */
    private RuleType ruleType;


//    private String expectValueField;
//
//    private Object expectedValue;

    /**
     * Rule type enum
     */
    public enum RuleType {
        PRODUCT_ITEM_STANDARD, // 产品项目标准规则
        DISPLAY_PROJECT, // 陈列项目规则
        PRODUCT_CATEGORIZATION, // 产品分类规则
        MATERIAL_STANDARD, // 物料标准规则
        DISTRIBUTION_PRODUCT // 必分销品规则
    }
    
    /**
     * 评估规则模板
     * @param data 待评估数据
     * @return 评估结果
     */
    public boolean evaluate(IObjectData data) {
        if (conditions == null || conditions.isEmpty()) {
            return true; // 没有条件意味着成功
        }
        
        if (logicalOperator == LogicalOperator.AND) {
            return conditions.stream().allMatch(condition -> condition.evaluate(data));
        } else {
            return conditions.stream().anyMatch(condition -> condition.evaluate(data));
        }
    }
}