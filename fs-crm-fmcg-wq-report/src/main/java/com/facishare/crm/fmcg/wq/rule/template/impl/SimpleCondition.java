package com.facishare.crm.fmcg.wq.rule.template.impl;

import com.facishare.crm.fmcg.wq.rule.template.ConditionTemplate;
import com.facishare.paas.metadata.api.IObjectData;

import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * 简单条件实现
 * 用于评估单一字段条件
 */
@Data
@Builder
public class SimpleCondition implements ConditionTemplate {

    /**
     * 条件ID
     */
    private String conditionId;

    /**
     * 条件名称
     */
    private String conditionName;

    /**
     * 目标字段
     */
    private String field;

    /**
     * 比较运算符
     */
    private String operator;

    /**
     * 比较值
     */
    private Object value;

    /**
     * 条件类型
     */
    private String conditionType;

    /**
     * 条件描述
     */
    private String description;


    @Override
    public boolean evaluate(IObjectData data) {
        // 获取实际值
        Object actualValue = data.get(field);
        if (actualValue == null) {
            return false;
        }
        // 比较值
        return compareValues(actualValue, value, operator);
    }

    /**
     * 比较值
     */
    private boolean compareValues(Object actualValue, Object expectedValue, String operator) {
        // 如果实际值为空，则返回false
        if (actualValue == null) {
            return false;
        }
        // 如果期望值为空，则返回true
        if (expectedValue == null) {
            return true;
        }

        boolean result;
        switch (operator) {
            case "=":
                result = expectedValue.equals(actualValue);
                break;
            case "!=":
                result = !expectedValue.equals(actualValue);
                break;
            case ">":
                result = compareValues(actualValue, expectedValue) > 0;
                break;
            case "<":
                result = compareValues(actualValue, expectedValue) < 0;
                break;
            case ">=":
                result = compareValues(actualValue, expectedValue) >= 0;
                break;
            case "<=":
                result = compareValues(actualValue, expectedValue) <= 0;
                break;
            case "contains":
                result = actualValue.toString().contains(expectedValue.toString());
                break;
            case "not contains":
                result = !actualValue.toString().contains(expectedValue.toString());
                break;
            case "in":
                result = expectedValue instanceof java.util.List && ((java.util.List<?>) expectedValue).contains(actualValue);
                break;
            case "not in":
                result = expectedValue instanceof java.util.List && !((java.util.List<?>) expectedValue).contains(actualValue);
                break;
            default:
                result = false;
                break;
        }
        return result;
    }

    /**
     * 比较值大小
     */
    private int compareValues(Object v1, Object v2) {
        if (v1 instanceof Number && v2 instanceof Number) {
            return Double.compare(((Number) v1).doubleValue(), ((Number) v2).doubleValue());
        }else if (NumberUtils.isCreatable(v1.toString()) && NumberUtils.isCreatable(v2.toString())){
            return Double.compare(Double.parseDouble(v1.toString()), Double.parseDouble(v2.toString()));
        }
        return v1.toString().compareTo(v2.toString());
    }

}