package com.facishare.crm.fmcg.wq.rule.model.mapping;

import java.util.List;

import com.facishare.crm.fmcg.wq.rule.model.mapping.base.FieldMappingConfigField;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * Tenant-level Field Mapping Configuration
 * <p>
 * This class defines mapping relationships between tenant-specific data objects.
 * It contains the configuration for mapping source fields to standard fields.
 * <p>
 * Usage example:
 * <pre>
 * TenantFieldMappingConfig config = new TenantFieldMappingConfig();
 * config.setConfigId("unique-id");
 * config.setSourceObjectApiName("CustomObject__c");
 * config.setStandardObjectApiName("StandardObject__c");
 *
 * // Add field mappings
 * List<FieldMappingConfigField> fieldMappings = new ArrayList<>();
 * FieldMappingConfigField mapping = new FieldMappingConfigField();
 * mapping.setSourceField("customField__c");
 * mapping.setTargetField("standardField__c");
 * fieldMappings.add(mapping);
 * config.setFieldMappings(fieldMappings);
 * </pre>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantFieldCustomConfig {

    /**
     * Unique identifier for this mapping configuration
     */
    private String configId;

    /**
     * Source object API name (e.g., "CustomReport__c")
     */
    private String sourceObjectApiName;

    /**
     * Standard object API name that the source will map to (e.g., "StandardObject__c")
     */
    private String standardObjectApiName;

    /**
     * List of field mappings that define how source fields map to standard fields
     */
    private List<FieldMappingConfigField> fieldMappings;
    
    /**
     * Tenant ID for which this mapping configuration applies
     */
    private String tenantId;
    

} 