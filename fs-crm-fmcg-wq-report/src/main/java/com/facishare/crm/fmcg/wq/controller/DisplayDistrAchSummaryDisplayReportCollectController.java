package com.facishare.crm.fmcg.wq.controller;

import com.facishare.appserver.checkins.model.common.IdAndName;
import com.facishare.crm.fmcg.wq.constants.DisplayDistrAchSummaryFields;
import com.facishare.crm.fmcg.wq.model.RestResult;
import com.facishare.crm.fmcg.wq.report.model.DisplayReportCollect;
import com.facishare.crm.fmcg.wq.report.model.ReportType;
import com.facishare.crm.fmcg.wq.report.service.DisplayDistrAchSummaryService;
import com.facishare.crm.fmcg.wq.report.service.DistributionReportCollectService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/03/26/ 14:54
 **/
public class DisplayDistrAchSummaryDisplayReportCollectController extends PreDefineController<DisplayReportCollect.Arg, RestResult<DisplayReportCollect.Result>> {


    private DisplayDistrAchSummaryService displayDistrAchSummaryService = SpringUtil.getContext().getBean(DisplayDistrAchSummaryService.class);
    private DistributionReportCollectService distributionReportCollectService = SpringUtil.getContext().getBean(DistributionReportCollectService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected RestResult<DisplayReportCollect.Result> doService(DisplayReportCollect.Arg arg) {

        DisplayReportCollect.Result result = new DisplayReportCollect.Result();
        result.setOpenSuccess(1);
        result.setIsNew(1);
        checkArgs(arg);
        ReportType reportType = StringUtils.isNotBlank(arg.getDataId()) ? ReportType.ACTIVITY : ReportType.DISPLAY;

        if(arg.getIsShow() == 0 && arg.getIsMustShow() == 0){
            return RestResult.<DisplayReportCollect.Result>builder().code(0).message("success").data(result).build();
        }
        String tenantId = controllerContext.getTenantId();
        User systemUser = User.systemUser(tenantId);
        // 根据报告类型获取汇总数据
        IdAndName idAndName = displayDistrAchSummaryService.buildReportIdAndApiName(systemUser, reportType,arg.getDataId(), null, arg.getActionId(), arg.getCheckinsId());
        List<IObjectData> objectDataList = displayDistrAchSummaryService.getDataByReportType(systemUser,arg.getCheckinsId(),reportType,idAndName);
        if (CollectionUtils.isEmpty(objectDataList)) {
            return RestResult.<DisplayReportCollect.Result>builder().code(-1).message("no data").data(result).build();
        }
        
        if (arg.isShow == 1) {
            // 通过业务类型过滤
            IObjectData objectData = objectDataList.stream()
                    .filter(item -> item.getRecordType().equals(reportType.getRecord()))
                    .findFirst().orElse(null);
            distributionReportCollectService.buildDisplayReportData(systemUser,objectData,result);
        }
        if (arg.isMustShow == 1) {
            IObjectData objectData = objectDataList.stream()
                    .filter(item -> item.getRecordType().equals(DisplayDistrAchSummaryFields.RECORD_DISTRIBUTION))
                    .findFirst().orElse(null);
            if(Objects.nonNull(objectData)) {
                distributionReportCollectService.buildDistributionReportData(systemUser, objectData.getId(), result);
            }
        }
        // 构建展示字段
        distributionReportCollectService.buildShowField(systemUser,result,arg.getFieldsList(),idAndName);
        return RestResult.<DisplayReportCollect.Result>builder().code(0).message("success").data(result).build();
    }
    private void checkArgs(DisplayReportCollect.Arg arg){
        if(StringUtils.isBlank(arg.getCheckinsId()) || StringUtils.isBlank(arg.getActionId())){
            throw new ValidateException("参数错误"); //ignoreI18n
        }
    }
}
