package com.facishare.crm.fmcg.wq.rule.model.mapping.base;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * Rule Field Condition Configuration
 * <p>
 * Defines a single condition based on a field value.
 */
@Data
public class RuleFieldConditionConfig {
    /**
     * Target object API name
     */
    @JSONField(name = "targetApiName")
    private String targetApiName;

    /**
     * Target field name to apply the condition to
     */
    @J<PERSON>NField(name = "targetField")
    private String targetField;

    /**
     * Source field data type
     */
    @JSONField(name = "sourceFieldType")
    private String sourceFieldType;

    /**
     * API name for lookup fields
     */
    @JSONField(name = "sourceLookUpApiName")
    private String sourceLookUpApiName;

    /**
     * List of supported field types for this condition
     */
    @JSONField(name = "supportFields")
    private List<String> supportFields;

    /**
     * Condition operator (e.g., "=", ">", "CONTAINS")
     */
    @<PERSON><PERSON><PERSON>ield(name = "operator")
    private String operator;

    /**
     * Value mapping configuration to apply before condition evaluation
     */
    @JSONField(name = "valueMappingConfig")
    private MappingConfig valueMappingConfig;

    /**
     * Key mapping configuration for field names
     */
    @JSONField(name = "keyMappingConfig")
    private MappingConfig keyMappingConfig;

    /**
     * Aggregation configuration for multi-value fields
     */
    @JSONField(name = "aggregationConfig")
    private AggregationConfig aggregationConfig;
    @JSONField(name = "targetFieldName")
    private String targetFieldName;
    /**
     * 期望值字段
     */
    @JSONField(name = "expectValueField")
    private Boolean expectValueField;

    /**
     * Create a simple equals condition
     *
     * @param field Field name to check
     * @param value Value to compare against
     * @return The configured condition
     */
    public static RuleFieldConditionConfig createEqualsCondition(String field, Object value) {
        RuleFieldConditionConfig condition = new RuleFieldConditionConfig();
        condition.setTargetField(field);
        condition.setOperator("=");

        MappingConfig valueMapping = new MappingConfig();
        valueMapping.setMappingType(MappingType.DEFAULT_VALUE);
        valueMapping.setDefaultValue(value.toString());
        condition.setValueMappingConfig(valueMapping);

        return condition;
    }
} 