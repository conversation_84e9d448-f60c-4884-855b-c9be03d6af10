package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.DisplayProjectsFields;
import com.facishare.crm.fmcg.wq.dao.DataReportStandardDao;
import com.facishare.crm.fmcg.wq.exception.CheckinsErrorCode;
import com.facishare.crm.fmcg.wq.exception.CheckinsException;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantFieldCustomConfig;
import com.facishare.crm.fmcg.wq.rule.util.CheckStandardDetailArgsUtils;
import com.facishare.crm.fmcg.wq.rule.util.FieldMappingConfigManager;
import com.facishare.paas.appframework.core.predef.action.StandardAsyncBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;


@Slf4j
public class DisplayProjectsAsyncBulkInvalidAction extends StandardAsyncBulkInvalidAction {
    DataReportStandardDao dataReportStandardDao = SpringUtil.getContext().getBean(DataReportStandardDao.class);
    FieldMappingConfigManager fieldMappingConfigManager = SpringUtil.getContext().getBean(FieldMappingConfigManager.class);


    @Override
    protected void before(StandardBulkInvalidAction.Arg arg) {
        List<IObjectData> displayProjects = dataReportStandardDao.findByIdsIncludeInvalid(actionContext.getTenantId(), DisplayProjectsFields.API_NAME,arg.getDataIds());
        List<TenantFieldCustomConfig> teantFieldMapping = fieldMappingConfigManager.getTeantFieldMapping(actionContext.getTenantId());
        displayProjects.forEach(o-> CheckStandardDetailArgsUtils.checkDisplayProjectInvalid(o,teantFieldMapping));

    }
}
