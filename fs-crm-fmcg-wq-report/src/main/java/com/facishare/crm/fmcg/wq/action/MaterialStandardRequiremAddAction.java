package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.rule.util.CheckStandardDetailArgsUtils;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MaterialStandardRequiremAddAction extends FmcgSkipPermissionAddAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String tenantId = actionContext.getTenantId();
        CheckStandardDetailArgsUtils.checkMaterialItemStandardArgs(arg);
    }
}
