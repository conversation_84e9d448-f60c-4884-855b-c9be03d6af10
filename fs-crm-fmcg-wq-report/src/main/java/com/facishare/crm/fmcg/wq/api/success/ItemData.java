package com.facishare.crm.fmcg.wq.api.success;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface ItemData {
    @Data
    @ToString
    class Arg implements Serializable {

    }

    @Data
    @ToString
    class Result implements Serializable {

        private List<Item> data;
        private List<Item> sceneData;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Item{
        private String id;  //


        private String name;  //
    }
}
