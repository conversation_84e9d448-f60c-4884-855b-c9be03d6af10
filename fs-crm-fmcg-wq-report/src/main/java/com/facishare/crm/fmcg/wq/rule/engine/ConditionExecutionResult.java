package com.facishare.crm.fmcg.wq.rule.engine;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 条件执行结果
 * 用于记录规则中每个条件的执行结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConditionExecutionResult {
    
    /**
     * 条件ID
     */
    private String conditionId;
    
    /**
     * 条件名称
     */
    private String conditionName;
    
    /**
     * 条件类型
     */
    private String conditionType;
    
    /**
     * 字段名
     */
    private String field;
    
    /**
     * 操作符
     */
    private String operator;
    
    /**
     * 期望值
     */
    private Object expectedValue;
    
    /**
     * 实际值
     */
    private Object actualValue;
    
    /**
     * 是否执行成功
     */
    private boolean success;
    
    /**
     * 执行结果消息
     */
    private String message;
    
    /**
     * 执行时间（毫秒）
     */
    private long executionTime;

    private Boolean  expectValueField;
} 