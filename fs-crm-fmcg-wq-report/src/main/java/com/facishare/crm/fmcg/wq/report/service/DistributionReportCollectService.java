package com.facishare.crm.fmcg.wq.report.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.checkins.model.db.ObjectDetailEntity;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.metadata.util.ObjectConvertUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.facishare.appserver.checkins.model.common.CheckinsFields;
import com.facishare.appserver.checkins.model.common.IdAndName;
import com.facishare.crm.fmcg.wq.dao.DataReportStandardDao;
import com.facishare.crm.fmcg.wq.report.model.DisplayReportCollect;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DistributionReportCollectService {
    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected DataReportStandardDao dataReportStandardDao;

    public void buildDisplayReportData(User systemUser, IObjectData objectData,DisplayReportCollect.Result result) {
        // 如果分了陈列形式的 就不展示这数据
        if(Objects.isNull(objectData) || DisplayDistrAchSummaryFields.METHODS_DISPLAY_STANDARDS_Options_2.equals(objectData.get(DisplayDistrAchSummaryFields.METHODS_DISPLAY_STANDARDS))){
            result.setShow(0);
            return;
        }
        List<DisplayReportCollect.SimpleData> numbers = Lists.newArrayList();
        List<IObjectData> displayProjectAllDataList = dataReportStandardDao.getDisplayProjectAchievementByReportId(systemUser, objectData.getId());
        // 是否达标
        boolean isAchieved = DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS_Options_1.equals(objectData.get(DisplayDistrAchSummaryFields.ACHIEVEMENT_STATUS,String.class));
        for (IObjectData item : displayProjectAllDataList) {
            DisplayReportCollect.SimpleData simpleData = new DisplayReportCollect.SimpleData();
            String projectName = (String) item.get(DisplayProjectAchievementFields.PROJECT_NAME__R);
            // 层级
            String level = (String) item.get(DisplayProjectAchievementFields.LEVEL);
            if (Objects.nonNull(level) && level.compareTo("0") > 0) {
                projectName = projectName + "_" + item.get(DisplayProjectAchievementFields.LEVEL_NAME);
            }
            simpleData.setName(projectName);
            simpleData.setActual((String) item.get(DisplayProjectAchievementFields.ACTUAL_PROJECT_QUANTITY));
            simpleData.setTarget((String) item.get(DisplayProjectAchievementFields.REQUIRED_PROJECT_QUANTITY));
            numbers.add(simpleData);
        }
        result.setUpToPar(isAchieved ? 1 : 0);
        result.setNumbers(numbers);
        result.setShow(1);
    }

    public void buildDistributionReportData(User systemUser, String reportSummaryId,DisplayReportCollect.Result result) {
        List<IObjectData> distributionAllDataList = dataReportStandardDao.getDistributionByDisplaySummaryId(systemUser, reportSummaryId);
        // 目标标准
        int targetStandard = 0;
        // 实际达成
        int actualStandard = 0;
        for (IObjectData item : distributionAllDataList) {
            Object standardValue = item.get(DistributionProductsAchievedFields.STANDARD_VALUE);
            targetStandard += Objects.isNull(standardValue) ? 0 : Integer.parseInt((String) standardValue);
            List<String> actualStandardList = (List<String>) item.get(DistributionProductsAchievedFields.ACTUAL_STANDARD);
            actualStandard += CollectionUtils.isEmpty(actualStandardList) ? 0 : actualStandardList.size();
        }
        result.setSkuNum(targetStandard);
        result.setActualNum(actualStandard);
        result.setMustShow(1);
    }


    public void buildShowField(User systemUser, DisplayReportCollect.Result result, List<String> fieldsList,IdAndName idAndName) {
        try {
            SearchQuery searchQuery = SearchQuery.builder().eq(BaseField.id.getApiName(), idAndName.getId()).build();
            List<IObjectData> iObjectDataList = dataReportStandardDao.getAllIObjectDataListByQuery(systemUser, searchQuery, idAndName.getApiName());
            if (CollectionUtils.isEmpty(iObjectDataList)) {
                return;
            }
            IObjectDescribe describe = serviceFacade.findObject(systemUser.getTenantId(),idAndName.getApiName());
            serviceFacade.fillObjectDataWithRefObject(describe,iObjectDataList,systemUser);
            serviceFacade.fillSelectLabelInfo(describe,iObjectDataList);
            serviceFacade.fillDepartmentInfo(describe,iObjectDataList,systemUser);
            serviceFacade.fillUserInfo(describe,iObjectDataList,systemUser);
            serviceFacade.fillCountryAreaLabel(describe,iObjectDataList);
            IObjectData iObjectData = iObjectDataList.get(0);
            Map<String, IFieldDescribe> fieldMap = describe.getFieldDescribeMap();
            Map<String, BigDecimal> countFields = Maps.newHashMap();
            Map<String, JSONArray> imageFields = Maps.newHashMap();

            result.setFieldsShow(Lists.newArrayList());
            for (String field : fieldsList) {
                CheckinsFields checkinsFields = new CheckinsFields();
                IFieldDescribe fieldDesc = fieldMap.get(field);
                if (Objects.nonNull(fieldDesc)) {
                    checkinsFields.setFieldLabel(fieldDesc.getLabel());
                    checkinsFields.setType(fieldDesc.getType());
                    checkinsFields.setFieldApiName(field);
                    checkinsFields.setFieldValue(ObjectUtils.transitionField(checkinsFields, iObjectData, fieldMap.get(field)));
                    result.getFieldsShow().add(checkinsFields);
                    if (Lists.newArrayList("number", "count", "currency").contains(checkinsFields.getType())) {//求和字段
                        if (StringUtils.isNotEmpty(checkinsFields.getFieldValue().toString())) {
                            countFields.put(field, countFields.computeIfAbsent(field, o -> BigDecimal.ZERO).add(new BigDecimal(checkinsFields.getFieldValue().toString())));
                        }
                    } else if ("image".equals(checkinsFields.getType())) {
                        if (StringUtils.isNotEmpty(checkinsFields.getFieldValue().toString())) {
                            imageFields.computeIfAbsent(field, o -> new JSONArray()).addAll(JSONArray.parseArray(checkinsFields.getFieldValue().toString()));
                        }
                    }
                }
            }
            if (MapUtils.isNotEmpty(countFields)) {
                for (String field : countFields.keySet()) {
                    String value = (String) iObjectData.get(field);
                    if (StringUtils.isNotEmpty(value)) {
                        countFields.put(field, countFields.computeIfAbsent(field, o -> BigDecimal.ZERO).add(new BigDecimal(value)));
                    }
                }
                imageFields.keySet().forEach(o -> {
                    String value = JSONObject.toJSONString(iObjectData.get(o));
                    if (Objects.nonNull( iObjectData.get(o))) {
                        imageFields.get(o).addAll(JSONArray.parseArray(value));
                    }
                });
                for (CheckinsFields fields : result.getFieldsShow()) {
                    if (countFields.containsKey(fields.getFieldApiName())) {
                        fields.setFieldValue(countFields.get(fields.getFieldApiName()).toString());
                    }
                    if (imageFields.containsKey(fields.getFieldApiName())) {
                        fields.setFieldValue(imageFields.get(fields.getFieldApiName()).toJSONString());
                    }
                }
            }
        }catch (Exception e){
            log.error("handleShowFields is error",e);
        }
    }
}
