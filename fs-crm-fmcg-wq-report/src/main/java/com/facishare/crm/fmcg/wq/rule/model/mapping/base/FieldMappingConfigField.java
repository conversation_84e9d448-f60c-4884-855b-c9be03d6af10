package com.facishare.crm.fmcg.wq.rule.model.mapping.base;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.util.HashSet;
import java.util.Set;

/**
 * Field Mapping Configuration
 * <p>
 * Defines how a single field from source data maps to a standard field.
 * Extends RuleFieldConditionConfig to allow this mapping to also be used in rule conditions.
 */
@Data
public class FieldMappingConfigField extends RuleFieldConditionConfig {
    /**
     * Source field name from the source object
     * Can be:
     * - A direct field name (e.g., "amount__c")
     * - A SpEL expression starting with # (e.g., "#data.get('amount__c') * 100")
     */
    private String sourceField;

    /**
     * Source field data type (e.g., "TEXT", "NUMBER")
     *
     * @see FieldType
     */
    private String sourceFieldType;

    /**
     * Create a simple direct field mapping
     *
     * @param sourceField     Source field name
     * @param targetField     Target field name
     * @param sourceFieldType Source field type
     * @return The configured field mapping
     */
    public static FieldMappingConfigField createDirectMapping(String sourceField, String targetField,
                                                              String sourceFieldType) {
        FieldMappingConfigField mapping = new FieldMappingConfigField();
        mapping.setSourceField(sourceField);
        mapping.setTargetField(targetField);
        mapping.setSourceFieldType(sourceFieldType);
        return mapping;
    }
/**
 * Create a FieldMappingConfigField from a RuleFieldConditionConfig instance with additional source field details.
 *
 * @param config The RuleFieldConditionConfig to copy properties from.
 * @param sourceField The source field name to be set.
 * @param sourceFieldType The source field type to be set.
 * @return A new FieldMappingConfigField instance with properties copied from the input and additional source field details.
 */
public static FieldMappingConfigField createFromRuleFieldConditionConfig(RuleFieldConditionConfig config, FieldMappingConfigField sourceField, String sourceFieldType) {
    FieldMappingConfigField mapping = new FieldMappingConfigField();
    // BeanUtils.copyProperties(config, mapping);
    //改成深拷贝 json 转换
    mapping = JSON.parseObject(JSON.toJSONString(config), FieldMappingConfigField.class);
    if (sourceField.getAggregationConfig() != null && mapping.getAggregationConfig() != null) {
        mapping.getAggregationConfig().setType(sourceField.getAggregationConfig().getType());
    }
    // Copy all non-null properties from sourceField to override
//    BeanUtils.copyProperties(sourceField, mapping, getNullPropertyNames(sourceField));
    mapping.setSourceField(sourceField.getSourceField());
    mapping.setSourceFieldType(sourceFieldType);
    if (StringUtils.isNotBlank(sourceField.getSourceLookUpApiName())){
        mapping.setSourceLookUpApiName(sourceField.getSourceLookUpApiName());
    }
    return mapping;
}
/**
 * Get array of property names that have null values in the given object.
 *
 * @param source The source object to check for null properties
 * @return Array of property names that have null values
 */
//private static String[] getNullPropertyNames(Object source) {
//    final BeanWrapper src = new BeanWrapperImpl(source);
//    java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();
//
//    Set<String> emptyNames = new HashSet<>();
//    for(java.beans.PropertyDescriptor pd : pds) {
//        Object srcValue = src.getPropertyValue(pd.getName());
//        if (srcValue == null) {
//            emptyNames.add(pd.getName());
//        }
//    }
//
//    String[] result = new String[emptyNames.size()];
//    return emptyNames.toArray(result);
//}

} 