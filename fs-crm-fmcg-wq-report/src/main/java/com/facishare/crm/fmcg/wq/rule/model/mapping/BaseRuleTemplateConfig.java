package com.facishare.crm.fmcg.wq.rule.model.mapping;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleConditionGroup;

import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleFieldConditionConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Base Rule Generation Configuration
 * <p>
 * Defines the basic configuration for rule generation that is not tenant-specific.
 * This serves as a template that can be used to create tenant-specific rule configurations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseRuleTemplateConfig {
    
    /**
     * Unique identifier for this rule template
     */
    private String templateId;

    /**
     * Filter rules that determine which data records to process
     */
    private RuleConditionGroup filterRules;

    /**
     * Match rules that determine if a record matches the criteria
     */
    private RuleConditionGroup matchRules;

    /**
     * Achievement rules that calculate result values based on matched data
     */
    private RuleConditionGroup achievementRules;


    /**
     * 仅用于格式化字段，不参与计算逻辑。例如：格式化金额为千分位显示等场景
     */
    private List<RuleFieldConditionConfig> onlyFormatFields;

    /**
     * Rule priority (higher numbers indicate higher priority)
     */
    private int priority;

    /**
     * Human-readable description of this rule configuration
     */
    private String description;
    
    /**
     * Creates a copy of this base rule generation config
     * 
     * @return A deep copy of this base rule generation config
     */
    public BaseRuleTemplateConfig copy() {
        return JSON.parseObject(JSON.toJSONString(this), BaseRuleTemplateConfig.class);
    }

} 