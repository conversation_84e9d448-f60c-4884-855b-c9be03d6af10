package com.facishare.crm.fmcg.wq.util;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.FieldMappingConfigField;
import com.github.autoconf.ConfigFactory;
import org.apache.commons.lang3.StringUtils;

/**
 * 标准字段映射工具类
 * 用于处理标准主对象和子对象之间的字段映射关系
 */
public class StandardFieldMappingUtil {
    
    // 标准主对象和子对象之间的映射关系
    private static final Map<String, Map<String, String>> STANDARD_FIELD_MAPPING = new HashMap<>();
    // 业务类型和标准主对象之间的映射关系
    private static final Map<String, String> RECORD_TYPE_AND_MAIN_OBJECT_MAPPING = new HashMap<>();
    static {
        // 成功门店范围映射
        Map<String, String> successfulStoreRangeMap = new HashMap<>();
        // 标准详情ApiName
        successfulStoreRangeMap.put(ProjectStandardsFields.API_NAME, ProjectStandardsFields.API_NAME);
        // 层级
        successfulStoreRangeMap.put(
                DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, ProjectStandardsFields.DISPLAY_LEVEL);
        // 陈列形式
        successfulStoreRangeMap.put(
                DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, ProjectStandardsFields.DISPLAY_FORMAT);
        // 产品
        successfulStoreRangeMap.put(
                ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS, ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS);
        // 物料
        successfulStoreRangeMap.put(
                ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS, ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS);
        STANDARD_FIELD_MAPPING.put(SuccessfulStoreRangeFields.API_NAME, successfulStoreRangeMap);

        // TPM活动协议映射
        Map<String, String> tpmActivityAgreementMap = new HashMap<>();
        // 标准详情ApiName
        tpmActivityAgreementMap.put(ProjectStandardsFields.API_NAME, TPMActivityAgreementDetailFields.API_NAME);
        // 陈列形式
        tpmActivityAgreementMap.put(
                DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, TPMActivityAgreementDetailFields.DISPLAY_FORM_ID);
        // 产品
        tpmActivityAgreementMap.put(
                ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS, TPMActivityAgreementDetailFields.PRODUCT_ITEM_STANDARD_ID);
        // 物料
        tpmActivityAgreementMap.put(
                ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS, TPMActivityAgreementDetailFields.MATERIAL_STANDARD_REQUIREM_ID);
        STANDARD_FIELD_MAPPING.put(TPMActivityAgreementFields.API_NAME, tpmActivityAgreementMap);

        // TPM活动映射
        Map<String, String> tpmActivityMap = new HashMap<>();
        // 标准详情ApiName
        tpmActivityMap.put(ProjectStandardsFields.API_NAME, TPMActivityDetailFields.API_NAME);
        // 陈列形式
        tpmActivityMap.put(
                DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_DISPLAY_FORMAT, TPMActivityDetailFields.DISPLAY_FORM_ID);
        // 产品
        tpmActivityMap.put(
                ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS, TPMActivityDetailFields.PRODUCT_ITEM_STANDARD_ID);
        // 物料
        tpmActivityMap.put(
                ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS, TPMActivityDetailFields.MATERIAL_STANDARD_REQUIREM_ID);
        STANDARD_FIELD_MAPPING.put(TPMActivityFields.API_NAME, tpmActivityMap);
        //陈列标准
        RECORD_TYPE_AND_MAIN_OBJECT_MAPPING.put(DisplayDistrAchSummaryFields.RECORD_DISPLAY, SuccessfulStoreRangeFields.API_NAME);
        //分销标准
        RECORD_TYPE_AND_MAIN_OBJECT_MAPPING.put(DisplayDistrAchSummaryFields.RECORD_DISTRIBUTION, SuccessfulStoreRangeFields.API_NAME);
        //活动举证
        RECORD_TYPE_AND_MAIN_OBJECT_MAPPING.put(DisplayDistrAchSummaryFields.RECORD_ACTIVITY, TPMActivityFields.API_NAME);
    }

    
    /**
     * 根据主对象API名称获取字段映射关系
     * 
     * @param mainApiName 主对象API名称
     * @return 字段映射关系，如果不存在则返回空Map
     */
    public static Map<String, String> getFieldMappingByMainApiName(String mainApiName) {
        return STANDARD_FIELD_MAPPING.getOrDefault(mainApiName, new HashMap<>());
    }
    /**
     * 通过结果业务类型获取主对象API名称
     * @param recordType 结果业务类型
     * @return 主对象API名称
     */
    public static String getMainObjectApiName(String recordType) {
        return RECORD_TYPE_AND_MAIN_OBJECT_MAPPING.get(recordType);
    }
    /**
     * 通过结果业务类型获取字段映射关系
     * @param recordType 结果业务类型
     * @return 字段映射关系
     */
    public static Map<String, String> getFieldMappingByRecordType(String recordType) {
        return STANDARD_FIELD_MAPPING.getOrDefault(getMainObjectApiName(recordType), new HashMap<>());
    }
    
    /**
     * 获取所有标准字段映射关系
     * 
     * @return 所有标准字段映射关系
     */
    public static Map<String, Map<String, String>> getAllFieldMappings() {
        return STANDARD_FIELD_MAPPING;
    }
} 