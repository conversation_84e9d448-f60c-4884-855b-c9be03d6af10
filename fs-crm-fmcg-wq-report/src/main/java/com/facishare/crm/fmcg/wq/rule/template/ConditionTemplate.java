package com.facishare.crm.fmcg.wq.rule.template;

import com.facishare.paas.metadata.api.IObjectData;

/**
 * 条件模板接口
 * 采用组合模式设计，允许条件的嵌套和组合
 */
public interface ConditionTemplate {

    /**
     * 获取条件ID
     * @return 条件ID
     */
    String getConditionId();

    /**
     * 获取条件名称
     * @return 条件名称
     */
    String getConditionName();

    /**
     * 评估条件
     * @param data 待评估数据
     * @return 评估结果
     */
    boolean evaluate(IObjectData data);

    /**
     * 获取条件类型
     * @return 条件类型
     */
    String getConditionType();

    /**
     * 获取条件描述
     * @return 条件描述
     */
    String getDescription();
} 