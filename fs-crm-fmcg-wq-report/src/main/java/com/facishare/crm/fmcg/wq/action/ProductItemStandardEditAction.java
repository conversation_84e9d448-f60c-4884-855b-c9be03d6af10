package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.crm.fmcg.wq.rule.util.CheckStandardDetailArgsUtils;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ProductItemStandardEditAction extends FmcgSkipPermissionEditAction {


    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String tenantId = actionContext.getTenantId();
        CheckStandardDetailArgsUtils.checkProductItemStandardArgs(arg);
    }
}
