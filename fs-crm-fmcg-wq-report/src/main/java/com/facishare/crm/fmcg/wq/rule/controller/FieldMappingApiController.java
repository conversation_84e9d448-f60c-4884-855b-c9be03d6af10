package com.facishare.crm.fmcg.wq.rule.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.DataReport2PublicFieldsConstants;
import com.facishare.crm.fmcg.wq.constants.DisplayDistrAchSummaryFields;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeFields;
import com.facishare.crm.fmcg.wq.dao.AccountDao;
import com.facishare.crm.fmcg.wq.dao.BaseDaoInterface;
import com.facishare.crm.fmcg.wq.dao.DataReportResultDao;
import com.facishare.crm.fmcg.wq.dao.DataReportStandardDao;
import com.facishare.crm.fmcg.wq.model.RestResult;
import com.facishare.crm.fmcg.wq.rule.generator.RuleMappingFactory;
import com.facishare.crm.fmcg.wq.rule.generator.StandardRuleConfigAdapter;
import com.facishare.crm.fmcg.wq.rule.model.AchievementResult;
import com.facishare.crm.fmcg.wq.rule.model.mapping.BaseRuleTemplateConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantStandardRuleTemplate;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleFieldConditionConfig;
import com.facishare.crm.fmcg.wq.rule.service.DataProcessService;
import com.facishare.crm.fmcg.wq.rule.service.RuleService;
import com.facishare.crm.fmcg.wq.rule.util.FieldMappingConfigManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.github.autoconf.ConfigFactory;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 字段映射API控制器
 * <p>
 * 提供字段映射相关的REST API
 */
@RestController
@RequestMapping("/api/field-mapping")
@Slf4j
public class FieldMappingApiController {


    @Autowired
    private StandardRuleConfigAdapter standardRuleConfigAdapter;
    @Autowired
    private FieldMappingConfigManager fieldMappingConfigManager;
    @Autowired
    private RuleMappingFactory ruleMappingFactory;
    @Autowired
    private DataReportStandardDao dataReportStandardDao;
    @Autowired
    private DataReportResultDao dataReportResultDao;
    @Autowired
    private AccountDao accountDao;
    @Autowired
    private RuleService ruleService;

    @Autowired
    private BaseDaoInterface baseDao;
    @Autowired
    private DataProcessService dataProcessService;


    /**
     * 获取租户字段映射规则配置
     *
     * @param tenantId              租户ID
     * @param standardObjectApiName 标准对象API名称
     * @param queryDataMap          预查询数据Map，用于替代数据库查询 (key: 对象API名称, value: 数据列表)
     * @return 规则字段条件配置列表
     */
    @PostMapping(value = "/rules/{tenantId}/{standardObjectApiName}", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    public ResponseEntity<List<RuleFieldConditionConfig>> getTenantFieldMappingRuleConfig(
            @PathVariable String tenantId,
            @PathVariable String standardObjectApiName,
            @RequestBody(required = false) Map<String, List<Map<String, Object>>> queryDataMap) {
        try {
            // 获取基础规则配置
            BaseRuleTemplateConfig baseRuleConfig =
                    standardRuleConfigAdapter.getBaseRuleTemplateConfigCopy(standardObjectApiName);

            if (baseRuleConfig == null) {
                log.warn("No standard rule configuration found for {}", standardObjectApiName);
                return ResponseEntity.notFound().build();
            }

            // 创建租户规则配置
            TenantStandardRuleTemplate tenantStandardRuleTemplate = standardRuleConfigAdapter.getTenantStandardRuleTemplate(tenantId, baseRuleConfig, queryDataMap);

            // 获取租户映射规则
            List<RuleFieldConditionConfig> ruleFieldConditionConfigs = standardRuleConfigAdapter.getRuleFieldsFromTenantStandardRuleTemplate(tenantStandardRuleTemplate,standardObjectApiName);
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8"))
                    .body(ruleFieldConditionConfigs);
        } catch (Exception e) {
            log.error("Error retrieving tenant field mapping rule config for tenant {} and object {}", tenantId, standardObjectApiName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取数据报告到字段映射配置
     *
     * @param reportId 报告ID
     * @return 数据报告到字段映射配置
     */
    @GetMapping(value = "/rules/getConfig", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    public ResponseEntity<String> getConfig() {
        String s = ConfigFactory.getConfig("checkins-v2-config").get("dataReport2FieldMapping", "{\"83150\":[]}");
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8"))
                .body(s);
    }

    /**
     * 保存配置请求参数
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaveConfigRequest {
        private String tenantId;
        private String config;
    }

    /**
     * 保存配置响应结果
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaveConfigResponse {
        private boolean success;
        private String message;
    }

    /**
     * 获取指定租户的字段映射配置
     *
     * @param configRequest 配置请求
     * @return 配置响应
     */
    @PostMapping(value = "/getConfig", produces = MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8")
    public ResponseEntity<Map<String, String>> getFieldMappingConfig(@RequestBody SaveConfigRequest configRequest) {
        try {
            String tenantId = configRequest.getTenantId();
            if (tenantId == null || tenantId.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            Map<String, String> result = new HashMap<>();

            // 从配置中获取字段映射数据
            com.github.autoconf.api.IConfig config = com.github.autoconf.ConfigFactory.getConfig("checkins-v2-config");
            String dataReport2FieldMapping = config.get("dataReport2FieldMapping", String.format("{\"%s\":[]}", tenantId));

            result.put("dataReport2FieldMapping", dataReport2FieldMapping);

            log.info("获取租户 {} 的字段映射配置成功", tenantId);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_VALUE + ";charset=UTF-8"))
                    .body(result);
        } catch (Exception e) {
            log.error("获取字段映射配置失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 测试字段映射规则处理
     *
     * @param tenantId        租户ID
     * @param dataMainId      数据主ID
     * @param dataMainApiName 数据主API名称
     * @param standardMainId  标准主ID
     * @return 处理结果
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TestProcessRequest {
        @NotBlank(message = "tenantId不能为空: 企业id")
        private String tenantId;
        /**
         * 上报数据id
         */
        @NotBlank(message = "dataMainId不能为空:  上报数据主对象数据id")
        private String dataMainId;
        @NotBlank(message = "dataMainApiName不能为空: 上报数据主对象数据apiName")
        private String dataMainApiName;
        @NotBlank(message = "standardMainId不能为空: 匹配的标准主对象数据id")
        private String standardMainId;
        //标准主对象数据apiName
        private String standardMainApiName = SuccessfulStoreRangeFields.API_NAME;
        /**
         * 外勤id
         */
        private String checkinsId;
        /**
         * 业务单据数据
         */
        @NotBlank(message = "businessDataApiName不能为空: 业务数据主对象数据apiName")
        private String businessDataApiName;
        @NotBlank(message = "businessDataId不能为空: 业务数据主对象数据id 唯一标识,重复会覆盖")
        private String businessDataId;
        @NotBlank(message = "accountId不能为空: 门店id")
        private String accountId;
        @NotBlank(message = "recordType不能为空: 记录类型")
        //仅支持 陈列报告 (default__c)
        //         铺货报告 (distribution_report__c)
        //         活动报告 (activity_evidence_report__c)
        @Pattern(regexp = "default__c|distribution_report__c|activity_evidence_report__c", message = "recordType仅支持default__c(陈列报告)|distribution_report__c(铺货报告)|activity_evidence_report__c(活动报告)")
        private String recordType = "default__c";
    }


    /**
     * 根据数据ID查询数据并处理字段映射规则
     *
     * @param request 请求参数
     * @return 处理结果
     */
    @ResponseBody
    @RequestMapping(value = "/test-process")
    public RestResult testProcessData(@Valid @RequestBody TestProcessRequest request) {
        AchievementResult achievementResult = dataProcessService.processData(
                request.getTenantId(),
                request.getDataMainId(),
                request.getDataMainApiName(),
                request.getStandardMainApiName(),
                request.getStandardMainId(),
                request.getCheckinsId(),
                request.getBusinessDataApiName(),
                request.getBusinessDataId(),
                request.getAccountId(),
                request.getRecordType()
        );
        return RestResult.<AchievementResult>builder()
                .code(achievementResult.isSuccess() ? 0 : -1)
                .message(achievementResult.getError())
                .data(achievementResult)
                .build();
    }

    /**
     * 检查字段是否存在并添加到Map中
     */
    private void addFieldIfExists(Map<String, Object> map, IObjectData data, String fieldName) {
        try {
            Object value = data.get(fieldName);
            if (value != null) {
                map.put(fieldName, value);
            }
        } catch (Exception e) {
            // 忽略不存在的字段
        }
    }
} 