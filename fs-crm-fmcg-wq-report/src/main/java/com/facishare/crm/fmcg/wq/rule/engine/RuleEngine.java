package com.facishare.crm.fmcg.wq.rule.engine;

import com.facishare.crm.fmcg.wq.rule.template.ConditionTemplate;
import com.facishare.crm.fmcg.wq.rule.template.RuleTemplate;
import com.facishare.crm.fmcg.wq.rule.template.impl.SimpleCondition;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Rule execution engine
 * Responsible for executing rule templates and generating execution results
 */
@Slf4j
@Component
public class RuleEngine {
    
    // Thread pool for parallel rule execution
    private final ExecutorService executorService = Executors.newWorkStealingPool();
    
    @Autowired
    private List<RuleExecutor> ruleExecutors;
    
    /**
     * Execute a rule template
     * @param template Rule template
     * @param data Data to evaluate against
     * @return Execution result
     */
    public RuleExecutionResult execute(RuleTemplate template, IObjectData data) {
        long startTime = System.currentTimeMillis();
        
        // 查找适合的执行器
        RuleExecutor executor = findExecutor(template);
        boolean isSuccess = executor.evaluate(template, data);
        
        long executionTime = System.currentTimeMillis() - startTime;
        
        return RuleExecutionResult.builder()
            .ruleId(template.getTemplateId())
            .ruleName(template.getTemplateName())
            .success(isSuccess)
            .executionTime(executionTime)
            .build();
    }
    
    /**
     * 根据模板类型查找合适的执行器
     */
    private RuleExecutor findExecutor(RuleTemplate template) {
        // 默认使用标准执行器
        return ruleExecutors.stream()
            .filter(executor -> executor.supports(template))
            .findFirst()
            .orElse(new StandardRuleExecutor());
    }
    
    /**
     * Execute multiple rule templates in parallel
     * @param templates List of rule templates
     * @param data Data to evaluate against
     * @return List of execution results
     */
    public List<RuleExecutionResult> executeParallel(List<RuleTemplate> templates, IObjectData data) {
        List<CompletableFuture<RuleExecutionResult>> futures = templates.stream()
            .map(template -> CompletableFuture.supplyAsync(() -> execute(template, data), executorService))
            .collect(Collectors.toList());
        
        return futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
    }

    /**
     * Execute a single condition
     * @param condition Condition to evaluate
     * @param data Data to evaluate against
     * @return Condition execution result
     */
    private ConditionExecutionResult executeCondition(ConditionTemplate condition, IObjectData data) {
        try {
            boolean result = condition.evaluate(data);
            return ConditionExecutionResult.builder()
                .conditionId(condition.getConditionId())
                .conditionName(condition.getConditionName())
                .success(result)
                .actualValue(extractActualValue(condition, data))
                .expectedValue(getConditionValue(condition))
                .build();
                
        } catch (Exception e) {
            log.error("Error executing condition: {}", condition.getConditionId(), e);
            return ConditionExecutionResult.builder()
                .conditionId(condition.getConditionId())
                .conditionName(condition.getConditionName())
                .success(false)
                .message(e.getMessage())
                .build();
        }
    }
    
    /**
     * Extract the actual value from data for a condition
     */
    private Object extractActualValue(ConditionTemplate condition, IObjectData data) {
        try {
            String field = getConditionField(condition);
            return field != null ? data.get(field) : null;
        } catch (Exception e) {
            log.error("Error extracting actual value for condition: {}", condition.getConditionId(), e);
            return null;
        }
    }
    
    /**
     * 获取条件的字段名
     */
    private String getConditionField(ConditionTemplate condition) {
        if (condition instanceof SimpleCondition) {
            return ((SimpleCondition) condition).getField();
        }
        return null;
    }
    
    /**
     * 获取条件的比较值
     */
    private Object getConditionValue(ConditionTemplate condition) {
        if (condition instanceof SimpleCondition) {
            return ((SimpleCondition) condition).getValue();
        }
        return null;
    }
} 