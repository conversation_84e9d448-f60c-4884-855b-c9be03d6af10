package com.facishare.crm.fmcg.wq.report.model;

import java.util.List;

import com.facishare.appserver.checkins.model.common.IdAndName;
import lombok.Data;

public interface ActivityBasicHeader {
    @Data
    class Arg{
        // 需要动作Id
        public String actionId;
        // 外勤Id
        public String checkinId;
        // 活动举证对象Id
        public String activityProofId;
    }

    @Data
    class Result{
        public List<InnerActivityBasicHeader> dataList;
    }

    @Data
    class InnerActivityBasicHeader {
        // 活动举证信息
        public IdAndName activityProofInfo;
        // 活动协议信息
        public IdAndName activityAgreementInfo;
        // 活动申请信息
        public IdAndName activityApplyInfo;
    }

}
