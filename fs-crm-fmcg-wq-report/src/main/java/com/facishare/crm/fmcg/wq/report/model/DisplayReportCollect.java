package com.facishare.crm.fmcg.wq.report.model;

import com.facishare.appserver.checkins.model.common.CheckinsFields;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/26/ 14:56
 **/
public interface DisplayReportCollect {
    @Data
    class Arg{
        // 获取报告类型 0 陈列 2 举证
        public String reportType;
        // 数据Id
        public String dataId;
        public String actionId;
        public String checkinsId;
        public List<String> fieldsList;//需要查询的字段
        public int isShow;
        public int isMustShow;
    }

    @Data
    class Result{
        /**
         * 前端这个组件没做改动，兼容老的UI，所以保持数据结构和V2项目一致
         */
        //陈列汇总
        private int show; //0不展示 1展示
        private List<SimpleData> numbers;
        private int upToPar; // 0 未达标  1 达标
        //必分销
        private int mustShow; //0 不展示  1 展示
        private int skuNum;//目标
        private int actualNum;//实际完成
        private List<CheckinsFields> fieldsShow;
        private int openSuccess;
        private int isNew;
    }

    @Data
    class SimpleDataField {
        private String label;
        private String value;
    }
    @Data
    class SimpleData {
        private String name;//名称
        private String actual;//实际
        private String target;//目标
    }
}
