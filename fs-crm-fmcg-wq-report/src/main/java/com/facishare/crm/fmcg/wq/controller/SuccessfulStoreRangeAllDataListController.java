package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSONArray;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.api.success.AllDataList;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeConstants;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class SuccessfulStoreRangeAllDataListController extends PreDefineController<AllDataList.Arg, AllDataList.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected AllDataList.Result doService(AllDataList.Arg arg) {
        AllDataList.Result result = new AllDataList.Result();
        if(arg.getMaxSize()== 0){
            arg.setMaxSize(200);
        }
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();
        searchQuery.eq(SuccessfulStoreRangeConstants.STATE, true);
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);
        searchQuery.limit(arg.getMaxSize());
        List<IObjectData> data = ObjectUtils.queryDataSimple(serviceFacade,controllerContext.getUser(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj,searchQuery.build().getSearchTemplateQuery(),describe);
        if(CollectionUtils.isNotEmpty(data)){
            IObjectDescribe itemDesc = serviceFacade.findObject(controllerContext.getTenantId(), SuccessfulStoreRangeConstants.ProjectStandardsObj);
            List<IObjectData> itemData =  serviceFacade.findDetailObjectDataList(controllerContext.getUser(),itemDesc,data);
            serviceFacade.fillObjectDataWithRefObject(itemDesc,itemData,controllerContext.getUser());
            IObjectDescribe mustProDesc = serviceFacade.findObject(controllerContext.getTenantId(), SuccessfulStoreRangeConstants.MustDistributeProductsObj);
            List<IObjectData> mustProData = serviceFacade.findDetailObjectDataList(controllerContext.getUser(),mustProDesc,data);
            Map<String, List<IObjectData>> itemMap = Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(itemData)) {
                itemMap = itemData.stream().collect(Collectors.groupingBy(o->o.get(SuccessfulStoreRangeConstants.SUCCESSFUL_STORE,String.class)));

            }
            Map<String, List<IObjectData>> mustProMap = Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(mustProData)){
                mustProMap = mustProData.stream().collect(Collectors.groupingBy(o->o.get(SuccessfulStoreRangeConstants.SUCCESSFUL_STORE,String.class)));
            }
            List<Map<String,Object>> res = Lists.newArrayList();
            for (IObjectData datum : data) {
                Map<String,Object> objectData = new HashMap<>();
                objectData.put("_id",datum.getId());
                objectData.put("sketch_map",datum.get("sketch_map"));
                List<IObjectData> item = itemMap.get(datum.getId());
                if(CollectionUtils.isNotEmpty(item)) {
                    objectData.put(SuccessfulStoreRangeConstants.ProjectStandardsObj, item.stream().map(o-> {
                        Map<String,Object> temp = new HashMap<>();;
                        temp.put("grade",o.get("quantity"));
                        temp.put("apiName",o.get(SuccessfulStoreRangeConstants.TPM_PROJECT));
                        temp.put("type", o.get(SuccessfulStoreRangeConstants.TPM_PROJECT+"__r"));
                        temp.put("typeId",o.get(SuccessfulStoreRangeConstants.TPM_PROJECT));
                        Object product = o.get(SuccessfulStoreRangeConstants.PRODUCT);
                        if (product != null && !product.toString().isEmpty()) {
                          temp.put("product", product);
                        }
                        return temp;
                    }).collect(Collectors.toList()));
                }

                List<IObjectData> mustPro = mustProMap.get(datum.getId());
                if(CollectionUtils.isNotEmpty(mustPro)) {
                    // 将单选和多选产品都聚合到多选字段上
                    Pair<List<String>, JSONArray> aggregatedData = buildAggregatedData(mustPro);
                    // 如果存在多选数据，则使用 JSONArray（Multi）
                    if(aggregatedData.getValue() != null && !aggregatedData.getValue().isEmpty()) {
                        objectData.put(SuccessfulStoreRangeConstants.MustDistributeProductsObj+"Multi", aggregatedData.getValue());
                    }
                    // 如果不存在多选数据，则使用 List<String>（单选）
                    else if(CollectionUtils.isNotEmpty(aggregatedData.getKey())) {
                        objectData.put(SuccessfulStoreRangeConstants.MustDistributeProductsObj, aggregatedData.getKey());
                    }
                }
                res.add(objectData);
            }
            result.setData(res);

        }
        return result;
    }

    private Pair<List<String>/*单选*/,JSONArray/*多选*/> buildAggregatedData(List<IObjectData> mustPro) {
        List<String> singleProductIds = Lists.newArrayList();
        JSONArray multiData = new JSONArray();
        boolean hasMultiData = mustPro.stream().anyMatch(o->o.get(SuccessfulStoreRangeConstants.PRODUCT_MULTIPLE)!=null);

        for (IObjectData datum : mustPro) {
            if (hasMultiData){
                List<String> multiProducts = (List<String>) datum.get(SuccessfulStoreRangeConstants.PRODUCT_MULTIPLE);
                if(multiProducts  == null) {
                    multiProducts = Lists.newArrayList();
                }
                //产品
                String productSingle = datum.get(SuccessfulStoreRangeConstants.PRODUCT, String.class);
                if (StringUtils.isNotBlank(productSingle)) {
                    multiProducts.add(productSingle);
                }
                if (CollectionUtils.isEmpty(multiProducts)){
                    continue;
                }

                Map<String,Object> temp = new HashMap<>();
                temp.put("minNum",datum.get(SuccessfulStoreRangeConstants.MIN_REPORT_PRODUCT,String.class,"1"));
                temp.put("scene",datum.get(SuccessfulStoreRangeConstants.DISPLAY_SCENE,String.class,"defaultV"));
                temp.put("ruleName",datum.get(SuccessfulStoreRangeConstants.MUST_RULE_NAME,String.class,""));
                temp.put("ruleGroup",datum.get(SuccessfulStoreRangeConstants.MUST_RULE_GROUP,String.class,""));
                temp.put("ruleMark",datum.get(SuccessfulStoreRangeConstants.MUST_RULE_MARK,String.class,""));
                //分类
                temp.put("proCategory",datum.get(SuccessfulStoreRangeConstants.PRODUCT_CATEGORY));
                //多选
                temp.put("proMulti",multiProducts);
                multiData.add(temp);
            }else{
                List<String> multiProducts = (List<String>) datum.get(SuccessfulStoreRangeConstants.PRODUCT_MULTIPLE);
                if(multiProducts  == null) {
                    multiProducts = Lists.newArrayList();
                }
                //产品
                String productSingle = datum.get(SuccessfulStoreRangeConstants.PRODUCT, String.class);
                if (StringUtils.isNotBlank(productSingle)) {
                    multiProducts.add(productSingle);
                }
                if (CollectionUtils.isEmpty(multiProducts)){
                    continue;
                }
                singleProductIds.addAll(multiProducts);
            }
        }

        // 去重单选数据
        List<String> distinctSingleProducts = singleProductIds.stream().distinct().collect(Collectors.toList());

        // 返回 Pair：左侧为单选数据，右侧为多选数据
        return new Pair<>(distinctSingleProducts, hasMultiData ? multiData : null);
    }
}
