package com.facishare.crm.fmcg.wq.rule.service;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.DataReport2PublicFieldsConstants;
import com.facishare.crm.fmcg.wq.rule.generator.RuleMappingFactory;
import com.facishare.crm.fmcg.wq.rule.generator.StandardRuleConfigAdapter;
import com.facishare.crm.fmcg.wq.rule.model.mapping.BaseRuleTemplateConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantFieldCustomConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantRuleRuntimeConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantStandardRuleTemplate;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.*;
import com.facishare.crm.fmcg.wq.rule.template.ConditionTemplate;
import com.facishare.crm.fmcg.wq.rule.template.RuleTemplate;
import com.facishare.crm.fmcg.wq.rule.template.impl.CompositeCondition;
import com.facishare.crm.fmcg.wq.rule.template.impl.SimpleCondition;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Rule Mapping Service
 * <p>
 * This service manages rule mappings and provides methods for creating,
 * retrieving, and applying rule mappings between source and standard objects.
 */
@Slf4j
@Service
public class RuleMappingService {

    @Autowired
    private StandardRuleConfigAdapter standardRuleConfigAdapter;

    @Autowired
    private RuleMappingFactory ruleMappingFactory;

    /**
     * Mapping configuration cache
     * Uses Guava cache with automatic expiration and size limits
     */
    private final Cache<String, TenantRuleRuntimeConfig> mappingConfigCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();


    /**
     * SpEL expression parser
     */
    private final ExpressionParser expressionParser = new SpelExpressionParser();

    /**
     * Find or create a rule mapping configuration for a source and standard object
     *
     * @param sourceObjectApiName   Source object API name
     * @param standardObjectApiName Standard object API name
     * @return Rule mapping configuration (from cache or newly created)
     */
    public TenantRuleRuntimeConfig findOrCreateRuleMapping(String tenantId, String sourceObjectApiName, String standardObjectApiName) {
        String cacheKey = tenantId + "_" + sourceObjectApiName + "_" + standardObjectApiName;

        try {
            return mappingConfigCache.get(cacheKey, () ->
                    {

                        TenantRuleRuntimeConfig ruleMapping = ruleMappingFactory.createRuleMapping(tenantId, sourceObjectApiName, standardObjectApiName);
                        if (ruleMapping != null) {
                            return ruleMapping;
                        }else{
                            return new TenantRuleRuntimeConfig();
                        }
                    }
            );
        } catch (ExecutionException e) {
            log.error("Error creating rule mapping for {} -> {}", sourceObjectApiName, standardObjectApiName, e);
            return null;
        }
    }

    /**
     * Retrieves a list of mapping configurations for a given source and standard object API names.
     * This method assumes that each tenant may have different configurations and uses the tenant ID from the user.
     *
     * @param user                  The user object containing tenant information.
     * @param sourceObjectApiName   The API name of the source object.
     * @param standardObjectApiName The API name of the standard object.
     * @return A list of matching rule mapping configurations for the tenant.
     */
    public TenantRuleRuntimeConfig findMappingConfigs(User user, String sourceObjectApiName, String standardObjectApiName) {
        String tenantId = user.getTenantId();
        return findOrCreateRuleMapping(tenantId, sourceObjectApiName, standardObjectApiName);
    }

    /**
     * Add or update a mapping configuration in the cache
     *
     * @param config The mapping configuration to cache
     */
    public void cacheMapping(TenantRuleRuntimeConfig config) {
        if (config == null || StringUtils.isEmpty(config.getConfigId())) {
            log.warn("Cannot cache null or invalid mapping configuration");
            return;
        }

        mappingConfigCache.put(config.getConfigId(), config);
        log.debug("Cached mapping configuration: {}", config.getConfigId());
    }

    /**
     * Clear all caches
     */
    public void clearAllCaches() {
        mappingConfigCache.invalidateAll();
        log.info("All caches cleared");
    }
    public void clearCache(String tenantId, String sourceObjectApiName, String standardObjectApiName) {
        mappingConfigCache.invalidate(tenantId + "_" + sourceObjectApiName + "_" + standardObjectApiName);
    }


    /**
     * Convert source data to standard format data (without processing aggregated fields)
     *
     * @param sourceDataList Source data list
     * @param fieldMappings  Field mappings
     * @return Standard format data list
     */
    public List<IObjectData> convertToStandardData(
            Map<String, IObjectData> sourceDataList,
            List<FieldMappingConfigField> fieldMappings) {

        if (sourceDataList == null || sourceDataList.isEmpty() || fieldMappings == null || fieldMappings.isEmpty()) {
            return new ArrayList<>();
        }

        // Basic mapping conversion
        List<IObjectData> basicMappedDataList = new ArrayList<>();
        try {
            for (Map.Entry<String, IObjectData> sourceData : sourceDataList.entrySet()) {
                IObjectData data = sourceData.getValue();

                IObjectData mappedData = ObjectDataExt.of(data).copy();

                // Process each field mapping
                for (FieldMappingConfigField fieldMapping : fieldMappings) {
                    try {
                        Object value = MappingConfig.convertStandardValue(
                                mappedData.get(fieldMapping.getSourceField()), fieldMapping.getValueMappingConfig());
                        String key = fieldMapping.getTargetField();
                        if (value != null) {
                            mappedData.set(key, value);
                        }
                    } catch (Exception e) {
                        log.warn("Field conversion error for field {}: {}",
                                fieldMapping.getSourceField(), e.getMessage());
                    }
                }
                if (mappedData != null) {
                    basicMappedDataList.add(mappedData);
                }
            }
        } catch (Exception e) {
            log.error("Error converting source data to standard format", e);
        }

        return basicMappedDataList;
    }


    /**
     * Create a custom rule mapping with user-defined field mappings
     *
     * @param tenantId                Tenant ID
     * @param sourceObjectApiName     Source object API name
     * @param standardObjectApiName   Standard object API name
     * @param fieldMappingDefinitions Map of source field names to target field names
     * @return The created custom rule mapping
     */
    public TenantRuleRuntimeConfig createCustomRuleMapping(
            String tenantId,
            String sourceObjectApiName,
            String standardObjectApiName,
            Map<String, String> fieldMappingDefinitions) {

        // 获取基础规则配置
        BaseRuleTemplateConfig baseRuleConfig =
                standardRuleConfigAdapter.getBaseRuleTemplateConfigCopy(standardObjectApiName);

        if (baseRuleConfig == null) {
            log.warn("No standard rule configuration found for {}", standardObjectApiName);
            return null;
        }

        // 创建租户规则配置
        TenantStandardRuleTemplate tenantStandardRuleTemplate = standardRuleConfigAdapter.getTenantStandardRuleTemplate(tenantId, baseRuleConfig);

        // 获取租户映射规则
        List<RuleFieldConditionConfig> ruleFieldConditionConfigs = standardRuleConfigAdapter.getRuleFieldsFromTenantStandardRuleTemplate(tenantStandardRuleTemplate,standardObjectApiName);
        // 创建字段映射
        List<FieldMappingConfigField> fieldMappings = new ArrayList<>();

        for (Map.Entry<String, String> entry : fieldMappingDefinitions.entrySet()) {
            String sourceField = entry.getKey();
            String targetField = entry.getValue();

            // 创建直接字段映射
            FieldMappingConfigField mapping =
                    FieldMappingConfigField.createDirectMapping(
                            sourceField, targetField, "TEXT"); // 默认为 TEXT 类型

            fieldMappings.add(mapping);
        }

        // 创建租户字段映射配置
        TenantFieldCustomConfig fieldMappingConfig = TenantFieldCustomConfig.builder()
                .configId(sourceObjectApiName + "_" + standardObjectApiName + "_" + System.currentTimeMillis())
                .tenantId(tenantId)
                .sourceObjectApiName(sourceObjectApiName)
                .standardObjectApiName(standardObjectApiName)
                .fieldMappings(fieldMappings)
                .build();

        // 创建完整配置
        TenantRuleRuntimeConfig config = TenantRuleRuntimeConfig.builder()
                .tenantFieldCustomConfig(fieldMappingConfig)
                .tenantStandardRuleTemplate(tenantStandardRuleTemplate)
                .build();

        // 缓存配置
        cacheMapping(config);

        return config;
    }

    /**
     * 合并数据列表（处理聚合字段）
     *
     * @param dataList      数据列表
     * @param aggFieldMappings 映射配置
     * @return 合并后的数据列表
     */
    public IObjectData mergeData(List<IObjectData> dataList,
                                 List<FieldMappingConfigField> aggFieldMappings) {
        if (dataList == null || dataList.isEmpty()) {
            return new ObjectData();
        }

        // 获取所有聚合配置
        List<FieldMappingConfigField> aggregationConfigs;
        if (aggFieldMappings == null) {
            aggregationConfigs = new ArrayList<>();
        }else{

                aggregationConfigs = aggFieldMappings.stream()
                .filter(config -> config.getAggregationConfig() != null)
                .collect(Collectors.toList());
        }

        // 如果没有聚合配置，直接返回原始数据
        if (aggregationConfigs.isEmpty()) {
            return dataList.get(0);
        }

        // 聚合结果列表
        IObjectData aggregatedData = new ObjectData();
        // 对dataList所有字段做聚合，key还是原来的，value 是每个value组成的数组
        Map<String, List<Object>> aggregatedAllFields = dataList.stream()
                .flatMap(data -> ObjectDataExt.of(data).toMap().entrySet().stream())
                .collect(Collectors.groupingBy(Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())));
        aggregatedAllFields.forEach((key, value) -> {
            aggregatedData.set(key, value);
        });
        // 对所有数据进行聚合计算
        for (FieldMappingConfigField aggregationConfig : aggregationConfigs) {
            // 计算聚合值
            Object aggregatedValue = aggregateValues(
                    dataList,
                    aggregationConfig);

            if (aggregatedValue != null) {
                aggregatedData.set(
                        convertKey(aggregationConfig.getTargetField(), aggregationConfig.getKeyMappingConfig()),
                        aggregatedValue);
            }
        }
        return aggregatedData;
    }

    /**
     * 聚合值
     */
    private Object aggregateValues(List<IObjectData> groupData,
                                   FieldMappingConfigField fieldMapping) {
        List<Object> values = groupData.stream()
                .map(data -> data.get(convertKey(fieldMapping.getTargetField(), fieldMapping.getKeyMappingConfig())))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Object result;
        AggregationType type = fieldMapping.getAggregationConfig().getType();
        //如果值有不是数值的话
//        type 改成 DISTINCT_COUNT
       if (StringUtils.isNotBlank(fieldMapping.getSourceLookUpApiName())){
           type = AggregationType.DISTINCT_COUNT;
       }

        switch (type) {
            case SUM:

                result = values.stream()
                        .mapToDouble(v ->  Double.parseDouble(v.toString()))
                        .sum();
                break;

            case COUNT:
                result = values.size();
                break;

            case AVG:
                result = values.stream()
                        .mapToDouble(v -> Double.parseDouble(v.toString()))
                        .average()
                        .orElse(0.0);
                break;

            case MAX:
                result = values.stream()
                        .max(Comparator.comparing(Object::toString))
                        .orElse(null);
                break;

            case MIN:
                result = values.stream()
                        .min(Comparator.comparing(Object::toString))
                        .orElse(null);
                break;

            case FIRST:
                result = values.isEmpty() ? null : values.get(0);
                break;

            case LAST:
                result = values.isEmpty() ? null : values.get(values.size() - 1);
                break;

            case CONCAT:
                result = String.join(",", values.stream()
                        .map(Object::toString)
                        .collect(Collectors.toList()));
                break;

            case MERGE_LIST:
                result = values;
                break;

            case DISTINCT:
                result = new ArrayList<>(new LinkedHashSet<>(values));
                break;

            case DISTINCT_COUNT:
                result = new ArrayList<>(new LinkedHashSet<>(values)).size();
                break;

            default:
                result = null;
                break;
        }
       log.info("values:{} type:{} fieldMapping:{} aggregatedValue: {}",values,type,fieldMapping,result);
        return result;
    }

    /**
     * 处理key映射
     */
    private String convertKey(String targetField, MappingConfig keyMappingConfig) {
        return MappingConfig.convertStandardValue(targetField, keyMappingConfig).toString();
    }


    /**
     * 生成规则模板
     */
    public List<RuleTemplate> generateRuleTemplates(TenantStandardRuleTemplate tenantRuleConfig,
                                                    IObjectData standardData) {
        List<RuleTemplate> templates = new ArrayList<>();
        // 生成过滤规则模板
        if (tenantRuleConfig.getTenantFilterRules() != null) {
            RuleTemplate filterTemplate = new RuleTemplate();
            filterTemplate.setTemplateId(standardData.get(BaseField.id.getApiName()) + "_filter");
            filterTemplate.setTemplateName(standardData.get(BaseField.name.getApiName()) + "_filter");
            filterTemplate.setDescription("用于筛选符合条件的数据"); //ignoreI18n
            filterTemplate.setLogicalOperator(
                    tenantRuleConfig.getTenantFilterRules().getOperator());
            filterTemplate.setConditions(
                    generateConditions(tenantRuleConfig.getTenantFilterRules(), standardData));
            templates.add(filterTemplate);
        }

        // 生成匹配规则模板
        if (tenantRuleConfig.getTenantMatchRules() != null) {
            RuleTemplate matchTemplate = new RuleTemplate();
            matchTemplate.setTemplateId(standardData.get(BaseField.id.getApiName()) + "_match");
            matchTemplate.setTemplateName(standardData.get(BaseField.name.getApiName()) + "_match");
            matchTemplate.setDescription("用于匹配符合条件的数据"); //ignoreI18n
            matchTemplate.setLogicalOperator(
                    tenantRuleConfig.getTenantMatchRules().getOperator());
            matchTemplate.setConditions(
                    generateConditions(tenantRuleConfig.getTenantMatchRules(), standardData));
            templates.add(matchTemplate);
        }

        // 生成达成规则模板
        if (tenantRuleConfig.getTenantAchievementRules() != null) {
            RuleTemplate achievementTemplate = new RuleTemplate();
            achievementTemplate.setTemplateId(standardData.get(BaseField.id.getApiName()) + "_achievement");
            achievementTemplate.setTemplateName(standardData.get(BaseField.name.getApiName()) + "_achievement");
            achievementTemplate.setDescription("用于评估达成情况"); //ignoreI18n
            achievementTemplate.setLogicalOperator(
                    tenantRuleConfig.getTenantAchievementRules().getOperator());
            achievementTemplate.setConditions(
                    generateConditions(tenantRuleConfig.getTenantAchievementRules(), standardData));
//            if (tenantRuleConfig.getTenantAchievementRules().getSubGroups() != null) {
//                Optional<RuleFieldConditionConfig> first = tenantRuleConfig.getTenantAchievementRules().getSubGroups().stream().flatMap(subGroup -> subGroup.getConditions().stream()).filter(condition -> Boolean.TRUE.equals(condition.getExpectValueField())).findFirst();
//                if (first.isPresent()) {
//                    achievementTemplate.setExpectValueField(first.get().getTargetField());
//                    achievementTemplate.setExpectedValue(standardData.get(first.get().getTargetField()));
//                }else{
//                    achievementTemplate.setExpectValueField(null);
//                    achievementTemplate.setExpectedValue(null);
//                }
//            }else{
//                Optional<RuleFieldConditionConfig> first = tenantRuleConfig.getTenantAchievementRules().getConditions().stream().filter(condition ->Boolean.TRUE.equals(condition.getExpectValueField())).findFirst();
//                if (first.isPresent()) {
//                    achievementTemplate.setExpectValueField(first.get().getTargetField());
//                    achievementTemplate.setExpectedValue(standardData.get(first.get().getTargetField()));
//                }else{
//                    achievementTemplate.setExpectValueField(null);
//                    achievementTemplate.setExpectedValue(null);
//                }
//            }
            templates.add(achievementTemplate);
        }

        return templates;
    }

    private List<ConditionTemplate> generateConditions(RuleConditionGroup group,
                                                       IObjectData standardData) {
        List<ConditionTemplate> conditions = new ArrayList<>();
        boolean removeLevel = false;
        // 处理直接条件
        if (group.getConditions() != null) {
            for (RuleFieldConditionConfig config : group.getConditions()) {
                ConditionTemplate condition = createCondition(config, standardData);
                if (condition != null) {
                    conditions.add(condition);
                }
            }
        }

        // 处理子条件组
        if (group.getSubGroups() != null) {
            for (RuleConditionGroup subGroup : group.getSubGroups()) {
                ConditionTemplate groupCondition = createGroupCondition(subGroup, standardData);
                conditions.add(groupCondition);
            }
        }

        return conditions;
    }

    /**
     * 创建组条件
     */
    private ConditionTemplate createGroupCondition(RuleConditionGroup group,
                                                        IObjectData standardData) {
        ConditionTemplate condition = CompositeCondition.builder()
                .logicalOperator(group.getOperator())
                .subConditions(generateConditions(group, standardData))
                .build();
        return condition;
    }

    /**
     * 创建规则条件
     */
    private ConditionTemplate createCondition(RuleFieldConditionConfig config,
                                                   IObjectData standardData) {
        Object value = standardData.get(config.getTargetField());
        if (value == null) {
            return null;
        }
        //todo 默认值

        if (config.getTargetField().equals(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL) &&
                (DataReport2PublicFieldsConstants.LEVEL_Options_anyLevel.equals(value.toString()) || DataReport2PublicFieldsConstants.LEVEL_Options_AllLevel.equals(value.toString()))) {
//            任意的 不返回条件
            return null;
        }
        ConditionTemplate condition = SimpleCondition.builder()
                .operator(config.getOperator())
                .field(config.getTargetField())
                .value(value)
                .build();
        return condition;
    }


}
