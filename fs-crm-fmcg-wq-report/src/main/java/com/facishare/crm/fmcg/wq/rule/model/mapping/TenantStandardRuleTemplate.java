package com.facishare.crm.fmcg.wq.rule.model.mapping;

import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleConditionGroup;

import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleFieldConditionConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Tenant-specific Rule Generation Configuration
 * <p>
 * This class extends the base rule generation configuration with tenant-specific data.
 * It is generated from the base rule configuration and a tenant ID.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantStandardRuleTemplate {
    
    /**
     * The tenant ID for which this rule configuration applies
     */
    private String tenantId;
    
    /**
     * Base rule generation configuration from which this was created
     */
    private BaseRuleTemplateConfig baseConfig;
    
    /**
     * Unique identifier for this tenant-specific rule configuration
     */
    private String configId;
    
    /**
     * Custom tenant-specific rule data that overrides or extends the base configuration
     */
    private RuleConditionGroup tenantFilterRules;
    
    /**
     * Custom tenant-specific match rules
     */
    private RuleConditionGroup tenantMatchRules;
    
    /**
     * Custom tenant-specific achievement rules
     */
    private RuleConditionGroup tenantAchievementRules;

    private List<RuleFieldConditionConfig> onlyFormatFields;
    
    /**
     * Rule priority specific to this tenant (overrides base priority if set)
     */
    private Integer tenantPriority;
} 