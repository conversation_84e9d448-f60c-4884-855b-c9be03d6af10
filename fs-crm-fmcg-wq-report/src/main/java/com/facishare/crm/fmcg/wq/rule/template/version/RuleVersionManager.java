package com.facishare.crm.fmcg.wq.rule.template.version;

import com.facishare.crm.fmcg.wq.rule.template.RuleTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * 规则版本管理器
 * 负责规则模板的版本管理
 */
@Slf4j
@Component
public class RuleVersionManager {

    /**
     * 规则版本映射
     * key: 规则模板ID
     * value: 该规则的版本历史
     */
    private final Map<String, List<RuleVersion>> versionMap = new ConcurrentHashMap<>();
    
    /**
     * 创建规则版本
     * @param template 规则模板
     * @param version 版本号
     * @param description 版本描述
     * @return 版本号
     */
    public String createVersion(RuleTemplate template, String version, String description) {
        RuleVersion ruleVersion = new RuleVersion();
        ruleVersion.setTemplateId(template.getTemplateId());
        ruleVersion.setVersion(version);
        ruleVersion.setDescription(description);
        ruleVersion.setCreateTime(LocalDateTime.now());
        ruleVersion.setTemplate(cloneTemplate(template));
        
        // 存储版本
        versionMap.computeIfAbsent(template.getTemplateId(), k -> new ArrayList<>())
            .add(ruleVersion);
            
        return version;
    }
    
    /**
     * 获取规则的最新版本
     * @param templateId 规则模板ID
     * @return 最新版本的规则模板
     */
    public RuleTemplate getLatestVersion(String templateId) {
        List<RuleVersion> versions = versionMap.get(templateId);
        if (versions == null || versions.isEmpty()) {
            return null;
        }
        
        return versions.stream()
            .max(Comparator.comparing(RuleVersion::getCreateTime))
            .map(RuleVersion::getTemplate)
            .orElse(null);
    }
    
    /**
     * 获取规则的指定版本
     * @param templateId 规则模板ID
     * @param version 版本号
     * @return 指定版本的规则模板
     */
    public RuleTemplate getVersion(String templateId, String version) {
        List<RuleVersion> versions = versionMap.get(templateId);
        if (versions == null || versions.isEmpty()) {
            return null;
        }
        
        return versions.stream()
            .filter(v -> v.getVersion().equals(version))
            .map(RuleVersion::getTemplate)
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 获取规则的所有版本历史
     * @param templateId 规则模板ID
     * @return 版本历史列表
     */
    public List<RuleVersion> getVersionHistory(String templateId) {
        List<RuleVersion> versions = versionMap.get(templateId);
        if (versions == null) {
            return new ArrayList<>();
        }
        
        return versions.stream()
            .sorted(Comparator.comparing(RuleVersion::getCreateTime).reversed())
            .collect(Collectors.toList());
    }
    
    /**
     * 复制规则模板（深复制）
     * @param template 原规则模板
     * @return 复制后的规则模板
     */
    private RuleTemplate cloneTemplate(RuleTemplate template) {
        // 这里简单实现，实际项目中可能需要更复杂的深复制逻辑
        RuleTemplate cloned = new RuleTemplate();
        cloned.setTemplateId(template.getTemplateId());
        cloned.setTemplateName(template.getTemplateName());
        cloned.setDescription(template.getDescription());
        cloned.setRuleType(template.getRuleType());
        cloned.setLogicalOperator(template.getLogicalOperator());
        cloned.setConditions(new ArrayList<>(template.getConditions()));
        
        return cloned;
    }

    /**
     * 规则版本实体类
     */
    @Data
    public static class RuleVersion {
        /**
         * 规则模板ID
         */
        private String templateId;
        
        /**
         * 版本号
         */
        private String version;
        
        /**
         * 版本描述
         */
        private String description;
        
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        
        /**
         * 规则模板
         */
        private RuleTemplate template;
    }
} 