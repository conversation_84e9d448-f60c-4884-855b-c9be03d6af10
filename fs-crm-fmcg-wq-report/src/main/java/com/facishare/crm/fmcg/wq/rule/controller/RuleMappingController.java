package com.facishare.crm.fmcg.wq.rule.controller;

import com.facishare.crm.fmcg.wq.rule.generator.RuleMappingFactory;
import com.facishare.crm.fmcg.wq.rule.generator.StandardRuleConfigAdapter;
import com.facishare.crm.fmcg.wq.rule.model.mapping.BaseRuleTemplateConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantRuleRuntimeConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantStandardRuleTemplate;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleFieldConditionConfig;
import com.facishare.crm.fmcg.wq.rule.service.RuleMappingService;
import com.facishare.paas.appframework.core.model.User;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * Rule Mapping Controller
 * 
 * This controller provides APIs for managing rule mappings between source and standard objects.
 */
@Slf4j
@RestController
@RequestMapping("/api/rule-mapping")
public class RuleMappingController {

    @Autowired
    private RuleMappingService ruleMappingService;
    
    @Autowired
    private RuleMappingFactory ruleMappingFactory;
    
    @Autowired
    private StandardRuleConfigAdapter standardRuleConfigAdapter;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    
    /**
     * Retrieve a list of Tenant Field Mapping Rule Configurations by tenant ID and target Entity API name.
     *
     * @param tenantId the tenant ID
     * @param standardObjectApiName the standard object API name
     * @param queryDataMap 预查询数据Map，用于替代数据库查询 (key: 对象API名称, value: 数据列表)
     * @return ResponseEntity containing a list of RuleFieldConditionConfig
     */
    @GetMapping("/getTenantFieldMappingRuleConfig/{tenantId}/{standardObjectApiName}")
    @ResponseBody
    public ResponseEntity<List<RuleFieldConditionConfig>> getTenantFieldMappingRuleConfig(
            @PathVariable String tenantId,
            @PathVariable String standardObjectApiName,
            @RequestBody(required = false) Map<String, List<Map<String, Object>>> queryDataMap) {
        try {
            // 获取基础规则配置
            BaseRuleTemplateConfig baseRuleConfig =
                    standardRuleConfigAdapter.getBaseRuleTemplateConfigCopy(standardObjectApiName);

            if (baseRuleConfig == null) {
                log.warn("No standard rule configuration found for {}", standardObjectApiName);
                return ResponseEntity.notFound().build();
            }

            // 创建租户规则配置
            TenantStandardRuleTemplate tenantStandardRuleTemplate = standardRuleConfigAdapter.getTenantStandardRuleTemplate(tenantId, baseRuleConfig, queryDataMap);

            // 获取租户映射规则
            List<RuleFieldConditionConfig> ruleFieldConditionConfigs = standardRuleConfigAdapter.getRuleFieldsFromTenantStandardRuleTemplate(tenantStandardRuleTemplate,standardObjectApiName);

            return ResponseEntity.ok(ruleFieldConditionConfigs);
        } catch (Exception e) {
            log.error("Error retrieving tenant field mapping rule config for tenant {} and object {}", tenantId, standardObjectApiName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    /**
     * Generate all rule mapping configurations for an object
     * 
     * @param objectDescribeJson Object description JSON
     * @return List of rule mapping configurations
     */
    @PostMapping("/generate-all")
    public ResponseEntity<List<TenantRuleRuntimeConfig>> generateAllMappingConfigs(@RequestBody String objectDescribeJson) {
        try {
            JsonNode rootNode = objectMapper.readTree(objectDescribeJson);
            String sourceObjectApiName = rootNode.path("api_name").asText();
            String tenantId = rootNode.path("tenant_id").asText();
            
            List<TenantRuleRuntimeConfig> configs = new ArrayList<>();
            
            // Generate configurations for all supported standard objects
            configs.add(ruleMappingFactory.createProductItemStandardRuleMapping(tenantId, sourceObjectApiName));
            configs.add(ruleMappingFactory.createMaterialStandardRuleMapping(tenantId, sourceObjectApiName));
            configs.add(ruleMappingFactory.createDistributionProductRuleMapping(tenantId, sourceObjectApiName));
            
            // Remove any null configurations
            configs.removeIf(config -> config == null);
            
            return ResponseEntity.ok(configs);
        } catch (Exception e) {
            log.error("Error generating rule mapping configurations", e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Generate rule mapping configuration for a specific standard object
     * 
     * @param standardObjectApiName Standard object API name
     * @param objectDescribeJson Object description JSON
     * @return Rule mapping configuration
     */
    @PostMapping("/generate/{standardObjectApiName}")
    public ResponseEntity<TenantRuleRuntimeConfig> generateMappingConfig(
            @PathVariable String tenantId,
            @PathVariable String standardObjectApiName,
            @RequestBody String objectDescribeJson) {
        try {
            JsonNode rootNode = objectMapper.readTree(objectDescribeJson);
            String sourceObjectApiName = rootNode.path("api_name").asText();
            
            TenantRuleRuntimeConfig config = ruleMappingFactory.createRuleMapping(tenantId, sourceObjectApiName, standardObjectApiName);
            
            if (config == null) {
                return ResponseEntity.badRequest().build();
            }
            
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            log.error("Error generating rule mapping configuration", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * Validate a rule mapping configuration
     * 
     * @param mappingConfig Rule mapping configuration to validate
     * @return Validation result
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateMappingConfig(@RequestBody TenantRuleRuntimeConfig mappingConfig) {
        try {
            StandardRuleConfigAdapter.ValidationResult result = 
                standardRuleConfigAdapter.validateMappingConfig(mappingConfig);
            
            Map<String, Object> response = new HashMap<>();
            response.put("valid", result.isValid());
            response.put("missingFields", result.getMissingFields());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error validating rule mapping configuration", e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * Find existing rule mappings
     * 
     * @param tenantId Tenant ID
     * @param sourceObjectApiName Source object API name
     * @param standardObjectApiName Standard object API name
     * @return List of rule mapping configurations
     */
    @GetMapping("/find/{sourceObjectApiName}/{standardObjectApiName}")
    public ResponseEntity<TenantRuleRuntimeConfig> findMappingConfigs(
            @PathVariable String tenantId,
            @PathVariable String sourceObjectApiName,
            @PathVariable String standardObjectApiName) {
        try {
            TenantRuleRuntimeConfig configs =
                ruleMappingService.findMappingConfigs(User.systemUser(tenantId), sourceObjectApiName, standardObjectApiName);
            
            return ResponseEntity.ok(configs);
        } catch (Exception e) {
            log.error("Error finding rule mapping configurations", e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * Create a custom rule mapping
     * 
     * @param request Custom rule mapping request
     * @return Created rule mapping configuration
     */
    @PostMapping("/custom")
    public ResponseEntity<TenantRuleRuntimeConfig> createCustomRuleMapping(@PathVariable String tenantId, @RequestBody CustomRuleMappingRequest request) {
        try {
            TenantRuleRuntimeConfig config = ruleMappingService.createCustomRuleMapping(
                tenantId,
                request.getSourceObjectApiName(),
                request.getStandardObjectApiName(),
                request.getFieldMappings());
            
            if (config == null) {
                return ResponseEntity.badRequest().build();
            }
            
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            log.error("Error creating custom rule mapping", e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * Clear rule mapping cache
     * 
     * @return Success message
     */
    @PostMapping("/clear-cache")
    public ResponseEntity<Map<String, String>> clearCache() {
        try {
            ruleMappingService.clearAllCaches();
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "Cache cleared successfully");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error clearing cache", e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * Custom rule mapping request
     */
    @Setter
    @Getter
    public static class CustomRuleMappingRequest {
        private String sourceObjectApiName;
        private String standardObjectApiName;
        private Map<String, String> fieldMappings;

    }
}