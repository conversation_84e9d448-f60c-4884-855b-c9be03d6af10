package com.facishare.crm.fmcg.wq.rule.cache;

import com.facishare.crm.fmcg.wq.rule.engine.RuleExecutionResult;
import com.facishare.crm.fmcg.wq.rule.template.RuleTemplate;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

/**
 * 规则缓存管理器
 * 用于缓存规则模板和执行结果，提升性能
 */
@Slf4j
@Component
public class RuleCacheManager {

    /**
     * 规则模板缓存
     * key: 规则模板ID
     * value: 缓存的规则模板
     */
    private final Map<String, TemplateCache> templateCache = new ConcurrentHashMap<>();
    
    /**
     * 规则执行结果缓存
     * key: 缓存键（规则ID + 数据哈希）
     * value: 缓存的执行结果
     */
    private final Map<String, ResultCache> resultCache = new ConcurrentHashMap<>();
    
    /**
     * 读写锁，用于缓存访问控制
     */
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    
    /**
     * 缓存规则模板
     * @param template 规则模板
     * @param expiration 过期时间（秒）
     */
    public void cacheTemplate(RuleTemplate template, long expiration) {
        if (template == null || template.getTemplateId() == null) {
            return;
        }
        
        try {
            lock.writeLock().lock();
            
            TemplateCache cache = new TemplateCache();
            cache.setTemplate(template);
            cache.setExpiration(LocalDateTime.now().plusSeconds(expiration));
            
            templateCache.put(template.getTemplateId(), cache);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 获取缓存的规则模板
     * @param templateId 规则模板ID
     * @return 规则模板，如果过期或不存在则返回null
     */
    public RuleTemplate getTemplate(String templateId) {
        try {
            lock.readLock().lock();
            
            TemplateCache cache = templateCache.get(templateId);
            if (cache == null || cache.getExpiration().isBefore(LocalDateTime.now())) {
                // 缓存不存在或已过期
                if (cache != null) {
                    templateCache.remove(templateId);
                }
                return null;
            }
            
            return cache.getTemplate();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 缓存执行结果
     * @param templateId 模板ID
     * @param dataKey 数据键（通常是数据哈希）
     * @param result 执行结果
     * @param expiration 过期时间（秒）
     */
    public void cacheResult(String templateId, String dataKey, RuleExecutionResult result, long expiration) {
        if (templateId == null || dataKey == null || result == null) {
            return;
        }
        
        try {
            lock.writeLock().lock();
            
            String cacheKey = templateId + ":" + dataKey;
            ResultCache cache = new ResultCache();
            cache.setResult(result);
            cache.setExpiration(LocalDateTime.now().plusSeconds(expiration));
            
            resultCache.put(cacheKey, cache);
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 获取缓存的执行结果
     * @param templateId 模板ID
     * @param dataKey 数据键
     * @return 执行结果，如果过期或不存在则返回null
     */
    public RuleExecutionResult getResult(String templateId, String dataKey) {
        try {
            lock.readLock().lock();
            
            String cacheKey = templateId + ":" + dataKey;
            ResultCache cache = resultCache.get(cacheKey);
            if (cache == null || cache.getExpiration().isBefore(LocalDateTime.now())) {
                // 缓存不存在或已过期
                if (cache != null) {
                    resultCache.remove(cacheKey);
                }
                return null;
            }
            
            return cache.getResult();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 清除所有缓存
     */
    public void clearAll() {
        try {
            lock.writeLock().lock();
            templateCache.clear();
            resultCache.clear();
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 清除指定模板的缓存
     * @param templateId 模板ID
     */
    public void clearTemplate(String templateId) {
        try {
            lock.writeLock().lock();
            templateCache.remove(templateId);
            
            // 清除相关的结果缓存
            resultCache.keySet().removeIf(key -> key.startsWith(templateId + ":"));
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 规则模板缓存实体
     */
    @Setter
    @Getter
    private static class TemplateCache {
        private RuleTemplate template;
        private LocalDateTime expiration;

    }
    
    /**
     * 规则执行结果缓存实体
     */
    @Setter
    @Getter
    private static class ResultCache {
        private RuleExecutionResult result;
        private LocalDateTime expiration;

    }
} 