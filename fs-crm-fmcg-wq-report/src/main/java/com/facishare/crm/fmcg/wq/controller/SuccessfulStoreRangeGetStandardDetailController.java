package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeFields;
import com.facishare.crm.fmcg.wq.dao.DataReportStandardDao;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/

public class SuccessfulStoreRangeGetStandardDetailController extends FmcgIdempotentPreDefineController<SuccessfulStoreRangeGetStandardDetailController.Arg, SuccessfulStoreRangeGetStandardDetailController.Result> {

    private DataReportStandardDao dataReportStandardDao = SpringUtil.getContext().getBean(DataReportStandardDao.class);


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.Detail.getFuncPrivilegeCodes();
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doServiceIdempotent(@Valid Arg request) {
        // 查标准数据对象
        Map<String, Map<String, IObjectData>> standardMap = dataReportStandardDao.getByMainObjectId(controllerContext.getUser(),SuccessfulStoreRangeFields.API_NAME, request.getObjectDataId(), arg.getRecordType());
        Result<Map<String, Map<String, ObjectDataDocument>>> mapResult = new Result<>();
        //IObjectData -> ObjectDataDocument   ObjectDataDocument::of
        mapResult.setData(standardMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry2 -> ObjectDataDocument.of(entry2.getValue()))))));
        return mapResult;

    }


    @Override
    protected String getIdempotentKey(Arg arg) {
        return controllerContext.getTenantId() + controllerContext.getMethodName() + JSON.toJSONString(arg).hashCode();
    }

    @Data
    public static class Arg {
        private String objectDataId;
        private String objectDescribeApiName;
        private String recordType = "default__c";

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Result<T> {
        private int code;
        private String message;
        private T data;
    }
}
