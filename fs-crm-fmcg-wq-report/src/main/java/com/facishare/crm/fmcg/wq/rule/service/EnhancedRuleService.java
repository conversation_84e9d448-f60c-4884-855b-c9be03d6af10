package com.facishare.crm.fmcg.wq.rule.service;

import com.facishare.crm.fmcg.wq.rule.cache.RuleCacheManager;
import com.facishare.crm.fmcg.wq.rule.engine.RuleEngine;
import com.facishare.crm.fmcg.wq.rule.engine.RuleExecutionResult;
import com.facishare.crm.fmcg.wq.rule.monitor.RuleExecutionMonitor;
import com.facishare.crm.fmcg.wq.rule.template.RuleTemplate;
import com.facishare.crm.fmcg.wq.rule.template.factory.RuleTemplateFactory;
import com.facishare.crm.fmcg.wq.rule.template.impl.SimpleCondition;
import com.facishare.crm.fmcg.wq.rule.template.version.RuleVersionManager;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 增强的规则服务
 * 整合规则缓存、监控和版本管理功能
 */
@Slf4j
@Service
public class EnhancedRuleService {

    @Autowired
    private RuleEngine ruleEngine;
    
    @Autowired
    private RuleCacheManager cacheManager;
    
    @Autowired
    private RuleExecutionMonitor monitor;
    
    @Autowired
    private RuleVersionManager versionManager;
    
    @Autowired
    private RuleTemplateFactory templateFactory;
    
    /**
     * 默认缓存过期时间（秒）
     */
    private static final long DEFAULT_CACHE_EXPIRATION = 3600;

    /**
     * 规则模板字段缓存
     */
    private Map<String, Set<String>> templateFieldsCache = new ConcurrentHashMap<>();

    /**
     * 执行规则
     * @param templateId 规则模板ID
     * @param data 待评估数据
     * @return 执行结果
     */
    public RuleExecutionResult executeRule(String templateId, IObjectData data) {
        return executeRule(templateId, data, null, true);
    }
    
    /**
     * 执行规则
     * @param templateId 规则模板ID
     * @param data 待评估数据
     * @param version 版本号，为null则使用最新版本
     * @param useCache 是否使用缓存
     * @return 执行结果
     */
    public RuleExecutionResult executeRule(String templateId, IObjectData data, String version, boolean useCache) {
        // 生成数据键
        String dataKey = generateDataKey(data);
        boolean isCacheHit = false;
        
        // 尝试从缓存获取
        if (useCache) {
            RuleExecutionResult cachedResult = cacheManager.getResult(templateId, dataKey);
            if (cachedResult != null) {
                log.debug("Cache hit for rule: {}, dataKey: {}", templateId, dataKey);
                isCacheHit = true;
                monitor.recordExecution(cachedResult, true);
                return cachedResult;
            }
        }
        
        // 获取规则模板
        RuleTemplate template;
        if (version != null) {
            // 获取指定版本
            template = versionManager.getVersion(templateId, version);
        } else {
            // 先尝试从缓存获取最新版本
            template = cacheManager.getTemplate(templateId);
            if (template == null) {
                // 缓存未命中，从版本管理器获取最新版本
                template = versionManager.getLatestVersion(templateId);
                if (template != null && useCache) {
                    // 将模板放入缓存
                    cacheManager.cacheTemplate(template, DEFAULT_CACHE_EXPIRATION);
                }
            }
        }
        
        // 执行规则
        RuleExecutionResult result;
        if (template != null) {
            result = ruleEngine.execute(template, data);
        } else {
            // 模板不存在，创建失败结果
            result = RuleExecutionResult.builder()
                .ruleId(templateId)
                .ruleName("Unknown Rule")
                .success(false)
                .message("Rule template not found: " + templateId)
                .build();
        }
        
        // 记录监控
        monitor.recordExecution(result, isCacheHit);
        
        // 缓存结果
        if (useCache && template != null) {
            cacheManager.cacheResult(templateId, dataKey, result, DEFAULT_CACHE_EXPIRATION);
        }
        
        return result;
    }
    
    /**
     * 并行执行多个规则
     * @param templateIds 规则模板ID列表
     * @param data 待评估数据
     * @return 执行结果列表
     */
    public List<RuleExecutionResult> executeRulesParallel(List<String> templateIds, IObjectData data) {
        if (templateIds == null || templateIds.isEmpty() || data == null) {
            return Collections.emptyList();
        }
        
        List<CompletableFuture<RuleExecutionResult>> futures = templateIds.stream()
            .map(id -> CompletableFuture.supplyAsync(() -> executeRule(id, data)))
            .collect(Collectors.toList());
            
        return futures.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
    }
    
    /**
     * 创建规则版本
     * @param template 规则模板
     * @param version 版本号
     * @param description 版本描述
     * @return 版本号
     */
    public String createRuleVersion(RuleTemplate template, String version, String description) {
        String versionId = versionManager.createVersion(template, version, description);
        // 更新字段缓存
        updateTemplateFieldsCache(template);
        return versionId;
    }
    
    /**
     * 更新单个模板的字段缓存
     * @param template 规则模板
     */
    private void updateTemplateFieldsCache(RuleTemplate template) {
        if (template == null || template.getTemplateId() == null) {
            return;
        }
        
        Set<String> fields = new HashSet<>();
        if (template.getConditions() != null) {
            template.getConditions().forEach(condition -> {
                if (condition instanceof SimpleCondition) {
                    SimpleCondition simpleCondition = (SimpleCondition) condition;
                    String field = simpleCondition.getField();
                    if (field != null) {
                        fields.add(field);
                    }
                }
            });
        }
        
        templateFieldsCache.put(template.getTemplateId(), fields);
    }
    
    /**
     * 生成数据键
     * 基于数据内容生成一个唯一键，用于缓存
     * @param data 数据
     * @return 数据键
     */
    private String generateDataKey(IObjectData data) {
        if (data == null) {
            return "null";
        }
        
        // 获取或构建字段集合
        Set<String> allFields = getAllTemplateFields();
        
        // 提取关键字段值
        Map<String, Object> keyFields = new HashMap<>();
        for (String field : allFields) {
            Object value = data.get(field);
            if (value != null) {
                keyFields.put(field, value);
            }
        }
        
        // 生成MD5哈希
        String json = keyFields.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .map(e -> e.getKey() + "=" + Objects.toString(e.getValue(), "null"))
            .collect(Collectors.joining("|"));
            
        return DigestUtils.md5Hex(json);
    }
    
    /**
     * 获取所有模板中使用的字段
     * @return 字段集合
     */
    private Set<String> getAllTemplateFields() {
        // 检查缓存的字段集合大小，如果为空则重建
        if (templateFieldsCache.isEmpty()) {
            rebuildTemplateFieldsCache();
        }
        
        // 合并所有模板的字段
        return templateFieldsCache.values().stream()
            .flatMap(Set::stream)
            .collect(Collectors.toSet());
    }
    
    /**
     * 重建模板字段缓存
     */
    private synchronized void rebuildTemplateFieldsCache() {
        templateFieldsCache.clear();
        
        for (RuleTemplate template : templateFactory.getAllTemplates()) {
            String templateId = template.getTemplateId();
            Set<String> fields = new HashSet<>();
            
            if (template.getConditions() != null) {
                template.getConditions().forEach(condition -> {
                    if (condition instanceof SimpleCondition) {
                        SimpleCondition simpleCondition = (SimpleCondition) condition;
                        String field = simpleCondition.getField();
                        if (field != null) {
                            fields.add(field);
                        }
                    }
                });
            }
            
            templateFieldsCache.put(templateId, fields);
        }
    }
    
    /**
     * 清除规则缓存
     * @param templateId 规则模板ID，为null则清除所有缓存
     */
    public void clearCache(String templateId) {
        if (templateId != null) {
            cacheManager.clearTemplate(templateId);
            // 更新特定模板的字段缓存
            RuleTemplate template = templateFactory.getTemplate(templateId);
            if (template != null) {
                updateTemplateFieldsCache(template);
            } else {
                templateFieldsCache.remove(templateId);
            }
        } else {
            cacheManager.clearAll();
            // 重建所有模板的字段缓存
            rebuildTemplateFieldsCache();
        }
    }
    
    /**
     * 获取规则执行统计
     * @return 监控统计
     */
    public Map<String, Object> getExecutionStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalExecutions", monitor.getTotalExecutions());
        stats.put("successExecutions", monitor.getSuccessExecutions());
        stats.put("failedExecutions", monitor.getFailedExecutions());
        stats.put("successRate", monitor.getSuccessRate());
        stats.put("cacheHitRate", monitor.getCacheHitRate());
        stats.put("recentErrors", monitor.getRecentErrors());
        
        return stats;
    }
} 