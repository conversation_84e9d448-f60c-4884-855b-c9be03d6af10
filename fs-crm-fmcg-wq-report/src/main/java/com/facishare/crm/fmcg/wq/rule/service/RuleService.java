package com.facishare.crm.fmcg.wq.rule.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantStandardRuleTemplate;
import com.facishare.crm.fmcg.wq.util.StandardFieldMappingUtil;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.dao.DataReportResultDao;
import com.facishare.crm.fmcg.wq.rule.converter.AchievementResultConverter;
import com.facishare.crm.fmcg.wq.rule.engine.RuleEngine;
import com.facishare.crm.fmcg.wq.rule.engine.RuleExecutionResult;
import com.facishare.crm.fmcg.wq.rule.model.AchievementResult;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantRuleRuntimeConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.FieldMappingConfigField;
import com.facishare.crm.fmcg.wq.rule.template.RuleTemplate;
import com.facishare.crm.fmcg.wq.rule.template.impl.SimpleCondition;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;

import lombok.extern.slf4j.Slf4j;

/**
 * 规则服务
 * 整合规则生成、执行和结果转换功能
 */
@Slf4j
@Service
public class RuleService {

    @Autowired
    private RuleEngine ruleEngine;

    @Autowired
    private AchievementResultConverter achievementResultConverter;

    @Autowired
    private RuleMappingService ruleMappingService;
    @Autowired
    private DataReportResultDao dataReportResultDao;

    /**
     * 处理上报数据并生成达成结果列表
     *
     * @param sourceObjMap          源对象API名称映射到上报数据列表的Map
     * @param standardDataMap       标准API名称映射到标准数据列表的Map
     * @param standardObjectApiName 陈列产品标准明细,ProductItemStandardDetailFields.API_NAME
     *                              陈列物料标准明细,MaterialStandardDetailsFields.API_NAME
     *                              陈列项目标注,ProjectStandardsFields.API_NAME
     *                              铺货必须分销,MustDistributeProductsFields.API_NAME
     * @param objPhotoMap           对象照片映射，key为对象apiName，value为对象id和照片图片list的map
     * @return 达成结果列表
     */
    public AchievementResult processReportDataList(User user, Map<String, Map<String, IObjectData>> sourceObjMap,
                                                   Map<String, Map<String, IObjectData>> standardDataMap, String recordType) {
        try {
            // 取owner
            List<String> owner = sourceObjMap.values().iterator().next().values().iterator().next().getOwner();
            // 规则组id 赋值
            // Create result object
            AchievementResult result = new AchievementResult();
            Map<String,Map<String, AchievementResultConverter.GroupAchievementDetail>> formIdAndGroupIdDetailsMap = null;
            // Process based on record type
            
            if (DataReport2PublicFieldsConstants.ReportRecordType.DISPLAY_ACHIEVEMENT.getApiName().equals(recordType)) {
                Map<String, Map<String, IObjectData>> displayAchievementMap = new HashMap<>();
//                Map<String, Map<String, IObjectData>> displayStandardDataMap = new HashMap<>();

                // 陈列产品
                List<IObjectData> productAchievements = processRuleMatchingAndAchievement(user,
                        sourceObjMap, standardDataMap, ProductItemStandardDetailFields.API_NAME);
//                displayStandardDataMap.put(ProductItemStandardDetailFields.API_NAME, productItemStandardPair.getKey().stream()
//                        .collect(Collectors.toMap(IObjectData::getId, Function.identity())));
                displayAchievementMap.put(ProductItemStandardDetailFields.API_NAME, productAchievements.stream()
                        .collect(Collectors.toMap(IObjectData::getId, Function.identity())));
                result.getTotalProjectAchievements().addAll(productAchievements);
                result.setProductProjectAchievements(productAchievements);

                // 陈列物料
                List<IObjectData> materialAchievements = processRuleMatchingAndAchievement(user,
                        sourceObjMap, standardDataMap, MaterialStandardDetailsFields.API_NAME);
//                displayStandardDataMap.put(MaterialStandardDetailsFields.API_NAME, materialStandardPair.getKey().stream()
//                        .collect(Collectors.toMap(IObjectData::getId, Function.identity())));
                displayAchievementMap.put(MaterialStandardDetailsFields.API_NAME, materialAchievements.stream()
                        .collect(Collectors.toMap(IObjectData::getId, Function.identity())));
                result.getTotalProjectAchievements().addAll(materialAchievements);
                result.setMaterialProjectAchievements(materialAchievements);

                // 陈列整体项目
                List<IObjectData> projectAchievements = processRuleMatchingAndAchievement(
                        user,
                        sourceObjMap,
                        standardDataMap,
                        ProjectStandardsFields.API_NAME);
                displayAchievementMap.put(ProjectStandardsFields.API_NAME, projectAchievements.stream()
                        .collect(Collectors.toMap(IObjectData::getId, Function.identity())));
                result.getTotalProjectAchievements().addAll(projectAchievements);
                result.setOverallProjectAchievements(projectAchievements);
                formIdAndGroupIdDetailsMap = achievementResultConverter.groupProjectAchievements(result.getTotalProjectAchievements());

                // 计算陈列形式达成结果
                List<IObjectData> displayFormAchievements = achievementResultConverter.convertDisplayFormAchievements(
                        user,
                        sourceObjMap,
                        standardDataMap,
                        formIdAndGroupIdDetailsMap);

                // 添加形式结果
                result.getSummaryDisplayAchievements().addAll(displayFormAchievements);
            } else if (DataReport2PublicFieldsConstants.ReportRecordType.DISTRIBUTION_ACHIEVEMENT.getApiName().equals(recordType)) {
                // 铺货必须分销
                List<IObjectData> distributionAchievements = processRuleMatchingAndAchievement(user,
                        sourceObjMap, standardDataMap, MustDistributeProductsFields.API_NAME);
                result.setDistributionProductsAchievements(distributionAchievements);
            } else if (DataReport2PublicFieldsConstants.ReportRecordType.ACTIVITY_ACHIEVEMENT.getApiName().equals(recordType)) {
                //算产品和物料的
                result = processProductAndMaterialReportDataList(user, sourceObjMap, standardDataMap, recordType);
                //查tpm整体结果的数据
                Map<String, IObjectData> iObjectDataMap = sourceObjMap.get(TPMActivityProofDetailFields.API_NAME);
                //查tpm陈列形式的结果
                Map<String, IObjectData> displayFormMap = sourceObjMap.get(TPMActivityProofDisplayImgFields.API_NAME);
                formIdAndGroupIdDetailsMap = achievementResultConverter.groupProjectAchievements(result.getTotalProjectAchievements());

                if (iObjectDataMap != null) {//有可能为空 产品和物料的标准为空，只有tpm整体的
                    Set<String> tpmProjectIds = iObjectDataMap.values().stream()
                            .map(achievementResultConverter::getRuleGroupIdAndApiNameFormTPM)
                            .filter(Objects::nonNull)
                            .map(Pair::getKey)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
                    // tpm 那没有数据的需要从结果里移除
                    result.getTotalProjectAchievements().removeIf(o -> !tpmProjectIds.contains(o.get(DisplayProjectAchievementFields.RULE_GROUP_ID, String.class)));
                    // result.getTotalProjectAchievements() 按照项目分组
                    Map<String, List<IObjectData>> productAndMaterialGroup = result.getTotalProjectAchievements().stream()
                            .collect(Collectors.groupingBy(o ->
                                    {
                                        String projectId = o.get(DisplayProjectAchievementFields.RULE_GROUP_ID, String.class);
                                        if (projectId == null) {
                                            return "default";
                                        }
                                        return projectId;
                                    }
                            ));
                    //陈列项目for
                    for (Map.Entry<String, IObjectData> entry : iObjectDataMap.entrySet()) {
                        IObjectData data = entry.getValue();
                        List<IObjectData> projectAchievements = achievementResultConverter.convertToDisplayAchievementByActivityItem(user,data,formIdAndGroupIdDetailsMap,productAndMaterialGroup);
                        if (projectAchievements != null) {
                            result.getTotalProjectAchievements().addAll(projectAchievements);
                            result.getOverallProjectAchievements().addAll(projectAchievements);

                        }
                    }
                    //陈列形式for
                    for (Map.Entry<String, IObjectData> entry : displayFormMap.entrySet()) {
                        IObjectData data = entry.getValue();
                        IObjectData displayFormAchievement = achievementResultConverter.convertToDisplayFromAchievementByActivityItem(user,data,formIdAndGroupIdDetailsMap);
                        if (displayFormAchievement != null) {
                            result.getSummaryDisplayAchievements().add(displayFormAchievement);
                        }
                    }
                  
                }
            }
            //达标计算方式
            String calculationRulesAttainment = Optional.ofNullable(standardDataMap.get(SuccessfulStoreRangeFields.API_NAME))
                    .map(data -> data.values().iterator().next().get(SuccessfulStoreRangeFields.CALCULATION_RULES_ATTAINMENT))
                    .map(String::valueOf)
                    .orElse(SuccessfulStoreRangeFields.CALCULATION_RULES_ATTAINMENT_Options_all_attainment);
            if (formIdAndGroupIdDetailsMap == null){
                formIdAndGroupIdDetailsMap = achievementResultConverter.groupProjectAchievements(result.getTotalProjectAchievements());
            }
            // 总结果
            achievementResultConverter.generateTotalResult(user, result, owner,formIdAndGroupIdDetailsMap,calculationRulesAttainment);
            setTotalResultIdToSubResults(result.getTotalProjectAchievements(),
                    result.getDisplayDistrAchSummary().getId(), owner);
            setTotalResultIdToSubResults(result.getSummaryDisplayAchievements(),
                    result.getDisplayDistrAchSummary().getId(), owner);
            setTotalResultIdToSubResults(result.getDistributionProductsAchievements(),
                    result.getDisplayDistrAchSummary().getId(), owner);
            // 设置业务类型
            result.getDisplayDistrAchSummary().setRecordType(recordType);
            return result;
        } catch (Exception e) {
            log.error("处理上报数据异常", e);
            throw new RuntimeException("处理上报数据异常", e); //ignoreI18n
        }
    }


    /**
     * Retrieves and filters standard data by API name, type, and required fields.
     *
     * @param standardDataMap The map of standard data categorized by API names.
     * @param standardApiName The API name to filter the standard data.
     * @param requiredTypes   The types of data that are required.
     * @param requiredFields  The fields that must be present in the data.
     * @return A filtered map of standard data.
     */
    private Map<String, IObjectData> getFilteredStandardData(
            Map<String, Map<String, IObjectData>> standardDataMap,
            String standardApiName) {

        Map<String, IObjectData> filteredData = new HashMap<>();
        //只有产品标准详情和物料标准详情才加后缀
        String newKey = standardApiName;
        if (standardApiName.equals(ProductItemStandardDetailFields.API_NAME) || standardApiName.equals(MaterialStandardDetailsFields.API_NAME)) {
            newKey = standardApiName + DataReport2PublicFieldsConstants.DATA_REPORT_STANDARD_DETAIL_SUFFIX;
        }
        Map<String, IObjectData> dataByApiName = standardDataMap.get(newKey);

        if (dataByApiName != null) {
            for (Map.Entry<String, IObjectData> entry : dataByApiName.entrySet()) {
                IObjectData data = entry.getValue();
                if (standardApiName.equals(ProjectStandardsFields.API_NAME)
                        && ProjectStandardsFields.SETTING_TYPE_Options_0.equals(data
                        .get(DataReport2PublicFieldsConstants.PROJECT_STANDARD_SETTING_TYPE, String.class))) {
                    filteredData.put(entry.getKey(), data);
                } else if (standardApiName.equals(ProductItemStandardDetailFields.API_NAME)
                        && ProjectStandardsFields.SETTING_TYPE_Options_1.equals(data
                        .get(DataReport2PublicFieldsConstants.PROJECT_STANDARD_SETTING_TYPE, String.class))) {
                    filteredData.put(entry.getKey(), data);
                } else if (standardApiName.equals(MaterialStandardDetailsFields.API_NAME)
                        && ProjectStandardsFields.SETTING_TYPE_Options_2.equals(data
                        .get(DataReport2PublicFieldsConstants.PROJECT_STANDARD_SETTING_TYPE, String.class))) {
                    filteredData.put(entry.getKey(), data);
                }else {
                    if (standardApiName.equals(ProductItemStandardDetailFields.API_NAME)) {
                        List productIds = data.get(ProductItemStandardDetailFields.PRODUCT_NAME, List.class);
                        List productCategorizationIds = data.get(ProductItemStandardDetailFields.PRODUCT_CATEGORIZATION, List.class);
                        if (CollectionUtils.isNotEmpty(productIds) || CollectionUtils.isNotEmpty(productCategorizationIds)) {
                            filteredData.put(entry.getKey(), data);
                        }
                    }else if (standardApiName.equals(MaterialStandardDetailsFields.API_NAME)) {
                        List materialIds = data.get(MaterialStandardDetailsFields.MATERIAL_NAMES, List.class);
                        if (CollectionUtils.isNotEmpty(materialIds)) {
                            filteredData.put(entry.getKey(), data);
                        }
                    }else if (standardApiName.equals(MustDistributeProductsFields.API_NAME)) {
                       filteredData.put(entry.getKey(), data);
                    }
                }
            }
        }

        return filteredData;
    }


    /**
     * 将总结果ID设置到子结果中
     *
     * @param achievementList 成果列表
     * @param totalResultId   总结果ID
     */
    private void setTotalResultIdToSubResults(List<IObjectData> achievementList, String totalResultId,
                                              List<String> owner) {
        // 将总结果ID设置为每个子结果的报告字段
        if (!CollectionUtils.isEmpty(achievementList)) {
            for (IObjectData achievement : achievementList) {
                achievement.set("related_report", totalResultId);
                achievement.setOwner(owner);
            }
        }
    }

    /**
     * 处理源数据和标准数据的规则匹配和达成情况计算
     * <p>
     * 该方法执行以下步骤：
     * 1. 遍历每个源对象的上报数据
     * 2. 查找并应用相应的规则映射配置
     * 3. 将源数据转换为标准格式
     * 4. 对每个标准数据执行过滤、匹配和达成规则
     * 5. 生成并返回达成结果
     *
     * @param sourceObjMap          源对象数据映射，key为源对象API名称，value为源对象数据列表
     * @param standardDataMap       标准数据映射，key为标准对象API名称，value为标准数据列表
     * @param standardObjectApiName 标准对象API名称
     * @return 处理后的达成结果列表
     */
    private List<IObjectData>/** 每个上报的结果 */
             processRuleMatchingAndAchievement(User user, Map<String, Map<String, IObjectData>> sourceObjMap,
                                                Map<String, Map<String, IObjectData>> standardDataMap,
                                                String standardObjectApiName) {
        List<IObjectData> result = new ArrayList<>();
        Map<String, IObjectData> standardData = getFilteredStandardData(standardDataMap, standardObjectApiName);
        if (MapUtils.isEmpty(standardData)) {
            return ListUtils.EMPTY_LIST;
        }
        // 项目标准处理的特殊逻辑 - 当处理项目标准时，先将所有数据转换为标准格式
        // 处理每个源对象API及其对应的上报数据
        List<Pair<TenantRuleRuntimeConfig, List<IObjectData>>> allRuleMappingAndStandardReportData = Lists.newArrayList();
        for (Map.Entry<String, Map<String, IObjectData>> sourceEntry : sourceObjMap.entrySet()) {
            String sourceObjectApiName = sourceEntry.getKey();
            Map<String, IObjectData> reportDataList = sourceEntry.getValue();
            // 转换上报数据为标准格式列表
            Pair<TenantRuleRuntimeConfig, List<IObjectData>> ruleMappingConfigAndStandardFormatData = convertToStandardFormatData(
                    user,
                    reportDataList,
                    sourceObjectApiName, standardObjectApiName);
            if (ruleMappingConfigAndStandardFormatData == null) {
                continue; // 无法获取映射配置，跳过处理
            }
            allRuleMappingAndStandardReportData.add(ruleMappingConfigAndStandardFormatData);
        }
        processStandardData(user, allRuleMappingAndStandardReportData, standardData,
                standardObjectApiName,
                result);

        return result;
    }

    /**
     * 将上报数据转换为标准格式列表
     *
     * @param reportDataList        上报数据列表
     * @param sourceObjectApiName   源对象API名称
     * @param standardObjectApiName 标准对象API名称
     * @return 转换后的标准格式数据列表，如果无法获取映射配置则返回null
     */
    private Pair<TenantRuleRuntimeConfig, List<IObjectData>> convertToStandardFormatData(
            User user,
            Map<String, IObjectData> reportDataList,
            String sourceObjectApiName,
            String standardObjectApiName) {
        // 获取匹配的规则映射配置
        TenantRuleRuntimeConfig mappingConfig = ruleMappingService.findMappingConfigs(user, sourceObjectApiName,
                standardObjectApiName);
        if (mappingConfig == null || mappingConfig.getTenantStandardRuleTemplate() == null
                || mappingConfig.getTenantStandardRuleTemplate() == null) {
            return null;
        }

        // 转换上报数据为标准格式列表（不处理聚合字段）
        return Pair.of(mappingConfig,
                ruleMappingService.convertToStandardData(reportDataList, mappingConfig.getFieldMappings()));
    }

    /**
     * 处理标准数据并执行规则
     *
     * @param standardReportDataList 标准格式的上报数据列表 虚拟字段
     * @param standardDataMap        标准数据映射 增加虚拟字段
     * @param standardObjectApiName  标准对象API名称
     * @param mappingConfig          规则映射配置
     * @param result                 结果列表，处理后的结果将添加到此列表中
     */
    private void processStandardData(User user, List<Pair<TenantRuleRuntimeConfig, List<IObjectData>>> allRuleMappingAndStandardReportData,
                                     Map<String, IObjectData> standardDataList,
                                     String standardObjectApiName,
                                     List<IObjectData> result) {
        if (standardDataList == null || standardDataList.isEmpty()) {
            // 没有标准数据， 算作成功
            return;
        }
        if (CollectionUtils.isEmpty(allRuleMappingAndStandardReportData)) {
            // 没有上报数据， 算作不成功
            standardDataList.forEach((id, standardData) -> {
                IObjectData achievement = achievementResultConverter
                        .convertToDisplayProjectAchievement(user,
                                RuleExecutionResult.builder().standardData(standardData).success(false).isDisplay(false).build(),
                                standardObjectApiName);
                if (achievement != null) {
                    result.add(achievement);
                }
            });
            return;
        }
        // SUB_CONDITIONS 字段不为空的化 需要处理完所有之后再处理
        // 1.分组 SUB_CONDITIONS 先处理为空的

        Map<Boolean, Map<String, IObjectData>> groupedStandardData = standardDataList.entrySet().stream()
                .collect(Collectors.partitioningBy(
                        entry -> CollectionUtils.isEmpty(
                                entry.getValue().get(DataReport2PublicFieldsConstants.SUB_CONDITIONS, List.class)),
                        Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

        Map<String, IObjectData> emptyRelatedIdMap = groupedStandardData.get(true);
        Map<String, IObjectData> nonEmptyRelatedIdMap = groupedStandardData.get(false);
        // 处理空的
        processEmptyRelatedStandard(user, allRuleMappingAndStandardReportData, emptyRelatedIdMap, standardObjectApiName, result);
        // 处理非空的
        if (MapUtils.isNotEmpty(nonEmptyRelatedIdMap)) {
            processNonEmptyRelatedStandard(user, result, nonEmptyRelatedIdMap);
        }
    }

    private void processNonEmptyRelatedStandard(User user, List<IObjectData> result,
                                                Map<String, IObjectData> nonEmptyRelatedIdMap) {
        // result 按照 RELATED_STANDARD_DETAIL_ID 分组
        Map<String, List<IObjectData>> resultGroupedByRelatedId = result.stream()
                .collect(Collectors.groupingBy(
                        data -> data.get(DataReport2PublicFieldsConstants.STANDARD_DETAIL_ID, String.class)));
        nonEmptyRelatedIdMap.forEach((id, standardData) -> {
            List<String> subConditions = standardData.get(DataReport2PublicFieldsConstants.SUB_CONDITIONS, List.class);
            // 聚合 id in subConditions 的数据
            List<IObjectData> subConditionsResult = Lists.newArrayList();
            subConditions.forEach(subCondition -> {
                // 获取标准字段
                String projectId = standardData.get(DataReport2PublicFieldsConstants.PROJECT_STANDARD_FIELD, String.class);
                String materialOrProductId = standardData.get(DataReport2PublicFieldsConstants.PRODUCT_OR_MATERIAL_STANDARD_FIELD, String.class);
                String key = StringUtils.joinWith("_", projectId, materialOrProductId, subCondition);
                List<IObjectData> subConditionResult = resultGroupedByRelatedId.get(key);
                if (CollectionUtils.isNotEmpty(subConditionResult)) {
                    subConditionsResult.addAll(subConditionResult);
                }
            });
            // 统计成功的子条件数量
            long successCount = 0;
            //需要对达成结果进行过滤
            //如果标准是物料标准 则需要过滤掉达成结果为达标的情况
            if (standardData.getDescribeApiName().equals(MaterialStandardDetailsFields.API_NAME) && CollectionUtils.isNotEmpty(standardData.get(MaterialStandardDetailsFields.MATERIAL_NAMES, List.class))) {
                // 物料或者物料分类不为空
                List materialIds = standardData.get(
                        MaterialStandardDetailsFields.MATERIAL_NAMES, List.class);
                //结果的实际物料 包含任意一个物料
                successCount = subConditionsResult.stream()
                        .filter(data -> !DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_1
                                .equals(data.get(DisplayProjectAchievementFields.ACHIEVED_RESULTS)) && isAnyListContains(materialIds, data.get(DisplayProjectAchievementFields.ACTUAL_MATERIALS, List.class)))
                        .count();
            } else if (standardData.getDescribeApiName().equals(ProductItemStandardDetailFields.API_NAME) && (CollectionUtils.isNotEmpty(standardData.get(ProductItemStandardDetailFields.PRODUCT_NAME, List.class)) || CollectionUtils.isNotEmpty(standardData.get(ProductItemStandardDetailFields.PRODUCT_CATEGORIZATION, List.class)))) {
                // 产品或者产品分类不为空
                List productIds = standardData.get(
                        ProductItemStandardDetailFields.PRODUCT_NAME, List.class);
                List productCategorizationIds = standardData.get(
                        ProductItemStandardDetailFields.PRODUCT_CATEGORIZATION, List.class);
                successCount = subConditionsResult.stream()
                        .filter(data -> DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_1
                                .equals(data.get(DisplayProjectAchievementFields.ACHIEVED_RESULTS)) && (isAnyListContains(productIds, data.get(DisplayProjectAchievementFields.ACTUAL_PRODUCTS, List.class)) || isAnyListContains(productCategorizationIds, data.get(DisplayProjectAchievementFields.ACTUAL_PRODUCTS_CLASSIFICATION, List.class))))
                        .count();
            } else {
                successCount = subConditionsResult.stream()
                        .filter(data -> DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_1
                                .equals(data.get(DisplayProjectAchievementFields.ACHIEVED_RESULTS)))
                        .count();
            }
            String expectedFieldKey = standardData.get(DataReport2PublicFieldsConstants.VIRTUAL_STANDARD_QUANTITY_KEY,
                    String.class);
            // 获取期望达成数量，默认为子条件总数
            double expectedValue = standardData.get(expectedFieldKey, Double.class) != null
                    ? standardData.get(expectedFieldKey, Double.class)
                    : 0;

            // 判断是否达成（实际成功数量 >= 期望值）
            boolean isSuccess = successCount >= expectedValue;

            // 创建模拟的执行结果
            IObjectData reportData = new ObjectData();
            reportData.set(expectedFieldKey, successCount);
            reportData.set(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, DataReport2PublicFieldsConstants.LEVEL_Options_anyLevel);
            //result 过滤保留 不是未陈列的
            RuleExecutionResult ruleResult = RuleExecutionResult.builder()
                    .standardData(standardData)
                    .reportData(reportData)
                    .isDisplay(!achievementResultConverter.isGroupAchievedByStandard(subConditionsResult, DisplayProjectAchievementFields.WAYS_ACHIEVE_STANDARD_Options_1, DisplayProjectAchievementFields.ACHIEVED_RESULTS, DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_4))
                    .success(isSuccess)
                    .build();

            // 转换为达成结果
            IObjectData achievement = achievementResultConverter
                    .convertToDisplayProjectAchievement(user, ruleResult, standardData.getDescribeApiName());
            achievement.set(DataReport2PublicFieldsConstants.SUB_CONDITIONS, subConditionsResult.stream().map(IObjectData::getId).collect(Collectors.toList()));
            if (achievement != null) {
                result.add(achievement);

                // 设置父子关系
                subConditionsResult.forEach(subConditionResult -> {
                    subConditionResult.set(DataReport2PublicFieldsConstants.CONDITION_PARENT_ID, achievement.getId());
                });
            }
        });
    }

    /**
     * 包含任意 空返回 false
     * @param list
     * @param list2
     * @return
     */
    private boolean isAnyListContains(List list, List list2) {
       //空
       if (CollectionUtils.isEmpty(list)) {
        return false;
       }
       if (CollectionUtils.isEmpty(list2)) {
        return false;
       }
       return CollectionUtils.containsAny(list, list2);
    }
    private void processEmptyRelatedStandard(User user, List<Pair<TenantRuleRuntimeConfig, List<IObjectData>>> allRuleMappingAndStandardReportData,
                                             Map<String, IObjectData> standardDataList, String standardObjectApiName, List<IObjectData> result) {
        // 对每个标准数据进行处理
        for (Map.Entry<String, IObjectData> standardDataEntry : standardDataList.entrySet()) {
            String standardId = standardDataEntry.getKey();
            IObjectData standardData = standardDataEntry.getValue();
            
            // 填充标准的虚拟字段
            RuleExecutionResult ruleResult = calcRuleExecutionResult(allRuleMappingAndStandardReportData,
                    standardData);

            // 检查规则结果是否为RuleExecutionResultExt类型（包含多个分组结果）
            if (ruleResult instanceof RuleExecutionResultExt) {
                RuleExecutionResultExt extResult = (RuleExecutionResultExt) ruleResult;
                List<IObjectData> groupDataList = extResult.getGroupAggregatedDataList();
                List<Boolean> groupSuccessList = extResult.getGroupSuccessList();

                // 保存原始报告数据，用于后续恢复
                IObjectData originalReportData = ruleResult.getReportData();

                // 处理每个分组结果
                for (int i = 0; i < groupDataList.size(); i++) {
                    // 设置临时报告数据为当前分组的数据
                    IObjectData groupData = groupDataList.get(i);
                    boolean groupSuccess = groupSuccessList.get(i);

                    // 设置报告数据和达成状态
                    ruleResult.setReportData(groupData);
                    // 设置当前分组的成功状态
                    ruleResult.setSuccess(groupSuccess);
                    ruleResult.setStandardData(standardData);

                    // 生成单个分组的达成结果
                    IObjectData groupAchievement = achievementResultConverter
                            .convertToDisplayProjectAchievement(user, ruleResult, standardObjectApiName);

                    if (groupAchievement != null) {
                        result.add(groupAchievement);
                    }
                }

                // 恢复原始报告数据和成功状态
                ruleResult.setReportData(originalReportData);
                ruleResult.setSuccess(extResult.isSuccess());
            } else {
                // 只有一个分组，使用原始处理逻辑
                IObjectData achievement = achievementResultConverter
                        .convertToDisplayProjectAchievement(user, ruleResult, standardObjectApiName);
                if (achievement != null) {
                    result.add(achievement);
                }
            }
        }
    }

    /**
     * 计算规则执行结果
     */
    private RuleExecutionResult calcRuleExecutionResult(List<Pair<TenantRuleRuntimeConfig, List<IObjectData>>> allRuleMappingAndStandardReportData,
                                                        IObjectData standardData) {
        //理论不会有多个 和规则绑定的
        TenantStandardRuleTemplate tenantStandardRuleTemplate = allRuleMappingAndStandardReportData.get(0).getKey().getTenantStandardRuleTemplate();
        // 生成规则模板（过滤规则、匹配规则和达成规则）
        List<RuleTemplate> ruleTemplates = ruleMappingService.generateRuleTemplates(
                tenantStandardRuleTemplate, standardData);

        // 获取过滤规则、匹配规则和达成规则
        RuleTemplate filterTemplate = ruleTemplates.stream()
                .filter(t -> t.getTemplateId().endsWith("_filter"))
                .findFirst()
                .orElse(null);

        RuleTemplate matchTemplate = ruleTemplates.stream()
                .filter(t -> t.getTemplateId().endsWith("_match"))
                .findFirst()
                .orElse(null);

        RuleTemplate achievementTemplate = ruleTemplates.stream()
                .filter(t -> t.getTemplateId().endsWith("_achievement"))
                .findFirst()
                .orElse(null);
        // 有聚合规则的 fieldMapping
        List<FieldMappingConfigField> aggFieldMappings = allRuleMappingAndStandardReportData.stream()
                .map(Pair::getKey)
                .map(TenantRuleRuntimeConfig::getFieldMappings)
                .flatMap(List::stream)
                .filter(fieldMapping -> fieldMapping.getAggregationConfig() != null)
                //同一个targetField 只保留一个
                .collect(Collectors.groupingBy(FieldMappingConfigField::getTargetField))
                .values().stream()
                .map(list -> list.get(0)).collect(Collectors.toList());

        return executeRules(standardData, allRuleMappingAndStandardReportData.stream().map(Pair::getValue).flatMap(List::stream).collect(Collectors.toList()), filterTemplate, matchTemplate,
                achievementTemplate, aggFieldMappings);
    }

    final static List<String> levelGroupByFields = Lists
            .newArrayList(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL);

    /**
     * 执行规则并构建结果对象，支持按组聚合
     *
     * @param standardData        标准数据
     * @param reportDataList      报告数据列表
     * @param filterTemplate      过滤规则模板
     * @param matchTemplate       匹配规则模板
     * @param achievementTemplate 达成规则模板
     * @return 规则执行结果
     */
    private RuleExecutionResult executeRules(
            IObjectData standardData,
            List<IObjectData> reportDataList,
            RuleTemplate filterTemplate,
            RuleTemplate matchTemplate,
            RuleTemplate achievementTemplate,
            List<FieldMappingConfigField> aggFieldMappings) {

        long startTime = System.currentTimeMillis();
        // 期望值 暂时这么处理 只有一个
        Object expectedValue = ((SimpleCondition) achievementTemplate.getConditions().get(0)).getValue();
        // 初始化数据和结果
        List<IObjectData> filteredDataList = new ArrayList<>();
        List<IObjectData> matchedDataList = new ArrayList<>();
        IObjectData aggregatedData = new ObjectData();
        boolean isAchieved = false;

        // 分组聚合结果变量
        GroupAchievementResults groupAchievementResults = null;

        // 步骤1: 执行过滤规则 (filterRules) 不要跳过需要算成未达成
        if (filterTemplate != null) {
            filteredDataList = applyFilterRule(filterTemplate, reportDataList);
        } else {
            filteredDataList = new ArrayList<>(reportDataList);
        }

        // 步骤2: 执行匹配规则 (matchRules) 不要跳过需要算成未达成
        if (matchTemplate != null && !filteredDataList.isEmpty()) {
            matchedDataList = applyFilterRule(matchTemplate, filteredDataList);
        } else {
            matchedDataList = new ArrayList<>(filteredDataList);
        }

        // 步骤3: 执行达成规则 (achievementRules) - 支持按组聚合
        if (achievementTemplate != null && !matchedDataList.isEmpty()
                && CollectionUtils.isNotEmpty(achievementTemplate.getConditions())) {
            // 分组字段
            List<String> groupByFields = null;
            // 获取层数判断是否是 -1
            if (DataReport2PublicFieldsConstants.LEVEL_Options_anyLevel.equals(standardData
                    .get(DataReport2PublicFieldsConstants.ProjectStandardsFields_Virtual_LEVEL, String.class))) {
                // 分组增加 层级
                groupByFields = levelGroupByFields;
            }
            // 获取分组聚合结果
            groupAchievementResults = applyAchievementRule(aggFieldMappings,
                    achievementTemplate, matchedDataList, groupByFields);
            isAchieved = groupAchievementResults.isOverallSuccess();
            aggregatedData = groupAchievementResults.getCombinedData();
        }

        // 确保聚合数据不为空
        if (aggregatedData == null && !matchedDataList.isEmpty()) {
            aggregatedData = matchedDataList.get(0);
        }

        // 构建基本结果对象
        RuleExecutionResult baseResult = RuleExecutionResult.builder()
                .ruleId(achievementTemplate != null ? achievementTemplate.getTemplateId() : "")
                .ruleName(achievementTemplate != null ? achievementTemplate.getTemplateName() : "")
                .success(isAchieved)
                .message(isAchieved ? "规则执行成功" : "规则执行失败") //ignoreI18n
                .standardData(standardData)
                .reportData(aggregatedData)
                .filteredDataList(filteredDataList)
                .isDisplay(CollectionUtils.isNotEmpty(filteredDataList))
                .matchedDataList(matchedDataList)
                .executionTime(System.currentTimeMillis() - startTime)
                .build();

        // 如果有分组聚合结果，创建扩展结果对象
        if (groupAchievementResults != null && groupAchievementResults.hasMultipleGroups()) {
            return new RuleExecutionResultExt(baseResult,
                    groupAchievementResults.getGroupAggregatedDataList(),
                    groupAchievementResults.getGroupSuccessList());
        }

        return baseResult;
    }

    /**
     * 应用达成规则，按组聚合并返回最终的达成结果
     * 修改为能按照数据进行分组聚合，每一个聚合值生成一个达成结果
     * 返回包含多个分组结果和达成状态的对象
     */
    private GroupAchievementResults applyAchievementRule(
            List<FieldMappingConfigField> aggFieldMappings, RuleTemplate achievementTemplate,
            List<IObjectData> dataList, List<String> groupByFields) {
        if (dataList == null || dataList.isEmpty()) {
            return GroupAchievementResults.createSingle(false, new ObjectData());
        }

        // 根据分组字段对数据进行分组
        Map<String, List<IObjectData>> groupedData = groupDataByFields(dataList, aggFieldMappings, groupByFields);

        // 如果没有分组或只有一个组，数据且错误的
        if (groupedData.size() <= 1 && groupedData.containsKey("default")) {
            if (CollectionUtils.isNotEmpty(groupByFields)) {
                //原样返回
                return GroupAchievementResults.createSingle(false, dataList.get(0));
            } else {
                IObjectData aggregatedData = ruleMappingService.mergeData(dataList, aggFieldMappings);
                boolean success = ruleEngine.execute(achievementTemplate, aggregatedData).isSuccess();
                return GroupAchievementResults.createSingle(success, aggregatedData);
            }

        }

        // 处理多个分组的情况
        List<IObjectData> aggregatedResults = new ArrayList<>();
        List<Boolean> groupSuccessList = new ArrayList<>();
        boolean overallSuccess = true;

        // 对每个分组进行处理
        for (Map.Entry<String, List<IObjectData>> entry : groupedData.entrySet()) {
            String groupByValue = entry.getKey();
            if ("default".equals(groupByValue)) {
                continue;
            }
            List<IObjectData> groupList = entry.getValue();
            if (!groupList.isEmpty()) {
                // 对分组数据进行聚合
                IObjectData groupAggregatedData = ruleMappingService.mergeData(groupList, aggFieldMappings);
                // 执行达成规则
                log.info("applyAchievementRule groupByValue:{},groupAggregatedData:{},groupList:{}", groupByValue, groupAggregatedData,groupList);
                boolean groupSuccess = ruleEngine.execute(achievementTemplate, groupAggregatedData).isSuccess();
                // 将分组聚合数据和达成状态添加到结果列表
                aggregatedResults.add(groupAggregatedData);
                groupSuccessList.add(groupSuccess);
                // 更新整体成功状态（所有分组都需要成功才算整体成功）
                overallSuccess = overallSuccess && groupSuccess;
            }
        }

        // 将所有分组结果合并到一个聚合数据对象中
        IObjectData combinedData = new ObjectData();
        // 保留原始字段
        Map<String, List<Object>> allFieldsValues = new HashMap<>();

        // 收集所有分组的字段值
        for (IObjectData groupData : aggregatedResults) {
            for (String fieldName : ObjectDataExt.of(groupData).toMap().keySet()) {
                allFieldsValues.computeIfAbsent(fieldName, k -> new ArrayList<>())
                        .add(groupData.get(fieldName));
            }
        }

        // 设置合并后的数据
        for (Map.Entry<String, List<Object>> entry : allFieldsValues.entrySet()) {
            combinedData.set(entry.getKey(), entry.getValue());
        }

        return new GroupAchievementResults(overallSuccess, combinedData, aggregatedResults, groupSuccessList);
    }

    /**
     * 根据分组字段对数据进行分组
     *
     * @param dataList      数据列表
     * @param aggFieldMappings 字段映射配置
     * @param groupByFields
     * @return 分组后的数据Map，键为分组字段值组合，值为该组的数据列表
     */
    private Map<String, List<IObjectData>> groupDataByFields(List<IObjectData> dataList,
                                                             List<FieldMappingConfigField> aggFieldMappings, List<String> groupByFields) {
        // 获取配置了分组的字段列表
        if (groupByFields == null) {
            groupByFields = aggFieldMappings.stream()
                    .filter(field -> field.getAggregationConfig() != null
                            && field.getAggregationConfig().getGroupByFields() != null
                            && !field.getAggregationConfig().getGroupByFields().isEmpty())
                    .flatMap(field -> field.getAggregationConfig().getGroupByFields().stream())
                    .distinct()
                    .collect(Collectors.toList());
        }

        // 如果没有分组字段，返回单一分组
        if (groupByFields.isEmpty()) {
            Map<String, List<IObjectData>> singleGroup = new HashMap<>();
            singleGroup.put("default", new ArrayList<>(dataList));
            return singleGroup;
        }

        // 根据分组字段的值进行分组
        Map<String, List<IObjectData>> groupedData = new HashMap<>();

        for (IObjectData data : dataList) {
            // 构建分组键
            String groupKeyStr = groupByFields.stream()
                    .map(field -> Optional.ofNullable(data.get(field))
                            .map(Object::toString)
                            .orElse("null"))
                    .collect(Collectors.joining("_"));
            if (groupKeyStr.contains("null")) {
                groupKeyStr = "default";
            }
            // 将数据添加到相应的组
            groupedData.computeIfAbsent(groupKeyStr, k -> new ArrayList<>()).add(data);
        }

        return groupedData;
    }

    /**
     * 应用过滤规则，返回满足条件的数据列表
     */
    private List<IObjectData> applyFilterRule(RuleTemplate filterTemplate, List<IObjectData> dataList) {
        List<IObjectData> filteredDataList = new ArrayList<>();

        for (IObjectData data : dataList) {
            RuleExecutionResult result = ruleEngine.execute(filterTemplate, data);
            if (result.isSuccess()) {
                filteredDataList.add(data);
            }
        }

        return filteredDataList;
    }

    /**
     * 将达成结果映射扁平化为列表
     *
     * @param displayAchievementMap 达成结果映射
     * @return 扁平化后的达成结果列表
     */
    private List<IObjectData> flattenAchievementMap(
            Map<String, List<IObjectData>> displayAchievementMap) {
        List<IObjectData> flatList = new ArrayList<>();
        for (List<IObjectData> achievements : displayAchievementMap.values()) {
            flatList.addAll(achievements);
        }
        return flatList;
    }

    /**
     * 分组达成结果类
     * 用于存储多个分组的聚合结果和对应的达成状态
     */
    @Getter
    private static class GroupAchievementResults {
        /**
         * 整体是否达成（所有分组都达成才算整体达成）
         * -- GETTER --
         *  获取整体是否达成

         */
        private final boolean overallSuccess;

        /**
         * 合并后的聚合数据（包含所有分组结果）
         * -- GETTER --
         *  获取合并后的聚合数据

         */
        private final IObjectData combinedData;

        /**
         * 每个分组的聚合数据列表
         * -- GETTER --
         *  获取每个分组的聚合数据列表

         */
        private final List<IObjectData> groupAggregatedDataList;

        /**
         * 每个分组的达成状态列表（与groupAggregatedDataList一一对应）
         * -- GETTER --
         *  获取每个分组的达成状态列表

         */
        private final List<Boolean> groupSuccessList;

        /**
         * 构造方法
         */
        public GroupAchievementResults(boolean overallSuccess, IObjectData combinedData,
                                       List<IObjectData> groupAggregatedDataList,
                                       List<Boolean> groupSuccessList) {
            this.overallSuccess = overallSuccess;
            this.combinedData = combinedData;
            this.groupAggregatedDataList = groupAggregatedDataList;
            this.groupSuccessList = groupSuccessList;
        }

        /**
         * 创建只有一个分组的结果
         */
        public static GroupAchievementResults createSingle(boolean success, IObjectData data) {
            List<IObjectData> dataList = new ArrayList<>();
            List<Boolean> successList = new ArrayList<>();

            if (data != null) {
                dataList.add(data);
                successList.add(success);
            }

            return new GroupAchievementResults(success, data, dataList, successList);
        }

        /**
         * 是否包含多个分组结果
         */
        public boolean hasMultipleGroups() {
            return groupAggregatedDataList != null;
        }
    }

    /**
     * RuleExecutionResult的扩展类，添加对分组聚合结果的支持
     */
    private static class RuleExecutionResultExt extends RuleExecutionResult {
        private final List<IObjectData> groupAggregatedDataList;
        private final List<Boolean> groupSuccessList;

        public RuleExecutionResultExt(RuleExecutionResult base,
                                      List<IObjectData> groupAggregatedDataList,
                                      List<Boolean> groupSuccessList) {
            // 复制原始RuleExecutionResult的所有字段
            super(base.getRuleId(),
                    base.getRuleName(),
                    base.isSuccess(),
                    base.getMessage(),
                    base.getConditionResults(),
                    base.getExecutionTime(),
                    base.getStandardData(),
                    base.getStandardDataMain(),
                    base.getReportData(),
                    base.getFilteredDataList(),
                    base.getMatchedDataList(),base.getIsDisplay());

            this.groupAggregatedDataList = groupAggregatedDataList;
            this.groupSuccessList = groupSuccessList;
        }

        /**
         * 覆盖setSuccess方法，尝试设置成功状态
         * 如果无法通过普通方式设置，则尝试使用反射
         */
        @Override
        public void setSuccess(boolean success) {
            try {
                // 先尝试使用父类的方法设置
                super.setSuccess(success);
            } catch (Exception e) {
                // 如果普通方式失败，使用反射设置success字段
                try {
                    java.lang.reflect.Field field = RuleExecutionResult.class.getDeclaredField("success");
                    field.setAccessible(true);
                    field.setBoolean(this, success);
                } catch (Exception ex) {
                    log.warn("无法设置success字段: {}", ex.getMessage());
                }
            }
        }

        /**
         * 获取分组聚合数据列表
         */
        public List<IObjectData> getGroupAggregatedDataList() {
            return groupAggregatedDataList;
        }

        /**
         * 获取分组达成状态列表
         */
        public List<Boolean> getGroupSuccessList() {
            return groupSuccessList;
        }

        /**
         * 是否有多个分组结果
         */
        public boolean hasMultipleGroups() {
            return groupAggregatedDataList != null;
        }

        /**
         * 获取指定索引的分组聚合数据
         */
        public IObjectData getGroupAggregatedData(int index) {
            if (groupAggregatedDataList == null || index < 0 || index >= groupAggregatedDataList.size()) {
                return null;
            }
            return groupAggregatedDataList.get(index);
        }

        /**
         * 获取指定索引的分组达成状态
         */
        public boolean getGroupSuccess(int index) {
            if (groupSuccessList == null || index < 0 || index >= groupSuccessList.size()) {
                return false;
            }
            return groupSuccessList.get(index);
        }
    }

    /**
     * 处理报告数据列表 - 只处理产品和物料
     *
     * @param user 用户
     * @param sourceObjMap 源报告数据Map
     * @param standardDataMap 标准数据Map
     * @param recordType 记录类型
     * @return 处理结果
     */
    public AchievementResult processProductAndMaterialReportDataList(User user, Map<String, Map<String, IObjectData>> sourceObjMap,
                                                                     Map<String, Map<String, IObjectData>> standardDataMap, String recordType) {
        AchievementResult result = new AchievementResult();
        // 取owner
        List<String> owner = sourceObjMap.values().iterator().next().values().iterator().next().getOwner();
        try {
            Map<String, Map<String, IObjectData>> displayAchievementMap = new HashMap<>();
            Map<String, String> fieldMappingByMainApiName = StandardFieldMappingUtil.getFieldMappingByMainApiName(recordType);
            // 陈列产品
            List<IObjectData> productAchievements = processRuleMatchingAndAchievement(user,
                    sourceObjMap, standardDataMap, ProductItemStandardDetailFields.API_NAME);
            displayAchievementMap.put(ProductItemStandardDetailFields.API_NAME, productAchievements.stream()
                    .collect(Collectors.toMap(IObjectData::getId, Function.identity())));
            result.getTotalProjectAchievements().addAll(productAchievements);
            result.setProductProjectAchievements(productAchievements);

            // 陈列物料
            List<IObjectData> materialAchievements = processRuleMatchingAndAchievement(user,
                    sourceObjMap, standardDataMap, MaterialStandardDetailsFields.API_NAME);
            displayAchievementMap.put(MaterialStandardDetailsFields.API_NAME, materialAchievements.stream()
                    .collect(Collectors.toMap(IObjectData::getId, Function.identity())));
            result.getTotalProjectAchievements().addAll(materialAchievements);
            result.setMaterialProjectAchievements(materialAchievements);

        } catch (Exception e) {
            log.error("处理产品和物料报告数据失败", e);
            result.setSuccess(false);
            result.setError("处理产品和物料报告数据失败: " + e.getMessage()); //ignoreI18n
            throw e;
        }
        return result;
    }


}
