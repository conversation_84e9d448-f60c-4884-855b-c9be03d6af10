package com.facishare.crm.fmcg.wq.rule.util;

import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.exception.CheckinsException;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantFieldCustomConfig;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 检查参数问题
 * @author: zhangsm
 * @create: 2025-03-27 14:37
 **/
@Slf4j
public class CheckStandardDetailArgsUtils {

 

    /**
     * 检查参数问题
     * 1.arg objectData 为 SuccessfulStoreRangeFields的对象字段
     * 2.arg details 分别为的对象
     * MustDistributeProductsFields,ProjectStandardsFields,DisplayTypeStandardsFields
     *
     * @param arg
     */
    public static void checkSuccessStoreRangeDetailArgs(BaseObjectSaveAction.Arg arg) {
        // 1.检查对象数据 如果主对象业务类型是 铺货标准则从对象只有 必分销
        if (arg.getObjectData() != null) {
            IObjectData successfulStoreRangeObjectData = arg.getObjectData().toObjectData();
            String businessType = successfulStoreRangeObjectData.getRecordType();
            if (businessType.equals(SuccessfulStoreRangeFields.RECORDETYPE_DISTRIBUTION_STANDARDS)) {
                // 从对对象只有必分销
                // List<ObjectDataDocument> mustDistributeProductsDetails = arg.getDetails()
                // .get(MustDistributeProductsFields.API_NAME);
                // if (CollectionUtils.isEmpty(mustDistributeProductsDetails)) {
                // throw new CheckinsException(MustDistributeProductsFields.DISPLAY_NAME
                // + "不能为空", -1);
                // }
            } else if (businessType.equals(SuccessfulStoreRangeFields.RECORDETYPE_DISPLAY_STANDARDS)) {
                // 必须有陈列项目
                List<ObjectDataDocument> projectStandardsDetails = arg.getDetails()
                        .get(ProjectStandardsFields.API_NAME);
                // 达标计算方式
                String calculationRulesAttainment = successfulStoreRangeObjectData
                        .get(SuccessfulStoreRangeFields.CALCULATION_RULES_ATTAINMENT, String.class);
                // 不能为空
                if (StringUtils.isBlank(calculationRulesAttainment)) {
                    throw new CheckinsException(SuccessfulStoreRangeFields.DISPLAY_NAME
                            + "的达标计算方式不能为空", -1); //ignoreI18n
                }

                // 取标准设置方式，如果有陈列形式的话，标准得有陈列形式
                String standardSettingMethod = successfulStoreRangeObjectData
                        .get(SuccessfulStoreRangeFields.STANDARD_SETTING_METHOD, String.class);
                // 不能为空
                if (StringUtils.isBlank(standardSettingMethod)) {
                    throw new CheckinsException(SuccessfulStoreRangeFields.DISPLAY_NAME
                            + "的标准设置方式不能为空", -1); //ignoreI18n
                }
                if (CollectionUtils.isNotEmpty(projectStandardsDetails)) {

                    if (SuccessfulStoreRangeFields.STANDARD_SETTING_METHOD_Options_2.equals(standardSettingMethod)) {
                        // 陈列形式从对象必须有
                        List<ObjectDataDocument> displayTypeStandardsDetails = arg.getDetails()
                                .get(DisplayTypeStandardsFields.API_NAME);
                        if (CollectionUtils.isEmpty(displayTypeStandardsDetails)) {
                            throw new CheckinsException(DisplayTypeStandardsFields.DISPLAY_NAME
                                    + "不能为空", -1); //ignoreI18n
                        }
                        Set<String> displayTypeSet = new HashSet<>();
                        // 检查陈列形式字段list list 里都有
                        for (ObjectDataDocument displayTypeStandardsDetail : displayTypeStandardsDetails) {
                            IObjectData objectData = displayTypeStandardsDetail.toObjectData();
                            String displayType = objectData
                                    .get(DisplayTypeStandardsFields.DISPLAY_FORM, String.class);
                            if (StringUtils.isBlank(displayType)) {
                                throw new CheckinsException(DisplayTypeStandardsFields.DISPLAY_NAME
                                        + "的陈列形式不能为空", -1); //ignoreI18n
                            }
                            displayTypeSet.add(displayType);
                        }
                        // 检查陈列形式 对象都必须有对应 陈列项目
                        Set<String> projectStandardDisplayFormSet = new HashSet<>();
                        // 在checkSuccessStoreRangeDetailArgs方法中，添加对projectStandardDisplayFormSet的填充
                        for (ObjectDataDocument projectStandardsDetail : projectStandardsDetails) {
                            IObjectData objectData = projectStandardsDetail.toObjectData();
                            String displayFormat = objectData.get(ProjectStandardsFields.DISPLAY_FORMAT, String.class);
                            if (StringUtils.isBlank(displayFormat)) {
                                throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                        + "的陈列形式不能为空", -1); //ignoreI18n
                            }
                            projectStandardDisplayFormSet.add(displayFormat); // 添加这行
                            String settingType = objectData
                                    .get(ProjectStandardsFields.SETTING_TYPE, String.class);
                            // SETTING_TYPE 不能为空
                            if (StringUtils.isBlank(settingType)) {
                                throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                        + "的设置类型不能为空", -1); //ignoreI18n
                            }
                            // 检查选项 整体标准 必填项 标准值 项目
                            if (ProjectStandardsFields.SETTING_TYPE_Options_0.equals(settingType)) {
                                String quantity = objectData
                                        .get(ProjectStandardsFields.QUANTITY, String.class);
                                if (StringUtils.isBlank(quantity)) {
                                    throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                            + "的必填项 最低标准 不能为空", -1); //ignoreI18n
                                }
                                // 项目
                                String projectStandard = objectData
                                        .get(ProjectStandardsFields.DISPLAY_PROJECT, String.class);
                                if (StringUtils.isBlank(projectStandard)) {
                                    throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                            + "的必填项 项目不能为空", -1); //ignoreI18n
                                }

                            } else if (ProjectStandardsFields.SETTING_TYPE_Options_1.equals(settingType)) {
                                // 产品标准不能为空
                                String productDisplayStandards = objectData
                                        .get(ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS, String.class);
                                if (StringUtils.isBlank(productDisplayStandards)) {
                                    throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                            + "的必填项 产品陈列标准不能为空", -1); //ignoreI18n
                                }
                            } else if (ProjectStandardsFields.SETTING_TYPE_Options_2.equals(settingType)) {
                                // 物料标准不能为空
                                String materialDisplayStandards = objectData
                                        .get(ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS, String.class);
                                if (StringUtils.isBlank(materialDisplayStandards)) {
                                    throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                            + "的必填项 物料陈列标准不能为空", -1); //ignoreI18n
                                }
                            }
                        }
                        // 检查陈列形式 对象都必须有对应 陈列项目
                        for (String displayType : displayTypeSet) {
                            if (!projectStandardDisplayFormSet.contains(displayType)) {
                                throw new CheckinsException(
                                        DisplayTypeStandardsFields.DISPLAY_NAME + "必须有对应" //ignoreI18n
                                                + ProjectStandardsFields.DISPLAY_NAME,
                                        -1);
                            }
                        }
                    } else if (SuccessfulStoreRangeFields.STANDARD_SETTING_METHOD_Options_1
                            .equals(standardSettingMethod)) {
                        for (ObjectDataDocument projectStandardsDetail : projectStandardsDetails) {
                            IObjectData objectData = projectStandardsDetail.toObjectData();
                            String settingType = objectData
                                    .get(ProjectStandardsFields.SETTING_TYPE, String.class);
                            // SETTING_TYPE 不能为空
                            if (StringUtils.isBlank(settingType)) {
                                throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                        + "的设置类型不能为空", -1); //ignoreI18n
                            }
                            if (ProjectStandardsFields.SETTING_TYPE_Options_0.equals(settingType)) {
                                String quantity = objectData
                                        .get(ProjectStandardsFields.QUANTITY, String.class);
                                if (StringUtils.isBlank(quantity)) {
                                    throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                            + "的必填项 最低标准 不能为空", -1); //ignoreI18n
                                }
                                String projectStandard = objectData
                                        .get(ProjectStandardsFields.DISPLAY_PROJECT, String.class);
                                if (StringUtils.isBlank(projectStandard)) {
                                    throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                            + "的必填项 项目不能为空", -1); //ignoreI18n
                                }
                            } else if (ProjectStandardsFields.SETTING_TYPE_Options_1.equals(settingType)) {
                                // 产品标准不能为空
                                String productDisplayStandards = objectData
                                        .get(ProjectStandardsFields.PRODUCT_DISPLAY_STANDARDS, String.class);
                                if (StringUtils.isBlank(productDisplayStandards)) {
                                    throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                            + "的必填项 产品陈列标准不能为空", -1); //ignoreI18n
                                }
                            } else if (ProjectStandardsFields.SETTING_TYPE_Options_2.equals(settingType)) {
                                // 物料标准不能为空
                                String materialDisplayStandards = objectData
                                        .get(ProjectStandardsFields.MATERIAL_DISPLAY_STANDARDS, String.class);
                                if (StringUtils.isBlank(materialDisplayStandards)) {
                                    throw new CheckinsException(ProjectStandardsFields.DISPLAY_NAME
                                            + "的必填项 物料陈列标准不能为空", -1); //ignoreI18n
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 检查产品项目标准
     * 1.arg objectData 为 ProductItemStandardFields的对象字段
     * 2.arg details ProductItemStandardDetailObj
     * 3.检查点，
     *
     * @param arg
     */
    public static void checkProductItemStandardArgs(BaseObjectSaveAction.Arg arg) {
        // 主对象达标方式必填
        IObjectData objectData = arg.getObjectData().toObjectData();
        String standardType = objectData.get(ProductItemStandardFields.WAYS_ACHIEVE_STANDARD, String.class);
        if (StringUtils.isBlank(standardType)) {
            throw new CheckinsException(ProductItemStandardFields.DISPLAY_NAME
                    + "的必填项 达标方式不能为空", -1); //ignoreI18n
        }
        // 从对象 不能为空
        List<ObjectDataDocument> productItemStandardDetails = arg.getDetails()
                .get(ProductItemStandardDetailFields.API_NAME);
        if (CollectionUtils.isEmpty(productItemStandardDetails)) {
            throw new CheckinsException(ProductItemStandardDetailFields.DISPLAY_NAME
                    + "不能为空", -1); //ignoreI18n
        }

        // 遍历从对象进行检查
        for (ObjectDataDocument detail : productItemStandardDetails) {
            IObjectData detailData = detail.toObjectData();
            // 最低标准不为空
            String quantity = detailData.get(ProductItemStandardDetailFields.PROJECT_STANDARDS, String.class);
            if (StringUtils.isBlank(quantity)) {
                throw new CheckinsException(ProductItemStandardDetailFields.DISPLAY_NAME
                        + "的必填项 最低标准不能为空", -1); //ignoreI18n
            }
            // 项目不能为空
            String project = detailData.get(ProductItemStandardDetailFields.DISPLAY_PROJECT, String.class);
            if (StringUtils.isBlank(project)) {
                throw new CheckinsException(ProductItemStandardDetailFields.DISPLAY_NAME
                        + "的必填项 陈列项目不能为空", -1); //ignoreI18n
            }
            // 产品或者产品分类不为空
            String productCategorization = detailData.get(ProductItemStandardDetailFields.PRODUCT_CATEGORIZATION,
                    String.class);
            String product = detailData.get(ProductItemStandardDetailFields.PRODUCT_NAME, String.class);
            if (StringUtils.isBlank(product) && StringUtils.isBlank(productCategorization)) {
                throw new CheckinsException(ProductItemStandardDetailFields.DISPLAY_NAME
                        + "的必填项 产品或者产品分类不能为空", -1); //ignoreI18n
            }
        }
        // 重复校验 如果 产品 产品分类 和 项目完全一样的2条数据需要报错
        Set<String> uniqueKeySet = new HashSet<>();
        for (ObjectDataDocument detail : productItemStandardDetails) {
            IObjectData detailData = detail.toObjectData();

            // 获取产品和产品分类列表
            List<String> productNames = detailData.get(ProductItemStandardDetailFields.PRODUCT_NAME, List.class);
            List<String> productCategories = detailData.get(ProductItemStandardDetailFields.PRODUCT_CATEGORIZATION,
                    List.class);
            String displayProject = detailData.get(ProductItemStandardDetailFields.DISPLAY_PROJECT, String.class);

            // 对列表进行排序以忽略顺序
            String productKey = "";
            if (productNames != null) {
                List<String> sortedProductNames = new ArrayList<>(productNames);
                Collections.sort(sortedProductNames);
                productKey = String.join(",", sortedProductNames);
            }

            String categoryKey = "";
            if (productCategories != null) {
                List<String> sortedProductCategories = new ArrayList<>(productCategories);
                Collections.sort(sortedProductCategories);
                categoryKey = String.join(",", sortedProductCategories);
            }

            // 组合唯一键
            String uniqueKey = productKey + "_" + categoryKey + "_" + displayProject;

            if (uniqueKeySet.contains(uniqueKey)) {
                throw new CheckinsException(ProductItemStandardDetailFields.DISPLAY_NAME
                        + "存在重复数据，产品、产品分类和陈列项目的组合必须唯一", -1); //ignoreI18n
            }
            uniqueKeySet.add(uniqueKey);
        }

    }

    /**
     * 检查物料项目标准
     * 1.arg objectData 为 MaterialStandardRequiremFields
     * 2.arg details MaterialStandardDetailsFields
     * 3.检查点，
     *
     * @param arg
     */
    public static void checkMaterialItemStandardArgs(BaseObjectSaveAction.Arg arg) {
        // 主对象达标方式必填
        IObjectData objectData = arg.getObjectData().toObjectData();
        String standardType = objectData.get(MaterialStandardRequiremFields.WAYS_ACHIEVE_STANDARD, String.class);
        if (StringUtils.isBlank(standardType)) {
            throw new CheckinsException(
                    MaterialStandardRequiremFields.DISPLAY_NAME
                            + "的必填项 达标方式不能为空", //ignoreI18n
                    -1);
        }
        // 从对象 不能为空
        List<ObjectDataDocument> materialItemStandardDetails = arg.getDetails()
                .get(MaterialStandardDetailsFields.API_NAME);
        if (CollectionUtils.isEmpty(materialItemStandardDetails)) {
            throw new CheckinsException(MaterialStandardDetailsFields.DISPLAY_NAME
                    + "不能为空", -1); //ignoreI18n
        }

        // 遍历从对象进行检查
        for (ObjectDataDocument detail : materialItemStandardDetails) {
            IObjectData detailData = detail.toObjectData();
            // 最低标准不为空
            String quantity = detailData.get(MaterialStandardDetailsFields.MATERIAL_QUANTITY, String.class);
            if (StringUtils.isBlank(quantity)) {
                throw new CheckinsException(MaterialStandardDetailsFields.DISPLAY_NAME
                        + "的必填项 最低标准不能为空", -1); //ignoreI18n
            }
            // 项目不能为空
            String project = detailData.get(MaterialStandardDetailsFields.DISPLAY_PROJECT, String.class);
            if (StringUtils.isBlank(project)) {
                throw new CheckinsException(MaterialStandardDetailsFields.DISPLAY_NAME
                        + "的必填项 陈列项目不能为空", -1); //ignoreI18n
            }
            // 物料或者物料分类不为空
            String materialCategorization = detailData.get(MaterialStandardDetailsFields.MATERIAL_CATEGORY,
                    String.class);
            String material = detailData.get(MaterialStandardDetailsFields.MATERIAL_NAMES, String.class);
            if (StringUtils.isBlank(material) && StringUtils.isBlank(materialCategorization)) {
                throw new CheckinsException(MaterialStandardDetailsFields.DISPLAY_NAME
                        + "的必填项 物料或者物料分类不能为空", -1); //ignoreI18n
            }
        }
        // 重复校验 如果 产品 产品分类 和 项目完全一样的2条数据需要报错
        Set<String> uniqueKeySet = new HashSet<>();
        for (ObjectDataDocument detail : materialItemStandardDetails) {
            IObjectData detailData = detail.toObjectData();

            // 获取产品和产品分类列表
            List<String> materialNames = detailData.get(MaterialStandardDetailsFields.MATERIAL_NAMES, List.class);
            List<String> materialCategories = detailData.get(MaterialStandardDetailsFields.MATERIAL_CATEGORY,
                    List.class);
            String displayProject = detailData.get(MaterialStandardDetailsFields.DISPLAY_PROJECT, String.class);

            // 对列表进行排序以忽略顺序
            String materialKey = "";
            if (materialNames != null) {
                List<String> sortedMaterialNames = new ArrayList<>(materialNames);
                Collections.sort(sortedMaterialNames);
                materialKey = String.join(",", sortedMaterialNames);
            }

            String categoryKey = "";
            if (materialCategories != null) {
                List<String> sortedMaterialCategories = new ArrayList<>(materialCategories);
                Collections.sort(sortedMaterialCategories);
                categoryKey = String.join(",", sortedMaterialCategories);
            }

            // 组合唯一键
            String uniqueKey = materialKey + "_" + categoryKey + "_" + displayProject;

            if (uniqueKeySet.contains(uniqueKey)) {
                throw new CheckinsException(MaterialStandardDetailsFields.DISPLAY_NAME
                        + "存在重复数据，物料、物料分类和陈列项目的组合必须唯一", -1); //ignoreI18n
            }
            uniqueKeySet.add(uniqueKey);
        }
    }

    /**
     * 1.取配置的 字段名称数组，如果名称内的数据不允许编辑新建
     *
     * @param arg
     * @param argmaster
     * @param allTenantFieldCustomConfigs
     */
    public static void checkEditDisplayProject(IObjectData dbMaster, ObjectDataDocument argMaster, List<TenantFieldCustomConfig> allTenantFieldCustomConfigs) {
        // 1.取配置的 字段名称数组，如果名称内的数据不允许编辑新建
        String dbName = dbMaster.getName();
        String dbId = dbMaster.getId();
        //name matched
        if (!dbName.equals(argMaster.toObjectData().getName())){
            List<TenantFieldCustomConfig> nameMatchedList = allTenantFieldCustomConfigs.stream().filter(o->{
                if (CollectionUtils.isNotEmpty(o.getFieldMappings())) {
                    return o.getFieldMappings().stream().anyMatch(f->StringUtils.isNotBlank(f.getTargetFieldName()) && dbName.equals(f.getTargetFieldName()));
                }
                return false;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(nameMatchedList)){
                //打印 所有的nameMatchedList sourceapiName 和targetApiName
                log.info(dbMaster.getName()+"has been mapped"+nameMatchedList.stream().map(o->o.getSourceObjectApiName()+"->"+o.getStandardObjectApiName()).collect(Collectors.joining(",")));
                throw new CheckinsException("名称存在映射数据，不允许编辑名称", -1);//ingoreI18n
            }
        }

    }

    public static void checkDisplayProjectInvalid(IObjectData dbMaster, List<TenantFieldCustomConfig> allTenantFieldCustomConfigs) {
        String dbId = dbMaster.getId();
        String dbName = dbMaster.getName();
        //name matched
        List<TenantFieldCustomConfig> nameMatchedList = allTenantFieldCustomConfigs.stream().filter(o->{
            if (CollectionUtils.isNotEmpty(o.getFieldMappings())) {
                return o.getFieldMappings().stream().anyMatch(f->StringUtils.isNotBlank(f.getTargetFieldName()) && dbName.equals(f.getTargetFieldName()));
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(nameMatchedList)){
            //打印 所有的nameMatchedList sourceapiName 和targetApiName
            log.info(dbName+"has been mapped"+nameMatchedList.stream().map(o->o.getSourceObjectApiName()+"->"+o.getStandardObjectApiName()).collect(Collectors.joining(",")));
            throw new CheckinsException("名称存在映射数据，不允许作废删除", -1);//ingoreI18n
        }
        //id matched
        List<TenantFieldCustomConfig> fieldIdMatchedList = allTenantFieldCustomConfigs.stream().filter(o->{
            if (CollectionUtils.isNotEmpty(o.getFieldMappings())) {
                return o.getFieldMappings().stream().anyMatch(f->StringUtils.isNotBlank(f.getTargetField()) && dbId.equals(f.getTargetField()));
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fieldIdMatchedList)){
            //打印 所有的nameMatchedList sourceapiName 和targetApiName
            log.info(dbName+"has been mapped"+fieldIdMatchedList.stream().map(o->o.getSourceObjectApiName()+"->"+o.getStandardObjectApiName()).collect(Collectors.joining(",")));
            throw new CheckinsException("存在映射数据，不允许作废删除", -1);//ingoreI18n
        }
    }

}
