package com.facishare.crm.fmcg.wq.rule.generator;

import com.facishare.crm.fmcg.wq.constants.MaterialStandardDetailsFields;
import com.facishare.crm.fmcg.wq.constants.MustDistributeProductsFields;
import com.facishare.crm.fmcg.wq.constants.ProductItemStandardDetailFields;
import com.facishare.crm.fmcg.wq.rule.model.mapping.BaseRuleTemplateConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantFieldCustomConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantRuleRuntimeConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.TenantStandardRuleTemplate;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.FieldMappingConfigField;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.MappingConfig;
import com.facishare.crm.fmcg.wq.rule.model.mapping.base.RuleFieldConditionConfig;
import com.facishare.crm.fmcg.wq.rule.util.FieldMappingConfigManager;

import java.util.*;

import com.facishare.paas.metadata.api.describe.IFieldType;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Factory class for creating and managing rule mapping configurations
 * 
 * This class centralizes the creation of rule mappings and provides a clearer
 * structure for understanding how rule mappings are created and managed.
 */
@Slf4j
@Component
public class RuleMappingFactory {

    @Autowired
    private StandardRuleConfigAdapter standardRuleConfigAdapter;
    
    @Autowired
    private FieldMappingConfigManager fieldMappingConfigManager;

    /**
     * Create a rule mapping configuration for a source and standard object
     * 
     * @param tenantId Tenant ID
     * @param sourceObjectApiName Source object API name
     * @param standardObjectApiName Standard object API name
     * @return The created rule mapping configuration
     */
    public TenantRuleRuntimeConfig createRuleMapping(String tenantId, String sourceObjectApiName, String standardObjectApiName) {
        // 获取映射配置
        Map<String, FieldMappingConfigField> fieldMappingConfigMap = fieldMappingConfigManager.getFieldMappingConfig(
                tenantId, sourceObjectApiName, standardObjectApiName);
        if (MapUtils.isEmpty(fieldMappingConfigMap)) {
            return null;
        }
        // 从缓存获取基础规则配置
        BaseRuleTemplateConfig baseRuleTemplateConfig = standardRuleConfigAdapter.getBaseRuleTemplateConfigCopy(standardObjectApiName);
        
        if (baseRuleTemplateConfig == null) {
            log.warn("No standard rule configuration found for {}", standardObjectApiName);
            return null;
        }
        
        // 创建租户规则配置
        TenantStandardRuleTemplate tenantRuleConfig = standardRuleConfigAdapter.getTenantStandardRuleTemplate(tenantId, baseRuleTemplateConfig);
        
        // 生成字段映射
        List<RuleFieldConditionConfig> ruleFieldMappings =
            standardRuleConfigAdapter.getRuleFieldsFromTenantStandardRuleTemplate(tenantRuleConfig,standardObjectApiName);
            

        
        // 构建字段映射
        List<FieldMappingConfigField> fieldMappings = buildFieldMappings(
            ruleFieldMappings, 
            fieldMappingConfigMap, 
            tenantId, 
            sourceObjectApiName, 
            standardObjectApiName
        );

        // 创建租户字段映射配置
        TenantFieldCustomConfig fieldMappingConfig = TenantFieldCustomConfig.builder()
            .configId(UUID.randomUUID().toString())
            .tenantId(tenantId)
            .sourceObjectApiName(sourceObjectApiName)
            .standardObjectApiName(standardObjectApiName)
            .fieldMappings(fieldMappings)
            .build();
            
        // 创建并返回完整配置
        return TenantRuleRuntimeConfig.builder()
            .tenantFieldCustomConfig(fieldMappingConfig)
            .tenantStandardRuleTemplate(tenantRuleConfig)
            .build();
    }
    
    /**
     * Create a product item standard rule mapping
     * 
     * @param tenantId Tenant ID
     * @param sourceObjectApiName Source object API name
     * @return Product item standard rule mapping
     */
    public TenantRuleRuntimeConfig createProductItemStandardRuleMapping(String tenantId, String sourceObjectApiName) {
        return createRuleMapping(tenantId, sourceObjectApiName, ProductItemStandardDetailFields.API_NAME);
    }
    
    /**
     * Create a material standard rule mapping
     * 
     * @param tenantId Tenant ID
     * @param sourceObjectApiName Source object API name
     * @return Material standard rule mapping
     */
    public TenantRuleRuntimeConfig createMaterialStandardRuleMapping(String tenantId, String sourceObjectApiName) {
        return createRuleMapping(tenantId, sourceObjectApiName, MaterialStandardDetailsFields.API_NAME);
    }
    
    /**
     * Create a distribution product rule mapping
     * 
     * @param tenantId Tenant ID
     * @param sourceObjectApiName Source object API name
     * @return Distribution product rule mapping
     */
    public TenantRuleRuntimeConfig createDistributionProductRuleMapping(String tenantId, String sourceObjectApiName) {
        return createRuleMapping(tenantId, sourceObjectApiName, MustDistributeProductsFields.API_NAME);
    }

    /**
     * Create a custom rule mapping with the provided components
     * 
     * @param tenantId Tenant ID
     * @param sourceObjectApiName Source object API name
     * @param standardObjectApiName Standard object API name
     * @param fieldMappings Field mappings
     * @param ruleConfig Rule generation configuration
     * @return The created custom rule mapping
     */
    public TenantRuleRuntimeConfig createCustomRuleMapping(
            String tenantId,
            String sourceObjectApiName,
            String standardObjectApiName,
            List<FieldMappingConfigField> fieldMappings,
            TenantStandardRuleTemplate ruleConfig) {
        

        // 确保所有字段映射的 sourceField 为空（如果需要）
        for (FieldMappingConfigField fieldMapping : fieldMappings) {
            if (fieldMapping.getSourceField() == null) {
                // 尝试根据目标字段找到匹配的源字段
                String sourceField = standardRuleConfigAdapter.findMatchingSourceField(fieldMapping.getTargetField(), sourceObjectApiName);
                fieldMapping.setSourceField(sourceField);
            }
        }
            
        // 创建租户字段映射配置
        TenantFieldCustomConfig fieldMappingConfig = TenantFieldCustomConfig.builder()
            .configId(tenantId + "_" + sourceObjectApiName + "_" + standardObjectApiName)
            .tenantId(tenantId)
            .sourceObjectApiName(sourceObjectApiName)
            .standardObjectApiName(standardObjectApiName)
            .fieldMappings(fieldMappings)
            .build();
        
        // 创建并返回完整配置
        return TenantRuleRuntimeConfig.builder()
            .tenantFieldCustomConfig(fieldMappingConfig)
            .tenantStandardRuleTemplate(ruleConfig)
            .build();
    }
    
    /**
     * 构建字段映射列表，根据规则字段条件配置和租户映射映射配置表
     *
     * @param ruleFieldMappings 规则字段条件配置列表
     * @param fieldMappingConfigMap 字段映射配置表，key为字段名，value为源字段
     * @param tenantId 租户ID
     * @param sourceObjectApiName 源对象API名称
     * @param standardObjectApiName 标准对象API名称
     * @return 构建的字段映射配置字段列表
     */
    public List<FieldMappingConfigField> buildFieldMappings(
            List<RuleFieldConditionConfig> ruleFieldMappings,
            Map<String, FieldMappingConfigField> fieldMappingConfigMap,
            String tenantId,
            String sourceObjectApiName,
            String standardObjectApiName) {
        
        List<FieldMappingConfigField> fieldMappings = new ArrayList<>();
        
        for (RuleFieldConditionConfig ruleFieldConfig : ruleFieldMappings) {
            MappingConfig keyMappingConfig = ruleFieldConfig.getKeyMappingConfig();
            // 获取目标字段
            String targetField = ruleFieldConfig.getTargetField();
            if (targetField == null || targetField.isEmpty()) {
                continue;
            }
            if (Objects.nonNull(keyMappingConfig)){
                targetField = MappingConfig.convertStandardValue(targetField, keyMappingConfig).toString();
            }
            
            // 从配置映射中查找对应的源字段
            // 这里直接从Map中获取字段名对应的源字段，Map结构已经由FieldMappingConfigManager处理
            FieldMappingConfigField sourceField = fieldMappingConfigMap.get(targetField);
            if (sourceField == null && StringUtils.isNotBlank(ruleFieldConfig.getTargetFieldName())) {
                //通过名称再匹配一遍
                sourceField = fieldMappingConfigMap.get(ruleFieldConfig.getTargetFieldName());
            }

            // 如果还是没有找到源字段，跳过这个映射
            if (sourceField == null) {
//                log.warn("No source field found for target field: {} in {}.{}.{}",
//                        targetField, tenantId, sourceObjectApiName, standardObjectApiName);
                continue;
            }
            
            // 确定源字段类型
            String sourceFieldType = determineSourceFieldType(ruleFieldConfig, sourceField);
            
            // 创建字段映射配置
            FieldMappingConfigField fieldMapping = FieldMappingConfigField.createFromRuleFieldConditionConfig(
                    ruleFieldConfig, sourceField, sourceFieldType);
            
            // 添加到映射列表
            fieldMappings.add(fieldMapping);
        }
        
        return fieldMappings;
    }
    
    /**
     * 确定源字段的类型
     *
     * @param ruleFieldConfig 规则字段条件配置
     * @param sourceField 源字段名称
     * @return 源字段类型
     */
    private String determineSourceFieldType(RuleFieldConditionConfig ruleFieldConfig, FieldMappingConfigField fieldMappingConfigField) {
        // 如果规则配置中已有源字段类型，则直接使用
        if (ruleFieldConfig.getSourceFieldType() != null && !ruleFieldConfig.getSourceFieldType().isEmpty()) {
            return ruleFieldConfig.getSourceFieldType();
        }
        String sourceField = fieldMappingConfigField.getSourceField();
        // 如果源字段是表达式（以#开头），则假定为文本类型
        if (sourceField.startsWith("#")) {
            return IFieldType.TEXT;
        }
        
        // 可以在这里添加更多的类型推断逻辑
        // 例如，根据字段名称的后缀或前缀判断类型
        if (sourceField.endsWith("__c") || sourceField.contains("Name")) {
            return IFieldType.TEXT;
        } else if (sourceField.contains("Amount") || sourceField.contains("Count") || 
                  sourceField.contains("Num") || sourceField.contains("Price")) {
            return IFieldType.NUMBER;
        } else if (sourceField.contains("Date") || sourceField.contains("Time")) {
            return IFieldType.TIME;
        } else if (sourceField.contains("IsActive") || sourceField.contains("Has") ||
                  sourceField.contains("Is")) {
            return IFieldType.TRUE_OR_FALSE;
        }
        
        // 默认返回文本类型
        return IFieldType.TEXT;
    }
} 