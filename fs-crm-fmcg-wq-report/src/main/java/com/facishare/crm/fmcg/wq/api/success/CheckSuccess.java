package com.facishare.crm.fmcg.wq.api.success;

import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface CheckSuccess {
    @Data
    @ToString
    class Arg implements Serializable {

        @NotEmpty(message = "参数为空")
        private Map<String,Config>  successConfig;

        @NotEmpty(message = "参数为空")
        private String storeId;

        private Map<String,String> customData;//东鹏客开

        private int matchType; //完全匹配(0)  还是优先级匹配(1)

        private List<Config> matchFields;//字段匹配顺序 按优先级匹配时使用

        /**
         * 业务类型 默认都有的 新版本
         */
        private String recordType;

    }

    @Data
    @ToString
    class Result implements Serializable {

        private String id;
    }

    @Data
    class Config implements Serializable{
        private String apiName;  //对应客户下字段

        private String type;  //类型

        private String objApiName;  //对象

        private String ruleApiName;//铺货数据下字段api名称
        private Object data;//执行时使用
    }
}
