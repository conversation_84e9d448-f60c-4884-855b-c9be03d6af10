package com.facishare.crm.fmcg.wq.report.service.displayStandard;

import java.util.Map;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class DisplayStandardServiceManager implements ApplicationContextAware {

    private Map<String, DisplayStandardService> displayStandardServiceMap = Maps.newHashMap();

    private void initTransQueryServiceMap(ApplicationContext applicationContext) {
        Map<String, DisplayStandardService> springBeanMap = applicationContext.getBeansOfType(DisplayStandardService.class);
        springBeanMap.values().forEach(iDisplayStandardService -> {
            if (StringUtils.isNotEmpty(iDisplayStandardService.getDisplayStandardKey())) {
                displayStandardServiceMap.put(iDisplayStandardService.getDisplayStandardKey(), iDisplayStandardService);
            }
        });
    }

    public DisplayStandardService getDisplayStandardService(String key) {
        return displayStandardServiceMap.get(key);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        initTransQueryServiceMap(applicationContext);
    }
}
