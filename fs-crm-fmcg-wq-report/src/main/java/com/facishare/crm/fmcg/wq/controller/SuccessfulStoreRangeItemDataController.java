package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.api.success.ItemData;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeConstants;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

public class SuccessfulStoreRangeItemDataController extends PreDefineController<ItemData.Arg,ItemData.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected ItemData.Result doService(ItemData.Arg arg) {
        ItemData.Result result = new ItemData.Result();
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();
        searchQuery.eq(SuccessfulStoreRangeConstants.STATE, true);
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), "TPMActivityItemObj");
        searchQuery.limit(1000);
        List<IObjectData> data = ObjectUtils.queryDataSimple(serviceFacade, User.systemUser(controllerContext.getTenantId()), "TPMActivityItemObj",searchQuery.build().getSearchTemplateQuery(),describe);
        if(CollectionUtils.isNotEmpty(data)){
            List<ItemData.Item> allData = Lists.newArrayList();
            for (IObjectData datum : data) {
                allData.add(new ItemData.Item(datum.getId(),datum.getName()));
            }
            result.setData(allData);
        }

        IObjectDescribe mustProDesc = serviceFacade.findObject(controllerContext.getTenantId(), SuccessfulStoreRangeConstants.MustDistributeProductsObj);
        IFieldDescribe fieldService = mustProDesc.getFieldDescribe(SuccessfulStoreRangeConstants.DISPLAY_SCENE);
        if(Objects.nonNull(fieldService)){
            SelectOneFieldDescribe oneFieldDescribe= (SelectOneFieldDescribe)fieldService;
            List<ItemData.Item> selectData = Lists.newArrayList();
            for (ISelectOption option : oneFieldDescribe.getSelectOptions()) {
                if(option.isNotUsable()){
                    continue;
                }
                selectData.add(new ItemData.Item(option.getValue(),option.getLabel()));
            }
            result.setSceneData(selectData);
        }
        return result;
    }
}
