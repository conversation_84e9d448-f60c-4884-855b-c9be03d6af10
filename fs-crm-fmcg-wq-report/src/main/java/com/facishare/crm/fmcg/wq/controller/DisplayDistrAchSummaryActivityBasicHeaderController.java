package com.facishare.crm.fmcg.wq.controller;

import java.util.List;
import java.util.Objects;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.appserver.checkins.model.common.IdAndName;
import com.facishare.crm.fmcg.wq.constants.ActivityProofFields;
import com.facishare.crm.fmcg.wq.dao.DataReportResultDao;
import com.facishare.crm.fmcg.wq.model.RestResult;
import com.facishare.crm.fmcg.wq.report.model.ActivityBasicHeader;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

public class DisplayDistrAchSummaryActivityBasicHeaderController extends PreDefineController<ActivityBasicHeader.Arg, RestResult<ActivityBasicHeader.Result>> {

    private DataReportResultDao dataReportResultDao = SpringUtil.getContext().getBean(DataReportResultDao.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected RestResult<ActivityBasicHeader.Result> doService(ActivityBasicHeader.Arg arg) {
        checkArg(arg);
        String tenantId = controllerContext.getTenantId();
        User systemUser = User.systemUser(tenantId);
        return RestResult.<ActivityBasicHeader.Result>builder().code(0).message("success").data(buildResult(systemUser, arg)).build();
    }

    private void checkArg(ActivityBasicHeader.Arg arg){
        if(StringUtils.isBlank(arg.getActivityProofId())){
            if(StringUtils.isBlank(arg.getActionId()) || StringUtils.isBlank(arg.getCheckinId())){
                throw new ValidateException("参数错误"); //ignoreI18n
            }
        }
    }

    private ActivityBasicHeader.Result buildResult(User systemUser,ActivityBasicHeader.Arg arg){
        ActivityBasicHeader.Result result = new ActivityBasicHeader.Result();
        List<IObjectData> activityProofDataList = getActivityProofData(systemUser, arg);
        result.setDataList(Lists.newArrayList());
        activityProofDataList.stream().filter(o-> Objects.isNull(o.get("source_proof__c")) || o.getId().equals(o.get("source_proof__c",String.class))).forEach(item -> {
            ActivityBasicHeader.InnerActivityBasicHeader innerActivityBasicHeader = new ActivityBasicHeader.InnerActivityBasicHeader();
            innerActivityBasicHeader.setActivityProofInfo(new IdAndName(item.getId(), String.valueOf(item.get(ActivityProofFields.NAME))));
            innerActivityBasicHeader.setActivityAgreementInfo(new IdAndName((String)item.get(ActivityProofFields.ACTIVITY_AGREEMENT_ID), (String)item.get(ActivityProofFields.ACTIVITY_AGREEMENT_NAME)));
            innerActivityBasicHeader.setActivityApplyInfo(new IdAndName((String)item.get(ActivityProofFields.ACTIVITY_APPLY_ID), (String)item.get(ActivityProofFields.ACTIVITY_APPLY_NAME)));
            result.getDataList().add(innerActivityBasicHeader);
        });
        return result;
    }

    private List<IObjectData> getActivityProofData(User systemUser,ActivityBasicHeader.Arg arg){
        List<IObjectData> result = null;
        if(StringUtils.isNotBlank(arg.getActivityProofId())){
            // 通过活动举证Id查询活动对象
            result = dataReportResultDao.getByIds(controllerContext.getTenantId(), ActivityProofFields.API_NAME, Lists.newArrayList(arg.getActivityProofId()));
        }else{
            // 通过actionId和checkinId查询活动举证对象
            SearchQuery searchQuery = SearchQuery.builder().eq(ActivityProofFields.ACTION_ID, arg.getActionId()).eq(ActivityProofFields.CHECK_ID, arg.getCheckinId()).build();
            result = dataReportResultDao.getAllIObjectDataListByQuery(systemUser,
                    searchQuery,
                    ActivityProofFields.API_NAME);
        }
        if(CollectionUtils.isEmpty(result)){
            return Lists.newArrayList();
        }
        IObjectDescribe describe = serviceFacade.findObject(systemUser.getTenantId(),
                ActivityProofFields.API_NAME);
        serviceFacade.fillObjectDataWithRefObject(describe, result,systemUser);
        return result;
    }

}

