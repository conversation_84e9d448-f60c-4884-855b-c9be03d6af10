package com.facishare.crm.fmcg.wq.rule.model.mapping;

import java.util.List;

import com.facishare.crm.fmcg.wq.rule.model.mapping.base.FieldMappingConfigField;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * Rule Mapping Configuration
 * <p>
 * This class defines mapping relationships and transformation rules between data objects.
 * It serves two main purposes:
 * <p>
 * 1. Field Mapping: Defines how fields from source objects map to standard object fields
 * 2. Rule Configuration: Specifies how to filter, match, and process data using those mappings
 * <p>
 * The class has been restructured to separate tenant-specific configurations from base configurations.
 * <p>
 * Usage example:
 * <pre>
 * // Create tenant field mapping
 * TenantFieldMappingConfig fieldMappingConfig = new TenantFieldMappingConfig();
 * fieldMappingConfig.setConfigId("unique-id");
 * fieldMappingConfig.setSourceObjectApiName("CustomObject__c");
 * fieldMappingConfig.setStandardObjectApiName("StandardObject__c");
 * 
 * // Create tenant rule generation config
 * TenantRuleGenerationConfig ruleConfig = TenantRuleGenerationConfig.fromBaseConfig(
 *     "tenant123", baseRuleConfig);
 * 
 * // Create rule mapping config
 * RuleMappingConfig config = new RuleMappingConfig();
 * config.setFieldMappingConfig(fieldMappingConfig);
 * config.setRuleConfig(ruleConfig);
 * </pre>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantRuleRuntimeConfig {

    /**
     * Tenant-specific field mapping configuration
     */
    private TenantFieldCustomConfig tenantFieldCustomConfig;

    /**
     * Tenant-specific rule generation configuration
     */
    private TenantStandardRuleTemplate tenantStandardRuleTemplate;

    
    /**
     * For backward compatibility, this method retrieves the configId from the field mapping config
     * @return The configuration ID
     */
    public String getConfigId() {
        return tenantFieldCustomConfig != null ? tenantFieldCustomConfig.getConfigId() : null;
    }
    
    /**
     * For backward compatibility, this method retrieves the sourceObjectApiName from the field mapping config
     * @return The source object API name
     */
    public String getSourceObjectApiName() {
        return tenantFieldCustomConfig != null ? tenantFieldCustomConfig.getSourceObjectApiName() : null;
    }
    
    /**
     * For backward compatibility, this method retrieves the standardObjectApiName from the field mapping config
     * @return The standard object API name
     */
    public String getStandardObjectApiName() {
        return tenantFieldCustomConfig != null ? tenantFieldCustomConfig.getStandardObjectApiName() : null;
    }
    
    /**
     * For backward compatibility, this method retrieves the fieldMappings from the field mapping config
     * @return The field mappings
     */
    public List<FieldMappingConfigField> getFieldMappings() {
        return tenantFieldCustomConfig != null ? tenantFieldCustomConfig.getFieldMappings() : null;
    }
    
    /**
     * For backward compatibility, this method sets the fieldMappings in the field mapping config
     * @param fieldMappings The field mappings to set
     */
    public void setFieldMappings(List<FieldMappingConfigField> fieldMappings) {
        if (tenantFieldCustomConfig != null) {
            tenantFieldCustomConfig.setFieldMappings(fieldMappings);
        }
    }


    /**
     * Creates a basic configuration for mapping between two objects
     *
     * @param sourceObjectApiName   Source object API name
     * @param standardObjectApiName Standard object API name
     * @return A basic mapping configuration
     */
    public static TenantRuleRuntimeConfig createBasicConfig(String sourceObjectApiName, String standardObjectApiName) {
        TenantFieldCustomConfig fieldMappingConfig = TenantFieldCustomConfig.builder()
                .sourceObjectApiName(sourceObjectApiName)
                .standardObjectApiName(standardObjectApiName)
                .build();
                
        return TenantRuleRuntimeConfig.builder()
                .tenantFieldCustomConfig(fieldMappingConfig)
                .build();
    }

} 