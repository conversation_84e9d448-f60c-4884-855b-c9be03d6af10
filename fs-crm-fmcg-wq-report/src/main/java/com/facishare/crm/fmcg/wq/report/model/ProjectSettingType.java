package com.facishare.crm.fmcg.wq.report.model;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/21/ 16:20
 **/
@AllArgsConstructor
public enum ProjectSettingType {

    // TODO 国际化
    // 整体陈列标准
    all("1","整体","all"),
    // 产品陈列标准
    product("2","产品","product"),
    // 物料陈列标准
    material("3","物料","material");

    public final String code;
    public final String i18nKey;
    public final String serviceKey;

    public static ProjectSettingType getByCode(String code){
        for (ProjectSettingType item : ProjectSettingType.values()) {
            if(item.code.equals(code)){
                return item;
            }
        }
        return null;
    }
}
