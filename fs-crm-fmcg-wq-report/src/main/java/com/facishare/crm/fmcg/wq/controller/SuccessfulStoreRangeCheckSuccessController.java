package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.api.success.CheckSuccess;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeConstants;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeFields;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class SuccessfulStoreRangeCheckSuccessController extends PreDefineController<CheckSuccess.Arg, CheckSuccess.Result> {

    private static final Logger log = LoggerFactory.getLogger(SuccessfulStoreRangeCheckSuccessController.class);

    RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);

    BaseDao baseDao = SpringUtil.getContext().getBean(BaseDao.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected CheckSuccess.Result doService(CheckSuccess.Arg arg) {
        CheckSuccess.Result result = new CheckSuccess.Result();
        String custom = ConfigFactory.getConfig("checkin-custom-config").get("successfulCustomCheck_"+controllerContext.getTenantId());
        if(StringUtils.isNotEmpty(custom)){
            return handleCustomService(arg,custom);
        }
        IObjectData storeData = serviceFacade.findObjectData(controllerContext.getUser(),arg.getStoreId(), CommonConstants.ACCOUNT_OBJ);
        try {
            if (arg.getMatchType() == 0) {
                getResultMatchType0(arg, result, storeData);//老的完全匹配
            } else {
                getResultMatchType1(arg, result, storeData); //模糊匹配
            }
        }catch (Exception e){
            log.error("CheckSuccess error arg:{} ",arg,e);
        }


        return result;
    }

    /**
     * 模糊匹配模式 (MatchType = 1)
     *
     * 匹配策略说明：
     * 1. 采用逐级降级匹配策略，优先匹配更多条件的规则
     * 2. 第一个字段作为保底条件，必须匹配
     * 3. 后续字段按顺序进行匹配，支持部分匹配
     * 4. 匹配优先级：完全匹配 > 部分匹配 > 保底匹配
     *
     * 示例：如果有ABCD四个匹配字段，可能的匹配规则优先级为：
     * ABCD (完全匹配) > ABC_ (三字段匹配) > AB__ (两字段匹配) > A___ (保底匹配)
     *
     * @param arg 检查成功参数，包含匹配字段配置
     * @param result 返回结果对象
     * @param storeData 门店数据对象
     */
    private void getResultMatchType1(CheckSuccess.Arg arg, CheckSuccess.Result result, IObjectData storeData) {
        // 验证匹配字段配置是否为空，如果为空则直接返回
        if(CollectionUtils.isEmpty(arg.getMatchFields())){
            return;
        }

        // 构建基础查询条件
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();

        // 添加业务类型条件（如果指定）
        if (StringUtils.isNotBlank(arg.getRecordType())){
            searchQuery.eq(BaseField.recordType.getApiName(), arg.getRecordType());
        }
        //查 描述
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);
        // 添加状态过滤条件：只查询启用且显示的规则
        searchQuery.eq(SuccessfulStoreRangeFields.STATE, SuccessfulStoreRangeFields.STATE_Options_true);
        searchQuery.eq(SuccessfulStoreRangeFields.DISPLAY_STATUS, SuccessfulStoreRangeFields.DISPLAY_STATUS_Options_1);

        // 处理第一个字段作为保底匹配条件（必须匹配）
        CheckSuccess.Config config = arg.getMatchFields().get(0);
        switch (getFieldType(config.getType(),describe.getFieldDescribe(config.getRuleApiName()))) {
            case "channel":
            case "select":
                // 处理渠道或选择类型字段
                String data = storeData.get(config.getApiName(),String.class);
                if(StringUtils.isNotEmpty(data)) {
                    searchQuery.eq(config.getRuleApiName(), data);
                }else{
                    // 如果门店数据为空，则匹配规则中该字段也为空的记录
                    searchQuery.notExist(config.getRuleApiName());
                }
                break;
            case "select_many":
                // 处理多选字段
                // 通过值类型判断门店字段值的实际类型
                Object selectManyValue = storeData.get(config.getApiName());
                if (selectManyValue instanceof List) {
                    // 如果是List类型，直接使用
                    List<String> selectManyData = (List<String>) selectManyValue;
                    if(CollectionUtils.isNotEmpty(selectManyData)) {
                        searchQuery.hasAnyOf(config.getRuleApiName(), selectManyData);
                    } else {
                        searchQuery.notExist(config.getRuleApiName());
                    }
                } else if (selectManyValue instanceof String) {
                    // 如果是String类型，直接将整个字符串放到List中进行匹配
                    String selectManyString = (String) selectManyValue;
                    if(StringUtils.isNotEmpty(selectManyString)) {
                        List<String> stringValues = Lists.newArrayList();
                        stringValues.add(selectManyString);
                        searchQuery.hasAnyOf(config.getRuleApiName(), stringValues);
                    } else {
                        searchQuery.notExist(config.getRuleApiName());
                    }
                } else {
                    // 如果门店多选数据为空或其他类型，则匹配规则中该字段也为空的记录
                    searchQuery.notExist(config.getRuleApiName());
                }
                break;
            case "dept":
                // 处理部门类型字段
                ArrayList dept = storeData.get(config.getApiName(), ArrayList.class, Lists.newArrayList());
                if(CollectionUtils.isNotEmpty(dept)) {
                    searchQuery.inDepartmentNotSub(config.getRuleApiName(),dept);
                }else{
                    // 如果门店部门为空，则匹配规则中该字段也为空的记录
                    searchQuery.notExist(config.getRuleApiName());
                }
                break;
        }

        // 记录查询条件日志
        log.info( "searchQuery:{}", JSON.toJSONString(searchQuery.build()));

        // 执行查询获取符合保底条件的所有规则
        List<IObjectData> data = baseDao.getAllIObjectDataListByQuery(User.systemUser(controllerContext.getTenantId()),searchQuery.build(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);

        if (CollectionUtils.isNotEmpty(data)) {
            // 如果只有一个匹配字段（保底规则），直接返回第一个匹配的规则
            if(arg.getMatchFields().size()==1){
                result.setId(data.get(0).getId());
                return;
            }

            // 预处理：提取门店数据中其他字段的值，用于后续匹配
            for (int i = 1; i < arg.getMatchFields().size(); i++) {
                CheckSuccess.Config subConfig = arg.getMatchFields().get(i);
                subConfig.setData(getDataByConfig(storeData,subConfig,true,getFieldType(subConfig.getType(),describe.getFieldDescribe(subConfig.getRuleApiName()))));
            }

            // 创建规则ID数组，索引对应匹配字段数量
            // ruleDataId[i] 存储匹配了前i+1个字段的最佳规则ID
            String[] ruleDataId = new String[arg.getMatchFields().size()];


            // 遍历所有符合保底条件的规则，进行逐级匹配
            for (IObjectData datum : data) {
                boolean isMatch = true;  // 标记当前规则是否持续匹配
                int matchSize = 0;       // 记录当前规则匹配的字段数量

                // 从第二个字段开始进行逐级匹配
                for (int i = 1; i < arg.getMatchFields().size(); i++) {
                    CheckSuccess.Config subConfig = arg.getMatchFields().get(i);

                    // 获取规则中对应字段的值
                    Object ruleData = getDataByConfig(datum,subConfig,false,getFieldType(subConfig.getType(),describe.getFieldDescribe(subConfig.getRuleApiName())));
                    Object storeFieldData = subConfig.getData();

                    boolean fieldMatched = false;

                    // 判断匹配逻辑
                    if(isMatch) {
                        // 添加空 = 空的匹配逻辑：如果规则数据和门店数据都为空，则匹配成功
                        if (((ruleData == null) || (ruleData instanceof String && StringUtils.isEmpty((String) ruleData)) || (ruleData instanceof List && ((List<?>) ruleData).isEmpty())) &&
                                ((storeFieldData == null) || (storeFieldData instanceof String && StringUtils.isEmpty((String) storeFieldData)) || (storeFieldData instanceof List && ((List<?>) storeFieldData).isEmpty()))) {
                            fieldMatched = true;
                        } else if(ruleData instanceof List && !((List<?>) ruleData).isEmpty()) {
                            // 规则数据是List类型，使用hasAny匹配
                            List<?> ruleList = (List<?>) ruleData;
                            if(storeFieldData instanceof String && StringUtils.isNotEmpty((String) storeFieldData)) {
                                // 门店数据是字符串，检查是否包含在规则列表中
                                String[] storeValues = ((String) storeFieldData).split(",");
                                for(String storeValue : storeValues) {
                                    if(ruleList.contains(storeValue.trim())) {
                                        fieldMatched = true;
                                    }
                                }
                            } else if(storeFieldData instanceof List && CollectionUtils.isNotEmpty((List<?>) storeFieldData)) {
                                // 门店数据也是List类型，检查是否有交集
                                List<?> storeList = (List<?>) storeFieldData;
                                for(Object storeItem : storeList) {
                                    if(ruleList.contains(storeItem)) {
                                        fieldMatched = true;
                                    }
                                }
                            }
                        } else if(ruleData instanceof String) {
                            // 规则数据是String类型，使用完全匹配
                            if(storeFieldData instanceof String) {
                                fieldMatched = Objects.equals(ruleData, storeFieldData);
                            } else if(storeFieldData instanceof List && CollectionUtils.isNotEmpty((List<?>) storeFieldData)) {
                                // 门店数据是List，规则数据是String，检查List中是否包含该String
                                List<?> storeList = (List<?>) storeFieldData;
                                fieldMatched = storeList.contains(ruleData);
                            }
                        }
                    }

                    if(fieldMatched){
                        // 字段值匹配
                        matchSize++;
                        if(i==arg.getMatchFields().size()-1){
                            // 如果是最后一个字段，记录该规则ID
                            ruleDataId[matchSize]=datum.getId();
                        }
                    }else if((ruleData instanceof String && StringUtils.isEmpty((String) ruleData)) ||
                             (ruleData instanceof List && ((List<?>) ruleData).isEmpty())){
                        // 规则中该字段为空，视为通配符，可以匹配任意值
                        if(i==arg.getMatchFields().size()-1){
                            // 如果是最后一个字段，记录该规则ID
                            ruleDataId[matchSize]=datum.getId();
                        }
                        isMatch = false; // 标记为非完全匹配
                    }else {
                        // 字段值不匹配且规则字段不为空，停止匹配该规则
                        break;
                    }
                }
            }

            // 查找最适配的规则：从匹配字段最多的开始查找
            log.info("storeId:{} ,ruleDataId:{}",arg.getStoreId(), Arrays.toString(ruleDataId));
            for (int i = ruleDataId.length - 1; i >= 0; i--) {
                if(StringUtils.isNotEmpty(ruleDataId[i])){
                    result.setId(ruleDataId[i]);
                    break;
                }
            }
        }
    }

    /**
     * 根据配置从数据对象中提取字段值
     *
     * @param data 数据对象（门店数据或规则数据）
     * @param subConfig 字段配置信息
     * @param isData true-从门店数据中提取，false-从规则数据中提取
     * @return 提取的字段值，String类型或List类型
     */
    private Object getDataByConfig(IObjectData data, CheckSuccess.Config subConfig,boolean isData,String fieldType) {
        // 根据isData标志选择对应的字段API名称
        String field = isData?subConfig.getApiName():subConfig.getRuleApiName();
        fieldType = isData?subConfig.getType():fieldType;

        switch (fieldType) {
            case "channel":
            case "select":
                // 处理渠道或选择类型字段，直接获取字符串值
                return data.get(field, String.class,"");
            case "select_many":
                // 处理多选字段，返回List类型
                // 通过值类型判断门店字段值的实际类型
                Object fieldValue = data.get(field);
                if (fieldValue instanceof List) {
                    // 如果是List类型，直接返回
                    List<String> selectManyList = (List<String>) fieldValue;
                    return CollectionUtils.isNotEmpty(selectManyList) ? selectManyList : Lists.newArrayList();
                } else if (fieldValue instanceof String) {
                    // 如果是String类型，直接将整个字符串放到List中
                    String selectManyString = (String) fieldValue;
                    if(StringUtils.isNotEmpty(selectManyString)) {
                        List<String> result = Lists.newArrayList();
                        result.add(selectManyString);
                        return result;
                    }
                }
                return Lists.newArrayList();
            case "dept":
                // 处理部门类型字段，部门通常是数组，取第一个元素
                ArrayList dept = data.get(field, ArrayList.class, Lists.newArrayList());
                if(CollectionUtils.isNotEmpty(dept)) {
                    return dept.get(0).toString();
                }
                return "";
        }
        return null;
    }



    /**
     * 完全匹配模式 (MatchType = 0)
     *
     * 匹配策略说明：
     * 1. 严格按照所有配置条件进行精确匹配
     * 2. 支持渠道层级匹配（包含上级渠道）
     * 3. 支持部门层级匹配（包含上级部门）
     * 4. 当匹配到多个规则时，按优先级排序选择最佳匹配
     *
     * 排序优先级：
     * 1. 部门层级越低（越具体）优先级越高
     * 2. 渠道层级越低（越具体）优先级越高
     *
     * @param arg 检查成功参数，包含成功配置
     * @param result 返回结果对象
     * @param storeData 门店数据对象
     */
    private void getResultMatchType0(CheckSuccess.Arg arg, CheckSuccess.Result result, IObjectData storeData) {
        // 构建基础查询条件
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();

        // 添加业务类型条件（新版本匹配逻辑）
        if (StringUtils.isNotBlank(arg.getRecordType())){
            searchQuery.eq(BaseField.recordType.getApiName(), arg.getRecordType());
        }

        // 添加状态过滤条件：只查询启用且显示的规则
        searchQuery.eq(SuccessfulStoreRangeFields.STATE, SuccessfulStoreRangeFields.STATE_Options_true);
        searchQuery.eq(SuccessfulStoreRangeFields.DISPLAY_STATUS, SuccessfulStoreRangeFields.DISPLAY_STATUS_Options_1);

        if(Objects.nonNull(storeData)){
            boolean flag = false;                    // 标记是否有有效的匹配条件
            List<String> deptTemp = Lists.newArrayList();  // 存储部门层级列表
            String deptApi = "";                     // 部门字段API名称
            String channelApi ="";                   // 渠道字段API名称
            List<String> channelList = Lists.newArrayList(); // 存储渠道层级列表
            //查 描述
            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);

            // 遍历所有成功配置，构建查询条件
            for (Map.Entry<String, CheckSuccess.Config> entry : arg.getSuccessConfig().entrySet()) {
                if(Objects.nonNull(entry.getValue()) && Objects.nonNull(entry.getValue().getApiName())) {
                    switch (getFieldType(entry.getValue().getType(),describe.getFieldDescribe(entry.getValue().getRuleApiName()))) {
                        case "channel":
                        case "select":
                            // 处理渠道或选择类型字段
                            String data = storeData.get(entry.getValue().getApiName(),String.class);
                            if(StringUtils.isNotEmpty(data)) {
                                // 特殊处理渠道对象：需要包含上级渠道
                                if("ChannelObj".equals(entry.getValue().getObjApiName())){
                                    channelList.addAll(getChannelList(data));
                                    if(CollectionUtils.isNotEmpty(channelList)) {
                                        // 使用IN查询匹配当前渠道及其所有上级渠道
                                        searchQuery.in(entry.getKey(), channelList);
                                        channelApi = entry.getKey();
                                    }else{
                                        // 如果获取渠道层级失败，则进行精确匹配
                                        searchQuery.eq(entry.getKey(), data);
                                    }
                                }else {
                                    // 非渠道对象进行精确匹配
                                    searchQuery.eq(entry.getKey(), data);
                                }
                            }else{
                                // 如果门店数据为空，则匹配规则中该字段也为空的记录
                                searchQuery.notExist(entry.getKey());
                            }
                            flag = true;
                            break;
                        case "select_many":
                            // 处理多选字段
                            // 通过值类型判断门店字段值的实际类型
                            Object selectManyValue = storeData.get(entry.getValue().getApiName());
                            if (selectManyValue instanceof List) {
                                // 如果是List类型，直接使用
                                List<String> selectManyData = (List<String>) selectManyValue;
                                if(CollectionUtils.isNotEmpty(selectManyData)) {
                                    // 特殊处理渠道对象：需要包含上级渠道
                                    if("ChannelObj".equals(entry.getValue().getObjApiName())){
                                        List<String> expandedChannelList = getChannelList(selectManyData);
                                        if(CollectionUtils.isNotEmpty(expandedChannelList)) {
                                            searchQuery.hasAnyOf(entry.getKey(), expandedChannelList);
                                        } else {
                                            searchQuery.hasAnyOf(entry.getKey(), selectManyData);
                                        }
                                    } else {
                                        // 非渠道对象进行常规多选匹配
                                        searchQuery.hasAnyOf(entry.getKey(), selectManyData);
                                    }
                                } else {
                                    searchQuery.notExist(entry.getKey());
                                }
                            } else if (selectManyValue instanceof String) {
                                // 如果是String类型，直接将整个字符串放到List中进行匹配
                                String selectManyString = (String) selectManyValue;
                                if(StringUtils.isNotEmpty(selectManyString)) {
                                    List<String> stringValues = Lists.newArrayList();
                                    stringValues.add(selectManyString);
                                    // 特殊处理渠道对象：需要包含上级渠道
                                    if("ChannelObj".equals(entry.getValue().getObjApiName())){
                                        List<String> expandedChannelList = getChannelList(stringValues);
                                        if(CollectionUtils.isNotEmpty(expandedChannelList)) {
                                            searchQuery.hasAnyOf(entry.getKey(), expandedChannelList);
                                        } else {
                                            searchQuery.hasAnyOf(entry.getKey(), stringValues);
                                        }
                                    } else {
                                        // 非渠道对象进行常规多选匹配
                                        searchQuery.hasAnyOf(entry.getKey(), stringValues);
                                    }
                                } else {
                                    searchQuery.notExist(entry.getKey());
                                }
                            } else {
                                // 如果门店多选数据为空或其他类型，则匹配规则中该字段也为空的记录
                                searchQuery.notExist(entry.getKey());
                            }
                            flag = true;
                            break;
                        case "dept":
                            // 处理部门类型字段
                            ArrayList dept = storeData.get(entry.getValue().getApiName(), ArrayList.class, Lists.newArrayList());
                            if(CollectionUtils.isNotEmpty(dept)) {
                                // 获取部门的所有上级部门
                                Map<String, List<String>> depts = serviceFacade.getAllSuperDeptIdsByDeptIds(controllerContext.getTenantId(), User.systemUser(controllerContext.getTenantId()).getUserId(),dept);
                                for (String depId: depts.get(dept.get(0))) {
                                    deptTemp.add(depId);
                                }
                                deptApi = entry.getKey();
                                // 使用部门层级查询（不包含子部门）
                                searchQuery.inDepartmentNotSub(entry.getKey(), deptTemp);
                                flag = true;
                            }else{
                                // 如果门店部门为空，则匹配规则中该字段也为空的记录
                                searchQuery.notExist(entry.getKey());
                            }
                            break;
                    }
                }
            }

            // 只有在有有效匹配条件时才执行查询
            if(flag) {
                List<IObjectData> data = baseDao.getAllIObjectDataListByQuery(User.systemUser(controllerContext.getTenantId()),searchQuery.build(), SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj);
                if (CollectionUtils.isNotEmpty(data)) {
                    // 如果只匹配到一个规则，直接返回
                    if(data.size() == 1){
                        result.setId(data.get(0).getId());
                    }

                    // 保存API名称供排序使用
                    final String deptApiFinal = deptApi;
                    final String channelApiFinal = channelApi;

                    // 如果匹配到多个规则，需要按优先级排序
                    if(data.size() > 1){
                        Collections.sort(data, new Comparator<IObjectData>() {
                            @Override
                            public int compare(IObjectData o1, IObjectData o2) {
                                // 首先按部门层级排序（层级越低优先级越高）
                                Integer dept1 = getDeptSize(o1);
                                Integer dept2 = getDeptSize(o2);
                                int res = dept1.compareTo(dept2);
                                if(res!=0){
                                    return res;
                                }
                                // 如果部门层级相同，再按渠道层级排序
                                if(CollectionUtils.isNotEmpty(channelList)){
                                    return getChannelSize(o1).compareTo(getChannelSize(o2));
                                }
                                return res ;
                            }

                            /**
                             * 计算部门层级大小
                             * @param o 规则数据对象
                             * @return 部门在层级列表中的索引，索引越小优先级越高
                             */
                            public Integer getDeptSize(IObjectData o){
                                List<String> dept = o.get(deptApiFinal, ArrayList.class, Lists.newArrayList());
                                if(CollectionUtils.isNotEmpty(deptTemp) && CollectionUtils.isNotEmpty(dept)) {
                                    int size =  deptTemp.indexOf(dept.get(0));
                                    return size==-1?deptTemp.size():size;
                                }
                                return deptTemp.size();
                            }

                            /**
                             * 计算渠道层级大小
                             * @param o 规则数据对象
                             * @return 渠道在层级列表中的索引，索引越小优先级越高
                             */
                            public Integer getChannelSize(IObjectData o){
                                String data = o.get(channelApiFinal,String.class,"");
                                if(StringUtils.isNotEmpty(data)) {
                                    int size = channelList.indexOf(data);
                                    return size==-1?channelList.size():size;
                                }
                                return channelList.size();
                            }
                        });
                        // 返回排序后的第一个（优先级最高的）规则
                        result.setId(data.get(0).getId());
                    }
                }
            }
        }
    }

    private CheckSuccess.Result handleCustomService(CheckSuccess.Arg arg, String custom) {
        CheckSuccess.Result result = new CheckSuccess.Result();
        JSONObject config = JSONObject.parseObject(custom);

        if(MapUtils.isNotEmpty(arg.getCustomData())){
            String objApi= config.getString("objApi");
            if(arg.getCustomData().containsKey(objApi)){
                IObjectData data = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()),arg.getCustomData().get(objApi),objApi);
                String succ = data.get(config.getString("field"),String.class);
                if(StringUtils.isNotEmpty(succ)){
                    result.setId(succ);
                }
            }
        }

        return result;
    }


    /**
     * 获取多个渠道ID及其上级渠道列表
     * @param ids 渠道ID列表
     * @return 合并后的渠道ID列表（包含所有渠道及其上级渠道，已去重）
     */
    private List<String> getChannelList(List<String> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }

        Set<String> resultSet = new LinkedHashSet<>();
        JSONObject data = getChannelData();

        for(String id : ids) {
            if(StringUtils.isNotEmpty(id)) {
                List<String> channelList = Lists.newArrayList();
                channelList.add(id);
                getParentChannel(id, channelList, data);
                resultSet.addAll(channelList);
            }
        }

        return Lists.newArrayList(resultSet);
    }

    /**
     * 获取多个渠道ID及其上级渠道列表（可变参数版本）
     * @param ids 渠道ID可变参数
     * @return 合并后的渠道ID列表（包含所有渠道及其上级渠道，已去重）
     */
    private List<String> getChannelList(String... ids) {
        if(ids == null || ids.length == 0){
            return Lists.newArrayList();
        }
        return getChannelList(Arrays.asList(ids));
    }

    /**
     * 获取渠道数据，优先从Redis获取，如果没有则从数据库查询并缓存到Redis
     * @return 渠道数据JSONObject，key为渠道ID，value为上级渠道ID
     */
    private JSONObject getChannelData() {
        String channelData = redisUtils.getChannelData(controllerContext.getTenantId());
        JSONObject data = new JSONObject();
        if(StringUtils.isEmpty(channelData)) {
            SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();
            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), "ChannelObj");
            searchQuery.limit(1000);
            List<IObjectData> channelObj = ObjectUtils.queryDataSimple(serviceFacade, User.systemUser(controllerContext.getTenantId()), "ChannelObj",searchQuery.build().getSearchTemplateQuery(),describe);
            if(CollectionUtils.isNotEmpty(channelObj)){
                for (IObjectData datum : channelObj) {
                    data.put(datum.getId(),datum.get("superior_channel",String.class,""));
                }
            }
            redisUtils.setChannelData(controllerContext.getTenantId(), data.toString());
        }else {
            data = JSON.parseObject(channelData);
        }
        return data;
    }

    private void getParentChannel(String id, List<String> res, JSONObject data) {
        String parent = data.getString(id);
        if(StringUtils.isNotEmpty(parent)&& !res.contains(parent)){
            res.add(parent);
            getParentChannel(parent,res,data);
        }else{
            return;
        }
    }

    // ==================== 重构后的代码 ====================

    /**
     * 模糊匹配模式 (MatchType = 1) - 重构版本
     *
     * 采用逐级降级匹配策略，优先匹配更多条件的规则
     *
     * @param arg 检查成功参数
     * @param result 返回结果对象
     * @param storeData 门店数据对象
     */
    private void getResultMatchType1Refactored(CheckSuccess.Arg arg, CheckSuccess.Result result, IObjectData storeData) {
        if (CollectionUtils.isEmpty(arg.getMatchFields())) {
            return;
        }

        // 1. 构建基础查询条件
        SearchQuery.SearchQueryBuilder baseQuery = buildBaseQuery(arg);

        // 2. 添加保底匹配条件（第一个字段必须匹配）
        if (!addFallbackCondition(baseQuery, arg.getMatchFields().get(0), storeData)) {
            return; // 保底条件构建失败，直接返回
        }

        // 3. 查询符合保底条件的所有规则
        List<IObjectData> candidateRules = queryCandidateRules(baseQuery);
        if (CollectionUtils.isEmpty(candidateRules)) {
            return;
        }

        // 4. 如果只有一个匹配字段，直接返回第一个规则
        if (arg.getMatchFields().size() == 1) {
            result.setId(candidateRules.get(0).getId());
            return;
        }

        // 5. 预处理门店数据
        List<MatchFieldData> storeFieldDataList = preprocessStoreData(arg.getMatchFields(), storeData);

        // 6. 执行逐级匹配算法
        String bestRuleId = findBestMatchingRule(candidateRules, storeFieldDataList);

        // 7. 设置结果
        if (StringUtils.isNotEmpty(bestRuleId)) {
            result.setId(bestRuleId);
        }
    }

    /**
     * 构建基础查询条件
     */
    private SearchQuery.SearchQueryBuilder buildBaseQuery(CheckSuccess.Arg arg) {
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();

        if (StringUtils.isNotBlank(arg.getRecordType())) {
            searchQuery.eq(BaseField.recordType.getApiName(), arg.getRecordType());
        }

        // 添加状态过滤条件：只查询启用且显示的规则
        searchQuery.eq(SuccessfulStoreRangeFields.STATE, SuccessfulStoreRangeFields.STATE_Options_true);
        searchQuery.eq(SuccessfulStoreRangeFields.DISPLAY_STATUS, SuccessfulStoreRangeFields.DISPLAY_STATUS_Options_1);

        return searchQuery;
    }

    /**
     * 添加保底匹配条件（第一个字段必须匹配）
     */
    private boolean addFallbackCondition(SearchQuery.SearchQueryBuilder searchQuery,
                                       CheckSuccess.Config config,
                                       IObjectData storeData) {
        try {
            IObjectDescribe describe = serviceFacade.findObject(
                controllerContext.getTenantId(),
                SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj
            );

            String fieldType = getFieldType(config.getType(), describe.getFieldDescribe(config.getRuleApiName()));

            switch (fieldType) {
                case "channel":
                case "select":
                    return addSelectFieldCondition(searchQuery, config, storeData);
                case "select_many":
                    return addSelectManyFieldCondition(searchQuery, config, storeData);
                case "dept":
                    return addDepartmentFieldCondition(searchQuery, config, storeData);
                default:
                    log.warn("不支持的字段类型: {}", fieldType);
                    return false;
            }
        } catch (Exception e) {
            log.error("添加保底匹配条件失败", e);
            return false;
        }
    }

    /**
     * 添加选择字段条件
     */
    private boolean addSelectFieldCondition(SearchQuery.SearchQueryBuilder searchQuery,
                                          CheckSuccess.Config config,
                                          IObjectData storeData) {
        String storeValue = storeData.get(config.getApiName(), String.class);
        if (StringUtils.isNotEmpty(storeValue)) {
            searchQuery.eq(config.getRuleApiName(), storeValue);
        } else {
            searchQuery.notExist(config.getRuleApiName());
        }
        return true;
    }

    /**
     * 添加多选字段条件
     */
    private boolean addSelectManyFieldCondition(SearchQuery.SearchQueryBuilder searchQuery,
                                              CheckSuccess.Config config,
                                              IObjectData storeData) {
        Object storeValue = storeData.get(config.getApiName());
        Set<String> storeValueSet = DataConverter.toStringSet(storeValue);

        if (CollectionUtils.isNotEmpty(storeValueSet)) {
            searchQuery.hasAnyOf(config.getRuleApiName(), new ArrayList<>(storeValueSet));
        } else {
            searchQuery.notExist(config.getRuleApiName());
        }
        return true;
    }

    /**
     * 添加部门字段条件
     */
    private boolean addDepartmentFieldCondition(SearchQuery.SearchQueryBuilder searchQuery,
                                              CheckSuccess.Config config,
                                              IObjectData storeData) {
        ArrayList<String> departments = storeData.get(config.getApiName(), ArrayList.class, Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(departments)) {
            searchQuery.inDepartmentNotSub(config.getRuleApiName(), departments);
        } else {
            searchQuery.notExist(config.getRuleApiName());
        }
        return true;
    }

    /**
     * 查询候选规则
     */
    private List<IObjectData> queryCandidateRules(SearchQuery.SearchQueryBuilder searchQuery) {
        try {
            log.info("查询条件: {}", JSON.toJSONString(searchQuery.build()));
            return baseDao.getAllIObjectDataListByQuery(
                User.systemUser(controllerContext.getTenantId()),
                searchQuery.build(),
                SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj
            );
        } catch (Exception e) {
            log.error("查询候选规则失败", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 预处理门店数据
     */
    private List<MatchFieldData> preprocessStoreData(List<CheckSuccess.Config> matchFields, IObjectData storeData) {
        List<MatchFieldData> storeFieldDataList = Lists.newArrayList();

        // 跳过第一个字段（已用作保底条件）
        for (int i = 1; i < matchFields.size(); i++) {
            CheckSuccess.Config config = matchFields.get(i);
            try {
                IObjectDescribe describe = serviceFacade.findObject(
                    controllerContext.getTenantId(),
                    SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj
                );

                String fieldType = getFieldType(config.getType(), describe.getFieldDescribe(config.getRuleApiName()));
                Object fieldValue = extractFieldValue(storeData, config, fieldType);

                storeFieldDataList.add(new MatchFieldData(config, fieldValue, fieldType));
            } catch (Exception e) {
                log.error("预处理门店数据失败，字段: {}", config.getApiName(), e);
                storeFieldDataList.add(new MatchFieldData(config, null, "unknown"));
            }
        }

        return storeFieldDataList;
    }

    /**
     * 查找最佳匹配规则（修复版本，与原始算法保持一致）
     */
    private String findBestMatchingRule(List<IObjectData> candidateRules, List<MatchFieldData> storeFieldDataList) {
        int totalFields = storeFieldDataList.size() + 1; // +1 for fallback field
        String[] ruleDataId = new String[totalFields + 1]; // 使用与原始算法相同的数组结构

        // 遍历所有符合保底条件的规则，进行逐级匹配（与原始算法一致）
        for (IObjectData rule : candidateRules) {
            boolean isMatch = true;  // 标记当前规则是否持续匹配
            int matchSize = 1;       // 记录当前规则匹配的字段数量（保底字段已匹配）

            // 遍历除第一个字段外的其他字段进行匹配
            for (int i = 0; i < storeFieldDataList.size(); i++) {
                MatchFieldData storeFieldData = storeFieldDataList.get(i);
                Object ruleValue = rule.get(storeFieldData.getConfig().getRuleApiName());
                Object storeValue = storeFieldData.getValue();

                boolean fieldMatched = false;

                // 使用与原始算法完全相同的匹配逻辑
                if (isMatch) {
                    fieldMatched = evaluateFieldMatchOriginalLogic(ruleValue, storeValue);
                }

                if (fieldMatched) {
                    // 字段值匹配
                    matchSize++;
                    if (i == storeFieldDataList.size() - 1) {
                        // 如果是最后一个字段，记录该规则ID
                        ruleDataId[matchSize] = rule.getId();
                    }
                } else if (isEmptyRuleValue(ruleValue)) {
                    // 规则中该字段为空，视为通配符，可以匹配任意值
                    if (i == storeFieldDataList.size() - 1) {
                        // 如果是最后一个字段，记录该规则ID
                        ruleDataId[matchSize] = rule.getId();
                    }
                    isMatch = false; // 标记为非完全匹配
                } else {
                    // 字段值不匹配且规则字段不为空，停止匹配该规则
                    break;
                }
            }
        }

        // 查找最适配的规则：从匹配字段最多的开始查找（与原始算法一致）
        log.info("ruleDataId: {}", Arrays.toString(ruleDataId));
        for (int i = ruleDataId.length - 1; i >= 0; i--) {
            if (StringUtils.isNotEmpty(ruleDataId[i])) {
                return ruleDataId[i];
            }
        }

        return null;
    }



    // ==================== 统一的工具方法 ====================

    /**
     * 获取字段类型（统一版本）
     */
    private String getFieldType(String configType, IFieldDescribe fieldDescribe) {
        if (fieldDescribe != null) {
            String dbType = fieldDescribe.getType();
            if ("department_many".equals(dbType) || "select_many".equals(dbType) || "object_reference_many".equals(dbType)) {
                return "select_many";
            }
        }
        return configType;
    }

    /**
     * 统一的数据转换工具类
     */
    private static class DataConverter {

        /**
         * 转换为字符串集合
         */
        public static Set<String> toStringSet(Object value) {
            Set<String> result = Sets.newHashSet();
            if (value instanceof List) {
                ((List<?>) value).forEach(item -> {
                    if (item != null) {
                        result.add(item.toString().trim());
                    }
                });
            } else if (value instanceof String && StringUtils.isNotEmpty((String) value)) {
                String[] items = ((String) value).split(",");
                for (String item : items) {
                    String trimmed = item.trim();
                    if (StringUtils.isNotEmpty(trimmed)) {
                        result.add(trimmed);
                    }
                }
            }
            return result;
        }

        /**
         * 转换为字符串列表
         */
        public static List<String> toStringList(Object value) {
            List<String> result = Lists.newArrayList();

            if (value instanceof List) {
                ((List<?>) value).forEach(item -> {
                    if (item != null) {
                        result.add(item.toString());
                    }
                });
            } else if (value instanceof String && StringUtils.isNotEmpty((String) value)) {
                result.add((String) value);
            }

            return result;
        }

        /**
         * 转换为字符串
         */
        public static String toString(Object value) {
            return value != null ? value.toString() : "";
        }

        /**
         * 判断值是否为空
         */
        public static boolean isEmpty(Object value) {
            if (value == null) {
                return true;
            }
            if (value instanceof String) {
                return StringUtils.isEmpty((String) value);
            }
            if (value instanceof List) {
                return ((List<?>) value).isEmpty();
            }
            return false;
        }
    }

    /**
     * 转换为字符串集合，优化性能
     */
    private Set<String> convertToStringSet(Object value) {
        Set<String> result = Sets.newHashSet();
        if (value instanceof List) {
            ((List<?>) value).forEach(item -> {
                if (item != null) {
                    result.add(item.toString().trim());
                }
            });
        } else if (value instanceof String && StringUtils.isNotEmpty((String) value)) {
            String[] items = ((String) value).split(",");
            for (String item : items) {
                String trimmed = item.trim();
                if (StringUtils.isNotEmpty(trimmed)) {
                    result.add(trimmed);
                }
            }
        }
        return result;
    }

    /**
     * 使用与原始算法完全相同的字段匹配逻辑
     */
    private boolean evaluateFieldMatchOriginalLogic(Object ruleData, Object storeFieldData) {
        boolean fieldMatched = false;

        if (ruleData instanceof List && !((List<?>) ruleData).isEmpty()) {
            // 规则数据是List类型，使用hasAny匹配
            List<?> ruleList = (List<?>) ruleData;
            if (storeFieldData instanceof String && StringUtils.isNotEmpty((String) storeFieldData)) {
                // 门店数据是字符串，检查是否包含在规则列表中
                String[] storeValues = ((String) storeFieldData).split(",");
                for (String storeValue : storeValues) {
                    if (ruleList.contains(storeValue.trim())) {
                        fieldMatched = true;
                    }
                }
            } else if (storeFieldData instanceof List && CollectionUtils.isNotEmpty((List<?>) storeFieldData)) {
                // 门店数据也是List类型，检查是否有交集
                List<?> storeList = (List<?>) storeFieldData;
                for (Object storeItem : storeList) {
                    if (ruleList.contains(storeItem)) {
                        fieldMatched = true;
                    }
                }
            }
        } else if (ruleData instanceof String) {
            // 规则数据是String类型，使用完全匹配
            if (storeFieldData instanceof String) {
                fieldMatched = Objects.equals(ruleData, storeFieldData);
            } else if (storeFieldData instanceof List && CollectionUtils.isNotEmpty((List<?>) storeFieldData)) {
                // 门店数据是List，规则数据是String，检查List中是否包含该String
                List<?> storeList = (List<?>) storeFieldData;
                fieldMatched = storeList.contains(ruleData);
            }
        }

        return fieldMatched;
    }

    /**
     * 判断规则值是否为空（用于通配符逻辑）
     */
    private boolean isEmptyRuleValue(Object ruleData) {
        return (ruleData instanceof String && StringUtils.isEmpty((String) ruleData)) ||
               (ruleData instanceof List && ((List<?>) ruleData).isEmpty());
    }



    /**
     * 提取字段值
     */
    private Object extractFieldValue(IObjectData storeData, CheckSuccess.Config config, String fieldType) {
        return getDataByConfig(storeData, config, true, fieldType);
    }

    // ==================== getResultMatchType0 重构版本 ====================

    /**
     * 完全匹配模式 (MatchType = 0) - 重构版本
     *
     * 采用严格匹配策略，支持层级匹配和优先级排序
     *
     * @param arg 检查成功参数
     * @param result 返回结果对象
     * @param storeData 门店数据对象
     */
    private void getResultMatchType0Refactored(CheckSuccess.Arg arg, CheckSuccess.Result result, IObjectData storeData) {
        if (storeData == null) {
            return;
        }

        // 1. 构建基础查询条件
        SearchQuery.SearchQueryBuilder baseQuery = buildBaseQuery(arg);

        // 2. 构建匹配条件上下文
        MatchContext matchContext = buildMatchContext(arg, storeData);

        // 3. 添加所有匹配条件
        if (!addAllMatchConditions(baseQuery, matchContext)) {
            return; // 没有有效的匹配条件
        }

        // 4. 查询匹配的规则
        List<IObjectData> matchedRules = queryMatchedRules(baseQuery);
        if (CollectionUtils.isEmpty(matchedRules)) {
            return;
        }

        // 5. 选择最佳规则
        String bestRuleId = selectBestRuleForType0(matchedRules, matchContext);
        if (StringUtils.isNotEmpty(bestRuleId)) {
            result.setId(bestRuleId);
        }
    }

    /**
     * 构建Type0的基础查询条件
     */
    private SearchQuery.SearchQueryBuilder buildBaseQueryForType0(CheckSuccess.Arg arg) {
        SearchQuery.SearchQueryBuilder searchQuery = SearchQuery.builder();

        // 添加业务类型条件
        if (StringUtils.isNotBlank(arg.getRecordType())) {
            searchQuery.eq(BaseField.recordType.getApiName(), arg.getRecordType());
        }

        // 添加状态过滤条件：只查询启用且显示的规则
        searchQuery.eq(SuccessfulStoreRangeFields.STATE, SuccessfulStoreRangeFields.STATE_Options_true);
        searchQuery.eq(SuccessfulStoreRangeFields.DISPLAY_STATUS, SuccessfulStoreRangeFields.DISPLAY_STATUS_Options_1);

        return searchQuery;
    }

    /**
     * 构建匹配上下文
     */
    private MatchContext buildMatchContext(CheckSuccess.Arg arg, IObjectData storeData) {
        try {
            IObjectDescribe describe = serviceFacade.findObject(
                controllerContext.getTenantId(),
                SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj
            );

            MatchContext context = new MatchContext();
            context.setSuccessConfig(arg.getSuccessConfig());
            context.setStoreData(storeData);
            context.setObjectDescribe(describe);

            return context;
        } catch (Exception e) {
            log.error("构建匹配上下文失败", e);
            return new MatchContext();
        }
    }

    /**
     * 添加所有匹配条件
     */
    private boolean addAllMatchConditions(SearchQuery.SearchQueryBuilder searchQuery, MatchContext context) {
        boolean hasValidCondition = false;

        for (Map.Entry<String, CheckSuccess.Config> entry : context.getSuccessConfig().entrySet()) {
            CheckSuccess.Config config = entry.getValue();
            if (config == null || config.getApiName() == null) {
                continue;
            }

            String fieldType = getFieldType(config.getType(),
                context.getObjectDescribe().getFieldDescribe(config.getRuleApiName()));

            boolean conditionAdded = addSingleMatchCondition(searchQuery, entry.getKey(), config, fieldType, context);
            if (conditionAdded) {
                hasValidCondition = true;
            }
        }

        return hasValidCondition;
    }

    /**
     * 添加单个匹配条件
     */
    private boolean addSingleMatchCondition(SearchQuery.SearchQueryBuilder searchQuery,
                                          String ruleFieldName,
                                          CheckSuccess.Config config,
                                          String fieldType,
                                          MatchContext context) {
        switch (fieldType) {
            case "channel":
            case "select":
                return addSelectConditionForType0(searchQuery, ruleFieldName, config, context);
            case "select_many":
                return addSelectManyConditionForType0(searchQuery, ruleFieldName, config, context);
            case "dept":
                return addDepartmentConditionForType0(searchQuery, ruleFieldName, config, context);
            default:
                log.warn("不支持的字段类型: {}", fieldType);
                return false;
        }
    }

    /**
     * 添加选择字段条件（Type0版本）
     */
    private boolean addSelectConditionForType0(SearchQuery.SearchQueryBuilder searchQuery,
                                             String ruleFieldName,
                                             CheckSuccess.Config config,
                                             MatchContext context) {
        String storeValue = context.getStoreData().get(config.getApiName(), String.class);

        if (StringUtils.isNotEmpty(storeValue)) {
            if ("ChannelObj".equals(config.getObjApiName())) {
                // 处理渠道对象：包含上级渠道
                List<String> channelHierarchy = getChannelList(storeValue);
                if (CollectionUtils.isNotEmpty(channelHierarchy)) {
                    searchQuery.in(ruleFieldName, channelHierarchy);
                    context.setChannelApi(ruleFieldName);
                    context.setChannelHierarchy(channelHierarchy);
                } else {
                    searchQuery.eq(ruleFieldName, storeValue);
                }
            } else {
                searchQuery.eq(ruleFieldName, storeValue);
            }
        } else {
            searchQuery.notExist(ruleFieldName);
        }

        return true;
    }

    /**
     * 添加多选字段条件（Type0版本）
     */
    private boolean addSelectManyConditionForType0(SearchQuery.SearchQueryBuilder searchQuery,
                                                 String ruleFieldName,
                                                 CheckSuccess.Config config,
                                                 MatchContext context) {
        Object storeValue = context.getStoreData().get(config.getApiName());
        List<String> storeValues = DataConverter.toStringList(storeValue);

        if (CollectionUtils.isNotEmpty(storeValues)) {
            if ("ChannelObj".equals(config.getObjApiName())) {
                // 处理渠道对象：包含上级渠道
                List<String> expandedChannelList = getChannelList(storeValues);
                if (CollectionUtils.isNotEmpty(expandedChannelList)) {
                    searchQuery.hasAnyOf(ruleFieldName, expandedChannelList);
                } else {
                    searchQuery.hasAnyOf(ruleFieldName, storeValues);
                }
            } else {
                searchQuery.hasAnyOf(ruleFieldName, storeValues);
            }
        } else {
            searchQuery.notExist(ruleFieldName);
        }

        return true;
    }

    // ==================== 内部数据类 ====================

    /**
     * 匹配字段数据封装类
     */
    private static class MatchFieldData {
        private final CheckSuccess.Config config;
        private final Object value;
        private final String fieldType;

        public MatchFieldData(CheckSuccess.Config config, Object value, String fieldType) {
            this.config = config;
            this.value = value;
            this.fieldType = fieldType;
        }

        public CheckSuccess.Config getConfig() {
            return config;
        }

        public Object getValue() {
            return value;
        }

        public String getFieldType() {
            return fieldType;
        }
    }

    /**
     * 添加部门字段条件（Type0版本）
     */
    private boolean addDepartmentConditionForType0(SearchQuery.SearchQueryBuilder searchQuery,
                                                 String ruleFieldName,
                                                 CheckSuccess.Config config,
                                                 MatchContext context) {
        ArrayList<String> departments = context.getStoreData().get(config.getApiName(), ArrayList.class, Lists.newArrayList());

        if (CollectionUtils.isNotEmpty(departments)) {
            try {
                // 获取部门的所有上级部门
                Map<String, List<String>> deptHierarchyMap = serviceFacade.getAllSuperDeptIdsByDeptIds(
                    controllerContext.getTenantId(),
                    User.systemUser(controllerContext.getTenantId()).getUserId(),
                    departments
                );

                List<String> deptHierarchy = Lists.newArrayList();
                for (String deptId : deptHierarchyMap.get(departments.get(0))) {
                    deptHierarchy.add(deptId);
                }

                context.setDeptApi(ruleFieldName);
                context.setDeptHierarchy(deptHierarchy);

                searchQuery.inDepartmentNotSub(ruleFieldName, deptHierarchy);
            } catch (Exception e) {
                log.error("获取部门层级失败", e);
                searchQuery.notExist(ruleFieldName);
            }
        } else {
            searchQuery.notExist(ruleFieldName);
        }

        return true;
    }

    /**
     * 查询匹配的规则
     */
    private List<IObjectData> queryMatchedRules(SearchQuery.SearchQueryBuilder searchQuery) {
        try {
            log.info("Type0查询条件: {}", JSON.toJSONString(searchQuery.build()));
            return baseDao.getAllIObjectDataListByQuery(
                User.systemUser(controllerContext.getTenantId()),
                searchQuery.build(),
                SuccessfulStoreRangeConstants.SuccessfulStoreRangeObj
            );
        } catch (Exception e) {
            log.error("查询匹配规则失败", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 选择最佳规则（Type0版本）
     */
    private String selectBestRuleForType0(List<IObjectData> matchedRules, MatchContext context) {
        if (matchedRules.size() == 1) {
            return matchedRules.get(0).getId();
        }

        // 多个规则时需要排序选择最佳匹配
        List<IObjectData> sortedRules = sortRulesByPriority(matchedRules, context);
        String bestRuleId = sortedRules.get(0).getId();

        log.info("Type0选择最佳规则: {}, 总候选数: {}", bestRuleId, matchedRules.size());
        return bestRuleId;
    }

    /**
     * 按优先级排序规则
     */
    private List<IObjectData> sortRulesByPriority(List<IObjectData> rules, MatchContext context) {
        List<IObjectData> sortedRules = Lists.newArrayList(rules);

        sortedRules.sort((rule1, rule2) -> {
            // 首先按部门层级排序（层级越低优先级越高）
            int deptComparison = compareDepartmentPriority(rule1, rule2, context);
            if (deptComparison != 0) {
                return deptComparison;
            }

            // 如果部门层级相同，再按渠道层级排序
            return compareChannelPriority(rule1, rule2, context);
        });

        return sortedRules;
    }

    /**
     * 比较部门优先级
     */
    private int compareDepartmentPriority(IObjectData rule1, IObjectData rule2, MatchContext context) {
        if (StringUtils.isEmpty(context.getDeptApi()) || CollectionUtils.isEmpty(context.getDeptHierarchy())) {
            return 0;
        }

        int priority1 = getDepartmentPriority(rule1, context);
        int priority2 = getDepartmentPriority(rule2, context);

        return Integer.compare(priority1, priority2);
    }

    /**
     * 获取部门优先级
     */
    private int getDepartmentPriority(IObjectData rule, MatchContext context) {
        List<String> ruleDepts = rule.get(context.getDeptApi(), ArrayList.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(ruleDepts)) {
            return context.getDeptHierarchy().size(); // 最低优先级
        }

        int index = context.getDeptHierarchy().indexOf(ruleDepts.get(0));
        return index == -1 ? context.getDeptHierarchy().size() : index;
    }

    /**
     * 比较渠道优先级
     */
    private int compareChannelPriority(IObjectData rule1, IObjectData rule2, MatchContext context) {
        if (StringUtils.isEmpty(context.getChannelApi()) || CollectionUtils.isEmpty(context.getChannelHierarchy())) {
            return 0;
        }

        int priority1 = getChannelPriority(rule1, context);
        int priority2 = getChannelPriority(rule2, context);

        return Integer.compare(priority1, priority2);
    }

    /**
     * 获取渠道优先级
     */
    private int getChannelPriority(IObjectData rule, MatchContext context) {
        String ruleChannel = rule.get(context.getChannelApi(), String.class, "");
        if (StringUtils.isEmpty(ruleChannel)) {
            return context.getChannelHierarchy().size(); // 最低优先级
        }

        int index = context.getChannelHierarchy().indexOf(ruleChannel);
        return index == -1 ? context.getChannelHierarchy().size() : index;
    }





    /**
     * Type0匹配上下文封装类
     */
    @Data
    private static class MatchContext {
        private Map<String, CheckSuccess.Config> successConfig;
        private IObjectData storeData;
        private IObjectDescribe objectDescribe;
        private String deptApi = "";
        private String channelApi = "";
        private List<String> deptHierarchy = Lists.newArrayList();
        private List<String> channelHierarchy = Lists.newArrayList();
    }
}
