package com.facishare.crm.fmcg.wq.report.service;

import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.DataReportStandardDao;
import com.facishare.crm.fmcg.wq.report.model.ProjectSettingType;
import com.facishare.crm.fmcg.wq.report.model.DisplayReportDetail;
import com.facishare.crm.fmcg.wq.report.model.ReportType;
import com.facishare.crm.fmcg.wq.report.service.displayStandard.DisplayStandardService;
import com.facishare.crm.fmcg.wq.report.service.displayStandard.DisplayStandardServiceManager;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DisplayReportDetailService {

    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected DataReportStandardDao dataReportStandardDao;
    @Autowired
    private DisplayStandardServiceManager displayStandardServiceManager;

    public List<DisplayReportDetail.DisplayInfo> buildReportData(User systemUser, IObjectData objectData){
        List<DisplayReportDetail.DisplayInfo> displayInfos = Lists.newArrayList();
        // 判断是否为陈列形式
        boolean display = DisplayDistrAchSummaryFields.METHODS_DISPLAY_STANDARDS_Options_2.equals(String.valueOf(objectData.get(DisplayDistrAchSummaryFields.METHODS_DISPLAY_STANDARDS)));

        // 汇总报告Id 查询陈列项目达成对象
        List<IObjectData> displayProjectAllDataList = dataReportStandardDao.getDisplayProjectAchievementByReportId(systemUser, objectData.getId());
        Map<String,String> displayProjectValueAndColorMap = buildDisplayProjectValueAndColorMap(systemUser);
        // 陈列形式+项目标准
        if(display){
            IObjectDescribe describe = serviceFacade.findObject(systemUser.getTenantId(),
                    SummaryDisplayAchievementFields.API_NAME);
//            Map<String,String> valueAndColorMap = ObjectUtils.getValueAndColorByDesc(describe.getFieldDescribeMap().get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS));
            Map<String, ISelectOption> optionDescribes = ObjectUtils.getOptionDescribes(describe.getFieldDescribeMap().get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS));
            // 查询陈列形式达成总结
            List<IObjectData> displayDataList = dataReportStandardDao.getDisShapeSumByDispDistrAchSummaryId(systemUser,objectData.getId(),describe);
            //主数据id
            String businessId = objectData.get(DisplayDistrAchSummaryFields.BUSINESS_DOCUMENTS_ID,String.class);
            String businessApiName = objectData.get(DisplayDistrAchSummaryFields.BUSINESS_DOCUMENTS_APINAME,String.class);
            if (ReportType.ACTIVITY.getRecord().equals(objectData.getRecordType())){
              businessApiName = ActivityProofFields.API_NAME;
              Map<String, List<String>> relatedObjectDataMap = CollectionUtils.nullToEmpty((Map)objectData.get(DisplayDistrAchSummaryFields.RELATED_OBJECT, Map.class));
                List<String> ids = relatedObjectDataMap.get(ActivityProofFields.API_NAME);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(ids)){
                	businessId = ids.get(0);
                }
            }
            //陈列形式id 和 业务子数据id映射
            Map<String,IObjectData> displayIdAndBusinessSubIdMap;
            //活动举证
            if (ActivityProofFields.API_NAME.equals(businessApiName)) {
                //更具 businessId 查询举证图片对象
                displayIdAndBusinessSubIdMap = dataReportStandardDao.getActivityProofDisplayImgIdAndBusinessSubObjMap(systemUser, businessId);
            } else {
                displayIdAndBusinessSubIdMap = Maps.newHashMap();
            }
            String finalBusinessId = businessId;
            displayDataList.forEach(item -> {
                DisplayReportDetail.DisplayInfo displayInfo = buildDisplayInfo(item, optionDescribes, finalBusinessId,displayIdAndBusinessSubIdMap);
                displayInfos.add(displayInfo);
                String displayId = String.valueOf(item.get(DisplayProjectAchievementFields.RELATED_DISPLAY_ACHIEVEMENT));
                // 过滤出当前陈列形式的项目达成数据。
                List<IObjectData> displayProjectDataList = displayProjectAllDataList.stream().filter(o->displayId.equals(String.valueOf(o.get(DisplayProjectAchievementFields.RELATED_DISPLAY_ACHIEVEMENT)))).collect(Collectors.toList());
                displayInfo.setProjectInfos(buildDisplayFormInfo(systemUser,displayProjectDataList,displayProjectValueAndColorMap));
            });
        }else{
            // 项目标准
            DisplayReportDetail.DisplayInfo displayInfo = new DisplayReportDetail.DisplayInfo();
            displayInfos.add(displayInfo);
            displayInfo.setProjectInfos(buildDisplayFormInfo(systemUser,displayProjectAllDataList,displayProjectValueAndColorMap));
        }

        return displayInfos;
    }

    private Map<String,String> buildDisplayProjectValueAndColorMap(User systemUser){
        IObjectDescribe displayProjectDescribe = serviceFacade.findObject(systemUser.getTenantId(),
                DisplayProjectAchievementFields.API_NAME);
        return ObjectUtils.getValueAndColorByDesc(
                displayProjectDescribe.getFieldDescribeMap().get(DisplayProjectAchievementFields.ACHIEVED_RESULTS));
    }

    private DisplayReportDetail.DisplayInfo buildDisplayInfo(IObjectData item, Map<String, ISelectOption> optionDescribeMap, String businessId, Map<String, IObjectData> displayIdAndBusinessSubIdMap) {
        DisplayReportDetail.DisplayInfo displayInfo = new DisplayReportDetail.DisplayInfo();
        // 陈列照片
        displayInfo.setDisplayPhoto((List<Object>) item.get(SummaryDisplayAchievementFields.DISPLAY_PHOTO));
        // 陈列形式
        displayInfo.setName((String)item.get(SummaryDisplayAchievementFields.RELATED_DISPLAY_ACHIEVEMENT_NAME));
        displayInfo.setReachProject(ObjectUtils.buildManySelectValue(item, SummaryDisplayAchievementFields.ACHIEVEMENT_SUMMARY_NAME));
        displayInfo.setUnReachProject(ObjectUtils.buildManySelectValue(item, SummaryDisplayAchievementFields.SUMMARY_ISSUES_NAME));
        ISelectOption iSelectOption = optionDescribeMap.get(String.valueOf(item.get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS)));
        displayInfo.setBusinessDataId(businessId);
        IObjectData iObjectData = displayIdAndBusinessSubIdMap.get(item.get(SummaryDisplayAchievementFields.RELATED_DISPLAY_ACHIEVEMENT, String.class));
        if(Objects.nonNull(iObjectData)){
            displayInfo.setBusinessSubDataId(iObjectData.getId());
            String aiStatus = iObjectData.get(TPMActivityProofDisplayImgFields.AI_IDENTIFY_STATUS, String.class);
            displayInfo.setAiStatus(aiStatus);
            //识别失败
            if(TPMActivityProofDisplayImgFields.AI_IDENTIFY_STATUS_Options_identify_failed.equals(aiStatus)){
                iSelectOption = optionDescribeMap.get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_7);
            }else if (TPMActivityProofDisplayImgFields.AI_IDENTIFY_STATUS_Options_identifying.equals(aiStatus)){
                //识别中
                iSelectOption = optionDescribeMap.get(SummaryDisplayAchievementFields.ACHIEVED_RESULTS_Options_6);
            }
        }
        if (Objects.nonNull(iSelectOption)){
            displayInfo.setReachStatus(iSelectOption.getLabel());
            displayInfo.setReachColor(iSelectOption.get("font_color", String.class));
        }
        return displayInfo;
    }
    private List<DisplayReportDetail.ProjectInfo> buildDisplayFormInfo(User systemUser, List<IObjectData> displayProjectDataList,Map<String,String> displayProjectValueAndColorMap) {
        Map<String, List<IObjectData>> displayProjectDataMap = displayProjectDataList.stream().collect(
                Collectors.groupingBy(
                        item -> String.valueOf(item.get(DisplayProjectAchievementFields.SETTING_TYPE))));

        List<DisplayReportDetail.ProjectInfo> result = Lists.newArrayList();
        for (Map.Entry<String, List<IObjectData>> item : displayProjectDataMap.entrySet()) {
            ProjectSettingType projectSettingType = ProjectSettingType.getByCode(item.getKey());
            if (Objects.isNull(projectSettingType)) {
                log.info("buildProjectInfo enum is null:{}", item.getKey());
                continue;
            }
            DisplayReportDetail.ProjectInfo projectInfo = new DisplayReportDetail.ProjectInfo();
            // TODO 国际化
            projectInfo.setTitle(projectSettingType.i18nKey);
            projectInfo.setCode(projectSettingType.code);
            DisplayStandardService displayStandardService = displayStandardServiceManager.getDisplayStandardService(projectSettingType.serviceKey);
            projectInfo.setProjectDetailInfos(displayStandardService.buildProjectInfo(systemUser,item.getValue(),displayProjectValueAndColorMap));
            result.add(projectInfo);
        }
        // 按照类型排序
        result.sort(Comparator.comparing(DisplayReportDetail.ProjectInfo::getCode));
        return result;
    }
}
