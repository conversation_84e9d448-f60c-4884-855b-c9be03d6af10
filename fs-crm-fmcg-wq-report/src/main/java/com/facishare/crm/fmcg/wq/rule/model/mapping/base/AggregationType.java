package com.facishare.crm.fmcg.wq.rule.model.mapping.base;

/**
 * Aggregation Type
 * <p>
 * Defines types of aggregations for multiple values.
 */
public enum AggregationType {
    /**
     * Sum of all values
     */
    SUM,

    /**
     * Count of values
     */
    COUNT,

    /**
     * Average of values
     */
    AVG,

    /**
     * Maximum value
     */
    MAX,

    /**
     * Minimum value
     */
    MIN,

    /**
     * First value in the collection
     */
    FIRST,

    /**
     * Last value in the collection
     */
    LAST,

    /**
     * Concatenate string values
     */
    CONCAT,

    /**
     * Merge list values
     */
    MERGE_LIST,

    /**
     * Distinct values
     */
    DISTINCT,

    /**
     * Count of distinct values
     */
    DISTINCT_COUNT
} 