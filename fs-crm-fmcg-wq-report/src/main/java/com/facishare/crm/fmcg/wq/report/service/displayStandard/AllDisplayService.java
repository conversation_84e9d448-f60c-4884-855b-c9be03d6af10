package com.facishare.crm.fmcg.wq.report.service.displayStandard;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.report.model.DisplaySimpleInfo;
import com.facishare.crm.fmcg.wq.report.model.ProjectSettingType;
import com.facishare.crm.fmcg.wq.report.model.DisplayReportDetail;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

@Component
public class AllDisplayService extends DisplayStandardService {

    @Override
    public String getDisplayStandardKey() {
        return ProjectSettingType.all.serviceKey;
    }

    @Override
    public List<DisplayReportDetail.ProjectDetailInfo> buildProjectInfo(User systemUser, List<IObjectData> dataList,Map<String,String> displayProjectValueAndColorMap) {
        List<DisplayReportDetail.ProjectDetailInfo> result = Lists.newArrayList();
        // 获取整体陈列标准ID
        Set<String> displayStandardIds = dataList.stream().map(item -> String.valueOf(item.get(DisplayProjectAchievementFields.RULE_GROUP_ID))).collect(Collectors.toSet());

        Map<String,IObjectData> displayItemIdAndDataMap = dataList.stream().collect(Collectors.toMap(item -> String.valueOf(item.get(BaseField.id.getApiName())), item -> item));
        // 查询陈列标准 得到规则描述 Map<陈列标准ID,陈列标准描述>
        Map<String,String> displayStandardMap = buildDisplayStandardMap(systemUser,displayStandardIds);
        // 获取树关系
        List<String> existIds = dataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        Map<String,DisplaySimpleInfo> treeMap = buildDisplaySimpleInfo(dataList,existIds);
        Map<String,List<String>> ruleIdAndDataIds = dataList.stream().filter(o->Objects.isNull(o.get(DisplayProjectAchievementFields.CONDITION_PARENT_ID))
                || !existIds.contains(o.get(DisplayProjectAchievementFields.CONDITION_PARENT_ID,String.class))).collect(
                Collectors.groupingBy(k->k.get(DisplayProjectAchievementFields.RULE_GROUP_ID,String.class),Collectors.mapping(DBRecord::getId,Collectors.toList())));
        for (Map.Entry<String, List<String>> stringListEntry : ruleIdAndDataIds.entrySet()) {
            List<String> parentIds = stringListEntry.getValue();
            if(CollectionUtils.isEmpty(parentIds)){
                continue;
            }
            IObjectData displayItemData = displayItemIdAndDataMap.get(parentIds.get(0));
            // 关联规则组ID
            String productItemStandardId = String.valueOf(displayItemData.get(DisplayProjectAchievementFields.RULE_GROUP_ID));
            DisplayReportDetail.ProjectDetailInfo projectDetailInfo = new DisplayReportDetail.ProjectDetailInfo();
            projectDetailInfo.setProjectBasicsInfos(Lists.newArrayList());
            projectDetailInfo.setRuleDesc(displayStandardMap.get(productItemStandardId));
            for (String parentId : parentIds) {
                displayItemData = displayItemIdAndDataMap.get(parentId);
                projectDetailInfo.getProjectBasicsInfos().add(buildBaseProjectDetailInfo(displayItemData,null,null,displayProjectValueAndColorMap));
                projectDetailInfo.getProjectBasicsInfos().addAll(buildChildProjectDetailInfo(treeMap.get(parentId).getChildInfos(),displayItemIdAndDataMap,displayProjectValueAndColorMap));
            }
            buildProjectDetailInfoHeadInfo(projectDetailInfo);
            result.add(projectDetailInfo);
        }
        return result;
    }


    private List<DisplayReportDetail.ProjectBasicsInfo> buildChildProjectDetailInfo(List<DisplaySimpleInfo> dataList,
                                                                                    Map<String, IObjectData> displayItemIdAndDataMap,Map<String,String> displayProjectValueAndColorMap) {
        List<DisplayReportDetail.ProjectBasicsInfo> result = Lists.newArrayList();
        for (DisplaySimpleInfo displayItem : dataList) {
            if(CollectionUtils.isNotEmpty(displayItem.getChildInfos())){
                buildChildProjectDetailInfo(displayItem.getChildInfos(),displayItemIdAndDataMap,displayProjectValueAndColorMap);
            }
            IObjectData displayItemData = displayItemIdAndDataMap.get(displayItem.getDataId());
            result.add(buildBaseProjectDetailInfo(displayItemData,null,null,displayProjectValueAndColorMap));
        }
        return result;
    }
    private Map<String,String> buildDisplayStandardMap(User systemUser,Set<String> displayStandardIds) {
        SearchQuery searchQuery = SearchQuery.builder().in(BaseField.id.getApiName(),
                displayStandardIds).build();
        List<IObjectData> productStandardList = dataReportStandardDao.getAllQueryDataListByQueryWithFields(systemUser,
                searchQuery, ProjectStandardsFields.API_NAME,
                Lists.newArrayList(ProjectStandardsFields.EXPLANATION_PROJECT)).getData();
        return productStandardList.stream().filter(o->Objects.nonNull(o.get(ProjectStandardsFields.EXPLANATION_PROJECT)))
                .collect(Collectors.toMap(item -> String.valueOf(item.get(BaseField.id.getApiName())),
                        item -> String.valueOf(item.get(ProjectStandardsFields.EXPLANATION_PROJECT))));
    }
    
    
}
