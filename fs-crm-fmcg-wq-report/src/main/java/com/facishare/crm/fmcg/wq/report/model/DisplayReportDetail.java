package com.facishare.crm.fmcg.wq.report.model;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/03/20/ 18:17
 **/
public interface DisplayReportDetail {

    @Data
    class Arg {
        // 需要动作Id
        public String actionId;
        // 外勤ID
        public String checkinsId;
        // 客户ID
        public String customerId;
        // 数据Id
        public String dataId;
        // 获取报告类型 0 陈列 2 举证
        public String reportType;
        // 上报主对象的APiName
        public String reportApiName;
        public int isShow;
        public int isMustShow;
    }

    @Data
    class Result {
        // 陈列信息
        public List<DisplayInfo> displayInfos;
        // 必分销
        public List<DistributionInfo> distributionInfos;
        public int isShow;
        public int isMustShow;
    }

    @Data
    class DistributionInfo {
        // 陈列名称 可以为空
        public String displayName;
        // 产品分类信息
        public List<ProductCategoryInfo> productCategoryInfos;
    }

    @Data
    class ProductCategoryInfo {
        // 分类名称
        public String categoryName;
        // 产品分类ID
        public List<String> categoryIds;
        // 分销规则描述
        public String distributionRuleDesc;
        // 分销信息
        public String distributionMsg;
        // 分销颜色
        public String distributionColor;
        // 颜色是否填充当前行 1 是 0 否
        public int isFillCurrentRow;
        // 产品信息
        public List<ProductInfo> productInfos;
    }

    @Data
    class ProductInfo {
        // 产品ID
        public String productId;
        // 产品名称
        public String productName;
        // 是否分销 0 否 1 是
        public int isDistribution;
    }

    @Data
    class DisplayInfo {
        // 陈列名称
        public String name;
        // 陈列照片
        public List<Object> displayPhoto;
        // 达标状态
        public String reachStatus;
        // 达标颜色
        public String reachColor;
        // 已达标的陈列
        public String reachProject;
        // 未达标的陈列
        public String unReachProject;
        /**
         * 业务数据id
         */
        public String businessDataId;
        /**
         * 业务子数据id
         */
        public String businessSubDataId;
        /**
         * ai识别状态
         */
        public String aiStatus;
        // 项目信息
        public List<ProjectInfo> projectInfos;
    }

    @Data
    class ProjectInfo {
        // title (整理、产品、物料)
        public String title;
        // 类型 1 整体 2 产品 3 物料
        public String code;
        // 项目详细信息
        public List<ProjectDetailInfo> projectDetailInfos;
    }

    @Data
    class ProjectDetailInfo {
        // 规则描述
        public String ruleDesc;
        // 是否展示规则 1 展示 0 不展示
        public int isShowRule;
        // 是否展示 1 展示 0 不展示
        public int isShowLevel;
        // 项目基础信息
        public List<ProjectBasicsInfo> projectBasicsInfos;
    }

    @Data
    class ProjectBasicsInfo {
        // 规则范围 品类名称
        public String categoryName;
        // 规则范围 产品名称
        public String productName;
        // 项目名称
        public String projectName;
        // 层级
        public String level;
        // 标准
        public String standard;
        // 实际
        public String actual;
        // 差值
        public String diff;
        // 达标文案
        public String reachText;
        // 文案颜色
        public String reachColor;
    }

}
