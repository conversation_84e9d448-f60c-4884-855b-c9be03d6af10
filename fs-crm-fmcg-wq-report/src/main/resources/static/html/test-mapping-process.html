<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段映射处理测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1000px;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin-top: 30px;
        }
        .result-area {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">生成上报结果测试</h1>
        
        <form id="testForm">
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="tenantId" class="form-label">企业ID</label>
                    <input type="text" class="form-control" id="tenantId" name="tenantId" required>
                </div>
                <div class="col-md-6">
                    <label for="dataMainApiName" class="form-label">数据主API名称</label>
                    <input type="text" class="form-control" id="dataMainApiName" name="dataMainApiName" required>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="dataMainId" class="form-label">数据主ID</label>
                    <input type="text" class="form-control" id="dataMainId" name="dataMainId" required>
                </div>
                <div class="col-md-6">
                    <label for="standardMainId" class="form-label">标准主ID</label>
                    <input type="text" class="form-control" id="standardMainId" name="standardMainId" required>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="checkinsId" class="form-label">签到ID</label>
                    <input type="text" class="form-control" id="checkinsId" name="checkinsId">
                </div>
                <div class="col-md-6">
                    <label for="businessDataApiName" class="form-label">业务单据API名称(唯一键)</label>
                    <input type="text" class="form-control" id="businessDataApiName" name="businessDataApiName">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="businessDataId" class="form-label">业务单据ID(唯一键)</label>
                    <input type="text" class="form-control" id="businessDataId" name="businessDataId">
                </div>
                <div class="col-md-6">
                    <label for="accountId" class="form-label">客户ID</label>
                    <input type="text" class="form-control" id="accountId" name="accountId">
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-12">
                    <label class="form-label">业务类型</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="businessType" id="displayStandard" value="default__c" checked>
                        <label class="form-check-label" for="displayStandard">
                            陈列报告 (default__c)
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="businessType" id="distributionStandard" value="distribution__c">
                        <label class="form-check-label" for="distributionStandard">
                            铺货报告 (distribution__c)
                        </label>
                    </div>
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="businessType" id="activityStandard" value="activity__c">
                    <label class="form-check-label" for="activityStandard">
                        活动报告 (activity_evidence_report__c)
                    </label>
                  </div>
                </div>
            </div>
            
            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary" id="submitButton">
                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="submitSpinner"></span>
                    处理数据
                </button>
            </div>
        </form>
        
        <div id="resultArea" class="result-area hidden">
            <h3 class="mb-3">处理结果</h3>
            <div class="alert" id="resultAlert"></div>
            <h4>详细信息</h4>
            <pre id="resultJson"></pre>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const testForm = document.getElementById('testForm');
            const submitButton = document.getElementById('submitButton');
            const submitSpinner = document.getElementById('submitSpinner');
            const resultArea = document.getElementById('resultArea');
            const resultAlert = document.getElementById('resultAlert');
            const resultJson = document.getElementById('resultJson');
            
            testForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 显示加载状态
                submitButton.disabled = true;
                submitSpinner.classList.remove('d-none');
                
                // 获取表单数据
                const formData = {
                    tenantId: document.getElementById('tenantId').value,
                    dataMainId: document.getElementById('dataMainId').value,
                    dataMainApiName: document.getElementById('dataMainApiName').value,
                    standardMainId: document.getElementById('standardMainId').value,
                    checkinsId: document.getElementById('checkinsId').value,
                    businessDataApiName: document.getElementById('businessDataApiName').value,
                    businessDataId: document.getElementById('businessDataId').value,
                    accountId: document.getElementById('accountId').value,
                    businessType: document.querySelector('input[name="businessType"]:checked').value
                };
                
                // 发送请求
                fetch('/api/field-mapping/test-process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json;charset=UTF-8'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`请求失败，状态码: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // 显示结果
                    resultArea.classList.remove('hidden');
                    
                    if (data.code === 0) {
                        resultAlert.classList.remove('alert-danger');
                        resultAlert.classList.add('alert-success');
                        resultAlert.textContent = '处理成功！';
                    } else {
                        resultAlert.classList.remove('alert-success');
                        resultAlert.classList.add('alert-danger');
                        resultAlert.textContent = `处理失败: ${data.message || '未知错误'}`;
                    }
                    
                    // 显示详细信息
                    resultJson.textContent = JSON.stringify(data, null, 2);
                    
                    // 滚动到结果区域
                    resultArea.scrollIntoView({ behavior: 'smooth' });
                })
                .catch(error => {
                    resultArea.classList.remove('hidden');
                    resultAlert.classList.remove('alert-success');
                    resultAlert.classList.add('alert-danger');
                    resultAlert.textContent = `请求错误: ${error.message}`;
                    resultJson.textContent = '';
                })
                .finally(() => {
                    // 恢复按钮状态
                    submitButton.disabled = false;
                    submitSpinner.classList.add('d-none');
                });
            });
        });
    </script>
</body>
</html> 