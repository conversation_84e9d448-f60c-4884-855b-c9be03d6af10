<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段映射工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 添加 select2 的 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .input-area {
            margin-bottom: 20px;
        }
        textarea {
            font-family: monospace;
            min-height: 300px;
        }
        .mapping-table {
            margin-top: 30px;
        }
        .mapping-row {
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .field-info {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .result-area {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .hidden {
            display: none;
        }
        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
        }
        .btn-warning:hover {
            background-color: #e0a800;
            border-color: #d39e00;
        }
        /* 淡入淡出动画 */
        .fade-in {
            animation: fadeIn 0.5s;
        }
        .fade-out {
            animation: fadeOut 0.5s;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        /* 提交成功和失败的颜色 */
        .submit-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
            transition: all 0.3s ease;
        }
        .submit-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
            transition: all 0.3s ease;
        }
        /* 现有配置标记样式 */
        .existing-config-badge {
            margin-top: 5px;
            font-size: 0.8rem;
            color: #007bff;
        }
        /* select2 相关样式调整 */
        .select2-container {
            width: 100% !important;
        }
        
        .select2-container--bootstrap-5 .select2-selection {
            min-height: 38px;
        }
        
        .select2-container--bootstrap-5 .select2-selection--single {
            padding: 0.375rem 0.75rem;
        }
        
        .select2-container--bootstrap-5 .select2-search__field {
            padding: 0.375rem 0.75rem;
        }
        
        .select2-container--bootstrap-5 .select2-results__option {
            padding: 0.375rem 0.75rem;
        }
        
        .select2-container--bootstrap-5 .select2-selection__rendered {
            line-height: 1.5;
        }
        
        /* 映射行列样式 */
        .mapping-row .col-4, .mapping-row .col-2 {
            display: block !important;
            padding: 8px;
        }
        
        /* 聚合类型列样式 */
        .aggregation-type-select {
            display: block !important;
            width: 100% !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">字段映射工具</h1>
        
        <div class="row input-area">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="tenantId" class="form-label">企业ID</label>
                    <input type="text" class="form-control" id="tenantId" placeholder="请输入企业ID">
                </div>
                
                <div class="mb-3">
                    <label for="standardObjectApiName" class="form-label">标准对象API名称</label>
                    <select class="form-select" id="standardObjectApiName">
                        <option value="ProductItemStandardDetailObj">产品项目标准(ProductItemStandardDetailObj)</option>
                        <option value="MaterialStandardDetailsObj">物料标准(MaterialStandardDetailsObj)</option>
                        <option value="MustDistributeProductsObj">必铺产品(MustDistributeProductsObj)</option>
                        <option value="ProjectStandardsObj">铺货项目标准(ProjectStandardsObj)</option>
                    </select>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="sourceObjectApiName" class="form-label">源对象API名称</label>
                    <input type="text" class="form-control" id="sourceObjectApiName" placeholder="请输入源对象API名称">
                </div>
            </div>
        </div>
        
        <div class="row input-area">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="objectDescribe" class="form-label">原对象描述 (JSON格式)</label>
                    <p class="form-text text-muted">企业ID和源对象API名称将从此处自动获取</p>
                    <textarea class="form-control" id="objectDescribe" placeholder="请输入原对象描述JSON"></textarea>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="dataList" class="form-label">数据项目列表 (JSON格式)</label>
                    <textarea class="form-control" id="dataList" placeholder="请输入数据项目列表JSON"></textarea>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-12 d-flex justify-content-center gap-3">
                <button id="loadConfigButton" class="btn btn-info">读取现有配置</button>
                <button id="runButton" class="btn btn-primary">运行</button>
            </div>
        </div>
        
        <div id="configDisplayArea" class="mb-4 hidden">
            <h4>现有配置</h4>
            <div class="alert alert-info">
                <p>已加载企业ID <span id="loadedTenantId" class="fw-bold"></span> 的配置</p>
                <p>配置数量: <span id="configCount" class="fw-bold"></span></p>
                <p>配置文件名称: checkins-v2-config </p>
                <p>配置文件key: dataReport2FieldMapping</p>
            </div>
            <pre id="configJson" class="border p-3 bg-light overflow-auto" style="max-height: 200px;"></pre>
        </div>
        
        <div id="mappingArea" class="mapping-table hidden">
            <h3 class="mt-4 mb-3">字段映射</h3>
            <div id="mappingFields"></div>
            
            <div class="row mt-4">
                <div class="col-12 text-center">
                    <button id="generateButton" class="btn btn-success">生成配置</button>
                </div>
            </div>
        </div>
        
        <div id="resultArea" class="result-area hidden">
            <h3 class="mb-3">生成的配置</h3>
            <pre id="resultJson"></pre>
            <div class="text-center mt-3 d-flex justify-content-center gap-2">
                <button id="copyButton" class="btn btn-outline-primary">复制</button>
                <button id="submitButton" class="btn btn-warning">加入并重新生成配置字符串</button>
            </div>
            <div id="submitResultAlert" class="alert mt-3 hidden"></div>
        </div>
    </div>

    <!-- 在 Bootstrap 之后添加 jQuery 和 select2 的 JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/i18n/zh-CN.js"></script>
    
    <script>
        // 全局变量，保存已加载的现有配置
        let globalLoadedConfig = null;
        
        // 全局定义聚合类型
        const aggregationTypes = [
            { value: 'NONE', label: '无' },
            { value: 'SUM', label: '求和' },
            { value: 'AVG', label: '平均值' },
            { value: 'COUNT', label: '计数' },
            { value: 'MAX', label: '最大值' },
            { value: 'MIN', label: '最小值' },
            { value: 'FIRST', label: '第一个' },
            { value: 'LAST', label: '最后一个' },
            { value: 'DISTINCT_COUNT', label: '去重计数' },
            { value: 'GROUP_CONCAT', label: '字符串拼接' },
            { value: 'WEIGHTED_AVG', label: '加权平均' },
            { value: 'MEDIAN', label: '中位数' },
            { value: 'MODE', label: '众数' },
            { value: 'PERCENTILE', label: '百分位数' },
            { value: 'STDDEV', label: '标准差' },
            { value: 'VARIANCE', label: '方差' }
        ];
        
        document.addEventListener('DOMContentLoaded', function() {
            // 示例数据
            const exampleObjectDescribe = document.getElementById('objectDescribe');
            const exampleDataList = document.getElementById('dataList');
            
            // 监听原对象描述变化，自动提取企业ID和源对象API名称
            exampleObjectDescribe.addEventListener('input', function() {
                const objectDescribeText = this.value.trim();
                if (objectDescribeText) {
                    try {
                        const objectDescribe = JSON.parse(objectDescribeText);
                        
                        // 提取企业ID - 多种可能的位置
                        let tenantId = '';
                        if (objectDescribe.fields && objectDescribe.fields.tenant_id && objectDescribe.fields.tenant_id.default_value) {
                            tenantId = objectDescribe.fields.tenant_id.default_value;
                            console.log("从fields.tenant_id.default_value获取到企业ID:", tenantId);
                        } else if (objectDescribe.tenant_id) {
                            tenantId = objectDescribe.tenant_id;
                            console.log("从tenant_id获取到企业ID:", tenantId);
                        }
                        
                        // 提取源对象API名称 - 多种可能的位置
                        let sourceObjectApiName = '';
                        if (objectDescribe.api_name) {
                            sourceObjectApiName = objectDescribe.api_name;
                            console.log("从api_name获取到源对象API名称:", sourceObjectApiName);
                        } else if (objectDescribe.apiName) {
                            sourceObjectApiName = objectDescribe.apiName;
                            console.log("从apiName获取到源对象API名称:", sourceObjectApiName);
                        }
                        
                        // 如果sourceObjectApiName包含$符号并且有下划线，只取下划线之前的部分
                        if (sourceObjectApiName && sourceObjectApiName.includes('$') && sourceObjectApiName.includes('_')) {
                            const parts = sourceObjectApiName.split('_');
                            if (parts.length > 1) {
                                sourceObjectApiName = parts[0];
                                console.log("从分割后获取源对象API名称:", sourceObjectApiName);
                            }
                        }
                        
                        // 立即更新输入框值
                        if (tenantId) {
                            document.getElementById('tenantId').value = tenantId;
                        }
                        if (sourceObjectApiName) {
                            document.getElementById('sourceObjectApiName').value = sourceObjectApiName;
                        }
                        
                    } catch (error) {
                        console.error('JSON解析错误:', error);
                    }
                }
            });
            
            // 运行按钮点击事件
            document.getElementById('runButton').addEventListener('click', function() {
                const standardObjectApiName = document.getElementById('standardObjectApiName').value;
                const objectDescribeText = exampleObjectDescribe.value.trim();
                const dataListText = exampleDataList.value.trim();
                
                if (!standardObjectApiName || !objectDescribeText || !dataListText) {
                    alert('请填写所有必填字段');
                    return;
                }
                
                try {
                    const objectDescribe = JSON.parse(objectDescribeText);
                    const dataList = JSON.parse(dataListText);
                    
                    // 从界面获取已经提取的企业ID和源对象API名称
                    let tenantId = document.getElementById('tenantId').value.trim();
                    let sourceObjectApiName = document.getElementById('sourceObjectApiName').value.trim();
                    
                    // 如果界面上没有值，才尝试从数据中提取
                    if (!tenantId) {
                        // 获取企业ID - 多种可能的位置
                        if (objectDescribe.fields && objectDescribe.fields.tenant_id && objectDescribe.fields.tenant_id.default_value) {
                            tenantId = objectDescribe.fields.tenant_id.default_value;
                            console.log("从fields.tenant_id.default_value获取到企业ID:", tenantId);
                        } else if (objectDescribe.tenant_id) {
                            tenantId = objectDescribe.tenant_id;
                            console.log("从tenant_id获取到企业ID:", tenantId);
                        } else if (dataList && dataList.length > 0) {
                            // 尝试从数据列表中获取
                            if (dataList[0].tenant_id) {
                                tenantId = dataList[0].tenant_id;
                                console.log("从dataList[0].tenant_id获取到企业ID:", tenantId);
                            } else if (dataList[0].tenantId) {
                                tenantId = dataList[0].tenantId;
                                console.log("从dataList[0].tenantId获取到企业ID:", tenantId);
                            }
                        }
                    }
                    
                    // 如果界面上没有值，才尝试从数据中提取
                    if (!sourceObjectApiName) {
                        // 获取源对象API名称 - 多种可能的位置
                        if (objectDescribe.api_name) {
                            sourceObjectApiName = objectDescribe.api_name;
                            console.log("从api_name获取到源对象API名称:", sourceObjectApiName);
                        } else if (objectDescribe.apiName) {
                            sourceObjectApiName = objectDescribe.apiName;
                            console.log("从apiName获取到源对象API名称:", sourceObjectApiName);
                        } else if (dataList && dataList.length > 0 && dataList[0].object_describe_api_name) {
                            sourceObjectApiName = dataList[0].object_describe_api_name;
                            console.log("从dataList[0].object_describe_api_name获取到源对象API名称:", sourceObjectApiName);
                        }
                        
                        // 如果sourceObjectApiName包含$符号并且有下划线，只取下划线之前的部分
                        if (sourceObjectApiName && sourceObjectApiName.includes('$') && sourceObjectApiName.includes('_')) {
                            const parts = sourceObjectApiName.split('_');
                            if (parts.length > 1) {
                                sourceObjectApiName = parts[0];
                                console.log("从分割后获取源对象API名称:", sourceObjectApiName);
                            }
                        }
                    }
                    
                    // 更新输入框值
                    document.getElementById('tenantId').value = tenantId;
                    document.getElementById('sourceObjectApiName').value = sourceObjectApiName;
                    
                    if (!tenantId || !sourceObjectApiName) {
                        alert(`无法获取完整信息:
                        企业ID: ${tenantId || '未找到'}
                        源对象API名称: ${sourceObjectApiName || '未找到'}
                        
                        请手动填写这些值或检查输入数据。`);
                        return;
                    }
                    
                    // 调用处理数据函数，传递所有必要参数
                    processData(tenantId, standardObjectApiName, sourceObjectApiName, objectDescribe, dataList);
                } catch (error) {
                    alert('JSON解析错误: ' + error.message);
                }
            });
            
            // 生成配置按钮点击事件
            document.getElementById('generateButton').addEventListener('click', function() {
                generateConfig();
            });
            
            // 复制按钮点击事件
            document.getElementById('copyButton').addEventListener('click', function() {
                const resultJson = document.getElementById('resultJson');
                
                // 创建一个临时textarea元素
                const textarea = document.createElement('textarea');
                textarea.value = resultJson.textContent;
                document.body.appendChild(textarea);
                
                // 选择并复制文本
                textarea.select();
                document.execCommand('copy');
                
                // 移除临时元素
                document.body.removeChild(textarea);
                
                // 显示复制成功提示
                this.textContent = '已复制!';
                setTimeout(() => {
                    this.textContent = '复制';
                }, 2000);
            });
            
            // 添加提交按钮点击事件
            document.getElementById('submitButton').addEventListener('click', function() {
                submitConfig();
            });
            
            // 添加读取配置按钮事件
            document.getElementById('loadConfigButton').addEventListener('click', function() {
                loadExistingConfig();
            });
        });
        
        // 显示错误信息
        function showError(message) {
            const mappingArea = document.getElementById('mappingArea');
            if (mappingArea) {
                mappingArea.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">错误</h4>
                        <p>${message}</p>
                        <hr>
                        <p class="mb-0">请检查以下内容：</p>
                        <ul>
                            <li>企业ID是否正确</li>
                            <li>标准对象API名称是否正确</li>
                            <li>网络连接是否正常</li>
                        </ul>
                    </div>
                `;
                mappingArea.classList.remove('hidden');
            }
            console.error(message);
        }

        // 处理数据
        function processData(tenantId, standardObjectApiName, sourceObjectApiName, objectDescribe, dataList) {
            console.log('开始处理数据');
            
            if (!tenantId || !standardObjectApiName) {
                console.error('缺少必要参数:', { tenantId, standardObjectApiName });
                showError('请填写租户ID和标准对象API名称');
                return;
            }
            
            // 准备查询数据
            const queryDataMap = prepareQueryDataMap(dataList);
            
            console.log('准备查询数据:', queryDataMap);

            
            // 显示加载提示
            const mappingArea = document.getElementById('mappingArea');
            mappingArea.innerHTML = '<div class="text-center my-5"><div class="spinner-border" role="status"></div><p class="mt-2">正在加载字段映射...</p></div>';
            mappingArea.classList.remove('hidden');
            
            // 获取规则字段 - 使用POST方法
            fetch(`/api/field-mapping/rules/${tenantId}/${standardObjectApiName}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    'Accept': 'application/json;charset=UTF-8'
                },
                body: JSON.stringify(queryDataMap)
            })
                .then(response => {
                    console.log('API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`API请求失败: ${response.status}`);
                    }
                    return response.json();
                })
                .then(ruleFields => {
                    console.log('获取到的规则字段:', ruleFields);
                    
                    if (!ruleFields || !Array.isArray(ruleFields)) {
                        console.error('规则字段数据无效:', ruleFields);
                        
                        // 尝试转换非数组格式的响应
                        if (ruleFields && typeof ruleFields === 'object') {
                            console.warn('尝试将非数组对象转换为数组');
                            ruleFields = [ruleFields];
                        } else {
                            throw new Error('获取规则字段失败：数据格式不正确');
                        }
                    }
                    
                    if (ruleFields.length === 0) {
                        throw new Error('规则字段列表为空');
                    }
                    
                    // 使用 objectDescribe 解析源字段
                    console.log('开始解析源字段，原对象描述:', objectDescribe);
                    const sourceFields = parseSourceFields(objectDescribe);
                    console.log('解析后的源字段:', sourceFields);
                    
                    if (!sourceFields || sourceFields.length === 0) {
                        throw new Error('未获取到有效的源字段数据');
                    }
                    
                    // 获取现有配置 - 使用自定义方法
                    return fetchExistingConfig(tenantId, sourceObjectApiName, standardObjectApiName)
                        .then(existingConfig => {
                            console.log("获取到的现有配置:", existingConfig);
                            
                            // 渲染映射界面
                            console.log('开始调用renderMappingInterface');
                            renderMappingInterface(ruleFields, sourceFields, existingConfig);
                            console.log('renderMappingInterface调用完成');
                            
                            // 显示映射区域
                            document.getElementById('mappingArea').classList.remove('hidden');
                        });
                })
                .catch(error => {
                    console.error('处理数据时发生错误:', error);
                    showError(`处理数据失败: ${error.message}`);
                });
        }
        
        // 获取现有配置
        function fetchExistingConfig(tenantId, sourceObjectApiName, standardObjectApiName) {
            // 如果已经加载了配置，则直接从全局变量中获取
            if (globalLoadedConfig && globalLoadedConfig.tenantConfigs && globalLoadedConfig.tenantConfigs[tenantId]) {
                console.log('从全局变量中获取配置');
                
                const tenantConfigs = globalLoadedConfig.tenantConfigs[tenantId];
                if (Array.isArray(tenantConfigs)) {
                    // 查找匹配的配置
                    for (let i = 0; i < tenantConfigs.length; i++) {
                        const configItem = tenantConfigs[i];
                        if (configItem.sourceObjectApiName === sourceObjectApiName && 
                            configItem.standardObjectApiName === standardObjectApiName) {
                            console.info(`从全局配置中找到匹配的映射配置: ${configItem.configId}`);
                            return Promise.resolve({
                                fieldMappings: configItem.fieldMappings || [],
                                configId: configItem.configId,
                                tenantId: configItem.tenantId,
                                sourceObjectApiName: configItem.sourceObjectApiName,
                                standardObjectApiName: configItem.standardObjectApiName
                            });
                        }
                    }
                }
                
                console.info(`全局配置中未找到匹配的源对象和标准对象的配置`);
                return Promise.resolve({ fieldMappings: [] });
            }
            
            // 否则，从服务器获取配置 - 使用POST方法
            console.log('从服务器获取配置');
            return fetch(`/api/field-mapping/getConfig`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    'Accept': 'application/json;charset=UTF-8'
                },
                body: JSON.stringify({
                    tenantId: tenantId,
                    config: ""  // 根据SaveConfigRequest类添加空配置字段
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`获取现有配置失败: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                try {
                    console.log('从服务器获取的原始配置数据:', data);
                    
                    if (!data || !data.dataReport2FieldMapping) {
                        console.warn('获取配置成功，但未找到dataReport2FieldMapping数据');
                        return { fieldMappings: [] };
                    }
                    
                    // 解析嵌套的JSON字符串，增加容错处理
                    let jsonObject;
                    try {
                        jsonObject = JSON.parse(data.dataReport2FieldMapping);
                    } catch (parseError) {
                        console.warn('标准JSON解析失败，尝试清理和重新解析:', parseError);
                        
                        // 尝试清理数据并重新解析
                        let cleanedJson = data.dataReport2FieldMapping
                            .replace(/\\n/g, '')  // 移除换行符
                            .replace(/\\"/g, '"') // 处理转义的引号
                            .replace(/\\/g, '')   // 移除多余的反斜杠
                            .trim();              // 移除首尾空白
                        
                        // 如果第一个字符不是{，尝试找到第一个{
                        if (!cleanedJson.startsWith('{')) {
                            const firstBrace = cleanedJson.indexOf('{');
                            if (firstBrace !== -1) {
                                cleanedJson = cleanedJson.substring(firstBrace);
                            }
                        }
                        
                        // 如果最后一个字符不是}，尝试找到最后一个}
                        if (!cleanedJson.endsWith('}')) {
                            const lastBrace = cleanedJson.lastIndexOf('}');
                            if (lastBrace !== -1) {
                                cleanedJson = cleanedJson.substring(0, lastBrace + 1);
                            }
                        }
                        
                        try {
                            jsonObject = JSON.parse(cleanedJson);
                        } catch (secondError) {
                            console.error('清理后JSON解析仍然失败:', secondError);
                            // 如果还是失败，返回空配置
                            return { fieldMappings: [] };
                        }
                    }
                    
                    console.log('解析后的配置对象:', jsonObject);
                    
                    // 保存到全局变量
                    globalLoadedConfig = {
                        originalResponse: data,
                        tenantConfigs: jsonObject
                    };
                    
                    if (!jsonObject[tenantId] || !Array.isArray(jsonObject[tenantId]) || jsonObject[tenantId].length === 0) {
                        console.info(`未找到租户${tenantId}的配置数据`);
                        return { fieldMappings: [] };
                    }
                    
                    // 遍历租户配置数组，查找匹配的配置
                    const tenantConfigs = jsonObject[tenantId];
                    console.info(`找到租户${tenantId}的配置 ${tenantConfigs.length} 条`);
                    
                    // 遍历每个配置对象，找到匹配的源对象和标准对象
                    for (let i = 0; i < tenantConfigs.length; i++) {
                        const configItem = tenantConfigs[i];
                        
                        // 检查是否匹配当前的源对象和标准对象
                        if (configItem.sourceObjectApiName === sourceObjectApiName && 
                            configItem.standardObjectApiName === standardObjectApiName) {
                            console.info(`找到匹配的映射配置: ${configItem.configId}`);
                            
                            // 确保fieldMappings是数组
                            const fieldMappings = Array.isArray(configItem.fieldMappings) ? 
                                configItem.fieldMappings : [];
                            
                            // 处理历史数据中可能的格式问题
                            const processedMappings = fieldMappings.map(mapping => {
                                const processed = {
                                    targetField: mapping.targetField || '',
                                    sourceField: mapping.sourceField || ''
                                };
                                // 处理聚合配置的新旧格式
                                if (mapping.aggregationConfig) {
                                    processed.aggregationConfig = mapping.aggregationConfig;
                                } else if (mapping.aggregationType) {
                                    processed.aggregationConfig = {
                                        type: mapping.aggregationType
                                    };
                                }
                                return processed;
                            });
                            
                            // 返回处理后的配置
                            return {
                                fieldMappings: processedMappings,
                                configId: configItem.configId,
                                tenantId: configItem.tenantId,
                                sourceObjectApiName: configItem.sourceObjectApiName,
                                standardObjectApiName: configItem.standardObjectApiName
                            };
                        }
                    }
                    
                    console.info(`未找到匹配的源对象(${sourceObjectApiName})和标准对象(${standardObjectApiName})的配置`);
                    return { fieldMappings: [] };
                } catch (error) {
                    console.error('解析配置数据错误:', error);
                    return { fieldMappings: [] };
                }
            })
            .catch(error => {
                console.warn('获取现有配置失败，将使用空配置:', error);
                return { fieldMappings: [] };
            });
        }
        
        // 解析源对象字段
        function parseSourceFields(objectDescribe) {
            const sourceFields = [];
            
            if (objectDescribe && objectDescribe.fields) {
                // 遍历所有字段
                for (const fieldName in objectDescribe.fields) {
                    const field = objectDescribe.fields[fieldName];
                    
                    if (field) {
                        // 处理字段名称
                        let name = field.name || fieldName;
                        let label = field.label || name;
                        let type = field.type || 'TEXT';
                        
                        // 收集字段信息
                        sourceFields.push({
                            name: name,
                            label: label,
                            type: type,
                            description: field.description || '',
                            originalField: field // 保留原始字段信息，以便需要时使用
                        });
                    }
                }
                
                // 按字段名称排序
                sourceFields.sort((a, b) => a.label.localeCompare(b.label));
            }
            
            console.log('解析的源对象字段:', sourceFields);
            return sourceFields;
        }
        
        // 准备查询数据Map
        function prepareQueryDataMap(dataList) {
            const queryDataMap = {};
            
            if (dataList && dataList.length > 0) {
                // 按对象API名称分组
                dataList.forEach(item => {
                    const apiName = item.object_describe_api_name;
                    if (!apiName) {
                        console.warn('数据项缺少object_describe_api_name:', item);
                        return; // 跳过没有API名称的项
                    }
                    
                    if (!queryDataMap[apiName]) {
                        queryDataMap[apiName] = [];
                    }
                    
                    // 转换为简化的数据结构
                    const simplifiedItem = {
                        id: item._id || item.id,
                        name: item.name
                    };
                    
                    // 添加其他字段
                    Object.keys(item).forEach(key => {
                        if (key !== '_id' && key !== 'id' && key !== 'name') {
                            simplifiedItem[key] = item[key];
                        }
                    });
                    
                    queryDataMap[apiName].push(simplifiedItem);
                });
                
                // 如果没有找到任何有效的分组，添加一个默认分组
                if (Object.keys(queryDataMap).length === 0) {
                    console.warn('未找到有效的object_describe_api_name，将使用默认分组');
                    
                    // 尝试从第一个元素获取API名称
                    const firstItem = dataList[0];
                    let defaultApiName = 'defaultObject';
                    
                    // 如果存在sourceObjectApiName字段，使用它作为默认API名称
                    if (firstItem.sourceObjectApiName) {
                        defaultApiName = firstItem.sourceObjectApiName;
                    } else if (document.getElementById('sourceObjectApiName').value) {
                        defaultApiName = document.getElementById('sourceObjectApiName').value;
                    }
                    
                    queryDataMap[defaultApiName] = dataList.map(item => {
                        const copy = {...item};
                        return copy;
                    });
                }
            }
            
            console.log('构造的queryDataMap:', queryDataMap);
            return queryDataMap;
        }
        
        // 渲染映射界面
        function renderMappingInterface(ruleFields, sourceFields, existingConfig) {
            console.log('开始渲染映射界面');
            const mappingArea = document.getElementById('mappingArea');
            
            if (!mappingArea) {
                console.error('找不到必要的DOM元素: mappingArea');
                return;
            }
            
            // 先重置映射区域内容
            mappingArea.innerHTML = `
                <h3 class="mt-4 mb-3">字段映射</h3>
                <div id="mappingFields"></div>
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <button id="generateButton" class="btn btn-success">生成配置</button>
                    </div>
                </div>
            `;
            
            // 在重置HTML后再获取容器
            const mappingFieldsContainer = document.getElementById('mappingFields');
            
            if (!mappingFieldsContainer) {
                console.error('找不到必要的DOM元素: mappingFieldsContainer');
                return;
            }
            
            if (!ruleFields || ruleFields.length === 0) {
                console.warn('没有规则字段数据');
                mappingFieldsContainer.innerHTML = `
                    <div class="alert alert-warning" role="alert">
                        没有找到可映射的字段。请检查标准对象API名称是否正确。
                    </div>
                `;
                return;
            }
            
            // 获取现有映射关系的映射
            const existingMappings = {};
            if (existingConfig && existingConfig.fieldMappings && existingConfig.fieldMappings.length > 0) {
                existingConfig.fieldMappings.forEach(mapping => {
                    existingMappings[mapping.targetField] = mapping.sourceField;
                });

                // 显示已经加载的现有配置信息
                const configInfo = document.createElement('div');
                configInfo.className = 'alert alert-info mb-3';
                configInfo.innerHTML = `
                    <h5>已加载现有配置（仅用于填充值，不改变字段结构）</h5>
                    <p>配置ID: ${existingConfig.configId || '未知'}</p>
                    <p>租户ID: ${existingConfig.tenantId || '未知'}</p>
                    <p>源对象: ${existingConfig.sourceObjectApiName || '未知'}</p>
                    <p>标准对象: ${existingConfig.standardObjectApiName || '未知'}</p>
                    <p>字段映射数量: ${existingConfig.fieldMappings.length}</p>
                    <p class="text-danger"><strong>注意:</strong> 最终生成的配置以当前界面上的字段为准，现有配置仅用于预填充值</p>
                `;
                mappingFieldsContainer.appendChild(configInfo);
            }
            
            // 创建字段映射表头
            const header = document.createElement('div');
            header.className = 'row fw-bold mb-2 pb-2 border-bottom';
            header.innerHTML = `
                <div class="col-4">目标字段</div>
                <div class="col-4">源字段/表达式</div>
                <div class="col-2">聚合类型</div>
                <div class="col-2">操作</div>
            `;
            mappingFieldsContainer.appendChild(header);
            
            // 为每个规则字段创建映射行
            ruleFields.forEach((ruleField, index) => {
                console.log(`创建映射行 ${index + 1}/${ruleFields.length}:`, ruleField);
                const row = document.createElement('div');
                row.className = 'mapping-row row mb-3';  // 添加row类使布局正确
                row.dataset.targetField = ruleField.targetField;
                
                // 检查是否存在现有映射
                const existingMapping = existingMappings[ruleField.targetField];
                const hasExistingMapping = !!existingMapping;
                
                // 如果存在映射，添加高亮样式
                if (hasExistingMapping) {
                    row.classList.add('border-primary');
                }
                
                // 目标字段信息
                const fieldInfoCol = document.createElement('div');
                fieldInfoCol.className = 'col-4';
                
                // 获取显示名称，优先使用targetFieldName
                const displayName = ruleField.targetFieldName || ruleField.targetField;
                
                // 获取当前字段的完整映射配置
                const currentMapping = existingConfig && existingConfig.fieldMappings ? 
                    existingConfig.fieldMappings.find(m => m.targetField === ruleField.targetField) : null;
                
                fieldInfoCol.innerHTML = `
                    <div class="field-info">
                        <strong>${displayName}</strong>
                        ${ruleField.targetFieldName && ruleField.targetField !== ruleField.targetFieldName ? 
                            `<div class="small text-muted">字段: ${ruleField.targetField}</div>` : ''}
                        <div class="small text-muted">
                            类型: <span class="badge bg-secondary">${ruleField.sourceFieldType || 'TEXT'}</span>
                            操作符: <span class="badge bg-info">${ruleField.operator || '='}</span>
                            ${currentMapping && currentMapping.sourceLookUpApiName ? 
                                `<br>关联对象: <span class="badge bg-warning">${currentMapping.sourceLookUpApiName}</span>` : ''}
                        </div>
                    </div>
                `;
                
                // 源字段选择列
                const selectCol = document.createElement('div');
                selectCol.className = 'col-4';
                
                // 创建源字段选择下拉框
                const select = document.createElement('select');
                select.className = 'form-select source-field-select';
                select.id = `sourceField_${index}`;
                select.setAttribute('data-placeholder', '搜索或选择字段');
                
                // 添加空选项
                const emptyOption = document.createElement('option');
                emptyOption.value = '';
                emptyOption.textContent = '';  // 清空文本，让placeholder生效
                select.appendChild(emptyOption);
                
                // 添加源字段选项
                let hasMatchingSourceField = false;
                sourceFields.forEach(sourceField => {
                    const option = document.createElement('option');
                    option.value = sourceField.name;
                    option.setAttribute('data-search', `${sourceField.label} ${sourceField.name} ${sourceField.type} ${sourceField.description}`);
                    option.textContent = `${sourceField.label} (${sourceField.name})`;
                    
                    if (sourceField.originalField && sourceField.originalField.type === 'object_reference') {
                        option.dataset.isLookup = 'true';
                        option.dataset.lookupApiName = sourceField.originalField.target_api_name || '';
                        option.textContent += ` [关联: ${sourceField.originalField.target_api_name}]`;
                    }
                    
                    if (hasExistingMapping && existingMapping === sourceField.name) {
                        option.selected = true;
                        hasMatchingSourceField = true;
                    }
                    
                    select.appendChild(option);
                });
                
                // 添加表达式选项
                const expressionOption = document.createElement('option');
                expressionOption.value = 'expression';
                expressionOption.textContent = '使用表达式';
                
                if (hasExistingMapping && !hasMatchingSourceField) {
                    let isExpression = true;
                    const simpleFieldPattern = /^[a-zA-Z0-9_]+$/;
                    if (simpleFieldPattern.test(existingMapping)) {
                        const matchingField = sourceFields.find(f => f.name === existingMapping);
                        if (matchingField) {
                            isExpression = false;
                            const optionIndex = Array.from(select.options).findIndex(o => o.value === existingMapping);
                            if (optionIndex > -1) {
                                select.selectedIndex = optionIndex;
                                hasMatchingSourceField = true;
                            }
                        }
                    }
                    
                    if (isExpression) {
                        expressionOption.selected = true;
                    }
                }
                
                select.appendChild(expressionOption);
                selectCol.appendChild(select);
                
                // 初始化 select2
                $(select).select2({
                    theme: 'bootstrap-5',
                    language: 'zh-CN',
                    width: '100%',
                    placeholder: '搜索或选择字段',
                    allowClear: true,
                    escapeMarkup: function(markup) {
                        return markup;
                    }
                });
                
                // 监听 select2 的变化
                $(select).on('change', function() {
                    const sourceValue = $(this).val();
                    const expressionGroup = $(this).closest('.col-4').find('.expression-group');
                    
                    if (sourceValue === 'expression') {
                        expressionGroup.removeClass('hidden');
                    } else {
                        expressionGroup.addClass('hidden');
                    }
                });
                
                // 创建表达式输入框
                const expressionGroup = document.createElement('div');
                expressionGroup.className = 'mt-2 expression-group';
                expressionGroup.classList.add(hasExistingMapping && !hasMatchingSourceField ? '' : 'hidden');
                
                const expressionInput = document.createElement('input');
                expressionInput.type = 'text';
                expressionInput.className = 'form-control expression-input';
                expressionInput.placeholder = '例如: #data.get(\'fieldName\') * 100';
                
                if (hasExistingMapping && !hasMatchingSourceField) {
                    expressionInput.value = existingMapping;
                }
                
                expressionGroup.appendChild(expressionInput);
                selectCol.appendChild(expressionGroup);
                
                // 添加聚合类型选择列
                const aggregationCol = document.createElement('div');
                aggregationCol.className = 'col-2';
                
                // 创建聚合类型选择下拉框
                const aggregationSelect = document.createElement('select');
                aggregationSelect.className = 'form-select aggregation-type-select';
                aggregationSelect.id = `aggregationType_${index}`;
                
                // 添加聚合类型选项
                aggregationTypes.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type.value;
                    option.textContent = `${type.label} (${type.value})`;
                    
                    if (currentMapping && currentMapping.aggregationConfig && 
                        currentMapping.aggregationConfig.type === type.value) {
                        option.selected = true;
                    }
                    aggregationSelect.appendChild(option);
                });
                
                // 添加聚合选择器到列
                aggregationCol.appendChild(aggregationSelect);
                
                // 初始化聚合类型的select2
                $(aggregationSelect).select2({
                    theme: 'bootstrap-5',
                    language: 'zh-CN',
                    width: '100%',
                    minimumResultsForSearch: 5,
                    placeholder: '选择聚合类型'
                });
                
                // 操作列
                const actionCol = document.createElement('div');
                actionCol.className = 'col-2';
                
                if (hasExistingMapping) {
                    const badge = document.createElement('span');
                    badge.className = 'badge bg-primary existing-config-badge';
                    badge.textContent = '已配置';
                    badge.title = '此字段已从现有配置加载';
                    actionCol.appendChild(badge);
                }
                
                const clearButton = document.createElement('button');
                clearButton.className = 'btn btn-sm btn-outline-secondary ms-2';
                clearButton.innerHTML = '<i class="bi bi-x"></i>';
                clearButton.title = '清除此映射';
                clearButton.addEventListener('click', function() {
                    // 清除select2选择
                    $(select).val(null).trigger('change');
                    // 清除表达式输入
                    expressionInput.value = '';
                    expressionGroup.classList.add('hidden');
                });
                actionCol.appendChild(clearButton);
                
                // 将所有列直接添加到行
                row.appendChild(fieldInfoCol);
                row.appendChild(selectCol);
                row.appendChild(aggregationCol);
                row.appendChild(actionCol);
                
                // 将行添加到容器
                mappingFieldsContainer.appendChild(row);
                
                // 添加调试信息
                console.log(`映射行 ${index + 1} 创建完成:`, {
                    targetField: ruleField.targetField,
                    hasSelectCol: !!row.querySelector('.source-field-select'),
                    hasAggregationCol: !!row.querySelector('.aggregation-type-select'),
                    columnsCount: row.children.length
                });
            });
            
            console.log('映射界面渲染完成');
            
            // 重新绑定生成按钮事件
            const generateButton = document.getElementById('generateButton');
            if (generateButton) {
                generateButton.addEventListener('click', generateConfig);
                console.log('生成按钮事件已绑定');
            } else {
                console.error('未找到生成按钮元素');
            }
        }
        
        // 生成配置
        function generateConfig() {
            const tenantId = document.getElementById('tenantId').value.trim();
            const sourceObjectApiName = document.getElementById('sourceObjectApiName').value.trim();
            const standardObjectApiName = document.getElementById('standardObjectApiName').value.trim();
            
            if (!tenantId || !sourceObjectApiName || !standardObjectApiName) {
                alert('缺少必要的字段信息');
                return;
            }
            
            // 收集所有映射字段
            const mappingRows = document.querySelectorAll('.mapping-row');
            const fieldMappings = [];
            
            mappingRows.forEach(row => {
                const targetField = row.dataset.targetField;
                const select = row.querySelector('.source-field-select');
                const aggregationSelect = row.querySelector('.aggregation-type-select');
                
                if (select.value) {
                    let sourceField = select.value;
                    let mapping = {
                        targetField: targetField,
                        sourceField: sourceField
                    };
                    
                    if (sourceField === 'expression') {
                        sourceField = row.querySelector('.expression-input').value.trim();
                        if (!sourceField) {
                            return; // 跳过空表达式
                        }
                        mapping.sourceField = sourceField;
                    } else {
                        // 检查选中的选项是否是查找关联类型
                        const selectedOption = select.options[select.selectedIndex];
                        if (selectedOption.dataset.isLookup === 'true' && selectedOption.dataset.lookupApiName) {
                            mapping.sourceLookUpApiName = selectedOption.dataset.lookupApiName;
                        }
                    }
                    
                    // 添加聚合配置
                    const aggregationType = aggregationSelect.value;
                    if (aggregationType && aggregationType !== 'NONE') {
                        mapping.aggregationConfig = {
                            type: aggregationType
                        };
                    }
                    
                    fieldMappings.push(mapping);
                }
            });
            
            // 创建TenantFieldCustomConfig对象，注意configId格式
            const config = {
                configId: `${tenantId}_${sourceObjectApiName}_${standardObjectApiName}`,
                tenantId: tenantId,
                sourceObjectApiName: sourceObjectApiName,
                standardObjectApiName: standardObjectApiName,
                fieldMappings: fieldMappings
            };
            
            // 显示结果
            document.getElementById('resultJson').textContent = JSON.stringify(config, null, 2);
            document.getElementById('resultArea').classList.remove('hidden');
            
            // 自动滚动到结果区域
            document.getElementById('resultArea').scrollIntoView({ behavior: 'smooth' });
        }
        
        // 提交配置到服务器
        function submitConfig() {
            const tenantId = document.getElementById('tenantId').value.trim();
            const sourceObjectApiName = document.getElementById('sourceObjectApiName').value.trim();
            const standardObjectApiName = document.getElementById('standardObjectApiName').value.trim();
            const configJson = document.getElementById('resultJson').textContent;
            
            if (!tenantId || !sourceObjectApiName || !standardObjectApiName || !configJson) {
                showSubmitResult(false, '无法生成合并配置：缺少必要的数据');
                return;
            }
            
            // 显示提交中状态
            const submitButton = document.getElementById('submitButton');
            const originalText = submitButton.textContent;
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';
            
            try {
                // 解析当前配置
                const currentConfig = JSON.parse(configJson);
                
                // 验证当前配置是否是有效的映射配置对象
                if (!currentConfig.configId || !currentConfig.tenantId || 
                    !currentConfig.sourceObjectApiName || !currentConfig.standardObjectApiName || 
                    !Array.isArray(currentConfig.fieldMappings)) {
                    throw new Error('无效的配置对象格式');
                }
                
                // 如果没有加载过配置，创建初始结构
                if (!globalLoadedConfig || !globalLoadedConfig.tenantConfigs) {
                    globalLoadedConfig = { tenantConfigs: {} };
                }
                
                // 确保租户配置是数组
                if (!globalLoadedConfig.tenantConfigs[tenantId]) {
                    globalLoadedConfig.tenantConfigs[tenantId] = [];
                }
                
                // 检查是否已存在相同configId的配置，执行去重和合并
                const tenantConfigs = globalLoadedConfig.tenantConfigs[tenantId];
                const configId = currentConfig.configId;
                
                let existingIndex = -1;
                for (let i = 0; i < tenantConfigs.length; i++) {
                    if (tenantConfigs[i].configId === configId) {
                        existingIndex = i;
                        break;
                    }
                }
                
                if (existingIndex >= 0) {
                    // 更新现有的配置
                    console.log(`更新已存在的配置 ${configId}`);
                    tenantConfigs[existingIndex] = currentConfig;
                } else {
                    // 添加新配置
                    console.log(`添加新配置 ${configId}`);
                    tenantConfigs.push(currentConfig);
                }
                
                // 更新全局变量
                globalLoadedConfig.tenantConfigs[tenantId] = tenantConfigs;
                
                // 更新配置显示
                showLoadedConfig(tenantId, globalLoadedConfig);
                
                // 生成最终配置字符串（只包含tenantConfigs部分）
                const dataReport2FieldMappingValue = JSON.stringify(globalLoadedConfig.tenantConfigs);
                
                // 显示结果
                document.getElementById('resultJson').textContent = dataReport2FieldMappingValue;
                showSubmitResult(true, "已更新配置并生成合并后的配置字符串值。请复制此字符串用于提交。");
                
                // 更新配置显示区域
                const configJsonPre = document.getElementById('configJson');
                if (configJsonPre) {
                    configJsonPre.textContent = JSON.stringify(globalLoadedConfig.tenantConfigs, null, 2);
                }
                
            } catch (error) {
                console.error('合并配置错误:', error);
                showSubmitResult(false, `合并配置失败: ${error.message}`);
            } finally {
                // 恢复按钮状态
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            }
        }
        
        // 显示提交结果
        function showSubmitResult(success, message) {
            const alertDiv = document.getElementById('submitResultAlert');
            alertDiv.classList.remove('hidden', 'alert-success', 'alert-danger', 'fade-out');
            alertDiv.classList.add(success ? 'alert-success' : 'alert-danger', 'fade-in');
            alertDiv.textContent = message;
            
            // 点击配置后自动滚动到结果
            alertDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // 5秒后自动隐藏提示
            setTimeout(() => {
                alertDiv.classList.add('fade-out');
                setTimeout(() => {
                    alertDiv.classList.add('hidden');
                }, 500); // 等待动画完成
            }, 5000);
        }
        
        // 加载现有配置
        function loadExistingConfig() {
            const tenantId = document.getElementById('tenantId').value.trim();
            
            if (!tenantId) {
                alert('请先填写企业ID');
                return;
            }
            
            // 显示加载状态
            const loadButton = document.getElementById('loadConfigButton');
            const originalText = loadButton.textContent;
            loadButton.disabled = true;
            loadButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 加载中...';
            
            // 使用 POST 方法获取配置
            fetch(`/api/field-mapping/getConfig`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    'Accept': 'application/json;charset=UTF-8'
                },
                body: JSON.stringify({ 
                    tenantId: tenantId,
                    config: "" // 根据 SaveConfigRequest 类添加可选的 config 字段
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`获取配置失败: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                try {
                    console.log('读取到的配置数据:', data);
                    
                    if (!data || !data.dataReport2FieldMapping) {
                        alert('未找到配置数据，将创建新配置');
                        // 空配置
                        globalLoadedConfig = { tenantConfigs: {} };
                        globalLoadedConfig.tenantConfigs[tenantId] = [];
                        
                        // 显示空配置
                        showLoadedConfig(tenantId, globalLoadedConfig);
                        return;
                    }
                    
                    // 解析配置
                    let jsonObject;
                    try {
                        jsonObject = JSON.parse(data.dataReport2FieldMapping);
                    } catch (parseError) {
                        console.warn('标准JSON解析失败，尝试清理和重新解析:', parseError);
                        
                        // 尝试清理数据并重新解析
                        let cleanedJson = data.dataReport2FieldMapping
                            .replace(/\\n/g, '')
                            .replace(/\\"/g, '"')
                            .replace(/\\/g, '')
                            .trim();
                        
                        // 如果第一个字符不是{，尝试找到第一个{
                        if (!cleanedJson.startsWith('{')) {
                            const firstBrace = cleanedJson.indexOf('{');
                            if (firstBrace !== -1) {
                                cleanedJson = cleanedJson.substring(firstBrace);
                            }
                        }
                        
                        // 如果最后一个字符不是}，尝试找到最后一个}
                        if (!cleanedJson.endsWith('}')) {
                            const lastBrace = cleanedJson.lastIndexOf('}');
                            if (lastBrace !== -1) {
                                cleanedJson = cleanedJson.substring(0, lastBrace + 1);
                            }
                        }
                        
                        try {
                            jsonObject = JSON.parse(cleanedJson);
                        } catch (secondError) {
                            console.error('清理后JSON解析仍然失败:', secondError);
                            // 如果还是失败，使用空对象
                            jsonObject = {};
                        }
                    }
                    
                    console.log('解析后的配置对象:', jsonObject);
                    
                    // 保存到全局变量
                    globalLoadedConfig = {
                        originalResponse: data,
                        tenantConfigs: jsonObject
                    };
                    
                    // 显示配置信息
                    showLoadedConfig(tenantId, globalLoadedConfig);
                    
                } catch (error) {
                    console.error('解析配置数据错误:', error);
                    alert(`解析配置时出错: ${error.message}`);
                }
            })
            .catch(error => {
                console.error('获取配置失败:', error);
                alert(`获取配置失败: ${error.message}`);
            })
            .finally(() => {
                // 恢复按钮状态
                loadButton.disabled = false;
                loadButton.textContent = originalText;
            });
        }
        
        // 显示已加载的配置
        function showLoadedConfig(tenantId, loadedConfig) {
            const configArea = document.getElementById('configDisplayArea');
            const tenantIdSpan = document.getElementById('loadedTenantId');
            const configCountSpan = document.getElementById('configCount');
            const configJsonPre = document.getElementById('configJson');
            
            // 设置信息
            tenantIdSpan.textContent = tenantId;
            
            const tenantConfigs = loadedConfig.tenantConfigs[tenantId] || [];
            configCountSpan.textContent = tenantConfigs.length;
            
            // 显示配置内容（只显示值部分，不包含wrapper对象）
            configJsonPre.textContent = JSON.stringify(loadedConfig.tenantConfigs, null, 2);
            
            // 显示配置区域
            configArea.classList.remove('hidden');
        }

        // 通用错误处理函数
        function handleApiError(error, element, message = '操作失败') {
            console.error(error);
            
            const errorMessage = error instanceof Error ? error.message : '未知错误';
            const displayMessage = `${message}: ${errorMessage}`;
            
            if (element) {
                element.innerHTML = `
                    <div class="alert alert-danger my-3" role="alert">
                        <h4 class="alert-heading">错误</h4>
                        <p>${displayMessage}</p>
                        <hr>
                        <p class="mb-0">请检查以下内容：</p>
                        <ul>
                            <li>API请求是否正确</li>
                            <li>网络连接是否正常</li>
                            <li>控制台是否有更多错误信息</li>
                        </ul>
                    </div>
                `;
                element.classList.remove('hidden');
            }
            
            return displayMessage;
        }
    </script>
</body>
</html> 