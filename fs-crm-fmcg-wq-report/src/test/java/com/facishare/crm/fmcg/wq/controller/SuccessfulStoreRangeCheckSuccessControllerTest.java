package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SuccessfulStoreRangeCheckSuccessController 测试类
 * 主要测试 getChannelList 方法的各种重载版本
 */
@ExtendWith(MockitoExtension.class)
class SuccessfulStoreRangeCheckSuccessControllerTest {

  @Mock
  private RedisUtils redisUtils;

  @Mock
  private ServiceFacade serviceFacade;

  @Mock
  private ControllerContext controllerContext;

  @Mock
  private IObjectDescribe objectDescribe;

  @InjectMocks
  private SuccessfulStoreRangeCheckSuccessController controller;

  private Method getChannelListSingleMethod;
  private Method getChannelListMultipleMethod;
  private Method getChannelListVarArgsMethod;
  private Method getChannelDataMethod;

  @BeforeEach
  void setUp() throws Exception {
    // 使用反射获取私有方法
    getChannelListSingleMethod = SuccessfulStoreRangeCheckSuccessController.class
        .getDeclaredMethod("getChannelList", String.class);
    getChannelListSingleMethod.setAccessible(true);

    getChannelListMultipleMethod = SuccessfulStoreRangeCheckSuccessController.class
        .getDeclaredMethod("getChannelList", List.class);
    getChannelListMultipleMethod.setAccessible(true);

    getChannelListVarArgsMethod = SuccessfulStoreRangeCheckSuccessController.class
        .getDeclaredMethod("getChannelList", String[].class);
    getChannelListVarArgsMethod.setAccessible(true);

    getChannelDataMethod = SuccessfulStoreRangeCheckSuccessController.class
        .getDeclaredMethod("getChannelData");
    getChannelDataMethod.setAccessible(true);

    // 设置基础 mock
    when(controllerContext.getTenantId()).thenReturn("test-tenant");
  }

  @Test
  void testGetChannelListSingle_WithValidId() throws Exception {
    // 准备测试数据
    String channelId = "channel1";
    JSONObject channelData = new JSONObject();
    channelData.put("channel1", "channel2");
    channelData.put("channel2", "channel3");
    channelData.put("channel3", "");

    // Mock Redis 返回数据
    when(redisUtils.getChannelData("test-tenant")).thenReturn(channelData.toString());

    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) getChannelListSingleMethod.invoke(controller, channelId);

    // 验证结果
    assertNotNull(result);
    assertEquals(3, result.size());
    assertEquals("channel1", result.get(0));
    assertEquals("channel2", result.get(1));
    assertEquals("channel3", result.get(2));
  }

  @Test
  void testGetChannelListSingle_WithEmptyId() throws Exception {
    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) getChannelListSingleMethod.invoke(controller, "");

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void testGetChannelListMultiple_WithValidIds() throws Exception {
    // 准备测试数据
    List<String> channelIds = Arrays.asList("channel1", "channel4");
    JSONObject channelData = new JSONObject();
    channelData.put("channel1", "channel2");
    channelData.put("channel2", "channel3");
    channelData.put("channel3", "");
    channelData.put("channel4", "channel5");
    channelData.put("channel5", "");

    // Mock Redis 返回数据
    when(redisUtils.getChannelData("test-tenant")).thenReturn(channelData.toString());

    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) getChannelListMultipleMethod.invoke(controller, channelIds);

    // 验证结果
    assertNotNull(result);
    assertEquals(5, result.size());
    assertTrue(result.contains("channel1"));
    assertTrue(result.contains("channel2"));
    assertTrue(result.contains("channel3"));
    assertTrue(result.contains("channel4"));
    assertTrue(result.contains("channel5"));
  }

  @Test
  void testGetChannelListMultiple_WithEmptyList() throws Exception {
    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) getChannelListMultipleMethod.invoke(controller, Lists.newArrayList());

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void testGetChannelListVarArgs_WithValidIds() throws Exception {
    // 准备测试数据
    String[] channelIds = {"channel1", "channel4"};
    JSONObject channelData = new JSONObject();
    channelData.put("channel1", "channel2");
    channelData.put("channel2", "");
    channelData.put("channel4", "channel5");
    channelData.put("channel5", "");

    // Mock Redis 返回数据
    when(redisUtils.getChannelData("test-tenant")).thenReturn(channelData.toString());

    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) getChannelListVarArgsMethod.invoke(controller, (Object) channelIds);

    // 验证结果
    assertNotNull(result);
    assertEquals(4, result.size());
    assertTrue(result.contains("channel1"));
    assertTrue(result.contains("channel2"));
    assertTrue(result.contains("channel4"));
    assertTrue(result.contains("channel5"));
  }

  @Test
  void testGetChannelListVarArgs_WithEmptyArray() throws Exception {
    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) getChannelListVarArgsMethod.invoke(controller, (Object) new String[]{});

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void testGetChannelListMultiple_WithDuplicateChannels() throws Exception {
    // 准备测试数据 - 包含重复的渠道ID
    List<String> channelIds = Arrays.asList("channel1", "channel2", "channel1");
    JSONObject channelData = new JSONObject();
    channelData.put("channel1", "channel3");
    channelData.put("channel2", "channel3");
    channelData.put("channel3", "");

    // Mock Redis 返回数据
    when(redisUtils.getChannelData("test-tenant")).thenReturn(channelData.toString());

    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) getChannelListMultipleMethod.invoke(controller, channelIds);

    // 验证结果 - 应该去重
    assertNotNull(result);
    assertEquals(3, result.size());
    assertTrue(result.contains("channel1"));
    assertTrue(result.contains("channel2"));
    assertTrue(result.contains("channel3"));
  }
}
