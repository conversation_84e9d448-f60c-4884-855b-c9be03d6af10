package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.api.success.AllDataList;
import com.facishare.crm.fmcg.wq.constants.SuccessfulStoreRangeConstants;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SuccessfulStoreRangeAllDataListController 测试类
 * 主要测试产品聚合逻辑的修改
 */
@DisplayName("成功门店范围全数据列表Controller测试")
@ExtendWith(MockitoExtension.class)
class SuccessfulStoreRangeAllDataListControllerTest {

  @Mock
  private ServiceFacade serviceFacade;

  @Mock
  private ControllerContext controllerContext;

  @Mock
  private IObjectDescribe mustProDesc;

  @Mock
  private IObjectData mustProData1;

  @Mock
  private IObjectData mustProData2;

  @InjectMocks
  private SuccessfulStoreRangeAllDataListController controller;

  private Method buildAggregatedDataMethod;

  @BeforeEach
  void setUp() throws Exception {
    // 使用反射获取私有方法
    buildAggregatedDataMethod = SuccessfulStoreRangeAllDataListController.class
        .getDeclaredMethod("buildAggregatedData", List.class);
    buildAggregatedDataMethod.setAccessible(true);

    // 设置控制器上下文
    when(controllerContext.getTenantId()).thenReturn("test-tenant");
    when(controllerContext.getUser()).thenReturn(User.systemUser("test-tenant"));

    // 使用反射设置私有字段
    try {
      java.lang.reflect.Field field = SuccessfulStoreRangeAllDataListController.class.getDeclaredField("controllerContext");
      field.setAccessible(true);
      field.set(controller, controllerContext);

      field = SuccessfulStoreRangeAllDataListController.class.getDeclaredField("serviceFacade");
      field.setAccessible(true);
      field.set(controller, serviceFacade);
    } catch (Exception e) {
      fail("Failed to set controller fields: " + e.getMessage());
    }
  }

  @Test
  @DisplayName("测试聚合数据方法 - 多选产品为List类型")
  void testBuildAggregatedData_WithMultipleProductAsList() throws Exception {
    // 准备测试数据
    List<IObjectData> mustProList = Arrays.asList(mustProData1);

    // Mock 多选产品为List类型
    List<String> productList = Arrays.asList("product1", "product2", "product3");
    when(mustProData1.get(SuccessfulStoreRangeConstants.PRODUCT_MULTIPLE)).thenReturn(productList);
    when(mustProData1.get(SuccessfulStoreRangeConstants.PRODUCT)).thenReturn("single_product");

    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) buildAggregatedDataMethod.invoke(controller, mustProList);

    // 验证结果
    assertNotNull(result);
    assertEquals(4, result.size()); // 3个多选产品 + 1个单选产品
    assertTrue(result.contains("product1"));
    assertTrue(result.contains("product2"));
    assertTrue(result.contains("product3"));
    assertTrue(result.contains("single_product"));
  }

  @Test
  @DisplayName("测试聚合数据方法 - 多选产品为字符串类型")
  void testBuildAggregatedData_WithMultipleProductAsString() throws Exception {
    // 准备测试数据
    List<IObjectData> mustProList = Arrays.asList(mustProData1);

    // Mock 多选产品为字符串类型（逗号分隔）
    when(mustProData1.get(SuccessfulStoreRangeConstants.PRODUCT_MULTIPLE)).thenReturn("product1,product2,product3");
    when(mustProData1.get(SuccessfulStoreRangeConstants.PRODUCT)).thenReturn("single_product");

    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) buildAggregatedDataMethod.invoke(controller, mustProList);

    // 验证结果
    assertNotNull(result);
    assertEquals(4, result.size()); // 3个多选产品 + 1个单选产品
    assertTrue(result.contains("product1"));
    assertTrue(result.contains("product2"));
    assertTrue(result.contains("product3"));
    assertTrue(result.contains("single_product"));
  }

  @Test
  @DisplayName("测试聚合数据方法 - 只有单选产品")
  void testBuildAggregatedData_WithSingleProductOnly() throws Exception {
    // 准备测试数据
    List<IObjectData> mustProList = Arrays.asList(mustProData1);

    // Mock 只有单选产品
    when(mustProData1.get(SuccessfulStoreRangeConstants.PRODUCT_MULTIPLE)).thenReturn(null);
    when(mustProData1.get(SuccessfulStoreRangeConstants.PRODUCT)).thenReturn("single_product");

    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) buildAggregatedDataMethod.invoke(controller, mustProList);

    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertTrue(result.contains("single_product"));
  }

  @Test
  @DisplayName("测试聚合数据方法 - 去重功能")
  void testBuildAggregatedData_WithDuplicateProducts() throws Exception {
    // 准备测试数据
    List<IObjectData> mustProList = Arrays.asList(mustProData1, mustProData2);

    // Mock 重复的产品
    when(mustProData1.get(SuccessfulStoreRangeConstants.PRODUCT_MULTIPLE)).thenReturn("product1,product2");
    when(mustProData1.get(SuccessfulStoreRangeConstants.PRODUCT)).thenReturn("product1"); // 重复

    when(mustProData2.get(SuccessfulStoreRangeConstants.PRODUCT_MULTIPLE)).thenReturn("product2,product3");
    when(mustProData2.get(SuccessfulStoreRangeConstants.PRODUCT)).thenReturn("product3"); // 重复

    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) buildAggregatedDataMethod.invoke(controller, mustProList);

    // 验证结果 - 应该去重
    assertNotNull(result);
    assertEquals(3, result.size()); // product1, product2, product3 (去重后)
    assertTrue(result.contains("product1"));
    assertTrue(result.contains("product2"));
    assertTrue(result.contains("product3"));
  }

  @Test
  @DisplayName("测试聚合数据方法 - 空列表")
  void testBuildAggregatedData_WithEmptyList() throws Exception {
    // 准备测试数据
    List<IObjectData> mustProList = Lists.newArrayList();

    // 执行测试
    @SuppressWarnings("unchecked")
    List<String> result = (List<String>) buildAggregatedDataMethod.invoke(controller, mustProList);

    // 验证结果
    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  @DisplayName("测试参数对象创建")
  void testArgCreation() {
    AllDataList.Arg arg = new AllDataList.Arg();
    assertNotNull(arg);
  }

  @Test
  @DisplayName("测试结果对象创建")
  void testResultCreation() {
    AllDataList.Result result = new AllDataList.Result();
    assertNotNull(result);
  }
}
