POST http://127.0.0.1:8080/API/v1/rest/object/DisplayDistrAchSummaryObj/controller/DisplayReportDetail
authority: crm.ceshi112.com
pragma: no-cache
cache-control: no-cache
fs-device-type: mobile
netstarttime: 1753699123599
accept-language: zh-CN
user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 wechatdevtools/1.06.2412050 MicroMessenger/8.0.5 Language/zh_CN webview/
accept: */*
sec-fetch-site: cross-site
sec-fetch-mode: cors
sec-fetch-dest: empty
referer: https://servicewechat.com/wx9843d888f2348765/devtools/page-frame.html
cookie: FSAuthX=0G60C32D1mC00004faUcDNCVHUpFB0dc9uO8oJkTdWspLkWVhh7ZjLsOZSrPIcttbBlCrbCocaIGBVi7OMHsw4710BctVPfTJhx38K5yEQkCyARfzRwB4KiypivVxmqTN7KygM1ptyztez00hExmPZl6OgfPNkjKFYMEbkG1QSFwBdTDlyfzuOl3AzakdyUXIMuVJI0g16ktClp9BiORg5x39PS6JKVNLzilRIzjuPhTL6bkYjDfK3tRcbE5; FSAuthXC=0G60C32D1mC00004faUcDNCVHUpFB0dc9uO8oJkTdWspLkWVhh7ZjLsOZSrPIcttbBlCrbCocaIGBVi7OMHsw4710BctVPfTJhx38K5yEQkCyARfzRwB4KiypivVxmqTN7KygM1ptyztez00hExmPZl6OgfPNkjKFYMEbkG1QSFwBdTDlyfzuOl3AzakdyUXIMuVJI0g16ktClp9BiORg5x39PS6JKVNLzilRIzjuPhTL6bkYjDfK3tRcbE5;
Content-Type: application/json; charset=UTF-8
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 83921
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"accountId":"67ed010850368e0001645d62","actionId":"684bd7b7df4e6d4ee4e91647","checkinsId":"684bd7b7df4e6d4ee4e91645","sourceActionId":"6315da743263d52b08660682","reportType":"0","checkinId":"684bd7b7df4e6d4ee4e91645","dataId":"6846aeb4130922000171f04a","isShow":1,"isMustShow":1}



###



POST http://127.0.0.1:80/API/v1/rest/object/DisplayDistrAchSummaryObj/controller/DisplayReportDetail
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 783092
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "accountId": "68020b27f696320001a45bd3",
  "actionId": "689969f56047255eacc26729",
  "checkinsId": "689969f56047255eacc26727",
  "sourceActionId": "655c673d96005b3bbad17d78",
  "reportType": "0",
  "checkinId": "689969f56047255eacc26727",
  "isShow": 1,
  "isMustShow": 1
}
###


# curl -X "POST" "http://fs-paas-auth.nsvc.foneshare.cn/fs-paas-auth/tenant/cache/clear"
#-H 'x-fs-ei: 590172'
#-H 'Content-Type: application/json; charset=utf-8'
#-d '{
#"tenantIds": "590172"
#}'
POST http://fs-paas-auth.nsvc.foneshare.cn/fs-paas-auth/tenant/cache/clear
x-fs-ei: 590172
Content-Type: application/json; charset=utf-8

{
"tenantIds": "590172"
}

###

# curl --location --request POST 'http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/Task?triggerFlow=true&skipFunctionAction=true&triggerWorkFlow=false'
#--header 'x-fs-peer-name: OpenAPI-V2.0'
#--header 'x-fs-userInfo: -10000'
#--header 'x-fs-ei: 84788'
#--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)'
#--header 'Content-Type: application/json;charset=utf-8'
#--data-raw '{
#    "salaryTaskMessage": {
#        "tenantId": "84788",
#        "ea": "84788",
#        "dateStr": "2025-06-16",
#        "salaryRuleId": "684fedfbe5e5147bf428229f",
#        "startDateStr": "2025-06-16",
#        "endDateStr": "2025-06-16",
#        "flag": 0
#    }
#}'
POST http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/Task
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 84319
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "salaryTaskMessage": {
    "tenantId": "88270",
    "ea": "88270",
    "dateStr": "2026-06-30",
    "salaryRuleId": "685a6d118a80d000075e7139",
    "startDateStr": "2026-02-08",
    "endDateStr": "2026-02-08",
    "flag": 0
  }
}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryPaymentSlipObj/action/UpdateSalaryData
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"objectDataId":"68707d798d69bf0007eca07d","trigger_info":{"trigger_page":"Detail"}}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/RelatedList
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"serializeEmpty":false,"extractExtendInfo":true,"name":"20250715-0129","associate_object_data_id":"687545aa1ca9007213fb9408","associate_object_describe_api_name":"SalaryPaymentSlipObj","associated_object_describe_api_name":"SalaryDataObj","associated_object_field_related_list_name":"target_related_list_salary_payment_slip_SalaryPaymentSlipObj__c","include_associated":true,"search_query_info":"{\"limit\":20,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}","object_describe_api_name":"SalaryDataObj","search_template_id":"5d0c806a7cfed91f3e95ba8e","include_describe":false,"include_layout":false,"need_tag":true,"search_template_type":"default","ignore_scene_record_type":false,"pageSizeOption":[20,50,100],"list_type":"related","field_related_api_name":"salary_payment_slip","lookup_describe_api_name":"SalaryPaymentSlipObj","lookup_data_id":"687545aa1ca9007213fb9408","related_list_component":{"type":"relatedlist","buttons":[],"relationType":2,"api_name":"SalaryDataObj_salary_payment_slip_related_list","header":"工资条","ref_object_api_name":"SalaryDataObj","related_list_name":"target_related_list_salary_payment_slip_SalaryPaymentSlipObj__c","field_api_name":"salary_payment_slip","nameI18nKey":"SalaryDataObj.field.salary_payment_slip.reference_label","limit":1,"order":10,"is_hidden":false,"_id":"SalaryDataObj_salary_payment_slip_related_list","render_type":"RelatedlistNew"}}

###
POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/ListHeader
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"include_layout":true,"apiname":"SalaryRuleObj","layout_type":"list","layout_by_template":true,"check_edit_permission":true,"list_type":"list","list_component":{"isRunning":true,"type":"list","isConnectApp":false,"api_name":"list_component","header":"列表页","nameI18nKey":"paas.udobj.list_page","view_info":[{"name":"list_view","is_default":true,"is_show":true},{"name":"split_view","is_default":false,"is_show":true}],"filters_info":[{"fields":[],"page_type":"list"}],"button_info":[{"hidden":[],"page_type":"list","render_type":"list_normal","order":["Add_button_default","IntelligentForm_button_default","Export_button_default","ExportFile_button_default"],"exposed_button":1},{"hidden":[],"page_type":"list","render_type":"list_batch","order":["ChangeOwner_button_default","Abolish_button_default","AddTeamMember_button_default","DeleteTeamMember_button_default","Lock_button_default","Unlock_button_default","Export_button_default","ExportFile_button_default","ChangePartnerOwner_button_default","SendMail_button_default","Print_button_default"],"exposed_button":null},{"hidden":[],"page_type":"list","render_type":"list_single","order":[],"exposed_button":0}],"define_view_info":["list_view","split_view"],"scene_info":[{"hidden":[],"page_type":"list","render_type":"drop_down","order":["All","Participate","InCharge","SubInCharge","InChargeDept","Shared","SubParticipate"]}],"attributes":{"field_align":null,"enable_mobile_layout":null},"sourceId":"SalaryRuleObj","pluginParams":{"entryType":"","objectApiName":"SalaryRuleObj"},"disableLazyload":true},"cross_object_filter":false,"describeVersionMap":{"SalaryRuleObj":71}}
###

POST http://127.0.0.1:8080/API/v1/rest/object/EmployeeFixedSalaryObj/controller/WebDetail
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 84319
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"layoutVersion":"V3","objectDataId":"686ba27caff01600077d435a","objectDescribeApiName":"EmployeeFixedSalaryObj","fromRecycleBin":false,"management":false,"serializeEmpty":false,"describeVersionMap":{"EmployeeFixedSalaryObj":56}}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryPaymentSlipObj/action/BulkInvalid
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"json":"{\"dataList\":[{\"object_describe_api_name\":\"SalaryPaymentSlipObj\",\"tenant_id\":\"88270\",\"_id\":\"687545b11ca9007213fb9411\"}]}"}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryPaymentSlipObj/action/UpdateSalaryData
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"objectDataId":"68707d798d69bf0007eca07d","trigger_info":{"trigger_page":"Detail"}}
###

POST http://127.0.0.1:80/API/v1/rest/object/SuccessfulStoreRangeObj/controller/CheckSuccess
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 784077
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"matchFields":[{"apiName":"ext13__c","ruleApiName":"latitude1__c","objApiName":"object_Vik5s__c","type":"channel"},{"apiName":"ext18Name__c","ruleApiName":"latitude2__c","objApiName":"object_c4K2n__c","type":"channel"},{"apiName":"department__c","ruleApiName":"latitude3__c","objApiName":"AccountObj","type":"dept"}],"matchType":1,"recordType":"distribution_standards__c","successConfig":{"latitude1__c":{"apiName":"ext13__c","ruleApiName":"latitude1__c","objApiName":"object_Vik5s__c","type":"channel"},"latitude2__c":{"apiName":"ext18Name__c","ruleApiName":"latitude2__c","objApiName":"object_c4K2n__c","type":"channel"},"latitude3__c":{"apiName":"department__c","ruleApiName":"latitude3__c","objApiName":"AccountObj","type":"dept"}},"customData":{},"storeId":"65f436ae2358140001db194f"}
###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/ListHeader
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"include_layout":true,"apiname":"SalaryRuleObj","layout_type":"list","layout_by_template":true,"check_edit_permission":true,"list_type":"list","list_component":{"isRunning":true,"type":"list","isConnectApp":false,"api_name":"list_component","header":"列表页","nameI18nKey":"paas.udobj.list_page","view_info":[{"name":"list_view","is_default":true,"is_show":true},{"name":"split_view","is_default":false,"is_show":true}],"filters_info":[{"fields":[],"page_type":"list"}],"button_info":[{"hidden":[],"page_type":"list","render_type":"list_normal","order":["Add_button_default","IntelligentForm_button_default","Export_button_default","ExportFile_button_default"],"exposed_button":1},{"hidden":[],"page_type":"list","render_type":"list_batch","order":["ChangeOwner_button_default","Abolish_button_default","AddTeamMember_button_default","DeleteTeamMember_button_default","Lock_button_default","Unlock_button_default","Export_button_default","ExportFile_button_default","ChangePartnerOwner_button_default","SendMail_button_default","Print_button_default"],"exposed_button":null},{"hidden":[],"page_type":"list","render_type":"list_single","order":[],"exposed_button":0}],"define_view_info":["list_view","split_view"],"scene_info":[{"hidden":[],"page_type":"list","render_type":"drop_down","order":["All","Participate","InCharge","SubInCharge","InChargeDept","Shared","SubParticipate"]}],"attributes":{"field_align":null,"enable_mobile_layout":null},"sourceId":"SalaryRuleObj","pluginParams":{"entryType":"","objectApiName":"SalaryRuleObj"},"disableLazyload":true},"cross_object_filter":false}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/action/Edit
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"object_data":{"lock_rule":null,"calculation_formula":"工资项：加班时长（小时）","applicable_department_inte":[],"extend_obj_data_id":null,"created_by__r":{"picAddr":"","mobile":null,"description":"","dept":"1001","supervisorId":"1005","title":null,"empNum":"","modifyTime":1750330404399,"post":"系统扛把子","createTime":1678679831999,"phone":"","name":"企业管理员2","nickname":"企业管理员2","tenantId":"88270","id":"1000","position":null,"enterpriseName":null,"email":"","status":0},"life_status_before_invalid":null,"owner_department_id":"1001","effective_date":1750780800000,"owner_department":"销售中心","next_month_distribution_op":"1","applicable_person_inte__l":[{"id":"1026","tenantId":"88270","name":"十三","picAddr":"","email":"","nickname":"十三","supervisorId":"1000","phone":"","description":"","status":0,"createTime":1687772867999,"modifyTime":1687772997244,"dept":"1009","post":"","empNum":""}],"lock_status":"0","package":"CRM","data_own_department__r":{"deptName":"销售中心","leaderName":null,"leaderUserId":null,"deptId":"1001","deptType":"dept","parentId":"1000","status":0},"create_time":1750837409761,"applicable_out_tenant":null,"version":"3","created_by":["1000"],"relevant_team":[{"teamMemberEmployee":["1000"],"teamMemberRole":"1","teamMemberRoleList":["1"],"teamMemberPermissionType":"2","outTenantId":"","sourceType":"","teamMemberType":"0","teamMemberDeptCascade":"0"}],"data_own_department":["1001"],"salary_item__r":[{"name":"工资项：加班时长（小时）","_id":"685b67eb4a4b2300073d9973"}],"salary_statement_header":"[\"employee\",\"main_department\",\"phone_number\",\"start_date\",\"end_date\",\"payable_salary\"]","name":"十三月结工-加班时长小时","_id":"685ba8a14a4b2300073e8ef3","applicable_person_inte":["1026"],"data_own_department__l":[{"parentId":"1000","deptId":"1001","deptName":"销售中心","status":0,"deptType":"dept"}],"distribution_cycle":"3","tenant_id":"88270","distribution_time":"3","salary_item":["685b67eb4a4b2300073d9973"],"origin_source":null,"lock_user":null,"is_deleted":false,"object_describe_api_name":"SalaryRuleObj","applicable_out_role":null,"owner__l":[{"id":"1000","tenantId":"88270","name":"企业管理员2","picAddr":"","email":"","nickname":"企业管理员2","supervisorId":"1005","phone":"","description":"","status":0,"createTime":1678679831999,"modifyTime":1750330404399,"dept":"1001","post":"系统扛把子","empNum":""}],"out_owner":null,"relevant_team__r":"企业管理员2","owner__r":{"picAddr":"","mobile":null,"description":"","dept":"1001","supervisorId":"1005","title":null,"empNum":"","modifyTime":1750330404399,"post":"系统扛把子","createTime":1678679831999,"phone":"","name":"企业管理员2","nickname":"企业管理员2","tenantId":"88270","id":"1000","position":null,"enterpriseName":null,"email":"","status":0},"owner":["1000"],"last_modified_time":1750994980360,"life_status":"normal","last_modified_by__l":[{"id":"1000","tenantId":"88270","name":"企业管理员2","picAddr":"","email":"","nickname":"企业管理员2","supervisorId":"1005","phone":"","description":"","status":0,"createTime":1678679831999,"modifyTime":1750330404399,"dept":"1001","post":"系统扛把子","empNum":""}],"created_by__l":[{"id":"1000","tenantId":"88270","name":"企业管理员2","picAddr":"","email":"","nickname":"企业管理员2","supervisorId":"1005","phone":"","description":"","status":0,"createTime":1678679831999,"modifyTime":1750330404399,"dept":"1001","post":"系统扛把子","empNum":""}],"last_modified_by":["1000"],"out_tenant_id":null,"record_type":"default__c","salary_method":"3","auto_created_payrollvouche":true,"last_modified_by__r":{"picAddr":"","mobile":null,"description":"","dept":"1001","supervisorId":"1005","title":null,"empNum":"","modifyTime":1750330404399,"post":"系统扛把子","createTime":1678679831999,"phone":"","name":"企业管理员2","nickname":"企业管理员2","tenantId":"88270","id":"1000","position":null,"enterpriseName":null,"email":"","status":0},"applicable_role_inte":[],"order_by":null,"next_week_distribution_op":null,"applicable_out_user":null},"fillMultiLang":true,"maskFieldApiNames":{"SalaryItemObj":[]},"optionInfo":{"supportValidationResult":true}}

###



POST http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/TypeQuery
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: 1003
x-fs-ei: 92390
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"salary_data_id":"686ab9a8aff01600077aba88"}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/TotalQuery
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: 1019
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "period_id": "2026-02-12_2026-02-12",
  "period_title": "2026年02月12日",
  "start_date": "2026-02-12",
  "end_date": "2026-02-12",
  "is_current": false,
  "distribution_status": "0",
  "salary_data_id": "68637f77c3f0b200074ab88c"
}


###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/DailyQuery
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: 1020
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"month":"2025-12","query_date":"2025-12-05","salary_data_id":"68592d3db7e3ae0001e42ccc"}


###

POST http://127.0.0.1:8080/API/v1/rest/object/button/service/findButtonList
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"describeApiName":"SalaryPaymentSlipObj"}

###


POST http://127.0.0.1:8080/API/v1/rest/object/layout/service/enable_edit_layout
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 84319
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "describeApiName": "SalaryItemObj",
  "detailPageReferenceFieldConfig": false
}

###


POST http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/ListHeader
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"include_layout":true,"apiname":"SalaryDataObj","layout_type":"list","layout_by_template":true,"check_edit_permission":true,"list_type":"related","cross_object_filter":false,"field_related_api_name":"salary_payment_slip","lookup_describe_api_name":"SalaryPaymentSlipObj","lookup_data_id":"685e1040d63f3d1de197c873","related_list_component":{"type":"relatedlist","buttons":[],"relationType":2,"api_name":"SalaryDataObj_salary_payment_slip_related_list","header":"工资条","ref_object_api_name":"SalaryDataObj","related_list_name":"target_related_list_salary_payment_slip_SalaryPaymentSlipObj__c","field_api_name":"salary_payment_slip","nameI18nKey":"SalaryDataObj.field.salary_payment_slip.reference_label","limit":1,"order":7,"is_hidden":false,"_id":"SalaryDataObj_salary_payment_slip_related_list","render_type":"RelatedlistNew"}}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryItemObj/controller/ExpressionCheck
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"json_data":"{\"expressionType\":\"change_order\",\"expression\":\"IF($EXT#KPI#685a907dd44b3d000755d8ab$ > 10, $EXT#KPI#685a907dd44b3d000755d8ab$ *0.3, $EXT#KPI#685a907dd44b3d000755d8ab$*0.1)\",\"objectDescribeApiName\":\"SalaryKPIObj\",\"returnType\":\"true_or_false\",\"setDefaultToZero\":true}","onlySupportGrounded":false,"extFields":[],"errorReminder":true}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryPaymentSlipObj/action/Distribute
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"objectDataId":"68707d798d69bf0007eca07d","trigger_info":{"trigger_page":"Detail"}}
###

POST http://127.0.0.1:8080/API/v1/rest/object/SuccessfulStoreRangeObj/controller/CheckSuccess
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 83921
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "matchFields": [
    {
      "apiName": "ext13__c",
      "ruleApiName": "latitude10__c",
      "objApiName": "object_hk67z__c",
      "type": "channel"
    },
    {
      "apiName": "system_name__c",
      "ruleApiName": "latitude11__c",
      "objApiName": "object_n6kaN__c",
      "type": "channel"
    },
    {
      "apiName": "field_z6moI__c",
      "ruleApiName": "latitude12__c",
      "objApiName": "AccountObj",
      "type": "dept"
    },
    {
      "apiName": "ext31__c",
      "ruleApiName": "latitude13__c",
      "objApiName": "AccountObj",
      "type": "select"
    }
  ],
  "matchType": 1,
  "recordType": "distribution_standards__c",
  "successConfig": {
    "latitude11__c": {
      "apiName": "system_name__c",
      "ruleApiName": "latitude11__c",
      "objApiName": "object_n6kaN__c",
      "type": "channel"
    },
    "latitude12__c": {
      "apiName": "field_z6moI__c",
      "ruleApiName": "latitude12__c",
      "objApiName": "AccountObj",
      "type": "dept"
    },
    "latitude13__c": {
      "apiName": "ext31__c",
      "ruleApiName": "latitude13__c",
      "objApiName": "AccountObj",
      "type": "select"
    },
    "latitude10__c": {
      "apiName": "ext13__c",
      "ruleApiName": "latitude10__c",
      "objApiName": "object_hk67z__c",
      "type": "channel"
    }
  },
  "customData": {

  },
  "storeId": "659fd3d281e51600019ee41a"
}
###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/ListHeader
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"include_layout":true,"apiname":"SalaryRuleObj","layout_type":"list","layout_by_template":true,"check_edit_permission":true,"list_type":"list","list_component":{"isRunning":true,"type":"list","isConnectApp":false,"api_name":"list_component","header":"列表页","nameI18nKey":"paas.udobj.list_page","view_info":[{"name":"list_view","is_default":true,"is_show":true},{"name":"split_view","is_default":false,"is_show":true}],"filters_info":[{"fields":[],"page_type":"list"}],"button_info":[{"hidden":[],"page_type":"list","render_type":"list_normal","order":["Add_button_default","IntelligentForm_button_default","Export_button_default","ExportFile_button_default"],"exposed_button":1},{"hidden":[],"page_type":"list","render_type":"list_batch","order":["ChangeOwner_button_default","Abolish_button_default","AddTeamMember_button_default","DeleteTeamMember_button_default","Lock_button_default","Unlock_button_default","Export_button_default","ExportFile_button_default","ChangePartnerOwner_button_default","SendMail_button_default","Print_button_default"],"exposed_button":null},{"hidden":[],"page_type":"list","render_type":"list_single","order":[],"exposed_button":0}],"define_view_info":["list_view","split_view"],"scene_info":[{"hidden":[],"page_type":"list","render_type":"drop_down","order":["All","Participate","InCharge","SubInCharge","InChargeDept","Shared","SubParticipate"]}],"attributes":{"field_align":null,"enable_mobile_layout":null},"sourceId":"SalaryRuleObj","pluginParams":{"entryType":"","objectApiName":"SalaryRuleObj"},"disableLazyload":true},"cross_object_filter":false}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/action/Edit
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"object_data":{"lock_rule":null,"calculation_formula":"工资项：加班时长（小时）","applicable_department_inte":[],"extend_obj_data_id":null,"created_by__r":{"picAddr":"","mobile":null,"description":"","dept":"1001","supervisorId":"1005","title":null,"empNum":"","modifyTime":1750330404399,"post":"系统扛把子","createTime":1678679831999,"phone":"","name":"企业管理员2","nickname":"企业管理员2","tenantId":"88270","id":"1000","position":null,"enterpriseName":null,"email":"","status":0},"life_status_before_invalid":null,"owner_department_id":"1001","effective_date":1750780800000,"owner_department":"销售中心","next_month_distribution_op":"1","applicable_person_inte__l":[{"id":"1026","tenantId":"88270","name":"十三","picAddr":"","email":"","nickname":"十三","supervisorId":"1000","phone":"","description":"","status":0,"createTime":1687772867999,"modifyTime":1687772997244,"dept":"1009","post":"","empNum":""}],"lock_status":"0","package":"CRM","data_own_department__r":{"deptName":"销售中心","leaderName":null,"leaderUserId":null,"deptId":"1001","deptType":"dept","parentId":"1000","status":0},"create_time":1750837409761,"applicable_out_tenant":null,"version":"3","created_by":["1000"],"relevant_team":[{"teamMemberEmployee":["1000"],"teamMemberRole":"1","teamMemberRoleList":["1"],"teamMemberPermissionType":"2","outTenantId":"","sourceType":"","teamMemberType":"0","teamMemberDeptCascade":"0"}],"data_own_department":["1001"],"salary_item__r":[{"name":"工资项：加班时长（小时）","_id":"685b67eb4a4b2300073d9973"}],"salary_statement_header":"[\"employee\",\"main_department\",\"phone_number\",\"start_date\",\"end_date\",\"payable_salary\"]","name":"十三月结工-加班时长小时","_id":"685ba8a14a4b2300073e8ef3","applicable_person_inte":["1026"],"data_own_department__l":[{"parentId":"1000","deptId":"1001","deptName":"销售中心","status":0,"deptType":"dept"}],"distribution_cycle":"3","tenant_id":"88270","distribution_time":"3","salary_item":["685b67eb4a4b2300073d9973"],"origin_source":null,"lock_user":null,"is_deleted":false,"object_describe_api_name":"SalaryRuleObj","applicable_out_role":null,"owner__l":[{"id":"1000","tenantId":"88270","name":"企业管理员2","picAddr":"","email":"","nickname":"企业管理员2","supervisorId":"1005","phone":"","description":"","status":0,"createTime":1678679831999,"modifyTime":1750330404399,"dept":"1001","post":"系统扛把子","empNum":""}],"out_owner":null,"relevant_team__r":"企业管理员2","owner__r":{"picAddr":"","mobile":null,"description":"","dept":"1001","supervisorId":"1005","title":null,"empNum":"","modifyTime":1750330404399,"post":"系统扛把子","createTime":1678679831999,"phone":"","name":"企业管理员2","nickname":"企业管理员2","tenantId":"88270","id":"1000","position":null,"enterpriseName":null,"email":"","status":0},"owner":["1000"],"last_modified_time":1750994980360,"life_status":"normal","last_modified_by__l":[{"id":"1000","tenantId":"88270","name":"企业管理员2","picAddr":"","email":"","nickname":"企业管理员2","supervisorId":"1005","phone":"","description":"","status":0,"createTime":1678679831999,"modifyTime":1750330404399,"dept":"1001","post":"系统扛把子","empNum":""}],"created_by__l":[{"id":"1000","tenantId":"88270","name":"企业管理员2","picAddr":"","email":"","nickname":"企业管理员2","supervisorId":"1005","phone":"","description":"","status":0,"createTime":1678679831999,"modifyTime":1750330404399,"dept":"1001","post":"系统扛把子","empNum":""}],"last_modified_by":["1000"],"out_tenant_id":null,"record_type":"default__c","salary_method":"3","auto_created_payrollvouche":true,"last_modified_by__r":{"picAddr":"","mobile":null,"description":"","dept":"1001","supervisorId":"1005","title":null,"empNum":"","modifyTime":1750330404399,"post":"系统扛把子","createTime":1678679831999,"phone":"","name":"企业管理员2","nickname":"企业管理员2","tenantId":"88270","id":"1000","position":null,"enterpriseName":null,"email":"","status":0},"applicable_role_inte":[],"order_by":null,"next_week_distribution_op":null,"applicable_out_user":null},"fillMultiLang":true,"maskFieldApiNames":{"SalaryItemObj":[]},"optionInfo":{"supportValidationResult":true}}

###



POST http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/TypeQuery
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: 1003
x-fs-ei: 92390
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"salary_data_id":"686ab9a8aff01600077aba88"}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/TotalQuery
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: 1019
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "period_id": "2026-02-12_2026-02-12",
  "period_title": "2026年02月12日",
  "start_date": "2026-02-12",
  "end_date": "2026-02-12",
  "is_current": false,
  "distribution_status": "0",
  "salary_data_id": "68637f77c3f0b200074ab88c"
}


###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/DailyQuery
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: 1020
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"month":"2025-12","query_date":"2025-12-05","salary_data_id":"68592d3db7e3ae0001e42ccc"}


###

POST http://127.0.0.1:8080/API/v1/rest/object/button/service/findButtonList
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"describeApiName":"SalaryPaymentSlipObj"}

###


POST http://127.0.0.1:8080/API/v1/rest/object/layout/service/enable_edit_layout
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 84319
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "describeApiName": "SalaryItemObj",
  "detailPageReferenceFieldConfig": false
}

###


POST http://127.0.0.1:8080/API/v1/rest/object/SalaryDataObj/controller/ListHeader
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"include_layout":true,"apiname":"SalaryDataObj","layout_type":"list","layout_by_template":true,"check_edit_permission":true,"list_type":"related","cross_object_filter":false,"field_related_api_name":"salary_payment_slip","lookup_describe_api_name":"SalaryPaymentSlipObj","lookup_data_id":"685e1040d63f3d1de197c873","related_list_component":{"type":"relatedlist","buttons":[],"relationType":2,"api_name":"SalaryDataObj_salary_payment_slip_related_list","header":"工资条","ref_object_api_name":"SalaryDataObj","related_list_name":"target_related_list_salary_payment_slip_SalaryPaymentSlipObj__c","field_api_name":"salary_payment_slip","nameI18nKey":"SalaryDataObj.field.salary_payment_slip.reference_label","limit":1,"order":7,"is_hidden":false,"_id":"SalaryDataObj_salary_payment_slip_related_list","render_type":"RelatedlistNew"}}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryItemObj/controller/ExpressionCheck
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"json_data":"{\"expressionType\":\"change_order\",\"expression\":\"IF($EXT#KPI#685a907dd44b3d000755d8ab$ > 10, $EXT#KPI#685a907dd44b3d000755d8ab$ *0.3, $EXT#KPI#685a907dd44b3d000755d8ab$*0.1)\",\"objectDescribeApiName\":\"SalaryKPIObj\",\"returnType\":\"true_or_false\",\"setDefaultToZero\":true}","onlySupportGrounded":false,"extFields":[],"errorReminder":true}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryPaymentSlipObj/action/Distribute
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 84788
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "objectDataId": "685109a25cd6bb32495af92c",
  "salaryTaskMessage": {
    "tenantId": "84788",
    "ea": "84788",
    "dateStr": "2025-06-17",
    "salaryRuleId": "6850d886e5e514580ce151e9",
    "startDateStr": "2025-06-17",
    "endDateStr": "2025-06-17",
    "flag": 0
  }
}

###

POST http://127.0.0.1:8080/API/v1/rest/object/SalaryPaymentSlipObj/action/Distribute
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{"objectDataId":"685a78d346eda5258a6a808e","trigger_info":{"trigger_page":"Detail"}}

###

# curl 'http://localhost:8888/FHH/EM1HNCRM/API/v1/object/SalaryItemObj/controller/ExpressionCheck?_fs_token=PMLbE3GqOcKjCcKnOIqqDZHXBM5aPMOjCJHbEJ9ZC3SmE3Ct&traceId=E-E.84319.1000-54108251'
#  -H 'Accept: application/json, text/javascript, */*; q=0.01'
#  -H 'Cache-Control: no-cache'
#  -H 'Connection: keep-alive'
#  -H 'Content-Type: application/json; charset=UTF-8'
#  -H 'Origin: http://localhost:8888'
#  -H 'Pragma: no-cache'
#  -H 'Referer: http://localhost:8888/XV/UI/Home'
#  -H 'Sec-Fetch-Dest: empty'
#  -H 'Sec-Fetch-Mode: cors'
#  -H 'Sec-Fetch-Site: same-origin'
#  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
#  -H 'X-Requested-With: XMLHttpRequest'
#  -H 'accept-language: zh-CN,zh-TW;0.9,en;0.8'
#  -H 'sec-ch-ua: "Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "macOS"'
#  -H 'x-trace-id: 84319_1000_1749033342494:333'
#  --data-raw '{"json_data":"{\"expressionType\":\"change_order\",\"expression\":\"$4c64x6c451as6d4adas4d5a$\",\"objectDescribeApiName\":\"EmployeeKPIObj\",\"returnType\":\"true_or_false\",\"setDefaultToZero\":true}","onlySupportGrounded":false,"extFields":[],"errorReminder":true}'
POST http://crm.ceshi112.com/FHH/EM1HNCRM/API/v1/object/SalaryItemObj/controller/ExpressionCheck?_fs_token=PMLbE3GqOcKjCcKnOIqqDZHXBM5aPMOjCJHbEJ9ZC3SmE3Ct&traceId=E-E.84319.1000-54108251
Accept: application/json, text/javascript, */*; q=0.01
Cache-Control: no-cache
Connection: keep-alive
Sec-Fetch-Dest: empty
Sec-Fetch-Mode: cors
Sec-Fetch-Site: same-origin
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
X-Requested-With: XMLHttpRequest
accept-language: zh-CN,zh-TW;0.9,en;0.8
sec-ch-ua: "Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "macOS"
x-trace-id: 84319_1000_1749033342494:333
Content-Type: application/json; charset=UTF-8
cookie: FSAuthX=0G60RF9n1WC0000zHwKyBnndAkQD3XHXkFftCzj41KjgYGJXXSNYhPd556eO1EdyFlp4xkyiW0j6JMdFIujLc0twaC9vnDmlfzAByyCbBtQiozJtvBxJQ5mSRmQyuwj3znbdWkNWfamVKlAPPYJW5KHZYz88sJyB4dyNSKXtujPw8msUBsrfnUDdYUQOUxuVAcA5huilCsetGTeyrd298jWM0QOeoakROQj4TCyRCqyESE45YVBpq9cznPzV7; FSAuthXC=0G60RF9n1WC0000zHwKyBnndAkQD3XHXkFftCzj41KjgYGJXXSNYhPd556eO1EdyFlp4xkyiW0j6JMdFIujLc0twaC9vnDmlfzAByyCbBtQiozJtvBxJQ5mSRmQyuwj3znbdWkNWfamVKlAPPYJW5KHZYz88sJyB4dyNSKXtujPw8msUBsrfnUDdYUQOUxuVAcA5huilCsetGTeyrd298jWM0QOeoakROQj4TCyRCqyESE45YVBpq9cznPzV7;

{"json_data":"{\"expressionType\":\"change_order\",\"expression\":\"$4c64x6c451as6d4adas4d5a$\",\"objectDescribeApiName\":\"EmployeeKPIObj\",\"returnType\":\"true_or_false\",\"setDefaultToZero\":true}","onlySupportGrounded":false,"extFields":[],"errorReminder":true}

###




POST http://************:43022/open/material/wx/link/outapi/notice/sendNotice
accept: application/json, text/javascript, */*; q=0.01
accept-language: zh-CN,zh-TW;0.9,en;0.8
Content-Type: application/json; charset=UTF-8
x-tenant-id:84788
x-user-id:-10000

{"isFixedTime":2,"noticeCategoryId":null,"userVisibleRangeType":1,"targets":{"departments":[],"employees":[]},"downstreamAccepterVO":{"outerTenantIds":[],"outerTgroupIds":[],"outerUids":["E.84788.300404414"],"outerRoleIds":[],"stopOuterUids":[],"outerDepartmentIds":[],"outerTreeDepartmentIds":[],"publicTenantIds":[],"publicTgroupIds":[],"parentDeptListIds":[]},"imageTextParams":[{"title":"爱上对方水电费","content":"https://www.ceshi112.com","summary":"","coverImageUrl":"","type":"URL","isCoverImageInText":false,"fileAttachments":[]}]}

###



# curl 'https://crm.ceshi112.com/FHH/EM1HWaiQinV2/ruleWebServiceV2/checkAdminInfo?_fs_token=PZCqCsDZPZOjP68uCoqqOMHcBM9YPZ0jOJ1cPJHbEJGqEJbc&traceId=E-E.83150.1000-83599111'
#  -H 'accept: application/json, text/javascript, */*; q=0.01'
#  -H 'accept-language: zh-CN,zh-TW;0.9,en;0.8'
#  -H 'content-type: application/json; charset=UTF-8'
#  -H 'origin: https://crm.ceshi112.com'
#  -H 'priority: u=1, i'
#  -H 'referer: https://crm.ceshi112.com/XV/UI/Home'
#  -H 'sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"'
#  -H 'sec-ch-ua-mobile: ?0'
#  -H 'sec-ch-ua-platform: "Windows"'
#  -H 'sec-fetch-dest: empty'
#  -H 'sec-fetch-mode: cors'
#  -H 'sec-fetch-site: same-origin'
#  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
#  -H 'x-requested-with: XMLHttpRequest'
#  -H 'x-trace-id: 83150_1000_1746692056795:55'
#  --data-raw '{}'
POST https://crm.ceshi112.com/FHH/EM1HWaiQinV2/outerService/getQRCodeUrl
accept: application/json, text/javascript, */*; q=0.01
accept-language: zh-CN,zh-TW;0.9,en;0.8
origin: https://crm.ceshi112.com
priority: u=1, i
referer: https://crm.ceshi112.com/XV/UI/Home
sec-ch-ua: "Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Windows"
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: same-origin
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
x-requested-with: XMLHttpRequest
x-trace-id: 83150_1000_1746692056795:55
Content-Type: application/json; charset=UTF-8
cookie: FSAuthX=0G60kERc1GC0003pw1AFHknwkYERMAjLy4nRJqYgVyvOG3LnpmeI6pcXYkSWiUAESKGT4u2BXLLnTeqRzes1Q2uwpU3dey4QbENHni8Pp9vDkNvqSLKKR9PdHPK8pStTWyhmLQCa8hnAKZIFUH9OzXaL0yTkKb09WwY2VpCtbRbAfMXCHamXC374eT1XP1IahgUsmFvcKeo4xIM8YeUmzOMdUSDCPVsvNg0zy7I7FdZwmbFllsizsvkrdA70; FSAuthXC=0G60kERc1GC0003pw1AFHknwkYERMAjLy4nRJqYgVyvOG3LnpmeI6pcXYkSWiUAESKGT4u2BXLLnTeqRzes1Q2uwpU3dey4QbENHni8Pp9vDkNvqSLKKR9PdHPK8pStTWyhmLQCa8hnAKZIFUH9OzXaL0yTkKb09WwY2VpCtbRbAfMXCHamXC374eT1XP1IahgUsmFvcKeo4xIM8YeUmzOMdUSDCPVsvNg0zy7I7FdZwmbFllsizsvkrdA70;

{"id": "MN_ae3b6d2b8726cb08b5534ee0c3e2edc7"}

###

# curl --location --request POST 'http://127.0.0.1:8080/fs-crm-fmcg-wq-web/API/v1/rest/object/UserScheduleObj/controller/ListFillData?triggerFlow=true&skipFunctionAction=true&triggerWorkFlow=false'
#--header 'x-fs-peer-name: OpenAPI-V2.0'
#--header 'x-fs-userInfo: -10000'
#--header 'x-fs-ei: 84788'
#--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)'
#--header 'Content-Type: application/json;charset=utf-8'
#--data-raw '{
#    "userScheduleContent": {
#        "excelAccountIds": [
#            "63730ce7be72310001aaf525"
#        ]
#    }
#}'
POST http://127.0.0.1:80/API/v1/rest/object/DisplayDistrAchSummaryObj/controller/TotalReport
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 784077
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "accountId": "65f7e0451fb7080001d04dee",
  "businessDataApiName": "TPMActivityProofObj",
  "businessDataId": "681075ebf643240001483a20",
  "checkinsId": "68103c944a024834cae65e1d",
  "dataMainApiName": "TPMActivityProofObj",
  "dataMainId": "681075ebf643240001483a20",
  "recordType": "activity_evidence_report__c",
  "standardMainApiName": "TPMActivityAgreementObj",
  "standardMainId": "6810759a63f29b0001274cbf",
  "tenantId": "784077"
}

###

14:47:39.934 [Thread-8] INFO  c.f.p.a.j.p.RequestLogFilter fs-crm-fmcg-service/foneshare-gray/6810760af643240001484e07  uri:/
v1/object/DisplayDistrAchSummaryObj/controller/TotalReport,method:POST,queryParameters:{},headers:[accept-encoding=gzip,apibu
s-inner-application=FS-CRM-FMCG-WQ,apibus-inner-route-vip=FS-CRM-FMCG-WQ-STAGE,content-length=403,content-type=application/js
on; charset=utf-8,host=************:15238,user-agent=okhttp/4.11.0,x-forwarded-for=*************,x-forwarded-host=ncrm.nsvc.f
oneshare.cn,x-forwarded-port=8887,x-forwarded-proto=http,x-fs-ei=784077,x-fs-employee-id=6449,x-fs-enterprise-id=784077,x-fs-
peer-name=fs-crm-fmcg-service,x-fs-rpc-id=0.164,x-fs-trace-id=fs-crm-fmcg-service/foneshare-gray/6810760af643240001484e07,x-f
s-userinfo=6449,x-peer-name=fs-crm-fmcg-service/foneshare-gray,x-real-ip=*************,x-sys-not-replace=all,x-tenant-id=7840
77,x-trace=traceId=fs-crm-fmcg-service/foneshare-gray/6810760af643240001484e07;userId=null;color=false;rpcId=0.164,x-user-id=
6449],body:{"accountId":"65f7e0451fb7080001d04dee","businessDataApiName":"TPMActivityProofObj","businessDataId":"681075ebf643
240001483a20","checkinsId":"68103c944a024834cae65e1d","dataMainApiName":"TPMActivityProofObj","dataMainId":"681075ebf64324000
1483a20","recordType":"activity_evidence_report__c","standardMainApiName":"TPMActivityAgreementObj","standardMainId":"6810759
a63f29b0001274cbf","tenantId":"784077"},bodySize:403,byteCount:403 B




# curl --location --request POST 'http://127.0.0.1:8080/API/v1/object/CheckinsObj/controller/TestRoute'
#--header 'x-fs-ei: <x-fs-ei>'
#--header 'X-fs-Enterprise-Id: <X-fs-Enterprise-Id>'
#--header 'User-Agent: Apifox/1.0.0 (https://apifox.com)'
#--header 'content-type: <content-type>'
#--data-raw '<body data here>'
#DisplayDistrAchSummaryProdMatResController
POST http://127.0.0.1:8080/API/v1/object/DisplayDistrAchSummaryObj/controller/ProdMatRes
x-fs-ei: 83921
X-fs-Enterprise-Id: 83921
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json; charset=UTF-8

{
  "accountId" : "659fd3d281e51600019ee41a",
  "businessDataApiName" : "TPMActivityProofObj",
  "businessDataId" : "67f488a1aeb42f000192e025",
  "checkinsId" : "67f48839ea3fa82cdcc5a7ca",
  "dataMainApiName" : "TPMActivityProofObj",
  "dataMainId" : "67f4cb37716e460001a4d991",
  "recordType" : "activity_evidence_report__c",
  "standardMainApiName" : "TPMActivityAgreementObj",
  "standardMainId" : "67f33ca80a96e200011a1914",
  "tenantId" : "83921"
}
###


POST http://127.0.0.1:80/API/v1/object/DisplayDistrAchSummaryObj/controller/TotalReport
x-fs-ei: 784077
X-fs-Enterprise-Id: 784077
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json; charset=UTF-8

{"accountId":"6645a4b5ea66c200014108e2","businessDataApiName":"TPMActivityAgreementObj","businessDataId":"6863b181a0081c0001c8a285","checkinsId":"6863b018af7e2b12675b0f95","dataMainApiName":"TPMActivityProofObj","dataMainId":"6863b24950a7970001697646","recordType":"activity_evidence_report__c","standardMainApiName":"TPMActivityAgreementObj","standardMainId":"6863b181a0081c0001c8a285","tenantId":"784077"}

###
POST http://127.0.0.1:80/API/v1/object/DisplayDistrAchSummaryObj/controller/DisplayReportDetail
x-fs-ei: 784077
X-fs-Enterprise-Id: 784077
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json; charset=UTF-8

{"accountId":"6645a4b5ea66c200014108e2","actionId":"6863b018af7e2b12675b0f99","checkinsId":"6863b018af7e2b12675b0f95","sourceActionId":"67fd0416837e7911588312f2","reportType":"0","checkinId":"6863b018af7e2b12675b0f95","isShow":1,"isMustShow":1}

###

# SalaryRuleAdaptationTestController 场景1：单个内部员工适配检查
POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/AdaptationTest
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "testType": "single_internal",
  "employeeId": "1027"
}

###

# 场景2：单个外部员工适配检查
POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/AdaptationTest
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "testType": "session_MethodMismatch",
  "employeeId": "*********",
  "employeeIds" :["*********"]
}

###

# 场景2：单个外部员工适配检查
POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/AdaptationTest
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "testType": "session_NewEmployee",
  "employeeId": "100000001",
  "employeeIds" :["1035"]
}

###
# 场景2：单个外部员工适配检查
POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/AdaptationTest
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "testType": "session_NewRule",
  "employeeId": "100000001",
  "employeeIds" :["1035"]
}

###
# 场景2：单个外部员工适配检查
POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/AdaptationTest
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "testType": "session_MultipleRules",
  "employeeId": "100000001",
  "employeeIds" :["1035"]
}

###
# 场景2：单个外部员工适配检查
POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/AdaptationTest
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "testType": "session_RuleRemoved",
  "employeeId": "100000001",
  "employeeIds" :["1035"]
}

###


# 场景3：批量内部员工适配检查
POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/AdaptationTest
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "testType": "batch_internal",
  "employeeIds": ["1001", "1002", "1003"]
}

###

# 场景4：新员工是否需要创建固定工资表检查（内部/外部由ID推断）
POST http://127.0.0.1:8080/API/v1/rest/object/SalaryRuleObj/controller/AdaptationTest
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 88270
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
  "testType": "check_new_employee",
  "employeeId": "1002"
}
