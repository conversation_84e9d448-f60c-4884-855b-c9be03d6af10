# 薪资任务处理 API 文档

## 基本信息

- **服务器地址**: `http://10.112.5.251:35548`
- **API 路径**: `/API/v1/rest/object/SalaryDataObj/controller/Task`
- **请求方法**: `POST`
- **Content-Type**: `application/json;charset=utf-8`

## 接口描述

薪资任务处理接口，用于手动触发薪资计算任务。该接口调用 `PMMSalaryConsumer.handleTaskMessage` 中的逻辑来处理薪资相关任务。

### 业务逻辑说明

1. **计算周期**: `startDateStr` 和 `endDateStr` 定义薪资计算的时间范围
2. **当前日期**: `dateStr` 为当前日期，用于判断是否满足发放单生成条件
3. **智能处理**: 当 `flag=0` 时，如果当前日期满足发放条件，会自动生成发放单
4. **发放条件**: 根据薪资规则的发放时机设置判断：
   - **次日发放**: 每天都满足条件
   - **次周发放**: 仅在设定的星期几且为周一时满足
   - **次月发放**: 仅在设定的日期且为月初时满足

## 请求头

| 参数名         | 类型   | 必填 | 描述                                                |
| -------------- | ------ | ---- | --------------------------------------------------- |
| x-fs-peer-name | String | 是   | API 版本标识，固定值：`OpenAPI-V2.0`                |
| x-fs-userInfo  | String | 是   | 用户信息，系统用户使用：`-10000`                    |
| x-fs-ei        | String | 是   | 企业ID/租户ID                                       |
| User-Agent     | String | 否   | 用户代理，示例：`Apifox/1.0.0 (https://apifox.com)` |
| Content-Type   | String | 是   | 内容类型，固定值：`application/json;charset=utf-8`  |

## 请求参数

### 请求体结构

```json
{
    "salaryTaskMessage": {
        "tenantId": "string",
        "ea": "string", 
        "dateStr": "string",
        "salaryRuleId": "string",
        "startDateStr": "string",
        "endDateStr": "string",
        "flag": 0
    }
}
```

### 参数说明

| 参数名                         | 类型    | 必填 | 描述                                                                                                                         |
| ------------------------------ | ------- | ---- | ---------------------------------------------------------------------------------------------------------------------------- |
| salaryTaskMessage              | Object  | 是   | 薪资任务消息对象                                                                                                             |
| salaryTaskMessage.tenantId     | String  | 是   | 租户ID                                                                                                                       |
| salaryTaskMessage.ea           | String  | 否   | 企业账号                                                                                                                     |
| salaryTaskMessage.dateStr      | String  | 否   | 当前日期字符串，格式：`yyyy-MM-dd`<br/>用于判断是否满足发放单生成条件                                                        |
| salaryTaskMessage.salaryRuleId | String  | 是   | 薪资规则ID                                                                                                                   |
| salaryTaskMessage.startDateStr | String  | 是   | 计算周期开始日期，格式：`yyyy-MM-dd`                                                                                         |
| salaryTaskMessage.endDateStr   | String  | 是   | 计算周期结束日期，格式：`yyyy-MM-dd`                                                                                         |
| salaryTaskMessage.flag         | Integer | 是   | 处理标志<br/>- `0`: 计算薪资明细（如当前日期满足条件会自动生成发放单）<br/>- `1`: 生成发放单（如已在flag=0阶段处理过则跳过） |

## 响应结果

### 成功响应

```json
{
    "success": true,
    "message": "薪资任务处理成功"
}
```

### 失败响应

```json
{
    "success": false,
    "message": "薪资任务处理失败: 具体错误信息"
}
```

### 响应参数说明

| 参数名  | 类型    | 描述         |
| ------- | ------- | ------------ |
| success | Boolean | 处理是否成功 |
| message | String  | 处理结果消息 |

## 错误码说明

| 错误信息               | 描述                            |
| ---------------------- | ------------------------------- |
| "薪资任务消息不能为空" | salaryTaskMessage 参数为空      |
| "租户ID不能为空"       | tenantId 参数为空或空字符串     |
| "薪资规则ID不能为空"   | salaryRuleId 参数为空或空字符串 |
| "开始日期不能为空"     | startDateStr 参数为空或空字符串 |
| "结束日期不能为空"     | endDateStr 参数为空或空字符串   |
| "处理标志不能为空"     | flag 参数为空                   |

## 请求示例

### cURL 示例

```bash
curl -X POST "http://10.112.5.251:35548/API/v1/rest/object/SalaryDataObj/controller/Task" \
  -H "x-fs-peer-name: OpenAPI-V2.0" \
  -H "x-fs-userInfo: -10000" \
  -H "x-fs-ei: 84788" \
  -H "Content-Type: application/json;charset=utf-8" \
  -d '{
    "salaryTaskMessage": {
        "tenantId": "84788",
        "ea": "84788",
        "dateStr": "2025-06-17",
        "salaryRuleId": "6850d886e5e514580ce151e9",
        "startDateStr": "2025-06-17",
        "endDateStr": "2025-06-17",
        "flag": 0
    }
}'
```

### HTTP 示例

```http
POST http://10.112.5.251:35548/API/v1/rest/object/SalaryDataObj/controller/Task
x-fs-peer-name: OpenAPI-V2.0
x-fs-userInfo: -10000
x-fs-ei: 84788
User-Agent: Apifox/1.0.0 (https://apifox.com)
Content-Type: application/json;charset=utf-8

{
    "salaryTaskMessage": {
        "tenantId": "84788",
        "ea": "84788",
        "dateStr": "2025-06-17",
        "salaryRuleId": "6850d886e5e514580ce151e9",
        "startDateStr": "2025-06-17",
        "endDateStr": "2025-06-17",
        "flag": 0
    }
}
```

## 业务逻辑说明

1. **参数校验**: 接口首先会对所有必填参数进行校验

2. **任务处理**: 根据 `flag` 参数决定处理类型：
   - `flag = 0`: 计算薪资明细数据
     - 如果提供了 `dateStr` 且当前日期满足发放条件，会自动执行发放单生成逻辑
     - 发放条件判断基于薪资规则的自动创建发放单设置
   - `flag = 1`: 生成薪资发放单
     - 如果已在 `flag=0` 阶段处理过且满足条件，则跳过处理避免重复

3. **发放条件判断**: 系统根据薪资规则配置判断是否需要生成发放单：
   - **自动创建发放单**: 必须开启此选项
   - **次日发放** (`DISTRIBUTION_TIME=1`): 每天都满足条件
   - **次周发放** (`DISTRIBUTION_TIME=2`): 仅在设定的星期几且为周一时满足
   - **次月发放** (`DISTRIBUTION_TIME=3`): 仅在设定的日期且为月初时满足

4. **异常处理**: 如果处理过程中出现异常，会返回失败响应并包含具体错误信息

## 注意事项

1. **日期格式**: 所有日期参数必须为 `yyyy-MM-dd` 格式
2. **参数一致性**: `tenantId` 和 `x-fs-ei` 应保持一致
3. **薪资规则**: `salaryRuleId` 必须是系统中存在的有效薪资规则ID
4. **性能考虑**: 该接口为同步接口，处理时间可能较长，建议设置合适的超时时间
5. **系统用户**: 系统用户调用时使用 `x-fs-userInfo: -10000`
6. **智能处理**: 当 `flag=0` 且提供 `dateStr` 时，系统会智能判断是否需要同时生成发放单
7. **重复处理**: `flag=1` 会检查是否已在 `flag=0` 阶段处理过，避免重复生成
8. **日志查看**: 如有问题可通过 `service.log` 查看 `SalaryService` 或 `PMMSalaryConsumer` 相关日志

## 发放条件详细说明

系统根据薪资规则的以下字段判断是否满足发放单生成条件：

### 必要条件
- `AUTO_CREATED_PAYROLLVOUCHE = "true"` (开启自动创建发放单)

### 发放时机判断
- **次日发放** (`DISTRIBUTION_TIME = "1"`)
  - 每天都满足条件，会立即生成发放单

- **次周发放** (`DISTRIBUTION_TIME = "2"`)
  - 需要当前日期是周一 (`dayOfWeek = 1`)
  - 且匹配 `NEXT_WEEK_DISTRIBUTION_OP` 设置的星期几

- **次月发放** (`DISTRIBUTION_TIME = "3"`)
  - 需要当前日期是月初 (`dayOfMonth = 1`)
  - 且匹配 `NEXT_MONTH_DISTRIBUTION_OP` 设置的日期

### 示例场景
1. **日薪次日发放**: 每天执行 `flag=0` 都会生成发放单
2. **周薪次周发放**: 只有在周一且规则设置为周一发放时才生成
3. **月薪次月发放**: 只有在月初且规则设置为1号发放时才生成

## 相关接口

- 薪资规则查询接口
- 薪资明细查询接口
- 薪资发放单查询接口
