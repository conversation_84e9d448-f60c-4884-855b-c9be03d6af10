# SalaryRule SALARY_ITEM 数组支持改进

## 概述

根据 `SalaryRuleFields.SALARY_ITEM` 字段是数组类型的特性，我们改进了 `SalaryRuleAddAction` 和 `SalaryRuleEditAction` 中的验证逻辑，使其能够正确处理数组格式的工资项数据。

## 问题分析

### 原有实现的问题

**原始代码（存在问题）：**
```java
// 原有实现假设 SALARY_ITEM 是逗号分隔的字符串
Object salaryItems = objectData.get(SalaryRuleFields.SALARY_ITEM);
String[] itemIds = salaryItems.toString().split(",");
List<String> salaryItemIds = Lists.newArrayList(itemIds);
```

**问题：**
1. **数据类型假设错误**：假设 `SALARY_ITEM` 总是字符串格式
2. **数组处理不当**：当字段是数组时，`toString()` 会产生类似 `[Ljava.lang.String;@hashcode` 的结果
3. **解析失败**：无法正确解析数组中的实际工资项ID
4. **验证失效**：导致工资项存在性验证和匹配性验证失败

## 改进方案

### 1. 新增 `parseSalaryItemIds` 方法

我们在两个 Action 类中都添加了通用的解析方法：

```java
/**
 * 解析工资项ID列表，支持多种数据格式
 * 
 * @param salaryItems 工资项数据，可能是数组、List或逗号分隔的字符串
 * @return 工资项ID列表
 */
private List<String> parseSalaryItemIds(Object salaryItems) {
    List<String> result = new ArrayList<>();
    
    if (salaryItems == null) {
        return result;
    }
    
    try {
        if (salaryItems instanceof List) {
            // 处理 List 类型
            List<?> itemList = (List<?>) salaryItems;
            for (Object item : itemList) {
                if (item != null && StringUtils.isNotBlank(item.toString())) {
                    result.add(item.toString().trim());
                }
            }
        } else if (salaryItems instanceof Object[]) {
            // 处理数组类型
            Object[] itemArray = (Object[]) salaryItems;
            for (Object item : itemArray) {
                if (item != null && StringUtils.isNotBlank(item.toString())) {
                    result.add(item.toString().trim());
                }
            }
        } else if (salaryItems instanceof String) {
            // 处理字符串类型（逗号分隔）
            String itemsStr = (String) salaryItems;
            if (StringUtils.isNotBlank(itemsStr)) {
                String[] itemIds = itemsStr.split(",");
                for (String itemId : itemIds) {
                    if (StringUtils.isNotBlank(itemId)) {
                        result.add(itemId.trim());
                    }
                }
            }
        } else {
            // 其他类型的兜底处理
            String itemsStr = salaryItems.toString();
            if (StringUtils.isNotBlank(itemsStr)) {
                if (itemsStr.contains(",")) {
                    String[] itemIds = itemsStr.split(",");
                    for (String itemId : itemIds) {
                        if (StringUtils.isNotBlank(itemId)) {
                            result.add(itemId.trim());
                        }
                    }
                } else {
                    result.add(itemsStr.trim());
                }
            }
        }
        
        log.debug("解析工资项ID列表: 原始数据类型={}, 解析结果={}", 
                salaryItems.getClass().getSimpleName(), result);
        
    } catch (Exception e) {
        log.error("解析工资项ID列表失败: {}", salaryItems, e);
        throw new ValidateException("工资项数据格式错误"); //ignoreI18n
    }
    
    return result;
}
```

### 2. 支持的数据格式

新的解析方法支持以下数据格式：

1. **数组格式**：`String[]` 或 `Object[]`
   ```java
   String[] items = {"item1", "item2", "item3"};
   ```

2. **List格式**：`List<String>` 或 `List<Object>`
   ```java
   List<String> items = Arrays.asList("item1", "item2", "item3");
   ```

3. **字符串格式**：逗号分隔的字符串（向后兼容）
   ```java
   String items = "item1,item2,item3";
   ```

4. **单个值**：单个工资项ID
   ```java
   String item = "item1";
   ```

### 3. 改进的验证逻辑

**SalaryRuleAddAction 中的改进：**
```java
// 4. 验证工资项是否存在且与定薪方式匹配
Object salaryItems = objectData.get(SalaryRuleFields.SALARY_ITEM);
if (salaryItems == null) {
    throw new ValidateException("请选择工资项"); //ignoreI18n
}

// 解析工资项ID列表（支持数组和字符串格式）
List<String> salaryItemIds = parseSalaryItemIds(salaryItems);

if (salaryItemIds.isEmpty()) {
    throw new ValidateException("请选择工资项"); //ignoreI18n
}

List<IObjectData> salaryItemObjs = salaryItemDao.getbyIds(actionContext.getTenantId(), salaryItemIds);

if (salaryItemObjs.size() != salaryItemIds.size()) {
    throw new ValidateException("部分薪资项目不存在"); //ignoreI18n
}

// 验证工资项的定薪方式是否与规则的定薪方式匹配
for (IObjectData salaryItemObj : salaryItemObjs) {
    ObjectDataExt salaryItemObjectData = ObjectDataExt.of(salaryItemObj);
    String itemSalaryMethod = salaryItemObjectData.getStringValue(SalaryItemFields.SALARY_METHOD);
    if (!salaryMethod.equals(itemSalaryMethod)) {
        throw new ValidateException("工资项[" + salaryItemObj.getName() + "]的定薪方式与规则不匹配"); //ignoreI18n
    }
}
```

**SalaryRuleEditAction 中的改进：**
```java
// 4. 验证工资项是否存在且与定薪方式匹配
Object salaryItems = objectData.get(SalaryRuleFields.SALARY_ITEM);
if (salaryItems != null) {
    // 解析工资项ID列表（支持数组和字符串格式）
    List<String> salaryItemIds = parseSalaryItemIds(salaryItems);
    
    if (!salaryItemIds.isEmpty()) {
        List<IObjectData> salaryItemObjs = salaryItemDao.getbyIds(actionContext.getTenantId(), salaryItemIds);
        
        if (salaryItemObjs.size() != salaryItemIds.size()) {
            throw new ValidateException("部分薪资项目不存在"); //ignoreI18n
        }
        
        // 验证工资项的定薪方式是否与规则的定薪方式匹配
        for (IObjectData salaryItemObj : salaryItemObjs) {
            ObjectDataExt salaryItemObjectData = ObjectDataExt.of(salaryItemObj);
            String itemSalaryMethod = salaryItemObjectData.getStringValue(SalaryItemFields.SALARY_METHOD);
            if (!salaryMethod.equals(itemSalaryMethod)) {
                throw new ValidateException("工资项[" + salaryItemObj.getName() + "]的定薪方式与规则不匹配"); //ignoreI18n
            }
        }
    }
}
```

## 改进效果

### 1. 兼容性
- **向后兼容**：仍然支持原有的逗号分隔字符串格式
- **向前兼容**：正确支持数组和List格式

### 2. 健壮性
- **类型安全**：通过 `instanceof` 检查确保类型安全
- **异常处理**：提供详细的错误信息和日志记录
- **空值处理**：正确处理 null 和空值情况

### 3. 可维护性
- **代码复用**：两个 Action 使用相同的解析逻辑
- **日志记录**：提供调试级别的日志，便于问题排查
- **清晰逻辑**：代码结构清晰，易于理解和维护

## 测试建议

建议对以下场景进行测试：

1. **数组格式输入**：`["item1", "item2", "item3"]`
2. **List格式输入**：`List.of("item1", "item2", "item3")`
3. **字符串格式输入**：`"item1,item2,item3"`
4. **单个值输入**：`"item1"`
5. **空值输入**：`null`, `[]`, `""`
6. **异常格式输入**：验证错误处理逻辑

## 总结

通过这次改进，我们解决了 `SalaryRuleFields.SALARY_ITEM` 数组字段处理的问题，使得工资规则的验证逻辑能够正确处理各种数据格式，提高了系统的健壮性和兼容性。
