# Redis 分布式锁在多集群环境下的问题分析

## 当前实现的问题

### 1. 脑裂问题（Split-Brain）

**问题描述：**
```java
// 当前实现只在单个 Redis 节点上设置锁
String result = redisCmd.set(lockKey, "1", SetParams.setParams().nx().ex(LOCK_EXPIRE_TIME));
```

**风险场景：**
- Redis 主从集群中，主节点宕机
- 锁信息可能还没同步到从节点
- 从节点提升为主节点后，锁丢失
- 多个客户端可能同时获得锁

### 2. 网络分区问题

**问题描述：**
- 网络分区导致客户端连接到不同的 Redis 节点
- 每个分区都可能认为自己拥有锁
- 违反了分布式锁的互斥性

### 3. 时钟偏移问题

**问题描述：**
- 不同服务器时钟不同步
- 锁的过期时间计算不准确
- 可能导致锁提前或延迟释放

## Redisson 的优势

### 1. Redlock 算法

**核心思想：**
- 在多个独立的 Redis 实例上获取锁
- 只有在大多数节点（N/2+1）上成功获取锁才算成功
- 即使部分节点失败，仍能保证锁的正确性

```java
// Redisson 实现示例
RedissonClient redisson = Redisson.create(config);
RLock lock = redisson.getLock("salary-schedule-lock");

try {
    // 尝试获取锁，最多等待100秒，锁定10秒后自动释放
    boolean acquired = lock.tryLock(100, 10, TimeUnit.SECONDS);
    if (acquired) {
        // 执行业务逻辑
        processSalaryRules();
    }
} finally {
    if (lock.isHeldByCurrentThread()) {
        lock.unlock();
    }
}
```

### 2. 多节点锁定

```java
// 在多个 Redis 实例上同时获取锁
RLock lock1 = redisson1.getLock("myLock");
RLock lock2 = redisson2.getLock("myLock");
RLock lock3 = redisson3.getLock("myLock");

RedissonRedLock redLock = new RedissonRedLock(lock1, lock2, lock3);
try {
    boolean acquired = redLock.tryLock(100, 10, TimeUnit.SECONDS);
    if (acquired) {
        // 只有在大多数节点上成功获取锁才会执行
    }
} finally {
    redLock.unlock();
}
```

### 3. 看门狗机制（Watchdog）

**自动续期：**
```java
RLock lock = redisson.getLock("myLock");
lock.lock(); // 默认30秒过期，每10秒自动续期

try {
    // 长时间业务逻辑，锁会自动续期
    longRunningTask();
} finally {
    lock.unlock();
}
```

**优势：**
- 防止业务执行时间超过锁过期时间
- 自动检测客户端是否存活
- 客户端宕机时停止续期，锁自然过期

### 4. 公平锁和读写锁

```java
// 公平锁 - 按请求顺序获取锁
RLock fairLock = redisson.getFairLock("fairLock");

// 读写锁 - 支持多读单写
RReadWriteLock rwLock = redisson.getReadWriteLock("rwLock");
RLock readLock = rwLock.readLock();
RLock writeLock = rwLock.writeLock();
```

## 改进建议

### 1. 短期改进（基于当前架构）

```java
// 增加重试机制和唯一标识
private boolean acquireLockWithRetry(String lockKey) {
    String lockValue = generateUniqueLockValue();
    int maxRetries = 3;
    
    for (int i = 0; i < maxRetries; i++) {
        if (tryAcquireLock(lockKey, lockValue)) {
            return true;
        }
        // 指数退避重试
        sleep(100 * (1 << i));
    }
    return false;
}

// 安全释放锁（使用 Lua 脚本）
private void safeReleaseLock(String lockKey, String lockValue) {
    String luaScript = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";
    
    redisCmd.eval(luaScript, Arrays.asList(lockKey), Arrays.asList(lockValue));
}
```

### 2. 长期改进（推荐方案）

**引入 Redisson：**

```xml
<dependency>
    <groupId>org.redisson</groupId>
    <artifactId>redisson</artifactId>
    <version>3.20.1</version>
</dependency>
```

**配置多节点 Redis：**

```java
@Configuration
public class RedissonConfig {
    
    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        
        // 配置多个 Redis 节点
        config.useClusterServers()
            .addNodeAddress("redis://127.0.0.1:7000")
            .addNodeAddress("redis://127.0.0.1:7001")
            .addNodeAddress("redis://127.0.0.1:7002");
            
        return Redisson.create(config);
    }
}
```

**使用 Redisson 分布式锁：**

```java
@Service
public class SalaryScheduleService {
    
    @Autowired
    private RedissonClient redissonClient;
    
    public void processSalaryRulesWithRedLock(String tenantId, int flag, LocalDate date) {
        String lockKey = "salary-schedule-" + tenantId + "-" + flag + "-" + date;
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            // 尝试获取锁，最多等待10秒，锁定30分钟
            boolean acquired = lock.tryLock(10, 1800, TimeUnit.SECONDS);
            if (acquired) {
                // 执行业务逻辑
                pmmSalaryProvider.processSalaryRulesForTenant(tenantId, flag, date);
            } else {
                log.info("获取锁失败，任务可能正在其他节点执行: {}", lockKey);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("获取锁被中断: {}", lockKey);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

## 总结

### 当前实现的局限性：
1. **单点故障**：依赖单个 Redis 节点
2. **脑裂风险**：主从切换时可能丢失锁
3. **网络分区**：无法处理网络分区场景
4. **时钟依赖**：依赖服务器时钟同步

### Redisson 的优势：
1. **高可用**：Redlock 算法支持多节点
2. **容错性**：部分节点失败不影响锁的正确性
3. **自动续期**：看门狗机制防止锁意外过期
4. **丰富功能**：公平锁、读写锁、信号量等

### 建议：
- **生产环境**：使用 Redisson + Redlock 算法
- **开发环境**：可以使用改进的单节点锁
- **监控告警**：添加锁获取失败的监控和告警
- **降级策略**：锁获取失败时的业务降级方案
