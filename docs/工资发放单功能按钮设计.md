# 工资发放单功能按钮设计

## 概述

为工资发放单新增两个重要的功能按钮：【更新工资数据】和【下发工资条】，以支持工资数据的重新计算和工资条的下发流程。

## 1. 【更新工资数据】按钮

### 基本信息
- **按钮名称**：更新工资数据
- **功能描述**：工资发放单下发前，若修正/调整了工资条明细数据，则可操作"更新工资数据"，系统将重新计算工资条数据
- **显示位置**：详情页
- **显示条件**：工资发放单生成后显示该按钮
- **隐藏条件**：当工资发放单.发放状态=已发放后，该按钮隐藏

### 实现类
```java
SalaryPaymentSlipUpdateSalaryDataAction
```

### 核心逻辑
1. **验证发放状态**：只有未发放和发放中状态才能更新工资数据
2. **重新计算工资数据**：调用 `SalaryService.recalculateSalaryDataForPaymentSlip()` 方法
3. **更新工资条总额**：重新计算所有工资条的总金额
4. **记录操作日志**：记录更新操作的详细信息

### 验证规则
```java
// 验证发放状态
private void validatePayStatus(IObjectData salaryPaymentSlip) {
    String payStatus = salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class);
    
    // 已发放状态不能更新数据
    if (SalaryPaymentSlipFields.PAY_STATUS_Options_4.equals(payStatus)) {
        throw new ValidateException("工资发放单已发放，不能更新工资数据");
    }
}
```

### 参数定义
```java
public static class Arg extends FmcgPreActionArgs {
    private Boolean forceUpdate = false;    // 是否强制更新（可选）
    private String updateReason;            // 更新说明（可选）
}
```

### 返回结果
```java
public static class Result {
    private boolean success;                // 是否成功
    private String message;                 // 消息
    private int updatedSalaryDataCount;     // 更新的工资条数量
    private int updatedDetailCount;         // 更新的明细数量
}
```

## 2. 【下发工资条】按钮

### 基本信息
- **按钮名称**：下发工资条
- **功能描述**：下发工资条，让员工查看到对应的工资数据
- **显示位置**：列表页、详情页
- **显示条件**：工资发放单数据生成后，显示该按钮

### 实现类
```java
SalaryPaymentSlipDistributeAction
```

### 核心逻辑
1. **更新发放状态**：将发放状态更新为"发放中"
2. **获取员工列表**：从工资条数据中获取需要通知的员工
3. **发送通知消息**：给员工推送消息提醒
4. **区分通知方式**：
   - 内部用户：使用CRM通知
   - 外部用户：使用互联通知公告

### 验证规则
```java
// 验证下发状态
private void validateDistributeStatus(IObjectData salaryPaymentSlip) {
    String payStatus = salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class);
    
    // 只有生成完成状态才能下发
    if (!SalaryPaymentSlipFields.PAY_STATUS_Options_1.equals(payStatus)) {
        throw new ValidateException("只有生成完成的工资发放单才能下发工资条");
    }
}

// 验证是否存在工资条数据
private void validateSalaryDataExists(String tenantId, String salaryPaymentSlipId) {
    List<IObjectData> salaryDataList = salaryDataDao.getBySalaryPaymentSlipId(tenantId, salaryPaymentSlipId);
    
    if (CollectionUtils.isEmpty(salaryDataList)) {
        throw new ValidateException("该工资发放单下没有工资条数据，无法下发");
    }
}
```

### 通知消息格式
```java
// 构建通知消息
String notificationMessage = String.format("请查收%s的工资条", payPeriod);

// 发薪期间格式化示例
// 单日：2024年01月15日
// 多日：2024年01月01日至2024年01月31日
```

### 参数定义
```java
public static class Arg extends FmcgPreActionArgs {
    private Boolean sendSmsNotification = false;  // 是否发送短信通知（可选）
    private String customMessage;                 // 自定义通知消息（可选）
}
```

### 返回结果
```java
public static class Result {
    private boolean success;                // 是否成功
    private String message;                 // 消息
    private int notifiedEmployeeCount;      // 通知的员工数量
    private Date distributeTime;            // 下发时间
}
```

## 3. 工资通知服务

### 服务类
```java
SalaryNotificationService
```

### 核心功能
1. **批量通知处理**：支持大量员工的通知发送
2. **异步处理**：使用异步方式提高处理效率
3. **通知方式区分**：
   - 内部员工：CRM通知
   - 外部员工：互联通知公告
4. **错误处理**：单个员工通知失败不影响其他员工

### 通知流程
```java
public int sendSalaryNotification(String tenantId, List<String> employeeIds, 
                                String message, String salaryPaymentSlipId) {
    // 1. 分批处理员工通知（每批50个）
    // 2. 异步处理每批员工
    // 3. 根据员工类型选择通知方式
    // 4. 记录成功和失败的数量
    // 5. 返回成功通知的数量
}
```

## 4. 工资条查看规则

### 查看权限
- **普通员工**：只能查看自己的工资数据
- **管理员**：可以查看所有员工的工资数据

### 查看条件
- **数据发放状态**：已发放
- **生命状态**：正常

### 日工资支持
- **适用条件**：工资规则中"定薪方式=日薪"
- **数据来源**：工资条明细
- **默认展示**：当工资条未下发时，默认展示日工资

## 5. 状态流转

### 工资发放单状态
```
正在生成 → 生成完成 → 发放中 → 已发放
    ↓           ↓         ↓        ↓
   不可操作   可更新数据  可下发   不可操作
```

### 按钮显示逻辑
```java
// 更新工资数据按钮
if (payStatus != PAY_STATUS_Options_4) {  // 非已发放状态
    showUpdateButton = true;
}

// 下发工资条按钮  
if (payStatus == PAY_STATUS_Options_1) {  // 生成完成状态
    showDistributeButton = true;
}
```

## 6. 错误处理

### 常见错误场景
1. **工资发放单不存在**
2. **发放状态不符合操作条件**
3. **没有工资条数据**
4. **员工信息不完整**
5. **通知服务异常**

### 错误处理策略
- **验证失败**：抛出 `ValidateException` 并提供明确的错误信息
- **部分失败**：记录失败详情，返回成功处理的数量
- **系统异常**：记录详细日志，提供用户友好的错误提示

## 7. 性能优化

### 批量处理
- **员工通知**：每批50个员工，避免单次处理过多数据
- **异步处理**：使用 `CompletableFuture` 提高并发处理能力

### 缓存策略
- **员工信息**：缓存员工基本信息，减少数据库查询
- **工资规则**：缓存工资规则配置，提高计算效率

## 8. 监控和日志

### 操作日志
- **更新工资数据**：记录更新的工资条数量和明细数量
- **下发工资条**：记录通知的员工数量和成功率

### 性能监控
- **处理时间**：监控每个操作的执行时间
- **成功率**：监控通知发送的成功率
- **错误统计**：统计各类错误的发生频率

## 总结

通过新增这两个功能按钮，工资发放单的功能更加完善：

1. **【更新工资数据】**：解决了工资条明细修正后需要重新计算总额的问题
2. **【下发工资条】**：提供了统一的工资条下发和通知机制

这些功能的实现遵循了系统的设计原则，具有良好的扩展性和维护性。
