# 工资项公式描述功能说明

## 功能概述

在工资项详情页中，当工资项的取值方式为"计算公式"时，系统会自动重写`calculation_formula_desc`（公式描述）字段，将原始计算公式格式化为易读的中文描述，帮助用户更好地理解公式内容。

## 主要特性

### 1. 条件触发
- **仅在计算公式类型时生效**：只有当取值方式为"计算公式"（VALUE_TYPE_Options_2）时才会重写字段
- **固定值类型不处理**：取值方式为"固定值"的工资项不会生成公式描述
- **自动执行**：在工资项详情页加载时自动执行，无需用户操作

### 2. 纯公式格式化
- **仅转换公式内容**：只对计算公式本身进行中文格式化，不包含其他属性信息
- **操作符中文化**：将数学操作符转换为中文表达
- **特殊字符处理**：参考SalaryServiceImpl的formatFormula逻辑

### 3. 智能公式格式化
- 将数学操作符转换为中文：+ → 加，- → 减，* → 乘以，/ → 除以
- 将英文括号转换为中文括号：() → （）
- 处理特殊字符：&& 和 & 的转换逻辑
- 自动处理过长公式的截断显示（超过200字符）

## 描述格式

### 基本格式
```
[格式化后的公式]
```

### 示例输出

#### 示例1：固定值工资项（不处理）
**工资项配置**：
- 取值方式：固定值
- 计算公式：5000

**结果**：
```
不会生成公式描述（因为不是计算公式类型）
```

#### 示例2：绩效奖金（计算公式类型）
**工资项配置**：
- 取值方式：计算公式
- 计算公式：base_salary * 0.2 + $kpi_ext_12345$ * 100
- 包含KPI：销售业绩指标（ID: 12345）

**生成的描述**：
```
base_salary 乘以 0.2 加 销售业绩指标 乘以 100
```

#### 示例3：社保扣款（计算公式类型）
**工资项配置**：
- 取值方式：计算公式
- 计算公式：(base_salary + allowance) * 0.08

**生成的描述**：
```
（base_salary 加 allowance） 乘以 0.08
```

#### 示例4：复杂计算公式
**工资项配置**：
- 取值方式：计算公式
- 计算公式：(base_salary * 1.2 + performance_bonus - deduction) / working_days * actual_days

**生成的描述**：
```
（base_salary 乘以 1.2 加 performance_bonus 减 deduction） 除以 working_days 乘以 actual_days
```

#### 示例5：包含KPI和特殊字符的公式
**工资项配置**：
- 取值方式：计算公式
- 计算公式：base_salary & $kpi_ext_67890$ && overtime_hours
- 包含KPI：加班效率指标（ID: 67890）

**生成的描述**：
```
base_salary 加 加班效率指标 && overtime_hours
```

## 触发条件

### 取值方式判断
| 字段值 | 字段名称 | 是否处理 | 说明 |
|--------|----------|----------|------|
| 1 | VALUE_TYPE_Options_1 | ❌ 不处理 | 固定值类型，无需公式描述 |
| 2 | VALUE_TYPE_Options_2 | ✅ 处理 | 计算公式类型，生成公式描述 |
| 其他 | 未知类型 | ❌ 不处理 | 未知类型，跳过处理 |

## 格式化规则

### 公式操作符映射
| 原始符号 | 中文描述 |
|----------|----------|
| + | 加 |
| - | 减 |
| * | 乘以 |
| / | 除以 |
| ( | （ |
| ) | ） |

### 特殊字符处理
| 原始字符 | 处理步骤 | 最终结果 |
|----------|----------|----------|
| && | 保持不变 | && |
| & | 转换为 + | 加 |
| &（在&&中） | 不转换 | && |

## 技术实现

### 1. 控制器实现
```java
@Slf4j
public class SalaryItemWebDetailController extends StandardWebDetailController {

  @Override
  protected Result after(Arg arg, Result result) {
    ObjectDataExt salaryItemExt = ObjectDataExt.of(result.getData());

    // 只有在取值方式为"计算公式"时才重写公式描述字段
    String valueType = salaryItemExt.get(SalaryItemFields.VALUE_TYPE, String.class);
    if (SalaryItemFields.VALUE_TYPE_Options_2.equals(valueType)) {
      String formulaDesc = buildFormulaDescription(salaryItemExt);
      if (StringUtils.isNotBlank(formulaDesc)) {
        result.getData().put(SalaryItemFields.CALCULATION_FORMULA_DESC, formulaDesc);
        log.info("工资项公式描述已重写: {}", formulaDesc);
      }
    }

    return result;
  }
}
```

### 2. 描述构建逻辑
```java
private String buildFormulaDescription(ObjectDataExt salaryItemExt) {
  // 获取计算公式
  String calculationFormula = salaryItemExt.get(SalaryItemFields.CALCULATION_FORMULA, String.class);

  if (StringUtils.isBlank(calculationFormula)) {
    return null;
  }

  // 构造extDataNameMap，用于变量名到实际名称的映射
  Map<String, Object> extDataNameMap = buildExtDataNameMap(salaryItemExt);

  // 使用SalaryServiceImpl的formatFormula逻辑进行变量替换
  String formattedFormula = formatFormula(calculationFormula, extDataNameMap);

  // 再进行中文操作符转换
  return formatCalculationFormula(formattedFormula);
}
```

### 3. 公式格式化（参考SalaryServiceImpl逻辑）
```java
private String formatCalculationFormula(String calculationFormula) {
  String formattedFormula = calculationFormula.trim();

  // 参考SalaryExpressionCalcServiceImpl.formatExpression的逻辑
  // 替换特殊字符处理
  formattedFormula = formattedFormula.replace("&&", "$$") // 防止&& 一同被转换的
          .replace("&", "+").replace("$$", "&&");

  // 替换操作符为中文
  formattedFormula = formattedFormula.replaceAll("\\+", " 加 ");
  formattedFormula = formattedFormula.replaceAll("-", " 减 ");
  formattedFormula = formattedFormula.replaceAll("\\*", " 乘以 ");
  formattedFormula = formattedFormula.replaceAll("/", " 除以 ");
  formattedFormula = formattedFormula.replaceAll("\\(", "（");
  formattedFormula = formattedFormula.replaceAll("\\)", "）");

  // 清理空格并截断
  formattedFormula = formattedFormula.replaceAll("\\s+", " ").trim();
  if (formattedFormula.length() > 200) {
    formattedFormula = formattedFormula.substring(0, 197) + "...";
  }

  return formattedFormula;
}
```

### 4. 扩展数据名称映射构建
```java
private Map<String, Object> buildExtDataNameMap(ObjectDataExt salaryItemExt) {
  Map<String, Object> extDataNameMap = Maps.newHashMap();

  String tenantId = salaryItemExt.getTenantId();

  // 获取工资项中包含的KPI指标
  List<String> kpiIds = salaryItemExt.getDimensionValues(SalaryItemFields.FORMULA_INCLUDES_KPI);

  if (CollectionUtils.isNotEmpty(kpiIds)) {
    SalaryKPIDao salaryKPIDao = SpringUtil.getContext().getBean(SalaryKPIDao.class);
    List<IObjectData> kpiObjs = salaryKPIDao.getbyKpiIds(tenantId, kpiIds);

    for (IObjectData kpiObj : kpiObjs) {
      // 构造KPI变量名映射：kpi_ext_ID -> KPI名称
      String key = "kpi_ext_" + kpiObj.getId();
      extDataNameMap.put(key, kpiObj.getName());
    }
  }

  return extDataNameMap;
}
```

### 5. 变量替换逻辑
```java
private String formatFormula(String formulaStr, Map<String, Object> extDataNameMap) {
  if (formulaStr == null || extDataNameMap == null) {
    return formulaStr;
  }

  for (Map.Entry<String, Object> entry : extDataNameMap.entrySet()) {
    // 将 $kpi_ext_12345$ 替换为实际的KPI名称
    String key = Pattern.quote("$" + entry.getKey() + "$");
    String value = entry.getValue() != null ? entry.getValue().toString() : "";
    formulaStr = formulaStr.replaceAll(key, value);
  }
  return formulaStr;
}
```

## 使用场景

### 1. 公式理解
- 帮助用户快速理解复杂的计算公式
- 将技术性的公式表达转换为易懂的中文描述
- 减少公式理解的学习成本

### 2. 配置验证
- 在工资项配置完成后，通过中文描述验证公式是否正确
- 便于发现公式配置错误
- 提高配置的准确性

### 3. 问题排查
- 当工资计算出现问题时，通过中文公式描述快速定位问题
- 便于理解公式的计算逻辑
- 提高问题排查效率

### 4. 培训和文档
- 为新用户提供直观的公式说明
- 减少技术门槛，提高系统易用性
- 便于制作培训材料和用户手册

## 注意事项

### 1. 性能考虑
- 只在详情页显示时执行，不影响列表页性能
- 格式化逻辑简单高效，不会造成明显延迟
- 异常情况下不影响页面正常显示

### 2. 数据一致性
- 描述基于实时数据生成，确保准确性
- 不存储生成的描述，避免数据不一致
- 每次访问详情页都会重新生成

### 3. 条件处理
- 只处理计算公式类型的工资项
- 固定值类型不会生成描述，避免无意义的处理
- 确保功能的针对性和有效性

### 4. 兼容性
- 异常情况下返回原始公式，不影响页面显示
- 向后兼容现有的工资项配置
- 对空公式或无效公式进行安全处理

## 日志记录

系统会记录详细的操作日志：

```
SalaryItemWebDetailController after method called
添加KPI变量映射: kpi_ext_12345 -> 销售业绩指标
构建extDataNameMap完成，映射数量: 1
构建的工资项公式描述: base_salary 乘以 0.2 加 销售业绩指标 乘以 100
工资项公式描述已重写: base_salary 乘以 0.2 加 销售业绩指标 乘以 100
```

异常情况日志：
```
重写工资项公式描述时发生异常
构建extDataNameMap失败
格式化计算公式失败: invalid_formula
```

条件不满足日志：
```
工资项计算公式为空，无法生成公式描述
```

## 扩展建议

### 1. 增加更多属性
- 舍位方式描述
- 小数位数描述
- 启用状态描述

### 2. 支持多语言
- 根据用户语言设置显示对应语言的描述
- 支持英文、中文等多种语言

### 3. 自定义格式
- 允许用户自定义描述格式
- 支持模板化的描述生成

### 4. 智能建议
- 根据工资项配置提供优化建议
- 检测潜在的配置问题并给出提示
