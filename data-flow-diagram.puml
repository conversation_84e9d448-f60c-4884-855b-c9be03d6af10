@startuml fs-crm-fmcg-wq数据流图

!theme plain
skinparam backgroundColor #FFFFFF

title fs-crm-fmcg-wq 系统数据流图

skinparam rectangle {
  BackgroundColor LightBlue
  BorderColor Blue
}

skinparam database {
  BackgroundColor LightGreen
  BorderColor Green
}

skinparam cloud {
  BackgroundColor LightYellow
  BorderColor Orange
}

rectangle "用户界面层" {
  [Web前端] as WebUI
  [移动端] as MobileUI
  [管理后台] as AdminUI
}

rectangle "控制器层" {
  [供货管理Controller] as SupplyController
  [办公排班Controller] as OfficeController
  [薪资管理Controller] as SalaryController
  [报表查询Controller] as ReportController
}

rectangle "业务服务层" {
  [供货业务服务] as SupplyService
  [排班业务服务] as OfficeService
  [薪资业务服务] as SalaryService
  [报表业务服务] as ReportService
  [同步服务] as SyncService
  [审批服务] as ApprovalService
}

rectangle "数据访问层" {
  [供货数据DAO] as SupplyDAO
  [办公数据DAO] as OfficeDAO
  [薪资数据DAO] as SalaryDAO
  [报表数据DAO] as ReportDAO
  [基础数据DAO] as BaseDAO
}

database "数据存储层" {
  database "PostgreSQL\n(主数据库)" as PostgreSQL
  database "MongoDB\n(文档数据库)" as MongoDB
  database "Redis\n(缓存数据库)" as Redis
}

cloud "外部服务" {
  cloud "PaaS平台服务" as PaasService
  cloud "考勤服务" as CheckinService
  cloud "消息队列" as MessageQueue
  cloud "文件存储" as FileStorage
  cloud "通知服务" as NotificationService
}

' 用户请求数据流
WebUI --> SupplyController : HTTP请求\n(供货管理)
MobileUI --> OfficeController : HTTP请求\n(排班管理)
AdminUI --> SalaryController : HTTP请求\n(薪资管理)
AdminUI --> ReportController : HTTP请求\n(报表查询)

' 控制器到服务层数据流
SupplyController --> SupplyService : 业务请求数据
OfficeController --> OfficeService : 排班数据
SalaryController --> SalaryService : 薪资数据
ReportController --> ReportService : 报表查询参数

' 服务层内部数据流
SupplyService --> SyncService : 同步请求数据
SupplyService --> ApprovalService : 审批流程数据
OfficeService --> SyncService : 排班同步数据
SalaryService --> ReportService : 薪资统计数据

' 服务层到数据访问层数据流
SupplyService --> SupplyDAO : 供货关系数据
OfficeService --> OfficeDAO : 排班数据
SalaryService --> SalaryDAO : 薪资计算数据
ReportService --> ReportDAO : 报表原始数据
SupplyService --> BaseDAO : 通用数据操作
OfficeService --> BaseDAO : 通用数据操作

' 数据访问层到存储层数据流
SupplyDAO --> PostgreSQL : 供货关系\n经销商信息\n产品信息
OfficeDAO --> PostgreSQL : 用户排班\n班次信息\n考勤数据
SalaryDAO --> PostgreSQL : 薪资记录\n计算结果\n支付信息
ReportDAO --> PostgreSQL : 报表数据\n统计结果
BaseDAO --> MongoDB : 元数据\n配置信息\n日志数据
BaseDAO --> Redis : 缓存数据\n会话信息\n临时数据

' 外部服务数据流
SyncService --> CheckinService : 考勤数据同步
ApprovalService --> MessageQueue : 审批消息
SalaryService --> NotificationService : 薪资通知
ReportService --> FileStorage : 报表文件
SupplyService --> PaasService : 元数据查询
OfficeService --> PaasService : 权限验证

' 反向数据流（响应）
PostgreSQL --> SupplyDAO : 查询结果
MongoDB --> BaseDAO : 文档数据
Redis --> BaseDAO : 缓存数据
CheckinService --> SyncService : 同步结果
MessageQueue --> ApprovalService : 审批状态
NotificationService --> SalaryService : 发送状态
FileStorage --> ReportService : 文件URL
PaasService --> SupplyService : 元数据
PaasService --> OfficeService : 权限信息

SupplyDAO --> SupplyService : 业务数据对象
OfficeDAO --> OfficeService : 排班数据对象
SalaryDAO --> SalaryService : 薪资数据对象
ReportDAO --> ReportService : 报表数据对象
BaseDAO --> SupplyService : 通用数据对象
BaseDAO --> OfficeService : 通用数据对象

SupplyService --> SupplyController : 处理结果
OfficeService --> OfficeController : 处理结果
SalaryService --> SalaryController : 处理结果
ReportService --> ReportController : 处理结果

SupplyController --> WebUI : JSON响应
OfficeController --> MobileUI : JSON响应
SalaryController --> AdminUI : JSON响应
ReportController --> AdminUI : JSON响应

note top of WebUI
  **数据流特点:**
  1. 请求-响应模式
  2. 分层数据传递
  3. 异步数据同步
  4. 缓存数据优化
  5. 外部服务集成
end note

note right of PostgreSQL
  **主要存储数据:**
  - 供货关系数据
  - 用户排班数据
  - 薪资计算数据
  - 报表统计数据
  - 业务流程数据
end note

note right of MongoDB
  **文档存储数据:**
  - 系统配置信息
  - 元数据定义
  - 操作日志记录
  - 临时业务数据
end note

note right of Redis
  **缓存存储数据:**
  - 用户会话信息
  - 频繁查询数据
  - 计算中间结果
  - 分布式锁信息
end note

@enduml
