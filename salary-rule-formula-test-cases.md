# 工资规则计算公式测试用例

## 测试目的

验证SalaryRuleWebDetailController中计算公式字段的重写逻辑，确保工资项按照增减属性正确连接。

## 测试场景

### 场景1：第一个工资项是应发项

**输入数据**：
- 工资项1：基本工资（应发项）
- 工资项2：绩效奖金（应发项）  
- 工资项3：社保扣款（扣减项）

**预期输出**：
```
基本工资 + 绩效奖金 - 社保扣款
```

**说明**：第一个应发项不需要符号，后续应发项用+号，扣减项用-号

### 场景2：第一个工资项是扣减项

**输入数据**：
- 工资项1：社保扣款（扣减项）
- 工资项2：基本工资（应发项）
- 工资项3：绩效奖金（应发项）

**预期输出**：
```
- 社保扣款 + 基本工资 + 绩效奖金
```

**说明**：第一个扣减项需要减号，后续应发项用+号

### 场景3：全部是应发项

**输入数据**：
- 工资项1：基本工资（应发项）
- 工资项2：岗位津贴（应发项）
- 工资项3：绩效奖金（应发项）

**预期输出**：
```
基本工资 + 岗位津贴 + 绩效奖金
```

### 场景4：全部是扣减项

**输入数据**：
- 工资项1：社保扣款（扣减项）
- 工资项2：公积金扣款（扣减项）
- 工资项3：个税（扣减项）

**预期输出**：
```
- 社保扣款 - 公积金扣款 - 个税
```

### 场景5：混合顺序

**输入数据**：
- 工资项1：基本工资（应发项）
- 工资项2：社保扣款（扣减项）
- 工资项3：绩效奖金（应发项）
- 工资项4：个税（扣减项）
- 工资项5：交通补贴（应发项）

**预期输出**：
```
基本工资 - 社保扣款 + 绩效奖金 - 个税 + 交通补贴
```

### 场景6：只有一个工资项（应发项）

**输入数据**：
- 工资项1：基本工资（应发项）

**预期输出**：
```
基本工资
```

### 场景7：只有一个工资项（扣减项）

**输入数据**：
- 工资项1：社保扣款（扣减项）

**预期输出**：
```
- 社保扣款
```

## 关键逻辑验证

### 1. 第一个工资项的符号处理

```java
// 第一个工资项：如果是扣减项需要加减号，应发项不需要符号
if (SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
  formulaBuilder.append("- ").append(salaryItemName);
} else {
  formulaBuilder.append(salaryItemName);
}
```

### 2. 后续工资项的符号处理

```java
// 应发项使用+号，扣减项使用-号
if (SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1.equals(incrementDecrementAttrib)) {
  return "+";
} else if (SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
  return "-";
}
```

### 3. 符号前后的空格

- 所有操作符前后都有空格，确保可读性
- 格式：`工资项1 + 工资项2 - 工资项3`

## 边界情况

### 1. 空工资项列表
- 输入：[]
- 输出：null（不设置计算公式字段）

### 2. 工资项名称为空
- 跳过该工资项，继续处理下一个

### 3. 增减属性为空或未知
- 默认按应发项处理（使用+号）

### 4. 工资项不存在
- 跳过该工资项，记录警告日志

## 数据格式兼容性

### 1. List格式
```java
List<String> salaryItemIds = Arrays.asList("item1", "item2", "item3");
```

### 2. 数组格式
```java
String[] salaryItemIds = {"item1", "item2", "item3"};
```

### 3. 逗号分隔字符串格式
```java
String salaryItemIds = "item1,item2,item3";
```

## 实际使用示例

### 示例1：标准工资结构
```
基本工资 + 岗位津贴 + 绩效奖金 + 加班费 - 社保扣款 - 公积金扣款 - 个税
```

### 示例2：简单工资结构
```
基本工资 - 社保扣款
```

### 示例3：纯扣减开始
```
- 预扣税 + 基本工资 + 补贴
```

## 注意事项

1. **顺序重要性**：公式按照工资项在工资规则中的配置顺序生成
2. **符号规则**：第一个应发项无符号，第一个扣减项有减号
3. **空格格式**：操作符前后都有空格，提高可读性
4. **异常处理**：任何异常都不影响页面正常显示，只记录日志
5. **性能考虑**：只在详情页显示时重新计算，不影响数据存储
