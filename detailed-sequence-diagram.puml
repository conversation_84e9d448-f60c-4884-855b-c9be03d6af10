@startuml fs-crm-fmcg-wq详细调用序列图

!theme plain
skinparam backgroundColor #FFFFFF

title fs-crm-fmcg-wq 系统核心业务调用序列图

actor "用户" as User
participant "Web应用" as WebApp
participant "Controller" as Controller
participant "Service" as Service
participant "DAO" as DAO
participant "数据库" as DB
participant "外部服务" as External

== 系统初始化流程 ==
User -> WebApp: 启动应用
WebApp -> WebApp: 加载Spring配置
WebApp -> WQInitService: 初始化服务
WQInitService -> CheckinsDefaultObject: 初始化签到对象
WQInitService -> MCPreDefineObject: 初始化MC预定义对象
WQInitService -> OfficePreDefineObject: 初始化办公对象
WQInitService -> ReportDefaultObject: 初始化报表对象
WebApp --> User: 系统启动完成

== 供货关系管理流程 ==
User -> Controller: 创建供货关系请求
Controller -> SupplyService: 调用供货服务
SupplyService -> SupplyService: 业务逻辑验证
SupplyService -> SupplyDao: 查询现有数据
SupplyDao -> DB: 执行数据库查询
DB --> SupplyDao: 返回查询结果
SupplyDao --> SupplyService: 返回数据对象
SupplyService -> SupplyService: 冲突检测
SupplyService -> SyncAvailableRangeService: 同步可售范围
SyncAvailableRangeService -> SupplyDao: 更新相关数据
SupplyDao -> DB: 执行数据更新
DB --> SupplyDao: 更新确认
SupplyDao --> SyncAvailableRangeService: 返回结果
SyncAvailableRangeService --> SupplyService: 同步完成
SupplyService -> SupplyApproService: 触发审批流程
SupplyApproService -> External: 发送审批消息
External --> SupplyApproService: 审批结果
SupplyApproService --> SupplyService: 审批完成
SupplyService --> Controller: 返回处理结果
Controller --> User: 返回响应

== 用户排班管理流程 ==
User -> Controller: 用户排班请求
Controller -> OfficeShiftService: 调用排班服务
OfficeShiftService -> OfficeShiftService: 排班冲突检测
OfficeShiftService -> AccountShiftDao: 查询班次信息
AccountShiftDao -> DB: 查询班次数据
DB --> AccountShiftDao: 返回班次信息
AccountShiftDao --> OfficeShiftService: 返回班次对象
OfficeShiftService -> UserScheduleDao: 查询用户排班
UserScheduleDao -> DB: 查询排班数据
DB --> UserScheduleDao: 返回排班信息
UserScheduleDao --> OfficeShiftService: 返回排班对象
OfficeShiftService -> OfficeShiftService: 验证排班规则
OfficeShiftService -> UserScheduleDao: 保存排班数据
UserScheduleDao -> DB: 执行数据保存
DB --> UserScheduleDao: 保存确认
UserScheduleDao --> OfficeShiftService: 保存完成
OfficeShiftService -> CheckinsOfficeV2Service: 同步考勤数据
CheckinsOfficeV2Service -> External: 调用考勤服务
External --> CheckinsOfficeV2Service: 同步结果
CheckinsOfficeV2Service --> OfficeShiftService: 同步完成
OfficeShiftService --> Controller: 返回处理结果
Controller --> User: 返回响应

== 薪资计算流程 ==
User -> Controller: 薪资计算请求
Controller -> SalaryService: 调用薪资服务
SalaryService -> SalaryKPIFactory: 创建KPI计算器
SalaryKPIFactory -> SalaryKPICalculator: 获取计算实例
SalaryKPICalculator --> SalaryKPIFactory: 返回计算器
SalaryKPIFactory --> SalaryService: 返回计算器
SalaryService -> SalaryExpressionCalcService: 执行表达式计算
SalaryExpressionCalcService -> SalaryDao: 查询薪资数据
SalaryDao -> DB: 查询薪资基础数据
DB --> SalaryDao: 返回薪资数据
SalaryDao --> SalaryExpressionCalcService: 返回数据对象
SalaryExpressionCalcService -> SalaryExpressionCalcService: 执行计算逻辑
SalaryExpressionCalcService --> SalaryService: 返回计算结果
SalaryService -> SalaryService: 生成薪资单
SalaryService -> SalaryDao: 保存薪资记录
SalaryDao -> DB: 执行数据保存
DB --> SalaryDao: 保存确认
SalaryDao --> SalaryService: 保存完成
SalaryService -> NotificationService: 发送薪资通知
NotificationService -> External: 发送通知消息
External --> NotificationService: 发送结果
NotificationService --> SalaryService: 通知完成
SalaryService --> Controller: 返回处理结果
Controller --> User: 返回响应

== 报表数据收集流程 ==
User -> Controller: 报表查询请求
Controller -> ReportService: 调用报表服务
ReportService -> DistributionReportCollectService: 收集铺货数据
DistributionReportCollectService -> DataReportStandardDao: 查询标准数据
DataReportStandardDao -> DB: 查询报表标准
DB --> DataReportStandardDao: 返回标准数据
DataReportStandardDao --> DistributionReportCollectService: 返回标准对象
DistributionReportCollectService -> DataReportResultDao: 查询结果数据
DataReportResultDao -> DB: 查询报表结果
DB --> DataReportResultDao: 返回结果数据
DataReportResultDao --> DistributionReportCollectService: 返回结果对象
DistributionReportCollectService -> DistributionReportCollectService: 数据聚合计算
DistributionReportCollectService --> ReportService: 返回聚合结果
ReportService -> DisplayDistrAchSummaryService: 生成汇总报表
DisplayDistrAchSummaryService -> DisplayStandardService: 获取陈列标准
DisplayStandardService --> DisplayDistrAchSummaryService: 返回标准配置
DisplayDistrAchSummaryService -> DisplayDistrAchSummaryService: 生成汇总数据
DisplayDistrAchSummaryService --> ReportService: 返回汇总报表
ReportService --> Controller: 返回报表结果
Controller --> User: 返回响应

note over User, External
  **关键调用特点:**
  1. 分层架构：Controller -> Service -> DAO -> DB
  2. 服务间协作：多个Service相互调用完成复杂业务
  3. 异步处理：审批、通知等使用异步机制
  4. 数据同步：跨模块数据一致性保证
  5. 外部集成：与PaaS平台、考勤系统等集成
end note

@enduml
