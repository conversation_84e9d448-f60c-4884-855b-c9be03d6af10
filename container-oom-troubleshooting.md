# 容器内存溢出(OOM)排查指南

## 1. 查看容器资源限制和使用情况

### 1.1 Docker容器资源查看
```bash
# 查看容器资源使用情况
docker stats <container_id>

# 查看容器详细信息，包括内存限制
docker inspect <container_id> | grep -i memory

# 查看容器内存限制
docker exec <container_id> cat /sys/fs/cgroup/memory/memory.limit_in_bytes
docker exec <container_id> cat /sys/fs/cgroup/memory/memory.usage_in_bytes

# 查看容器OOM事件
docker exec <container_id> dmesg | grep -i "killed process"
```

### 1.2 Kubernetes Pod资源查看
```bash
# 查看Pod资源使用情况
kubectl top pod <pod_name> -n <namespace>

# 查看Pod详细信息
kubectl describe pod <pod_name> -n <namespace>

# 查看Pod资源限制
kubectl get pod <pod_name> -n <namespace> -o yaml | grep -A 10 resources

# 查看Pod事件，包括OOM事件
kubectl get events --field-selector involvedObject.name=<pod_name> -n <namespace>
```

## 2. Java应用内存分析

### 2.1 JVM内存配置检查
```bash
# 查看JVM启动参数
ps aux | grep java
jps -v

# 查看JVM内存使用情况
jstat -gc <pid>
jstat -gccapacity <pid>
jstat -gcutil <pid>

# 生成堆转储文件
jmap -dump:format=b,file=heapdump.hprof <pid>

# 查看JVM内存分布
jmap -histo <pid>
```

### 2.2 应用内存监控
```bash
# 查看进程内存使用
top -p <pid>
ps -o pid,ppid,cmd,%mem,%cpu -p <pid>

# 查看系统内存使用
free -h
cat /proc/meminfo

# 查看内存使用最多的进程
ps aux --sort=-%mem | head -10
```

## 3. 容器内存配置优化

### 3.1 Docker容器内存配置
```bash
# 启动容器时设置内存限制
docker run -m 4g <image>

# 设置内存和swap限制
docker run -m 4g --memory-swap 6g <image>

# 设置OOM时不杀死容器
docker run --oom-kill-disable <image>
```

### 3.2 Kubernetes资源配置
```yaml
apiVersion: v1
kind: Pod
spec:
  containers:
  - name: app
    resources:
      requests:
        memory: "2Gi"
        cpu: "1"
      limits:
        memory: "4Gi"
        cpu: "2"
```

### 3.3 JVM内存参数优化
```bash
# 基本内存设置
-Xms2g -Xmx4g

# G1垃圾收集器设置
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m

# 内存溢出时生成堆转储
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/tmp/heapdump.hprof

# 打印GC信息
-XX:+PrintGC
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-Xloggc:/tmp/gc.log

# 元空间设置
-XX:MetaspaceSize=256m
-XX:MaxMetaspaceSize=512m

# 直接内存设置
-XX:MaxDirectMemorySize=1g
```

## 4. 监控和告警设置

### 4.1 容器监控
```bash
# 使用cAdvisor监控容器
docker run -d --name=cadvisor -p 8080:8080 \
  --volume=/:/rootfs:ro \
  --volume=/var/run:/var/run:ro \
  --volume=/sys:/sys:ro \
  --volume=/var/lib/docker/:/var/lib/docker:ro \
  --volume=/dev/disk/:/dev/disk:ro \
  gcr.io/cadvisor/cadvisor:latest
```

### 4.2 应用监控
```java
// 添加内存监控代码
MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();

log.info("Heap Memory: used={}, max={}", 
    heapUsage.getUsed(), heapUsage.getMax());
log.info("Non-Heap Memory: used={}, max={}", 
    nonHeapUsage.getUsed(), nonHeapUsage.getMax());
```

## 5. 常见OOM原因和解决方案

### 5.1 堆内存溢出
**原因**: 
- 内存泄漏
- 大对象创建
- 缓存过大

**解决方案**:
- 增加堆内存: `-Xmx`
- 优化代码，避免内存泄漏
- 使用内存分析工具(MAT, JProfiler)

### 5.2 元空间溢出
**原因**:
- 类加载过多
- 动态代理类过多

**解决方案**:
- 增加元空间: `-XX:MaxMetaspaceSize`
- 检查类加载器泄漏

### 5.3 直接内存溢出
**原因**:
- NIO操作过多
- Netty等框架使用直接内存

**解决方案**:
- 增加直接内存: `-XX:MaxDirectMemorySize`
- 优化NIO使用

### 5.4 容器内存限制
**原因**:
- 容器内存限制过小
- JVM不感知容器限制

**解决方案**:
- 增加容器内存限制
- 使用容器感知的JVM参数: `-XX:+UseContainerSupport`

## 6. 应急处理步骤

### 6.1 立即处理
```bash
# 1. 重启容器
docker restart <container_id>
# 或
kubectl delete pod <pod_name> -n <namespace>

# 2. 临时增加内存限制
kubectl patch deployment <deployment_name> -p '{"spec":{"template":{"spec":{"containers":[{"name":"<container_name>","resources":{"limits":{"memory":"8Gi"}}}]}}}}'

# 3. 扩容Pod数量
kubectl scale deployment <deployment_name> --replicas=3
```

### 6.2 数据收集
```bash
# 收集系统信息
dmesg | tail -100
free -h
df -h
ps aux --sort=-%mem | head -20

# 收集容器信息
docker logs <container_id> --tail=1000
kubectl logs <pod_name> -n <namespace> --tail=1000

# 收集JVM信息
jstack <pid> > thread_dump.txt
jmap -histo <pid> > heap_histo.txt
```

## 7. 预防措施

### 7.1 资源规划
- 根据应用特点合理设置内存限制
- 预留足够的内存缓冲
- 定期评估和调整资源配置

### 7.2 监控告警
- 设置内存使用率告警(如80%)
- 监控GC频率和时间
- 监控容器重启次数

### 7.3 代码优化
- 定期进行内存泄漏检查
- 优化大对象的使用
- 合理使用缓存

### 7.4 测试验证
- 进行压力测试
- 模拟OOM场景
- 验证监控和告警机制
