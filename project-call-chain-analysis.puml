@startuml fs-crm-fmcg-wq项目调用链分析图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle
skinparam packageStyle rectangle

title fs-crm-fmcg-wq 快消CRM系统调用链分析图

package "Web层 (fs-crm-fmcg-wq-web)" {
  [Web应用入口] as WebApp
  [Spring配置] as SpringConfig
  [应用上下文] as AppContext
}

package "通用模块 (fs-crm-fmcg-wq-common)" {
  [基础DAO] as BaseDao
  [通用工具类] as CommonUtils
  [常量定义] as Constants
  [预置角色服务] as PreRoleService
  [员工DAO] as EmployeeDao
  [Redis工具] as RedisUtils
}

package "供货管理模块 (fs-crm-fmcg-wq-supply)" {
  package "Controller层" {
    [供货关系Controller] as SupplyController
    [经销商供货Controller] as DealerSupplyController
    [配送商供货Controller] as DistributorSupplyController
    [区域管理Controller] as AreaManageController
    [覆盖门店Controller] as CoveredStoresController
  }

  package "Service层" {
    [供货服务] as SupplyService
    [供货服务实现] as SupplyServiceImpl
    [同步可售范围服务] as SyncAvailableRangeService
    [供货审批服务] as SupplyApproService
    [销售区域服务] as SalesAreaService
    [渠道服务] as ChannelService
    [促销员服务] as PMMService
  }

  package "DAO层" {
    [供货DAO] as SupplyDao
    [供货DAO接口] as SupplyDaoInterface
    [公共员工DAO] as PublicEmployeeDao
  }

  package "Action层" {
    [区域管理Action] as AreaManageAction
    [促销员Action] as PromoterAction
    [供货变更Action] as SupplyChangeAction
  }

  package "预定义对象" {
    [MC预定义对象] as MCPreDefineObject
  }
}

package "办公模块 (fs-crm-fmcg-wq-office)" {
  package "Controller层" {
    [用户排班Controller] as UserScheduleController
    [排班批量添加Controller] as UserScheduleBatchController
    [排班导入Controller] as UserScheduleImportController
  }

  package "Service层" {
    [办公排班服务] as OfficeShiftService
    [办公排班服务实现] as OfficeShiftServiceImpl
  }

  package "DAO层" {
    [客户班次DAO] as AccountShiftDao
    [用户排班DAO] as UserScheduleDao
  }

  package "Action层" {
    [用户排班Action] as UserScheduleAction
  }
}

package "薪资模块 (fs-crm-fmcg-wq-salary)" {
  package "API层" {
    [薪资API] as SalaryAPI
  }

  package "配置层" {
    [薪资配置] as SalaryConfig
  }

  package "常量层" {
    [薪资常量] as SalaryConstants
  }

  package "工具层" {
    [薪资工具] as SalaryUtils
  }

  package "Action层" {
    [薪资Action] as SalaryAction
  }
}

package "报表模块 (fs-crm-fmcg-wq-report)" {
  package "Service层" {
    [铺货报表收集服务] as DistributionReportCollectService
    [陈列达成汇总服务] as DisplayDistrAchSummaryService
    [陈列标准服务] as DisplayStandardService
  }

  package "DAO层" {
    [数据报表标准DAO] as DataReportStandardDao
    [数据报表结果DAO] as DataReportResultDao
  }
}

package "签到模块 (fs-crm-fmcg-wq-checkins)" {
  [签到默认对象] as CheckinsDefaultObject
  [签到DAO] as CheckinsDao
  [签到代理] as CheckinsProxy
}

package "全量模块 (fs-crm-fmcg-wq-all)" {
  [WQ初始化服务] as WQInitService
  [应用配置] as ApplicationConfig
}

package "外部依赖" {
  [PaaS框架] as PaasFramework
  [元数据服务] as MetadataService
  [权限服务] as PrivilegeService
  [消息队列] as MessageQueue
  [Redis缓存] as RedisCache
  [MongoDB] as MongoDB
  [PostgreSQL] as PostgreSQL
}

' 调用关系
WebApp --> SpringConfig
SpringConfig --> AppContext
AppContext --> WQInitService

WQInitService --> CheckinsDefaultObject
WQInitService --> MCPreDefineObject
WQInitService --> OfficeShiftService

' Controller到Service的调用
SupplyController --> SupplyService
DealerSupplyController --> SupplyService
DealerSupplyController --> SyncAvailableRangeService
AreaManageController --> SupplyService
UserScheduleController --> OfficeShiftService
UserScheduleBatchController --> OfficeShiftService

' Service到DAO的调用
SupplyServiceImpl --> SupplyDao
SupplyServiceImpl --> BaseDao
OfficeShiftServiceImpl --> AccountShiftDao
OfficeShiftServiceImpl --> UserScheduleDao
OfficeShiftServiceImpl --> EmployeeDao
PMMService --> PublicEmployeeDao
SalesAreaService --> BaseDao

' Service之间的调用
SupplyService --> SyncAvailableRangeService
SupplyService --> SupplyApproService
OfficeShiftService --> CheckinsProxy

' DAO到外部服务的调用
SupplyDao --> MetadataService
BaseDao --> PostgreSQL
BaseDao --> MongoDB
RedisUtils --> RedisCache

' Action的调用关系
AreaManageAction --> SupplyDao
PromoterAction --> PMMService
UserScheduleAction --> OfficeShiftService

' 报表模块调用
DistributionReportCollectService --> DataReportStandardDao
DisplayDistrAchSummaryService --> DataReportResultDao

' 外部框架依赖
SupplyService --> PaasFramework
OfficeShiftService --> PaasFramework
BaseDao --> MetadataService
SupplyApproService --> MessageQueue

note right of WebApp : Web应用入口\n负责HTTP请求处理

note right of SupplyService : 核心供货业务逻辑\n处理供货关系管理

note right of OfficeShiftService : 办公排班核心服务\n处理用户排班逻辑

note right of BaseDao : 通用数据访问层\n提供基础CRUD操作

note right of WQInitService : 系统初始化服务\n注册预定义对象

note bottom of PaasFramework : PaaS平台框架\n提供基础服务能力

' 详细调用流程说明
note top of WebApp
  **主要调用流程:**
  1. HTTP请求 -> Controller
  2. Controller -> Service
  3. Service -> DAO
  4. DAO -> 数据库/外部服务
  5. 返回响应链路
end note

note top of SupplyController
  **供货管理调用链:**
  - 供货关系CRUD
  - 经销商供货管理
  - 配送商供货管理
  - 特殊供货处理
  - 供货审批流程
end note

note top of OfficeShiftService
  **办公排班调用链:**
  - 用户排班管理
  - 班次冲突检测
  - 排班数据同步
  - 批量排班处理
  - 排班规则验证
end note

note top of SalaryAPI
  **薪资模块调用链:**
  - 薪资计算逻辑
  - KPI计算处理
  - 薪资单生成
  - 薪资数据查询
  - 薪资通知发送
end note

@enduml
