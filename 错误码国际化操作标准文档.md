# 错误码国际化操作标准文档

## 目录
- [1. 前置条件与环境要求](#1-前置条件与环境要求)
- [2. 快速参考指南](#2-快速参考指南)
- [3. 错误码国际化设计原则](#3-错误码国际化设计原则)
- [4. 错误码合并策略](#4-错误码合并策略)
- [5. 实际应用示例](#5-实际应用示例)
- [6. 最佳实践指南](#6-最佳实践指南)
- [7. 维护规范](#7-维护规范)
- [8. 代码审查检查清单](#8-代码审查检查清单)
- [9. 工具和自动化](#9-工具和自动化)
- [10. 硬编码扫描与处理](#10-硬编码扫描与处理)
- [11. 总结](#11-总结)

## 1. 前置条件与环境要求

### 1.1 技术栈要求
- **Java版本**: JDK 1.8+
- **Maven版本**: 3.6+
- **Spring框架**: 基于现有的PaaS应用框架
- **国际化框架**: I18NExt + MessageFormat

### 1.2 项目依赖
```xml
<!-- 国际化工具依赖 -->
<dependency>
    <groupId>com.facishare</groupId>
    <artifactId>fs-paas-app-core</artifactId>
    <version>${appframework.version}</version>
</dependency>

<!-- 国际化工具包 -->
<dependency>
    <groupId>com.facishare</groupId>
    <artifactId>i18n-util</artifactId>
    <version>${i18n-util.version}</version>
</dependency>
```

### 1.3 必要的开发工具
- **IDE插件**: 支持中文字符串检测的插件
- **Maven插件**: 用于代码扫描和国际化验证
- **SonarQube**: 代码质量检查（已配置）

## 2. 快速参考指南

### 2.1 错误码命名速查表
| 操作类型 | 错误原因 | 错误码格式 | 示例 |
|---------|---------|-----------|------|
| BULK_* | *_USED_ERROR | 批量操作-已被使用 | BULK_INVALID_USED_ERROR |
| DIRECT_* | *_UNSUPPORTED_ERROR | 直接操作-不支持 | DIRECT_OPERATION_UNSUPPORTED_ERROR |
| INVALID_* | *_DEPENDENCY_ERROR | 作废操作-依赖错误 | INVALID_DEPENDENCY_ERROR |
| OPERATION_* | *_PRECONDITION_ERROR | 操作-前置条件 | OPERATION_PRECONDITION_ERROR |
| DISTRIBUTE_* | *_NO_DATA_ERROR | 分发-缺少数据 | DISTRIBUTE_NO_DATA_ERROR |

### 2.2 国际化Key命名速查
```properties
# 格式：fs.crm.fmcg.wq.{module}.{operation}.{reason}.error
fs.crm.fmcg.wq.bulk.invalid.used.error=以下{0}已被使用，不可作废：{1}
fs.crm.fmcg.wq.invalid.dependency.error={0}[{1}]已被{2}使用，不可作废。使用该{0}的{2}：{3}
fs.crm.fmcg.wq.direct.operation.unsupported.error=不允许直接{0}{1}，请通过{2}管理
```

### 2.3 错误码编号分配速查
```java
// 305_002_001 ~ 305_002_050: 原有错误码（保持不变）
// 305_002_051 ~ 305_002_100: 通用化错误码
// 305_002_101 ~ 305_002_150: 批量操作错误码
// 305_002_151 ~ 305_002_200: 直接操作错误码
// 305_002_201 ~ 305_002_250: 分发操作错误码
```

## 3. 错误码国际化设计原则

### 3.1 通用化命名策略

#### 命名模式
错误码命名应遵循 `{操作类型}_{错误原因}_ERROR` 的模式：

```java
// ✅ 推荐：基于操作类型和错误原因
BULK_INVALID_USED_ERROR          // 批量作废-已被使用错误
DIRECT_OPERATION_UNSUPPORTED_ERROR // 直接操作-不支持错误
INVALID_DEPENDENCY_ERROR         // 作废操作-被依赖错误

// ❌ 避免：基于具体业务对象
SALARY_ITEM_BULK_INVALID_USED_ERROR
SALARY_KPI_INVALID_DEPENDENCY_ERROR
```

#### 操作类型分类
- `BULK_*` - 批量操作相关
- `DIRECT_*` - 直接操作相关  
- `INVALID_*` - 作废操作相关
- `OPERATION_*` - 通用操作相关
- `DISTRIBUTE_*` - 分发操作相关

#### 错误原因分类
- `*_USED_ERROR` - 已被使用/依赖
- `*_UNSUPPORTED_ERROR` - 不支持的操作
- `*_DEPENDENCY_ERROR` - 依赖关系错误
- `*_PRECONDITION_ERROR` - 前置条件不满足
- `*_NO_DATA_ERROR` - 缺少必要数据

### 3.2 参数化设计模式

#### 占位符使用规范
```java
// 标准参数化文案格式
"{0}[{1}]已被{2}使用，不可作废。使用该{0}的{2}：{3}"
//  ^   ^      ^                    ^      ^
// 对象 名称   依赖对象              对象   依赖列表
```

#### 参数顺序约定
1. `{0}` - 主要操作对象（如"工资项"、"指标"）
2. `{1}` - 具体记录标识（如记录名称、ID）
3. `{2}` - 关联对象或操作名称
4. `{3}` - 详细信息或列表

### 3.3 错误码分类规范

```java
public enum CheckinsErrorCode implements ErrorCode {
    
    // === 原有错误码（保持不变） ===
    UNSUPPORTED(305_002_019,"不支持此操作","不支持此操作"),
    GO_TO_WAIQIN(305_002_018,I18nZhCNEnum.oaappsrv_waiqin_msg_checkinspreobjunsupport.getI18nKey(),I18nZhCNEnum.oaappsrv_waiqin_msg_checkinspreobjunsupport.getName()),
    UNSUPPORTED_OPERATION(305_002_017,I18nZhCNEnum.oaappsrv_waiqin_msg_checkinspreobjunsupport.getI18nKey(),I18nZhCNEnum.oaappsrv_waiqin_msg_checkinspreobjunsupport.getName()),
    GO_TO_630_WAIQIN(305_002_020,"请使用6.3版本以上外勤，执行该操作","请使用6.3版本以上外勤，执行该操作"),
    UNSUPPORTED_INVALID_CONDITION(305_002_021,"fs.crm.fmcg.wq.unsupported.invalid.condition","{0}的{1}是{2}，不可作废"),
    
    // === 通用化错误码（按操作类型分组） ===
    
    // 批量操作错误组
    BULK_INVALID_USED_ERROR(305_002_022,"fs.crm.fmcg.wq.bulk.invalid.used.error","以下{0}已被使用，不可作废：{1}"),
    BULK_OPERATION_UNSUPPORTED_ERROR(305_002_026,"fs.crm.fmcg.wq.bulk.operation.unsupported.error","暂不支持{0}{1}"),
    
    // 作废操作错误组
    INVALID_DEPENDENCY_ERROR(305_002_023,"fs.crm.fmcg.wq.invalid.dependency.error","{0}[{1}]已被{2}使用，不可作废。使用该{0}的{2}：{3}"),
    
    // 直接操作错误组
    DIRECT_OPERATION_UNSUPPORTED_ERROR(305_002_024,"fs.crm.fmcg.wq.direct.operation.unsupported.error","不允许直接{0}{1}，请通过{2}管理"),
    
    // 操作前置条件错误组
    OPERATION_PRECONDITION_ERROR(305_002_025,"fs.crm.fmcg.wq.operation.precondition.error","该{0}下没有{1}数据，无法{2}"),
    
    ;
}
```

## 4. 错误码合并策略

### 4.1 功能相似错误码识别

#### 识别标准
1. **错误场景相同**：都是批量作废、都是直接操作等
2. **错误原因相同**：都是被使用、都是不支持等
3. **用户体验相同**：错误提示的核心信息一致

#### 合并前后对比
```java
// 合并前：7个业务特定错误码
SALARY_ITEM_BULK_INVALID_USED_ERROR
SALARY_KPI_BULK_INVALID_USED_ERROR
SALARY_KPI_INVALID_DEPENDENCY_ERROR
SALARY_DETAIL_DATA_BULK_INVALID_UNSUPPORTED_ERROR
SALARY_DETAIL_DATA_INVALID_UNSUPPORTED_ERROR
SALARY_PAYMENT_SLIP_DISTRIBUTE_NO_DATA_ERROR
PRODUCT_COLLECTION_BULK_INVALID_UNSUPPORTED_ERROR

// 合并后：4个通用错误码
BULK_INVALID_USED_ERROR              // 覆盖前2个
INVALID_DEPENDENCY_ERROR             // 覆盖第3个
DIRECT_OPERATION_UNSUPPORTED_ERROR   // 覆盖第4、5个
OPERATION_PRECONDITION_ERROR         // 覆盖第6个
BULK_OPERATION_UNSUPPORTED_ERROR     // 覆盖第7个
```

### 4.2 归并方法

#### 步骤1：错误场景分析
```java
// 分析现有错误码的使用场景
Map<String, List<String>> errorScenarios = Map.of(
    "批量作废-已被使用", List.of("SALARY_ITEM_BULK_INVALID_USED_ERROR", "SALARY_KPI_BULK_INVALID_USED_ERROR"),
    "直接操作-不支持", List.of("SALARY_DETAIL_DATA_BULK_INVALID_UNSUPPORTED_ERROR", "SALARY_DETAIL_DATA_INVALID_UNSUPPORTED_ERROR"),
    "操作前置条件-数据不足", List.of("SALARY_PAYMENT_SLIP_DISTRIBUTE_NO_DATA_ERROR")
);
```

#### 步骤2：通用错误码设计
```java
// 为每个场景设计一个通用错误码
BULK_INVALID_USED_ERROR -> 覆盖所有"批量作废-已被使用"场景
DIRECT_OPERATION_UNSUPPORTED_ERROR -> 覆盖所有"直接操作-不支持"场景
```

## 5. 实际应用示例

### 5.1 BULK_INVALID_USED_ERROR 使用示例

#### 场景：工资项批量作废
```java
public class SalaryItemBulkInvalidAction extends AbstractBulkInvalidAction {
    @Override
    public void before(ActionContext actionContext) throws Exception {
        List<String> usedItems = checkUsedItems(actionContext.getIds());
        if (!usedItems.isEmpty()) {
            IObjectDescribe objectDescribe = getObjectDescribe(actionContext.getTenantId());
            throw new CheckinsException(CheckinsErrorCode.BULK_INVALID_USED_ERROR, 
                new String[]{objectDescribe.getDisplayName(), String.join("、", usedItems)});
        }
    }
}
```

#### 场景：指标批量作废
```java
public class SalaryKPIBulkInvalidAction extends AbstractBulkInvalidAction {
    @Override
    public void before(ActionContext actionContext) throws Exception {
        List<String> usedKPIs = checkUsedKPIs(actionContext.getIds());
        if (!usedKPIs.isEmpty()) {
            IObjectDescribe objectDescribe = getObjectDescribe(actionContext.getTenantId());
            throw new CheckinsException(CheckinsErrorCode.BULK_INVALID_USED_ERROR, 
                new String[]{objectDescribe.getDisplayName(), String.join("、", usedKPIs)});
        }
    }
}
```

### 5.2 INVALID_DEPENDENCY_ERROR 使用示例

```java
public class SalaryKPIInvalidAction extends AbstractInvalidAction {
    @Override
    public void before(ActionContext actionContext) throws Exception {
        String kpiId = actionContext.getId();
        List<String> usingItems = findUsingSalaryItems(kpiId);
        
        if (!usingItems.isEmpty()) {
            IObjectDescribe kpiDescribe = getObjectDescribe(actionContext.getTenantId());
            IObjectDescribe itemDescribe = getSalaryItemDescribe(actionContext.getTenantId());
            String kpiName = getKPIName(kpiId);
            
            throw new CheckinsException(CheckinsErrorCode.INVALID_DEPENDENCY_ERROR,
                new String[]{
                    kpiDescribe.getDisplayName(),    // {0} - "指标"
                    kpiName,                         // {1} - 具体指标名称
                    itemDescribe.getDisplayName(),   // {2} - "工资项"  
                    String.join("、", usingItems)    // {3} - 使用该指标的工资项列表
                });
        }
    }
}
```

### 5.3 DIRECT_OPERATION_UNSUPPORTED_ERROR 使用示例

```java
public class SalaryDetailDataInvalidAction extends AbstractInvalidAction {
    @Override
    public void before(ActionContext actionContext) throws Exception {
        IObjectDescribe detailDescribe = getObjectDescribe(actionContext.getTenantId());
        IObjectDescribe slipDescribe = getSalaryPaymentSlipDescribe(actionContext.getTenantId());
        
        throw new CheckinsException(CheckinsErrorCode.DIRECT_OPERATION_UNSUPPORTED_ERROR, 
            new String[]{
                "作废",                              // {0} - 操作名称
                detailDescribe.getDisplayName(),     // {1} - "工资条明细"
                slipDescribe.getDisplayName()        // {2} - "工资发放单"
            });
    }
}
```

## 6. 最佳实践指南

### 6.1 新增错误码决策流程

```
需要新的错误提示
    ↓
是否存在相同操作类型的错误码？
    ├─ 是 → 是否存在相同错误原因的错误码？
    │       ├─ 是 → 复用现有错误码，调整参数
    │       └─ 否 → 在现有操作类型下新增错误原因
    └─ 否 → 创建新的操作类型错误码
```

### 6.2 错误信息用户友好性要求

#### 必须包含的信息
1. **明确的错误原因**：用户能理解为什么操作失败
2. **具体的影响对象**：哪些数据受到影响
3. **正确的操作指引**：告诉用户应该怎么做

#### 文案编写规范
```java
// ✅ 好的错误文案
"工资项[基本工资]已被工资规则使用，不可作废。使用该工资项的工资规则：销售提成规则、管理津贴规则"

// ❌ 不好的错误文案  
"操作失败"
"数据异常"
"系统错误"
```

### 6.3 多语言环境文案管理

#### 国际化Key命名规范
```properties
# 格式：fs.crm.fmcg.wq.{操作类型}.{错误原因}.error
fs.crm.fmcg.wq.bulk.invalid.used.error=以下{0}已被使用，不可作废：{1}
fs.crm.fmcg.wq.invalid.dependency.error={0}[{1}]已被{2}使用，不可作废。使用该{0}的{2}：{3}
fs.crm.fmcg.wq.direct.operation.unsupported.error=不允许直接{0}{1}，请通过{2}管理
```

#### 多语言文案示例
```properties
# 中文
fs.crm.fmcg.wq.bulk.invalid.used.error=以下{0}已被使用，不可作废：{1}

# 英文  
fs.crm.fmcg.wq.bulk.invalid.used.error=The following {0} are in use and cannot be invalidated: {1}

# 繁体中文
fs.crm.fmcg.wq.bulk.invalid.used.error=以下{0}已被使用，不可作廢：{1}
```

## 7. 维护规范

### 7.1 错误码编号分配规则

#### 编号范围划分
```java
// 305_002_001 ~ 305_002_050: 原有错误码（保持不变）
// 305_002_051 ~ 305_002_100: 通用化错误码
// 305_002_101 ~ 305_002_150: 批量操作错误码
// 305_002_151 ~ 305_002_200: 直接操作错误码
```

#### 编号分配流程
1. **确定错误码类型**：通用/批量/直接操作等
2. **选择对应编号范围**：根据类型选择编号段
3. **递增分配编号**：在范围内按顺序分配
4. **更新编号记录**：在文档中记录已使用的编号

### 7.2 国际化Key命名约定

#### 命名模式
```
fs.crm.fmcg.wq.{module}.{operation}.{reason}.error
```

#### 各部分说明
- `fs.crm.fmcg.wq` - 固定前缀
- `{module}` - 模块名（bulk/invalid/direct/operation）
- `{operation}` - 操作类型（invalid/unsupported/dependency）  
- `{reason}` - 错误原因（used/precondition/no_data）
- `error` - 固定后缀

### 7.3 向后兼容性保证

#### 兼容性原则
1. **不删除现有错误码**：已发布的错误码不能删除
2. **不修改错误码编号**：编号一旦分配不能更改
3. **可以修改文案内容**：在不改变语义的前提下可以优化文案
4. **新增错误码向前兼容**：新错误码不能影响现有功能

#### 废弃错误码处理
```java
// 标记为废弃但保留定义
@Deprecated
SALARY_ITEM_BULK_INVALID_USED_ERROR(305_002_027,"fs.crm.fmcg.wq.salary.item.bulk.invalid.used.error","请使用 BULK_INVALID_USED_ERROR 替代"),
```

## 8. 代码审查检查清单

### 8.1 错误码使用检查
- [ ] 是否优先复用现有通用错误码？
- [ ] 错误码命名是否遵循规范？
- [ ] 参数传递是否按照约定顺序？
- [ ] 错误文案是否用户友好？

### 8.2 国际化配置检查
- [ ] 是否添加了国际化Key配置？
- [ ] Key命名是否符合约定？
- [ ] 是否提供了默认文案？
- [ ] 参数占位符是否正确？

### 8.3 兼容性检查
- [ ] 是否影响现有错误码？
- [ ] 编号是否在正确范围内？
- [ ] 是否有重复的错误码定义？
- [ ] 是否更新了相关文档？

## 9. 工具和自动化

### 9.1 错误码检查工具
```java
// 错误码重复检查
public class ErrorCodeValidator {
    public static void validateNoDuplicateCodes() {
        Set<Integer> codes = new HashSet<>();
        for (CheckinsErrorCode errorCode : CheckinsErrorCode.values()) {
            if (!codes.add(errorCode.getCode())) {
                throw new IllegalStateException("Duplicate error code: " + errorCode.getCode());
            }
        }
    }
}
```

### 9.2 国际化文案检查
```java
// 检查国际化Key是否存在对应的文案配置
public class I18nValidator {
    public static void validateI18nKeys() {
        for (CheckinsErrorCode errorCode : CheckinsErrorCode.values()) {
            String message = I18NExt.getOrDefault(errorCode.getI18nKey(), null);
            if (message == null) {
                log.warn("Missing i18n configuration for: " + errorCode.getI18nKey());
            }
        }
    }
}
```

### 9.3 Maven插件配置
```xml
<!-- 在pom.xml中添加国际化检查插件 -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-antrun-plugin</artifactId>
    <version>3.1.0</version>
    <executions>
        <execution>
            <id>check-hardcoded-chinese</id>
            <phase>validate</phase>
            <goals>
                <goal>run</goal>
            </goals>
            <configuration>
                <target>
                    <exec executable="grep" failonerror="false" resultproperty="grep.result">
                        <arg value="-r"/>
                        <arg value="-n"/>
                        <arg value="--include=*.java"/>
                        <arg value="[\u4e00-\u9fa5]"/>
                        <arg value="src/main/java"/>
                    </exec>
                    <condition property="has.chinese">
                        <equals arg1="${grep.result}" arg2="0"/>
                    </condition>
                    <fail if="has.chinese" message="发现硬编码中文字符串，请使用国际化处理"/>
                </target>
            </configuration>
        </execution>
    </executions>
</plugin>
```

### 9.4 IDE集成工具
```java
// IntelliJ IDEA插件配置示例
// 在.idea/inspectionProfiles/Project_Default.xml中添加：
<inspection_tool class="HardCodedStringLiteral" enabled="true" level="WARNING" enabled_by_default="true">
    <option name="ignoreForAssertStatements" value="true" />
    <option name="ignoreForExceptionConstructors" value="true" />
    <option name="ignoreForSpecifiedExceptionConstructors" value="" />
    <option name="ignoreForJUnitAsserts" value="true" />
    <option name="ignoreForClassReferences" value="true" />
    <option name="ignoreForPropertyKeyReferences" value="true" />
    <option name="ignoreForNonAlpha" value="true" />
    <option name="ignoreAssignedToConstants" value="false" />
    <option name="ignoreToString" value="false" />
    <option name="nonNlsCommentPattern" value="NON-NLS-\d+|ignoreI18n" />
</inspection_tool>
```

## 10. 硬编码扫描与处理

### 10.1 硬编码识别标准

#### 需要国际化的中文字符串
```java
// ❌ 需要国际化处理
throw new CheckinsException("操作失败");
log.error("数据异常");
return "系统错误";

// ✅ 正确的国际化处理
throw new CheckinsException(CheckinsErrorCode.OPERATION_FAILED);
log.error("Data exception occurred, context: {}", context);
return I18NExt.getOrDefault("fs.crm.fmcg.wq.system.error", "System Error");
```

#### 可以忽略的中文字符串（需要添加//ignoreI18n注释）
```java
// ✅ 日志记录中的调试信息
log.debug("开始处理薪资计算任务"); // ignoreI18n

// ✅ 枚举常量的标签
NORMAL("normal", "正常"), // ignoreI18n

// ✅ 测试用例中的测试数据
@Test
public void testSalaryCalculation() {
    String testName = "测试用户"; // ignoreI18n
}

// ✅ 配置文件路径或技术性字符串
private static final String CONFIG_PATH = "配置/薪资规则.xml"; // ignoreI18n
```

### 10.2 自动化扫描脚本

#### Bash脚本（适用于Linux/Mac）
```bash
#!/bin/bash
# 文件名：scan_hardcoded_chinese.sh

echo "开始扫描硬编码中文字符串..."

# 扫描Java文件中的中文字符串，排除已标记ignoreI18n的行
find src/main/java -name "*.java" -exec grep -Hn "[\u4e00-\u9fa5]" {} \; | \
grep -v "//.*ignoreI18n" | \
grep -v "log\." | \
grep -v "/\*" | \
grep -v "\*/" > hardcoded_chinese.log

if [ -s hardcoded_chinese.log ]; then
    echo "发现硬编码中文字符串："
    cat hardcoded_chinese.log
    echo ""
    echo "请处理以上硬编码字符串或添加 //ignoreI18n 注释"
    exit 1
else
    echo "未发现需要处理的硬编码中文字符串"
    exit 0
fi
```

#### PowerShell脚本（适用于Windows）
```powershell
# 文件名：Scan-HardcodedChinese.ps1

Write-Host "开始扫描硬编码中文字符串..." -ForegroundColor Green

$chinesePattern = '[\u4e00-\u9fa5]'
$javaFiles = Get-ChildItem -Path "src\main\java" -Filter "*.java" -Recurse

$results = @()

foreach ($file in $javaFiles) {
    $lineNumber = 0
    Get-Content $file.FullName | ForEach-Object {
        $lineNumber++
        if ($_ -match $chinesePattern -and $_ -notmatch '//.*ignoreI18n' -and $_ -notmatch 'log\.' -and $_ -notmatch '/\*' -and $_ -notmatch '\*/') {
            $results += [PSCustomObject]@{
                File = $file.FullName
                Line = $lineNumber
                Content = $_.Trim()
            }
        }
    }
}

if ($results.Count -gt 0) {
    Write-Host "发现硬编码中文字符串：" -ForegroundColor Red
    $results | Format-Table -AutoSize
    Write-Host "请处理以上硬编码字符串或添加 //ignoreI18n 注释" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "未发现需要处理的硬编码中文字符串" -ForegroundColor Green
    exit 0
}
```

### 10.3 批量处理工具

#### 错误码替换工具
```java
/**
 * 硬编码中文字符串批量替换工具
 */
public class HardcodedChineseReplacer {

    private static final Map<String, String> COMMON_REPLACEMENTS = Map.of(
        "操作失败", "CheckinsErrorCode.OPERATION_FAILED",
        "数据异常", "CheckinsErrorCode.DATA_EXCEPTION",
        "系统错误", "CheckinsErrorCode.SYSTEM_ERROR",
        "参数错误", "CheckinsErrorCode.PARAMETER_ERROR",
        "权限不足", "CheckinsErrorCode.PERMISSION_DENIED"
    );

    public static void replaceInFile(Path filePath) throws IOException {
        String content = Files.readString(filePath, StandardCharsets.UTF_8);

        for (Map.Entry<String, String> entry : COMMON_REPLACEMENTS.entrySet()) {
            String pattern = "\"" + entry.getKey() + "\"";
            String replacement = entry.getValue();
            content = content.replaceAll(Pattern.quote(pattern), replacement);
        }

        Files.writeString(filePath, content, StandardCharsets.UTF_8);
    }

    public static void processDirectory(String directoryPath) throws IOException {
        Files.walk(Paths.get(directoryPath))
            .filter(path -> path.toString().endsWith(".java"))
            .forEach(path -> {
                try {
                    replaceInFile(path);
                } catch (IOException e) {
                    System.err.println("处理文件失败: " + path + ", 错误: " + e.getMessage());
                }
            });
    }
}
```

### 10.4 持续集成配置

#### Jenkins Pipeline示例
```groovy
pipeline {
    agent any

    stages {
        stage('硬编码检查') {
            steps {
                script {
                    def result = sh(
                        script: './scan_hardcoded_chinese.sh',
                        returnStatus: true
                    )

                    if (result != 0) {
                        error("发现硬编码中文字符串，构建失败")
                    }
                }
            }
        }

        stage('国际化验证') {
            steps {
                sh 'mvn test -Dtest=I18nValidatorTest'
            }
        }
    }

    post {
        always {
            archiveArtifacts artifacts: 'hardcoded_chinese.log', allowEmptyArchive: true
        }
    }
}
```

#### GitHub Actions配置
```yaml
name: 国际化检查

on: [push, pull_request]

jobs:
  i18n-check:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: 设置Java环境
      uses: actions/setup-java@v3
      with:
        java-version: '8'
        distribution: 'temurin'

    - name: 硬编码中文检查
      run: |
        chmod +x ./scan_hardcoded_chinese.sh
        ./scan_hardcoded_chinese.sh

    - name: 运行国际化测试
      run: mvn test -Dtest=I18nValidatorTest
```

## 11. 总结

通过遵循这个标准文档，团队可以确保错误码的一致性、可维护性和用户友好性，同时为未来的扩展提供良好的基础。

### 核心收益
- **减少重复代码**：7个业务特定错误码合并为4个通用错误码
- **提高可维护性**：统一的命名规范和参数化设计
- **增强用户体验**：清晰友好的错误提示信息
- **支持国际化**：完善的多语言支持机制
- **便于扩展**：通用化设计支持未来业务场景
- **自动化保障**：完整的扫描和验证工具链

### 实施路线图

#### 第一阶段：基础设施建设（1-2周）
1. **环境准备**
   - 配置Maven插件和IDE检查工具
   - 建立CI/CD流水线中的国际化检查
   - 部署硬编码扫描脚本

2. **工具集成**
   - 集成SonarQube规则
   - 配置IDE插件
   - 建立自动化测试

#### 第二阶段：错误码标准化（2-3周）
1. **新增通用错误码**
   - 按照设计原则添加通用错误码
   - 配置对应的国际化资源文件
   - 编写单元测试验证

2. **现有代码迁移**
   - 使用批量替换工具处理明显的硬编码
   - 逐步替换业务特定错误码
   - 保持向后兼容性

#### 第三阶段：全面优化（3-4周）
1. **硬编码清理**
   - 扫描并处理所有硬编码中文字符串
   - 添加必要的//ignoreI18n注释
   - 验证国际化覆盖率

2. **质量保障**
   - 执行完整的回归测试
   - 验证多语言环境下的功能
   - 建立长期维护机制

### 质量指标

#### 代码质量指标
- **硬编码中文字符串**: 0个（除已标记ignoreI18n的）
- **错误码重复率**: 0%
- **国际化覆盖率**: 100%
- **单元测试覆盖率**: ≥80%

#### 用户体验指标
- **错误信息清晰度**: 包含具体原因、影响对象、操作指引
- **多语言支持**: 中文、英文、繁体中文
- **响应时间**: 错误码解析时间 <10ms

### 长期维护策略

#### 新增错误码流程
1. **需求评估**: 检查是否可复用现有错误码
2. **设计审查**: 确保符合命名规范和设计原则
3. **实现验证**: 编写测试用例，验证多语言支持
4. **文档更新**: 更新本标准文档和相关说明

#### 定期检查机制
- **每月代码扫描**: 自动检查新增的硬编码字符串
- **季度质量评估**: 评估错误码使用情况和用户反馈
- **年度标准更新**: 根据业务发展更新设计原则和规范

### 团队培训要点

#### 开发人员培训
1. **国际化意识**: 理解国际化的重要性和最佳实践
2. **工具使用**: 熟练使用IDE插件和扫描工具
3. **错误码设计**: 掌握通用化设计原则和命名规范
4. **代码审查**: 在代码审查中关注国际化问题

#### 测试人员培训
1. **多语言测试**: 验证不同语言环境下的功能
2. **错误场景测试**: 确保错误信息的准确性和友好性
3. **自动化测试**: 编写国际化相关的自动化测试用例

### 风险控制

#### 技术风险
- **兼容性问题**: 通过渐进式迁移和充分测试降低风险
- **性能影响**: 国际化处理对性能影响微乎其微
- **维护成本**: 通过自动化工具降低长期维护成本

#### 业务风险
- **用户体验**: 错误信息更加清晰，提升用户体验
- **国际化支持**: 为产品国际化奠定基础
- **代码质量**: 提高代码的可维护性和一致性

### 成功案例参考

#### 类似项目经验
- **Spring Framework**: 完善的国际化支持和错误码设计
- **Apache Commons**: 统一的异常处理和错误信息管理
- **阿里巴巴开发手册**: 异常处理和日志规范的最佳实践

#### 预期效果
- **开发效率提升**: 减少重复代码，提高开发效率
- **维护成本降低**: 统一的错误码管理，降低维护成本
- **用户满意度提升**: 清晰的错误信息，提升用户体验
- **国际化就绪**: 为产品国际化做好技术准备

---

## 附录

### A. 常用错误码模板
```java
// 批量操作错误模板
BULK_OPERATION_ERROR(305_002_XXX, "fs.crm.fmcg.wq.bulk.operation.error", "批量{0}操作失败：{1}"),

// 权限检查错误模板
PERMISSION_CHECK_ERROR(305_002_XXX, "fs.crm.fmcg.wq.permission.check.error", "用户无权限执行{0}操作"),

// 数据验证错误模板
DATA_VALIDATION_ERROR(305_002_XXX, "fs.crm.fmcg.wq.data.validation.error", "{0}数据验证失败：{1}"),
```

### B. 国际化资源文件示例
```properties
# 中文资源文件 (messages_zh_CN.properties)
fs.crm.fmcg.wq.bulk.invalid.used.error=以下{0}已被使用，不可作废：{1}
fs.crm.fmcg.wq.invalid.dependency.error={0}[{1}]已被{2}使用，不可作废。使用该{0}的{2}：{3}

# 英文资源文件 (messages_en_US.properties)
fs.crm.fmcg.wq.bulk.invalid.used.error=The following {0} are in use and cannot be invalidated: {1}
fs.crm.fmcg.wq.invalid.dependency.error={0}[{1}] is used by {2} and cannot be invalidated. {2} using this {0}: {3}
```

### C. 单元测试模板
```java
@Test
public void testErrorCodeMessage() {
    // 测试错误码消息格式化
    String message = CheckinsErrorCode.BULK_INVALID_USED_ERROR.getMessage(
        new String[]{"工资项", "基本工资、绩效工资"}
    );
    assertEquals("以下工资项已被使用，不可作废：基本工资、绩效工资", message);
}

@Test
public void testI18nKeyExists() {
    // 测试国际化Key是否存在
    for (CheckinsErrorCode errorCode : CheckinsErrorCode.values()) {
        String message = I18NExt.getOrDefault(errorCode.getI18nKey(), null);
        assertNotNull("Missing i18n key: " + errorCode.getI18nKey(), message);
    }
}
```

---

*本文档版本：v2.0*
*最后更新：2025年7月*
*维护团队：FMCG-WQ开发组*
*审核人员：技术架构组*