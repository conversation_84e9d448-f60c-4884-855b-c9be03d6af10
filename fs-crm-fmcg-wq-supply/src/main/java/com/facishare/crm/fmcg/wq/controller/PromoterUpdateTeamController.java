package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.crmrestapi.common.contants.AccountFieldContants;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 更新成员
 */
public class PromoterUpdateTeamController extends FmcgIdempotentPreDefineController<PromoterUpdateTeamController.Arg, PromoterUpdateTeamController.Result> {
    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);
    @Override
    Result doServiceIdempotent(Arg arg) {
        //重新聚合参数里的删除和新增，把又删除又新增的去掉 需要判空
        if (CollectionUtils.isNotEmpty(arg.getDelIds()) && CollectionUtils.isNotEmpty(arg.getAddIds())) {
            arg.getDelIds().removeAll(arg.getAddIds());
            arg.getAddIds().removeAll(arg.getDelIds());
        }
        //去重 判空
        if (CollectionUtils.isNotEmpty(arg.getDelIds())) {
            arg.setDelIds(arg.getDelIds().stream().distinct().collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(arg.getAddIds())) {
            arg.setAddIds(arg.getAddIds().stream().distinct().collect(Collectors.toList()));
        }
        //修改相关团队
        if (CollectionUtils.isNotEmpty(arg.getDelIds())) {
            pmmService.removePromoterFromTeam(controllerContext.getTenantId(),arg.getOutTenantId(),arg.getOutUserId(), arg.getDelIds(),arg.getApiName());
        }
        if (CollectionUtils.isNotEmpty(arg.getAddIds())) {
            pmmService.addPromoterToTeam(controllerContext.getTenantId(),arg.getOutTenantId(), arg.getOutUserId(), arg.getAddIds(),arg.getApiName());
        }

        return new Result();
    }

    @Override
    protected String getIdempotentKey(Arg arg) {
        return controllerContext.getTenantId() + "PromoterUpdateTeam" + JSON.toJSONString(arg).hashCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Data
    public static class Arg {
        /**
         * apiName
         */
        private String apiName;
        /**
         * 增加对象ids
         */
        private List<String> addIds;
        /**
         * 删除的对象ids
         */
        private List<String> delIds;
        /**
         * 外部成员
         */
        private String outTenantId;
        private String outUserId;
    }

    @Data
    public static class Result {

    }
}