package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.Available.GetProductList;
import com.facishare.crm.fmcg.wq.constants.AvailableAccountObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.service.SyncAvailableRangeService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class AvailableRangeGetProductListController extends PreDefineController<GetProductList.Arg, GetProductList.Result> {
    SupplyDao supplyDao =  SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SyncAvailableRangeService syncAvailableRangeService=SpringUtil.getContext().getBean(SyncAvailableRangeService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    // 可售产品为指定条件不支持
    @Override
    protected GetProductList.Result doService(GetProductList.Arg arg) {
        GetProductList.Result result=new GetProductList.Result();
        if(CollectionUtils.isEmpty(arg.getStoreIds())){
            return result;
        }


        SearchQuery accountQuery=SearchQuery.builder()
                .in(AvailableAccountObjConstants.Field.accountId.getApiName(),arg.getStoreIds()).build();
        List<IObjectData>  data= supplyDao.getAllIObjectDataListByQuery(User.systemUser(controllerContext.getTenantId()),accountQuery,AvailableAccountObjConstants.API_NAME);
        if(CollectionUtils.isNotEmpty(data)){
            Map<String,Set<String>> map=data.stream().collect(Collectors.groupingBy(o->(String)o.get("account_id"),Collectors.mapping(a->(String)a.get("availableRangeId"),Collectors.toSet())));

        }

//        List<IObjectData> availableRangeData= syncAvailableRangeService.getAvailableRangeByAccountIds(controllerContext.getTenantId(),arg.getStoreIds());
//        if(CollectionUtils.isNotEmpty(availableRangeData)){
//
//        }


        return null;
    }
}
