package com.facishare.crm.fmcg.wq;

import com.facishare.crm.fmcg.wq.constants.SupplyChangeFields;
import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;


public enum MCPreDefineObject implements PreDefineObject {
    AreaManage("AreaManageObj"),
    ProductCollection("ProductCollectionObj"),
    CoveredProduct("CoveredProductObj"),
    CoveredStores("CoveredStoresObj"),

    //供货关系设置
    DealerSupply("DealerSupplyObj"),
    //特例供货
    SpecialSupply("SpecialSupplyObj"),
    //供货门店
    SupplyStore("SupplyStoreObj"),
    //拜访路线
    VisitRoute("VisitRouteObj"),
    //线路门店
    RouteCustomer("RouteCustomerObj"),
    //下级配送商
    DistributorSupply("DistributorSupplyObj"),
    Promoter("PromoterObj"),
    PromoterIO("PromoterIOObj"),
    PromotionActivity("PromotionActivityObj"),
    //Account("AccountObj"),

    //付款单
    PaymentOrder("PaymentOrderObj"),
    // 渠道对象
    Channel("ChannelObj"),
    // 人员拜访数据
    UserVisitObj("UserVisitObj"),
//    SuccessfulStoreRange("SuccessfulStoreRangeObj"),
//    ProjectStandards("ProjectStandardsObj"),
//    MustDistributeProducts("MustDistributeProductsObj"),
    SupplyChange("SupplyChangeObj"),
    SupplyChangeCustomerDetail("SupplyChangeCustomerDetailObj"),
    SupplyChangeProductDetail("SupplyChangeProductDetailObj"),
    StorePhotoWallInspect("StorePhotoWallInspectObj"),
    StorePhotoInspectDetail("StorePhotoInspectDetailObj"),
    // 销售区域
    SalesArea("SalesAreaObj"),
    StoreAuthenticationStrategy("store_authentication_strategy__c")
    ;


//    //经营产品明细
//    CoveredProduct(CoveredProductObjConstants.API_NAME),
//    //经营范围
//    ProductCollection(ProductCollectionObjConstants.API_NAME);

    private static final Logger logger = LoggerFactory.getLogger(MCPreDefineObject.class);

    private final String apiName;

    private static final String PACKAGE_NAME = MCPreDefineObject.class.getPackage().getName();

    MCPreDefineObject(String apiName) {
        this.apiName = apiName;
    }

    public static MCPreDefineObject getEnum(String apiName) {
        List<MCPreDefineObject> list = Arrays.asList(MCPreDefineObject.values());
        return list.stream().filter(m -> m.getApiName().equalsIgnoreCase(apiName)).findAny().orElse(null);
    }

    public static void init() {
        for (MCPreDefineObject object : MCPreDefineObject.values()) {

            // todo:zhangchq8550 unnecessary toString()
            logger.info("init {}", object.toString());
            PreDefineObjectRegistry.register(object);
        }
    }

    @Override
    public String getApiName() {
        return this.apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }
}
