package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.util.DurationUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import org.apache.commons.collections.CollectionUtils;

import java.util.Objects;

public class UserVisitObjListController extends StandardListController {

    @Override
    protected Result after(Arg arg, Result result) {
        if(CollectionUtils.isNotEmpty(result.getDataList())){
            result.getDataList().forEach(o->{
                if(Objects.nonNull(o.get("working_hours__c"))){
                    o.put("working_hours__c", DurationUtils.getDurationSecond(Long.valueOf(o.get("working_hours__c").toString())));
                }
                if(Objects.nonNull(o.get("visit_hours__c"))){
                    o.put("visit_hours__c", DurationUtils.getDurationSecond(Long.valueOf(o.get("visit_hours__c").toString())));
                }
                if(Objects.nonNull(o.get("working_hours"))){
                    o.put("working_hours", DurationUtils.getDurationSecond(Long.valueOf(o.get("working_hours").toString())));
                }
                if(Objects.nonNull(o.get("visit_hours"))){
                    o.put("visit_hours", DurationUtils.getDurationSecond(Long.valueOf(o.get("visit_hours").toString())));
                }
            });
        }
        result = super.after(arg, result);
        return super.after(arg, result);
    }

}
