package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.service.RouteService;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2022 - 12 - 29  18:38
 **/
@Slf4j
public class VisitRouteAddAction  extends StandardAddAction {

    private RouteService routeService = SpringUtil.getContext().getBean(RouteService.class);

    @Override
    protected void before(Arg arg) {
        this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("VisitRouteAddAction after is start!");
        IObjectData newObjectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getObjectData().getId(), objectDescribe.getApiName());
        String tenantId = newObjectData.getTenantId();
        String routeId = newObjectData.getId();
        if (!isApprovalFlowStartSuccessOrAsynchronous(routeId)){
            log.info("VisitRouteAddAction no isApprovalFlowStartSuccessOrAsynchronous tenantId:{},routeId:{}",tenantId,routeId);
            routeService.addNoNeedTriggerApprovalFlow(tenantId,routeId);
        }
        return super.after(arg, result);
    }

}
