package com.facishare.crm.fmcg.wq.api.area;


import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.appserver.checkins.api.model.BaseResult;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public interface DealerSupplyUpSave {
    @Data
    @ToString
    class Arg implements Serializable {

        /**
         * 当只传了客户id的时候，返回 经销商id
         */
        @NotEmpty(message = "数据为空，请填写数据")
        @JSONField(name = "object_data")
        @JsonProperty("object_data")
        @SerializedName("object_data")
        private ObjectDataDocument iObjectData;
        @JSONField(name = "no_update")
        @JsonProperty("no_update")
        @SerializedName("no_update")
        private boolean noUpdate;

    }
    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {
    }
}
