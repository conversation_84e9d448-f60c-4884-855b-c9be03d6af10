package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.api.area.MapStoreFilter;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.ProductCollectionConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 *  地图模式左侧列表接口    查询全量的数据  循环查
 */
public class CoveredStoresMapStoreFilterController extends PreDefineController<MapStoreFilter.Arg, MapStoreFilter.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected MapStoreFilter.Result doService(MapStoreFilter.Arg arg) {
        MapStoreFilter.Result result = new MapStoreFilter.Result();
        if(GrayRelease.isAllow("checkin-server-v2","areaManage",controllerContext.getEa())){
            return jmlMapStoreFilter(arg);
        }else if (GrayRelease.isAllow("checkin-server-v2", "isYLArea",controllerContext.getEa())){
            return ylMapStoreFilter(arg);
        }
        return result;
    }

    private MapStoreFilter.Result ylMapStoreFilter(MapStoreFilter.Arg arg) {
        List<String> county = arg.getCounty();
        MapStoreFilter.Result result= new MapStoreFilter.Result();
        List<JSONObject> data = Lists.newArrayList();
        if(CollectionUtils.empty(county)){
            result.setDataList(data);
            return result;
        }
        List<IFilter> filters = Lists.newArrayList();
        Filter recordFilter = new Filter();
        recordFilter.setFieldName(CommonConstants.RECORD_TYPE);
        recordFilter.setOperator(Operator.EQ);
        recordFilter.setFieldValues(Lists.newArrayList(CommonConstants.RECORD_DEFAULT));
        filters.add(recordFilter);

        //归属部门
        Filter deptFilter = new Filter();
        deptFilter.setFieldName(CommonConstants.DATA_OWN_DEPARTMENT);
        deptFilter.setOperator(Operator.IN);
        deptFilter.setFieldValues(county);
        deptFilter.setIsCascade(true);
        filters.add(deptFilter);

        //所属片区为空
        Filter areaFilter = new Filter();
        areaFilter.setFieldName("belong_routeNew__c");
        areaFilter.setOperator(Operator.IS);
        areaFilter.setFieldValues(Lists.newArrayList());
        filters.add(areaFilter);

        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(CommonConstants.CREATE_TIME);
        orderBy.setIsAsc(true);
        List<OrderBy> order = Lists.newArrayList(orderBy);
        IObjectDescribe describeExt = this.serviceFacade.findObject(controllerContext.getTenantId(),CommonConstants.ACCOUNT_OBJ);

        int limitSize = 200;
        int totalSize = 1;
        int i = 0;
        int offset = 0;
        while (true) {
            offset = i * limitSize;
            if (totalSize - offset <= 0) {
                break;
            }
            SearchTemplateQuery newQuery = new SearchTemplateQuery();
            newQuery.setLimit(limitSize);
            newQuery.setOffset(offset);
            newQuery.setOrders(order);

            newQuery.setFilters(filters);
            try{
                QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), CommonConstants.ACCOUNT_OBJ, newQuery);
                totalSize = queryResult.getTotalNumber();
                serviceFacade.fillUserInfo(describeExt, queryResult.getData(),controllerContext.getUser());
//                log.info("data dddddd:{}",queryResult.getData());
                if(CollectionUtils.notEmpty(queryResult.getData())){
                    for (IObjectData datum : queryResult.getData()) {
                        JSONObject info = new JSONObject();
                        info.put(CommonConstants.ID,datum.getId());
                        info.put(CommonConstants.Account_NO,datum.get(CommonConstants.Account_NO));
                        info.put(CommonConstants.ACCOUNT_LOCATION,datum.get(CommonConstants.ACCOUNT_LOCATION));
                        info.put(CommonConstants.NAME,datum.getName());
                        info.put("owner__r",datum.get("owner__r"));
                        data.add(info);
                    }
                }
            }catch (Exception e){
                log.error("CoveredStoresMapStoreFilterController query accountInfo error",e);
            }
            i++;
        }
        result.setDataList(data);
        return result;
    }

    private MapStoreFilter.Result jmlMapStoreFilter(MapStoreFilter.Arg arg) {
        String productGroupId=arg.getProductGroupId();
        String areaAttributeName = arg.getAreaAttributeName();
        List<String> county = arg.getCounty();
        MapStoreFilter.Result result= new MapStoreFilter.Result();
        List<JSONObject> data = Lists.newArrayList();
        if(StringUtils.isEmpty(productGroupId) || StringUtils.isEmpty(areaAttributeName) || CollectionUtils.empty(county)){
            result.setDataList(data);
            return result;
        }
        IObjectDescribe describeExt = this.serviceFacade.findObject(controllerContext.getTenantId(),CommonConstants.ACCOUNT_OBJ);
        IObjectData productGroup = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()),productGroupId, ProductCollectionConstants.PRODUCT_COLLECTION_OBJ);
        String productGroupName = (String)productGroup.get(CommonConstants.NAME);
        boolean allPro = Objects.nonNull(productGroup.get(ProductCollectionConstants.ALL_PRODUCT))?(Boolean)productGroup.get(ProductCollectionConstants.ALL_PRODUCT):false;
        Wheres wheres = new Wheres();
        Wheres areaWheres = new Wheres();
        Filter recordFilter = new Filter();
        recordFilter.setFieldName(CommonConstants.RECORD_TYPE);
        recordFilter.setOperator(Operator.EQ);
        recordFilter.setFieldValues(Lists.newArrayList(CommonConstants.RECORD_DEFAULT));

        Filter deptFilter = new Filter();
        deptFilter.setFieldName(CommonConstants.DATA_OWN_DEPARTMENT);
        deptFilter.setOperator(Operator.IN);
        deptFilter.setFieldValues(county);
        deptFilter.setIsCascade(true);
        if(allPro){
            Filter filter = new Filter();
            filter.setFieldName(CommonConstants.ACCOUNT_AREA);
            filter.setOperator(Operator.IS);
            filter.setFieldValues(Lists.newArrayList());
            wheres.setFilters(Lists.newArrayList(filter,recordFilter,deptFilter));

            Filter areaFilter = new Filter();
            areaFilter.setFieldName(CommonConstants.ACCOUNT_ATTRIBUTE);
            areaFilter.setOperator(Operator.NLIKE);
            areaFilter.setFieldValues(Lists.newArrayList(areaAttributeName));

            areaWheres.setFilters(Lists.newArrayList(areaFilter,recordFilter,deptFilter));

        }else {
            String attrPro = areaAttributeName + "-" + productGroupName;
            String attrAllPro = areaAttributeName + "-全品"; //ignoreI18n

            Filter proFilter = new Filter();
            proFilter.setFieldName(CommonConstants.ACCOUNT_PRODUCT);
            proFilter.setOperator(Operator.NLIKE);
            proFilter.setFieldValues(Lists.newArrayList(attrPro));

            Filter proAllFilter = new Filter();
            proAllFilter.setFieldName(CommonConstants.ACCOUNT_PRODUCT);
            proAllFilter.setOperator(Operator.NLIKE);
            proAllFilter.setFieldValues(Lists.newArrayList(attrAllPro));

            areaWheres.setFilters(Lists.newArrayList(proAllFilter,proFilter,recordFilter,deptFilter));

            Filter areaNonFilter = new Filter();
            areaNonFilter.setFieldName(CommonConstants.ACCOUNT_ATTRIBUTE);
            areaNonFilter.setOperator(Operator.NLIKE);
            areaNonFilter.setFieldValues(Lists.newArrayList(areaAttributeName));

            wheres.setFilters(Lists.newArrayList(areaNonFilter,recordFilter,deptFilter));

        }

        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(CommonConstants.CREATE_TIME);
        orderBy.setIsAsc(true);
        List<OrderBy> order = Lists.newArrayList(orderBy);
        int limitSize = 200;
        int totalSize = 1;
        int i = 0;
        int offset = 0;
        while (true) {
            offset = i * limitSize;
            if (totalSize - offset <= 0) {
                break;
            }
            SearchTemplateQuery newQuery = new SearchTemplateQuery();
            newQuery.setLimit(limitSize);
            newQuery.setOffset(offset);
            newQuery.setOrders(order);

            newQuery.setWheres(Lists.newArrayList(wheres, areaWheres));
            try{
                QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), CommonConstants.ACCOUNT_OBJ, newQuery);
                totalSize = queryResult.getTotalNumber();
                serviceFacade.fillUserInfo(describeExt, queryResult.getData(),controllerContext.getUser());
//                log.info("data dddddd:{}",queryResult.getData());
                if(CollectionUtils.notEmpty(queryResult.getData())){
                    for (IObjectData datum : queryResult.getData()) {
                        JSONObject info = new JSONObject();
                        info.put(CommonConstants.ID,datum.getId());
                        info.put(CommonConstants.Account_NO,datum.get(CommonConstants.Account_NO));
                        info.put(CommonConstants.ACCOUNT_LOCATION,datum.get(CommonConstants.ACCOUNT_LOCATION));
                        info.put(CommonConstants.NAME,datum.getName());
                        info.put("owner__r",datum.get("owner__r"));
                        data.add(info);
                    }
                }
            }catch (Exception e){
                log.error("CoveredStoresMapStoreFilterController query accountInfo error",e);
            }
            i++;
        }
        result.setDataList(data);
        return result;
    }
}
