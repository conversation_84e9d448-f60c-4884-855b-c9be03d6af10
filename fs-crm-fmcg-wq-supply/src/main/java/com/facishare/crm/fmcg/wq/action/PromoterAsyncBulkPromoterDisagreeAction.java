package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterAsyncBulkPromoterDisagreeAction extends AbstractStandardAsyncBulkAction<PromoterAsyncBulkPromoterDisagreeAction.Arg, PromoterPromoterDisagreeAction.Arg> {




    @Override
    protected String getDataIdByParam(PromoterPromoterDisagreeAction.Arg arg) {
        return arg.getObjectDataId();
    }

    @Override
    protected List<PromoterPromoterDisagreeAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(id -> {
                    PromoterPromoterDisagreeAction.Arg arg = new PromoterPromoterDisagreeAction.Arg();
                    arg.setObjectDataId(id);
                    return arg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.PROMOTER_DISAGREE.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.PROMOTER_DISAGREE.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.PROMOTER_DISAGREE.getActionCode());
    }

    @Data
    public static class Arg {
        private List<String> dataIds;
    }

}
