package com.facishare.crm.fmcg.wq.action;


import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.constants.DistributorSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 供货关系作废
 * @author: zhangsm
 * @create: 2021-04-29 14:42
 **/
public class DistributorSupplyInvalidAction extends StandardInvalidAction {

    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //初始化审批流上下文
        supplyOperaContext.setArgs(arg);
        supplyOperaContext.setResultClazz(Result.class);
        IObjectData byId = supplyDao.getById(actionContext.getTenantId(), DistributorSupplyObjConstants.API_NAME, arg.getObjectDataId());
        supplyOperaContext.setOutUpCustomerId(byId.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName()).toString());
        supplyOperaContext.setOutUpSupplyId(byId.get(DistributorSupplyObjConstants.Field.upSupplyId.getApiName()).toString());
        supplyOperaContext.setCustermerIds(Lists.newArrayList(byId.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()).toString()));
        //触发审批流程
        Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(actionContext, supplyOperaContext);
        if (booleanIObjectDataPair.first){
            throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE,SupplyOperaContext.TRIGGERAPPROCODE);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        ObjectDataDocument objectData = after.getObjectData();
        String thisDealerId = objectData.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()).toString();
        List<String> delDealerIds = Lists.newArrayList(thisDealerId);
        String tenantId = actionContext.getTenantId();
        //删除对应的特殊门店
        fmcgThreadPoolExecutor.execute(()->{
            supplyDao.delSpecialStore(tenantId,delDealerIds);
            supplyDao.updateDistributorUpSupplyFields(tenantId,thisDealerId);
            supplyDao.updateCoveredProduct(tenantId,thisDealerId);
        });
        supplyService.addAvailableObjSyncTask(actionContext.getTenantId(), result.getObjectData()
                .get(DistributorSupplyObjConstants.Field.upSupplyId.getApiName()).toString());
        return after;
    }
}
