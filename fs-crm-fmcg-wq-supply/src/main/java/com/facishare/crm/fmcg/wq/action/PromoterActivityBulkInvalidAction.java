package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import lombok.extern.slf4j.Slf4j;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterActivityBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {
        //跳过基础校验
        this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        super.before(arg);
    }

}
