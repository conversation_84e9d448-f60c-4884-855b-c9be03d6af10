package com.facishare.crm.fmcg.wq.dao;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.common.SupplyOperateEnum;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.AvailableAccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.AvailablePriceBookConstants;
import com.facishare.crm.fmcg.wq.constants.AvailableProductObjConstants;
import com.facishare.crm.fmcg.wq.constants.AvailableRangeObjConstants;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredProductObjConstants;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.constants.DistributorSupplyObjConstants;
import com.facishare.crm.fmcg.wq.constants.PriceBookObjConstants;
import com.facishare.crm.fmcg.wq.constants.ProductCollectionObjConstants;
import com.facishare.crm.fmcg.wq.constants.SpecialSupplyObjConstants;
import com.facishare.crm.fmcg.wq.constants.SupplyChangeCustomerDetailFields;
import com.facishare.crm.fmcg.wq.constants.SupplyChangeFields;
import com.facishare.crm.fmcg.wq.constants.SupplyChangeProductDetailFields;
import com.facishare.crm.fmcg.wq.constants.SupplyStoreObjConstants;
import com.facishare.crm.fmcg.wq.model.DistributorSupply;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.model.obj.ProductCollectionObj;
import com.facishare.crm.fmcg.wq.model.obj.ProductObj;
import com.facishare.crm.fmcg.wq.model.obj.ShopProductSupplierRelationshipObj;
import com.facishare.crm.fmcg.wq.model.yldb.YLTempSubProduct;
import com.facishare.crm.fmcg.wq.model.yldb.YLTempSupplyEntity;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.enterprise.common.util.CollectionUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * c
 *
 * @program: fs-crm-fmcg
 * @description: 基础数据查询
 * @author: zhangsm
 * @create: 2021-04-21 18:15
 **/
@Slf4j
@Component
public class SupplyDao extends AbstractDao implements SupplyDaoInterface {
    @Autowired
    SupplyService supplyService;

    @Override
    public ProductCollectionObj getProductCollectionObjByAccountId(String tenantId, String supplierId, boolean isQueryProduct) {
        return getProductCollectionObjListByAccountIds(tenantId, Lists.newArrayList(supplierId), isQueryProduct).get(0);
    }

    public Map<String, Set<String>> delDataByDealerSupply(IObjectData dealerSupply) {
        Map<String, Set<String>> map = new HashMap<>();
        String dealerSupplyId = dealerSupply.getId();
        String tenantId = dealerSupply.getTenantId();
        //删除供货门店
        Set<String> delStoreIds = delByApiName(tenantId, SupplyStoreObjConstants.API_NAME, SupplyStoreObjConstants.Field.upSupplyId.getApiName(), dealerSupplyId);
        map.put(SupplyStoreObjConstants.API_NAME, delStoreIds);
        //删除供货分销
        Set<String> dsids = delByApiName(tenantId, DistributorSupplyObjConstants.API_NAME, DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), dealerSupplyId);
        map.put(DistributorSupplyObjConstants.API_NAME, dsids);
        //删除供货特例
        Set<String> ssIds = delByApiName(tenantId, SpecialSupplyObjConstants.API_NAME, SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyId);
        map.put(SpecialSupplyObjConstants.API_NAME, ssIds);
        //删除可售范围
        Set<String> arIds = delByApiName(tenantId, AvailableRangeObjConstants.API_NAME, AvailableRangeObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyId);
        map.put(AvailableRangeObjConstants.API_NAME, arIds);
        String delear = dealerSupply.get("delear", String.class);
        if (StringUtils.isNotBlank(delear)) {
            //删除经营范围
            SearchQuery.SearchQueryBuilder builder = SearchQuery.builder();
            builder.eq(ProductCollectionObjConstants.Field.dealerId.getApiName(), delear);
            SearchQuery.SearchQueryBuilder builder2 = SearchQuery.builder();
            builder2.eq(ProductCollectionObjConstants.Field.distributorId.getApiName(), delear);
            builder.addOrWheres(builder2);
            Set<String> pcIds = delByApiName(tenantId, ProductCollectionObjConstants.API_NAME, builder);
            map.put(ProductCollectionObjConstants.API_NAME, pcIds);
        }
        //删除供货关系
        Set<String> id = delByApiName(tenantId, DealerSupplyObjConstants.API_NAME, "_id", dealerSupplyId);
        map.put(DealerSupplyObjConstants.API_NAME, id);
        return map;
    }

    private Set<String> delByApiName(String tenantId, String apiName, String fieldId, String supplyId) {
        return delByApiName(tenantId, apiName, SearchQuery.builder().eq(fieldId, supplyId));
    }

    private Set<String> delByApiName(String tenantId, String apiName, SearchQuery.SearchQueryBuilder builder) {
        Set<String> ids = batchInvalidAndDelByQuery(User.systemUser(tenantId), builder, apiName).keySet();
        if (CollectionUtils.isNotEmpty(ids)) {
            log.info("del data apiName : {},dataIds {}", apiName, ids.stream().collect(Collectors.joining(",")));
        }
        return ids;
    }

    @Override
    public List<DistributorSupply> getDistributorSupplyByUpIds(String tenantId, List<String> supplierIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), supplierIds).build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
        if (CollectionUtils.isEmpty(data)) {
//            throw new ObjectDataNotFoundException(
//                    String.format("%s ，%s 查询为空",
//                            DistributorSupplyObjConstants.Field.thisDealerId.getLabel(),
//                            DistributorSupplyObjConstants.DISPLAY_NAME));
        }
        return data.stream().map(o -> convertDistributorSupply(o)).collect(Collectors.toList());
    }

    @Override
    public List<IObjectData> getDistributorSupplyByUpSupplyIds(String tenantId, List<String> supplierIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), supplierIds).build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
        return data;
    }

    /**
     * 参数 是本级别 的id   是所有的父级 关系
     *
     * @param supplierIds
     * @return
     */
    @Override
    public List<DistributorSupply> getDistributorSupplyByThisIds(String tenantId, List<String> supplierIds) {
        List<IObjectData> data = getDistributorSupplyObjByThisIds(tenantId, supplierIds, Lists.newArrayList(BaseField.name.getApiName(),
                DistributorSupplyObjConstants.Field.specialSupply.getApiName(),
                DistributorSupplyObjConstants.Field.upSupplyId.getApiName(),
                DistributorSupplyObjConstants.Field.thisDealerId.getApiName(),
                DistributorSupplyObjConstants.Field.upDealerId.getApiName()));
        if (CollectionUtils.isEmpty(data)) {
//            throw new ObjectDataNotFoundException(
//                    String.format("%s ，%s 查询为空",
//                            DistributorSupplyObjConstants.Field.thisDealerId.getLabel(),
//                            DistributorSupplyObjConstants.DISPLAY_NAME));
        }
        return data.stream().map(o -> convertDistributorSupply(o)).collect(Collectors.toList());
    }

    private List<IObjectData> getDistributorSupplyObjByThisIds(String tenantId, List<String> supplierIds, List<String> fields) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), supplierIds).build();
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME, fields);
    }

    private List<IObjectData> getDistributorSupplyObj(String tenantId, List<String> supplierIds, String dealerSupplyId) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), dealerSupplyId)
                .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), supplierIds).build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
    }

    @Override
    public List<IObjectData> getCircularReferenceDistributorSupply(String tenantId, String dataId, String thisSupplierId, String upSupplierId) {
        SearchQuery.SearchQueryBuilder where2 = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), Lists.newArrayList(upSupplierId));
        SearchQuery.SearchQueryBuilder where1 = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), Lists.newArrayList(thisSupplierId));
        if (StringUtils.isNotBlank(dataId)) {
            where1.neq(BaseField.id.getApiName(), dataId);
            where2.neq(BaseField.id.getApiName(), dataId);
        }
        SearchQuery searchQuery = where1.addOrWheres(where2).build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
        return data;
    }

    @Override
    public List<IObjectData> getCircularReferenceDistributorSupply(String tenantId, String thisSupplierId, List<String> upSupplierIds) {
        if (upSupplierIds.contains(thisSupplierId)) {
            throw new ValidateException("数据循环引用"); //ignoreI18n

        }
        SearchQuery.SearchQueryBuilder where1 = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), Lists.newArrayList(thisSupplierId))
                .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), upSupplierIds);
        SearchQuery searchQuery = where1.build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
        if (CollectionUtils.isNotEmpty(data)) {
            throw new ValidateException("数据存在循环引用"); //ignoreI18n
        }
        return data;
    }

    @Override
    public List<IObjectData> getDistributorSuppliers(String tenantId, String dataId, String thisSupplierId, String upSupplierId) {
        return getDistributorSuppliers(tenantId, dataId, Lists.newArrayList(thisSupplierId), Lists.newArrayList(upSupplierId));
    }

    @Override
    public List<IObjectData> getDistributorSuppliers(String tenantId, String dataId, String thisSupplierId, List<String> upSupplierIds) {
        return getDistributorSuppliers(tenantId, dataId, Lists.newArrayList(thisSupplierId), upSupplierIds);
    }


    @Override
    public List<IObjectData> getDistributorSuppliers(String tenantId, String dataId, List<String> thisSupplierIds, String upSupplierId) {
        return getDistributorSuppliers(tenantId, dataId, thisSupplierIds, Lists.newArrayList(upSupplierId));
    }

    private List<IObjectData> getDistributorSuppliers(String tenantId, String dataId, List<String> thisSupplierIds, List<String> upSupplierIds) {
        SearchQuery.SearchQueryBuilder where1 = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), upSupplierIds)
                .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), thisSupplierIds);
        if (StringUtils.isNotBlank(dataId)) {
            where1.neq(BaseField.id.getApiName(), dataId);
        }
        SearchQuery searchQuery = where1.build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
        return data;
    }

    private List<IObjectData> getDistributorSuppliersByUpIds(String tenantId, String dataId, List<String> upSupplierIds) {
        SearchQuery.SearchQueryBuilder where1 = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), upSupplierIds);
//                .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), thisSupplierIds);
        if (StringUtils.isNotBlank(dataId)) {
            where1.neq(BaseField.id.getApiName(), dataId);
        }
        SearchQuery searchQuery = where1.build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
        return data;
    }

    private List<IObjectData> getDistributorSuppliersByDownIds(String tenantId, String dataId, List<String> upSupplierIds) {
        SearchQuery.SearchQueryBuilder where1 = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), upSupplierIds);
//                .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), thisSupplierIds);
        if (StringUtils.isNotBlank(dataId)) {
            where1.neq(BaseField.id.getApiName(), dataId);
        }
        SearchQuery searchQuery = where1.build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
        return data;
    }

    @Override
    public List<IObjectData> getDealerSupplyBySuppliers(String tenantId, List<String> suppliers) {
        return getDealerSuppliers(tenantId, suppliers, true);
    }

    @NotNull
    private List<IObjectData> getDealerSuppliers(String tenantId, List<String> suppliers, boolean preCreate) {
        suppliers = suppliers.stream().distinct().collect(Collectors.toList());
        SearchQuery searchQuery = SearchQuery.builder()
                .in(DealerSupplyObjConstants.Field.dealerId.getApiName(), suppliers).build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DealerSupplyObjConstants.API_NAME);
        if (preCreate && data.size() < suppliers.size()) {
            //数据对不上
            Set<String> dataDealerIds = data.stream().map(e -> e.get(DealerSupplyObjConstants.Field.dealerId.getApiName()
                    , String.class)).collect(Collectors.toSet());
            List<String> addDealerIds = suppliers.stream().filter(o ->
                    !dataDealerIds.contains(o)
            ).collect(Collectors.toList());
            data.addAll(createDealerSupplyObj(tenantId, "-10000", addDealerIds));
        }
        return data;
    }

    public List<IObjectData> getDealerSupplyBySuppliersNOPreCreate(String tenantId, List<String> suppliers) {
        return getDealerSuppliers(tenantId, suppliers, false);
    }


    private DistributorSupply convertDistributorSupply(IObjectData o) {
        DistributorSupply distributorSupply = new DistributorSupply();

        distributorSupply.setUpDealerAccountId(o.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), String.class));
        distributorSupply.setThisDistributorAccountId(o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class));
        distributorSupply.setUpSupplyId(o.get(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), String.class));
        distributorSupply.setSpecialSupply(o.get(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), Boolean.class));
//        distributorSupply.setUpSupplierName(getAccountObjById(o.getTenantId(), distributorSupply.getUpSupplierId()).getName());
//        distributorSupply.setThisSupplierName(getAccountObjById(o.getTenantId(), distributorSupply.getThisSupplierId()).getName());
        distributorSupply.setName(o.get(BaseField.name.getApiName(), String.class));
        distributorSupply.setId(o.getId());
        return distributorSupply;
    }

    @Override
    public List<ProductCollectionObj> getProductCollectionObjListByAccountIds(String tenantId, List<String> accountIds, boolean isQueryProduct) {
        List<IObjectData> data = queryCollectionObjByLeaderIds(tenantId, accountIds);
        if (CollectionUtils.isEmpty(data) || data.size() != accountIds.size()) {
            List<String> dataIds = data.stream().map(o -> {
                String distributorId = o.get(ProductCollectionObjConstants.Field.distributorId.getApiName(), String.class);
                if (StringUtils.isBlank(distributorId)) {
                    return o.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class);
                } else {
                    return distributorId;
                }
            }).collect(Collectors.toList());
            //初始化
            List<String> addAccountIds = accountIds.stream().filter(o -> !dataIds.contains(o)).collect(Collectors.toList());

            data.addAll(createDefaultProductCollection(tenantId, addAccountIds));
        }
        return data.stream().map(o -> convertProductCollectionObj(tenantId, o, isQueryProduct)).collect(Collectors.toList());
    }

    public List<IObjectData> queryCollectionObjByLeaderIds(String tenantId, List<String> accountIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(ProductCollectionObjConstants.Field.distributorId.getApiName(), accountIds)
                .eq(BaseField.recordType.getApiName(), ProductCollectionObjConstants.Value.recordType_distributor.getValue())
                .addOrWheres(SearchQuery.builder()
                        .in(ProductCollectionObjConstants.Field.dealerId.getApiName(), accountIds)
                        .eq(BaseField.recordType.getApiName(), ProductCollectionObjConstants.Value.recordType_dealer.getValue()))
                .build();
        List<IObjectData> iObjectDataListByQueryAndApiName = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, ProductCollectionObjConstants.API_NAME);
        //判断distributorId或者dealerId已经重复出现在iObjectDataListByQueryAndApiName里则删除重复
        if (CollectionUtils.isNotEmpty(iObjectDataListByQueryAndApiName)) {
            Set<String> ids = new HashSet<>();
            List<IObjectData> list = new ArrayList<>();
            for (IObjectData iObjectData : iObjectDataListByQueryAndApiName) {
                String distributorId = iObjectData.get(ProductCollectionObjConstants.Field.distributorId.getApiName(), String.class);
                if (StringUtils.isBlank(distributorId)) {
                    distributorId = iObjectData.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class);
                }
                if (ids.contains(distributorId)) {
                    list.add(iObjectData);
                } else {
                    ids.add(distributorId);
                }
            }
            iObjectDataListByQueryAndApiName.removeAll(list);
            //删除重复的数据
            batchInvalidAndDelOfSingleApiName(list, User.systemUser(tenantId));
        }


        if (CollectionUtils.isEmpty(iObjectDataListByQueryAndApiName) || accountIds.size() != iObjectDataListByQueryAndApiName.size()) {
            return getAllIObjectDataListByQueryWithDb(User.systemUser(tenantId), searchQuery, ProductCollectionObjConstants.API_NAME);
        }
        return iObjectDataListByQueryAndApiName;
    }

    @Override
    public AccountObj getAccountObjById(String tenantId, String shopId) {
        return getAccountObjByIds(tenantId, Lists.newArrayList(shopId)).get(0);
    }

    @Override
    public List<AccountObj> getAccountObjByIds(String tenantId, Collection<String> accountIdList) {
        if (CollectionUtils.isEmpty(accountIdList)) {
            return ListUtils.EMPTY_LIST;
        }
        Set<String> accountIds = Sets.newHashSet(accountIdList);
        SearchQuery searchQuery = SearchQuery.builder()
                .in(BaseField.id.getApiName(), accountIds)
                .build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, AccountObjConstants.API_NAME);
        if (CollectionUtils.isEmpty(data) || accountIds.size() != data.size()) {
            data = getAllIObjectDataListByQueryWithDb(User.systemUser(tenantId), searchQuery, AccountObjConstants.API_NAME);
            if (CollectionUtils.isEmpty(data) || accountIds.size() != data.size()) {
                List<String> existIds = data.stream().map(o -> o.get("_id", String.class)).collect(Collectors.toList());
                accountIds.removeAll(existIds);
                throw new ObjectDataNotFoundException(
                        String.format("%s 查询为空 id %s", //ignoreI18n
                                AccountObjConstants.DISPLAY_NAME, accountIds.toString()));
            }
        }
        return data.stream().map(o -> convertAccountObj(o)).collect(Collectors.toList());
    }

    @Override
    public List<AccountObj> getAccountObjByIdsIgnoreException(String tenantId, Collection<String> accountIdList) {
        if (CollectionUtils.isEmpty(accountIdList)) {
            return ListUtils.EMPTY_LIST;
        }
        Set<String> accountIds = Sets.newHashSet(accountIdList);
        SearchQuery searchQuery = SearchQuery.builder()
                .in(BaseField.id.getApiName(), accountIds)
                .build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, AccountObjConstants.API_NAME);
        if (CollectionUtils.isEmpty(data)) {
            return ListUtils.EMPTY_LIST;
        }
        return data.stream().map(o -> convertAccountObj(o)).collect(Collectors.toList());
    }

    public List<String> getNoDeleteByIdsIgnoreException(String tenantId, Collection<String> accountIdList) {
        if (CollectionUtils.isEmpty(accountIdList)) {
            return ListUtils.EMPTY_LIST;
        }
        Set<String> accountIds = Sets.newHashSet(accountIdList);
        SearchQuery searchQuery = SearchQuery.builder()
                .in(BaseField.id.getApiName(), accountIds)
                .build();
        List<IObjectData> data = getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, AccountObjConstants.API_NAME, Lists.newArrayList(BaseField.id.getApiName()));
        if (CollectionUtils.isEmpty(data)) {
            return ListUtils.EMPTY_LIST;
        }
        return data.stream().map(o -> o.getId()).collect(Collectors.toList());
    }

    private AccountObj convertAccountObj(IObjectData iObjectData) {
        AccountObj accountObj = new AccountObj();
        List departmentId = iObjectData.get(AccountObjConstants.Field.ownDepartment.getApiName(), List.class);
        if (CollectionUtils.isNotEmpty(departmentId)) {
            accountObj.setDepartmentId(departmentId.get(0).toString());
        }
        List otherList = iObjectData.get(AccountObjConstants.Field.ownDepartment.getApiName(), List.class);
        if (CollectionUtils.isNotEmpty(otherList)) {
            List<String> list = (List<String>) otherList.stream().map(o -> o.toString()).collect(Collectors.toList());
            accountObj.setOtherDepartmentIds(list);
        }
        accountObj.setRecordType(iObjectData.getRecordType());
        accountObj.setId(iObjectData.getId());
        accountObj.setName(iObjectData.getName());
        accountObj.setOwner(iObjectData.getOwner().get(0));
        return accountObj;
    }


    @Override
    public List<ShopProductSupplierRelationshipObj> getSupplyStoresByShopId(String tenantId, String shopId) {

        List<IObjectData> data = getSupplyStoreByShopIds(tenantId, Lists.newArrayList(shopId),
                Lists.newArrayList(SupplyStoreObjConstants.Field.specialSupply.getApiName(),
                        SupplyStoreObjConstants.Field.upDealerId.getApiName(),
                        SupplyStoreObjConstants.Field.thisDealerId.getApiName()));
        return convertRelationBySupplyStoreObj(data);
    }

    private List<IObjectData> getSupplyStoreByShopIdAndSupplierIds(String tenantId, String accountId, List<String> supplierIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), accountId)
                .in(SupplyStoreObjConstants.Field.upSupplyId.getApiName(), supplierIds)
                .build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME);
    }

    private List<IObjectData> getSupplyStoreByShopIds(String tenantId, List<String> shopIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), shopIds)
                .build();
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME, null);
    }

    private List<IObjectData> getSupplyStoreByShopIds(String tenantId, List<String> shopIds, List<String> fields) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), shopIds)
                .build();
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME, fields);
    }

    public List<IObjectData> getSupplyStoreByDealerIds(String tenantId, List<String> dealers) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(SupplyStoreObjConstants.Field.upDealerId.getApiName(), dealers)
                .build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME);
    }

    public List<String> getSupplyStoreAccountIdsByDealerIds(String tenantId, List<String> dealers) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(SupplyStoreObjConstants.Field.upDealerId.getApiName(), dealers)
                .build();
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME, Lists.newArrayList(SupplyStoreObjConstants.Field.thisDealerId.getApiName()))
                .stream().map(o -> o.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)).collect(Collectors.toList());
    }

    private List<IObjectData> getSupplyStore(String tenantId, List<String> shopIds, String dealerSupplyId) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(SupplyStoreObjConstants.Field.upSupplyId.getApiName(), dealerSupplyId)
                .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), shopIds)
                .build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME);
    }

    private List<IObjectData> getSupplyStores(String tenantId, List<String> shopIds, List<String> dealerIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(SupplyStoreObjConstants.Field.upDealerId.getApiName(), dealerIds)
                .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), shopIds)
                .build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME);
    }


    private List<ShopProductSupplierRelationshipObj> convertRelationBySupplyStoreObj(List<IObjectData> data) {
        List<ShopProductSupplierRelationshipObj> shopProductSupplierRelationshipObjlist = Lists.newArrayList();
        for (IObjectData iObjectData : data) {
            shopProductSupplierRelationshipObjlist.add(convertFromSupplyStoreObj(iObjectData));
        }
        return shopProductSupplierRelationshipObjlist;
    }

    private ShopProductSupplierRelationshipObj convertFromSupplyStoreObj(IObjectData iObjectData) {
        ShopProductSupplierRelationshipObj shopProductSupplierRelationshipObj = new ShopProductSupplierRelationshipObj();
        shopProductSupplierRelationshipObj.setShopId(iObjectData.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class));
        shopProductSupplierRelationshipObj.setSupplierId(iObjectData.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class));
        shopProductSupplierRelationshipObj.setSpecial(iObjectData.get(SupplyStoreObjConstants.Field.specialSupply.getApiName(), Boolean.class, false));
        shopProductSupplierRelationshipObj.setId(iObjectData.get(BaseField.id.getApiName(), String.class));
        return shopProductSupplierRelationshipObj;
    }

    @Override
    public List<ShopProductSupplierRelationshipObj> getSpecialSupplyByShopId(String tenantId, String shopId) {
        List<String> shopIds = Lists.newArrayList(shopId);
        List<IObjectData> data = getSpecialSupplyByShopIds(tenantId, shopIds, Lists.newArrayList(SpecialSupplyObjConstants.Field.productId.getApiName(),
                SpecialSupplyObjConstants.Field.shopId.getApiName(),
                SpecialSupplyObjConstants.Field.dealerId.getApiName(),
                BaseField.id.getApiName()));
        if (CollectionUtils.isEmpty(data)) {
            return ListUtils.EMPTY_LIST;
        }
        return convertRelationBySpecialSupplyObj(data);
    }

    public List<ShopProductSupplierRelationshipObj> getSpecialSupplyByDealerId(String tenantId, String dealerId, List<String> specialAccountIds) {
        List<ShopProductSupplierRelationshipObj> result = Lists.newArrayList();
        Collections.synchronizedList(result);
        specialAccountIds.stream().parallel().forEach(o -> {

            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(SpecialSupplyObjConstants.Field.dealerId.getApiName(), dealerId)
                    .eq(SpecialSupplyObjConstants.Field.shopId.getApiName(), o)
                    .build();
            List<IObjectData> data = getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SpecialSupplyObjConstants.API_NAME, Lists.newArrayList(
                    SpecialSupplyObjConstants.Field.dealerId.getApiName(),
                    BaseField.id.getApiName(),
                    SpecialSupplyObjConstants.Field.shopId.getApiName(),
                    SpecialSupplyObjConstants.Field.productId.getApiName()
            ));
            result.addAll(Collections.synchronizedList(convertRelationBySpecialSupplyObj(data)));
        });
        return result;
    }

    public List<ShopProductSupplierRelationshipObj> getSpecialSupplyByDealerId(String tenantId, String dealerId, String specialAccountId) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(SpecialSupplyObjConstants.Field.dealerId.getApiName(), dealerId)
                .eq(SpecialSupplyObjConstants.Field.shopId.getApiName(), specialAccountId)
                .build();
        List<IObjectData> data = getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SpecialSupplyObjConstants.API_NAME, Lists.newArrayList(
                SpecialSupplyObjConstants.Field.dealerId.getApiName(),
                BaseField.id.getApiName(),
                SpecialSupplyObjConstants.Field.shopId.getApiName(),
                SpecialSupplyObjConstants.Field.productId.getApiName()
        ));
        return convertRelationBySpecialSupplyObj(data);
    }

    private List<IObjectData> getSpecialSupplyByShopIds(String tenantId, List<String> shopIds, List<String> fields) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(SpecialSupplyObjConstants.Field.shopId.getApiName(), shopIds)
                .build();
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SpecialSupplyObjConstants.API_NAME, fields);
    }

    private List<IObjectData> getSpecialSupplyByShopIds(String tenantId, List<String> shopIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(SpecialSupplyObjConstants.Field.shopId.getApiName(), shopIds)
                .build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, SpecialSupplyObjConstants.API_NAME);
    }

    @Override
    public List<IObjectData> getSpecialSupplyByShopIds(String tenantId, String accountId, List<String> supplierIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(SpecialSupplyObjConstants.Field.shopId.getApiName(), accountId)
                .in(SpecialSupplyObjConstants.Field.dealerId.getApiName(), supplierIds)
                .build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, SpecialSupplyObjConstants.API_NAME);
    }

    /**
     * 判断分销商 是否是特例供货
     *
     * @param tenantId
     * @param upId
     * @param distributorId
     * @return
     */
    public boolean checkIsSpecialSupplyByDistributorId(String tenantId, String upId, String distributorId) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), upId)
                .eq(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), distributorId)
                .eq(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), true)
                .build();
        return CollectionUtils.isNotEmpty(getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME));
    }

    /**
     * 获取分销商特例供货的数据
     *
     * @param tenantId
     * @param upId
     * @param thisId
     * @return
     */
    public List<IObjectData> getSpecialSupplyProductsByUpAndThis(String tenantId, String upId, String thisId) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(SpecialSupplyObjConstants.Field.shopId.getApiName(), thisId)
                .eq(SpecialSupplyObjConstants.Field.dealerId.getApiName(), upId)
                .build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, SpecialSupplyObjConstants.API_NAME);
    }

    @Override
    public List<String> getServiceCenterDepartmentIds(String tenantId, List<String> departmentIds) {
        List<String> stringlist = Lists.newArrayList();
        List<IObjectData> departmentObjList = getDepartmentList(tenantId, departmentIds);
        for (IObjectData iObjectData : departmentObjList) {
            String deptParentPathStr = iObjectData.get("dept_parent_path", String.class);
            String[] split = deptParentPathStr.split("[.]");
            if (split.length < 5) {
                throw new ValidateException("归属部门/跨区域供货 必须是服务处或其子集"); //ignoreI18n
            } else if (split.length == 5) {
                stringlist.add(iObjectData.get("dept_id", String.class));
            } else {
                stringlist.add(split[5]);
            }
        }
        return stringlist;
    }

    public Map<String, String> getServiceCenterDepartmentIdsMap(String tenantId, List<String> departmentIds) {
        Map<String, String> map = new HashMap<>();
        List<IObjectData> departmentObjList = getDepartmentList(tenantId, departmentIds);
        for (IObjectData iObjectData : departmentObjList) {
            String deptParentPathStr = iObjectData.get("dept_parent_path", String.class);
            String[] split = deptParentPathStr.split("[.]");
            if (split.length < 5) {
                throw new ValidateException("归属部门/跨区域供货 必须是服务处或其子集"); //ignoreI18n
            } else if (split.length == 5) {
                map.put(iObjectData.get("dept_id", String.class), iObjectData.get("dept_id", String.class));
            } else {
                map.put(iObjectData.get("dept_id", String.class), split[5]);
            }
        }
        return map;
    }

    //    public String getParentIdByDepartmentId(String tenantId,String departmentId){
//        List<IObjectData> departmentObj = getDepartmentList(tenantId, Lists.newArrayList(departmentId));
//        return (String) departmentObj.get(0).get("parent_id",List.class).get(0);
//    }
    public List<IObjectData> getDepartmentList(String tenantId, List<String> departmentIds) {
        SearchQuery build = SearchQuery.builder()
                .in("dept_id", departmentIds)
                .build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), build, "DepartmentObj");
    }

    public List<ShopProductSupplierRelationshipObj> convertRelationBySpecialSupplyObj(List<IObjectData> data) {
        List<ShopProductSupplierRelationshipObj> shopProductSupplierRelationshipObjlist = Lists.newArrayList();
        for (IObjectData iObjectData : data) {
            shopProductSupplierRelationshipObjlist.add(convertFromSpecialSupplyObj(iObjectData));
        }
        return shopProductSupplierRelationshipObjlist;
    }

    private ShopProductSupplierRelationshipObj convertFromSpecialSupplyObj(IObjectData iObjectData) {
        ShopProductSupplierRelationshipObj shopProductSupplierRelationshipObj = new ShopProductSupplierRelationshipObj();
        shopProductSupplierRelationshipObj.setProductId(iObjectData.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class));
        //todo 看下查找关联 名字咋取 //ignoreI18n
//        specialSupplyObj.setProductName();
        shopProductSupplierRelationshipObj.setShopId(iObjectData.get(SpecialSupplyObjConstants.Field.shopId.getApiName(), String.class));
        shopProductSupplierRelationshipObj.setSupplierId(iObjectData.get(SpecialSupplyObjConstants.Field.dealerId.getApiName(), String.class));
        shopProductSupplierRelationshipObj.setId(iObjectData.get(BaseField.id.getApiName(), String.class));
        return shopProductSupplierRelationshipObj;
    }

    @Override
    public List<ProductObj> getProductListByProductCollectionId(String tenantId, String productCollectionId) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(CoveredProductObjConstants.Field.businessScopeId.getApiName(), productCollectionId)
                .build();
        List<IObjectData> data = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, CoveredProductObjConstants.API_NAME);
        if (CollectionUtils.isNotEmpty(data)) {

//            return data.stream().map(o -> convertProductObj(o)).collect(Collectors.toList());
            //增加按照产品id 去重的逻辑
            return data.stream().map(o -> convertProductObj(o)).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ProductObj::getProductId))), ArrayList::new));
        }
        return ListUtils.EMPTY_LIST;
    }

    private ProductObj convertProductObj(IObjectData o) {
        ProductObj productObj = new ProductObj();
        productObj.setProductId(o.get(CoveredProductObjConstants.Field.productId.getApiName(), String.class));
        productObj.setProductName(o.get(String.format(rFormat, o.get(CoveredProductObjConstants.Field.productId.getApiName(), String.class)), String.class));
        return productObj;
    }

    @Override
    public ProductCollectionObj getProductCollectionObjByDepartmentId(String tenantId, String supplierId, boolean isQueryProduct) {
        if (StringUtils.isBlank(supplierId)) {
            throw new ValidateException(ProductCollectionObjConstants.Field.departmentId.getLabel() + " 为空"); //ignoreI18n
        }
        return getProductCollectionObjByDepartmentIds(tenantId, Lists.newArrayList(supplierId), isQueryProduct).get(0);

    }

    @Override
    public List<ProductCollectionObj> getProductCollectionObjByDepartmentIds(String tenantId, List<String> departmentIds, boolean isQueryProduct) {
        List<IObjectData> data = queryProductCollectionByDepIds(tenantId, departmentIds);
        if (CollectionUtils.isEmpty(data) || data.size() < departmentIds.size()) {
            //初始化
            List<String> dataDepIds = data.stream().map(o -> {
                List departIds = o.get(ProductCollectionObjConstants.Field.departmentId.getApiName(), List.class);
                return departIds.get(0).toString();
            }).collect(Collectors.toList());
            List<String> addDepIds = departmentIds.stream().filter(o -> !dataDepIds.contains(o)).collect(Collectors.toList());
            data.addAll(createDefaultProductCollection(tenantId, addDepIds));
//            throw new ObjectDataNotFoundException(
//                    String.format("%s ，%s 查询为空",
//                            ProductCollectionObjConstants.Field.departmentId.getLabel(),
//                            ProductCollectionObjConstants.DISPLAY_NAME));
        }
        return data.stream().map(o -> convertProductCollectionObj(tenantId, o, isQueryProduct)).collect(Collectors.toList());
    }

    @Override
    public List<IObjectData> getDuplicatProductCollectionObjs(String tenantId, String dataId, String bizId) {
        List<IObjectData> iObjectDataList = null;
        if (bizId.length() < 13) {
            iObjectDataList = queryProductCollectionByDepIds(tenantId, Lists.newArrayList(bizId));
        } else {
            iObjectDataList = queryCollectionObjByLeaderIds(tenantId, Lists.newArrayList(bizId));
        }
        if (StringUtils.isNotBlank(dataId)) {
            iObjectDataList.removeIf(o -> o.getId().equals(dataId));
        }
        return iObjectDataList;
    }


    public List<IObjectData> queryProductCollectionByDepIds(String tenantId, List<String> departmentIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(ProductCollectionObjConstants.Field.departmentId.getApiName(), departmentIds)
                .eq(BaseField.recordType.getApiName(), ProductCollectionObjConstants.Value.recordType_server_center.getValue())
                .build();

        List<IObjectData> iObjectDataListByQueryAndApiName = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, ProductCollectionObjConstants.API_NAME);
        //判断departmentId已经重复出现在iObjectDataListByQueryAndApiName里则删除重复
        if (CollectionUtils.isNotEmpty(iObjectDataListByQueryAndApiName)) {
            Set<String> ids = new HashSet<>();
            List<IObjectData> list = new ArrayList<>();
            for (IObjectData iObjectData : iObjectDataListByQueryAndApiName) {
                String departmentId = iObjectData.get(ProductCollectionObjConstants.Field.departmentId.getApiName(), List.class).get(0).toString();
                if (ids.contains(departmentId)) {
                    list.add(iObjectData);
                } else {
                    ids.add(departmentId);
                }
            }
            iObjectDataListByQueryAndApiName.removeAll(list);
            //删除重复的数据
            batchInvalidAndDelOfSingleApiName(list, User.systemUser(tenantId));
        }

        if (CollectionUtils.isEmpty(iObjectDataListByQueryAndApiName) || iObjectDataListByQueryAndApiName.size() != departmentIds.size()) {
            return getAllIObjectDataListByQueryWithDb(User.systemUser(tenantId), searchQuery, ProductCollectionObjConstants.API_NAME);
        }
        return iObjectDataListByQueryAndApiName;
    }

    private ProductCollectionObj convertProductCollectionObj(String tenantId, IObjectData iObjectData, boolean isQueryProduct) {
        ProductCollectionObj productCollectionObj = new ProductCollectionObj();
        productCollectionObj.setScopeId(iObjectData.getId());
        String businessCopeType = iObjectData.get(ProductCollectionObjConstants.Field.businessScopeType.getApiName(), String.class);
        productCollectionObj.setAllParentProduct(ProductCollectionObjConstants.Value.isInheritUpProduct(businessCopeType));
        if (!productCollectionObj.isAllParentProduct() && isQueryProduct) {
            productCollectionObj.setProductList(getProductListByProductCollectionId(tenantId, productCollectionObj.getScopeId()));
        }
        List list = iObjectData.get(ProductCollectionObjConstants.Field.departmentId.getApiName(), List.class);
        if (CollectionUtils.isNotEmpty(list)) {
            productCollectionObj.setDepartmentId((String) list.get(0));
        }
        productCollectionObj.setDealerId(iObjectData.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class));
        productCollectionObj.setDistributorId(iObjectData.get(ProductCollectionObjConstants.Field.distributorId.getApiName(), String.class));
        return productCollectionObj;
    }

    @Override
    public IObjectData createAvailableRangeObj(String tenantId, String ownerId, String dealerSupplyId, String accountId, Object ownDepartment) {
        return createAvailableRangeObj(tenantId, ownerId, dealerSupplyId, accountId, AvailableRangeObjConstants.Value.FIXED
                , AvailableRangeObjConstants.Value.FIXED, ownDepartment);
    }

    public IObjectData createAvailableRangeObj(String tenantId, String ownerId, String dealerSupplyId, String accountId,
                                               AvailableRangeObjConstants.Value accountRange, AvailableRangeObjConstants.Value productRange, Object ownDepartment) {
        IObjectData objectData = convertAvailableRangeObj(tenantId, ownerId, dealerSupplyId, accountId, null
                , accountRange, productRange, ownDepartment);
        return save(User.systemUser(tenantId), objectData);
    }

    public IObjectData createAvailableRangeObj4Special(String tenantId, String ownerId, String dealerSupplyId, String accountId, String storeId, Object ownDepartment) {
        IObjectData objectData = convertAvailableRangeObj4Special(tenantId, ownerId, dealerSupplyId, accountId, storeId, ownDepartment);
        return save(User.systemUser(tenantId), objectData);
    }

    @Override
    public List<IObjectData> createPriceBookObj(String tenantId, String ownerId, List<String> masterObjectIds, Object ownDepartment) {
        List<IObjectData> list = Lists.newArrayList();

        List<IObjectData> standardData = getStandardBook(tenantId);

        for (String id : masterObjectIds) {
            IObjectData objectData = createBaseObjectData(tenantId, ownerId, AvailablePriceBookConstants.API_NAME);
            objectData.set(AvailablePriceBookConstants.Field.priceBookId.getApiName(), standardData.get(0).getId());
            objectData.set("data_own_department", ownDepartment);
            objectData.set(AvailablePriceBookConstants.Field.availableRangeId.getApiName(), id);
            list.add(objectData);
        }


        return batchSave(User.systemUser(tenantId), list);
    }

    private List<IObjectData> getStandardBook(String tenantId) {
        List<IObjectData> standardData = null;
        {
            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(BaseField.id.getApiName(), "61b84cac41f4ad0001149dfa")
                    .build();
            standardData = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, PriceBookObjConstants.API_NAME);

        }
        if (CollectionUtils.isEmpty(standardData)) {
            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(PriceBookObjConstants.Field.isStandard.getApiName(), true)
                    .build();
            standardData = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, PriceBookObjConstants.API_NAME);
        }
        return standardData;
    }

    @NotNull
    public IObjectData convertAvailableRangeObj4Special(String tenantId, String ownerId, String dealerSupplyId, String accountId, String storeId, Object ownDepartment) {
        return convertAvailableRangeObj(tenantId, ownerId, dealerSupplyId, accountId, storeId
                , AvailableRangeObjConstants.Value.FIXED, AvailableRangeObjConstants.Value.FIXED, ownDepartment);
    }

    @NotNull
    public IObjectData convertAvailableRangeObj(String tenantId, String ownerId, String dealerSupplyId, String accountId, String storeId
            , AvailableRangeObjConstants.Value accountRange, AvailableRangeObjConstants.Value productRange, Object ownDepartment) {
        IObjectData objectData = createBaseObjectData(tenantId, ownerId, AvailableRangeObjConstants.API_NAME);
        objectData.set(AvailableRangeObjConstants.Field.accountRange.getApiName(),
                accountRange.getValue());
        objectData.set(AvailableRangeObjConstants.Field.productRange.getApiName(),
                productRange.getValue());
        objectData.set(AvailableRangeObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyId);
        List<String> ids = Lists.newArrayList(accountId);
        objectData.set(AvailableRangeObjConstants.Field.accountId.getApiName(), accountId);
        if (ownDepartment != null) {
            objectData.set(BaseField.dataOwnDepartment.getApiName(), ownDepartment);
        }
        if (StringUtils.isNotBlank(storeId)) {
            objectData.set(AvailableRangeObjConstants.Field.storeId.getApiName(), storeId);
            ids.add(storeId);
        }
        //增加 逻辑 name 命名规则，storeName +-+ AccountName
        List<AccountObj> accountObjByIds = getAccountObjByIds(tenantId, ids);
        List<String> departIds = Lists.newArrayList();
        String name = "";
        for (AccountObj accountObjById : accountObjByIds) {
            if (accountObjById.getId().equals(accountId)) {
                name = name + accountObjById.getName();
                departIds.add(accountObjById.getDepartmentId());
                if (CollectionUtils.isNotEmpty(accountObjById.getOtherDepartmentIds())) {
                    departIds.addAll(accountObjById.getOtherDepartmentIds());
                }
            } else {
                name = accountObjById.getName() + "_" + name;
            }
        }
        name = name + "_" + new ObjectId().toString();
        List<String> serviceCenterDepartmentIds = getServiceCenterDepartmentIds(tenantId, departIds);
        OrgRange orgRange = new OrgRange();
        orgRange.getValue().setDepartments(serviceCenterDepartmentIds);
        objectData.set(AvailableRangeObjConstants.Field.orgRange.getApiName(),
                JSON.toJSONString(orgRange));
        //todo 测试使用
        objectData.set(AvailableRangeObjConstants.Field.orgRange.getApiName(),
                AvailableRangeObjConstants.Value.All.getValue());
        objectData.setName(name);
        return objectData;
    }

    public List<IObjectData> getSupplyStoreBySupplyId(String tenantId, String id, int isAll, List<String> customerIds) {
        SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                .eq(SupplyStoreObjConstants.Field.upSupplyId.getApiName(), id);
        if (isAll != 1) {
            if (CollectionUtils.isEmpty(customerIds)) {
                return Lists.newArrayList();
            }
            searchQueryBuilder.in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), customerIds);
        }
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQueryBuilder.build(), SupplyStoreObjConstants.API_NAME);
    }

    public List<IObjectData> getSupplyDistributionBySupplyId(String tenantId, String id, int isAll, List<String> customerIds) {

        SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                .eq(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), id);
        if (isAll != 1) {
            if (CollectionUtils.isEmpty(customerIds)) {
                return Lists.newArrayList();
            }
            searchQueryBuilder.in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), customerIds);
        }
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQueryBuilder.build(), DistributorSupplyObjConstants.API_NAME);
    }

    public List<IObjectData> getSpecialSupplyByDealerIdAndNoProductIds(String tenantId, String transferAccountId, Set<String> noProductIds) {
        if (CollectionUtils.isNotEmpty(noProductIds) && noProductIds.contains("ALL")) {
            return Lists.newArrayList();
        }
        SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                .eq(SpecialSupplyObjConstants.Field.dealerId.getApiName(), transferAccountId);
        if (CollectionUtils.isNotEmpty(noProductIds)) {
            searchQueryBuilder.nin(SpecialSupplyObjConstants.Field.productId.getApiName(), noProductIds);
        }
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQueryBuilder.build(), SpecialSupplyObjConstants.API_NAME);
    }




    /**
     * 可售范围组织
     */
    @Data
    static class OrgRange {

        /**
         * ORG 组织部门
         */
        private String type = "ORG";

        /**
         *
         */
        private Org value = new Org();

        @Data
        public static class Org {
            private List<String> employees = Lists.newArrayList();
            private List<String> departments = Lists.newArrayList();
        }
    }

    @Override
    public List<IObjectData> createDealerSupplyObj(String tenantId, String ownerId, List<String> addDealerSupplierIds) {
        List<IObjectData> list = Lists.newArrayList();
        List<AccountObj> accountObjList = getAccountObjByIds(tenantId, addDealerSupplierIds);
        for (AccountObj addDealerSupplierId : accountObjList) {
            IObjectData objectData = createBaseObjectData(tenantId, ownerId, DealerSupplyObjConstants.API_NAME);
            objectData.set(DealerSupplyObjConstants.Field.dealerId.getApiName(), addDealerSupplierId.getId());
            objectData.set(DealerSupplyObjConstants.Field.ownDepartment.getApiName(),
                    Lists.newArrayList(addDealerSupplierId.getDepartmentId()));
            list.add(objectData);
        }
        return batchSaveOfSingleApiName(list, User.systemUser(tenantId));
    }

    @Override
    public List<IObjectData> createSupplyStoreObj(String tenantId, AccountObj thisAccountObj, List<IObjectData> dealerSupplyBySuppliers) {

        return createSupplyStoreObj(tenantId, thisAccountObj.getOwner(), thisAccountObj.getId(), dealerSupplyBySuppliers, false);
    }

    @Override
    public List<IObjectData> createSupplyStoreObj4Special(String tenantId, AccountObj thisAccountObj, List<IObjectData> dealerSupplyBySuppliers) {
        return createSupplyStoreObj(tenantId, thisAccountObj.getOwner(), thisAccountObj.getId(), dealerSupplyBySuppliers, true);
    }


    @Override
    public List<IObjectData> createSupplyStoreObj(String tenantId, String userId, String accountId, List<IObjectData> dealerSupplyBySuppliers, boolean isSpecial) {
        log.info("createSupplyStoreObj tenantId {},accountId  {}", tenantId, accountId);
        List<IObjectData> list = Lists.newArrayList();
        List<IObjectData> updateList = Lists.newArrayList();
        List<IObjectData> resultData = Lists.newArrayList();
        //按照 下级 取供货门店
        List<IObjectData> supplyStoreByShopIds = getSupplyStoreByShopIds(tenantId, Lists.newArrayList(accountId));
        //key是上级
        Map<String, IObjectData> map = supplyStoreByShopIds.stream().collect(Collectors.toMap(
                k -> k.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class),
                v -> v));
        for (IObjectData dealerSupplyObj : dealerSupplyBySuppliers) {
            //如果存在 则 更新
            String tempDealerId = dealerSupplyObj.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class);
            Object ownDepartment = dealerSupplyObj.getDataOwnDepartment();
            if (map.containsKey(tempDealerId)) {
                //更新
                IObjectData iObjectData = map.get(tempDealerId);
                if (!iObjectData.get(SupplyStoreObjConstants.Field.specialSupply.getApiName(), Boolean.class) && isSpecial) {
                    iObjectData.set(SupplyStoreObjConstants.Field.specialSupply.getApiName(), isSpecial);
                    updateList.add(iObjectData);
                }
                resultData.add(iObjectData);
            } else {
//                新建

                IObjectData objectData = createBaseObjectData(tenantId, userId, SupplyStoreObjConstants.API_NAME);
                objectData.set(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), accountId);
                objectData.set(SupplyStoreObjConstants.Field.upSupplyId.getApiName(),
                        dealerSupplyObj.get(BaseField.id.getApiName(), String.class));
                objectData.set(SupplyStoreObjConstants.Field.upDealerId.getApiName(),
                        tempDealerId);
                objectData.set(SupplyStoreObjConstants.Field.specialSupply.getApiName(), isSpecial);
                if (ownDepartment != null)
                    objectData.set(BaseField.dataOwnDepartment.getApiName(), ownDepartment);
                list.add(objectData);
            }
        }

        if (CollectionUtils.isNotEmpty(list)) {
            resultData.addAll(batchSaveOfSingleApiName(list, User.systemUser(tenantId)));
            updateShopUpSupplyFields(tenantId, accountId);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            resultData = batchUpdate(updateList, User.systemUser(tenantId));
        }
        return resultData;
    }

    @Override
    public List<IObjectData> createSupplyStoreObj(String tenantId, String userId, List<String> accountIds, IObjectData dealerSupplyBySupplier, boolean isSpecial) {
        List<IObjectData> list = Lists.newArrayList();
        List<IObjectData> updateList = Lists.newArrayList();
        List<IObjectData> resultData = Lists.newArrayList();
        Object ownDepartment = dealerSupplyBySupplier.getDataOwnDepartment();
        List<IObjectData> supplyStoreList = getSupplyStore(tenantId, accountIds, dealerSupplyBySupplier.getId());
        Map<String, IObjectData> map = supplyStoreList.stream().collect(Collectors.toMap(
                k -> k.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class),
                v -> v));
        for (String accountId : accountIds) {

            if (Objects.nonNull(map.get(accountId))) {
                IObjectData iObjectData = map.get(accountId);
                iObjectData.set(SupplyStoreObjConstants.Field.specialSupply.getApiName(), isSpecial);
                updateList.add(iObjectData);
            } else {

                IObjectData objectData = createBaseObjectData(tenantId, userId, SupplyStoreObjConstants.API_NAME);
                objectData.set(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), accountId);
                objectData.set(SupplyStoreObjConstants.Field.upSupplyId.getApiName(),
                        dealerSupplyBySupplier.get(BaseField.id.getApiName(), String.class));
                objectData.set(SupplyStoreObjConstants.Field.upDealerId.getApiName(),
                        dealerSupplyBySupplier.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class));
                objectData.set(SupplyStoreObjConstants.Field.specialSupply.getApiName(), isSpecial);
                if (ownDepartment != null)
                    objectData.set(BaseField.dataOwnDepartment.getApiName(), ownDepartment);
                list.add(objectData);
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            resultData.addAll(batchSaveOfSingleApiName(list, User.systemUser(tenantId)));
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            resultData.addAll(batchUpdate(updateList, User.systemUser(tenantId)));
        }
        return resultData;
    }

    @Override
    public List<IObjectData> createSupplyStoreObjNoUpdate(String tenantId, String userId, List<String> accountIds, IObjectData dealerSupplyBySupplier, boolean isSpecial) {
        List<IObjectData> list = Lists.newArrayList();
        Object ownDepartment = dealerSupplyBySupplier.getDataOwnDepartment();
        for (String accountId : accountIds) {
            IObjectData objectData = createBaseObjectData(tenantId, userId, SupplyStoreObjConstants.API_NAME);
            objectData.set(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), accountId);
            objectData.set(SupplyStoreObjConstants.Field.upSupplyId.getApiName(),
                    dealerSupplyBySupplier.get(BaseField.id.getApiName(), String.class));
            objectData.set(SupplyStoreObjConstants.Field.upDealerId.getApiName(),
                    dealerSupplyBySupplier.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class));
            objectData.set(SupplyStoreObjConstants.Field.specialSupply.getApiName(), isSpecial);
            if (ownDepartment != null)
                objectData.set(BaseField.dataOwnDepartment.getApiName(), ownDepartment);
            list.add(objectData);
        }
        return batchSaveOfSingleApiName(list, User.systemUser(tenantId));
    }

    @Override
    public List<YLTempSupplyEntity> createSupplyStoryObjs(String tenantId, String userId, List<YLTempSupplyEntity> ylTempSupplyEntities) {
        List<IObjectData> list = Lists.newArrayList();
        List<IObjectData> updateList = Lists.newArrayList();
        List<IObjectData> resultData = Lists.newArrayList();
        ylTempSupplyEntities = ylTempSupplyEntities.stream().filter(o -> StringUtils.isBlank(o.getSyncObjectId())).collect(Collectors.toList());
        List<String> thisIds = ylTempSupplyEntities.stream().map(o -> o.getFsId()).distinct().collect(Collectors.toList());
        List<String> upIds = ylTempSupplyEntities.stream().map(o -> o.getFsUpAccountId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(upIds) || CollectionUtils.isEmpty(thisIds)) {
            return ylTempSupplyEntities;
        }
        List<IObjectData> supplyStoreList = getSupplyStores(tenantId, thisIds, upIds);
        Map<String, IObjectData> map = supplyStoreList.stream().collect(Collectors.toMap(
                k -> k.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)
                        + "_" + k.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class),
                v -> v));

        ylTempSupplyEntities.stream().forEach(o -> {
            String id = o.getFsId() + "_" + o.getFsUpAccountId();
            if (map.containsKey(id)) {
                IObjectData iObjectData = map.get(id);
                iObjectData.set(SupplyStoreObjConstants.Field.specialSupply.getApiName(), o.getSpecial() == 1);
                updateList.add(iObjectData);
            } else {

                IObjectData objectData = createBaseObjectData(tenantId, userId, SupplyStoreObjConstants.API_NAME);
                objectData.set(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), o.getFsId());
                //供货关系的id
                objectData.set(SupplyStoreObjConstants.Field.upSupplyId.getApiName(), o.getUpSupplySyncObjectId());
                objectData.set(SupplyStoreObjConstants.Field.upDealerId.getApiName(), o.getFsUpAccountId());
                objectData.set(SupplyStoreObjConstants.Field.specialSupply.getApiName(), o.getSpecial() == 1);
                if (StringUtils.isNotBlank(o.getDepartmentId()))
                    objectData.setDataOwnDepartment(Lists.newArrayList(o.getDepartmentId()));
                list.add(objectData);
            }
        });
        if (CollectionUtils.isNotEmpty(list)) {
            resultData.addAll(batchSaveOfSingleApiName(list, User.systemUser(tenantId)));
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            resultData.addAll(batchUpdate(updateList, User.systemUser(tenantId)));
        }
        Map<String, String> idMap = resultData.stream().collect(Collectors.toMap(k ->
                        k.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class) + "_" + k.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class)
                , v -> v.getId()));
        ylTempSupplyEntities.stream().forEach(o -> {
            String id = o.getFsId() + "_" + o.getFsUpAccountId();
            o.setSyncObjectId(idMap.get(id));
        });
        return ylTempSupplyEntities;
    }

    @Override
    public List<YLTempSupplyEntity> createDistributorSupplyObjs(String tenantId, String userId, List<YLTempSupplyEntity> ylTempSupplyEntities) {
        List<IObjectData> list = Lists.newArrayList();
        List<IObjectData> updateList = Lists.newArrayList();
        List<IObjectData> resultData = Lists.newArrayList();
        ylTempSupplyEntities = ylTempSupplyEntities.stream().filter(o -> StringUtils.isBlank(o.getSyncObjectId())).collect(Collectors.toList());
        List<String> thisIds = ylTempSupplyEntities.stream().map(o -> o.getFsId()).distinct().collect(Collectors.toList());
        List<String> upIds = ylTempSupplyEntities.stream().map(o -> o.getFsUpAccountId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(upIds) || CollectionUtils.isEmpty(thisIds)) {
            return ylTempSupplyEntities;
        }
        List<IObjectData> supplyStoreList = getDistributorSuppliers(tenantId, null, thisIds, upIds);
        Map<String, IObjectData> map = supplyStoreList.stream().collect(Collectors.toMap(
                k -> k.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class)
                        + "_" + k.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), String.class),
                v -> v));

        ylTempSupplyEntities.stream().forEach(o -> {
            String id = o.getFsId() + "_" + o.getFsUpAccountId();
            if (map.containsKey(id)) {
                IObjectData iObjectData = map.get(id);
                iObjectData.set(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), o.getSpecial() == 1);
                updateList.add(iObjectData);
            } else {

                IObjectData objectData = createBaseObjectData(tenantId, userId, DistributorSupplyObjConstants.API_NAME);
                objectData.set(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), o.getFsId());
                //供货关系的id
                objectData.set(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), o.getUpSupplySyncObjectId());
                objectData.set(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), o.getFsUpAccountId());
                objectData.set(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), o.getSpecial() == 1);
                if (StringUtils.isNotBlank(o.getDepartmentId()))
                    objectData.setDataOwnDepartment(Lists.newArrayList(o.getDepartmentId()));
                list.add(objectData);
            }
        });
        if (CollectionUtils.isNotEmpty(list)) {
            resultData.addAll(batchSaveOfSingleApiName(list, User.systemUser(tenantId)));
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            resultData.addAll(batchUpdate(updateList, User.systemUser(tenantId)));
        }
        Map<String, String> idMap = resultData.stream().collect(Collectors.toMap(k ->
                        k.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class) + "_" + k.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), String.class)
                , v -> v.getId()));
        ylTempSupplyEntities.stream().forEach(o -> {
            String id = o.getFsId() + "_" + o.getFsUpAccountId();
            o.setSyncObjectId(idMap.get(id));
        });
        return ylTempSupplyEntities;
    }

    @Override
    public List<YLTempSupplyEntity> createSpecialSupplyObjs(String tenantId, String userId, List<YLTempSupplyEntity> ylTempSupplyEntities) {
        List<IObjectData> list = Lists.newArrayList();
        Map<String, YLTempSubProduct> idMap = new HashMap<>();
        List<IObjectData> specialSupplyByShopIds = getSpecialSupplyByShopIds(tenantId, ylTempSupplyEntities.stream().map(o -> o.getFsId()).collect(Collectors.toList()));
        Map<String, String> existIdMap = specialSupplyByShopIds.stream().collect(Collectors.toMap(k ->
                        k.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class) + "_" +
                                k.get(SpecialSupplyObjConstants.Field.shopId.getApiName(), String.class) + "_" +
                                k.get(SpecialSupplyObjConstants.Field.dealerId.getApiName(), String.class)
                , v -> v.getId()));
        for (YLTempSupplyEntity ylTempSupplyEntity : ylTempSupplyEntities) {
            if (CollectionUtils.isNotEmpty(ylTempSupplyEntity.getYlProducts())) {
                List<YLTempSubProduct> noSoiList = ylTempSupplyEntity.getYlProducts().stream().filter(o -> StringUtils.isBlank(o.getSyncObjectId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(noSoiList)) {
                    for (YLTempSubProduct ylTempSubProduct : noSoiList) {
                        if (StringUtils.isNotBlank(ylTempSubProduct.getFsProductId())) {
                            String id = ylTempSubProduct.getFsProductId() + "_" + ylTempSupplyEntity.getFsId() + "_" + ylTempSupplyEntity.getFsUpAccountId();
                            if (existIdMap.containsKey(id)) {
                                ylTempSubProduct.setSyncObjectId(existIdMap.get(id));
                            } else {
                                idMap.put(id, ylTempSubProduct);
                                IObjectData objectData = createBaseObjectData(tenantId, userId, SpecialSupplyObjConstants.API_NAME);
                                objectData.set(SpecialSupplyObjConstants.Field.productId.getApiName(), ylTempSubProduct.getFsProductId());
                                objectData.set(SpecialSupplyObjConstants.Field.shopId.getApiName(), ylTempSupplyEntity.getFsId());
                                objectData.set(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName(), ylTempSupplyEntity.getUpSupplySyncObjectId());
                                objectData.set(SpecialSupplyObjConstants.Field.dealerId.getApiName(), ylTempSupplyEntity.getFsUpAccountId());
                                if (StringUtils.isNotBlank(ylTempSupplyEntity.getDepartmentId()))
                                    objectData.setDataOwnDepartment(Lists.newArrayList(ylTempSupplyEntity.getDepartmentId()));
                                list.add(objectData);
                            }
                        }
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            List<IObjectData> iObjectDataList = batchSaveOfSingleApiName(list, User.systemUser(tenantId));
            for (IObjectData iObjectData : iObjectDataList) {
                String productId = iObjectData.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class);
                String shopId = iObjectData.get(SpecialSupplyObjConstants.Field.shopId.getApiName(), String.class);
                String dealerId = iObjectData.get(SpecialSupplyObjConstants.Field.dealerId.getApiName(), String.class);
                String id = productId + "_" + shopId + "_" + dealerId;
                if (idMap.containsKey(id)) {
                    idMap.get(id).setSyncObjectId(iObjectData.getId());
                }
            }
        }

        return ylTempSupplyEntities;
    }

    @Override
    public void delSupplyAboutDelAccount(String tenantId, List<String> accountIds) {
        //删除经营范围
        delLookUp(User.systemUser(tenantId), accountIds, ProductCollectionObjConstants.API_NAME,
                Sets.newHashSet(ProductCollectionObjConstants.Field.dealerId.getApiName(), ProductCollectionObjConstants.Field.distributorId.getApiName()));
        //删除供货关系
        delLookUp(User.systemUser(tenantId), accountIds, DealerSupplyObjConstants.API_NAME,
                Sets.newHashSet(DealerSupplyObjConstants.Field.dealerId.getApiName()));
        //删除供货分销商
        delLookUp(User.systemUser(tenantId), accountIds, DistributorSupplyObjConstants.API_NAME,
                Sets.newHashSet(
                        DistributorSupplyObjConstants.Field.upDealerId.getApiName(),
                        DistributorSupplyObjConstants.Field.thisDealerId.getApiName()

                ));
        //删除特例供货
        delLookUp(User.systemUser(tenantId), accountIds, SpecialSupplyObjConstants.API_NAME,
                Sets.newHashSet(
                        SpecialSupplyObjConstants.Field.dealerId.getApiName(),
                        SpecialSupplyObjConstants.Field.shopId.getApiName()

                ));
        //删除供货门店
        delLookUp(User.systemUser(tenantId), accountIds, SupplyStoreObjConstants.API_NAME,
                Sets.newHashSet(
                        SupplyStoreObjConstants.Field.upDealerId.getApiName(),
                        SupplyStoreObjConstants.Field.thisDealerId.getApiName()

                ));
        //删除可售范围
        delLookUp(User.systemUser(tenantId), accountIds, "AvailableRangeObj",
                Sets.newHashSet(
                        "account_id__c"

                ));
        //删除可售客户
        delLookUp(User.systemUser(tenantId), accountIds, "AvailableAccountObj",
                Sets.newHashSet(
                        "account_id"

                ));
    }

    @Override
    public List<String> getSpecialAccountIds(String tenantId, String dealerId) {
        List<String> specialAccountIds = Lists.newArrayList();
        {
            //查询特例供货门店
            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(SupplyStoreObjConstants.Field.upDealerId.getApiName(), dealerId)
                    .eq(SupplyStoreObjConstants.Field.specialSupply.getApiName(), Boolean.TRUE).build();
            QueryResult<IObjectData> result = getAllQueryDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME, Lists.newArrayList(SupplyStoreObjConstants.Field.thisDealerId.getApiName()));
            specialAccountIds.addAll(result.getData().stream().map(o -> o.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)).collect(Collectors.toList()));
        }
        {
            //查询特例供货分销商
            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), dealerId)
                    .eq(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), Boolean.TRUE).build();
            QueryResult<IObjectData> result = getAllQueryDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME, Lists.newArrayList(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()));
            specialAccountIds.addAll(result.getData().stream().map(o -> o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class)).collect(Collectors.toList()));
        }

        return specialAccountIds;
    }

    @Override
    public List<String> getSpecialAccountIds(String tenantId, String dealerId, String shopId) {
        List<String> specialAccountIds = Lists.newArrayList();
        {
            //查询特例供货门店
            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(SupplyStoreObjConstants.Field.upDealerId.getApiName(), dealerId)
                    .eq(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), shopId)
                    .eq(SupplyStoreObjConstants.Field.specialSupply.getApiName(), Boolean.TRUE).build();
            QueryResult<IObjectData> result = getAllQueryDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME, Lists.newArrayList(SupplyStoreObjConstants.Field.thisDealerId.getApiName()));
            specialAccountIds.addAll(result.getData().stream().map(o -> o.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)).collect(Collectors.toList()));
        }
        {
            //查询特例供货分销商
            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), dealerId)
                    .eq(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), shopId)
                    .eq(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), Boolean.TRUE).build();
            QueryResult<IObjectData> result = getAllQueryDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME, Lists.newArrayList(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()));
            specialAccountIds.addAll(result.getData().stream().map(o -> o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class)).collect(Collectors.toList()));
        }

        return specialAccountIds;
    }

    @Override
    public List<IObjectData> createDistributorSupplyObj(String tenantId, String userId, List<String> accountIds, IObjectData dealerSupplyBySupplier, boolean isSpecial) {
        List<IObjectData> list = Lists.newArrayList();
        List<IObjectData> updateList = Lists.newArrayList();
        List<IObjectData> resultData = Lists.newArrayList();

        List<IObjectData> distributorSupplyList = getDistributorSupplyObj(tenantId, accountIds, dealerSupplyBySupplier.getId());
        Object ownDepartment = dealerSupplyBySupplier.getDataOwnDepartment();
        Map<String, IObjectData> map = distributorSupplyList.stream().collect(Collectors.toMap(
                k -> k.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class),
                v -> v));
        for (String accountId : accountIds) {
            if (Objects.nonNull(map.get(accountId))) {
                IObjectData iObjectData = map.get(accountId);
                iObjectData.set(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), isSpecial);
                updateList.add(iObjectData);
            } else {

                IObjectData objectData = createBaseObjectData(tenantId, userId, DistributorSupplyObjConstants.API_NAME);
                objectData.set(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), accountId);
                String id = dealerSupplyBySupplier.getId();
                objectData.set(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), id);
                objectData.set(DistributorSupplyObjConstants.Field.upDealerId.getApiName(),
                        dealerSupplyBySupplier.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class));
                objectData.set(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), isSpecial);
                if (ownDepartment != null)
                    objectData.set(BaseField.dataOwnDepartment.getApiName(), ownDepartment);
                list.add(objectData);
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            resultData.addAll(batchSaveOfSingleApiName(list, User.systemUser(tenantId)));
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            resultData.addAll(batchUpdate(updateList, User.systemUser(tenantId)));
        }

        return resultData;
    }

    @Override
    public List<IObjectData> createDistributorSupplyObjNoUpdate(String tenantId, String userId, List<String> accountIds, IObjectData dealerSupplyBySupplier, boolean isSpecial) {
        Object ownDepartment = dealerSupplyBySupplier.getDataOwnDepartment();
        List<IObjectData> addList = Lists.newArrayList();
        for (String accountId : accountIds) {


            IObjectData objectData = createBaseObjectData(tenantId, userId, DistributorSupplyObjConstants.API_NAME);
            objectData.set(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), accountId);
            String id = dealerSupplyBySupplier.getId();
            objectData.set(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), id);
            objectData.set(DistributorSupplyObjConstants.Field.upDealerId.getApiName(),
                    dealerSupplyBySupplier.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class));
            objectData.set(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), isSpecial);
            if (ownDepartment != null)
                objectData.set(BaseField.dataOwnDepartment.getApiName(), ownDepartment);
            addList.add(objectData);
        }
        return batchSaveOfSingleApiName(addList, User.systemUser(tenantId));
    }


    @Override
    public List<IObjectData> createDistributorSupplyObj(String tenantId, String ownerId, String accountId, List<String> addSupplierIds, Object ownDepartment) {
        return createDistributorSupply(tenantId, ownerId, accountId, addSupplierIds, false, ownDepartment);
    }

    private List<IObjectData> createDistributorSupply(String tenantId, String owner, String accountId, List<String> addSupplierIds, boolean isSpecial, Object ownDepartment) {
        log.info("createDistributorSupply tenantId {},accountId  {} addSupplierIds {}", tenantId, accountId, addSupplierIds);
        if (CollectionUtils.isEmpty(addSupplierIds)) {
            return ListUtils.EMPTY_LIST;
        }
        List<IObjectData> circularReferenceDistributorSupply = getCircularReferenceDistributorSupply(tenantId, accountId, addSupplierIds);
        List<IObjectData> list = Lists.newArrayList();
        List<IObjectData> resultData = Lists.newArrayList();
        List<IObjectData> updateList = Lists.newArrayList();
        List<IObjectData> dealerSupplyBySuppliers = getDealerSupplyBySuppliers(tenantId, addSupplierIds);

        SearchQuery searchQuery = SearchQuery.builder()
                .eq(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), accountId)
                .eq(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), addSupplierIds)
                .build();

        List<IObjectData> distributorSupplyObjByThisIds = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
        ;
        Map<String, IObjectData> map = distributorSupplyObjByThisIds.stream().collect(Collectors.toMap(
                k -> k.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), String.class),
                v -> v));
        for (IObjectData dealerSupplyObj : dealerSupplyBySuppliers) {
            //如果存在 则 更新
            String upId = dealerSupplyObj.get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();
            if (map.containsKey(upId)) {
                //更新
                IObjectData iObjectData = map.get(upId);
                if (!iObjectData.get(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), Boolean.class) && isSpecial) {
                    iObjectData.set(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), isSpecial);
                    updateList.add(iObjectData);
                }
                resultData.add(iObjectData);
            } else {
//                新建
                IObjectData objectData = createBaseObjectData(tenantId, owner, DistributorSupplyObjConstants.API_NAME);
                objectData.set(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), accountId);
                String id = dealerSupplyObj.getId();
                objectData.set(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), id);
                objectData.set(DistributorSupplyObjConstants.Field.upDealerId.getApiName(),
                        dealerSupplyObj.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class));
                objectData.set(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), isSpecial);
                if (ownDepartment != null)
                    objectData.set(BaseField.dataOwnDepartment.getApiName(), ownDepartment);
                list.add(objectData);
            }
        }

        if (CollectionUtils.isNotEmpty(list)) {
            resultData.addAll(batchSaveOfSingleApiName(list, User.systemUser(tenantId)));
            updateDistributorUpSupplyFields(tenantId, accountId);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            resultData = batchUpdate(updateList, User.systemUser(tenantId));
        }

//        for (String addSupplierId : addSupplierIds) {
//            IObjectData objectData = createBaseObjectData(tenantId,owner, DistributorSupplyObjConstants.API_NAME);
//            objectData.set(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(),accountId);
//            IObjectData iObjectData = dealerIdSupplyMap.get(addSupplierId);
//            //理论上不会为空  因为 getDealerSupplyBySuppliers 差量新增
//            if (iObjectData == null){
//                throw new ObjectDataNotFoundException(
//                        String.format("%s %s 查询为空",
//                                DealerSupplyObjConstants.DISPLAY_NAME,addSupplierId));
//            }
//            String id = iObjectData.getId();
//            objectData.set(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(),id);
//            objectData.set(DistributorSupplyObjConstants.Field.upDealerId.getApiName(),addSupplierId);
//            objectData.set(DistributorSupplyObjConstants.Field.specialSupply.getApiName(),isSpecial);
//            list.add(objectData);
//        }
//        List<IObjectData> iObjectDataList = batchSaveOfSingleApiName(list, User.systemUser(tenantId));
//        updateAccountUpDistributors(tenantId, accountId);
        return resultData;
    }

    @Override
    public List<IObjectData> createDistributorSupplyObj4Special(String tenantId, String ownerId, String accountId, List<String> addSupplierIds, Object ownDepartment) {
        return createDistributorSupply(tenantId, ownerId, accountId, addSupplierIds, true, ownDepartment);
    }

    @Override
    public List<IObjectData> createSpecialSupplyObj(String tenantId, String ownerId, List<String> accountIds, List<String> productIds, String dealerSupplyId, String distributorId, Object ownDepartment) {
        if (CollectionUtils.isEmpty(accountIds) || CollectionUtil.isEmpty(productIds)) {
            return ListUtils.EMPTY_LIST;
        }
        List<IObjectData> list = Lists.newArrayList();
        for (String accountId : accountIds) {

            for (String productId : productIds) {
                IObjectData objectData = createBaseObjectData(tenantId, ownerId, SpecialSupplyObjConstants.API_NAME);
                objectData.set(SpecialSupplyObjConstants.Field.productId.getApiName(), productId);
                objectData.set(SpecialSupplyObjConstants.Field.shopId.getApiName(), accountId);
                objectData.set(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyId);
                objectData.set(SpecialSupplyObjConstants.Field.dealerId.getApiName(), distributorId);
                if (ownDepartment != null)
                    objectData.set(BaseField.dataOwnDepartment.getApiName(), ownDepartment);
                list.add(objectData);
            }
        }
        List<IObjectData> iObjectDataList = batchSave(User.systemUser(tenantId), list);
        return iObjectDataList;
    }

    /**
     * 跟新门店供货
     *
     * @param tenantId
     * @param shopAccountId
     */
    @Override
    public void updateShopUpSupplyFields(String tenantId, String shopAccountId) {
        List<ShopProductSupplierRelationshipObj> supplyStoresByShopId = getSupplyStoresByShopId(tenantId, shopAccountId);
        if (CollectionUtils.isEmpty(supplyStoresByShopId)) {
            //置空
            updateAccountSupplyV2(tenantId, shopAccountId, ListUtils.EMPTY_LIST, 1);
        } else {
            List<String> distributors = supplyStoresByShopId.stream().map(o -> o.getSupplierId()).collect(Collectors.toList());
            updateAccountSupplyV2(tenantId, shopAccountId, distributors, 1);
        }
    }

    /**
     * 更新 门店商配送商 经销商字段逻辑
     *
     * @param tenantId
     * @param accountId
     * @param distributors
     * @param type         0是分销商  1 是客户
     */
    private void updateAccountSupply(String tenantId, String accountId, List<String> distributors, int type) {
        try {
            log.info("updateAccountSupply tenantId {},accountId  {} type {}", tenantId, accountId, type);
            List<DistributorSupply> distributorSupplyList = getDistributorSupplyByThisIds(tenantId, distributors);
            Map<String, List<DistributorSupply>> disMap = distributorSupplyList.stream().collect(Collectors.groupingBy(o -> o.getThisDistributorAccountId()));
            List<String> dealerIds = distributorSupplyList.stream().map(o -> o.getUpDealerAccountId()).collect(Collectors.toList());
            List<String> supplierIds = Lists.newArrayList();
            supplierIds.addAll(dealerIds);
            supplierIds.addAll(distributors);
            List<AccountObj> accountObjByIds = getAccountObjByIds(tenantId, supplierIds);
            Map<String, AccountObj> idAndAccountMap = accountObjByIds.stream().collect(Collectors.toMap(o -> o.getId(), v -> v));

            IObjectData iObjectData = new ObjectData();
            iObjectData.setDescribeApiName(AccountObjConstants.API_NAME);
            iObjectData.setTenantId(tenantId);
            iObjectData.setOrderBy(-10000);
            iObjectData.setId(accountId);
            Set<String> upDealerIds = Sets.newHashSet();
            distributors.stream().forEach(o -> {
                List<DistributorSupply> distributorSupplies = disMap.get(o);
                //客户的时候才会被当成是配送商是经销商，分销商可能就是真没配置。
                if (CollectionUtils.isEmpty(distributorSupplies)) {
                    if (type == 1) {
                        upDealerIds.add(o);
                    }
                } else {
                    upDealerIds.addAll(distributorSupplies.stream().map(distributorSupply -> distributorSupply.getUpDealerAccountId()).collect(Collectors.toList()));
                }
            });
            iObjectData.set(AccountObjConstants.Field.upDealerIds.getApiName(), Lists.newArrayList(upDealerIds));
            String upDealerDesc = upDealerIds.stream().map(o -> idAndAccountMap.get(o).getName()).collect(Collectors.joining(","));
            iObjectData.set(AccountObjConstants.Field.upDealerDesc.getApiName()
                    , upDealerDesc);
            String upDistributorDesc = distributors.stream().map(o -> idAndAccountMap.get(o).getName()).collect(Collectors.joining(","));
            if (type == 0) {
                iObjectData.set(AccountObjConstants.Field.upDistributorDesc.getApiName()
                        , upDealerDesc);
                iObjectData.set(AccountObjConstants.Field.upDistributorIds.getApiName(), Lists.newArrayList(upDealerIds));
            } else {
                iObjectData.set(AccountObjConstants.Field.upDistributorDesc.getApiName()
                        , upDistributorDesc);
                iObjectData.set(AccountObjConstants.Field.upDistributorIds.getApiName(), Lists.newArrayList(distributors));
            }

            update(User.systemUser(tenantId), iObjectData);
        } catch (Exception e) {
            log.info("updateAccountSupply error tenantId {},accountId  {} type {}", tenantId, accountId, type);
        }

    }

    /**
     * updateAccountSupply  的意义不明确，v2和updateAccountSupply  upAccountIds 参数不一致
     * 更新 门店商配送商 经销商字段逻辑
     * 对应逻辑表格
     * 客户类型 |   客户上的配送商字段   |   客户上的经销商字段
     * 门店   |   直接配送商可能是分销商也可能是经销商  |   对应的经销商，包含配送商字段下的经销商
     * 分销商  |   经销商 |   经销商
     *
     * @param tenantId
     * @param accountId          门店 或者  分销商id
     * @param directUpAccountIds 对应的直接配送商  上级配送商可能是经销商 可能是分销商
     * @param type               0是分销商  1 是客户
     */
    private void updateAccountSupplyV2(String tenantId, String accountId, List<String> directUpAccountIds, int type) {
        try {
            log.info("updateAccountSupply tenantId {},accountId  {} type {}", tenantId, accountId, type);
            User user = User.systemUser(tenantId);
            IActionContext actionContext = ActionContextExt.of(user).getContext();
            actionContext.put("triggerWorkflow", true);
            if (CollectionUtils.isEmpty(directUpAccountIds)) {
                //置空 供货商字段
                IObjectData iObjectData = new ObjectData();
                iObjectData.setDescribeApiName(AccountObjConstants.API_NAME);
                iObjectData.setTenantId(tenantId);
                iObjectData.setOrderBy(-10000);
                iObjectData.setId(accountId);
                //所有的经销商类型数据
                iObjectData.set(AccountObjConstants.Field.upDealerIds.getApiName(), ListUtils.EMPTY_LIST);
                iObjectData.set(AccountObjConstants.Field.upDealerDesc.getApiName(), "");
                //直接配送商 参数传过来的
                iObjectData.set(AccountObjConstants.Field.upDistributorDesc.getApiName(), "");
                iObjectData.set(AccountObjConstants.Field.upDistributorIds.getApiName(), ListUtils.EMPTY_LIST);
                //跟新
                serviceFacade.batchUpdateByFields(actionContext, Lists.newArrayList(iObjectData),
                        Lists.newArrayList(AccountObjConstants.Field.upDealerIds.getApiName(), AccountObjConstants.Field.upDealerDesc.getApiName(),
                                AccountObjConstants.Field.upDistributorDesc.getApiName(), AccountObjConstants.Field.upDistributorIds.getApiName()));
            } else {
                //更新参数构造
                IObjectData iObjectData = new ObjectData();
                iObjectData.setDescribeApiName(AccountObjConstants.API_NAME);
                iObjectData.setTenantId(tenantId);
                iObjectData.setOrderBy(-10000);
                iObjectData.setId(accountId);
                if (type == 1) {
                    //客户类型
                    List<DistributorSupply> distributorSupplyList = getDistributorSupplyByThisIds(tenantId, directUpAccountIds);
                    //所有相关数据 客户数据
                    Set<String> allUpAccountIds = Sets.newHashSet();
                    allUpAccountIds.addAll(directUpAccountIds);
                    for (DistributorSupply distributorSupply : distributorSupplyList) {
                        allUpAccountIds.add(distributorSupply.getUpDealerAccountId());
                        allUpAccountIds.add(distributorSupply.getThisDistributorAccountId());
                    }
                    List<AccountObj> allUpAccountList = getAccountObjByIds(tenantId, allUpAccountIds);
                    //经销商
                    Set<AccountObj> dealerAccounts = allUpAccountList.stream().filter(o -> AccountObjConstants.Value.dealer__c.toString().equals(o.getRecordType())).collect(Collectors.toSet());
                    iObjectData.set(AccountObjConstants.Field.upDealerIds.getApiName(), dealerAccounts.stream().map(o -> o.getId()).distinct().collect(Collectors.toList()));
                    iObjectData.set(AccountObjConstants.Field.upDealerDesc.getApiName()
                            , dealerAccounts.stream().map(o -> o.getName()).collect(Collectors.joining(",")));
                    //直接的配送商  包含经销商 和分销商
                    Set<AccountObj> disAccounts = allUpAccountList.stream().filter(o -> directUpAccountIds.contains(o.getId())).collect(Collectors.toSet());
                    iObjectData.set(AccountObjConstants.Field.upDistributorDesc.getApiName()
                            , disAccounts.stream().map(o -> o.getName()).collect(Collectors.joining(",")));
                    iObjectData.set(AccountObjConstants.Field.upDistributorIds.getApiName(), disAccounts.stream().map(o -> o.getId()).distinct().collect(Collectors.toList()));
                } else {
                    //分销商类型
                    //经销商
                    List<AccountObj> dealerAccounts = getAccountObjByIds(tenantId, directUpAccountIds);
                    iObjectData.set(AccountObjConstants.Field.upDistributorIds.getApiName(), dealerAccounts.stream().map(o -> o.getId()).distinct().collect(Collectors.toList()));
                    iObjectData.set(AccountObjConstants.Field.upDistributorDesc.getApiName()
                            , dealerAccounts.stream().map(o -> o.getName()).collect(Collectors.joining(",")));
                    iObjectData.set(AccountObjConstants.Field.upDealerIds.getApiName(), dealerAccounts.stream().map(o -> o.getId()).distinct().collect(Collectors.toList()));
                    iObjectData.set(AccountObjConstants.Field.upDealerDesc.getApiName()
                            , dealerAccounts.stream().map(o -> o.getName()).collect(Collectors.joining(",")));

                }

                //更新
                serviceFacade.batchUpdateByFields(actionContext, Lists.newArrayList(iObjectData),
                        Lists.newArrayList(AccountObjConstants.Field.upDealerIds.getApiName(), AccountObjConstants.Field.upDealerDesc.getApiName(),
                                AccountObjConstants.Field.upDistributorDesc.getApiName(), AccountObjConstants.Field.upDistributorIds.getApiName()));
            }

        } catch (Exception e) {
            log.info("updateAccountSupply error tenantId {},accountId  {} type {}", tenantId, accountId, type, e);
        }

    }


    /**
     * 更新上级配送商
     *
     * @param tenantId
     * @param distributorAccountId
     */
    @Override
    public void updateDistributorUpSupplyFields(String tenantId, String distributorAccountId) {
        //查到对应的上级
        List<DistributorSupply> distributorSupplyList = getDistributorSupplyByThisIds(tenantId, Lists.newArrayList(distributorAccountId));
        updateAccountSupplyV2(tenantId, distributorAccountId, distributorSupplyList.stream().map(o -> o.getUpDealerAccountId()).collect(Collectors.toList()), 0);
    }

    @Override
    public List<ObjectDataDocument> getSupplyDealerListByServiceCenterDepartmentIds(String tenantId, Set<String> usedSupplierIds, List<String> serviceCenterDepartmentIds) {
        SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                .inDepartmentAndSub(BaseField.dataOwnDepartment.getApiName(), serviceCenterDepartmentIds)
                .eq(BaseField.recordType.getApiName(), AccountObjConstants.Value.dealer__c.name());
        SearchQuery.SearchQueryBuilder searchQueryBuilder2 = SearchQuery.builder()
                .inDepartmentAndSub(AccountObjConstants.Field.otherDepartment.getApiName(), serviceCenterDepartmentIds)
                .eq(BaseField.recordType.getApiName(), AccountObjConstants.Value.dealer__c.name());

        if (CollectionUtils.isNotEmpty(usedSupplierIds)) {
            searchQueryBuilder.nin(BaseField.id.getApiName(), usedSupplierIds);
            searchQueryBuilder2.nin(BaseField.id.getApiName(), usedSupplierIds);
        }
        searchQueryBuilder.addOrWheres(searchQueryBuilder2);
        List<IObjectData> iObjectDataListByQuery = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQueryBuilder.build(), AccountObjConstants.API_NAME);
        return iObjectDataListByQuery.stream().map(o -> {
            ObjectDataDocument dealerObj = new ObjectDataDocument();
            dealerObj.put(BaseField.id.getApiName(), o.getId());
            dealerObj.put(BaseField.name.getApiName(), o.getName());
            ObjectDataDocument distributorObj = new ObjectDataDocument();
            distributorObj.put(BaseField.id.getApiName(), o.getId());
            distributorObj.put(BaseField.name.getApiName(), o.getName());
            dealerObj.put("distributors", Lists.newArrayList(distributorObj));
            return dealerObj;
        }).collect(Collectors.toList());
    }

    @Override
    public void checkSupplierExistDepartment(String tenantId, List<String> checkSupplierIds, List<String> serviceCenterDepartmentIds) {
        if (CollectionUtils.isEmpty(checkSupplierIds)) {
            return;
        }
        //转成业务组的id
        serviceCenterDepartmentIds = getServiceCenterDepartmentIds(tenantId, serviceCenterDepartmentIds);
        SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                .inDepartmentAndSub(BaseField.dataOwnDepartment.getApiName(), serviceCenterDepartmentIds)
                .in(BaseField.recordType.getApiName(),
                        Lists.newArrayList(AccountObjConstants.Value.dealer__c.name()
                                , AccountObjConstants.Value.distributor__c.name()));
        SearchQuery.SearchQueryBuilder searchQueryBuilder2 = SearchQuery.builder()
                .inDepartmentAndSub(AccountObjConstants.Field.otherDepartment.getApiName(), serviceCenterDepartmentIds)
                .in(BaseField.recordType.getApiName(),
                        Lists.newArrayList(AccountObjConstants.Value.dealer__c.name()
                                , AccountObjConstants.Value.distributor__c.name()));
        if (CollectionUtils.isNotEmpty(checkSupplierIds)) {
            searchQueryBuilder.in(BaseField.id.getApiName(), checkSupplierIds);
            searchQueryBuilder2.in(BaseField.id.getApiName(), checkSupplierIds);
        }
        searchQueryBuilder = searchQueryBuilder.addOrWheres(searchQueryBuilder2);
        List<IObjectData> iObjectDataListByQuery = getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQueryBuilder.build(), AccountObjConstants.API_NAME);
        if (iObjectDataListByQuery.size() < checkSupplierIds.size()) {
            Set<String> queryIds = iObjectDataListByQuery.stream().map(IObjectData::getId).collect(Collectors.toSet());
            Set<String> noCheckIds = checkSupplierIds.stream().filter(o ->
                    !queryIds.contains(o)
            ).collect(Collectors.toSet());
            List<AccountObj> accountObjByIds = getAccountObjByIds(tenantId, Lists.newArrayList(noCheckIds));
            Set<String> noCheckNames = accountObjByIds.stream().map(AccountObj::getName).collect(Collectors.toSet());
            throw new ValidateException(String.format("%s %s 不在门店对应的业务处范围下", AccountObjConstants.Value.dealer__c.getDisName() //ignoreI18n
                    , noCheckNames.stream().collect(Collectors.joining(","))));
        }
    }

    @Override
    public List<IObjectData> createDefaultProductCollection(String tenantId, List<String> ids) {
        List<IObjectData> argsIObjectDatas = new ArrayList<>();
        //兼容
        ids.removeIf(o -> o == null);
        Set<Map.Entry<Boolean, List<String>>> entries = ids.stream().collect(Collectors.groupingBy(o -> o.length() > 13)).entrySet();
        entries.stream().forEach(entry -> {
            if (entry.getKey()) {
                //客户的
                List<String> accountIds = entry.getValue();
                List<AccountObj> accountObjByIds = getAccountObjByIds(tenantId, accountIds);
                List<String> departmentIds = accountObjByIds.stream().map(o -> o.getDepartmentId()).distinct().collect(Collectors.toList());
                Map<String, String> serviceCenterDepartmentIdsMap = getServiceCenterDepartmentIdsMap(tenantId, departmentIds);
                accountObjByIds.stream().forEach(o -> o.setDepartmentId(serviceCenterDepartmentIdsMap.get(o.getDepartmentId())));
                for (AccountObj accountObj : accountObjByIds) {
                    IObjectData objectData = createBaseObjectData(tenantId, accountObj.getOwner(), ProductCollectionObjConstants.API_NAME);
                    AccountObjConstants.Value recordType = AccountObjConstants.Value.of(accountObj.getRecordType());
                    if (recordType == null) {
                        throw new ValidateException(String.format("%s 业务类型 不正确", accountObj.getName())); //ignoreI18n
                    }
                    switch (recordType) {
                        case default__c:
                            //门店
                            throw new ValidateException(String.format("%s 业务类型 不正确", accountObj.getName())); //ignoreI18n
                        case dealer__c:
                            //经销商
                            objectData.setRecordType(ProductCollectionObjConstants.Value.recordType_dealer.getValue());
                            objectData.set(ProductCollectionObjConstants.Field.dealerId.getApiName(), accountObj.getId());
                            objectData.set(ProductCollectionObjConstants.Field.businessScopeType.getApiName(),
                                    ProductCollectionObjConstants.Value.businessScopeType_2.getValue());
                            break;
                        case distributor__c:
                            objectData.setRecordType(ProductCollectionObjConstants.Value.recordType_distributor.getValue());
                            objectData.set(ProductCollectionObjConstants.Field.distributorId.getApiName(), accountObj.getId());
                            objectData.set(ProductCollectionObjConstants.Field.businessScopeType.getApiName(),
                                    ProductCollectionObjConstants.Value.businessScopeType_3.getValue());
                            break;
                        default:

                    }
                    objectData.set(ProductCollectionObjConstants.Field.departmentId.getApiName(), Lists.newArrayList(accountObj.getDepartmentId()));
                    objectData.set(BaseField.dataOwnDepartment.getApiName(), Lists.newArrayList(accountObj.getDepartmentId()));
                    objectData.setName(accountObj.getName());
                    //营业部赋值
                    objectData.set(ProductCollectionObjConstants.Field.salesDepartmentId.getApiName(),
                            getDepartmentList(tenantId, Lists.newArrayList(accountObj.getDepartmentId()))
                                    .get(0).get("parent_id", List.class)
                    );
                    argsIObjectDatas.add(objectData);
                }
            } else {
                //部门的
                List<String> depIds = entry.getValue();
                List<IObjectData> departmentList = getDepartmentList(tenantId, depIds);
                for (IObjectData iObjectData : departmentList) {
                    IObjectData objectData = createBaseObjectData(tenantId, "-10000", ProductCollectionObjConstants.API_NAME);
                    objectData.setRecordType(ProductCollectionObjConstants.Value.recordType_server_center.getValue());
                    objectData.set(ProductCollectionObjConstants.Field.businessScopeType.getApiName(),
                            ProductCollectionObjConstants.Value.businessScopeType_1.getValue());
                    objectData.set(ProductCollectionObjConstants.Field.departmentId.getApiName(),
                            Lists.newArrayList(iObjectData.get("dept_id").toString()));
                    objectData.set(BaseField.dataOwnDepartment.getApiName(),
                            Lists.newArrayList(iObjectData.get("dept_id").toString()));
                    objectData.setName(iObjectData.getName());
                    //营业部赋值
                    objectData.set(ProductCollectionObjConstants.Field.salesDepartmentId.getApiName()
                            , iObjectData.get("parent_id", List.class));

                    argsIObjectDatas.add(objectData);
                }

            }
        });


        return batchSaveOfSingleApiName(argsIObjectDatas, User.systemUser(tenantId));
    }

    @Override
    public void delProductCollection(String tenantId, List<String> accountIds) {
        //todo 删除 子集
        fmcgThreadPoolExecutor.execute(() -> {
            // 删除供货关系
            delDealerSupply(tenantId, accountIds);
        });
    }

    @Override
    public void delDealerSupply(String tenantId, List<String> accountIds) {
        List<IObjectData> dealerSupplyBySuppliers = getDealerSupplyBySuppliers(tenantId, accountIds);
        batchInvalidAndDelOfSingleApiName(dealerSupplyBySuppliers, User.systemUser(tenantId));
        delSupplyStore(tenantId, accountIds);
        delDistributorSupplyByUpIds(tenantId, accountIds);
    }

    @Override
    public void delDistributorSupplyByUpIds(String tenantId, List<String> accountIds) {
        List<IObjectData> distributorSuppliers = getDistributorSuppliersByUpIds(tenantId, null, accountIds);
        distributorSuppliers.stream().forEach(o -> {
            accountIds.add(o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class));
            accountIds.add(o.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), String.class));
        });
        batchInvalidAndDelOfSingleApiName(distributorSuppliers, User.systemUser(tenantId));
        delSupplyStore(tenantId, accountIds);
    }


    @Override
    public void delDistributorSupplyByDownIds(String tenantId, List<String> accountIds) {
        List<IObjectData> distributorSuppliers = getDistributorSuppliersByDownIds(tenantId, null, accountIds);
        batchInvalidAndDelOfSingleApiName(distributorSuppliers, User.systemUser(tenantId));
        delSupplyStore(tenantId, accountIds);
    }

    @Override
    public void delDistributorSupplyByDownIdAndUpIds(String tenantId, String downId, List<String> upIds) {
        List<IObjectData> distributorSuppliers = getDistributorSuppliers(tenantId, null, Lists.newArrayList(downId), upIds);
        batchInvalidAndDelOfSingleApiName(distributorSuppliers, User.systemUser(tenantId));
        // 只是删除部分 下级配送商关系 所以 不用删除供货门店的数据
    }

    @Override
    public void delSupplyStore(String tenantId, List<String> accountIds) {
        List<IObjectData> supplyStoreByShopIds = getSupplyStoreByShopIds(tenantId, accountIds);
        batchInvalidAndDelOfSingleApiName(supplyStoreByShopIds, User.systemUser(tenantId));
        delSpecialStore(tenantId, accountIds);
    }

    @Override
    public void delSupplyStoreBySupplierIds(String tenantId, List<String> accountIds) {
        List<IObjectData> supplyStoreByShopIds = getSupplyStoreByDealerIds(tenantId, accountIds);
        supplyStoreByShopIds.parallelStream().forEach(iObjectData -> {
            String shopId = iObjectData.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class);
            String dealerId = iObjectData.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class);
            delSpecialStore(tenantId, shopId, Lists.newArrayList(dealerId));
        });
        batchInvalidAndDelOfSingleApiName(supplyStoreByShopIds, User.systemUser(tenantId));
    }

    @Override
    public void delSupplyStoreByAccountIdAndSupplierIds(String tenantId, String accountId, List<String> supplierIds) {
        List<IObjectData> supplyStoreByShopIds = getSupplyStoreByShopIdAndSupplierIds(tenantId, accountId, supplierIds);
        batchInvalidAndDelOfSingleApiName(supplyStoreByShopIds, User.systemUser(tenantId));
        delSpecialStore(tenantId, accountId, supplierIds);
    }

    @Override
    public void delSpecialStore(String tenantId, List<String> accountIds) {
        List<IObjectData> supplyStoreByShopIds = getSpecialSupplyByShopIds(tenantId, accountIds);
        batchInvalidAndDelOfSingleApiName(supplyStoreByShopIds, User.systemUser(tenantId));

    }

    public void delSpecialStore(String tenantId, String accountId, List<String> supplierIds) {
        List<IObjectData> supplyStoreByShopIds = getSpecialSupplyByShopIds(tenantId, accountId, supplierIds);
        batchInvalidAndDelOfSingleApiName(supplyStoreByShopIds, User.systemUser(tenantId));

    }

    @Override
    public QueryResult<IObjectData> getAllProductList(String tenantId, SearchTemplateQuery query) {
//        SearchQuery searchQuery = SearchQuery.builder()
//                 .in(CoveredProductObjConstants.Field.belongGroup.getApiName(), relatedIds)
//                .build();
        SearchQuery searchQuery;
        if (Objects.nonNull(query)) {
            searchQuery = SearchQuery.builder().copyOf(query)
                    .build();
        } else {
            searchQuery = SearchQuery.builder().build();
        }
        return getQueryDataListByQuery(User.systemUser(tenantId), searchQuery, "ProductObj", true);

    }

    @Override
    public QueryResult<IObjectData> getProductListByQuery(String tenantId, SearchTemplateQuery query, boolean isQueryAll) {
//        SearchQuery searchQuery = SearchQuery.builder()
//                 .in(CoveredProductObjConstants.Field.belongGroup.getApiName(), relatedIds)
//                .build();
        if (isQueryAll) {
            return getAllProductList(tenantId, query);
        }
        SearchQuery searchQuery;
        if (Objects.nonNull(query)) {
            searchQuery = SearchQuery.builder().copyOf(query)
                    .build();
        } else {
            searchQuery = SearchQuery.builder().build();
        }
        return getQueryDataListByQuery(User.systemUser(tenantId), searchQuery, "ProductObj", false);

    }


    @Override
    public List<IObjectData> createAvailableAccountList(String tenantId, String mainId, List<String> acountIdList, Object ownDepartment) {
        List<IObjectData> availableAccountObjList = convertAvailableAccountList(tenantId, mainId, acountIdList, ownDepartment);
        return batchSaveOfSingleApiName(availableAccountObjList, User.systemUser(tenantId));
    }

    @NotNull
    public List<IObjectData> convertAvailableAccountList(String tenantId, String mainId, List<String> acountIdList, Object ownDepartment) {
        return acountIdList.stream().map(o -> {
            IObjectData availableAccountObj = createBaseObjectData(tenantId, "-10000", AvailableAccountObjConstants.API_NAME);
            availableAccountObj.set(AvailableAccountObjConstants.Field.availableRangeId.getApiName(), mainId);
            if (ownDepartment != null) {
                availableAccountObj.set(BaseField.dataOwnDepartment.getApiName(), ownDepartment);
            }
            if (o.equals("ALL")) {
                availableAccountObj.set(AvailableAccountObjConstants.Field.applyRange.getApiName(),
                        AvailableAccountObjConstants.Value.ALL.name());
            } else {
                availableAccountObj.set(AvailableAccountObjConstants.Field.accountId.getApiName(), o);
                availableAccountObj.set(AvailableAccountObjConstants.Field.applyRange.getApiName(),
                        AvailableAccountObjConstants.Value.FIXED.name());
            }
            return availableAccountObj;
        }).collect(Collectors.toList());
    }

    @Override
    public List<IObjectData> createAvailableProductList(String tenantId, String mainId, List<String> productIdList, Object ownDepartment) {
        List<IObjectData> availableProductObjList = convertAvailableProductList(tenantId, mainId, productIdList, ownDepartment);
        List<IObjectData> iObjectDataList = Lists.newArrayList();
        availableProductObjList.stream().collect(Collectors.groupingBy(o -> o.get(AvailableProductObjConstants.Field.productId.getApiName(), String.class)))
                .forEach((k, v) -> {
                    User user = User.systemUser(tenantId);
                    if ("ALL".equals(k)) {
                        for (IObjectData iObjectData : v) {
                            IActionContext context = ActionContextExt.of(user, RequestContextManager.getContext())
                                    .allowUpdateInvalid(true)
                                    .setNotValidate(true)
                                    .getContext();
                            context.setChildObjValidateSkip(true);
                            serviceFacade.saveObjectData(user, iObjectData, context);
                        }
                    } else {
                        iObjectDataList.addAll(batchSaveOfSingleApiName(availableProductObjList, user));
                    }
                });
        return availableProductObjList;
    }

    @NotNull
    public List<IObjectData> convertAvailableProductList(String tenantId, String mainId, List<String> productIdList, Object ownDepartment) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return ListUtils.EMPTY_LIST;
        }
        return productIdList.stream().map(o -> {
            IObjectData availableProductObj = createBaseObjectData(tenantId, "-10000", AvailableProductObjConstants.API_NAME);
            availableProductObj.set(AvailableProductObjConstants.Field.availableRangeId.getApiName(), mainId);
            if (o.equals("ALL")) {
                availableProductObj.set(AvailableProductObjConstants.Field.applyRange.getApiName(),
                        AvailableProductObjConstants.Value.ALL.name());
                availableProductObj.set(AvailableProductObjConstants.Field.productId.getApiName(), "ALL");
            } else {
                availableProductObj.set(AvailableProductObjConstants.Field.productId.getApiName(), o);
                availableProductObj.set(AvailableProductObjConstants.Field.applyRange.getApiName(),
                        AvailableProductObjConstants.Value.FIXED.name());
            }
            if (ownDepartment != null) {
                availableProductObj.set(BaseField.dataOwnDepartment.getApiName(), ownDepartment);
            }
            return availableProductObj;
        }).collect(Collectors.toList());
    }

    @Override
    public void deleteAvailableAccountList(String tenantId, String mainId, List<String> accountIds) {
        List<IObjectData> objectDataList = getAvailableAccountList(tenantId, mainId, accountIds);
        batchInvalidAndDelOfSingleApiName(objectDataList, User.systemUser(tenantId));
    }

    @Override
    public List<IObjectData> getAvailableAccountList(String tenantId, String mainId, List<String> accountIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .eq(AvailableAccountObjConstants.Field.availableRangeId.getApiName(), mainId)
                .in(AvailableAccountObjConstants.Field.accountId.getApiName(), accountIds).build();

        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, AvailableAccountObjConstants.API_NAME);
    }

    @Override
    public void deleteAvailableRange(String tenantId, List<String> dealerSupplyIds) {
        List<IObjectData> objectDataList = getAvailableRangeList(tenantId, dealerSupplyIds);
        batchInvalidAndDelOfSingleApiName(objectDataList, User.systemUser(tenantId));
    }


    public List<IObjectData> getAvailableRangeList(String tenantId, List<String> dealerSupplyIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(AvailableRangeObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyIds).build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, AvailableRangeObjConstants.API_NAME);
    }

    public List<IObjectData> getAvailableRangeExcludeList(String tenantId, List<String> dealerSupplyIds, List<String> excludeSpecialAccountIds) {
        SearchQuery.SearchQueryBuilder builder = SearchQuery.builder();
        builder.in(AvailableRangeObjConstants.Field.dealerSupplyId.getApiName(), dealerSupplyIds);
        if (CollectionUtils.isNotEmpty(excludeSpecialAccountIds)) {
            builder.nin(AvailableRangeObjConstants.Field.storeId.getApiName(), excludeSpecialAccountIds);
        }
        SearchQuery searchQuery = builder.build();
        return getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, AvailableRangeObjConstants.API_NAME).stream().filter(o -> StringUtils.isNotBlank(o.get(AvailableRangeObjConstants.Field.storeId.getApiName(), String.class))).collect(Collectors.toList());
    }

    @Override
    public void updateAccountAreaInfo(String tenantId, int userId, IObjectData areaInfo, List<String> accountIds, Boolean isYL) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }
        accountIds.removeIf(Objects::isNull);
        if (!isYL) {
            updateAccountAreaInfoStandard(tenantId, userId, areaInfo, accountIds);
        } else {
            List<String> areaOwner = Objects.nonNull(areaInfo) && Objects.nonNull(areaInfo.get(AreaManageConstants.AREA_OWNER)) ? (List<String>) areaInfo.get(AreaManageConstants.AREA_OWNER) : null;
            String areaId = Objects.nonNull(areaInfo) ? areaInfo.getId() : "";

            List<List<String>> subAccountList = Lists.partition(accountIds, 200);
            for (List<String> accounts : subAccountList) {
                List<JSONObject> updataData = Lists.newArrayList();
                for (String accountId : accounts) {
                    JSONObject data = new JSONObject();
                    data.put("_id", accountId);
                    data.put("route_owner__c", CollectionUtils.isEmpty(areaOwner) ? Lists.newArrayList() : areaOwner);
                    data.put("belong_routeNew__c", areaId);
                    updataData.add(data);
                }
//            log.info("updateAccountAreaInfo is updataData{},{},{}",updataData.size());

                batchUpdateDataByProxy(Integer.valueOf(tenantId), userId, CommonConstants.ACCOUNT_OBJ, updataData);
            }
        }
    }

    protected void updateAccountAreaInfoStandard(String tenantId, int userId, IObjectData areaInfo, List<String> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }

        List<String> areaOwner = Objects.nonNull(areaInfo) && Objects.nonNull(areaInfo.get(AreaManageConstants.AREA_OWNER_PRESER)) ? (List<String>) areaInfo.get(AreaManageConstants.AREA_OWNER_PRESER) : null;
        String areaId = Objects.nonNull(areaInfo) ? areaInfo.getId() : "";

        List<List<String>> subAccountList = Lists.partition(accountIds, 200);
        for (List<String> accounts : subAccountList) {
            List<JSONObject> updataData = Lists.newArrayList();
            for (String accountId : accounts) {
                JSONObject data = new JSONObject();
                data.put("_id", accountId);
                data.put(AreaManageConstants.AREA_OWNER_PRESER, CollectionUtils.isEmpty(areaOwner) ? Lists.newArrayList() : areaOwner);
                data.put(AreaManageConstants.BELONG_AREA_ACCOUNT, areaId);
                updataData.add(data);
            }
            batchUpdateDataByProxy(Integer.valueOf(tenantId), userId, CommonConstants.ACCOUNT_OBJ, updataData);
        }
    }

    @Override
    public void updateCoveredProduct(String tenantId, String thisDealerId) {

        List<IObjectData> objectDataList = queryCollectionObjByLeaderIds(tenantId, Lists.newArrayList(thisDealerId));
        List<IObjectData> cpData = supplyService.getDesignateProductListByRelatedIds(tenantId, CoveredProductObjConstants.API_NAME, Lists.newArrayList(objectDataList.get(0).getId()));
        List<IObjectData> delData = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cpData)) {
            Set<String> productIds = supplyService.getProductListByDistributionId(tenantId, thisDealerId);
            cpData.forEach(o -> {
                if (!productIds.contains(o.get("relation_product").toString())) {
                    delData.add(o);
                }
            });
            if (CollectionUtils.isNotEmpty(delData)) {
                batchInvalidAndDelOfSingleApiName(delData, User.systemUser(tenantId));
                log.info("updateCoveredProduct tenantId:{},thisDealerId:{},delData:{}", tenantId, thisDealerId, delData.size());
            }
        }


    }


    @Override
    public List<IObjectData> getOtherSupply(String tenantId, Set<String> accountIds, String transferApiName, String excludeSupplierId) {
        switch (transferApiName) {
            //供货分销
            case DistributorSupplyObjConstants.API_NAME:

                SearchQuery.SearchQueryBuilder queryBuilder = SearchQuery.builder()
                        .neq(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), excludeSupplierId)
                        .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), accountIds);
                return getAllIObjectDataListByQuery(User.systemUser(tenantId), queryBuilder.build(), DistributorSupplyObjConstants.API_NAME);
            //供货门店
            case SupplyStoreObjConstants.API_NAME:
                SearchQuery.SearchQueryBuilder queryBuilder2 = SearchQuery.builder()
                        .neq(SupplyStoreObjConstants.Field.upDealerId.getApiName(), excludeSupplierId)
                        .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), accountIds);
                return getAllIObjectDataListByQuery(User.systemUser(tenantId), queryBuilder2.build(), SupplyStoreObjConstants.API_NAME);
            default:
                throw new ValidateException("不支持的转换类型"); //ignoreI18n
        }
    }


    @Override
    public  Map<String, List<ObjectDataDocument>> createSupplyApproObj(User user, SupplyOperateEnum supplyOperateEnum, SupplyOperaContext supplyOperaContext) {

        RequestContext context = RequestContextManager.getContext();
        Map<String, List<ObjectDataDocument>>  details = Maps.newHashMap();
        //主对象
        IObjectData mainObjectData = createBaseObjectData(user.getTenantId(), user.getUserId(), SupplyChangeFields.API_NAME);
        mainObjectData.set(SupplyChangeFields.IN_UP_CUSTOMER, supplyOperaContext.getInUpCustomerId());
        mainObjectData.set(SupplyChangeFields.OUT_UP_CUSTOMER, supplyOperaContext.getOutUpCustomerId());
        mainObjectData.set(SupplyChangeFields.OPERATE, String.valueOf(supplyOperateEnum.getOperateCode()));
        mainObjectData.set(SupplyChangeFields.EXECUTE_STATUS, "0");
        mainObjectData.set(SupplyChangeFields.ARGS, JSON.toJSONString(supplyOperaContext.getArgs()));
        mainObjectData.set(SupplyChangeFields.IN_UP_SUPPLY, supplyOperaContext.getInUpSupplyId());
        mainObjectData.set(SupplyChangeFields.OUT_UP_SUPPLY, supplyOperaContext.getOutUpSupplyId());
        //子对象
        if (CollectionUtils.isNotEmpty(supplyOperaContext.getCustermerIds())){
            for (String lockCustomerId : supplyOperaContext.getCustermerIds()) {
                IObjectData supplyChangeCustomerDetail = createBaseObjectData(user.getTenantId(), user.getUserId(), SupplyChangeCustomerDetailFields.API_NAME);
                supplyChangeCustomerDetail.set(SupplyChangeCustomerDetailFields.CUSTOMER, lockCustomerId);
                supplyChangeCustomerDetail.set(SupplyChangeCustomerDetailFields.RANGE, "1");
                details.computeIfAbsent(SupplyChangeCustomerDetailFields.API_NAME,k->Lists.newArrayList()).add(ObjectDataDocument.of(supplyChangeCustomerDetail));
            }
        }else{
            IObjectData supplyChangeCustomerDetail = createBaseObjectData(user.getTenantId(), user.getUserId(), SupplyChangeCustomerDetailFields.API_NAME);
            supplyChangeCustomerDetail.set(SupplyChangeCustomerDetailFields.RANGE, "0");
            details.computeIfAbsent(SupplyChangeCustomerDetailFields.API_NAME,k->Lists.newArrayList()).add(ObjectDataDocument.of(supplyChangeCustomerDetail));
        }
        if (CollectionUtils.isNotEmpty(supplyOperaContext.getProductIds())){
            for (String lockProductId : supplyOperaContext.getProductIds()) {
                IObjectData supplyChangeProductDetail = createBaseObjectData(user.getTenantId(), user.getUserId(), SupplyChangeProductDetailFields.API_NAME);
                supplyChangeProductDetail.set(SupplyChangeProductDetailFields.PRODUCT, lockProductId);
                supplyChangeProductDetail.set(SupplyChangeProductDetailFields.RANGE, "1");
                details.computeIfAbsent(SupplyChangeProductDetailFields.API_NAME,k->Lists.newArrayList()).add(ObjectDataDocument.of(supplyChangeProductDetail));
            }
        }else{
            IObjectData supplyChangeProductDetail = createBaseObjectData(user.getTenantId(), user.getUserId(), SupplyChangeProductDetailFields.API_NAME);
            supplyChangeProductDetail.set(SupplyChangeProductDetailFields.RANGE, "0");
            details.computeIfAbsent(SupplyChangeProductDetailFields.API_NAME,k->Lists.newArrayList()).add(ObjectDataDocument.of(supplyChangeProductDetail));
        }

        //调用本地的新建方法
        BaseObjectSaveAction.Arg args = new BaseObjectSaveAction.Arg();
        args.setObjectData(ObjectDataDocument.of(mainObjectData));
        args.setDetails(details);
        ActionContext actionContext = new ActionContext(context,SupplyChangeFields.API_NAME, ObjectAction.CREATE.getActionCode());
        actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        BaseObjectSaveAction.Result result = serviceFacade.triggerAction(actionContext, args, BaseObjectSaveAction.Result.class);
        Map<String, List<ObjectDataDocument>> map = result.getDetails();
        map.put(SupplyChangeFields.API_NAME,Lists.newArrayList(result.getObjectData()));
        return map;
    }

    @Override
    public void fillSupplyCustomerApproObjDetail(String tenantId, IObjectData mainObj, List<String> accountIds) {
        //根据主对象id 删除所有详情
        batchInvalidAndDelByQuery(User.systemUser(tenantId), SearchQuery.builder().eq(SupplyChangeCustomerDetailFields.SUPPLY_CHANGE, mainObj.getId()), SupplyChangeCustomerDetailFields.API_NAME);
        //生成新的
        List<IObjectData> iObjectDataList = convertSupplyApproObjDetailObjByMainObj(tenantId, mainObj, accountIds);
        batchSaveOfSingleApiName(iObjectDataList, User.systemUser(tenantId));
    }
    @Override
    public void fillSupplyProductApproObjDetail(String tenantId, IObjectData mainObj, List<Pair<IObjectData, List<String>>> supplyApproAndProducts, int transferType) {
        if (CollectionUtils.isEmpty(supplyApproAndProducts)) {
            return;
        }
        List<IObjectData> approProductList = Lists.newArrayList();
        supplyApproAndProducts.stream().forEach(p->{
            String customerId = p.first.get(transferType == 1? DistributorSupplyObjConstants.Field.thisDealerId.getApiName() : DistributorSupplyObjConstants.Field.thisDealerId.getApiName(),String.class);
            List<String> productIds = p.second;
            if (CollectionUtils.isEmpty(productIds) || productIds.contains("ALL")) {
                return;
            }
            approProductList.addAll(convertSupplyApproProductDetail(tenantId, mainObj, customerId, productIds));
        });
        //根据主对象id 删除产品详情
//        batchInvalidAndDelByQuery(User.systemUser(tenantId), SearchQuery.builder().eq(SupplyChangeProductDetailFields.SUPPLY_CHANGE, mainObj.getId()), SupplyChangeProductDetailFields.API_NAME);
        //生成新的
        batchSaveOfSingleApiName(approProductList, User.systemUser(tenantId));
    }

    private List<IObjectData> convertSupplyApproProductDetail(String tenantId, IObjectData mainObj, String customerId, Collection<String> productIds) {
        return productIds.stream().map(o -> {
            IObjectData supplyChangeProductDetail = createBaseObjectData(tenantId, mainObj.getOwner().get(0), SupplyChangeProductDetailFields.API_NAME);
            supplyChangeProductDetail.set(SupplyChangeProductDetailFields.SUPPLY_CHANGE, mainObj.getId());
            supplyChangeProductDetail.set(SupplyChangeProductDetailFields.PRODUCT, o);
            supplyChangeProductDetail.set(SupplyChangeProductDetailFields.CUSTOMER, customerId);
            supplyChangeProductDetail.set(SupplyChangeProductDetailFields.RANGE, "1");
            return supplyChangeProductDetail;
        }).collect(Collectors.toList());
    }

    private List<IObjectData> convertSupplyApproObjDetailObjByMainObj(String tenantId, IObjectData mainObj, List<String> accountIds) {
        return accountIds.stream().map(o -> {
            IObjectData supplyChangeCustomerDetail = createBaseObjectData(tenantId, mainObj.getOwner().get(0), SupplyChangeCustomerDetailFields.API_NAME);
            supplyChangeCustomerDetail.set(SupplyChangeCustomerDetailFields.SUPPLY_CHANGE, mainObj.getId());
            supplyChangeCustomerDetail.set(SupplyChangeCustomerDetailFields.CUSTOMER, o);
            supplyChangeCustomerDetail.set(SupplyChangeCustomerDetailFields.RANGE, "1");
            return supplyChangeCustomerDetail;
        }).collect(Collectors.toList());
    }


}
