package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;


/**
 * @program: fs-crm-fmcg
 * @description: DistributorSupplyObj add
 * @author: zhangsm
 * @create: 2021-04-29 14:42
 **/
public class ProductCollectionInvalidAction extends StandardInvalidAction {

    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
        if (FmcgGray.Checkins.EA.isYLProductCollection.gray(ea)) {
            //删除去掉重新同步的逻辑，因为如果有问题 会禁止删除
//            ObjectDataDocument objectData = after.getObjectData();
//            String type = objectData.get(BaseField.recordType.getApiName()).toString();
//            String dealerId = null;
//            if (ProductCollectionObjConstants.Value.isDealer(type)){
//                dealerId = objectData.get(ProductCollectionObjConstants.Field.dealerId.getApiName()).toString();
//            }else if (ProductCollectionObjConstants.Value.isDistributor(type)){
//                dealerId = objectData.get(ProductCollectionObjConstants.Field.distributorId.getApiName()).toString();
//            }
//            if (dealerId != null){
//                List<String> delDealerIds = Lists.newArrayList(dealerId);
//                //删除对应的供货门店
//                supplyDao.delSupplyStore(actionContext.getTenantId(),delDealerIds);
//                //删除对应的下级供货商
//                supplyDao.delDistributorSupplyByUpIds(actionContext.getTenantId(),delDealerIds);
//                //删除可售范围
//                supplyDao.deleteAvailableRange(actionContext.getTenantId(),delDealerIds);
//            }

        }
        return after;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
        if (FmcgGray.Checkins.EA.isYLProductCollection.gray(ea)) {
            supplyService.checkDelProductCollection(actionContext.getTenantId(),Lists.newArrayList(arg.getObjectDataId()));
//            List<IObjectData> data = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), Lists.newArrayList(arg.getObjectDataId()), ProductCollectionConstants.PRODUCT_COLLECTION_OBJ);
//            String recordType = data.get(0).getRecordType();
//            String name = data.get(0).getName();
//            if ("server_center__c".equals(recordType)) {
//
//                Filter scFilter = new Filter();
//                scFilter.setFieldName("service_center");
//                scFilter.setOperator(Operator.EQ);
//                scFilter.setFieldValues(Lists.newArrayList(name));
//                SearchTemplateQuery scSearchQuery = new SearchTemplateQuery();
//                scSearchQuery.getFilters().add(scFilter);
//                List<IObjectData> serviceLists = serviceFacade.findBySearchQuery(actionContext.getUser(), ProductCollectionConstants.PRODUCT_COLLECTION_OBJ, scSearchQuery).getData();
//                if (CollectionUtils.notEmpty(serviceLists)) {
//                    throw new ValidateException(name + "正在被使用不能作废");
//                }
//
//            } else if ("dealer__c".equals(recordType)) {
//                Filter deFilter = new Filter();
//                deFilter.setFieldName("dealer");
//                deFilter.setOperator(Operator.EQ);
//                deFilter.setFieldValues(Lists.newArrayList(name));
//                SearchTemplateQuery deSearchQuery = new SearchTemplateQuery();
//                deSearchQuery.getFilters().add(deFilter);
//                List<IObjectData> dealerLists = serviceFacade.findBySearchQuery(actionContext.getUser(), ProductCollectionConstants.PRODUCT_COLLECTION_OBJ, deSearchQuery).getData();
//                if (CollectionUtils.notEmpty(dealerLists)) {
//                    throw new ValidateException(name + "正在被使用不能作废");
//                }
//
//            } else {
//                // 分销商
//
//            }
        }


//        @Override
//    protected void before(Arg arg) {
//        super.before(arg);
//        List<IObjectData> usingList = supplyService.distributorSupplyUsing(actionContext.getTenantId(), arg.getObjectData());
//        if (CollectionUtils.isNotEmpty(usingList)){
//            throw new ValidateException(String.format("数据 %s 正在被供货关系 %s 使用，无法作废",
//                    arg.getObjectData().get(BaseField.name.getApiName()).toString(),
//                    usingList.stream().map(o->o.getName()).collect(Collectors.joining(","))
//                    ));
//        }
//    }
    }
}
