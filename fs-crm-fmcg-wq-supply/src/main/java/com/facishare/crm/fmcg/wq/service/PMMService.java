package com.facishare.crm.fmcg.wq.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.appserver.checkins.api.model.BaseResult;
import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.dao.BaseDaoInterface;
import com.facishare.crm.fmcg.wq.dao.PublicEmployeeDao;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.fsi.proxy.FsiServiceProxyFactoryBean;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.service.model.ObjectDataDocument;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 促销员操作 相关逻辑
 * @author: zhangsm
 * @create: 2023-06-20 14:15
 **/
@Component
@Slf4j
public class PMMService {
    @Autowired
    private BaseDaoInterface baseDao;
    @Autowired
    CheckinsProxy checkinsProxy;

    @Autowired
    private PublicEmployeeDao publicEmployeeDao;

    @Autowired
    ServiceFacade serviceFacade;

    /**
     * 停用互联用户
     */
    public void stopPublicEmployee(String tenantId, String userId) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(tenantId)){
           throw  new ValidateException("停用互联用户参数异常"); //ignoreI18n
        }
        checkinsProxy.stopPublicEmployee(tenantId,tenantId, userId);
    }
    /**
     * 启用互联用户
     * @param tenantId
     * @param userId
     */
    public void startPublicEmployee(String tenantId, String userId) {
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(tenantId)){
          throw new ValidateException("启用互联用户参数异常"); //ignoreI18n
        }
        checkinsProxy.startPublicEmployee(tenantId,tenantId, userId);
    }
    public void startPublicEmployee(IObjectData iObjectData) {
        BaseResult baseResult = checkinsProxy.startPublicEmployeeByPromoter(iObjectData.getTenantId(), ObjectDataDocument.of(iObjectData));
        if (baseResult.getErrorCode() != 0){
            throw  new ValidateException(baseResult.getMessage());
        }
    }
    /**
     * 作废互联用户
     * @param tenantId
     * @param outerUserIds
     */
    public void invalidPublicEmployee(String tenantId, List<String> outerUserIds) {
        if (CollectionUtils.isEmpty(outerUserIds)){
            return;
        }
        baseDao.invalidData(User.systemUser(tenantId), SearchQuery.builder().in("_id",outerUserIds), "PublicEmployeeObj");
    }

    /**
     * 验证PromoterObj 是否是离职状态
     *
     * @param tenantId
     * @param promoterId
     * @return
     */
    public boolean isLeave(String tenantId, String promoterId) {
        IObjectData iObjectData = baseDao.getById(tenantId, PromoterFields.API_NAME, promoterId);
        if (iObjectData != null){
            return Optional.ofNullable(iObjectData.get(PromoterFields.IO_STATUS)).orElse("1").equals("0");
        }
        return true;
    }
    public boolean isLeave(String tenantId, IObjectData iObjectData) {
        if (iObjectData != null){
            return Optional.ofNullable(iObjectData.get(PromoterFields.IO_STATUS,String.class)).orElse("1").equals("0");
        }
        return true;
    }
    public IObjectData getPromoterById(String tenantId, String promoterId){
        return baseDao.getById(tenantId, PromoterFields.API_NAME, promoterId);
    }
    public List<IObjectData> getPromoterByIds(String tenantId, List<String> promoterIds){
        return baseDao.getByIds(tenantId, PromoterFields.API_NAME, promoterIds);
    }
    public List<IObjectData> findByIdsIncludeInvalid(String tenantId, List<String> promoterIds){
        return baseDao.findByIdsIncludeInvalid(tenantId, PromoterFields.API_NAME, promoterIds);
    }
    public List<IObjectData> getPromoterByIdsIncludeInvalid(String tenantId, List<String> promoterIds){
        return baseDao.findByIdsIncludeInvalid(tenantId, PromoterFields.API_NAME, promoterIds);
    }
    public void updatePromoter(String tenantId,String userId, IObjectData iObjectData){
        baseDao.update(tenantId, userId,iObjectData);
    }

    public void recoverPublicEmployee(String tenantId, String userId, List<String> ids) {
        if (CollectionUtils.isEmpty(ids) || StringUtils.isBlank(tenantId)){
//            new ValidateException("启用互联用户参数异常");
        }else{
            publicEmployeeDao.bulkRecover("PublicEmployeeObj",ids, User.builder().tenantId(tenantId).userId(userId).build());
        }
    }
    public void delPublicEmployee(String tenantId, String userId, List<String> ids) {
        if (CollectionUtils.isEmpty(ids) || StringUtils.isBlank(tenantId)){
//            new ValidateException("启用互联用户参数异常");
        }else{
            publicEmployeeDao.delData("PublicEmployeeObj",ids, User.builder().tenantId(tenantId).userId(userId).build());
        }
    }
    /**
     * 更新 互联用户手机号
     */
    public void updatePublicEmployeeMobile(String tenantId, String userId, String outerUserId, String mobile,String name) {
        //手机号可以置空
        if (StringUtils.isBlank(outerUserId) || StringUtils.isBlank(tenantId)) {
            throw new ValidateException("更新互联用户手机号参数异常"); //ignoreI18n
        }
        IObjectData publicEmployee = publicEmployeeDao.getByOutUserId(tenantId, outerUserId);
        publicEmployee.set("mobile", mobile);
        publicEmployee.set("name", name);
        IObjectData update = publicEmployeeDao.update(tenantId, userId, publicEmployee);

        //返回值已经变回来了的话
        boolean updated = StringUtils.isBlank(mobile) ? StringUtils.isBlank(update.get("mobile", String.class)) : update.get("mobile").equals(mobile);
        if (!updated) {
            throw new ValidateException("更新互联用户手机号失败"); //ignoreI18n
        }
    }
    public void changeRolesWithPromoter(String tenantId, IObjectData promoterObj) {
        try {
            BaseResult baseResult = checkinsProxy.changeRolesWithPromoter(tenantId, ObjectDataDocument.of(promoterObj));
            if (baseResult.getErrorCode() != 0) {
                throw new ValidateException("更新角色失败：" + baseResult.getMessage()); //ignoreI18n
            }
        } catch (Exception e) {
            throw new ValidateException("更新角色失败"); //ignoreI18n
        }
    }

    /**
     * 移除促销员给对象的相关团队
     * {"teamMemberInfos":[{"teamMemberEmployee":[],"teamMemberPermissionType":"1","teamMemberRole":"","teamMemberRoleList":["4"],"teamMemberType":0,"outTeamMemberEmployee":[{"userId":"300388147","outTenantId":"300021590"}]}],"otherObjects":[],"dataIDs":["63918992d38f1f7e92f90434"]}
     *
     * @param tenantId
     * @param outUserId
     * @param string
     * @param del
     * @param apiName
     */
    public void removePromoterFromTeam(String tenantId, String outTenantId, String outUserId, List<String> del, String apiName) {
        if (CollectionUtils.isEmpty(del) || StringUtils.isBlank(tenantId) || StringUtils.isBlank(outUserId) || StringUtils.isBlank(outTenantId)){
            return;
        }
        List<IObjectData> accountObjects = baseDao.getByIds(tenantId, apiName, del);
        TeamMember teamMember = new TeamMember(outUserId, TeamMember.Role.NORMAL_STAFF.getValue(),
                TeamMember.Permission.READANDWRITE,outTenantId);
        List<IObjectData> updatedList = Lists.newArrayList();
        for (IObjectData accountObject : accountObjects) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(accountObject);
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            boolean removed = teamMembers.removeIf(o -> o.getEmployee().contains(outUserId));
            if (removed){
                objectDataExt.setTeamMembers(teamMembers);
                updatedList.add(accountObject);
            }
        }
        if (CollectionUtils.isNotEmpty(updatedList)){
            serviceFacade.batchUpdateRelevantTeam(User.systemUser(tenantId), updatedList, true);
        }

    }

    /**
     * 增加促销员给 对象的相关团队
     * @param tenantId
     * @param outTenantId
     * @param outUserId
     * @param add
     * @param apiName
     */
    public void addPromoterToTeam(String tenantId, String outTenantId, String outUserId, List<String> add, String apiName) {

        if (CollectionUtils.isEmpty(add) || StringUtils.isBlank(tenantId) || StringUtils.isBlank(outUserId) || StringUtils.isBlank(outTenantId)){
            return;
        }
        List<IObjectData> accountObjects = baseDao.getByIds(tenantId, apiName, add);
        TeamMember teamMember = new TeamMember(outUserId, TeamMember.Role.NORMAL_STAFF.getValue(),
                TeamMember.Permission.READANDWRITE,outTenantId);
        List<IObjectData> updatedList = Lists.newArrayList();
        for (IObjectData accountObject : accountObjects) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(accountObject);
            //如果不包含 这个人的
            List<TeamMember> teamMembers = objectDataExt.getTeamMembers();
            if (!teamMembers.stream().anyMatch(o -> o.getEmployee().contains(outUserId))){
                teamMembers.add(teamMember);
                objectDataExt.setTeamMembers(teamMembers);
                updatedList.add(accountObject);
            }
        }
        if (CollectionUtils.isNotEmpty(updatedList)){
            serviceFacade.batchUpdateRelevantTeam(User.systemUser(tenantId), updatedList, true);
        }
    }
}
