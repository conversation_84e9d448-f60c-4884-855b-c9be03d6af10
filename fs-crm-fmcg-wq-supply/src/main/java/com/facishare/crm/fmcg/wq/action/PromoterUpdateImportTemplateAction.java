package com.facishare.crm.fmcg.wq.action;

import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import org.apache.commons.collections.SetUtils;

import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;


public class PromoterUpdateImportTemplateAction extends StandardUpdateImportTemplateAction {
    //PromoterFields 下所有字段 不允许修改
    final static List<String> removeFields = ConfigUtils.getFields(PromoterFields.class);

    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        Iterator<IFieldDescribe> iterator = headerFieldList.iterator();

        while(iterator.hasNext()) {
            IFieldDescribe iField = (IFieldDescribe)iterator.next();
            if (removeFields.contains(iField.getApiName())) {
                iterator.remove();
            }
        }
    }

//    @Override
//    protected List<IFieldDescribe> sortHeader(List<IFieldDescribe> validFieldList) {
//        List<IFieldDescribe> iFieldDescribes = super.sortHeader(validFieldList);
//        //如果包含start_time和end_time字段,将end_time字段放在start_time字段之后一位，其他顺序不改变
//        if (iFieldDescribes.stream().anyMatch(iFieldDescribe -> iFieldDescribe.getApiName().equals(AccountShiftDetailFields.START_TIME)) &&
//                iFieldDescribes.stream().anyMatch(iFieldDescribe -> iFieldDescribe.getApiName().equals(AccountShiftDetailFields.END_TIME))) {
//            //移除end_time字段，获取start_time字段的位置，将end_time字段放在start_time字段后面
//            IFieldDescribe end_time = iFieldDescribes.stream().filter(iFieldDescribe -> iFieldDescribe.getApiName().equals(AccountShiftDetailFields.END_TIME)).findFirst().get();
//            iFieldDescribes.remove(end_time);
//            int index = iFieldDescribes.indexOf(iFieldDescribes.stream().filter(iFieldDescribe -> iFieldDescribe.getApiName().equals(AccountShiftDetailFields.START_TIME)).findFirst().get());
//            iFieldDescribes.add(index + 1, end_time);
//        }
//
//        return iFieldDescribes;
//    }
}
