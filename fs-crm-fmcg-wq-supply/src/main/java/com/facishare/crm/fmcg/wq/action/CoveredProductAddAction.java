package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.predef.action.StandardAddAction;

/**
 *
 */
@SuppressWarnings("Duplicates")
public class CoveredProductAddAction extends StandardAddAction {

    //public static final Logger log = LoggerFactory.getLogger(TPMActivityAddAction.class);

    @Override
    protected void before(Arg arg) {
        validateName(arg);
        super.before(arg);
    }

    private void validateName(Arg arg) {

    }

}
