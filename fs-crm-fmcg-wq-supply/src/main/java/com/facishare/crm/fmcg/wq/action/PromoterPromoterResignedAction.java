package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.model.FmcgPreActionArgs;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 促销员离职
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterPromoterResignedAction extends FmcgAbstractStandardAction<PromoterPromoterResignedAction.Arg,PromoterPromoterResignedAction.Result> {
    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);


    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.PROMOTER_RESIGNED;
    }

    @Override
    protected Result doAct(Arg arg) {
        if (("0").equals(objectData.get(PromoterFields.IO_STATUS, String.class))) {
            throw new ValidateException("促销员已离职"); //ignoreI18n
        }
        if (StringUtils.isNotBlank(objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class))) {
            try {
                pmmService.stopPublicEmployee(actionContext.getTenantId(), objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class));
            } catch (Exception e) {
                throw new ValidateException("停用互联用户失败"); //ignoreI18n
            }
        }
        objectData.set(PromoterFields.IO_STATUS, "0");
        Map<String, Object> update = new HashMap<>();
        //待审核
        if (!"false".equals(objectData.get(PromoterFields.REVIEW_STATUS,String.class))){
            objectData.set(PromoterFields.REVIEW_STATUS, "false");
            update.put(PromoterFields.REVIEW_STATUS, "false");
        }
        pmmService.updatePromoter(actionContext.getTenantId(), actionContext.getUser().getUserId(), objectData);
        recodeLog();
        return new Result();
    }

    @Data
    public static class Arg extends FmcgPreActionArgs {
    }

    @Data
    public static class Result {

    }
}
