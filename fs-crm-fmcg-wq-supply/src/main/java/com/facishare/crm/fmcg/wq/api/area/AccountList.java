package com.facishare.crm.fmcg.wq.api.area;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface AccountList {
    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "accountCustomerLabelMap")
        @JsonProperty("accountCustomerLabelMap")
        @SerializedName("accountCustomerLabelMap")
        private Map<String,List<String>> accountCustomerLabelMap;

        @JSONField(name = "fieldsMap")
        @JsonProperty("fieldsMap")
        @SerializedName("fieldsMap")
        private Map<String,Map<String,Object>> fieldsMap;

        @JSONField(name = "ea")
        @JsonProperty("ea")
        @SerializedName("ea")
        private String ea;
        @JSONField(name = "eid")
        @JsonProperty("eid")
        @SerializedName("eid")
        private String eid;
        @JSONField(name = "objApiName")
        @JsonProperty("objApiName")
        @SerializedName("objApiName")
        private String objApiName;
    }

    @Data
    @ToString
    class Result implements Serializable {

    }

}
