package com.facishare.crm.fmcg.wq.action;


import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.constants.SupplyStoreObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.dao.BaseDaoInterface;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;

/**
 * @program: fs-crm-fmcg
 * @description: DistributorSupplyObj add
 * @author: zhangsm
 * @create: 2021-04-29 14:42
 **/
public class SupplyStoreAddAction extends StandardAddAction {

    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String tenantId = actionContext.getTenantId();
        String shopId = arg.getObjectData().get(SupplyStoreObjConstants.Field.thisDealerId.getApiName()).toString();
        String dealerId = arg.getObjectData().get(SupplyStoreObjConstants.Field.upDealerId.getApiName()).toString();
        boolean isSpecial = (Boolean) arg.getObjectData().get(SupplyStoreObjConstants.Field.specialSupply.getApiName());
        if (shopId.equals(dealerId) || isSpecial == true){
            throw new ValidateException("参数错误"); //ignoreI18n
        }
        //初始化审批流上下文
        supplyOperaContext.setArgs(arg);
        supplyOperaContext.setResultClazz(Result.class);
        supplyOperaContext.setInUpSupplyId(arg.getObjectData().get(SupplyStoreObjConstants.Field.upSupplyId.getApiName()).toString());
        supplyOperaContext.setInUpCustomerId(dealerId);
        supplyOperaContext.setCustermerIds(Lists.newArrayList(shopId));

        supplyService.checkAddSupplyStoreObj(tenantId,
                supplyDao.getAccountObjById(tenantId, shopId)
                , Lists.newArrayList(dealerId));
        //触发审批流程
        Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(actionContext, supplyOperaContext);
        if (booleanIObjectDataPair.first){
            throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE,SupplyOperaContext.TRIGGERAPPROCODE);
        }

    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        //更新 客户的上级
        supplyDao.updateShopUpSupplyFields(actionContext.getTenantId(),arg.getObjectData().get(SupplyStoreObjConstants.Field.thisDealerId.getApiName()).toString());
        supplyService.addAvailableObjSyncTask(actionContext.getTenantId(), result.getObjectData()
                .get(SupplyStoreObjConstants.Field.upSupplyId.getApiName()).toString());
        return after;
    }
}
