package com.facishare.crm.fmcg.wq.api.route;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.api.model.BaseResult;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface VisitRouteGetPrivilege {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "func_privilege_codes")
        @JsonProperty("func_privilege_codes")
        @SerializedName("func_privilege_codes")
        private List<String> funcPrivilegeCodes;
        @JSONField(name = "api_name")
        @JsonProperty("api_name")
        @SerializedName("api_name")
        private String apiName = "VisitRouteObj";

    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {
        @J<PERSON><PERSON>ield(name = "check_map")
        @JsonProperty("check_map")
        @SerializedName("check_map")
        private Map<String,Boolean> checkMap;
    }

}
