package com.facishare.crm.fmcg.wq.service;

import com.facishare.organization.api.model.RunStatus;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.BatchGetDepartmentDtoArg;
import com.facishare.organization.api.model.department.arg.BatchGetLowDepartmentsDtoArg;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.arg.GetOrderlyUpperDepartmentArg;
import com.facishare.organization.api.model.department.result.BatchGetLowDepartmentsDtoResult;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.model.department.result.GetOrderlyUpperDepartmentResult;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.organization.api.service.EmployeeProviderService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @zhangchq
 * create time 2020/11/12 7:19 PM
 */
@Component
@Slf4j
public class OrganizationJMLService {

    @Resource
    private EmployeeProviderService employeeProviderService;

    @Resource
    private DepartmentProviderService departmentProviderService;

    /**
     * 获取有序的上级部门
     * @param tenantId
     * @param departmentId
     * @return
     */
    public List<DepartmentDto> getDepartmentIds(int tenantId, String departmentId) {
        GetOrderlyUpperDepartmentArg depArg = new GetOrderlyUpperDepartmentArg();
        depArg.setDepartmentId(Integer.valueOf(departmentId));
        depArg.setEnterpriseId(tenantId);
        depArg.setSelf(false);

        log.info("find department arg : {}", depArg);

        GetOrderlyUpperDepartmentResult depResult = departmentProviderService.getOrderlyUpperDepartment(depArg);

        log.info("find department result : {}", depResult);

        return depResult.getDepartments();
    }

    /**
     * 获取人员的主属部门
     * @param tenantId
     * @param employeeId
     * @return
     */
    public Integer getDepartmentId(int tenantId, int employeeId) {
        GetEmployeeDtoArg empArg = new GetEmployeeDtoArg();
        empArg.setEnterpriseId(tenantId);
        empArg.setEmployeeId(employeeId);

        log.info("find employee arg : {}", empArg);

        GetEmployeeDtoResult empResult = employeeProviderService.getEmployeeDto(empArg);

        log.info("find employee arg : {}", empResult);

        if (empResult.getEmployeeDto().getMainDepartmentId() == null) {
            return -1;
        }
        return empResult.getEmployeeDto().getMainDepartmentId();
    }
    public List<Integer> getDepartmentIds(int tenantId, int employeeId) {
        GetEmployeeDtoArg empArg = new GetEmployeeDtoArg();
        empArg.setEnterpriseId(tenantId);
        empArg.setEmployeeId(employeeId);

        log.info("find employee arg : {}", empArg);

        GetEmployeeDtoResult empResult = employeeProviderService.getEmployeeDto(empArg);

        log.info("find employee arg : {}", empResult);

        if (empResult.getEmployeeDto().getMainDepartmentId() == null) {
            return Lists.newArrayList();
        }

        List<Integer> departmentIds = Lists.newArrayList(empResult.getEmployeeDto().getMainDepartmentId());

        GetDepartmentDtoArg depArg = new GetDepartmentDtoArg();
        depArg.setDepartmentId(empResult.getEmployeeDto().getMainDepartmentId());
        depArg.setEnterpriseId(tenantId);

        log.info("find department arg : {}", depArg);

        GetDepartmentDtoResult depResult = departmentProviderService.getDepartmentDto(depArg);
        departmentIds.addAll(depResult.getDepartment().getAncestors());

        return departmentIds;
    }

    public boolean employeeInRange(int tenantId, int employeeId, List<Integer> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return false;
        }

        GetEmployeeDtoArg empArg = new GetEmployeeDtoArg();
        empArg.setEnterpriseId(tenantId);
        empArg.setEmployeeId(employeeId);

        log.info("find employee arg : {}", empArg);

        GetEmployeeDtoResult empResult = employeeProviderService.getEmployeeDto(empArg);

        log.info("find employee arg : {}", empResult);

        if (empResult.getEmployeeDto().getMainDepartmentId() == null) {
            return false;
        }

        if (departmentIds.contains(empResult.getEmployeeDto().getMainDepartmentId())) {
            return true;
        }

        GetDepartmentDtoArg depArg = new GetDepartmentDtoArg();
        depArg.setDepartmentId(empResult.getEmployeeDto().getMainDepartmentId());
        depArg.setEnterpriseId(tenantId);

        log.info("find department arg : {}", depArg);

        GetDepartmentDtoResult depResult = departmentProviderService.getDepartmentDto(depArg);

        log.info("find department result : {}", depResult);

        for (Integer departmentId : departmentIds) {
            if (depResult.getDepartment().getAncestors().contains(departmentId)) {
                return true;
            }
        }
        return false;
    }


    public List<DepartmentDto> queryDepartments(int tenantId, List<Integer> departmentIds) {
        BatchGetDepartmentDtoArg arg = new BatchGetDepartmentDtoArg();
        arg.setDepartmentIds(departmentIds);
        arg.setRunStatus(RunStatus.ALL);
        arg.setEnterpriseId(tenantId);
        return departmentProviderService.batchGetDepartmentDto(arg).getDepartments();
    }
    public List<DepartmentDto> batchQueryLowDepartment(int tenantId, List<Integer> departmentIds) {
        BatchGetLowDepartmentsDtoArg arg = new BatchGetLowDepartmentsDtoArg();
        arg.setDepartmentIds(departmentIds);
        arg.setSelf(false);
        arg.setRunStatus(RunStatus.ACTIVE);
        arg.setEnterpriseId(tenantId);

        BatchGetLowDepartmentsDtoResult result = departmentProviderService.batchGetLowDepartment(arg);
        return result.getDepartmentDtos();
    }
}
