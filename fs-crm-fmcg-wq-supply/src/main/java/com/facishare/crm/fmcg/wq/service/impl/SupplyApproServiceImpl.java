package com.facishare.crm.fmcg.wq.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.common.SupplyOperateEnum;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SupplyChangeCustomerDetailFields;
import com.facishare.crm.fmcg.wq.constants.SupplyChangeFields;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.common.Pair;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-11-13 17:59
 **/
@Service
@Slf4j
public class SupplyApproServiceImpl implements SupplyApproService {
    @Autowired
    ServiceFacade serviceFacade;
    @Autowired
    SupplyDao supplyDao;

    @Resource(name = "fmcgThreadPoolExecutor")
    AsyncTaskExecutor fmcgThreadPoolExecutor;

    //是否生成审批对象
    private static final String SKIP_APPRO = "skipAppro";
    private static final String SUPPLY_CHANGE_ID = "supplyChangeId";

    @Override
    public Pair<Boolean,IObjectData> triggerSupplyAppro(ActionContext actionContext,SupplyOperaContext supplyOperaContext) {
        boolean skipAppro = Boolean.TRUE.equals(actionContext.getRequestContext().getAttribute(SKIP_APPRO));
        if (!skipAppro){
            SupplyOperateEnum supplyOperate = SupplyOperateEnum.getOperateCode(actionContext.getObjectApiName(), ObjectAction.of(actionContext.getActionCode()), null, supplyOperaContext.getArgs());
            //判断是否有审批中的数据
//            checkApproLock(actionContext.getTenantId(),supplyOperate, supplyOperaContext.getOutUpCustomerId(), inUpCustomerId, productIds, customerIds);
            checkApproLock(actionContext.getTenantId(),supplyOperate, supplyOperaContext.getOutUpCustomerId(), supplyOperaContext.getInUpCustomerId(), supplyOperaContext.getProductIds(), supplyOperaContext.getCustermerIds());


//            IObjectData objectData = supplyDao.createSupplyApproObj(actionContext.getUser(), supplyOperate, outUpCustomerId, inUpCustomerId, productIds, customerIds,arg, resultClass).get(SupplyChangeFields.API_NAME).get(0).toObjectData();
           IObjectData objectData = supplyDao.createSupplyApproObj(actionContext.getUser(), supplyOperate, supplyOperaContext).get(SupplyChangeFields.API_NAME).get(0).toObjectData();

           //生命状态是否是审批中  under_review 审批中
            if (objectData.get(BaseField.lifeStatus.getApiName(),String.class).equals("under_review")){
                return Pair.of(Boolean.TRUE, objectData);
            }
            //更新成执行完成
            fmcgThreadPoolExecutor.execute(()->{
                ObjectDataExt objectDataExt = ObjectDataExt.of(objectData).remove("version");
                objectDataExt.set(SupplyChangeFields.EXECUTE_STATUS,"1");
                objectDataExt.setLastModifiedTime(System.currentTimeMillis());
                supplyDao.update(User.systemUser(actionContext.getTenantId()),objectDataExt.getObjectData());
            });
            return Pair.of(Boolean.FALSE , objectData);
        }
        return Pair.of(Boolean.FALSE , null);
    }

    @Override
    public Pair<Boolean,IObjectData> triggerSupplyAppro(ControllerContext controllerContext, SupplyOperaContext supplyOperaContext) {
        boolean skipAppro = Boolean.TRUE.equals(controllerContext.getRequestContext().getAttribute(SKIP_APPRO));
        if (!skipAppro){
            SupplyOperateEnum supplyOperate = SupplyOperateEnum.getOperateCode(controllerContext.getObjectApiName(), null, controllerContext.getMethodName(), supplyOperaContext.getArgs());
            //判断是否有审批中的数据
//            checkApproLock(controllerContext.getTenantId(),supplyOperate, outUpCustomerId, inUpCustomerId, productIds, customerIds);
            checkApproLock(controllerContext.getTenantId(),supplyOperate, supplyOperaContext.getOutUpCustomerId(), supplyOperaContext.getInUpCustomerId(), supplyOperaContext.getProductIds(), supplyOperaContext.getCustermerIds());

//            IObjectData objectData = supplyDao.createSupplyApproObj(controllerContext.getUser(), supplyOperate, outUpCustomerId, inUpCustomerId, productIds, customerIds,arg, resultClass).get(SupplyChangeFields.API_NAME).get(0).toObjectData();
            IObjectData objectData = supplyDao.createSupplyApproObj(controllerContext.getUser(), supplyOperate, supplyOperaContext).get(SupplyChangeFields.API_NAME).get(0).toObjectData();
            //生命状态是否是审批中  under_review 审批中
            if (objectData.get(BaseField.lifeStatus.getApiName(),String.class).equals("under_review")){
                return Pair.of(Boolean.TRUE, objectData);
            }
            //更新成执行完成
            fmcgThreadPoolExecutor.execute(()->{
                ObjectDataExt objectDataExt = ObjectDataExt.of(objectData).remove("version");
                objectDataExt.set(SupplyChangeFields.EXECUTE_STATUS,"1");
                objectDataExt.setLastModifiedTime(System.currentTimeMillis());
                supplyDao.update(User.systemUser(controllerContext.getTenantId()),objectDataExt.getObjectData());
            });
            return Pair.of(Boolean.FALSE , objectData);
        }
        return Pair.of(Boolean.FALSE , null);
    }


    @Override
    public  void redoAct(User user, IObjectData objectData) {
        //通过操作code转换成操作枚举
        SupplyOperateEnum supplyOperate = SupplyOperateEnum.of(objectData.get(SupplyChangeFields.OPERATE, String.class));
        if (supplyOperate == null) {
            throw new ValidateException("操作类型不存在"); //ignoreI18n
        }
        //获取操作的方法
        ObjectAction objectAction = supplyOperate.getObjectAction();
        //参数
        Object arg = JSON.parseObject(objectData.get(SupplyChangeFields.ARGS, String.class), supplyOperate.getArgsClass());
        Class resultClass = supplyOperate.getResultClass();
        RequestContext context = RequestContextManager.getContext();
        context.setAttribute(SKIP_APPRO,Boolean.TRUE);
        context.setAttribute(SUPPLY_CHANGE_ID,objectData.getId());
        try {
            if (objectAction != null) {
                //action
                ActionContext actionContext = new ActionContext(context, supplyOperate.getBindApiName(), objectAction.getInterfaceCode());
                serviceFacade.triggerAction(actionContext, arg, resultClass);
                objectData.set(SupplyChangeFields.EXECUTE_STATUS,"1");
            } else if (StringUtils.isNotBlank(supplyOperate.getMethodName())) {
                //controller
                ControllerContext controllerContext = new ControllerContext(context, supplyOperate.getBindApiName(), supplyOperate.getMethodName());
                serviceFacade.triggerController(controllerContext, arg, resultClass);
                objectData.set(SupplyChangeFields.EXECUTE_STATUS,"1");
            } else {
                throw new ValidateException("操作方法不存在"); //ignoreI18n
            }
        }catch (Exception e){
            objectData.set(SupplyChangeFields.EXECUTE_STATUS,"2");
            objectData.set(SupplyChangeFields.FAIL_MESSAGE,e.getMessage());
            throw e;
        }finally {
            //更新
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            objectDataExt.setLastModifiedTime(System.currentTimeMillis());
            objectDataExt.remove("version");
            supplyDao.update(User.systemUser(user.getTenantId()),objectDataExt.getObjectData());
        }
    }

    @Override
    public IObjectData getSupplyChangeByContext(String tenantId, RequestContext requestContext) {
        String supplyChangeId = (String) requestContext.getAttribute(SUPPLY_CHANGE_ID);
        if (StringUtils.isNotBlank(supplyChangeId)){
            return supplyDao.getById(tenantId,SupplyChangeFields.API_NAME,supplyChangeId);
        }
        return null;
    }


    /**
     * 检查是否已经有审批中的数据了
     * @param supplyOperate
     * @param outUpCustomerId
     * @param inUpCustomerId
     * @param productIds
     * @param customerIds
     */
    private Map<String,List<IObjectData>>  checkApproLock(String tenantId, SupplyOperateEnum supplyOperate, String outUpCustomerId, String inUpCustomerId, List<String> productIds, List<String> customerIds) {
        Map<String, List<IObjectData>> lockMap = Maps.newHashMap();
        if (supplyOperate.getLockCodes().contains(SupplyOperateEnum.LOCK.IN_UP_CUSTOMER)) {
            //inUpCustomerId 不能有审批中的供货关系变更
            SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                    .eq(BaseField.lifeStatus.getApiName(), "under_review")
                    .eq(SupplyChangeFields.IN_UP_CUSTOMER, inUpCustomerId);
            SearchQuery.SearchQueryBuilder searchQueryBuilder1 = SearchQuery.builder()
                    .eq(BaseField.lifeStatus.getApiName(), "under_review")
                    .eq(SupplyChangeFields.OUT_UP_CUSTOMER, inUpCustomerId);
            searchQueryBuilder.addOrWheres(searchQueryBuilder1);
            List<IObjectData> data = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQueryBuilder.build(), SupplyChangeFields.API_NAME, Lists.newArrayList("_id", "name"));
            lockMap.put(SupplyChangeFields.API_NAME, data);
        } else if (supplyOperate.getLockCodes().contains(SupplyOperateEnum.LOCK.OUT_UP_CUSTOMER)) {
            //outUpCustomerId 不能有审批中的供货关系变更
            SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                    .eq(BaseField.lifeStatus.getApiName(), "under_review")
                    .eq(SupplyChangeFields.IN_UP_CUSTOMER, outUpCustomerId);
            SearchQuery.SearchQueryBuilder searchQueryBuilder1 = SearchQuery.builder()
                    .eq(BaseField.lifeStatus.getApiName(), "under_review")
                    .eq(SupplyChangeFields.OUT_UP_CUSTOMER, outUpCustomerId);
            searchQueryBuilder.addOrWheres(searchQueryBuilder1);
            List<IObjectData> data = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQueryBuilder.build(), SupplyChangeFields.API_NAME, Lists.newArrayList("_id", "name"));
            lockMap.put(SupplyChangeFields.API_NAME, data);
        } else if (supplyOperate.getLockCodes().contains(SupplyOperateEnum.LOCK.SUB_CUSTOMER)) {
            //涉及到的customerId 不能有锁定的供货关系变更
            Set<String> allCheckCustomerIds = Sets.newConcurrentHashSet();
            if (StringUtils.isNotBlank(outUpCustomerId)) {
                allCheckCustomerIds.add(outUpCustomerId);
            }
            if (StringUtils.isNotBlank(inUpCustomerId)) {
                allCheckCustomerIds.add(inUpCustomerId);
            }
            if (CollectionUtils.notEmpty(customerIds)) {
                allCheckCustomerIds.addAll(customerIds);
            }
            if (CollectionUtils.notEmpty(allCheckCustomerIds)) {
                SupplyOperateEnum.getLockMap().entrySet().stream().forEach(entry -> {
                    switch (entry.getKey()) {
                        case SUB_CUSTOMER: {
                            SearchQuery searchQuery = SearchQuery.builder()
                                    .eq(BaseField.lifeStatus.getApiName(), "under_review")
                                    .in(SupplyChangeCustomerDetailFields.CUSTOMER, allCheckCustomerIds)
                                    .build();
                            List<IObjectData> data = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SupplyChangeCustomerDetailFields.API_NAME, Lists.newArrayList("_id", "name", SupplyChangeCustomerDetailFields.SUPPLY_CHANGE));
                            //变更的名字__r
                            if (CollectionUtils.notEmpty(data)) {
                                Set<String> supplyChangeIds = Sets.newConcurrentHashSet();
                                data.stream().forEach(o -> {
                                    if (StringUtils.isNotBlank(o.get(SupplyChangeCustomerDetailFields.SUPPLY_CHANGE).toString())) {
                                        supplyChangeIds.add(o.get(SupplyChangeCustomerDetailFields.SUPPLY_CHANGE).toString());
                                    }
                                });
                                //查询名称
                                SearchQuery searchQuery1 = SearchQuery.builder()
                                        .in("_id", supplyChangeIds)
                                        .build();
                                List<IObjectData> supplyChangeData = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery1, SupplyChangeFields.API_NAME, Lists.newArrayList("_id", "name"));
                                Map<String, String> supplyChangeNameMap = Maps.newHashMap();
                                supplyChangeData.stream().forEach(o -> {
                                    supplyChangeNameMap.put(o.get("_id").toString(), o.get("name").toString());
                                });
                                data.stream().forEach(o -> {
                                    o.set(SupplyChangeCustomerDetailFields.SUPPLY_CHANGE + "__r", supplyChangeNameMap.get(o.get(SupplyChangeCustomerDetailFields.SUPPLY_CHANGE).toString()));
                                });
                            }
                            lockMap.put(SupplyChangeCustomerDetailFields.API_NAME, data);
                        }
                        break;
                        case IN_UP_CUSTOMER: {
                            SearchQuery searchQuery = SearchQuery.builder()
                                    .eq(BaseField.lifeStatus.getApiName(), "under_review")
                                    .in(SupplyChangeFields.IN_UP_CUSTOMER, allCheckCustomerIds)
                                    .in(SupplyChangeFields.OPERATE, entry.getValue().stream().map(o -> String.valueOf(o.getOperateCode())).collect(Collectors.toList()))
                                    .build();
                            List<IObjectData> data = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SupplyChangeFields.API_NAME, Lists.newArrayList("_id", "name"));
                            lockMap.put(SupplyChangeFields.API_NAME, data);

                        }
                        break;
                        case OUT_UP_CUSTOMER: {
                            SearchQuery searchQuery = SearchQuery.builder()
                                    .eq(BaseField.lifeStatus.getApiName(), "under_review")
                                    .in(SupplyChangeFields.OUT_UP_CUSTOMER, allCheckCustomerIds)
                                    .in(SupplyChangeFields.OPERATE, entry.getValue().stream().map(o -> String.valueOf(o.getOperateCode())).collect(Collectors.toList()))
                                    .build();
                            List<IObjectData> data = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, SupplyChangeFields.API_NAME, Lists.newArrayList("_id", "name"));
                            if (lockMap.containsKey(SupplyChangeFields.API_NAME)) {
                                lockMap.get(SupplyChangeFields.API_NAME).addAll(data);
                            } else {
                                lockMap.put(SupplyChangeFields.API_NAME, data);
                            }
                        }
                        break;
                    }
                });
            }
        }


        if (CollectionUtils.notEmpty(lockMap)) {
            //聚合 供货关系名称的id 和名字
            Map<String, String> supplyChangeNameMap = Maps.newHashMap();
            lockMap.entrySet().stream().forEach(entry -> {
                if (CollectionUtils.notEmpty(entry.getValue())) {
                    if (entry.getKey().equals(SupplyChangeCustomerDetailFields.API_NAME)) {
                        entry.getValue().stream().forEach(o -> {
                            if (StringUtils.isNotBlank(o.get(SupplyChangeCustomerDetailFields.SUPPLY_CHANGE).toString())) {
                                supplyChangeNameMap.put(o.get(SupplyChangeCustomerDetailFields.SUPPLY_CHANGE).toString(), o.get(SupplyChangeCustomerDetailFields.SUPPLY_CHANGE + "__r").toString());
                            }
                        });
                    } else {
                        entry.getValue().stream().forEach(o -> {
                            supplyChangeNameMap.put(o.get("_id").toString(), o.get("name").toString());
                        });
                    }
                }
            });
            if (!supplyChangeNameMap.isEmpty()) {
                //构建错误消息 错误消息格式： 当前操作xx，已有审批中的 /n 供货关系变更: 供货关系变更名称1、2
                throw new ValidateException(String.format("当前操作%s，已有审批中的%s: %s", supplyOperate.getOperateLabel(), SupplyChangeFields.DISPLAY_NAME, supplyChangeNameMap.values().stream().collect(Collectors.joining("、")))); //ignoreI18n
            }
        }
        return lockMap;
    }

}
