package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.SneakyThrows;

import java.util.List;
import java.util.stream.Collectors;


/**
 * @program: fs-crm-fmcg
 * @description:  指定门店的配送商逻辑
 * @author: zhangsm
 * @create: 2021-05-08 15:25
 **/
public class DealerSupplyWebDetailController extends StandardWebDetailController {

    @SneakyThrows
    @Override
    protected Result after(Arg arg, Result result) {

        if (result.getData().get("supply_type__v").toString().equals(AccountObjConstants.Value.distributor__c.name())){
            LayoutDocument layoutDocument = result.getLayout();
            ILayout layout = layoutDocument.toLayout();
            List<IComponent> components = layout.getComponents();
            String delApiName = "DistributorSupplyObj_up_level_dealer_related_list";
            components.removeIf(component-> {
                if (component.getType().equals(IComponent.TYPE_TABS)){
                    JSONArray array = JSONArray.parseArray(component.get(IComponent.TYPE_TABS, String.class));
                    array.removeIf(o -> JSONObject.parseObject(JSON.toJSONString(o)).getString("api_name").startsWith(delApiName));
                    component.set(IComponent.TYPE_TABS,array);
                    List<List<String>> subComponents = JSONObject.parseObject(component.get("components",String.class), new TypeReference<List<List<String>>>() {
                    });
                    subComponents = subComponents.stream().map(o->{
                       o.removeIf(e->e.equals(delApiName));
                       return o;
                    }).filter(o->o.size() != 0).collect(Collectors.toList());
                    component.set("components",subComponents);
                }
                return component.get("api_name").toString().equals(delApiName);
            });
            layout.setComponents(components);
//            for (IComponent component : layout.getComponents()) {

//                String child_components = component.get("child_components", String.class);
//                if (StringUtils.isBlank(child_components)){
//                    continue;
//                }
//                JSONArray jsonArray = JSONArray.parseArray(child_components);
//                if (jsonArray != null){
//                    JSONArray del = new JSONArray();
//                    for (int i = 0; i < jsonArray.size(); i++) {
//                        JSONObject jsonObject = jsonArray.getJSONObject(i);
//                        if (jsonObject.get("api_name").toString().equals("DistributorSupplyObj_up_level_dealer_related_list")){
//                            del.add(jsonObject);
//                        }
//                    }
//                    if (del.size() != 0){
//                        jsonArray.removeAll(del);
//                        component.set("child_components",jsonArray);
//                    }
//                }
//            }
            layoutDocument = LayoutDocument.of(layout);
            if (null != layoutDocument)
                result.setLayout(layoutDocument);
        }

        Result after = super.after(arg, result);
        return after;
    }
}
