package com.facishare.crm.fmcg.wq.service;

import com.facishare.crm.fmcg.wq.model.Shop;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 同步可售范围数据
 * @author: zhangsm
 * @create: 2021-05-19 17:11
 **/
public interface SyncAvailableRangeService {
    /**
     * 异步 同步逻辑 时间间隔 10 分钟
     * @param tenantId
     * @param dealerSupplyId
     */
    void addAvailableObjSyncTask(String tenantId,String dealerSupplyId);
    /**
     * 总的同步逻辑
     * @param tenantId
     * @param dealerSupplyObj
     */
    void syncAvailableObjByDealerSupplyObj(String tenantId, IObjectData dealerSupplyObj);

    /**
     * 刪除可售范围相关对象
     * @param tenantId
     * @param dealerSupplyObj
     * @param subAccountId
     */
    void delAvailableObjByDealerSupplyObj(String tenantId,String dealerSupplyObj,String subAccountId);
    /**
     * 同步本级和下级同步 可售范围
     * @param tenantId
     * @param dealerSupplyObj
     * @param subAccountId
     */
    List<IObjectData> syncAvailableObjByDealerSupplyObj(String tenantId, IObjectData dealerSupplyObj,String subAccountId);
    /**
     * 同步主对象 可售范围
     * @param dealerSupplyObj 供货关系对象
     */
    IObjectData syncAvailableRangeObj(String tenantId, IObjectData dealerSupplyObj);

    /**
     * 同步可售产品 只能同步 不是特例的数据
     * @param dealerSupplyObj 供货关系对象
     */
    void syncAvailableProductObj(String tenantId,IObjectData dealerSupplyObj);

    /**
     * 同步可售门店
     * @param dealerSupplyObj 供货关系对象
     *
     */
    void syncAvailableAccountObj(String tenantId,IObjectData dealerSupplyObj,IObjectData masterObject);

    /**
     * 同步可售门店
     * @param dealerSupplyObj 供货关系对象
     *
     */
    void syncAvailableAccountObj(String tenantId,IObjectData dealerSupplyObj,IObjectData masterObject,String subAccountId);

    /**
     * 同步特例数据
     */
    List<IObjectData> syncSpecialAvailableObj(String tenantId, IObjectData dealerSupplyObj);

    /**
     * 同步价目表
     * @param dealerSupplyObj 供货关系对象
     */
    void syncAvailablePriceBookObj(String tenantId,IObjectData dealerSupplyObj,List<IObjectData> masterObjectList);


    /**
     * 全量更新包括 要删除的
     * @param tenantId
     */
    void updateAllAvailableDataByShop(String tenantId, Shop shop);


    /**
     * 修復價目表
     * @param tenantId
     */
    void fixPriceBookObj(String tenantId);












}
