package com.facishare.crm.fmcg.wq.api.area;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface StoreFilter {
    @Data
    @ToString
    class Arg implements Serializable {
        private String areaId;

        private String areaName;

        private String areaAttribute;

        private String areaAttributeName;

        private String productGroup;

    }

    @Data
    @ToString
    class Result implements Serializable {
        List<String> storeIds = Lists.newArrayList();
    }
}
