package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction;

/**
 * <AUTHOR>
 * @create 2023 - 04 - 20  11:54
 **/
public class VisitRouteChangeOwnerAction extends StandardChangeOwnerAction {

    @Override
    protected void before(Arg arg) {
        this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        super.before(arg);
    }
}
