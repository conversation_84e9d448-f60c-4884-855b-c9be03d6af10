package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.api.area.MergeArea;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class AreaManageMergeAreaController extends PreDefineController<MergeArea.Arg, MergeArea.Result> {
    CheckinsDao checkinsDao = SpringUtil.getContext().getBean(CheckinsDao.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected MergeArea.Result doService(MergeArea.Arg arg) {
        log.info("AreaManageMergeAreaController arg:{}", JSON.toJSONString(arg));

        MergeArea.Result result = new MergeArea.Result();
        IObjectDescribe detailDescribe = serviceFacade.findObject(controllerContext.getTenantId(), CoveredStoresConstants.COVERED_STORES_OBJ);
        List<String> fromAccounts = getFromAccounts(arg,detailDescribe);
        String ea = serviceFacade.getEAByEI(controllerContext.getTenantId());
        Boolean isYL = GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea);
        List<IObjectData> details = getSourceAccounts(arg,detailDescribe);
        if(CollectionUtils.isNotEmpty(details)) {
            List<String> sourAccounts = details.stream().map(o -> o.get("store", String.class)).collect(Collectors.toList());
            fromAccounts.removeAll(sourAccounts);
        }else{
            details = Lists.newArrayList();
        }
        List<String> allMasterIds= Lists.newArrayList();
        allMasterIds.add(arg.getSourceAreaId());
        allMasterIds.addAll(arg.getFromAreaId());
        List<IObjectData> masterDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(),allMasterIds, AreaManageConstants.AREA_MANAGE_OBJ);
        Map<String,IObjectData> dataMap = masterDataList.stream().collect(Collectors.toMap(k->k.getId(),v->v));

        if(CollectionUtils.isNotEmpty(fromAccounts)) {
            IObjectData masterData = dataMap.get(arg.getSourceAreaId());
            List<IObjectData> addData = Lists.newArrayList();
            for (String account : fromAccounts) {
                IObjectData detail = new ObjectData();
                detail.set("store", account);
                detail.setTenantId(controllerContext.getTenantId());
                detail.setOwner(Lists.newArrayList(controllerContext.getUser().getUserId()));
                detail.setDescribeApiName(CoveredStoresConstants.COVERED_STORES_OBJ);
                detail.setRecordType("default__c");
                detail.set(CoveredStoresConstants.BELONG_AREA,masterData.getId());
                addData.add(detail);
            }
            List<IObjectData> data = serviceFacade.bulkSaveObjectData(addData,controllerContext.getUser());
            if(CollectionUtils.isNotEmpty(data)) {
                supplyDao.updateAccountAreaInfo(controllerContext.getTenantId(), controllerContext.getUser().getUserIdInt(), masterData,fromAccounts, isYL);
            }
        }
        checkinsDao.mergeArea(ea,arg.getSourceAreaId(),arg.getFromAreaId());
        List<IObjectData> list = Lists.newArrayList();
        for (String s : arg.getFromAreaId()) {
            list.add(dataMap.get(s));
        }
        serviceFacade.bulkInvalid(list, controllerContext.getUser());


        return result;
    }

    private List<String> getFromAccounts(MergeArea.Arg arg, IObjectDescribe describe) {
        SearchTemplateQuery query = SearchQuery.builder().in(CoveredStoresConstants.BELONG_AREA, arg.getFromAreaId()).limit(2000).build().getSearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        List<IObjectData> data = ObjectUtils.queryDataSimple(serviceFacade,controllerContext.getUser(),CoveredStoresConstants.COVERED_STORES_OBJ,query,describe);
        if(CollectionUtils.isNotEmpty(data)){
            return data.stream().map(o->o.get("store",String.class)).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private List<IObjectData> getSourceAccounts(MergeArea.Arg arg,IObjectDescribe describe) {
        SearchTemplateQuery query = SearchQuery.builder().eq(CoveredStoresConstants.BELONG_AREA, arg.getSourceAreaId()).limit(2000).build().getSearchTemplateQuery();
        query.setNeedReturnCountNum(false);
        List<IObjectData> data = ObjectUtils.queryDataSimple(serviceFacade,controllerContext.getUser(),CoveredStoresConstants.COVERED_STORES_OBJ,query,describe);
        return data;
    }
}
