package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.List;

@SuppressWarnings("Duplicates")
public class AreaManageChangeOwnerAction extends StandardChangeOwnerAction {

    public static final Logger log = LoggerFactory.getLogger(AreaManageChangeOwnerAction.class);

    @Override
    protected void before(Arg arg) {

        log.info("AreaManageChangeOwner !!");
        if(GrayRelease.isAllow("checkin-server-v2","areaManage",actionContext.getEa())) {
            validateData(arg);
        }else if (GrayRelease.isAllow("checkin-server-v2", "isYLArea",actionContext.getEa())) {
            validateDataYiLu(arg);
        }
        super.before(arg);
    }

    private void validateDataYiLu(Arg arg) {
        List<ChangeOwnerData> data  = arg.getData();

        String owner = data.get(0).getOwnerId().get(0);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);

        Filter ownerFilter = new Filter();
        ownerFilter.setFieldName(CommonConstants.OWNER);
        ownerFilter.setOperator(Operator.EQ);
        ownerFilter.setFieldValues(Lists.newArrayList(owner));
        query.setFilters(Lists.newArrayList(ownerFilter));

        List<IObjectData> getData = serviceFacade.findBySearchQuery(actionContext.getUser(), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();

        log.info("AreaManageChangeOwner:Uniqueness check  getData : {}, query : {}", getData, query);

        if (!CollectionUtils.isEmpty(getData)) {
            throw new ValidateException("该人员已经规划了片区"); //ignoreI18n
        }
    }

    /**
     *产品组+片区属性+负责人，不可重复；举例：当一个片区产品组为“A组”，片区属性为：区域承包，则不能在创建同一个相同内容的片区
     *
     * @param arg
     */
    private void validateData(Arg arg){
        List<ChangeOwnerData> data  = arg.getData();
        String objectDataId = data.get(0).getObjectDataId();

        IObjectData areaManageData = serviceFacade.findObjectData(actionContext.getUser(), objectDataId, AreaManageConstants.AREA_MANAGE_OBJ);
        String productGroup = (String) areaManageData.get(AreaManageConstants.PRODUCT_GROUP);
        String areaAttribute = (String) areaManageData.get(AreaManageConstants.AREA_ATTRIBUTE);
        String owner = data.get(0).getOwnerId().get(0);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setOffset(0);
        Filter productGroupFilter = new Filter();
        productGroupFilter.setFieldName(AreaManageConstants.PRODUCT_GROUP);
        productGroupFilter.setOperator(Operator.EQ);
        productGroupFilter.setFieldValues(Lists.newArrayList(productGroup));

        Filter ownerFilter = new Filter();
        ownerFilter.setFieldName(CommonConstants.OWNER);
        ownerFilter.setOperator(Operator.EQ);
        ownerFilter.setFieldValues(Lists.newArrayList(owner));

        Filter areaAttributeFilter = new Filter();
        areaAttributeFilter.setFieldName(AreaManageConstants.AREA_ATTRIBUTE);
        areaAttributeFilter.setOperator(Operator.EQ);
        areaAttributeFilter.setFieldValues(Lists.newArrayList(areaAttribute));
        query.setFilters(Lists.newArrayList(productGroupFilter,ownerFilter,areaAttributeFilter));

        List<IObjectData> getData = serviceFacade.findBySearchQuery(actionContext.getUser(), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();

        log.info("AreaManageChangeOwner:Uniqueness check  getData : {}, query : {}", getData, query);

        if (!CollectionUtils.isEmpty(getData)) {
            throw new ValidateException("该人员已经相同产品组和片区属性当区域"); //ignoreI18n
        }
    }
}
