package com.facishare.crm.fmcg.wq.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.DistributorSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.BaseDaoInterface;
import com.facishare.crm.fmcg.wq.service.ChannelService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
@Service
@Slf4j
public class ChannelServiceImpl implements ChannelService {

    @Autowired
    BaseDaoInterface baseDao;

    @Override
    public void checkIsValid(String tenantId, List<String> dataIds) {
        SearchQuery disSearchQuery = SearchQuery.builder()
                .in("_id", dataIds)
                .build();
        QueryResult<IObjectData> queryDataListByQuery = baseDao.getQueryDataListByQuery(User.systemUser(tenantId), disSearchQuery, "ChannelObj", false);
        List<String> usingDataNameList = queryDataListByQuery.getData().stream().filter(o->Boolean.valueOf(o.get("channel_state").toString())).map(o->o.get("name").toString()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(usingDataNameList)) {
            String usingNameStr = "";
            for (String s : usingDataNameList) {
                usingNameStr += (s + "，");
            }
            usingNameStr = usingNameStr.substring(0,usingNameStr.length()-1);
            throw new ValidateException(String.format("【%s】启用状态下的渠道不允许作废",usingNameStr, DistributorSupplyObjConstants.DISPLAY_NAME)); //ignoreI18n
        }
    }

    @Override
    public List<IFilter> buildFilter(List<IFilter> filters) {
        if(Objects.isNull(filters)){
            filters = Lists.newArrayList();
        }
        Filter filter = new Filter();
        filter.setFieldName("channel_state");
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList("true"));
        filters.add(filter);
        log.info("buildFilter findBySearchQuery query {}", JSON.toJSONString(filters));
        return filters;
    }

    @Override
    public Integer calculateLevel(String tenantId,BaseObjectSaveAction.Arg arg,boolean isAdd) {
        String superiorId = "";
        if(Objects.isNull(arg.getObjectData().get("superior_channel"))){
            if(Objects.nonNull(arg.getObjectData().get("_id")) && !isAdd){
                String dataId = arg.getObjectData().get("_id").toString();
                SearchQuery disSearchQuery = SearchQuery.builder()
                        .in("_id", Lists.newArrayList(dataId))
                        .build();
                List<IObjectData> resultList = baseDao.getQueryDataListByQuery(User.systemUser(tenantId), disSearchQuery, "ChannelObj", false).getData();
                if(CollectionUtils.isNotEmpty(resultList)){
                    if(Objects.nonNull(resultList.get(0).get("superior_channel"))){
                        superiorId = resultList.get(0).get("superior_channel").toString();
                    }
                }
            }
        }else{
            superiorId = arg.getObjectData().get("superior_channel").toString();
        }
        if(StringUtils.isBlank(superiorId)){
            return 0;
        }
        SearchQuery disSearchQuery = SearchQuery.builder()
                .in("_id", Lists.newArrayList(superiorId))
                .build();
        List<IObjectData> resultList = baseDao.getQueryDataListByQuery(User.systemUser(tenantId), disSearchQuery, "ChannelObj", false).getData();
        if(CollectionUtils.isEmpty(resultList)){
            return 0;
        }
        if(Objects.nonNull(resultList.get(0).get("channel_level"))){
            return Integer.valueOf(resultList.get(0).get("channel_level").toString()) + 1;
        }
        return 0;
    }

    @Override
    public void checkIsRoot(String tenantId, BaseObjectSaveAction.Arg arg) {
        if(Objects.nonNull(arg.getObjectData().get("channel_level")) && "0".equals(arg.getObjectData().get("channel_level").toString()) && !Boolean.valueOf(arg.getObjectData().get("channel_state").toString())){
            throw new ValidateException("根目录不允许禁用"); //ignoreI18n
        }
    }

}
