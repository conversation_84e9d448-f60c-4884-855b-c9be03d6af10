package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.api.area.AreaInfoByOwner;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.service.AreaService;
import com.facishare.crm.fmcg.wq.util.FieldUtils;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;

import java.util.List;
import java.util.Map;

public class AreaManageAreaInfoByOwnerController extends PreDefineController<AreaInfoByOwner.Arg, AreaInfoByOwner.Result> {

    AreaService areaService = SpringUtil.getContext().getBean(AreaService.class);


    private List<String> fixFields = Lists.newArrayList("name","sales_department","business_group","service_center");
    private List<String> QUERY_FIELDS = Lists.newArrayList("area_owner","post_1","post_2","post_3","post_4");

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected AreaInfoByOwner.Result doService(AreaInfoByOwner.Arg arg) {
        if(CollectionUtils.isEmpty(arg.getFilterField())){
            throw new ValidateException("参数有误,请检查!"); //ignoreI18n
        }
        for (String s : arg.getFilterField()) {
            if(!QUERY_FIELDS.contains(s)){
                throw new ValidateException("参数有误,请检查!"); //ignoreI18n
            }
        }
        AreaInfoByOwner.Result res = new AreaInfoByOwner.Result();

        SearchTemplateQuery query = getQuery(arg);
        IObjectDescribe areaDesc = serviceFacade.findObject(controllerContext.getTenantId(), AreaManageConstants.AREA_MANAGE_OBJ);
        List<String> finalFields = Lists.newArrayList();
        finalFields.addAll(fixFields);
        finalFields.addAll(arg.getFilterField());
        List<IObjectData>  data = ObjectUtils.queryDataWithFieldInfo(serviceFacade,controllerContext.getUser(),controllerContext.getRequestContext(),areaDesc,query,finalFields);
        if(CollectionUtils.isNotEmpty(data)){
            serviceFacade.fillDepartmentInfo(areaDesc,data,controllerContext.getUser());
            res.setAreaInfos(convertAreaInfoData(data,areaDesc,arg));
        }
        return res;
    }

    private SearchTemplateQuery getQuery(AreaInfoByOwner.Arg arg) {
        SearchQuery.SearchQueryBuilder builder = SearchQuery.builder();

        for (String field : arg.getFilterField()) {
            Wheres where = new Wheres();

            Filter areaFilter = new Filter();
            areaFilter.setFieldName(field);
            areaFilter.setOperator(Operator.IN);
            areaFilter.setFieldValues(Lists.newArrayList(String.valueOf(arg.getOwner())));

            where.setFilters(Lists.newArrayList(areaFilter));

            builder.addWheres(where);
        }
        builder.limit(20);
        return builder.build().getSearchTemplateQuery();
    }

    public List<AreaInfoByOwner.AreaInfoVO> convertAreaInfoData(List<IObjectData> data,IObjectDescribe describe,AreaInfoByOwner.Arg arg) {
        List<AreaInfoByOwner.AreaInfoVO> res = Lists.newArrayList();
        Map<String,IFieldDescribe> fieldMap = describe.getFieldDescribeMap();
        for (IObjectData datum : data) {
            AreaInfoByOwner.AreaInfoVO vo = new AreaInfoByOwner.AreaInfoVO();
            for (String field : arg.getFilterField()) {
                List<String> fData = FieldUtils.getStringList(datum,field);
                if(fData.contains(String.valueOf(arg.getOwner()))){
                    vo.setPostField(fieldMap.get(field).getLabel());
                    vo.setPostApiName(field);
                    break;
                }
            }
            vo.setAreaId(datum.getId());
            vo.setAreaName(datum.getName());
            vo.setSales_department(FieldUtils.getShowStr(datum,fieldMap.get("sales_department")));
            vo.setBusiness_group(FieldUtils.getShowStr(datum,fieldMap.get("business_group")));
            vo.setService_center(FieldUtils.getShowStr(datum,fieldMap.get("service_center")));
            res.add(vo);

        }
        return res;
    }
}
