package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

public class PaymentOrderGoodsFilterController extends StandardRelatedListController {

    SupplyDao supplyDao =  SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        String accountId = arg.getMasterData().get("account_id").toString();//供应商
        //查询采购订单对象
        SearchQuery searchQuery = SearchQuery.builder()
                .in("supplier_id",accountId).build();
        List<IObjectData> orderList = supplyDao.getAllIObjectDataListByQuery(User.systemUser(controllerContext.getTenantId()), searchQuery, "PurchaseOrderObj");
        List<String> orderIds= Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(orderList)){
            orderList.forEach(o->orderIds.add(o.getId()));
        }

        SearchQuery goodsSearchQuery = SearchQuery.builder()
                .in("purchase_order_id",orderIds).build();
        goodsSearchQuery.getSearchTemplateQuery().setLimit(query.getLimit());
        goodsSearchQuery.getSearchTemplateQuery().setOffset(query.getOffset());
        goodsSearchQuery.getSearchTemplateQuery().getFilters().addAll(SearchQuery.convertFilter(arg.getSearchQueryInfo()));
        QueryResult<IObjectData> goodsList = supplyDao.getQueryDataListByQuery(User.systemUser(controllerContext.getTenantId()), goodsSearchQuery, "GoodsReceivedNoteObj",false);
        return goodsList;

    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

}
