package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.supply.DealerSupplyDelBySupplyId;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @program: fs-crm-fmcg
 * @description: 删除数据通过供货关系id
 * @author: zhangsm
 * @create: 2022-04-11 16:24
 **/
public class DealerSupplyDelBySupplyIdController extends PreDefineController<DealerSupplyDelBySupplyId.Arg, DealerSupplyDelBySupplyId.Result> {
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected DealerSupplyDelBySupplyId.Result doService(DealerSupplyDelBySupplyId.Arg arg) {
        DealerSupplyDelBySupplyId.Result result = new DealerSupplyDelBySupplyId.Result();
        if (StringUtils.isBlank(arg.getSupplyId())) {
            return result;
        }
        String tenantId = controllerContext.getTenantId();
        //根据部门id查供货关系
        SearchQuery searchQuery = SearchQuery.builder()
                .eq("_id", arg.getSupplyId()).build();
        List<IObjectData> data = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DealerSupplyObjConstants.API_NAME);
        for (IObjectData dealerSupply : data) {
            Map<String, Set<String>> stringSetMap = supplyDao.delDataByDealerSupply(dealerSupply);
            result.setDelData(stringSetMap);
        }
        log.info("del data success supplyId {}", arg.getSupplyId());
        return result;
    }
}
