package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.service.ChannelService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

public class ChannelRelatedListController extends StandardRelatedListController {

    ChannelService channelService = SpringUtil.getContext().getBean(ChannelService.class);
    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        if(CollectionUtils.isEmpty(query.getFilters())){
            query.setFilters(Lists.newArrayList());
        }
        query.getFilters().addAll(channelService.buildFilter(query.getFilters()));

        return super.getQueryResult(query);
    }

}
