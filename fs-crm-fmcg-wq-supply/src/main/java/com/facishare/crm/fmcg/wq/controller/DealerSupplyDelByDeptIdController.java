package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.supply.DealerSupplyDelByDeptId;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 根据部门id删除 银鹭数据的脚本
 * @author: zhangsm
 * @create: 2021-06-10 11:38
 **/
public class DealerSupplyDelByDeptIdController extends PreDefineController<DealerSupplyDelByDeptId.Arg, DealerSupplyDelByDeptId.Result> {

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }


    @Override
    protected DealerSupplyDelByDeptId.Result doService(DealerSupplyDelByDeptId.Arg arg) {
        if (StringUtils.isBlank(arg.getDepartId())){
            return new DealerSupplyDelByDeptId.Result();
        }
        String tenantId = controllerContext.getTenantId();
        //根据部门id查供货关系
        SearchQuery searchQuery = SearchQuery.builder()
                .inDepartmentAndSub(DealerSupplyObjConstants.Field.ownDepartment.getApiName(), Lists.newArrayList(arg.getDepartId())).build();
        List<IObjectData> data = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DealerSupplyObjConstants.API_NAME);
        for (IObjectData dealerSupply : data) {
            supplyDao.delDataByDealerSupply(dealerSupply);
        }
        log.info("del data success depId {}", arg.getDepartId());
        return new DealerSupplyDelByDeptId.Result();
    }


}
