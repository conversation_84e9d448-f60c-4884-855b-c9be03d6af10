package com.facishare.crm.fmcg.wq.service;

import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.common.Pair;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 供货关系审核类
 * @author: zhangsm
 * @create: 2023-11-13 16:27
 **/
public interface SupplyApproService {


    /**
     * 触发供货关系审批
     * 1.如果已经有审批流程则报错
     * 2.如果没有审批流程则生成审批流程
     * @param user 操作人
     * @param arg 参数
     */
    Pair<Boolean,IObjectData> triggerSupplyAppro(ActionContext actionContext, SupplyOperaContext supplyOperaContext);
    Pair<Boolean,IObjectData> triggerSupplyAppro(ControllerContext controllerContext, SupplyOperaContext supplyOperaContext);

    /**
     * 审核同意执行动作
     *
     * 执行本地方法
     * @param objectData 审批对象
     * @param user       操作人
     */
    void redoAct(User user, IObjectData objectData);

    IObjectData getSupplyChangeByContext(String tenantId, RequestContext requestContext);

}
