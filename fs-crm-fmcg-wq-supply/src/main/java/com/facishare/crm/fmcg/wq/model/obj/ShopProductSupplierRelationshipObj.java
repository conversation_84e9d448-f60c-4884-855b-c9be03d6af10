package com.facishare.crm.fmcg.wq.model.obj;

import lombok.Data;

/**
 * @program: fs-appserver-checkins-v2
 * @description: 门店 供货商关系
 *  特例供货产品  特殊关系
 * @author: zhangsm
 * @create: 2021-04-21 09:20
 **/
@Data
public class ShopProductSupplierRelationshipObj {
    /**
     * 产品
     * 可能为空，为空的时候  是全局， 不为空是特例
     */
    private String productId;


    /**
     *
     */
    private String productName;
    /**
     * 门店
     */
    private String shopId;
    /**
     * 供货商 配货商，可能是一家  也可能是多家
     */
    private String supplierId;

    /**
     * 可能重复 也可能是空
     */
    private String id;
    /**
     * 是否是特例供货
     */
    private boolean isSpecial;
}
