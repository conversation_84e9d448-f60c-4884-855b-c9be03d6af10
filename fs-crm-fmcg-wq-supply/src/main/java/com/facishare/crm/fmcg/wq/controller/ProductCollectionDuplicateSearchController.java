package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardDuplicateSearchController;

/**
 * @program: fs-crm-fmcg
 * @description: 重复检查
 * @author: zhangsm
 * @create: 2021-05-17 14:46
 **/
public class ProductCollectionDuplicateSearchController extends StandardDuplicateSearchController {
//    SupplyDao supplyDao = SpringUtil.getContext().getBean(supplyDao.class);
//    @Override
//    protected GetResult.Result doService(GetResult.Arg arg) {
//        GetResult.Result result = super.doService(arg);
//        if (GrayRelease.isAllow("checkin-server-v2", "isYLProductCollection",controllerContext.getEa())) {
//            //自动创建 供货关系
//            String recordType = arg.getObjectData().get(BaseField.recordType.getApiName()).toString();
//            String tenantId = controllerContext.getTenantId();
//            List<IObjectData> duplicatProductCollectionObjs = null;
//            if (recordType
//                    .equals(ProductCollectionObjConstants.Value.recordType_dealer.getValue())
//                    || recordType
//                    .equals(ProductCollectionObjConstants.Value.recordType_distributor.getValue())){
//                //经销商的范围 创建供货关系
//                String leaderId = arg.getObjectData().get(ProductCollectionObjConstants.Field.dealerId.getApiName()).toString();
//                supplyDao.getDealerSupplyBySuppliers(tenantId,
//                        Lists.newArrayList(leaderId));
//
//                duplicatProductCollectionObjs = supplyDao.getDuplicatProductCollectionObjs(tenantId, arg.getObjectData().getId(), leaderId);
//            }else{
//                //业务处
//                duplicatProductCollectionObjs = supplyDao.getDuplicatProductCollectionObjs(tenantId, arg.getObjectData().getId(),
//                        ((List)arg.getObjectData().get(ProductCollectionObjConstants.Field.departmentId.getApiName())).get(0).toString()
//                );
//            }
//            //检验不能重复添加的逻辑
//            if (!CollectionUtils.isEmpty(duplicatProductCollectionObjs) && arg.getPageNumber() == 1){
//                if (result == null){
//                    result = new GetResult.Result();
//                }
//                if (CollectionUtils.isEmpty(result.getDataList())){
//                    result.setDataList(Lists.newArrayList());
//                }
//                result.getDataList().addAll(duplicatProductCollectionObjs.stream().map(o-> ObjectDataDocument.of(o)).collect(Collectors.toList()));
//                Iterator<ObjectDataDocument> iterator = result.getDataList().iterator();
//                Set<String> idSet = Sets.newHashSet();
//                while (iterator.hasNext()){
//                    ObjectDataDocument next = iterator.next();
//                    if (!idSet.add(next.getId())){
//                        iterator.remove();
//                    }
//                }
//                //默认让查看 不查权限了
//                if (result.getButtonInfo() == null || result.getButtonInfo().getButtons() == null){
//                    GetResult.ButtonInfo buttonInfo = new GetResult.ButtonInfo();
//                    GetResult.Button button = new GetResult.Button();
//                    button.setApiName("detail");
//                    button.setLabel("查看");
//                    List<GetResult.Button> buttons = Lists.newArrayList();
//                    buttonInfo.setButtons(buttons);
//
//                    result.setButtonInfo(buttonInfo);
//                }
//                //其他数据自己添加了 额
//                if (result.getPage() == null){
//                    result.setPage(new GetResult.Page());
//                }
//                result.getPage().setTotal(result.getDataList().size());
//            }
//        }
//        return result;
//    }
//
//    @Override
//    protected void before(GetResult.Arg arg) {
//        super.before(arg);
//    }
//
//    @Override
//    protected GetResult.Result after(GetResult.Arg arg, GetResult.Result result) {
//        return super.after(arg, result);
//    }
}
