package com.facishare.crm.fmcg.wq.controller;

import com.facishare.feeds.common.utils.I18nClientUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.fxiaoke.common.release.GrayRelease;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Map;

@Slf4j
public class StoreAuthenticationStrategyListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        if (CollectionUtils.isNotEmpty(result.getButtons())) {
            result.setButtons(Lists.newArrayList());
        }
        long eId = Long.valueOf(controllerContext.getTenantId());
        log.info("StoreAuthenticationStrategyListHeaderController 1 eId:{}",eId);
        if (!GrayRelease.isAllow("webPage", "isMengNiuAdminEi",eId)) {
            log.info("StoreAuthenticationStrategyListHeaderController getButtons().size():{}", result.getButtons().size());
            ArrayList<Map> newButtons = Lists.newArrayList();
            //暂时不权限控制 因为之前也没控制
            Map map1 = Maps.newHashMap();
            map1.put("action", "Export");
            map1.put("action_type", "default");
            map1.put("api_name", "Export_button_default");
            map1.put("label", I18nClientUtils.get("fs.wq.action.export","导出", TraceContext.get().getLocale())); //ignoreI18n
            newButtons.add(map1);
            result.getLayout().put("buttons", newButtons);
            log.info("StoreAuthenticationStrategyListHeaderController getLayout().size():{}", result.getLayout().size());
        }
        log.info("StoreAuthenticationStrategyListHeaderController 2 eId:{}",eId);
        return result;
    }
}
