package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.appserver.checkins.api.model.AreaPersonnelConfigPo;
import com.facishare.crm.fmcg.wq.api.area.ValidateData;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.crm.fmcg.wq.model.PostInfo;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.MapUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.ListUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class AreaManageValidateDataController extends PreDefineController<ValidateData.Arg, ValidateData.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    CheckinsDao checkinsDao = SpringUtil.getContext().getBean(CheckinsDao.class);

    @Override
    protected ValidateData.Result doService(ValidateData.Arg arg) {
        ValidateData.Result result = new ValidateData.Result();
        List<String> errorMessageList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(arg.getObjectDataList())){
            LinkedHashMap<String, AreaPersonnelConfigPo> personnelApiNameAndConfig = checkinsDao.getAreaPersonnelSetting(controllerContext.getTenantId()).getPersonnelApiNameAndConfig();
            Map<String,String> argUserError = Maps.newHashMap(); // key为UserId + 岗位APIName  value错误文案
            Map<String,String> dataBaseUserError = Maps.newHashMap(); // key为UserId + 岗位APIName  value错误文案
            List<String> errorAreaNameList = Lists.newArrayList();
            arg.getObjectDataList().forEach(o->{
                if(Objects.nonNull(o.getObjectDataDocument().get("adminMultiArea"))){
                    Map<String, PostInfo> personnelAndMultiAreaMap = JSONObject.parseObject(JSONObject.toJSONString(o.getObjectDataDocument().get("adminMultiArea")), new TypeReference<Map<String,PostInfo>>() {});
                    personnelAndMultiAreaMap.forEach((k,v)->{
                        if(MapUtils.isNullOrEmpty(personnelApiNameAndConfig) || Objects.isNull(personnelApiNameAndConfig.get(k)) || !personnelApiNameAndConfig.get(k).getIsStrideArea()) {
                            v.setApiName(k);
                            validateDataFromArg(o.getObjectDataDocument(), v,argUserError); // 参数校验
                            validateDataFromDataBase(o.getObjectDataDocument(), v, dataBaseUserError); // DB中校验
                        }
                    });
                }
                validateDetailData(o,errorAreaNameList);
            });
            String argError = buildErrorMessage(argUserError);
            String dataBaseError = buildErrorMessage(dataBaseUserError);
            if(!StringUtils.isBlank(argError)){
                argError+="不可分配多个片区，请检查本次提交。"; //ignoreI18n
                errorMessageList.add(argError);
                result.setErrorCode(-1);
            }
            if(!StringUtils.isBlank(dataBaseError)){
                dataBaseError+="已有片区，不可重复分配片区，请修改。"; //ignoreI18n
                errorMessageList.add(dataBaseError);
                result.setErrorCode(-1);
            }
            String errorDetail = "";
            for (String s : errorAreaNameList) {
                errorDetail += (s + "，");
            }
            if(!StringUtils.isBlank(errorDetail)){
                errorDetail+="覆盖门店中含有相同的门店，请检查！"; //ignoreI18n
                errorMessageList.add(errorDetail);
                result.setErrorCode(-1);
            }
        }
        result.setErrorMessageList(errorMessageList);
        return result;
    }

    protected void validateDataFromDataBase(ObjectDataDocument objectDataDocument, PostInfo postInfo,Map<String,String> dataBaseUserError) {
        List<String> areaOwnerArray = (List<String>) objectDataDocument.get(postInfo.getApiName());//片区负责人
        if (!CollectionUtils.isEmpty(areaOwnerArray)) {
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(1);
            query.setOffset(0);
            query.setNeedReturnCountNum(false);
            Filter ownerFilter = new Filter();
            ownerFilter.setFieldName(postInfo.getApiName());
            ownerFilter.setOperator(Operator.EQ);
            ownerFilter.setFieldValues(areaOwnerArray);
            query.setFilters(Lists.newArrayList(ownerFilter));
            List<IObjectData> getData = serviceFacade.findBySearchQuery(controllerContext.getUser(), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();

            if (!CollectionUtils.isEmpty(getData)) {
                dataBaseUserError.put(areaOwnerArray.get(0)+ "_"+ postInfo.getApiName(),"");
                validateDataFromArg(objectDataDocument,postInfo,dataBaseUserError);
            }

        }
    }

    protected void validateDataFromArg(ObjectDataDocument objectDataDocument, PostInfo postInfo,Map<String,String> argUserError) {
        List<String> userList = (List<String>) objectDataDocument.get(postInfo.getApiName());
        if (!CollectionUtils.isEmpty(userList)) {
            String key = userList.get(0) + "_"+ postInfo.getApiName();
            if(argUserError.containsKey(key)){
                if(StringUtils.isBlank(argUserError.get(key))){
                    argUserError.put(key,postInfo.getLabel() + "【" + postInfo.getUserName() + "】");
                }
            }else{
                argUserError.put(key,"");
            }
        }
    }

    public String buildErrorMessage(Map<String,String> errorMap){
        String errorMessage = "";
        for (Map.Entry<String, String> stringStringEntry : errorMap.entrySet()) {
            if(!StringUtils.isBlank(stringStringEntry.getValue())){
                errorMessage += (stringStringEntry.getValue() + "，");
            }
        }
        return errorMessage;
    }

    private void validateDetailData(ValidateData.ObjectData objectData,List<String> areaNameList) {
        //校验不能重复
        if(!org.springframework.util.CollectionUtils.isEmpty(objectData.getDetails())) {
            if (!org.springframework.util.CollectionUtils.isEmpty(objectData.getDetails().get(CoveredStoresConstants.COVERED_STORES_OBJ))) {
                List<String> coveredIds = objectData.getDetails().get(CoveredStoresConstants.COVERED_STORES_OBJ).stream().filter(o -> Objects.nonNull(o.get(CoveredStoresConstants.STORE))).map(o -> o.get(CoveredStoresConstants.STORE).toString()).collect(Collectors.toList());
                long count = coveredIds.stream().distinct().count();
                if(coveredIds.size() != count){
                    areaNameList.add("【" + objectData.getObjectDataDocument().get("name") + "】");
                }
            }
        }
    }

}
