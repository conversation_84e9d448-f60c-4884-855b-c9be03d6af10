package com.facishare.crm.fmcg.wq.api.area;


import com.facishare.appserver.checkins.api.model.BaseResult;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 分销商的上级配送商
 */
public interface AddDistributorSupplyFilter {
    @Data
    @ToString
    class Arg implements Serializable {
        /**
         * 当只传了客户id的时候，返回 经销商id
         */
        @NotEmpty(message = "参数为空")
        private String accountId;
//        /**
//         * 当传了经销商id 的时候 返回经销商下，分销商的id
//         */
//        private String supplierId;

        private int offset;

        private int limit = 200;

    }
    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {
        /**
         * 上级可选的经销商
         */
        private List<ObjectDataDocument> infos = Lists.newArrayList();
        private int total ;
        private int selectedCount;
    }
}
