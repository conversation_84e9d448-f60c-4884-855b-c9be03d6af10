package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.service.RouteService;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2023 - 04 - 12  11:26
 **/
@Slf4j
public class VisitRouteInvalidAction extends StandardInvalidAction {

    private RouteService routeService = SpringUtil.getContext().getBean(RouteService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("VisitRouteInvalidAction after is start!");
        IObjectData newObjectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getObjectDataId(), objectDescribe.getApiName());
        String tenantId = newObjectData.getTenantId();
        String routeId = newObjectData.getId();
        if (isApprovalFlowStartSuccessOrAsynchronous(routeId)){
            log.info("VisitRouteEditAction isApprovalFlowStartSuccessOrAsynchronous tenantId:{},routeId:{}",tenantId,routeId);
            ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(newObjectData).getLifeStatus();
            routeService.routeFlowStart(tenantId,routeId,objectLifeStatus.getCode());
        }else {
            log.info("VisitRouteInvalidAction no isApprovalFlowStartSuccessOrAsynchronous tenantId:{},routeId:{}",tenantId,routeId);
            routeService.invalidNoNeedTriggerApprovalFlow(tenantId,routeId);
        }
        return super.after(arg, result);
    }
}
