package com.facishare.crm.fmcg.wq.action;

import com.facishare.appserver.checkins.model.enums.LifeStatusEnum;
import com.facishare.crm.fmcg.wq.service.RouteService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2023 - 04 - 12  18:21
 **/
@Slf4j
public class VisitRouteBulkInvalidAction extends StandardBulkInvalidAction {

    private RouteService routeService = SpringUtil.getContext().getBean(RouteService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("VisitRouteBulkInvalidAction after is start!");
        Result after = super.after(arg, result);
        String tenantId = actionContext.getTenantId();
        List<ObjectDataDocument> delList = after.getObjectDataList();
        delList.parallelStream().forEach(objectDataDocument -> {
            String routeId = objectDataDocument.getId();
            if (isApprovalFlowStartSuccessOrAsynchronous(routeId)){
                log.info("VisitRouteEditAction isApprovalFlowStartSuccessOrAsynchronous tenantId:{},routeId:{}",tenantId,routeId);
                routeService.routeFlowStart(tenantId,routeId, LifeStatusEnum.INVALID.getCode());
            }else {
                log.info("VisitRouteBulkInvalidAction no isApprovalFlowStartSuccessOrAsynchronous tenantId:{},routeId:{}",tenantId,routeId);
                routeService.invalidNoNeedTriggerApprovalFlow(tenantId,routeId);
            }
        });
        return after;
    }
}
