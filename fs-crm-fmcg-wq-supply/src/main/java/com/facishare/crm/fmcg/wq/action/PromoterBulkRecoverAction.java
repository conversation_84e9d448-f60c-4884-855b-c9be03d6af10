package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-06-20 16:09
 **/
public class PromoterBulkRecoverAction extends StandardBulkRecoverAction {
    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        List<IObjectData> promoterByIdsIncludeInvalids = pmmService.getPromoterByIdsIncludeInvalid(actionContext.getTenantId(), arg.getIdList());
        for (IObjectData promoterByIdsIncludeInvalid : promoterByIdsIncludeInvalids) {
            SearchQuery.SearchQueryBuilder q1 = SearchQuery.builder()
                    .eq("mobile", promoterByIdsIncludeInvalid.get("mobile"))
                    .eq("enterprise_relation_id", promoterByIdsIncludeInvalid.get("enterprise_relation_id"))
                    .neq("_id", promoterByIdsIncludeInvalid.getId());
            SearchQuery.SearchQueryBuilder q2 = SearchQuery.builder()
                    .eq(PromoterFields.PUBLIC_EMPLOYEE_ID, promoterByIdsIncludeInvalid.get(PromoterFields.PUBLIC_EMPLOYEE_ID))
                    .neq("_id", promoterByIdsIncludeInvalid.getId());
            q1.addOrWheres(q2);
            int promoterObjTotal = supplyDao.getTotal(User.systemUser(actionContext.getTenantId()), q1.build(), "PromoterObj");
            if (promoterObjTotal != 0){
               throw new ValidateException("促销员对象手机号码或互联用户已存在,无法恢复"); //ignoreI18n
            }
            //恢复对应的互联用户
        }
        List<String> recoverIds = promoterByIdsIncludeInvalids.stream().map(o -> o.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        //life_status:"invalid"
        List<IObjectData> publicEmployeeObjs = supplyDao.getByIds(actionContext.getTenantId(),"PublicEmployeeObj", recoverIds)
                .stream().filter(o -> "invalid".equals(o.get("life_status", String.class))).collect(Collectors.toList());
        recoverIds = publicEmployeeObjs.stream().map(o -> o.getId()).collect(Collectors.toList());
        pmmService.recoverPublicEmployee(actionContext.getTenantId(),actionContext.getUser().getUserId(),recoverIds);
    }

}
