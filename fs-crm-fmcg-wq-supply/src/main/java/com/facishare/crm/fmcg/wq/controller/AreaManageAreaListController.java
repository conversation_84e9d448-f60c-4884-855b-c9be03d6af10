package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.AreaList;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 外勤路线选择片区   根据部门 查询片区信息
 */
@SuppressWarnings("Duplicates")
public class AreaManageAreaListController extends PreDefineController<AreaList.Arg, AreaList.Result> {
    @Override
    protected AreaList.Result doService(AreaList.Arg arg) {
        if (CollectionUtils.isEmpty(arg.getDepartIds())) {
            throw new ValidateException("depart can not be empty");
        }

        AreaList.Result result = new AreaList.Result();
        if(GrayRelease.isAllow("checkin-server-v2","areaManage",controllerContext.getEa())){
            jmlAreaList(arg,result);
        }else if (GrayRelease.isAllow("checkin-server-v2", "isYLArea",controllerContext.getEa())) {
            yinLuAreaList(arg,result);
        }else{
            areaList(arg,result);
        }

        return result;
    }

    private void areaList(AreaList.Arg arg, AreaList.Result result) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(500);
        query.setOffset(0);

        Filter areaFilter = new Filter();
        areaFilter.setFieldName(AreaManageConstants.SERVICE_CENTER);
        areaFilter.setOperator(Operator.IN);
        areaFilter.setFieldValues(arg.getDepartIds().stream().map(o->String.valueOf(o)).collect(Collectors.toList()));
        query.setFilters(Lists.newArrayList(areaFilter));
        if(Objects.nonNull(arg.getDealerId()) && arg.getDealerId()!=0){
            Filter dealerFilter = new Filter();
            dealerFilter.setFieldName(AreaManageConstants.BUSINESS_GROUP_FIX);
            dealerFilter.setOperator(Operator.IN);
            dealerFilter.setFieldValues(Lists.newArrayList(String.valueOf(arg.getDealerId())));
            query.getFilters().add(dealerFilter);
        }

        //过滤掉自己
        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
        if(CollectionUtils.isNotEmpty(data) &&StringUtils.isNotEmpty(arg.getAreaId()) ){
            data.removeIf(o->o.getId().equals(arg.getAreaId()));
        }
        if(CollectionUtils.isEmpty(data)){
//            throw new ValidateException("没有符合条件的片区");
            return;
        }
        List<AreaList.AreaInfoVO> areaInfos =covertAreaInfoVOList(data,null);
        result.setAreaInfos(areaInfos);
    }
    private List<AreaList.AreaInfoVO> covertAreaInfoVOList(List<IObjectData> data,String createBy) {
        List<AreaList.AreaInfoVO> areaInfos = Lists.newArrayList();
        for (IObjectData datum : data) {
            AreaList.AreaInfoVO area = new AreaList.AreaInfoVO();
            area.setId(datum.getId());
            area.setName(datum.getName());
            if(Objects.nonNull(datum.get(AreaManageConstants.AREA_OWNER_FIX))) {
                List<String> ownerArray = (List<String>) datum.get(AreaManageConstants.AREA_OWNER_FIX);
                area.setOwner(ownerArray.get(0));
            }
            if(StringUtils.isNotEmpty(createBy) && createBy.equals(area.getOwner())) {
                areaInfos.add(0,area);
            }else{
                areaInfos.add(area);
            }
        }
        return areaInfos;
    }
    private void yinLuAreaList(AreaList.Arg arg, AreaList.Result result) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(500);
        query.setOffset(0);

        Filter areaFilter = new Filter();
        areaFilter.setFieldName(AreaManageConstants.SALES_DEPARTMENT__C);
        areaFilter.setOperator(Operator.IN);
        areaFilter.setFieldValues(arg.getDepartIds().stream().map(o->String.valueOf(o)).collect(Collectors.toList()));
        query.setFilters(Lists.newArrayList(areaFilter));
        if(Objects.nonNull(arg.getDealerId()) && arg.getDealerId()!=0){
            Filter dealerFilter = new Filter();
            dealerFilter.setFieldName(AreaManageConstants.BUSINESS_GROUP);
            dealerFilter.setOperator(Operator.IN);
            dealerFilter.setFieldValues(Lists.newArrayList(String.valueOf(arg.getDealerId())));
            query.getFilters().add(dealerFilter);
        }

        //过滤掉自己
        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
        if(CollectionUtils.isNotEmpty(data) &&StringUtils.isNotEmpty(arg.getAreaId()) ){
            data.removeIf(o->o.getId().equals(arg.getAreaId()));
        }
        if(CollectionUtils.isEmpty(data)){
//            throw new ValidateException("没有符合条件的片区");
            return;
        }
        List<AreaList.AreaInfoVO> areaInfos =covertAreaInfoVO(data,null);
        result.setAreaInfos(areaInfos);

    }

    private void jmlAreaList(AreaList.Arg arg, AreaList.Result result) {
        List<IObjectData> data = queryAreaManage(controllerContext,arg);
        if(CollectionUtils.isEmpty(data)){
//            throw new ValidateException("没有符合条件的片区");
            return;
        }

        //jml优先取客户自己的片区
        String createBy = null;
        if(StringUtils.isNotEmpty(arg.getMainObjectId())){
            IObjectData accountData = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()),arg.getMainObjectId(),CommonConstants.ACCOUNT_OBJ);
            if(Objects.nonNull(accountData)){
                createBy = accountData.getCreatedBy();
            }
        }
        List<AreaList.AreaInfoVO> areaInfos =covertAreaInfoVO(data,createBy);
        result.setAreaInfos(areaInfos);
    }

    private List<AreaList.AreaInfoVO> covertAreaInfoVO(List<IObjectData> data,String createBy) {
        List<AreaList.AreaInfoVO> areaInfos = Lists.newArrayList();
        for (IObjectData datum : data) {
            AreaList.AreaInfoVO area = new AreaList.AreaInfoVO();
            area.setId(datum.getId());
            area.setName(datum.getName());
            area.setProductGroupId((String)datum.get(AreaManageConstants.PRODUCT_GROUP));
            area.setProductGroupName((String)datum.get(AreaManageConstants.PRODUCT_GROUP_NAME));
            if(Objects.nonNull(datum.get(AreaManageConstants.AREA_OWNER))) {
                List<String> ownerArray = (List<String>) datum.get(AreaManageConstants.AREA_OWNER);
                area.setOwner(ownerArray.get(0));
            }
            if(Objects.nonNull(datum.get(AreaManageConstants.DEALER))) {
                List<String> dealer = (List<String>) datum.get(AreaManageConstants.DEALER);
                area.setDealerId(dealer.get(0));
            }
            if(StringUtils.isNotEmpty(createBy) && createBy.equals(area.getOwner())) {
                areaInfos.add(0,area);
            }else{
                areaInfos.add(area);
            }
        }
        return areaInfos;
    }

    private List<IObjectData> queryAreaManage(ControllerContext context, AreaList.Arg arg) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(500);
        query.setOffset(0);

        Filter areaFilter = new Filter();
        areaFilter.setFieldName(AreaManageConstants.COUNTY__C);
        areaFilter.setOperator(Operator.IN);
        areaFilter.setFieldValues(arg.getDepartIds().stream().map(o->String.valueOf(o)).collect(Collectors.toList()));
        query.setFilters(Lists.newArrayList(areaFilter));
        if(Objects.nonNull(arg.getDealerId()) && arg.getDealerId()!=0){
            Filter dealerFilter = new Filter();
            dealerFilter.setFieldName(AreaManageConstants.DEALER);
            dealerFilter.setOperator(Operator.IN);
            dealerFilter.setFieldValues(Lists.newArrayList(String.valueOf(arg.getDealerId())));
            query.getFilters().add(dealerFilter);
        }
        //根据routeId 查询所属片区的属性和产品组，需要过滤
        if(StringUtils.isNotEmpty(arg.getAreaId())){
           IObjectData areaInfo =  serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), arg.getAreaId(), AreaManageConstants.AREA_MANAGE_OBJ);
            if(Objects.nonNull(areaInfo)){
                //产品组
                String productGroup = (String)areaInfo.get(AreaManageConstants.PRODUCT_GROUP_NAME);
//                log.info("productGroup :{}",productGroup,areaInfo.get(AreaManageConstants.PRODUCT_GROUP));
                if(Strings.isNotEmpty(productGroup)){
                    Filter productGroupFilter = new Filter();
                    productGroupFilter.setFieldName("product_group.name");
                    productGroupFilter.setOperator(Operator.EQ);
                    productGroupFilter.setFieldValues(Lists.newArrayList(productGroup));
                    query.getFilters().add(productGroupFilter);
                }
                //片区属性
                String area_attribute = (String) areaInfo.get(AreaManageConstants.AREA_ATTRIBUTE);
                if(Strings.isNotEmpty(area_attribute)){
                    Filter areaAttributeFilter = new Filter();
                    areaAttributeFilter.setFieldName(AreaManageConstants.AREA_ATTRIBUTE);
                    areaAttributeFilter.setOperator(Operator.EQ);
                    areaAttributeFilter.setFieldValues(Lists.newArrayList(area_attribute));
                    query.getFilters().add(areaAttributeFilter);
                }
            }
        }
        //过滤掉自己
        List<IObjectData> result = serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
        if(CollectionUtils.isNotEmpty(result) &&StringUtils.isNotEmpty(arg.getAreaId()) ){
            result.removeIf(o->o.getId().equals(arg.getAreaId()));
        }
        return result;
    }
    private List<IObjectData> queryAreaManageById(ControllerContext context, String areaId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(500);
        query.setOffset(0);

        Filter areaFilter = new Filter();
        areaFilter.setFieldName("_id");
        areaFilter.setOperator(Operator.EQ);
        areaFilter.setFieldValues(Lists.newArrayList(areaId));
        query.setFilters(Lists.newArrayList(areaFilter));
        return serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }
}
