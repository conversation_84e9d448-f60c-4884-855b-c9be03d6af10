package com.facishare.crm.fmcg.wq.service;

/**
 * <AUTHOR>
 * @create 2023 - 04 - 06  16:32
 **/
public interface RouteService {

    /**
     * 触发审批 更新路线的锁定状态和生命状态
     * @param tenantId
     * @param routeId
     * @param objectLifeStatus
     * @return
     */
    boolean routeFlowStart(String tenantId, String routeId,String objectLifeStatus);

    /**
     * 审批结束 更新路线的锁定状态和生命状态
     * @param tenantId
     * @param routeId
     * @param objectLifeStatus
     * @return
     */
    boolean routeFlowCompleted(String tenantId, String routeId,Boolean isPass,String objectLifeStatus);

    /**
     * 新增路线没有触发审批的处理逻辑
     * @param tenantId
     * @param routeId
     * @return
     */
    boolean addNoNeedTriggerApprovalFlow(String tenantId, String routeId);

    /**
     * 编辑路线没有触发审批的处理逻辑
     * @param tenantId
     * @param routeId
     * @return
     */
    boolean editNoNeedTriggerApprovalFlow(String tenantId, String routeId);

    /**
     * 作废路线没有触发审批的处理逻辑
     * @param tenantId
     * @param routeId
     * @return
     */
    boolean invalidNoNeedTriggerApprovalFlow(String tenantId, String routeId);


}
