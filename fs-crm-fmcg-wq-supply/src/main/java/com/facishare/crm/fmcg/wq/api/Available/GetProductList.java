package com.facishare.crm.fmcg.wq.api.Available;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.api.model.BaseResult;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface GetProductList {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "store_ids")
        @JsonProperty("store_ids")
        @SerializedName("store_ids")
        private List<String> storeIds;

    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {
        @JSONField(name = "product_ids")
        @JsonProperty("product_ids")
        @SerializedName("product_ids")
        private Map<String,List<String>> productIds;
    }

}
