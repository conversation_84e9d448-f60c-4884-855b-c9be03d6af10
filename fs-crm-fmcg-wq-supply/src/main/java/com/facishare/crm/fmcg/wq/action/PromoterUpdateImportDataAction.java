package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardUpdateImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.base.Strings;

import java.util.List;

/**
 *
 */
public class PromoterUpdateImportDataAction extends StandardUpdateImportDataAction {
    //PromoterFields 下所有字段 不允许修改
    final static List<String> removeFields = ConfigUtils.getFields(PromoterFields.class);
    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        super.customDefaultValue(validList);
        //移除 预置字段
        validList.forEach(data -> {
            ObjectDataDocument dataDocument = ObjectDataDocument.of(data);
            dataDocument.entrySet().removeIf(next -> {
               return removeFields.contains(next.getKey());
            });
        });
    }
}