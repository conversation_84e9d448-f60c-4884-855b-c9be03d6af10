package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *
 */
public class PromoterInsertImportDataAction extends StandardInsertImportDataAction {
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        validateRepeat(dataList);
    }
    private void validateRepeat(List<ImportData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<ImportError> errorList = Lists.newArrayList();
        Set<Pair<String,String>> set = new HashSet<>();
        Set<Pair<String,String>> nameSet = new HashSet<>();
        for (ImportData importData : dataList) {
            String mobile = importData.getData().get("mobile").toString();
            String name = importData.getData().get("name").toString();
            if (importData.getData().get(PromoterFields.IO_STATUS) == null) {
                importData.getData().set(PromoterFields.IO_STATUS, "2");
            }
            String outerTenantId = importData.getData().get("enterprise_relation_id").toString();
            if (StringUtils.isBlank(mobile) || StringUtils.isBlank(outerTenantId)){
                continue;
            }
            {
                Pair<String, String> pair = Pair.build(mobile, outerTenantId);
                boolean add = set.add(pair);
                if (!add){
                    errorList.add(new ImportError(importData.getRowNo(),"同互联企业手机号码重复")); //ignoreI18n
                }else {
                    SearchQuery build = SearchQuery.builder()
                            .eq("mobile", mobile)
                            .eq("enterprise_relation_id", outerTenantId)
                            .build();
                    int promoterObjTotal = supplyDao.getTotal(User.systemUser(actionContext.getTenantId()), build, "PromoterObj");
                    if (promoterObjTotal > 0) {
                        errorList.add(new ImportError(importData.getRowNo(),"同互联企业手机号码重复")); //ignoreI18n
                    }
                }
            }
            {
                Pair<String, String> pair = Pair.build(name, outerTenantId);
                boolean add = nameSet.add(pair);
                if (!add){
                    errorList.add(new ImportError(importData.getRowNo(),"同互联企业互联用户名称重复")); //ignoreI18n
                }else {
                    SearchQuery build = SearchQuery.builder()
                            .eq("name", name)
                            .eq("enterprise_relation_id", outerTenantId)
                            .build();
                    int promoterObjTotal = supplyDao.getTotal(User.systemUser(actionContext.getTenantId()), build, "PromoterObj");
                    if (promoterObjTotal > 0) {
                        errorList.add(new ImportError(importData.getRowNo(),"同互联企业互联用户名称重复")); //ignoreI18n
                    }
                }
            }
        }
        mergeErrorList(errorList);
    }
}