package com.facishare.crm.fmcg.wq.action;

import com.alibaba.fastjson.JSONArray;
import com.facishare.crm.fmcg.wq.constants.CoveredProductConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class BaseProductCollectionAction extends BaseAddAction{
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    /**
     *
     * @param arg
     * @param type 0-add，1-edit
     */
    protected void ylValidateData(Arg arg,int type) {
        String tenantId = actionContext.getTenantId();
        //查询服务处和对应的业务类型是否已经存在
        String businessType=arg.getObjectData().get("record_type").toString();
        String service_center__cStr = arg.getObjectData().get("service_center__c").toString();
        if(StringUtils.isBlank(service_center__cStr)){
            throw new ValidateException("服务处不能为空"); //ignoreI18n
        }
        List<String> serviceCenterIds = JSONArray.parseArray(service_center__cStr,String.class);
        if(CollectionUtils.isEmpty(serviceCenterIds) || StringUtils.isBlank(serviceCenterIds.get(0))){
            throw new ValidateException("服务处不能为空"); //ignoreI18n
        }
        String serviceCenterId= serviceCenterIds.get(0);

        String dealerId="";
        if(Objects.nonNull(arg.getObjectData().get("dealer"))) {
            dealerId = arg.getObjectData().get("dealer").toString();
        }
        String distributionId="";
        if(Objects.nonNull(arg.getObjectData().get("distribution"))) {
            distributionId=arg.getObjectData().get("distribution").toString();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(10);
        query.setOffset(0);

        Filter typeFilter = new Filter();
        typeFilter.setFieldName("record_type");
        typeFilter.setOperator(Operator.EQ);
        typeFilter.setFieldValues(Lists.newArrayList(businessType));

        if(businessType.equals("server_center__c")){//服务处经营品项
            //查看服务处是否已经存在
            Filter deptFilter = new Filter();
            deptFilter.setFieldName("service_center__c");
            deptFilter.setOperator(Operator.EQ);
            deptFilter.setFieldValues(Lists.newArrayList(serviceCenterId));

            query.setFilters(Lists.newArrayList(typeFilter,deptFilter));
            List<IObjectData> data = serviceFacade.findBySearchQuery(actionContext.getUser(), "ProductCollectionObj", query).getData();

            List<IObjectData> service_center__c = supplyDao.getDepartmentList(tenantId, serviceCenterIds);
            String serviceStr = service_center__c.get(0).getName();
            if(type==0) {
                if (!CollectionUtils.isEmpty(data)) {
                    throw new ValidateException("该服务处:" + //ignoreI18n
                            serviceStr
                            + "已经存在经营范围，请不要重复添加"); //ignoreI18n
                }
            }else{
                if (!CollectionUtils.isEmpty(data)&&data.size()>=2) {
                    throw new ValidateException("该服务处:" + serviceStr + "已经存在经营范围，请不要重复添加"); //ignoreI18n
                }
            }


        }else if(businessType.equals("dealer__c")){//经销商经营品项
            if(StringUtils.isEmpty(dealerId)){
                throw new ValidateException("经销商不能为空"); //ignoreI18n
            }
            Filter dealerFilter = new Filter();
            dealerFilter.setFieldName("dealer");
            dealerFilter.setOperator(Operator.EQ);
            dealerFilter.setFieldValues(Lists.newArrayList(dealerId));

            query.setFilters(Lists.newArrayList(typeFilter,dealerFilter));
            List<IObjectData> data = serviceFacade.findBySearchQuery(actionContext.getUser(), "ProductCollectionObj", query).getData();
            if(!CollectionUtils.isEmpty(data)){
                throw new ValidateException("经销商:"+supplyDao.getAccountObjById(tenantId,dealerId).getName()+"已经存在经营范围，请不要重复添加"); //ignoreI18n
            }

        }else {//分销邮差商经营品项
            if(StringUtils.isEmpty(distributionId)){
                throw new ValidateException("分销邮差商不能为空"); //ignoreI18n
            }
            Filter disFilter = new Filter();
            disFilter.setFieldName("distribution");
            disFilter.setOperator(Operator.EQ);
            disFilter.setFieldValues(Lists.newArrayList(distributionId));

            query.setFilters(Lists.newArrayList(typeFilter,disFilter));
            List<IObjectData> data = serviceFacade.findBySearchQuery(actionContext.getUser(), "ProductCollectionObj", query).getData();
            if(!CollectionUtils.isEmpty(data)){
                throw new ValidateException("分销邮差商:"+supplyDao.getAccountObjById(tenantId,distributionId).getName()+"已经存在经营范围，请不要重复添加"); //ignoreI18n
            }
        }

    }

    //校验产品不能重复添加
    protected void validateDetailData(Arg arg) {
        //校验不能重复
        if (!CollectionUtils.isEmpty(arg.getDetails())) {
            if (!CollectionUtils.isEmpty(arg.getDetails().get(CoveredProductConstants.COVERED_PRODUCT_OBJ))) {
                List<String> coveredIds = arg.getDetails().get(CoveredProductConstants.COVERED_PRODUCT_OBJ).stream().filter(o -> Objects.nonNull(o.get(CoveredProductConstants.RELATION_PRODUCT))).map(o -> o.get(CoveredProductConstants.RELATION_PRODUCT).toString()).collect(Collectors.toList());
                long count = coveredIds.stream().distinct().count();
                if (coveredIds.size() != count) {
                    throw new ValidateException("经营产品明细中存在相同的产品，请检查！"); //ignoreI18n
                }
            }
        }
    }
}
