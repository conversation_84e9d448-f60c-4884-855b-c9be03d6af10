package com.facishare.crm.fmcg.wq.api.area;

import com.alibaba.fastjson.annotation.JSONField;
import com.beust.jcommander.internal.Lists;
import com.facishare.appserver.checkins.api.model.BaseResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface AreaInfoByOwner {
    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "owner")
        @JsonProperty("owner")
        @SerializedName("owner")
        private int owner;

        @JSONField(name = "filter_field")
        @JsonProperty("filter_field")
        @SerializedName("filter_field")
        private List<String> filterField;

    }
    @Data
    @ToString
    class Result extends BaseResult {
        @SerializedName("area_info")
        @JSONField(name = "area_info")
        @JsonProperty("area_info")
        private List<AreaInfoVO> areaInfos = Lists.newArrayList();
    }

    @Data
    @ToString
    class AreaInfoVO implements Serializable {

        private String areaId;

        private String areaName;

        private String postField;

        private String postApiName;

        private String sales_department;

        private String service_center;

        private String business_group;



    }
}
