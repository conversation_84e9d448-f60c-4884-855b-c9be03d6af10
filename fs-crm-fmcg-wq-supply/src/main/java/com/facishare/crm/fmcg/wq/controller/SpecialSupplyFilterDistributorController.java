package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.DistributorSupply;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class SpecialSupplyFilterDistributorController extends StandardRelatedListController {
    SupplyDao supplyDao =  SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected void before(Arg arg) {
        controllerContext = new ControllerContext(controllerContext.getRequestContext(),"AccountObj",controllerContext.getMethodName());
        super.before(arg);
    }
    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {

        QueryResult<IObjectData> result=new QueryResult<IObjectData>();

        //供货关系ID DealerSupplyObj
        String dsId= arg.getObjectData().get("_id").toString();//
        List<IObjectData> dsData = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), Lists.newArrayList(dsId), "DealerSupplyObj");
        String dealerId=dsData.get(0).get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();

//        String dealerId=arg.getObjectData().get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();//供货关系中的配送商ID
        //查询经销商的归属部门
        List<IObjectData> customInfo = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), Lists.newArrayList(dealerId), "AccountObj");

        List<String> deptId=(List<String>)customInfo.get(0).get("data_own_department");

        if(Objects.nonNull(customInfo.get(0).get(AccountObjConstants.Field.otherDepartment.getApiName()))){
            List<String> otherDeptId=(List<String>)customInfo.get(0).get(AccountObjConstants.Field.otherDepartment.getApiName());
            deptId.addAll(otherDeptId);
        }
        Filter deptFilter = new Filter();
        deptFilter.setFieldName("data_own_department");
        deptFilter.setOperator(Operator.IN);
        deptFilter.setFieldValues(deptId);
        deptFilter.setIsCascade(true);
        //查询分销邮差
        Filter businessTypeFilter = new Filter();
        businessTypeFilter.setFieldName("record_type");
        businessTypeFilter.setOperator(Operator.EQ);
        businessTypeFilter.setFieldValues(Lists.newArrayList("distributor__c"));//分销邮差

        query.getFilters().addAll(SearchQuery.convertFilter(arg.getSearchQueryInfo()));
        query.getFilters().addAll(Lists.newArrayList(deptFilter,businessTypeFilter));
//        query.setFilters(Lists.newArrayList(deptFilter,businessTypeFilter));
        query.getFilters().removeIf(o->o.getFieldName().equals("object_describe_api_name"));
        JSONObject searchJson = JSON.parseObject(arg.getSearchQueryInfo());
        Integer fromSpecial = searchJson.getInteger("fromSpecial");
        JSONArray idArray = searchJson.getJSONArray("selectIds");
        if (fromSpecial != null && fromSpecial == 0){
            //
            List<DistributorSupply> distributorSupplyByUpIds = supplyDao.getDistributorSupplyByUpIds(controllerContext.getTenantId(), Lists.newArrayList(dealerId));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(distributorSupplyByUpIds)){
                Filter e = new Filter();
                e.setFieldName("_id");
                e.setFieldValues(distributorSupplyByUpIds.stream().map(o->o.getThisDistributorAccountId()).collect(Collectors.toList()));
                e.setIsCascade(Boolean.TRUE);
                e.setOperator(Operator.NIN);
                query.getFilters().add(e);
            }
        }
        result = serviceFacade.findBySearchQuery(controllerContext.getUser(), "AccountObj",query);
        return result;
    }
}
