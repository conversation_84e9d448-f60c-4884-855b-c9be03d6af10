package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.fxiaoke.common.release.GrayRelease;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;

@Slf4j
public class AreaManageListHeaderController extends StandardListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        String ea = serviceFacade.getEAByEI(controllerContext.getTenantId());
        if(CollectionUtils.isNotEmpty(result.getButtons())) {
            result.getButtons().removeIf(o -> o.get("api_name").equals("ChangeOwner_button_default"));
        }
        if (GrayRelease.isAllow("checkin-server-v2", "areaManage", ea) || GrayRelease.isAllow("checkin-server-v2", "areaRoute", ea)) {
            if(CollectionUtils.isNotEmpty(result.getButtons())) {
                result.getButtons().removeIf(o -> ObjectAction.AREA_BATCH_EDIT.getButtonApiName().equals(o.get("api_name").toString()) || ObjectAction.AREA_MERGE.getButtonApiName().equals(o.get("api_name").toString()));
            }
            List list = (List)result.getLayout().get("buttons");
            if(CollectionUtils.isNotEmpty(list)){
                Iterator iterator = list.iterator();
                while (iterator.hasNext()){
                    JSONObject jsonObject = (JSONObject) iterator.next();
                    if(Objects.nonNull(jsonObject) && ObjectAction.AREA_BATCH_ADD.getButtonApiName().equals(jsonObject.getString("api_name"))){
                        iterator.remove();
                    }
                }
            }
        }
        return result;
    }
}
