package com.facishare.crm.fmcg.wq.model;

import lombok.Data;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 门店
 * @author: zhangsm
 * @create: 2021-04-25 10:40
 **/
@Data
public class Shop {
    /**
     * 门店
     */
    private String id;
    /**
     * 客户的归属部门，用来判断基础的供货范围的
     * 客户的归属部门 + 跨区域供货范围 ……
     *  查到业务组 的范围
     * 门店只能  加到这些部门id下的范围里
     */
//    private Set<String> departmentIds;


    /**
     * 最上级的 业务范围id  和部门id 对应的
     */
//    private List<String> masterBusinessScopeIds;

    /**
     *  直接供货商 范围
     *  由 departmentIds 来取值的
     */
    private List<Supplier> supplierList;
    /**
     * 记录了每个产品都来自那里,有啥货
     */
    private List<Product> productList;
    /**
     * 是否是所有的产品 ，productList 里会记录 所有产品的来源
     */
//    private boolean isAllProduct;


    /**
     *  所有餐品 的来源
     */
//    private String  allSupplierId;
}
