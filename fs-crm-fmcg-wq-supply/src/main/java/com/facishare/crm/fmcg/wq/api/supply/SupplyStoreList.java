package com.facishare.crm.fmcg.wq.api.supply;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.api.model.BaseResult;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface SupplyStoreList {
    @Data
    @ToString
    class Arg extends SupplyApproArgs implements Serializable {


        @JSONField(name = "supply_id")
        @JsonProperty("supply_id")
        @SerializedName("supply_id")
        private String dealerSupplyId;//供货关系ID

        @JSONField(name = "store_ids")
        @JsonProperty("store_ids")
        @SerializedName("store_ids")
        private List<String> storeIds;

        /**
         * 上级客户id
         */
        private String upCustomerId;

    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {
        @J<PERSON>NField(name = "store_ids")
        @JsonProperty("store_ids")
        @SerializedName("store_ids")
        private List<String> storeIds;
        @JSONField(name = "approval_triggered")
        @JsonProperty("approval_triggered")
        @SerializedName("approval_triggered")
        private boolean approvalTriggered = false;
    }

}
