package com.facishare.crm.fmcg.wq.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.api.area.DealerSupplyUpSave;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.DistributorSupply;
import com.facishare.crm.fmcg.wq.model.Product;
import com.facishare.crm.fmcg.wq.model.Shop;
import com.facishare.crm.fmcg.wq.model.Supplier;
import com.facishare.crm.fmcg.wq.model.*;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.model.obj.ProductCollectionObj;
import com.facishare.crm.fmcg.wq.model.obj.ProductObj;
import com.facishare.crm.fmcg.wq.model.obj.ShopProductSupplierRelationshipObj;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.service.SyncAvailableRangeService;
import com.facishare.crm.fmcg.wq.util.FieldUtils;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.crm.privilege.util.EmployeeUtil;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.SqlEscaper;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.bson.types.ObjectId;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.service.model.NewCrmNotification.CUSTOM_REMIND_RECORD_TYPE;

/**
 * @program: fs-crm-fmcg
 * @description: 供应关系总接口
 * @author: zhangsm
 * @create: 2021-04-25 13:14
 **/
@Service
@Slf4j
public class SupplyServiceImpl implements SupplyService {
    @Autowired
    SupplyService supplyService;
    @Autowired
    SupplyDao supplyDao;

    @Autowired
    ServiceFacade serviceFacade;

    @Resource
    AsyncTaskExecutor fmcgThreadPoolExecutor;
    @Resource
    AsyncTaskExecutor fmcgSyncTaskExecutor;

    @Autowired
    SyncAvailableRangeService syncAvailableRangeService;
    @Autowired
    public RedisUtils redisUtils;
    @Autowired
    public CRMNotificationService crmNotificationService;

    @Autowired
    private SpecialTableMapper specialTableMapper;
    List<String> hideButtonApiNames = Lists.newArrayList("supply_store__c","batch_transfer_store__c","all_transfer_store__c","supply_distributor__c","batch_transfer_distributor__c","all_transfer_distributor__c","special_supply_store__c","special_distributor__c");

//    @Override
//    public UpBusinessScope getUpBusinessScope(String tenantId, String supplierId) {
//        UpBusinessScope upBusinessScope = new UpBusinessScope();
//        if (supplierId.length() < 13) {
//            upBusinessScope.setAllProduct(true);
//            return upBusinessScope;
//        }
//        Supplier supplier = supplyService.getSupplierById(tenantId, supplierId, 2);
//        List<Product> parentProductRange = supplier.getParentProductRange();
//        upBusinessScope.setAllProduct(parentProductRange.removeIf(o -> o.getProductId().equals("ALL")));
//        upBusinessScope.setProductIds(parentProductRange.stream().map(o -> o.getProductId()).distinct().collect(Collectors.toList()));
//        return upBusinessScope;
//    }

//    @Override
//    @Deprecated
//    public List<IObjectData> distributorSupplyCircularReference(String tenantId, ObjectDataDocument distributorSupply) {
//
//        return supplyDao.getCircularReferenceDistributorSupply(
//                tenantId,
//                distributorSupply.getId(),
//                distributorSupply.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()).toString(),
//                distributorSupply.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName()).toString()
//
//        );
//    }

//    @Override
//    public List<IObjectData> distributorSupplyRepetition(String tenantId, ObjectDataDocument distributorSupply) {
//        return supplyDao.getDistributorSuppliers(
//                tenantId,
//                distributorSupply.getId(),
//                distributorSupply.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()).toString(),
//                distributorSupply.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName()).toString()
//
//        );
//    }

    @Override
    public List<IObjectData> getDistributorSupplyListByIds(String tenantId, List<String> dataList) {
        return serviceFacade.findObjectDataByIds(tenantId, dataList, DistributorSupplyObjConstants.API_NAME);
    }


//    @Override
//    public List<IObjectData> distributorSupplyByIds(String tenantId, List<String> idList) {
//        SearchQuery searchQuery = SearchQuery.builder()
//                .in("_id", idList)
//                .build();
//        return supplyDao.getAllIObjectDataListByQuery(tenantId, searchQuery, DealerSupplyObjConstants.API_NAME);
//    }

//    @Override
//    public List<IObjectData> dealerSupplyExistProductCollection(String tenantId, ObjectDataDocument dealerSupply) {
//        // TODO: 2021/4/30
//        return null;
//    }

//    @Override
//    public void checkProductCollectionDependencies(String tenantId, ObjectDataDocument objectData, List<ObjectDataDocument> details, IObjectData dbMasterData, List<IObjectData> dbDetailList) {
//        //旧的数据是否是继承上一级别
//        boolean oldIsAllProduct = ProductCollectionObjConstants.Value.isInheritUpProduct(
//                dbMasterData.get(ProductCollectionObjConstants.Field.businessScopeType.getApiName(), String.class)
//        );
//        //旧的产品Id
//        List<String> oldProductList = dbDetailList.stream().map(o -> o.get(CoveredProductObjConstants.Field.productId.getApiName(), String.class))
//                .collect(Collectors.toList());
//        boolean argIsAllProduct =
//                ProductCollectionObjConstants.Value.isInheritUpProduct(
//                        objectData.get(ProductCollectionObjConstants.Field.businessScopeType.getApiName()).toString()
//                );
//        //新的产品Id
//        List<String> argProductList = details.stream().map(o -> o.get(CoveredProductObjConstants.Field.productId.getApiName()).toString())
//                .collect(Collectors.toList());
//        // TODO: 2021/4/30
//    }

//    @Override
//    public List<IObjectData> getRelatedSupplyStore(String tenantId, List<String> idList) {
//        SearchQuery searchQuery = SearchQuery.builder()
//                .in(SupplyStoreObjConstants.Field.upSupplyId.getApiName(), idList)
//                .build();
//        return supplyDao.getAllIObjectDataListByQuery(tenantId, searchQuery, SupplyStoreObjConstants.API_NAME);
////        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId)
////                , SupplyStoreObjConstants.API_NAME, searchQuery.getSearchTemplateQuery());
////        return queryResult.getData();
//    }

    @Override
    public void checkAddSupplyStoreObj(String tenantId, AccountObj thisAccountObj, List<String> addSupplierIds) {

        if (AccountObjConstants.Value.default__c.toString().equals(thisAccountObj.getRecordType())) {
            //门店
        } else {
            throw new ValidateException(String.format("%s 业务类型不是门店无法分配门店的配送商", thisAccountObj.getName())); //ignoreI18n
        }
        //0. 查询 新增的是否在 范围内
        supplyDao.checkSupplierExistDepartment(tenantId, addSupplierIds, Lists.newArrayList(thisAccountObj.getDepartmentId()));
        // 1. 查询已有的供货商
        Shop shopObjById = supplyService.getShopObjById(tenantId, thisAccountObj.getId());
        //2.查询参数的供货范围
        List<Supplier> supplierList = new ArrayList<>();
        for (String supplierId : addSupplierIds) {
            Supplier supplierById = supplyService.getSupplierById(tenantId,
                    supplierId, 1);
            supplierList.add(supplierById);
        }
        Set<String> productIds = new HashSet<>();
        for (Supplier supplier : supplierList) {
            for (Product product : supplier.getProductList()) {
                if (!productIds.add(product.getProductId())) {
                    log.info("选择的配送商之间经营品项重复 {} ", JSON.toJSONString(supplierList));
                    throw new ValidateException(
                            "选择的配送商之间经营品项重复" //ignoreI18n
//                            String.format("%s 配送商与当前配送商经营品项重复",
//                            supplyDao.getAccountObjById(controllerContext.getTenantId(),supplier.getId()).getName())
                    );
                }
            }
        }
        for (Product product : shopObjById.getProductList()) {
            if (!productIds.add(product.getProductId()) && !product.isShopSpecial()) {
                log.info("选择的配送商与当前配送商经营品项重复 {}  {}", JSON.toJSONString(supplierList),JSON.toJSONString(shopObjById.getProductList()));
                throw new ValidateException(String.format("%s 配送商与当前配送商经营品项重复", //ignoreI18n
                        supplyDao.getAccountObjById(tenantId, product.getSupplierId()).getName()));
            }
        }
    }

    @Override
    public void checkSpecialSupplyRepetition(String tenantId, IObjectData specialSupply) {
        SearchQuery.SearchQueryBuilder builder = SearchQuery.builder();
        String shopId = specialSupply.get(SpecialSupplyObjConstants.Field.shopId.getApiName(), String.class);
        String productId = specialSupply.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class);
        String dealerId = specialSupply.get(SpecialSupplyObjConstants.Field.dealerId.getApiName(), String.class);
        builder.eq(SpecialSupplyObjConstants.Field.shopId.getApiName(), shopId);
        builder.eq(SpecialSupplyObjConstants.Field.productId.getApiName(), productId);
        //特例供货判断重复的逻辑 是 shopId 和 productId 完全相同的重复
//        builder.eq(SpecialSupplyObjConstants.Field.dealerId.getApiName(),dealerId);
        String id = specialSupply.getId();
        if (StringUtils.isNotBlank(id)) {
            builder.nin(BaseField.id.getApiName(), Lists.newArrayList(id));
        }
        List<IObjectData> data = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), builder.build(), SpecialSupplyObjConstants.API_NAME);
        if (CollectionUtils.isNotEmpty(data)) {
            throw new ValidateException(String.format("已存在 %s 与当前数据重复", //ignoreI18n
                    data.get(0).getName()));
        }
    }

    @Override
    public void checkAddDistributorSupply(String tenantId, AccountObj thisAccountObj, List<String> addSupplierIds) {

        if (AccountObjConstants.Value.default__c.toString().equals(thisAccountObj.getRecordType())) {
            //门店
            throw new ValidateException(String.format("%s 业务类型是%s 无法分配上级配送商", thisAccountObj.getName() //ignoreI18n
                    , AccountObjConstants.Value.default__c.getDisName()));
        } else if (AccountObjConstants.Value.dealer__c.toString().equals(thisAccountObj.getRecordType())) {
            //经销商
            throw new ValidateException(String.format("%s 业务类型是%s 无法分配上级配送商", thisAccountObj.getName() //ignoreI18n
                    , AccountObjConstants.Value.dealer__c.getDisName()));
        }

        //0. 去掉已经存在的数据
        List<IObjectData> distributorSuppliers = supplyDao.getDistributorSuppliers(tenantId, null, thisAccountObj.getId(), addSupplierIds);
        Set<String> existIdSet = distributorSuppliers.stream().map(o -> o.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName()).toString()).collect(Collectors.toSet());
        addSupplierIds.removeIf(o -> existIdSet.contains(o));
        if (CollectionUtils.isEmpty(addSupplierIds)) {
            return;
        }
        //0, 验证是否循环引用
        List<IObjectData> circularReferenceList = supplyDao.getCircularReferenceDistributorSupply(tenantId, thisAccountObj.getId(), addSupplierIds);

        // 1. 验证 所选择的 数据 是否是在 该服务处下

        supplyDao.checkSupplierExistDepartment(tenantId, addSupplierIds, Lists.newArrayList(thisAccountObj.getDepartmentId()));
        // 2. 查询现在已经有的上级供货商
        Set<String> supplierIds = new HashSet();
        List<DistributorSupply> distributorSupplyList = supplyDao.getDistributorSupplyByThisIds(
                tenantId, Lists.newArrayList(thisAccountObj.getId()));
        supplierIds.addAll(distributorSupplyList.stream().map(o -> o.getUpDealerAccountId()).collect(Collectors.toSet()));
        // 3. 新增的上级 id 并入到已经存在的 经销商id
        supplierIds.addAll(addSupplierIds);
        List<Supplier> supplierList = new ArrayList<>();
        // 4. 查询所有的供货范围
        for (String supplierId : supplierIds) {
            Supplier supplierById = supplyService.getSupplierById(tenantId,
                    supplierId, 1);
            supplierList.add(supplierById);
        }
        // 5. 判断是否有产品冲突
        Set<String> productIds = new HashSet<>();
        for (Supplier supplier : supplierList) {
            for (Product product : supplier.getProductList()) {
                if (!productIds.add(product.getProductId())) {
                    throw new ValidateException(String.format("%s 配送商与当前配送商经营品项重复", //ignoreI18n
                            supplyDao.getAccountObjById(tenantId, supplier.getId()).getName()));
                }
            }
        }
    }

    @Override
    public void checkCoveredProductsAdd(String tenantId, List<String> productIds, String productCollectionId) {
        IObjectData productCollectionObj = supplyDao.getById(tenantId, ProductCollectionObjConstants.API_NAME, productCollectionId);
        String recordType = productCollectionObj.getRecordType();
        if (GrayRelease.isAllow("checkin-server-v2","ylOldCheckCoveredProduct",tenantId)){
            checkCoveredProductsAddOldV1(tenantId, productIds, productCollectionObj);
        }else{
            checkCoveredProductsAddV3(tenantId, productIds, productCollectionObj);

        }
//        checkCoveredProductsAddNew(tenantId, productIds, productCollectionObj);
//        if (ProductCollectionObjConstants.Value.isServerCenter(recordType)) {
//            checkCoveredProductsAddNew(tenantId, productIds, productCollectionObj);
//        } else {
//            checkCoveredProductsAddOldV1(tenantId, productIds, productCollectionObj);
//        }
    }

    /**
     * 只处理服务处的逻辑
     *
     * @param tenantId
     * @param productIds
     * @param productCollectionObj
     */
    @Deprecated
    private void checkCoveredProductsAddNew(String tenantId, List<String> productIds, IObjectData productCollectionObj) {
        //查询所有的跨区域供货经销商

        //根据经营范围的业务类型判断级别
        List<String> dealerIdList = Lists.newArrayList();
        List<String> distributorIdList = Lists.newArrayList();
        String recordType = productCollectionObj.getRecordType();

        //查询所有的跨区域的经销商 或者分销商
        //当前服务处跨区域供货给 其他服务处的
        List<String> thisCenterCrossAreaDealerIds = ListUtils.EMPTY_LIST;
        //其他服务处跨区域供货 给 当前的服务处的
        List<String> otherCenterCrossAreaDealerIds = ListUtils.EMPTY_LIST;
        if (ProductCollectionObjConstants.Value.isServerCenter(recordType)){
            //查询跨区域供货经销商
            SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                    .eq(BaseField.recordType.getApiName(), AccountObjConstants.Value.dealer__c.toString())
                    .inDepartmentAndSub(AccountObjConstants.Field.ownDepartment.getApiName()
                            , (List) productCollectionObj.get(ProductCollectionObjConstants.Field.departmentId.getApiName()))
                    .nNull(AccountObjConstants.Field.otherDepartment.getApiName());

            SearchQuery.SearchQueryBuilder searchQueryBuilder1 = SearchQuery.builder()
                    .eq(BaseField.recordType.getApiName(), AccountObjConstants.Value.dealer__c.toString())
                    .inDepartmentAndSub(AccountObjConstants.Field.otherDepartment.getApiName()
                            , (List) productCollectionObj.get(ProductCollectionObjConstants.Field.departmentId.getApiName()));
            thisCenterCrossAreaDealerIds = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQueryBuilder.build(), AccountObjConstants.API_NAME, Lists.newArrayList("_id"))
                    .stream().map(o -> o.get("_id", String.class)).collect(Collectors.toList());
            otherCenterCrossAreaDealerIds = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQueryBuilder1.build(), AccountObjConstants.API_NAME, Lists.newArrayList("_id"))
                    .stream().map(o -> o.get("_id", String.class)).collect(Collectors.toList());
        }else
        //在进行一次过滤
        if (ProductCollectionObjConstants.Value.isDealer(recordType)) {
            //经销商
            dealerIdList.add(productCollectionObj.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class));
        } else if (ProductCollectionObjConstants.Value.isDistributor(recordType)) {
            //分销商
            distributorIdList.add(productCollectionObj.get(ProductCollectionObjConstants.Field.distributorId.getApiName(), String.class));
        }
        {
            //本服务处的 判断到 分销商一级别 冲突的逻辑逻辑
            // 跨区域供货的 本区域的分销商
            if (CollectionUtils.isNotEmpty(thisCenterCrossAreaDealerIds)) {

                SearchQuery searchQuery = SearchQuery.builder()
                        .eq(BaseField.recordType.getApiName(), ProductCollectionObjConstants.Value.recordType_dealer.getValue())
                        //不等于指定上级的 都是继承的
                        .neq(ProductCollectionObjConstants.Field.businessScopeType.getApiName()
                                , ProductCollectionObjConstants.Value.businessScopeType_4.getValue())
                        .in(ProductCollectionObjConstants.Field.dealerId.getApiName(), thisCenterCrossAreaDealerIds)
                        .build();
                List<IObjectData> dealerCollectionList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, ProductCollectionObjConstants.API_NAME, Lists.newArrayList(ProductCollectionObjConstants.Field.dealerId.getApiName()));
                dealerIdList.addAll(dealerCollectionList.stream().map(o -> o.get(
                        ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class)).collect(Collectors.toList()));
                log.debug("dealerIdList 385{} ", dealerIdList);
            }

            // 取到所有收到影响的下游经销商
            if (CollectionUtils.isNotEmpty(dealerIdList)) {
                //通过上级 id 取 不是特例 的 下级配送商
                SearchQuery noSpecialSearchQuery = SearchQuery.builder()
                        .in(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), dealerIdList)
                        .eq(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), false).build();
                List<IObjectData> data = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), noSpecialSearchQuery, DistributorSupplyObjConstants.API_NAME
                        , Lists.newArrayList(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(),DistributorSupplyObjConstants.Field.upDealerId.getApiName()));
                Map<String,Set<String>> disMap = data.stream().collect(Collectors.groupingBy(
                        o->o.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName(),String.class),
                        Collectors.mapping(o->o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(),String.class),Collectors.toSet())

                ));
                // 分销商id
                distributorIdList.addAll(data.stream()
                        .map(o -> o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class)).distinct().collect(Collectors.toList()));//            SearchQuery searchQuery = SearchQuery.builder()
                log.debug("distributorIdList 406 {} ", distributorIdList);
                // 查对应上级的，看是否包含所有的
                if (CollectionUtils.isNotEmpty(distributorIdList)) {
                    //取 其他受影响的 分销商的上级
                    SearchQuery.SearchQueryBuilder in = SearchQuery.builder()
                            .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), distributorIdList);
                    if (CollectionUtils.isNotEmpty(dealerIdList)) {
                        in.nin(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), dealerIdList);
                    }
                    SearchQuery searchQuery = in.build();
                    // 真正需要调用 可用范围的
                    List<IObjectData> otherDistributors =
                            supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME, Lists.newArrayList(DistributorSupplyObjConstants.Field.upDealerId.getApiName()));
                    Set<String> otherDistributorIds = otherDistributors.stream()
                            .map(o -> o.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), String.class)).collect(Collectors.toSet());
                    checkConflictProductAdd(tenantId, productIds, otherDistributorIds,disMap);
                    log.debug("otherDealersByDistributor 423{} ", otherDistributorIds);
                }
            }
            // 所有的经销商 id 和分销商id 集合
            List<String> supplierIds = Lists.newArrayList();
            supplierIds.addAll(dealerIdList);
            supplierIds.addAll(distributorIdList);
            //查询所有供货门店的记录  不是 特例的
            Set<String> shopIds = Sets.newHashSet();
            //这个地方有可能数据很多

            if (CollectionUtils.isNotEmpty(supplierIds)) {
                SearchQuery searchQuery = SearchQuery.builder()
                        .eq(SupplyStoreObjConstants.Field.specialSupply.getApiName(), false)
                        .in(SupplyStoreObjConstants.Field.upDealerId.getApiName(), supplierIds)
                        .build();
                List<IObjectData> supplyStoreObjList
                        = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME);

                shopIds.addAll(supplyStoreObjList.stream().map(o ->
                        o.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)).collect(Collectors.toSet()));
                log.debug("shopIds 462 {} ", shopIds);
            }
            Set<String> otherDealersByShop = new HashSet<>();
            //配送商对应的下游门店 一样列出一个就行
            Map<String,Set<String>> shopMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(shopIds)) {
                //这不需要查是否是特例的 都的判断
                SearchQuery searchQuery2 = SearchQuery.builder()
                        .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), shopIds)
                        .nin(SupplyStoreObjConstants.Field.upDealerId.getApiName(), supplierIds)
                        .build();
                List<IObjectData> otherSupplyStoreList
                        = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery2, SupplyStoreObjConstants.API_NAME, Lists.newArrayList(SupplyStoreObjConstants.Field.thisDealerId.getApiName(),SupplyStoreObjConstants.Field.upDealerId.getApiName()));
                shopMap = otherSupplyStoreList.stream().collect(Collectors.groupingBy(o->o.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class)
                        ,Collectors.mapping(o->o.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(),String.class),Collectors.toSet())));
                otherDealersByShop.addAll(otherSupplyStoreList.stream()
                        .map(o -> o.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class)).collect(Collectors.toSet()));

                log.debug("otherDealersByShop 474 {}", otherDealersByShop);
            }
            //otherDealersByShop 的过滤 是不在当前服务处下的 在下面才处理

            if (CollectionUtils.isNotEmpty(otherDealersByShop)) {
                SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                        .ninDepartmentAndSub(AccountObjConstants.Field.ownDepartment.getApiName()
                                , (List) productCollectionObj.get(ProductCollectionObjConstants.Field.departmentId.getApiName()))
                        .in("_id", otherDealersByShop);
                SearchQuery searchQuery = searchQueryBuilder.build();
                otherDealersByShop = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, AccountObjConstants.API_NAME
                        , Lists.newArrayList("_id")).stream().map(o -> o.get("_id", String.class)).collect(Collectors.toSet());
                log.debug("otherDealersByShop 487  {} ", otherDealersByShop);
            }
            checkConflictProductAdd(tenantId, productIds, otherDealersByShop, shopMap);
        }

        {
            //其他服务处 的跨本服务处的分销商
            if (CollectionUtils.isNotEmpty(otherCenterCrossAreaDealerIds)) {
                //直接查门店 。跨区域供货的
                SearchQuery searchQuery = SearchQuery.builder().inDepartmentAndSub(AccountObjConstants.Field.ownDepartment.getApiName()
                                , (List) productCollectionObj.get(ProductCollectionObjConstants.Field.departmentId.getApiName()))
                        .hasAnyOf(AccountObjConstants.Field.upDealerIds.getApiName(), otherCenterCrossAreaDealerIds)
                        .nin(AccountObjConstants.Field.upDealerIds.getApiName(), otherCenterCrossAreaDealerIds)
                        .build();
                List<IObjectData> iObjectDataList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, AccountObjConstants.API_NAME
                        , Lists.newArrayList("_id", AccountObjConstants.Field.upDealerIds.getApiName()));
            }
        }


    }

    private void checkConflictProductAdd(String tenantId, List<String> productIds, Set<String> otherDealers, Map<String, Set<String>> map) {
        List<String> errorIds = new ArrayList<>();
        // 看其他的关联关系中 是否存在 新增的这些产品
        otherDealers.parallelStream().forEach(otherDealer -> {
            Supplier supplierById = supplyService.getSupplierById(tenantId, otherDealer, 1);
            //如果包含所有 那么 其他值 为nin
            if (productIds.removeIf(o -> o.equals("ALL"))) {
                if (supplierById.getProductList().stream().anyMatch(o -> !productIds.contains(o.getProductId()))) {
                    errorIds.add(supplierById.getId());
                }
            } else {
                if (supplierById.getProductList().stream().anyMatch(o -> productIds.contains(o.getProductId()))) {
                    errorIds.add(supplierById.getId());
                }
            }
        });
        if (CollectionUtils.isNotEmpty(errorIds)) {
            Set<String> errorThisIds = Sets.newHashSet();
            for (String errorId : errorIds) {
                if (map.containsKey(errorId)) {
                    errorThisIds.addAll(map.get(errorId));
                }
            }
            if (CollectionUtils.isNotEmpty(errorThisIds)) {
                throw new ValidateException(String.format("%s 的配送商 %s 与当前增加经营范围重复", //ignoreI18n
                        supplyDao.getAccountObjByIds(tenantId, errorThisIds).stream().map(o -> o.getName()).collect(Collectors.joining(",")),
                        supplyDao.getAccountObjByIds(tenantId, errorIds).stream().map(o -> o.getName()).collect(Collectors.joining(","))));
            } else {
                throw new ValidateException(String.format("%s 配送商与当前增加经营范围重复", //ignoreI18n
                        supplyDao.getAccountObjByIds(tenantId, errorIds).stream().map(o -> o.getName()).collect(Collectors.joining(","))));
            }
        }
    }

    void checkCoveredProductsAddOldV1(String tenantId, List<String> productIds, IObjectData productCollectionObj) {
        //转成产品id
//        List<String> productIds = coveredStoreObjList.stream()
//                .map(o->o.get(CoveredProductObjConstants.Field.productId.getApiName(),String.class))
//                .distinct().collect(Collectors.toList());
//        String productCollectionId = coveredStoreObjList.get(0)
//                .get(CoveredProductObjConstants.Field.businessScopeId.getApiName(), String.class);
//        IObjectData productCollectionObj = supplyDao.getById(tenantId, ProductCollectionObjConstants.API_NAME, productCollectionId);
        //根据经营范围的业务类型判断级别
        List<String> dealerIdList = Lists.newArrayList();
        List<String> distributorIdList = Lists.newArrayList();
        Set<String> otherDealers = new HashSet<>();
//        List<String> shopIdList = Lists.newArrayList();
        //
//        Set<UpAndDownId> inheritUpAndDownIdSet = Sets.newSet();
        //判断改变的类型
        String recordType = productCollectionObj.getRecordType();
        if (ProductCollectionObjConstants.Value.isServerCenter(recordType)) {
            //业务处
            //根据业务处id 查询所有的经销商id
            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(BaseField.recordType.getApiName(), ProductCollectionObjConstants.Value.recordType_dealer.getValue())
                    //不等于指定上级的 都是继承的
                    .neq(ProductCollectionObjConstants.Field.businessScopeType.getApiName()
                            , ProductCollectionObjConstants.Value.businessScopeType_4.getValue())
                    .inDepartmentAndSub(ProductCollectionObjConstants.Field.departmentId.getApiName()
                            , (List) productCollectionObj.get(ProductCollectionObjConstants.Field.departmentId.getApiName()))
                    .build();
            List<IObjectData> dealerCollectionList = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, ProductCollectionObjConstants.API_NAME);
            dealerIdList.addAll(dealerCollectionList.stream().map(o -> o.get(
                    ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class)).collect(Collectors.toList()));
        } else if (ProductCollectionObjConstants.Value.isDealer(recordType)) {
            //经销商
            dealerIdList.add(productCollectionObj.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class));
        } else if (ProductCollectionObjConstants.Value.isDistributor(recordType)) {
            //分销商
            distributorIdList.add(productCollectionObj.get(ProductCollectionObjConstants.Field.distributorId.getApiName(), String.class));
        }
        // 取到所有收到影响的下游经销商
        if (CollectionUtils.isNotEmpty(dealerIdList)) {
            //通过上级 id 取 不是特例 的 下级配送商
            SearchQuery noSpecialSearchQuery = SearchQuery.builder()
                    .in(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), dealerIdList)
                    .eq(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), false).build();
            List<IObjectData> data = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), noSpecialSearchQuery, DistributorSupplyObjConstants.API_NAME);
//            upAndDownIdSet.addAll(
//                    distributorSupplyByUpIds.stream().map(o -> {
//                        UpAndDownId upAndDownId = new UpAndDownId();
//                        upAndDownId.setDownId(o.getThisSupplierId());
//                        upAndDownId.setUpId(o.getUpSupplierId());
//                        return upAndDownId;
//                    }).collect(Collectors.toSet()));
            // 分销商id
            distributorIdList.addAll(data.stream()
                    .map(o -> o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class)).distinct().collect(Collectors.toList()));//            SearchQuery searchQuery = SearchQuery.builder()
            // 查对应上级的，看是否包含所有的
//                    .eq(BaseField.recordType.getApiName(),ProductCollectionObjConstants.Value.recordType_distributor.getValue())
//                    //不等于指定上级的 都是继承的
//                    .neq(ProductCollectionObjConstants.Field.businessScopeType.getApiName()
//                            ,ProductCollectionObjConstants.Value.businessScopeType_4.getValue())
//                    .inDepartmentAndSub(ProductCollectionObjConstants.Field.departmentId.getApiName()
//                            ,(List)productCollectionObj.get(ProductCollectionObjConstants.Field.departmentId.getApiName()))
//                    .in(ProductCollectionObjConstants.Field.distributorId.getApiName(),distributorIdList)
//                    .build();
//            List<IObjectData> distributorCollectionList = supplyDao.getIObjectDataListByQuery(tenantId, searchQuery, ProductCollectionObjConstants.API_NAME);
//            distributorIdList.clear();
//            distributorIdList.addAll(distributorCollectionList.stream().map(o->o.get(
//                    ProductCollectionObjConstants.Field.distributorId.getApiName(),String.class)).distinct().collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(distributorIdList)) {
                //取 其他受影响的 分销商的上级
                SearchQuery.SearchQueryBuilder in = SearchQuery.builder()
                        .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), distributorIdList);
                if (CollectionUtils.isNotEmpty(dealerIdList)) {
                    in.nin(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), dealerIdList);
                }
                SearchQuery searchQuery = in.build();

                // 真正需要调用 可用范围的
                List<IObjectData> otherDistributors =
                        supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
                otherDealers.addAll(otherDistributors.stream()
                        .map(o -> o.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), String.class)).collect(Collectors.toSet()));

            }
        }
        // 所有的经销商 id 和分销商id 集合
        List<String> supplierIds = Lists.newArrayList();
        supplierIds.addAll(dealerIdList);
        supplierIds.addAll(distributorIdList);
        //查询所有供货门店的记录  不是 特例的
        if (CollectionUtils.isNotEmpty(supplierIds)) {
            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(SupplyStoreObjConstants.Field.specialSupply.getApiName(), false)
                    .in(SupplyStoreObjConstants.Field.upDealerId.getApiName(), supplierIds)
                    .build();
            List<IObjectData> supplyStoreObjList
                    = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME);
            Set<String> shopIds = supplyStoreObjList.stream().map(o ->
                    o.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(shopIds)) {
                SearchQuery searchQuery2 = SearchQuery.builder()
                        .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), shopIds)
                        .nin(SupplyStoreObjConstants.Field.upDealerId.getApiName(), supplierIds)
                        .build();
                List<IObjectData> otherSupplyStoreList
                        = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery2, SupplyStoreObjConstants.API_NAME);
                otherDealers.addAll(otherSupplyStoreList.stream()
                        .map(o -> o.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class)).collect(Collectors.toSet()));
            }
        }
        List<String> errorIds = new ArrayList<>();
        // 看其他的关联关系中 是否存在 新增的这些产品
        otherDealers.parallelStream().forEach(otherDealer -> {
            Supplier supplierById = supplyService.getSupplierById(tenantId, otherDealer, 1);
            //如果包含所有 那么 其他值 为nin
            if (productIds.removeIf(o -> o.equals("ALL"))) {
                if (supplierById.getProductList().stream().anyMatch(o -> !productIds.contains(o.getProductId()))) {
                    errorIds.add(supplierById.getId());
                }
            } else {
                if (supplierById.getProductList().stream().anyMatch(o -> productIds.contains(o.getProductId()))) {
                    errorIds.add(supplierById.getId());
                }
            }
        });
        if (CollectionUtils.isNotEmpty(errorIds)) {
            throw new ValidateException(String.format("%s 配送商与当前增加经营范围重复", //ignoreI18n
                    supplyDao.getAccountObjByIds(tenantId, errorIds).stream().map(o -> o.getName()).collect(Collectors.joining(",/n"))));
        }
    }
    void checkCoveredProductsAddV3(String tenantId, List<String> productIds, IObjectData productCollectionObj) {
        User user = User.systemUser(tenantId);
        //根据经营范围的业务类型判断级别
        List<String> dealerIdList = Lists.newArrayList();
        List<String> distributorIdList = Lists.newArrayList();
        //判断改变的类型
        String recordType = productCollectionObj.getRecordType();
        productCollectionObj.get(ProductCollectionObjConstants.Field.departmentId.getApiName());
        List centerDepartment = (List) productCollectionObj.get(ProductCollectionObjConstants.Field.departmentId.getApiName());
        if (ProductCollectionObjConstants.Value.isServerCenter(recordType)) {
            //业务处
            //根据业务处id 查询所有的经销商id
            SearchQuery searchQuery = SearchQuery.builder()
                    .eq(BaseField.recordType.getApiName(), ProductCollectionObjConstants.Value.recordType_dealer.getValue())
                    //不等于指定上级的 都是继承的
                    .neq(ProductCollectionObjConstants.Field.businessScopeType.getApiName()
                            , ProductCollectionObjConstants.Value.businessScopeType_4.getValue())
                    .inDepartmentAndSub(ProductCollectionObjConstants.Field.departmentId.getApiName()
                            , centerDepartment)
                    .build();
            List<IObjectData> dealerCollectionList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, ProductCollectionObjConstants.API_NAME,
                    Lists.newArrayList(ProductCollectionObjConstants.Field.dealerId.getApiName()));
            dealerIdList.addAll(dealerCollectionList.stream().map(o -> o.get(
                    ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class)).collect(Collectors.toList()));
        } else if (ProductCollectionObjConstants.Value.isDealer(recordType)) {
            //经销商
            dealerIdList.add(productCollectionObj.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class));
        } else if (ProductCollectionObjConstants.Value.isDistributor(recordType)) {
            //分销商
            distributorIdList.add(productCollectionObj.get(ProductCollectionObjConstants.Field.distributorId.getApiName(), String.class));
        }
        // 查客户对象 配送的下级
        List<IObjectData> iObjectDataList = null;
        if (CollectionUtils.isNotEmpty(dealerIdList)) {
            //修改经销商
            SearchQuery searchQuery = SearchQuery.builder()
                    .hasAnyOf(AccountObjConstants.Field.upDealerIds.getApiName(), dealerIdList)
                    .like(AccountObjConstants.Field.upDealerDesc.getApiName(), ",")
                    .inDepartmentAndSub(BaseField.dataOwnDepartment.getApiName(),centerDepartment)
                    .build();
             iObjectDataList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, AccountObjConstants.API_NAME, Lists.newArrayList(
                    AccountObjConstants.Field.upDistributorIds.getApiName(),
                    AccountObjConstants.Field.upDealerIds.getApiName()
            ));

           //不是特例的数据 去批量验证
        }else if (CollectionUtils.isNotEmpty(distributorIdList)){
            //修改经销商
            SearchQuery searchQuery = SearchQuery.builder()
                    .hasAnyOf(AccountObjConstants.Field.upDistributorIds.getApiName(), distributorIdList)
                    .like(AccountObjConstants.Field.upDistributorDesc.getApiName(), ",")
                    .inDepartmentAndSub(BaseField.dataOwnDepartment.getApiName(),centerDepartment)
                    .build();
            iObjectDataList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, AccountObjConstants.API_NAME, Lists.newArrayList(
                    AccountObjConstants.Field.upDistributorIds.getApiName(),
                    AccountObjConstants.Field.upDealerIds.getApiName()
            ));
        }

        //这种其他的 都不用查 的 肯定是其他服务处的
        Set<String> otherDealerIds = new HashSet<>();
        Set<String> thisIds = new HashSet<>();
        Map<String,Set<String>> upAndThisMap = Maps.newHashMap();
        if (iObjectDataList == null){
            return;
        }
        for (IObjectData iObjectData : iObjectDataList) {
            otherDealerIds.addAll(JSONObject.parseObject(JSON.toJSONString(iObjectData.get(AccountObjConstants.Field.upDistributorIds.getApiName())), List.class));
            thisIds.add(iObjectData.getId());
            List<String> upIds = JSONArray.parseArray(iObjectData.get(AccountObjConstants.Field.upDistributorIds.getApiName(), String.class), String.class);
            for (String upId : upIds) {
                upAndThisMap.computeIfAbsent(upId, k -> new HashSet()).add(
                        iObjectData.getId()
                );
            }
        }
        if (CollectionUtils.isEmpty(otherDealerIds)){
            return;
        }
        if (CollectionUtils.isNotEmpty(dealerIdList)){
            //查询下供货分销 删除同一个经销商的数据
            List<IObjectData> tempData = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId),
                    SearchQuery.builder()
                            .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), otherDealerIds)
                            .in(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), dealerIdList).build()
                    , DistributorSupplyObjConstants.API_NAME, Lists.newArrayList(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()));
            if (CollectionUtils.isNotEmpty(tempData)){
                Set<String> rm = tempData.stream().map(o -> o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class))
                        .collect(Collectors.toSet());
                otherDealerIds.removeAll(rm);
            }
        }
        if (CollectionUtils.isNotEmpty(distributorIdList)){
            otherDealerIds.removeAll(distributorIdList);
        }
        if (CollectionUtils.isEmpty(otherDealerIds)){
            return;
        }
        SearchQuery specialQuery = SearchQuery.builder()
                .in(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName(), otherDealerIds)
                .in(SpecialSupplyObjConstants.Field.productId.getApiName(), productIds)
                .in(SpecialSupplyObjConstants.Field.shopId.getApiName(), thisIds)
                .build();
        List<IObjectData> specialList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), specialQuery, SpecialSupplyObjConstants.API_NAME,
                Lists.newArrayList(
                        SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName(),
                        SpecialSupplyObjConstants.Field.productId.getApiName(),
                        SpecialSupplyObjConstants.Field.shopId.getApiName()
                ));
        //特例的如果有问题直接就报错吧
        if (CollectionUtils.isNotEmpty(specialList)) {
            //分析原因
            Set<String> errorThisIds = Sets.newHashSet();
            Set<String> errorUpIds = Sets.newHashSet();
            Set<Pair<String, String>> pairSet = specialList.stream().map(o -> new Pair<String, String>(o.get(SpecialSupplyObjConstants.Field.shopId.getApiName(), String.class)
                    , o.get(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName(), String.class))).collect(Collectors.toSet());
            for (Pair<String, String> stringStringPair : pairSet) {
                String thisId = stringStringPair.first;
                String upId = stringStringPair.second;
                if (upAndThisMap.getOrDefault(upId, SetUtils.EMPTY_SET).contains(thisId)) {
                    errorThisIds.add(thisId);
                    errorUpIds.add(upId);
                }

            }
            if (CollectionUtils.isNotEmpty(errorThisIds)) {
                throw new ValidateException(String.format("%s 的配送商 %s 与当前增加经营范围重复", //ignoreI18n
                        supplyDao.getAccountObjByIds(tenantId, errorThisIds).stream().map(o -> o.getName()).collect(Collectors.joining(",")),
                        supplyDao.getAccountObjByIds(tenantId, errorUpIds).stream().map(o -> o.getName()).collect(Collectors.joining(","))));
            }
        }
        //剩余的判断 不是特例的 数据
        parallel(upAndThisMap.entrySet(),entry->{
            String parallelUpId = entry.getKey();
            Set<String> parallelThisIds = entry.getValue();
            //查询数据 是否是特例
            int total = supplyDao.getTotal(user, SearchQuery.builder()
                    .eq(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), Boolean.FALSE)
                    .eq(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), parallelUpId)
                    .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), parallelThisIds)
                    .build() ,DistributorSupplyObjConstants.API_NAME);
            boolean isCheck = false;
            if (total > 0){
                //不是特例 需要查询数据
                isCheck = true;
            }else {
                total = supplyDao.getTotal(user, SearchQuery.builder()
                        .eq(SupplyStoreObjConstants.Field.specialSupply.getApiName(), Boolean.FALSE)
                        .eq(SupplyStoreObjConstants.Field.upSupplyId.getApiName(), parallelUpId)
                        .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), parallelThisIds)
                        .build() ,SupplyStoreObjConstants.API_NAME);
                if (total > 0){
                    isCheck = true;
                    //如果包含所有 那么 其他值 为nin
                }
            }
            if (isCheck){
                Supplier supplierById = supplyService.getSupplierById(tenantId, parallelUpId, 1);
                boolean isThrow = false;
                if (productIds.removeIf(o -> o.equals("ALL"))) {
                    if (supplierById.getProductList().stream().anyMatch(o -> !productIds.contains(o.getProductId()))) {
                        isThrow = true;
                    }
                } else {
                    if (supplierById.getProductList().stream().anyMatch(o -> productIds.contains(o.getProductId()))) {
                        isThrow = true;
                    }
                }
                if (isThrow){
                    throw new ValidateException(String.format("%s 的配送商 %s 与当前增加经营范围重复", //ignoreI18n
                            supplyDao.getAccountObjByIds(tenantId, parallelThisIds).stream().map(o -> o.getName()).collect(Collectors.joining(",")),
                            supplyDao.getAccountObjByIds(tenantId, Lists.newArrayList(parallelUpId)).stream().map(o -> o.getName()).collect(Collectors.joining(","))));
                }
            }

        });
    }
    private <T> void parallel(Collection<T> datas, Consumer<? super T> action) {
        CompletableFuture.allOf(
                datas.stream().map(o ->
                        CompletableFuture.supplyAsync(() -> {
                                            ClassLoader contextClassLoader = Thread.currentThread()
                                                    .getContextClassLoader();
                                            if (contextClassLoader == null) {
                                                log.info("contextClassLoader is null {}", Thread.currentThread().getName());
                                                Thread.currentThread().setContextClassLoader(ClassLoader.getSystemClassLoader());
                                            }
                                            action.accept(o);
                                            return Boolean.TRUE;
                                        }
                                        , fmcgThreadPoolExecutor)
                                .whenCompleteAsync((r, e) -> {
                                    if (e != null) {
                                        throw new ValidateException(e.getMessage());
                                    }
                                })

                ).toArray(CompletableFuture[]::new)
        ).join();
    }
    @Override
    public void afterCoveredStoreDel(String tenantId, List<String> productIds, String productCollectionId) {
        fmcgThreadPoolExecutor.execute(() -> {
            IObjectData productCollectionObj = supplyDao.getById(tenantId, ProductCollectionObjConstants.API_NAME, productCollectionId);
            //根据经营范围的业务类型判断级别
            List<IObjectData> productCollectionList = Lists.newArrayList();

            //判断改变的类型
            String recordType = productCollectionObj.getRecordType();
            if (ProductCollectionObjConstants.Value.isServerCenter(recordType)) {
                //业务处
                //根据业务处id 查询所有的经销商id 和 配送商的 id
                SearchQuery searchQuery = SearchQuery.builder()
                        .in(BaseField.recordType.getApiName(),
                                Lists.newArrayList(
                                        ProductCollectionObjConstants.Value.recordType_dealer.getValue(),
                                        ProductCollectionObjConstants.Value.recordType_distributor.getValue()
                                )
                        )
                        .inDepartmentAndSub(ProductCollectionObjConstants.Field.departmentId.getApiName()
                                , (List) productCollectionObj.get(ProductCollectionObjConstants.Field.departmentId.getApiName()))
                        .build();
                List<IObjectData> dealerCollectionList = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, ProductCollectionObjConstants.API_NAME);
                productCollectionList.addAll(dealerCollectionList);
            } else if (ProductCollectionObjConstants.Value.isDealer(recordType)) {
                //经销商 查 所有的经销商的对应下级配送商
                List<DistributorSupply> distributorSupplyByUpIds = supplyDao.getDistributorSupplyByUpIds(tenantId,
                        Lists.newArrayList(productCollectionObj.get(ProductCollectionObjConstants.Field.dealerId.getApiName()
                                , String.class)));

                SearchQuery searchQuery = SearchQuery.builder()
                        .eq(BaseField.recordType.getApiName(),
                                ProductCollectionObjConstants.Value.recordType_distributor.getValue()
                        )
                        .in(ProductCollectionObjConstants.Field.distributorId.getApiName()
                                , distributorSupplyByUpIds.stream().map(o -> o.getThisDistributorAccountId()).collect(Collectors.toSet()))
                        .build();
                List<IObjectData> dealerCollectionList = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, ProductCollectionObjConstants.API_NAME);
                productCollectionList.addAll(dealerCollectionList);
                productCollectionList.add(productCollectionObj);
            } else {
                // 配送商
                productCollectionList.add(productCollectionObj);
            }
            List<IObjectData> delData = Lists.newArrayList();
            //如果包含所有 那么 其他值 为nin
            boolean isAll = productIds.removeIf(o -> o.equals("ALL"));
            // 根据分销商的id 查询 门店供货 在经营范围里删除的逻辑
            if (CollectionUtils.isNotEmpty(productCollectionList)) {
                // 查询 经营范围id List
                //删除所有的 经营范围自定的 产品明细对象
                SearchQuery.SearchQueryBuilder in = SearchQuery.builder()
                        // 产品分组的id
                        .in(CoveredProductObjConstants.Field.businessScopeId.getApiName(),
                                productCollectionList.stream().map(o -> o.getId()).distinct().collect(Collectors.toList()));
                if (isAll) {
                    in.nin(CoveredProductObjConstants.Field.productId.getApiName(),
                            productIds);
                } else {
                    in.in(CoveredProductObjConstants.Field.productId.getApiName(),
                            productIds);
                }
                SearchQuery delSearchQuery = in
                        .build();
                delData.addAll(
                        supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), delSearchQuery, CoveredProductObjConstants.API_NAME)
                );
                //删除特例
                Set<String> upIds = new HashSet<>();

                productCollectionList.stream().forEach(o -> {
                    upIds.add(o.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class));
                    upIds.add(o.get(ProductCollectionObjConstants.Field.distributorId.getApiName(), String.class));
                });
                upIds.remove(null);
                SearchQuery.SearchQueryBuilder in1 = SearchQuery.builder()
                        // 产品分组的id
                        .in(SpecialSupplyObjConstants.Field.dealerId.getApiName(),
                                Lists.newArrayList(upIds));
                if (isAll) {
                    in1.nin(SpecialSupplyObjConstants.Field.productId.getApiName(),
                            productIds);
                } else {
                    in1.in(SpecialSupplyObjConstants.Field.productId.getApiName(),
                            productIds);
                }
                SearchQuery delSpecialSearchQuery = in1
                        //产品id list
                        .build();
                delData.addAll(
                        supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), delSpecialSearchQuery, SpecialSupplyObjConstants.API_NAME)
                );
                // 特例清除 以后 查看 下级配送商 和门店供货对象的数据 如果特例都被清掉了的话， 门店供货对象 也被移除掉
                // todo

            }

            if (CollectionUtils.isNotEmpty(delData)) {
                supplyDao.batchInvalidAndDel(User.systemUser(tenantId), delData);
            }

        });


    }

    @Override
    public Shop getShopObjById(String tenantId, String shopId) {
        return getShopObjById(tenantId, shopId, 1);
    }

    @Override
    public Shop getShopObjById(String tenantId, String shopId, int upQueryLevel) {
        if (!GrayRelease.isAllow("checkin-server-v2", "getSupplierByIdOld", tenantId)) {
            return getShopObjByIdV2(tenantId, shopId, upQueryLevel);
        }
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        Shop shop = new Shop();
        shop.setId(shopId);
        //查询供货门店对象
        List<ShopProductSupplierRelationshipObj> dealerSupplyObjList = supplyDao.getSupplyStoresByShopId(tenantId, shopId);
        //查询特殊供货对象
        List<ShopProductSupplierRelationshipObj> specialList = supplyDao.getSpecialSupplyByShopId(tenantId, shopId);
        //特例的产品列表按照 经销商id 分组
        Map<String, List<Product>> supplierIdAndProductsListMap = null;
        if (CollectionUtils.isNotEmpty(specialList)) {
            List<Product> specialProductList = convertSpecialList(specialList);
            supplierIdAndProductsListMap = specialProductList.stream().collect(Collectors.groupingBy(o -> o.getSupplierId()));
        }
        Map<String, List<Product>> finalSupplierIdAndProductsListMap = supplierIdAndProductsListMap;
        // 特例的产品id
        Set<String> specialProductIds =
                specialList.stream().map(o -> o.getProductId()).collect(Collectors.toSet());
        shop.setSupplierList(dealerSupplyObjList.stream().map(o -> {
            //有特例数据的
            if (o.isSpecial()) {
                //有特例的不查产品
                Supplier tempSupplierById = getSupplierById(tenantId, o.getSupplierId(), upQueryLevel != 99 ? 0 : upQueryLevel);
                //设置 特例的产品
                tempSupplierById.setProductList(finalSupplierIdAndProductsListMap.getOrDefault(o.getSupplierId(), Lists.newArrayList()));
                return tempSupplierById;
            } else {
                //没有特例数据的
                Supplier tempSupplierById = getSupplierById(tenantId, o.getSupplierId(), upQueryLevel != 99 ? 1 : upQueryLevel);
                //去掉特例的产品
                if (CollectionUtils.isNotEmpty(specialProductIds)) {
                    tempSupplierById.getProductList().removeIf(e -> specialProductIds.contains(e.getProductId()));
                }
                return tempSupplierById;
            }
        }).collect(Collectors.toList()));
        //合并。理论上没有冲突的
        List<Product> productList = Lists.newArrayList();
        shop.setProductList(productList);


        shop.getSupplierList().forEach(supplier -> {
            productList.addAll(
                    //不需要去掉特例产品
//                    supplier.getProductList().stream().filter(o->!specialProductIds.contains(o.getSupplierId())).collect(Collectors.toSet())
                    supplier.getProductList()
            );
        });
        Set<String> tempSet = new HashSet<>();
        boolean isAll = false;
        for (Product product : productList) {
            if (!tempSet.add(product.getProductId())) {
                throw new ValidateException("存在冲突关系"); //ignoreI18n
            }
            if (product.getProductId().equals("ALL")) {
                isAll = true;
            }
        }
        if (isAll && tempSet.size() > 1) {
            throw new ValidateException("存在冲突关系"); //ignoreI18n
        }
        return shop;
    }

//    @Override
//    public Set<String> getProductListByObj(String tenantId, IObjectData objectData) {
//        //objectData 判断下是供货分销还是供货门店对象
//        String apiName = objectData.getDescribeApiName();
//        String specialFieldApiName = SupplyStoreObjConstants.API_NAME.equals(apiName) ?
//                SupplyStoreObjConstants.Field.specialSupply.getApiName() :
//                DistributorSupplyObjConstants.Field.specialSupply.getApiName();
//        String upDealerIdApiName = SupplyStoreObjConstants.API_NAME.equals(apiName) ?
//                SupplyStoreObjConstants.Field.upDealerId.getApiName() :
//                DistributorSupplyObjConstants.Field.upDealerId.getApiName();
//        String thisDealerIdApiName = SupplyStoreObjConstants.API_NAME.equals(apiName) ?
//                SupplyStoreObjConstants.Field.thisDealerId.getApiName() :
//                DistributorSupplyObjConstants.Field.thisDealerId.getApiName();
//
//        // 如果当前是特例 直接查特例产品对象
//        Boolean isSpecial = objectData.get(specialFieldApiName, Boolean.class);
//        if (isSpecial){
//            //查特例
//            List<IObjectData> specialSupplyProductsByUpAndThis = supplyDao.getSpecialSupplyProductsByUpAndThis(tenantId, objectData.get(upDealerIdApiName, String.class), objectData.get(thisDealerIdApiName, String.class));
//            //返回餐品列表
//            return specialSupplyProductsByUpAndThis.stream().map(o -> o.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class)).collect(Collectors.toSet());
//        }else{
//            //不是特例 查上级的经营范围
//            //查询供货关系设置
//           getProductCollectionByDealerId(tenantId, objectData.get(upDealerIdApiName, String.class), 1);
//        }
//
//    }

    public Shop getShopObjByIdV2(String tenantId, String shopId, int upQueryLevel) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        Shop shop = new Shop();
        shop.setId(shopId);
        //查询供货门店对象
        List<ShopProductSupplierRelationshipObj> dealerSupplyObjList = supplyDao.getSupplyStoresByShopId(tenantId, shopId);
        //查询特殊供货对象
        List<ShopProductSupplierRelationshipObj> specialList = supplyDao.getSpecialSupplyByShopId(tenantId, shopId);
        //特例的产品列表按照 经销商id 分组
        Map<String, List<Product>> supplierIdAndProductsListMap = MapUtils.EMPTY_MAP;
        if (CollectionUtils.isNotEmpty(specialList)) {
            List<Product> specialProductList = convertSpecialList(specialList);
            supplierIdAndProductsListMap = specialProductList.stream().collect(Collectors.groupingBy(o -> o.getSupplierId()));
        }
        Map<String, List<Product>> finalSupplierIdAndProductsListMap = supplierIdAndProductsListMap;
        shop.setSupplierList(dealerSupplyObjList.stream().map(o -> {
            //有特例数据的
            if (o.isSpecial()) {
                //有特例的不查当前的产品，但是有一种 需要继续向上查数据
                Supplier tempSupplierById = getSupplierById(tenantId, o.getSupplierId(), upQueryLevel != 99 ? 0 : upQueryLevel);
                //设置 特例的产品
                tempSupplierById.setProductList(finalSupplierIdAndProductsListMap.getOrDefault(o.getSupplierId(), Lists.newArrayList()));
                return tempSupplierById;
            } else {
                //没有特例数据的
                Supplier tempSupplierById = getSupplierById(tenantId, o.getSupplierId(), upQueryLevel != 99 ? 1 : upQueryLevel);
                return tempSupplierById;
            }
        }).collect(Collectors.toList()));
        //合并。理论上没有冲突的
        List<Product> productList = Lists.newArrayList();
        shop.setProductList(productList);


        shop.getSupplierList().forEach(supplier -> {
            productList.addAll(
                    supplier.getProductList()
            );
        });
        checkForProductConflicts(productList);
        return shop;
    }

    @Override
    public Set<String> getProductListByStoreId(String tenantId, String shopId) {
        //查询供货门店对象
        SearchQuery searchQuery = SearchQuery.builder()
                .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), Lists.newArrayList(shopId))
                .build();
        List<IObjectData> supplyStoreData = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, SupplyStoreObjConstants.API_NAME);
        //查询特殊供货对象
        List<ShopProductSupplierRelationshipObj> specialList = supplyDao.getSpecialSupplyByShopId(tenantId, shopId);
        Set<String> productIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(specialList)) {
            specialList.forEach(o -> productIds.add(o.getProductId()));
        }
        for (IObjectData iObjectData : supplyStoreData) {
            //如果这个门店是特例供货，去特例供货中去查产品,不是特例查经营范围
            if (iObjectData.get(SupplyStoreObjConstants.Field.specialSupply.getApiName()).equals(false)) {
                //查询供货关系设置
                SearchQuery dealerSupplySearchQuery = SearchQuery.builder()
                        .in("_id", iObjectData.get(SupplyStoreObjConstants.Field.upSupplyId.getApiName()).toString())
                        .build();
//                List<IObjectData> dealerSupplyData = supplyDao.getAllIObjectDataListByQuery(tenantId, dealerSupplySearchQuery, DealerSupplyObjConstants.API_NAME);
                List<IObjectData> dealerSupplyData = supplyDao.getQueryDataListByQuery(User.systemUser(tenantId), dealerSupplySearchQuery, DealerSupplyObjConstants.API_NAME, false).getData();
                String dealerId = dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();
                String supplyType = dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.supplyType.getApiName()).toString();
                productIds.addAll(getProductCollectionByDealerId(tenantId, dealerId, supplyType.equals("distributor__c") ? 1 : 0));
            }
        }

        return productIds;
    }

    @Override
    public Set<String> getProductListByDistributionId(String tenantId, String distributionId) {
        //查询下级配送商
        SearchQuery searchQuery = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), Lists.newArrayList(distributionId))
                .build();
        List<IObjectData> distributorSupplyData = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, DistributorSupplyObjConstants.API_NAME);
        //查询特殊供货对象
        List<ShopProductSupplierRelationshipObj> specialList = supplyDao.getSpecialSupplyByShopId(tenantId, distributionId);
        Set<String> productIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(specialList)) {
            specialList.forEach(o -> productIds.add(o.getProductId()));
        }

        for (IObjectData iObjectData : distributorSupplyData) {
            //如果这个配送商是特例供货，去特例供货中去查产品,不是特例查经营范围
            if (iObjectData.get(DistributorSupplyObjConstants.Field.specialSupply.getApiName()).equals(false)) {
                //查询供货关系设置
                SearchQuery dealerSupplySearchQuery = SearchQuery.builder()
                        .in("_id", iObjectData.get(DistributorSupplyObjConstants.Field.upSupplyId.getApiName()).toString())
                        .build();
//                List<IObjectData> dealerSupplyData = supplyDao.getAllIObjectDataListByQuery(tenantId, dealerSupplySearchQuery, DealerSupplyObjConstants.API_NAME);
                List<IObjectData> dealerSupplyData = supplyDao.getQueryDataListByQuery(User.systemUser(tenantId), dealerSupplySearchQuery, DealerSupplyObjConstants.API_NAME, false).getData();
                String dealerId = dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();
                String supplyType = dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.supplyType.getApiName()).toString();
                productIds.addAll(getProductCollectionByDealerId(tenantId, dealerId, supplyType.equals("distributor__c") ? 1 : 0));
            }
        }
        return productIds;
    }

    @Override
    public Set<String> getProductCollectionByDealerId(String tenantId, String dealerId, int type) {
        Set<String> productIds = new HashSet<>();
        //查询主记录(配送商或者经销商)的经营范围
        if (0 == (type)) {//经销商
            productIds.addAll(getProductListByDealer(tenantId, Lists.newArrayList(dealerId),false));
        } else {//配送商
            //查询配送商经营范围
            SearchQuery.SearchQueryBuilder dealerQuery = SearchQuery.builder()
                    .eq(ProductCollectionObjConstants.Field.distributorId.getApiName(), Lists.newArrayList(dealerId))
                    .eq("record_type", "default__c");
            List<IObjectData> dealerData = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ProductCollectionObjConstants.API_NAME, dealerQuery.build().getSearchTemplateQuery()).getData();
            if (CollectionUtils.isEmpty(dealerData)){
              throw new ValidateException("未找到对应配送商的经营范围"); //ignoreI18n
            }
            String scopeTypeValue = dealerData.get(0).get(ProductCollectionObjConstants.Field.businessScopeType.getApiName()).toString();
            if (scopeTypeValue.equals(ProductCollectionObjConstants.Value.businessScopeType_3.getValue())) {//继承上级经销商
                List<DistributorSupply> distributorSupplyList = getRelationListByDownIds(tenantId, Lists.newArrayList(dealerId));
//                List<String> dsIds = Lists.newArrayList();
                List<String> delearList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(distributorSupplyList)) {
                    distributorSupplyList.forEach(o -> delearList.add(o.getUpDealerAccountId()));//经销商
                    //查询供货关系设置表
                    productIds.addAll(getProductListByDealer(tenantId, delearList,false));

                }
            } else {//指定产品
                List<IObjectData> cpData = getDesignateProductListByRelatedIds(tenantId, CoveredProductObjConstants.API_NAME, Lists.newArrayList(dealerData.get(0).getId()));
                if (CollectionUtils.isNotEmpty(cpData)) {
                    cpData.forEach(o -> productIds.add(o.get("relation_product").toString()));
                }
            }
        }
        return productIds;
    }
    private Map<String,Set<String>> getProductListByCustomerIds(String tenantId,List<String> customerIds,boolean noQueryAllProduct) {
        Map<String, Set<String>> customerAndProductIdsMap = Maps.newHashMap();

        //根据customId 查询 经营范围
        SearchQuery.SearchQueryBuilder dealerQuery = SearchQuery.builder()
                .in(ProductCollectionObjConstants.Field.dealerId.getApiName(), customerIds);
        //分销商的query
        SearchQuery.SearchQueryBuilder distributorQuery = SearchQuery.builder()
                .in(ProductCollectionObjConstants.Field.distributorId.getApiName(), customerIds);
        //拼query
        dealerQuery.addOrWheres(distributorQuery); 
        Set<String> allProductIds = null;
        //查经营范围
        List<IObjectData> dealerData = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ProductCollectionObjConstants.API_NAME, dealerQuery.build().getSearchTemplateQuery()).getData();
        if (CollectionUtils.isEmpty(dealerData)) {
            throw new ValidateException("未找到对应经销商的经营范围"); //ignoreI18n
        }
        for (IObjectData dealerDatum : dealerData) {
            Set<String> productIds = Sets.newHashSet();
            //判断是经销商 还是分销商
            if (ProductCollectionObjConstants.Value.isDealer(dealerDatum.getRecordType())) {
                //经销商经营范围
                String scopeTypeValue = dealerDatum.get(ProductCollectionObjConstants.Field.businessScopeType.getApiName()).toString();
                if (scopeTypeValue.equals(ProductCollectionObjConstants.Value.businessScopeType_2.getValue())) {//继承服务处
                    //查询经销商的归属部门
                    List<IObjectData> customInfo = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(dealerDatum.get("dealer").toString()), "AccountObj");
                    if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(customInfo)) {
                        //查询服务处的经营范围
                        List<IObjectData> serviceData = getServiceCenterProductCollection(tenantId, customInfo);
                        if (CollectionUtils.isNotEmpty(serviceData)) {
                            if (ProductCollectionObjConstants.Value.businessScopeType_1.getValue().equals(serviceData.get(0).get("business_scope_type").toString())) {
                                //查询全部产品
                                if (noQueryAllProduct){
                                    productIds.add("ALL");
                                }else {
                                    if (allProductIds == null) {
                                        QueryResult<IObjectData> allProductList = supplyDao.getAllProductList(tenantId, null);
                                        if (Objects.nonNull(allProductList)) {
                                            allProductIds = allProductList.getData().stream().map(o -> o.getId()).collect(Collectors.toSet());
                                        }else{
                                            allProductIds = Sets.newHashSet();
                                        }
                                    }
                                    productIds.addAll(allProductIds);
                                }
                            } else {
                                //查询经营范围明细对象
                                List<IObjectData> cpData = getDesignateProductListByRelatedIds(tenantId, CoveredProductObjConstants.API_NAME, Lists.newArrayList(serviceData.get(0).getId()));
                                if (CollectionUtils.isNotEmpty(cpData)) {
                                    cpData.forEach(o -> productIds.add(o.get("relation_product").toString()));
                                }
                            }
                        }
                    }
                } else {//指定产品
                    //查询经营范围明细对象
                    List<IObjectData> cpData = getDesignateProductListByRelatedIds(tenantId, CoveredProductObjConstants.API_NAME, Lists.newArrayList(dealerDatum.getId()));
                    if (CollectionUtils.isNotEmpty(cpData)) {
                        cpData.forEach(o -> productIds.add(o.get("relation_product").toString()));
                    }
                }
                customerAndProductIdsMap.put(dealerDatum.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class), productIds);
            } else if (ProductCollectionObjConstants.Value.isDistributor(dealerDatum.getRecordType())) {
                //分销商
                String scopeTypeValue = dealerDatum.get(ProductCollectionObjConstants.Field.businessScopeType.getApiName()).toString();
                if (scopeTypeValue.equals(ProductCollectionObjConstants.Value.businessScopeType_3.getValue())) {//继承上级经销商
                    List<DistributorSupply> distributorSupplyList = getRelationListByDownIds(tenantId, Lists.newArrayList(dealerDatum.get(ProductCollectionObjConstants.Field.distributorId.getApiName(),String.class)));
                    if (CollectionUtils.isNotEmpty(distributorSupplyList)) {
                        //按照是否是特例供货 分组
                        Map<Boolean, List<DistributorSupply>> specialMap = distributorSupplyList.stream().collect(Collectors.groupingBy(o -> o.isSpecialSupply()));
                        specialMap.entrySet().forEach(entry->{
                            Boolean isSpecial = entry.getKey();
                            if (isSpecial){
                                //查特例表
                                entry.getValue().stream().map(o->o.getUpDealerAccountId()).forEach(upId->{
                                    productIds.addAll(supplyDao.getSpecialSupplyProductsByUpAndThis(tenantId, upId, dealerDatum.get(ProductCollectionObjConstants.Field.distributorId.getApiName(),String.class)).stream().map(o->o.get(SpecialSupplyObjConstants.Field.productId.getApiName(),String.class)).collect(Collectors.toSet()));
                                });
                            }else{
                                productIds.addAll(getProductListByDealer(tenantId, entry.getValue().stream().map(o->o.getUpDealerAccountId()).collect(Collectors.toList()),false));
                            }
                        });

                    }
                } else {//指定产品
                    List<IObjectData> cpData = getDesignateProductListByRelatedIds(tenantId, CoveredProductObjConstants.API_NAME, Lists.newArrayList(dealerDatum.getId()));
                    if (CollectionUtils.isNotEmpty(cpData)) {
                        cpData.forEach(o -> productIds.add(o.get("relation_product").toString()));
                    }
                }
                customerAndProductIdsMap.put(dealerDatum.get(ProductCollectionObjConstants.Field.distributorId.getApiName(), String.class), productIds);
            } else {
                throw new ValidateException("不支持的业务类型"); //ignoreI18n
            }
        }
        return customerAndProductIdsMap;
    }

    private List<IObjectData> getServiceCenterProductCollection(String tenantId, List<IObjectData> customInfo) {
        List<String> serviceCenterDepartmentIds = (List<String>) customInfo.get(0).get("data_own_department");
        serviceCenterDepartmentIds = supplyDao.getServiceCenterDepartmentIds(tenantId, serviceCenterDepartmentIds);
        SearchQuery.SearchQueryBuilder serviceQuery = SearchQuery.builder()
                .in(ProductCollectionObjConstants.Field.departmentId.getApiName(), serviceCenterDepartmentIds)
                .eq("record_type", "server_center__c");
        List<IObjectData> serviceData = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ProductCollectionObjConstants.API_NAME, serviceQuery.build().getSearchTemplateQuery()).getData();
        return serviceData;
    }

    /**
     * 批量查询经销商的经营范围
     *
     * @param dealerIds
     * @return 产品ids
     */
    private List<String> getProductListByDealer(String tenantId, List<String> dealerIds,boolean noQueryAll) {
        //查询经销商经营范围
        SearchQuery.SearchQueryBuilder dealerQuery = SearchQuery.builder()
                .in(ProductCollectionObjConstants.Field.dealerId.getApiName(), dealerIds)
                .eq("record_type", "dealer__c");
        List<IObjectData> dealerData = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ProductCollectionObjConstants.API_NAME, dealerQuery.build().getSearchTemplateQuery()).getData();

        List<String> productIds = Lists.newArrayList();
        for (IObjectData iObjectData : dealerData) {
            String scopeTypeValue = iObjectData.get(ProductCollectionObjConstants.Field.businessScopeType.getApiName()).toString();
            if (scopeTypeValue.equals(ProductCollectionObjConstants.Value.businessScopeType_2.getValue())) {//继承服务处
                //查询经销商的归属部门
                List<IObjectData> customInfo = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(iObjectData.get("dealer").toString()), "AccountObj");
                if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(customInfo)) {
                    //查询服务处的经营范围
                    List<IObjectData> serviceData = getServiceCenterProductCollection(tenantId, customInfo);
                    if (CollectionUtils.isNotEmpty(serviceData)) {
                        if (ProductCollectionObjConstants.Value.businessScopeType_1.getValue().equals(serviceData.get(0).get("business_scope_type").toString())) {
                            //查询全部产品
                            if (noQueryAll){
                                productIds.add("ALL");
                            }else {
                                QueryResult<IObjectData> allProducts = supplyDao.getAllProductList(tenantId, null);
                                if (Objects.nonNull(allProducts)) {
                                    allProducts.getData().forEach(o -> productIds.add(o.getId()));
                                }
                            }

                        } else {
                            //查询经营范围明细对象
                            List<IObjectData> cpData = getDesignateProductListByRelatedIds(tenantId, CoveredProductObjConstants.API_NAME, Lists.newArrayList(serviceData.get(0).getId()));
                            if (CollectionUtils.isNotEmpty(cpData)) {
                                cpData.forEach(o -> productIds.add(o.get("relation_product").toString()));
                            }
                        }
                    }
                }
            } else {//指定产品
                //查询经营范围明细对象
                List<IObjectData> cpData = getDesignateProductListByRelatedIds(tenantId, CoveredProductObjConstants.API_NAME, Lists.newArrayList(iObjectData.getId()));
                if (CollectionUtils.isNotEmpty(cpData)) {
                    cpData.forEach(o -> productIds.add(o.get("relation_product").toString()));
                }

            }
        }
        return productIds;
    }

    @Deprecated
    private List<Product> convertProduct(List<ProductObj> allProductObj, Set<String> specialProductIds, String allSupplierId, String shopId) {
        List<Product> productList = Lists.newArrayList();
        for (ProductObj productObj : allProductObj) {
            if (specialProductIds != null || !specialProductIds.contains(productObj.getProductId())) {
                productList.add(Product.builder().productId(productObj.getProductId())
                        .productName(productObj.getProductName())
                        .shopId(shopId)
                        .supplierId(allSupplierId)
                        .build());
            }
        }
        return productList;
    }

    private List<Product> convertSpecialList(List<ShopProductSupplierRelationshipObj> specialList) {
        List<Product> productList = Lists.newArrayList();
        for (ShopProductSupplierRelationshipObj shopProductSupplierRelationshipObj : specialList) {
            productList.add(Product.builder().productId(shopProductSupplierRelationshipObj.getProductId())
                    .productName(shopProductSupplierRelationshipObj.getProductName())
                    .shopId(shopProductSupplierRelationshipObj.getShopId())
                    .supplierId(shopProductSupplierRelationshipObj.getSupplierId())
                    .bizId(shopProductSupplierRelationshipObj.getId())
                    .isShopSpecial(true)
                    .build());
        }
        return productList;
    }

    public Supplier getSupplierByIdV2(String tenantId, String supplierId, int upQueryLevel) {
        //get所有相关数据
        Supplier supplier = null;
        if (StringUtils.isBlank(supplierId)) {
            return supplier;
        }
        //上级关系
        ProductCollectionObj productCollectionObj = null;
        //构建树状结构
        {
            if (supplierId.length() < 13) {
                //业务组
                productCollectionObj = supplyDao.getProductCollectionObjByDepartmentId(tenantId, supplierId, false);
                supplier = Supplier.builder().businessScopeId(productCollectionObj.getScopeId())
                        .isAllRange(productCollectionObj.isAllParentProduct())
                        .level(0)
                        .departmentId(productCollectionObj.getDepartmentId())
                        .id(supplierId)
                        .build();
            } else {
                productCollectionObj = supplyDao.getProductCollectionObjByAccountId(tenantId, supplierId, false);
                if (Strings.isBlank(productCollectionObj.getDistributorId())) {
                    //经销商
                    List<Supplier> parentSuppliers = Lists.newArrayList();
                    if (StringUtils.isBlank(productCollectionObj.getDepartmentId())) {
                        throw new ValidateException("所选的" + ProductCollectionObjConstants.Field.businessScopeText.getLabel() + ProductCollectionObjConstants.Field.departmentId.getLabel() + "为空"); //ignoreI18n
                    }
                    ProductCollectionObj parent = supplyDao.getProductCollectionObjByDepartmentId(tenantId, productCollectionObj.getDepartmentId(), false);
                    Supplier parentSupplier = Supplier.builder().businessScopeId(parent.getScopeId())
                            .isAllRange(parent.isAllParentProduct())
                            .level(0)
                            .departmentId(parent.getDepartmentId())
                            .id(parent.getDepartmentId())
                            .build();

                    if (parentSupplier != null) {
                        parentSuppliers.add(parentSupplier);
                    }
                    supplier = Supplier.builder().businessScopeId(productCollectionObj.getScopeId())
                            .isAllRange(productCollectionObj.isAllParentProduct())
                            .level(1)
                            .departmentId(productCollectionObj.getDepartmentId())
                            .parentSuppliers(parentSuppliers)
                            .id(supplierId)
                            .build();
                } else {
                    //分销商 需要构建层级关系
                    List<DistributorSupply> leaderRelationList = getRelationListByDownIds(tenantId, Lists.newArrayList(supplierId));
                    if (CollectionUtils.isEmpty(leaderRelationList)) {
                        throw new ObjectDataNotFoundException(
                                String.format("分销商 %s 没有查到对应的上级,请到 %s 中配置", //ignoreI18n
                                        supplyDao.getAccountObjById(tenantId, supplierId).getName(), DistributorSupplyObjConstants.DISPLAY_NAME));
                    }
                    Set<String> accountIds = Sets.newHashSet();
                    leaderRelationList.stream().forEach(o -> {
                        accountIds.add(o.getThisDistributorAccountId());
                        accountIds.add(o.getUpDealerAccountId());
                    });
                    //查询所有的范围数据 如果是 isExistStop 那么后续再查产品数据
                    List<ProductCollectionObj> productCollectionObjList =
                            supplyDao.getProductCollectionObjListByAccountIds(tenantId,
                                    Lists.newArrayList(accountIds), false);
                    Set<String> centerDepartmentIds = new HashSet<>();
                    /**
                     * 经销商和他爹
                     */
                    Map<String, Set<String>> dealerIdIdAndParentsMap = new HashMap<>();
                    productCollectionObjList.stream().forEach(o -> {
                        // 经销商 向上查
                        if (StringUtils.isBlank(o.getDistributorId())) {
                            centerDepartmentIds.add(o.getDepartmentId());
                            Set<String> dealerParents = dealerIdIdAndParentsMap.get(o.getDealerId());
                            if (CollectionUtils.isEmpty(dealerParents)) {
                                dealerParents = Sets.newHashSet();
                                dealerIdIdAndParentsMap.put(o.getDealerId(), dealerParents);
                            }
                            dealerParents.add(o.getDepartmentId());
                        }
                    });
                    if (CollectionUtils.isNotEmpty(centerDepartmentIds)) {
                        productCollectionObjList.addAll(
                                supplyDao.getProductCollectionObjByDepartmentIds(tenantId, Lists.newArrayList(centerDepartmentIds), upQueryLevel == 99)
                        );
                    } else {
                        throw new ObjectDataNotFoundException(
                                String.format("未找到 相关经销商")); //ignoreI18n
                    }
                    //构建基础的Supplier 数据到map
                    Map<String, Supplier> idAndSupplierMap =
                            productCollectionObjList.parallelStream().map(o -> {
                                String id = StringUtils.isNotBlank(o.getDistributorId())
                                        ? o.getDistributorId() : StringUtils.isNotBlank(o.getDealerId())
                                        ? o.getDealerId() : o.getDepartmentId();
                                int level = StringUtils.isNotBlank(o.getDistributorId())
                                        ? 99 : StringUtils.isNotBlank(o.getDealerId())
                                        ? 1 : 0;
                                return Supplier.builder().businessScopeId(o.getScopeId())
                                        .isAllRange(o.isAllParentProduct())
                                        .level(level)
                                        .departmentId(o.getDepartmentId())
                                        .id(id)
                                        .build();
                            }).collect(Collectors.toMap(k -> k.getId(), v -> v));

                    Map<String, Set<String>> sonIdAndParentMap = leaderRelationList.stream()
                            .collect(Collectors.groupingBy(DistributorSupply::getThisDistributorAccountId,
                                    Collectors.mapping(DistributorSupply::getUpDealerAccountId, Collectors.toSet())));
                    sonIdAndParentMap.putAll(dealerIdIdAndParentsMap);
                    //根据循环关系构建数据
                    supplier = convertSupplier(sonIdAndParentMap, idAndSupplierMap);

                }
            }
        }
        // 填充产品
        if (upQueryLevel > 0) {
            //树状结构已经构建 填充产品列表
            handleSupplierProductListIsExistStopV2(tenantId, supplier, upQueryLevel, null);
            // 判断产品范围冲突
            List<Product> productList = supplier.getProductList();
            checkForProductConflicts(productList);
        }
        return supplier;


    }

    @Override
    public Supplier getSupplierById(String tenantId, String supplierId, int upQueryLevel) {
        return getSupplierByIdV2(tenantId, supplierId, upQueryLevel);
    }

    @Override
    public void checkForProductConflicts(List<Product> productList) {
        if (CollectionUtils.isNotEmpty(productList)) {
            List<String> idList = productList.stream().map(o -> o.getProductId()).collect(Collectors.toList());
//            //如果配送商相同且产品id相同需要去除掉
//            Set<Pair<String,String>> productIds = productList.stream().map(o -> Pair.of(o.getSupplierId(),o.getProductId())).collect(Collectors.toSet());
//            List<String> idList = productIds.stream().map(o -> o.getValue()).collect(Collectors.toList());
            if (checkForProductIdConflicts(idList)) {
                throw new ValidateException("存在冲突关系"); //ignoreI18n
            }

        }
    }

    @Override
    public void checkForProductConflicts(Collection<Product> productList, Collection<Product> otherProductList) {
        List list = new ArrayList();
        if (CollectionUtils.isNotEmpty(productList)) {
            list.addAll(productList);
        }
        if (CollectionUtils.isNotEmpty(otherProductList)) {
            list.addAll(otherProductList);
        }
        checkForProductConflicts(list);
    }

    /**
     * 填充 产品数据
     *
     * @param tenantId
     * @param supplier
     * @param upQueryLevel
     */
    private void handleSupplierProductListIsExistStopV2(String tenantId, Supplier supplier, int upQueryLevel, String sonId) {
        {
            upQueryLevel--;
            if ((supplier.isParentAllRange() || upQueryLevel > 0) && supplier.getLevel() != 0) {
                //兼容错误
                if (CollectionUtils.isEmpty(supplier.getParentSuppliers())) {
                    throw new ValidateException(supplier.getLevel() == 2 ? "分销商" : "经销商" + "的上级经营范围不能为空"); //ignoreI18n
                }
                for (Supplier parentSupplier : supplier.getParentSuppliers()) {
                    //兼容错误
                    if (!supplier.getId().equals(parentSupplier.getId())) {
                        handleSupplierProductListIsExistStopV2(tenantId, parentSupplier, upQueryLevel, supplier.getId());
                    }
                }
            }
            boolean isSpecial = Boolean.FALSE;
            if (supplier.getLevel() != 0) {
                //是否是特例供货
                //经销商或者分销商
                if (StringUtils.isNotBlank(sonId)) {
                    //查询对儿子是否是特殊供货
                    //如果是 查询 特殊供货的产品
                    if (supplyDao.checkIsSpecialSupplyByDistributorId(tenantId, supplier.getId(), sonId)) {
                        isSpecial = true;
                        List<IObjectData> specialSupplyProductsByUpAndThis = supplyDao.getSpecialSupplyProductsByUpAndThis(tenantId, supplier.getId(), sonId);
                        if (CollectionUtils.isNotEmpty(specialSupplyProductsByUpAndThis)) {
//                            List<Product> collect = specialSupplyProductsByUpAndThis.stream().map(iObjectData -> {
//                                return Product.builder()
//                                        .productId(iObjectData.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class))
//                                        .isDistributorSpecial(true)
//                                        .supplierId(iObjectData.get(SpecialSupplyObjConstants.Field.dealerId.getApiName(), String.class))
//                                        .build();
//                            }).collect(Collectors.toList());
                            //productId 去重
                            List<Product> collect = specialSupplyProductsByUpAndThis.stream().map(iObjectData -> {
                                return Product.builder()
                                        .productId(iObjectData.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class))
                                        .isDistributorSpecial(true)
                                        .supplierId(iObjectData.get(SpecialSupplyObjConstants.Field.dealerId.getApiName(), String.class))
                                        .build();
                            }).collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(Product::getProductId))), ArrayList::new));
                            supplier.setProductList(collect);
                        }
                    }
                }
            }
            //正常流程
            if (!isSpecial) {
                resetDefaultProductList(tenantId, supplier);
            }

        }
    }

    private void resetDefaultProductList(String tenantId, Supplier supplier) {
        if (supplier.isParentAllRange()) {
            supplier.resetProductList();
        } else {
            supplier.setProductList(
                    supplyDao.getProductListByProductCollectionId(tenantId, supplier.getBusinessScopeId())
                            .stream().map(o -> convertProductByObj(o, supplier.getId())).collect(Collectors.toList())
            );

        }
    }

    /**
     * 填充 产品数据
     *
     * @param tenantId
     * @param supplier
     * @param upQueryLevel
     */
    @Deprecated
    private void handleSupplierProductListIsExistStop(String tenantId, Supplier supplier, int upQueryLevel) {
        if ((supplier.isParentAllRange() || upQueryLevel > 0) && supplier.getLevel() != 0) {

            for (Supplier parentSupplier : supplier.getParentSuppliers()) {
                if (!supplier.getId().equals(parentSupplier.getId())) {
                    handleSupplierProductListIsExistStop(tenantId, parentSupplier, upQueryLevel);
                }
            }
        }
//        if (upQueryLevel > 0) {
        //如果存在特例关系 ，name产品id使用特例产品的逻辑 不使用这里的
        if (supplier.isParentAllRange()) {
            //计算下继承的产品list
            supplier.resetProductList();
            if (supplier.getLevel() == 0) {
                //业务处
                ArrayList<Product> all = Lists.newArrayList();
                Product allProduct = Product.builder()
                        .productId("ALL")
                        .productName("所有产品") //ignoreI18n
                        .supplierId(supplier.getId())
                        .build();
                all.add(allProduct);
                supplier.setProductList(all);
            }
        } else {
            if (supplier.getLevel() == 2) {
                if (supplier.getLevel() != 0) {
                    List<ShopProductSupplierRelationshipObj> specialSupplyByShopIdList = supplyDao.getSpecialSupplyByShopId(tenantId, supplier.getId());
                    List<Product> specialProducts = specialSupplyByShopIdList.stream().map(o -> Product.builder().productName(o.getProductName())
                            .productId(o.getProductId())
                            .supplierId(o.getSupplierId())
                            .isDistributorSpecial(true)
                            .build()).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(specialProducts)) {
                        // 如果特例供货不为空 则替换
                        Map<String, List<Product>> map = specialProducts.stream().collect(Collectors.groupingBy(o -> o.getSupplierId()));
                        for (Supplier parentSupplier : supplier.getParentSuppliers()) {
                            List<Product> products = map.get(parentSupplier.getId());
                            if (CollectionUtils.isNotEmpty(products)) {
                                parentSupplier.setProductList(products);
                            }
                        }
                    }
                }
            } else {
                supplier.setProductList(
                        supplyDao.getProductListByProductCollectionId(tenantId, supplier.getBusinessScopeId())
                                .stream().map(o -> convertProductByObj(o, supplier.getId())).collect(Collectors.toList())
                );
            }
        }
        upQueryLevel--;
//        }

    }

    private Product convertProductByObj(ProductObj o, String supplierId) {
        return Product.builder().productName(o.getProductName())
                .productId(o.getProductId())
                .supplierId(supplierId)
                .build();

    }


    /**
     * 构造 树形结构
     *
     * @param sonIdAndParentMap
     * @param idAndSupplierMap
     * @return
     */
    private Supplier convertSupplier(Map<String, Set<String>> sonIdAndParentMap, Map<String, Supplier> idAndSupplierMap) {
        String resultSupplierId = null;
        Iterator<Map.Entry<String, Set<String>>> iterator = sonIdAndParentMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Set<String>> next = iterator.next();
            String sonId = next.getKey();
            Set<String> parentList = next.getValue();
            // 结果 最儿子节点
            if (StringUtils.isBlank(resultSupplierId) || parentList.contains(resultSupplierId)) {
                resultSupplierId = sonId;
            }
            // 当前的儿子节点
            Supplier sonSupplier = idAndSupplierMap.get(sonId);
            if (CollectionUtils.isEmpty(sonSupplier.getParentSuppliers())) {
                sonSupplier.setParentSuppliers(Lists.newArrayList());
            }
            for (String parentId : parentList) {
                sonSupplier.getParentSuppliers().add(idAndSupplierMap.get(parentId));
            }
        }
        return idAndSupplierMap.get(resultSupplierId);
    }

    @Override
    public List<DistributorSupply> getRelationListByDownIds(String tenantId, List<String> supplierIds) {
        List<DistributorSupply> distributorSupplyList = getDistributorSuppliesByDown(tenantId, supplierIds, 5);
        return distributorSupplyList;
    }

    @NotNull
    List<DistributorSupply> getDistributorSuppliesByDown(String tenantId, List<String> supplierIds, int index) {
        if (CollectionUtils.isEmpty(supplierIds)) {
            return ListUtils.EMPTY_LIST;
        }
        List<DistributorSupply> distributorSupplyList = Lists.newArrayList();
        List<DistributorSupply> tempDistributorSupplies = supplyDao.getDistributorSupplyByThisIds(tenantId, supplierIds);
        if (CollectionUtils.isNotEmpty(tempDistributorSupplies)) {
            distributorSupplyList.addAll(tempDistributorSupplies);
            List<String> subSupplierIds = tempDistributorSupplies.stream().map(o -> o.getUpDealerAccountId())
                    .filter(o -> !supplierIds.contains(o)).collect(Collectors.toList());
            subSupplierIds.removeAll(supplierIds);
            index--;
            List<DistributorSupply> subDistributorSupplies = null;
            if (index > 0 && CollectionUtils.isNotEmpty(subSupplierIds)) {
                subDistributorSupplies = getDistributorSuppliesByDown(tenantId, subSupplierIds, index);
            }
            if (CollectionUtils.isNotEmpty(subDistributorSupplies)) {
                distributorSupplyList.addAll(subDistributorSupplies);
            }
        }
        return distributorSupplyList;
    }

    @Override
    public List<DistributorSupply> getRelationListByUpIds(String tenantId, List<String> supplierIds) {
        List<DistributorSupply> distributorSupplyList = getDistributorSuppliesByUp(tenantId, supplierIds, 5);
        return distributorSupplyList;
    }

    @NotNull
    private List<DistributorSupply> getDistributorSuppliesByUp(String tenantId, List<String> supplierIds, int index) {
        if (CollectionUtils.isEmpty(supplierIds)) {
            return ListUtils.EMPTY_LIST;
        }
        List<DistributorSupply> distributorSupplyList = Lists.newArrayList();
        List<DistributorSupply> tempDistributorSupplies = supplyDao.getDistributorSupplyByUpIds(tenantId, supplierIds);
        if (CollectionUtils.isNotEmpty(tempDistributorSupplies)) {
            distributorSupplyList.addAll(tempDistributorSupplies);
            List<String> subSupplierIds = tempDistributorSupplies.stream().map(o -> o.getThisDistributorAccountId())
                    .filter(o -> !supplierIds.contains(o)).collect(Collectors.toList());
            index--;
            List<DistributorSupply> subDistributorSupplies = null;
            if (index > 0 && CollectionUtils.isNotEmpty(subSupplierIds)) {
                subDistributorSupplies = getDistributorSuppliesByUp(tenantId, subSupplierIds, index);
            }
            if (CollectionUtils.isNotEmpty(subDistributorSupplies)) {
                distributorSupplyList.addAll(subDistributorSupplies);
            }
        }
        return distributorSupplyList;
    }

    @Override
    public List<IObjectData> getProductCollectionListByService(String tenantId, String apiName, List<String> serviceName) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(ProductCollectionObjConstants.Field.departmentId.getApiName(), serviceName)
                .build();
        return supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, apiName);

    }

    public List<IObjectData> getProductListByDealerIds(String tenantId, String apiName, List<String> dealerIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(CoveredProductObjConstants.Field.belongGroup.getApiName(), dealerIds)
                .build();
        return supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, apiName);

    }

    @Override
    public List<IObjectData> getDesignateProductListByRelatedIds(String tenantId, String apiName, List<String> relatedIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(CoveredProductObjConstants.Field.belongGroup.getApiName(), relatedIds)
                .build();
        return supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, apiName);
    }

    @Override
    public List<IObjectData> getDistributionListByDealer(String tenantId, String apiName, String dealerName) {

        SearchQuery searchQuery = SearchQuery.builder()
                .eq(ProductCollectionObjConstants.Field.dealerId.getApiName(), dealerName)
                .build();
        return supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, apiName);
    }

    public List<IObjectData> getProductListByDistributionIds(String tenantId, String apiName, List<String> distributionIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(CoveredProductObjConstants.Field.belongGroup.getApiName(), distributionIds)
                .build();
        return supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, apiName);
    }

    @Override
    public List<IObjectData> getProductCollectionByDealerIds(String tenantId, String apiName, List<String> dealerIds) {
        SearchQuery searchQuery = SearchQuery.builder()
                .in(ProductCollectionObjConstants.Field.dealerId.getApiName(), dealerIds)
                .build();
        return supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), searchQuery, apiName);
    }

    @Override
    public void checkDelDealerSupply(String tenantId, List<String> dataIds) {
        checkDelDealerUSpply(tenantId, dataIds, DealerSupplyObjConstants.DISPLAY_NAME);
    }

    private void checkDelDealerUSpply(String tenantId, List<String> dataIds, String disName1) {
        SearchQuery disSearchQuery = SearchQuery.builder()
                .in(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), dataIds)
                .limit(1)
                .build();
        QueryResult<IObjectData> queryDataListByQuery = supplyDao.getQueryDataListByQuery(User.systemUser(tenantId), disSearchQuery, DistributorSupplyObjConstants.API_NAME, false);
        if (queryDataListByQuery.getTotalNumber() != null && queryDataListByQuery.getTotalNumber() > 0) {
            throw new ValidateException(String.format("%s 正在被 %s 使用，无法作废。", disName1, DistributorSupplyObjConstants.DISPLAY_NAME)); //ignoreI18n
        }

        SearchQuery storeSearchQuery = SearchQuery.builder()
                .in(SupplyStoreObjConstants.Field.upSupplyId.getApiName(), dataIds)
                .limit(1)
                .build();
        QueryResult<IObjectData> storeSearchQueryResult = supplyDao.getQueryDataListByQuery(User.systemUser(tenantId), storeSearchQuery, SupplyStoreObjConstants.API_NAME, false);
        if (storeSearchQueryResult.getTotalNumber() != null && storeSearchQueryResult.getTotalNumber() > 0) {
            throw new ValidateException(String.format("%s 正在被 %s 使用，无法作废。", disName1, SupplyStoreObjConstants.DISPLAY_NAME)); //ignoreI18n
        }
    }

    @Override
    public void checkDelProductCollection(String tenantId, List<String> dataIds) {
        List<IObjectData> productCollectionList = supplyDao.getByIds(tenantId, ProductCollectionObjConstants.API_NAME, dataIds);
        productCollectionList.parallelStream().collect(Collectors.groupingBy(o -> o.getRecordType()))
                .forEach((k, v) -> {
                    if (ProductCollectionObjConstants.Value.isServerCenter(k)) {
                        //查询 经销商是否使用
                        SearchQuery searchQuery = SearchQuery.builder()
                                .in(ProductCollectionObjConstants.Field.departmentId.getApiName(),
                                        v.stream().map(o -> o.get(ProductCollectionObjConstants.Field.departmentId.getApiName(), String.class)).collect(Collectors.toList()))
                                .neq(BaseField.recordType.getApiName(), ProductCollectionObjConstants.Value.recordType_server_center.getValue())
                                .limit(1)
                                .build();
                        QueryResult<IObjectData> queryDataListByQuery = supplyDao.getQueryDataListByQuery(User.systemUser(tenantId), searchQuery, ProductCollectionObjConstants.API_NAME, false);
                        if (queryDataListByQuery.getTotalNumber() != null && queryDataListByQuery.getTotalNumber() > 0) {
                            throw new ValidateException(String.format("%s 有下游经销商，无法作废。", ProductCollectionObjConstants.DISPLAY_NAME)); //ignoreI18n
                        }
                    } else if (ProductCollectionObjConstants.Value.isDistributor(k) || ProductCollectionObjConstants.Value.isDealer(k)) {
                        //去空聚合 经营范围的 dealerId 和 distributorId
                        List<String> dealerIds = v.stream().map(o -> o.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class))
                                .filter(o -> StringUtils.isNotBlank(o)).collect(Collectors.toList());
                        List<String> distributorIds = v.stream().map(o -> o.get(ProductCollectionObjConstants.Field.distributorId.getApiName(), String.class))
                                .filter(o -> StringUtils.isNotBlank(o)).collect(Collectors.toList());
                        // all upSupplyIds accountId
                        List<String> upSupplyIds = Lists.newArrayList();
                        if (CollectionUtils.isNotEmpty(dealerIds)) {
                            upSupplyIds.addAll(dealerIds);
                        }
                        if (CollectionUtils.isNotEmpty(distributorIds)) {
                            upSupplyIds.addAll(distributorIds);
                        }
                        //check del 供货关系占用
                        checkDelDealerUSpply(tenantId, upSupplyIds, ProductCollectionObjConstants.DISPLAY_NAME);
                    }
                });
    }

    public QueryResult<IObjectData> getProductListByProductCollectionIds(SearchTemplateQuery query, String tenantId, List<String> ids) {
        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(ids)) {
            //查询产品组明细对象
            List<IObjectData> cpData = getDesignateProductListByRelatedIds(tenantId, CoveredProductObjConstants.API_NAME, ids);
            // 查询产品明细对象关联的产品数据
            Set<String> productIds = new HashSet<>();
            if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(cpData)) {
                cpData.forEach(o -> productIds.add(o.get("relation_product").toString()));
            }
            if (com.facishare.paas.appframework.common.util.CollectionUtils.empty(productIds)) {
                return new QueryResult<>();
            }
            Filter productFilter = new Filter();
            productFilter.setFieldName("_id");
            productFilter.setOperator(Operator.IN);
            productFilter.setFieldValues(Lists.newArrayList(productIds));
            SearchTemplateQuery productSearchQuery = new SearchTemplateQuery();
            productSearchQuery.getFilters().add(productFilter);
            productSearchQuery.setLimit(query.getLimit());
            productSearchQuery.setOffset(query.getOffset());
            QueryResult<IObjectData> productData = serviceFacade.findBySearchQuery(User.systemUser(tenantId), "ProductObj", productSearchQuery);
            return productData;
        } else {
            return new QueryResult<>();
        }
    }

    @Override
    public void addAvailableObjSyncTask(String tenantId, String dealerSupplyId) {
        syncAvailableRangeService.addAvailableObjSyncTask(tenantId, dealerSupplyId);
    }

    @Override
    public void syncAvailableObjByDealerSupplyObj(String tenantId, IObjectData dealerSupplyObj, String subAccountId) {
        fmcgThreadPoolExecutor.execute(() -> {
            syncAvailableRangeService.syncAvailableObjByDealerSupplyObj(tenantId, dealerSupplyObj, subAccountId);
        });
    }

    @Override
    public boolean checkForProductIdConflicts(Collection<String> argProductIds, Collection<String> productIds) {
        List list = new ArrayList();
        if (CollectionUtils.isNotEmpty(argProductIds)) {
            list.addAll(argProductIds);
        }
        if (CollectionUtils.isNotEmpty(productIds)) {
            list.addAll(productIds);
        }
        return checkForProductIdConflicts(list);
    }

    @Override
    public List<String> getProductIdConflicts(Collection<String> argProductIds, Collection<String> productIds) {
        List list = new ArrayList();
        if (CollectionUtils.isNotEmpty(argProductIds)) {
            list.addAll(argProductIds);
        }
        if (CollectionUtils.isNotEmpty(productIds)) {
            list.addAll(productIds);
        }
        return getProductIdConflicts(list);
    }
    @Override
    public BatchSaveDistributorOrShopSupplyArgs checkBatchSaveDistributorOrShopSupply(String tenantId, String userId, String upSupplyId, List<String> thisAccountIds, int flag, List<String> addSpecialProductIds) {
        return checkBatchSaveDistributorOrShopSupply(tenantId, userId, upSupplyId, thisAccountIds, flag, addSpecialProductIds,Boolean.FALSE);
    }

    @Override
    public void upSaveDealerSupply(String tenantId, DealerSupplyUpSave.Arg arg) {
        IObjectData iObjectData = arg.getIObjectData().toObjectData();
        String recordType = iObjectData.getRecordType();
        List<String> upDealerIds = FieldUtils.getStringList(iObjectData, AccountObjConstants.Field.upDealerIds.getApiName());
        List<String> upDistributorIds = FieldUtils.getStringList(iObjectData, AccountObjConstants.Field.upDistributorIds.getApiName());
        Object dep = iObjectData.getDataOwnDepartment();
        if (recordType.equals(AccountObjConstants.Value.default__c.name())){
            //门店类型的数据 处理 门店的配送商逻辑
            if (CollectionUtils.isEmpty(upDistributorIds)){
                // 判空 空则删除逻辑
                if (StringUtils.isNotBlank(iObjectData.getId())){
                    supplyDao.delSupplyStore(tenantId,Lists.newArrayList(iObjectData.getId()));
                }
            }else{
                // 查询已经存在的 供货关系
                List<String> addSupplierIds = upDistributorIds;
                List<String> delSupplierIds = null;
                List<Product> existProductList = null;
                List<String> existSpecialSupplierIds = null;
                List<Product> existSpecialProductList = null;
                Shop shop = supplyService.getShopObjById(tenantId, iObjectData.getId(), 1);
                if (shop != null) {
                    // 对已经存在的进行拆分， 去不在当前的
                    existProductList = shop.getProductList().stream().filter(o -> upDistributorIds.contains(o.getSupplierId()))
                            .collect(Collectors.toList());
                    existSpecialProductList = existProductList.stream().filter(o->o.isShopSpecial() || o.isDistributorSpecial()).collect(Collectors.toList());
                    List<String> existSupplierIds = ListUtils.EMPTY_LIST;
                    if (StringUtils.isNotBlank(iObjectData.getId())){
                        existSupplierIds =    supplyDao.getSupplyStoresByShopId(tenantId,iObjectData.getId()).stream().map(o->o.getSupplierId()).collect(Collectors.toList());
                    }
                    //存在的特例数据 的id
                    existSpecialSupplierIds = existProductList.stream().filter(o -> o.isShopSpecial() || o.isDistributorSpecial())
                            .map(o -> o.getSupplierId()).distinct().collect(Collectors.toList());
                    // 新增的
                    List<String> finalExistSupplierIds = existSupplierIds;
                    addSupplierIds = upDistributorIds.stream().filter(o -> !finalExistSupplierIds.contains(o)).collect(Collectors.toList());
                    //删除的
                    delSupplierIds = existSupplierIds.stream().filter(o -> !upDistributorIds.contains(o)).collect(Collectors.toList());
                }
                // 获取新增的产品 判断冲突的逻辑
                if (CollectionUtils.isNotEmpty(addSupplierIds)) {
                    List<Product> addProductList = new ArrayList<>();
                    for (String addSupplierId : addSupplierIds) {
                        if (existSpecialSupplierIds == null || !existSpecialSupplierIds.contains(addSupplierId)){
                            addProductList.addAll(
                                    supplyService.getSupplierById(tenantId, addSupplierId, 1).getProductList()
                            );
                        }
                    }
                    supplyService.checkForProductConflicts(addProductList,existProductList);
                    // 新增的与原来的特例 分别不冲突

                }
                if (!arg.isNoUpdate()){
                    //如果不冲突的话 处理增加删除
                    if (CollectionUtils.isNotEmpty(delSupplierIds)){
                        supplyDao.delSupplyStoreByAccountIdAndSupplierIds(tenantId,iObjectData.getId(),delSupplierIds);
                    }

                    //处理增加的逻辑
                    if (CollectionUtils.isNotEmpty(addSupplierIds)){
                        supplyDao.createSupplyStoreObj(tenantId,iObjectData.getOwner().get(0),iObjectData.getId(),supplyDao.getDealerSupplyBySuppliers(tenantId,
                                addSupplierIds),false);
                    }
                }
            }

        }else if (recordType.equals(AccountObjConstants.Value.distributor__c.name())){
            //配送商 类型的数据， 处理 上级配送商逻辑
            // 判空 空则删除逻辑
            if (CollectionUtils.isEmpty(upDealerIds)){
                if (StringUtils.isNotBlank(iObjectData.getId())){
                    supplyDao.delDistributorSupplyByDownIds(tenantId,Lists.newArrayList(iObjectData.getId()));
                }
            }else{
                // 查询已经存在的 供货关系
                List<String> addSupplierIds = upDistributorIds;
                List<String> delSupplierIds = null;
                List<Product> existProductList = null;
                List<Product> existSpecialProductList = null;
                List<String> existSpecialSupplierIds = null;
                Supplier supplierById = supplyService.getSupplierById(tenantId, iObjectData.getId(), 1);
                if (supplierById != null) {
                    // 对已经存在的进行拆分， 去不在当前的
                    existProductList = supplierById.getProductList().stream().filter(o -> upDistributorIds.contains(o.getSupplierId()))
                            .collect(Collectors.toList());
                    existSpecialProductList = supplierById.getProductList().stream().filter(o->o.isShopSpecial() || o.isDistributorSpecial()).collect(Collectors.toList());
                    List<String> existSupplierIds = ListUtils.EMPTY_LIST;
                    if (StringUtils.isNotBlank(iObjectData.getId())){
                        existSupplierIds = supplyDao.getDistributorSupplyByThisIds(tenantId,Lists.newArrayList(iObjectData.getId()))
                                .stream().map(o->o.getUpDealerAccountId()).collect(Collectors.toList());
                    }
                    //存在的特例数据 的id
                    existSpecialSupplierIds = existProductList.stream().filter(o -> o.isShopSpecial() || o.isDistributorSpecial())
                            .map(o -> o.getSupplierId()).distinct().collect(Collectors.toList());
                    // 新增的
                    List<String> finalExistSupplierIds = existSupplierIds;
                    addSupplierIds = upDistributorIds.stream().filter(o -> !finalExistSupplierIds.contains(o)).collect(Collectors.toList());
                    //删除的
                    delSupplierIds = existSupplierIds.stream().filter(o -> !upDistributorIds.contains(o)).collect(Collectors.toList());
                }
                // 获取新增的产品 判断冲突的逻辑
                if (CollectionUtils.isNotEmpty(addSupplierIds)) {
                    List<Product> addProductList = new ArrayList<>();
                    for (String addSupplierId : addSupplierIds) {
                        if (existSpecialSupplierIds == null || !existSpecialSupplierIds.contains(addSupplierId)){
                            addProductList.addAll(
                                    supplyService.getSupplierById(tenantId, addSupplierId, 1).getProductList()
                            );
                        }
                    }
                    supplyService.checkForProductConflicts(addProductList,existProductList);

                }
                if (!arg.isNoUpdate()){
                    //如果不冲突的话 处理增加删除
                    if (CollectionUtils.isNotEmpty(delSupplierIds)){
                        supplyDao.delDistributorSupplyByDownIdAndUpIds(tenantId,iObjectData.getId(),delSupplierIds);
                    }
                    //处理增加的逻辑
                    if (CollectionUtils.isNotEmpty(addSupplierIds)){
                        supplyDao.createDistributorSupplyObj(tenantId,iObjectData.getOwner().get(0),iObjectData.getId(),addSupplierIds,dep);
                    }
                }

            }

        }else{
            throw new ValidateException("不支持的业务类型"); //ignoreI18n
        }
    }

    @Override
    public Set<String> checkTransferDealerSupply(String tenantId, IObjectData dealerSupplyObj, String targetCustomerId,List<IObjectData> transferDataList) {
        //冲突产品的客户ids 返回值
        Set<String> conflictCustomerIds = Sets.newHashSet();
        //供货关系 字段取 经销商或者配送商
        String upDealerId = dealerSupplyObj.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class);
        //配送商类型
        String supplyType = dealerSupplyObj.get(DealerSupplyObjConstants.Field.supplyType.getApiName(), String.class);
        if (CollectionUtils.isEmpty(transferDataList)) {
            return conflictCustomerIds;
        }
        int transferType = transferDataList.get(0).getDescribeApiName().equals(DistributorSupplyObjConstants.API_NAME) ? 1 : 0;
        //获取产品经营范围
        int isDis = supplyType.equals("distributor__c") ? 1 : 0;
        Set<String> dealerProductIds = supplyService.getProductCollectionByDealerId(tenantId, upDealerId, isDis);
        //targetCustomer 判空
        if (StringUtils.isBlank(targetCustomerId)) {
            throw new ValidateException("目标客户不能为空"); //ignoreI18n
        }
        //获取 targetCustomer 的客户数据
        AccountObj accountObjById = supplyDao.getAccountObjById(tenantId, targetCustomerId);
        if (accountObjById == null) {
            throw new ValidateException("目标客户不存在"); //ignoreI18n
        }
        if (isDis == 1 && accountObjById.getRecordType().equals("dealer__c")) {
            throw new ValidateException("配送商供货关系不能转移给经销商"); //ignoreI18n
        }
        if (isDis == 0 && accountObjById.getRecordType().equals("distributor__c")) {
            throw new ValidateException("经销商供货关系不能转移给配送商"); //ignoreI18n
        }
        //目标的业务类型 和
        Set<String> targetCustomerProductIds = null;
        switch (accountObjById.getRecordType()) {
            case "dealer__c":
                //经销商
                targetCustomerProductIds = supplyService.getProductCollectionByDealerId(tenantId, targetCustomerId, 0);
                break;
            case "distributor__c":
                //配送商
                targetCustomerProductIds = supplyService.getProductCollectionByDealerId(tenantId, targetCustomerId, 1);
                break;
            default:
                throw new ValidateException("不支持的业务类型"); //ignoreI18n
        }
        if (CollectionUtils.isEmpty(targetCustomerProductIds)) {
            throw new ValidateException("目标客户没有经营范围"); //ignoreI18n
        }
        if (dealerProductIds.contains("ALL") || dealerProductIds.containsAll(targetCustomerProductIds)) {
            //要转移到的目标 经营范围 是当前经营范围的子集则跳过
            return conflictCustomerIds;
        } else {
            //如果targetCustomerId 包含全品类
            if (targetCustomerProductIds.remove("ALL")) {
                targetCustomerProductIds.clear();
                targetCustomerProductIds.addAll(supplyDao.getAllProductList(tenantId,null).getData().stream().map(o -> o.getId().toString()).collect(Collectors.toList()));
            }
            //取targetCustomerProductIds 多出来的产品id
            List<String> productIds = targetCustomerProductIds.stream().filter(o -> !dealerProductIds.contains(o)).collect(Collectors.toList());
            //多出来的产品不为空则需要处理，判断冲突
            if (CollectionUtils.isNotEmpty(productIds)) {
                //如果是分销商的话 需要 对所有转移的 分销商供货门店做判断
                if (isDis == 1) {
                    //分销商
                    checkConflictProductAddByTransferData(tenantId, transferDataList, transferType, upDealerId, productIds, conflictCustomerIds);
                    //门店
                    for (IObjectData objectData : transferDataList) {
                        //分销商
                        String thisDealerId = objectData.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class);
                        //查分销商所有的供货门店 做冲突判断
                        List<IObjectData> storeTransferDataList = supplyDao.getSupplyStoreByDealerIds(tenantId, Lists.newArrayList(thisDealerId));
                        checkConflictProductAddByTransferData(tenantId, storeTransferDataList, 0, thisDealerId, productIds, conflictCustomerIds);
                    }
                } else {
                    //门店
                    checkConflictProductAddByTransferData(tenantId, transferDataList, transferType, upDealerId, productIds, conflictCustomerIds);
                }
            }
        }
        return conflictCustomerIds;
    }

    private void checkConflictProductAddByTransferData(String tenantId, List<IObjectData> transferDataList, int transferType, String upDealerId, List<String> productIds, Set<String> conflictCustomerIds) {
        String transferApiName = transferType == 1 ? DistributorSupplyObjConstants.API_NAME : SupplyStoreObjConstants.API_NAME;
        // transferDataList 按照下级客户id 分组
        Map<String, List<IObjectData>> transferDataMap = transferDataList.stream().collect(Collectors.groupingBy(o -> o.get(transferType == 1 ? DistributorSupplyObjConstants.Field.thisDealerId.getApiName() : SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)));
        Set<String> customerIds = transferDataMap.keySet();
        //另外的供货关系
        List<IObjectData> existDealerSupplyList = supplyDao.getOtherSupply(tenantId, customerIds, transferApiName, upDealerId);
        if (CollectionUtils.isNotEmpty(existDealerSupplyList)) {
            //经销商供货产品cache 不包含特例的
            Map<String, Set<String>> existDealerSupplyProductMap = Maps.newConcurrentMap();
            //existDealerSupplyList 按照下级客户分组
            Map<String, List<IObjectData>> existDealerSupplyMap = existDealerSupplyList.stream().collect(Collectors.groupingBy(o -> o.get(transferType == 1 ? DistributorSupplyObjConstants.Field.thisDealerId.getApiName() : SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)));
            for (Map.Entry<String, List<IObjectData>> stringListEntry : existDealerSupplyMap.entrySet()) {
                String customerId = stringListEntry.getKey();
                List<IObjectData> existDealerSupplys = stringListEntry.getValue();
                Set<String> existProductIds = Sets.newHashSet();
                //按照特例分组
                existDealerSupplys.stream().collect(Collectors.groupingBy(o -> o.get(transferType == 1 ? DistributorSupplyObjConstants.Field.specialSupply.getApiName() : SupplyStoreObjConstants.Field.specialSupply.getApiName(), Boolean.class))).forEach((k, v) -> {
                    if (k) {
                        //特例
                        for (IObjectData objectData : v) {
                            existProductIds.addAll(supplyDao.getSpecialSupplyProductsByUpAndThis(tenantId, objectData.get(transferType == 1 ? DistributorSupplyObjConstants.Field.upDealerId.getApiName() : SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class), objectData.get(transferType == 1 ? DistributorSupplyObjConstants.Field.thisDealerId.getApiName() : SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)).stream().map(o -> o.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class)).collect(Collectors.toList()));
                        }
                    } else {
                        //非特例
                        Map<String, Set<String>> productListByCustomerIds = getProductListByCustomerIds(tenantId, v.stream().map(o -> o.get(transferType == 1 ? DistributorSupplyObjConstants.Field.upDealerId.getApiName() : SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class)).collect(Collectors.toList()), true);
                        productListByCustomerIds.values().forEach(pbcs -> existProductIds.addAll(pbcs));
                    }
                });
                //查转移的 供货关系的产品
                transferDataMap.get(customerId).stream().collect(Collectors.groupingBy(o -> o.get(transferType == 1 ? DistributorSupplyObjConstants.Field.specialSupply.getApiName() : SupplyStoreObjConstants.Field.specialSupply.getApiName(), Boolean.class))).forEach((k, v) -> {
                    if (k) {
                        //特例
                        List<String> specialProductIds = supplyDao.getSpecialSupplyProductsByUpAndThis(tenantId, v.get(0).get(transferType == 1 ? DistributorSupplyObjConstants.Field.upDealerId.getApiName() : SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class),
                                v.get(0).get(transferType == 1 ? DistributorSupplyObjConstants.Field.thisDealerId.getApiName() : SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)).stream().map(o -> o.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class)).collect(Collectors.toList());
                        //判断 specialProductIds 和existProductIds 冲突
                        List<String> checkProductIds = Lists.newArrayList();
                        //specialProductIds 和 productIds 交集
                        checkProductIds.addAll(CollectionUtils.intersection(specialProductIds, productIds));
                        checkProductIds.addAll(existProductIds);
                        if (checkForProductIdConflicts(checkProductIds)) {
                            conflictCustomerIds.add(customerId);
                        }
                    } else {
                        //判断 productListByCustomerIds 和existProductIds 冲突
                        List<String> checkProductIds = Lists.newArrayList();
                        checkProductIds.addAll(productIds);
                        checkProductIds.addAll(existProductIds);
                        if (checkForProductIdConflicts(checkProductIds)) {
                            conflictCustomerIds.add(customerId);
                        }
                    }
                });
            }
        }
    }

    @Override
    public List<Pair<IObjectData,List<String>>> transferDealerSupply(User user, IObjectData objectData, String targetCustomerId, List<IObjectData> transferObjectList, int transferType, Set<String> errorIds) {
        List<Pair<IObjectData,List<String>>> successAccountProductList =Lists.newArrayList();


        if (CollectionUtils.isEmpty(errorIds)) {
            errorIds = Sets.newHashSet();
        }
        try {
            IObjectData targetDealerSupply = null;
            try {
                redisUtils.lockTransfer(user.getTenantId(), objectData.getId(), transferType);
                //获取target 供货关系
                targetDealerSupply = supplyDao.getDealerSupplyBySuppliers(user.getTenantId(), Lists.newArrayList(targetCustomerId)).get(0);
                //查目标的经营范围
                Set<String> targetProductIds = getProductListByCustomerIds(user.getTenantId(), Lists.newArrayList(targetCustomerId), false).values().iterator().next();
                for (IObjectData iObjectData : transferObjectList) {
                    Pair<Boolean, List<String>> booleanListPair = doOne(user, objectData, targetCustomerId, transferType, errorIds, iObjectData, targetDealerSupply, targetProductIds);
                    if (booleanListPair.first){
                        successAccountProductList.add(Pair.build(iObjectData,booleanListPair.second));
                        continue;
                    }else{
                        //错误的客户id
                        errorIds.add(iObjectData.get(transferType == 1 ? DistributorSupplyObjConstants.Field.thisDealerId.getApiName() : SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class));
                    }
                }
            }catch (Exception e){
                log.error("转移供货关系失败 transferDealerSupply ", e);
            }finally {
                syncAvailableRangeService.syncAvailableObjByDealerSupplyObj(user.getTenantId(), objectData, null);
                if (targetDealerSupply != null){
                    syncAvailableRangeService.syncAvailableObjByDealerSupplyObj(user.getTenantId(), targetDealerSupply, null);
                }
            }
        } catch (Exception e) {
            log.error("同步失败 transferDealerSupply ", e);
        } finally {
            //重新同步供货关系
            redisUtils.unlockTransfer(user.getTenantId(), objectData.getId(), transferType);
        }
        //供货关系xxxx 批量转移/一键转移 到 xxxxx 失败 x 条，成功 x 条
        NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                //本次操作覆盖 1 条 【片区覆盖门店】，执行成功 1 条， 失败 0 条。
                .fullContent(MessageFormat.format("本次操作【供货关系】{0} 转移共{1}条【{2}】到{3} ,执行成功{5}条,失败{4}条。", //ignoreI18n
                        objectData.getName(),
                        transferObjectList.size(),
                        transferType == 1?"供货分销":"供货门店", //ignoreI18n
                        supplyDao.getAccountObjById(user.getTenantId(),targetCustomerId).getName(),
                        errorIds.size(),
                        transferObjectList.size() - errorIds.size()))
                .sourceId(new ObjectId().toHexString())
                .senderId("-10000")
                .type(CUSTOM_REMIND_RECORD_TYPE)
                .receiverIDs(Sets.newHashSet(Integer.valueOf(user.getUserId())))
                //【删除门店】执行完毕， 执行人:银鹭管理员
                .title("【供货关系转移】执行完毕， 执行人:" +  EmployeeUtil.getUserName(user.getUserId(),user.getTenantId())) //ignoreI18n
                .build();
        crmNotificationService.sendNewCrmNotification(user, newCrmNotification);
        return successAccountProductList;
    }

    @Override
    public void presetCustomButton(String tenantId) {
        Map<String,List<ButtonInfo>> apiNameAndButtonApiNameMap = Maps.newHashMap();
        //供货门店	供货门店	业务按钮	supply_store__c	列表页批量	无条件，默认显示	全部人员；支持自定义调整
        //供货分销商	供货分销商	业务按钮	supply_distributor__c	列表页批量	无条件，默认显示	全部人员；支持自定义调整
        //特例供货
        //
        //特例供货门店	业务按钮
        //special_supply_store__c
        //
        //列表页批量	无条件，默认显示	全部人员；支持自定义调整
        //特例供货分销商	业务按钮	special_distributor__c	列表页批量	无条件，默认显示	全部人员；支持自定义调整
        //供货门店	批量转移	batch_transfer_store__c	列表页批量	无条件，默认显示	默认crm管理员，可以自定义调整权限范围
        //一键转移	all_transfer_store__c	列表页批量	无条件，默认显示	默认crm管理员，可以自定义调整权限范围
        //供货分销商	批量转移	batch_transfer_distributor__c	列表页批量	无条件，默认显示	默认crm管理员，可以自定义调整权限范围
        //一键转移	all_transfer_distributor__c	列表页批量	无条件，默认显示	默认crm管理员，可以自定义调整权限范围
        apiNameAndButtonApiNameMap.put(SupplyStoreObjConstants.API_NAME,Lists.newArrayList(ButtonInfo.builder().apiName("supply_store__c").name("供货门店").roleCode("personnelrole").use_pages(Lists.newArrayList("list_batch")).build(), //ignoreI18n
                ButtonInfo.builder().apiName("batch_transfer_store__c").name("批量转移").roleCode("00000000000000000000000000000006").use_pages(Lists.newArrayList("list_batch")).build(), //ignoreI18n
                ButtonInfo.builder().apiName("all_transfer_store__c").name("一键转移").roleCode("00000000000000000000000000000006").use_pages(Lists.newArrayList("list_batch")).build())); //ignoreI18n
        apiNameAndButtonApiNameMap.put(DistributorSupplyObjConstants.API_NAME,Lists.newArrayList(ButtonInfo.builder().apiName("supply_distributor__c").name("供货分销商").roleCode("personnelrole").use_pages(Lists.newArrayList("list_batch")).build(), //ignoreI18n
                ButtonInfo.builder().apiName("batch_transfer_distributor__c").name("批量转移").roleCode("00000000000000000000000000000006").use_pages(Lists.newArrayList("list_batch")).build(), //ignoreI18n
                ButtonInfo.builder().apiName("all_transfer_distributor__c").name("一键转移").roleCode("00000000000000000000000000000006").use_pages(Lists.newArrayList("list_batch")).build())); //ignoreI18n
        apiNameAndButtonApiNameMap.put(SpecialSupplyObjConstants.API_NAME,Lists.newArrayList(ButtonInfo.builder().apiName("special_supply_store__c").name("特例供货门店").roleCode("personnelrole").use_pages(Lists.newArrayList("list_batch")).build(), //ignoreI18n
                ButtonInfo.builder().apiName("special_distributor__c").name("特例供货分销商").roleCode("personnelrole").use_pages(Lists.newArrayList("list_batch")).build())); //ignoreI18n
        Long now = System.currentTimeMillis();
        for (Map.Entry<String, List<ButtonInfo>> entry : apiNameAndButtonApiNameMap.entrySet()) {
            for (ButtonInfo buttonInfo : entry.getValue()) {
                List<Map> button = getButton(tenantId, entry.getKey(), buttonInfo.getApiName());
                if (CollectionUtils.isEmpty(button)) {
                    insertButton(tenantId, entry.getKey(), buttonInfo, now);
                    insertFunC(tenantId, entry.getKey(), buttonInfo.getApiName(), buttonInfo.getName());
                    insertFunCAccess(tenantId, entry.getKey(), buttonInfo.getApiName(),buttonInfo.getRoleCode());
                }
                else{
                    //测试先删除掉
                    delButton(tenantId,entry.getKey(),buttonInfo.getApiName());
                    delFunc(tenantId,entry.getKey(),buttonInfo.getApiName());
                    delFunCAccess(tenantId,entry.getKey(),buttonInfo.getApiName());
                    insertButton(tenantId, entry.getKey(), buttonInfo, now);
                    insertFunC(tenantId, entry.getKey(), buttonInfo.getApiName(), buttonInfo.getName());
                    insertFunCAccess(tenantId, entry.getKey(), buttonInfo.getApiName(),buttonInfo.getRoleCode());
                }
            }

        }
    }

    @Override
    public LayoutDocument hideButtons(LayoutDocument layoutDocument, List<ButtonDocument> buttons, boolean hideButton) {
        if (CollectionUtils.isNotEmpty(buttons)) {
            if (hideButton) {
                buttons.removeIf(iButton -> hideButtonApiNames.contains((String)iButton.get("api_name")));
            } else {
                buttons.stream().forEach(iButton -> {
                    if (hideButtonApiNames.contains((String)iButton.get("api_name"))) {
                        iButton.put("use_pages", Lists.newArrayList("list_batch"));
                    }
                });
            }
        }
        ILayout layout = layoutDocument.toLayout();
        if (CollectionUtils.isNotEmpty(layout.getButtons())) {
            if (hideButton) {
                layout.getButtons().removeIf(iButton -> hideButtonApiNames.contains(iButton.getName()));
            } else {
                layout.getButtons().stream().forEach(iButton -> {
                    if (hideButtonApiNames.contains(iButton.getName())) {
                        iButton.set("use_pages", Lists.newArrayList("list_batch"));
                    }
                });
            }
        }
        layoutDocument = LayoutDocument.of(layout);
        return layoutDocument;
    }

    private void insertButton(String tenantId, String objApiName, ButtonInfo buttonInfo, Long now) {
        String sqlTemplate = "insert into mt_udef_button (id, tenant_id, api_name, label, use_pages, describe_api_name, description, wheres, param_form, actions, button_type, jump_url, create_time, last_modified_time, created_by, last_modified_by, is_active, is_deleted, version, define_type, is_batch, url, display, redirect_type, lock_data_show_button) values  ('%s', '%s', '%s', '%s', '%s', '%s', '', '[]', '[]', null, 'common', '', %d, %d, '-10000', '-10000', true, false, 1, 'custom', null, null, null, '', false);";
        specialTableMapper.setTenantId(tenantId).insertBySql(String.format(sqlTemplate, IdGenerator.get(), tenantId, buttonInfo.getApiName(), buttonInfo.getName(), JSON.toJSONString(buttonInfo.getUse_pages()), objApiName, now, now));
    }

    private List<Map> getButton(String tenantId,String ObjectApiName,String buttonApiName) {
        String sql = "select * from mt_udef_button where tenant_id = '%s' and api_name ='%s' and describe_api_name = '%s'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(buttonApiName), SqlEscaper.pg_escape(ObjectApiName)));
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : list;
    }

    private Integer delButton(String tenantId, String ObjectApiName, String buttonApiName) {
        String sql = "delete from mt_udef_button where tenant_id = '%s' and api_name ='%s' and describe_api_name = '%s'";
        int count = (specialTableMapper.setTenantId(tenantId)).deleteBySql(String.format(sql, SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(buttonApiName), SqlEscaper.pg_escape(ObjectApiName)));
        return count;
    }

    /**
     * 增加功能权限code
     */
    private  void insertFunC(String tenantId, String ObjectApiName, String buttonApiName,String buttonName){
        //INSERT INTO public.fc_func (id, tenant_id, app_id, func_code, func_name, func_order, func_type, parent_code, level_code, menu_code, created_by, create_time, last_modified_by, last_modified_time, is_deleted, limit_scope) VALUES ('650e96bf11173914509eeec2', '82093', 'CRM', 'DistributorSupplyObj||button_LjwR8__c', '按钮名称(示例)', null, 1, '00000000000000000000000000000000', null, null, '1000', 1695454911782, '1000', 1695456579812, true, null);
        String sql = "INSERT INTO fc_func (id, tenant_id, app_id, func_code, func_name, func_order, func_type, parent_code, level_code, menu_code, created_by, create_time, last_modified_by, last_modified_time, is_deleted, limit_scope) VALUES ('%s', '%s', 'CRM', '%s', '%s', null, 1, '00000000000000000000000000000000', null, null, '-10000', %d, '-10000', %d, false, null);";
        String funcCode = ObjectApiName + "||" + buttonApiName;
        Long now = System.currentTimeMillis();
        specialTableMapper.setTenantId(tenantId).insertBySql(String.format(sql, IdGenerator.get(), tenantId, funcCode, buttonName, now, now));
    }
    private List<Map> getFunc(String tenantId, String ObjectApiName, String buttonApiName,String buttonName){
        String sql = "select * from fc_func where tenant_id = '%s' and func_code ='%s'";
        List<Map> list = (specialTableMapper.setTenantId(tenantId)).findBySql(String.format(sql, SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(ObjectApiName + "||" + buttonApiName)));
        return list;
    }
    private  Integer delFunc(String tenantId, String ObjectApiName, String buttonApiName){
        String sql = "delete from fc_func where tenant_id = '%s' and func_code ='%s'";
        int count = (specialTableMapper.setTenantId(tenantId)).deleteBySql(String.format(sql, SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(ObjectApiName + "||" + buttonApiName)));
        return count;
    }

    /**
     * 增加 所有人角色功能权限
     * INSERT INTO public.fc_func_access (id, tenant_id, app_id, role_code, func_code, created_by, create_time, last_modified_by, last_modified_time, is_deleted) VALUES ('650ea2e323a14c37b29ef537', '82093', 'CRM', 'personnelrole', 'DistributorSupplyObj||button_7zC22__c', '1000', 1695458019686, '1000', 1695458019686, false);
     */
    private void insertFunCAccess(String tenantId, String ObjectApiName, String buttonApiName,String roleCode){
        String sql = "INSERT INTO fc_func_access (id, tenant_id, app_id, role_code, func_code, created_by, create_time, last_modified_by, last_modified_time, is_deleted) VALUES ('%s', '%s', 'CRM', '%s', '%s', '-10000', %d, '-10000', %d, false);";
        String funcCode = ObjectApiName + "||" + buttonApiName;
        Long now = System.currentTimeMillis();
        specialTableMapper.setTenantId(tenantId).insertBySql(String.format(sql, IdGenerator.get(), tenantId,roleCode, funcCode, now, now));
    }
    private Integer delFunCAccess(String tenantId, String ObjectApiName, String buttonApiName){
        String sql = "delete from fc_func_access where tenant_id = '%s' and func_code ='%s'";
        int count = (specialTableMapper.setTenantId(tenantId)).deleteBySql(String.format(sql, SqlEscaper.pg_escape(tenantId), SqlEscaper.pg_escape(ObjectApiName + "||" + buttonApiName)));
        return count;
    }

    @Data
    @Builder
    public static class ButtonInfo{
        String apiName;
        String name;
        List<String> use_pages;
        String roleCode;
    }
    /**
     * 转移一个供货关系
     *
     * @param user
     * @param sourceDealerSupplyObj
     * @param targetCustomerId
     * @param transferType
     * @param errorIds
     * @param transferObj
     * @param targetDealerSupply
     * @param targetProductIds
     * @return
     */
    private Pair<Boolean,List<String>> doOne(User user, IObjectData sourceDealerSupplyObj, String targetCustomerId, int transferType, Set<String> errorIds, IObjectData transferObj, IObjectData targetDealerSupply, Set<String> targetProductIds) {
        //获取门店id
        String transferAccountId = transferObj.get(transferType == 1 ? DistributorSupplyObjConstants.Field.thisDealerId.getApiName() : SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class);
        if (errorIds.contains(transferAccountId)) {
            return Pair.of(true,null);
        }
        List<String> specialProductIds = null;
        try {
            //如果是特例获取特例供货数据
            List<IObjectData> specialSupplyList = null;
            if (transferObj.get(transferType == 1 ? DistributorSupplyObjConstants.Field.specialSupply.getApiName() : SupplyStoreObjConstants.Field.specialSupply.getApiName(), Boolean.class)) {
                specialSupplyList = supplyDao.getSpecialSupplyProductsByUpAndThis(user.getTenantId(), transferObj.get(transferType == 1 ? DistributorSupplyObjConstants.Field.upDealerId.getApiName() : SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class), transferAccountId);
            }
            //transferType == 1 根据iObjectData新建供货分销商，否则新建供货门店
            if (transferType == 1) {
                //新建供货分销商
                supplyDao.createDistributorSupplyObj(user.getTenantId(), user.getUserId() ,Lists.newArrayList(transferAccountId), targetDealerSupply, transferObj.get(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), Boolean.class));
                //如果是特例则新建特例供货数据
                if (CollectionUtils.isNotEmpty(specialSupplyList)) {
                    //特例产品ids
                    specialProductIds = specialSupplyList.stream().map(o -> o.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class)).filter(o->targetProductIds.contains("ALL") || targetProductIds.contains(o)).distinct().collect(Collectors.toList());
                    supplyDao.createSpecialSupplyObj(user.getTenantId(), user.getUserId(), Lists.newArrayList(transferAccountId), specialProductIds, targetDealerSupply.getId(), targetCustomerId, sourceDealerSupplyObj.getDataOwnDepartment());
                    supplyDao.batchInvalidAndDel(user, specialSupplyList);
                }
                supplyDao.batchInvalidAndDel(user, Lists.newArrayList(transferObj));
                //更新客户上字段
                supplyDao.updateDistributorUpSupplyFields(user.getTenantId(),transferAccountId);
                //查询不在目标经营范围targetProductIds且当前分销商下的特例供货
                List<IObjectData> subSpecialSupplyList = supplyDao.getSpecialSupplyByDealerIdAndNoProductIds(user.getTenantId(), transferAccountId,targetProductIds);
                if (CollectionUtils.isNotEmpty(subSpecialSupplyList)){
                    //删除特例供货
                    supplyDao.batchInvalidAndDel(user, subSpecialSupplyList);
                }
                //查询转移分销商的供货关系 并重新同步可售范围
                List<IObjectData> transferDealerSupplyList = supplyDao.getDealerSupplyBySuppliers(user.getTenantId(), Lists.newArrayList(transferAccountId));
                if (CollectionUtils.isNotEmpty(transferDealerSupplyList)){
                    for (IObjectData iObjectData : transferDealerSupplyList) {
                        syncAvailableRangeService.syncAvailableObjByDealerSupplyObj(user.getTenantId(), iObjectData, null);
                    }
                }
            } else {
                //新建供货门店
                supplyDao.createSupplyStoreObj(user.getTenantId(), user.getUserId(), Lists.newArrayList(transferAccountId), targetDealerSupply, transferObj.get(SupplyStoreObjConstants.Field.specialSupply.getApiName(), Boolean.class));
                //如果是特例则新建特例供货数据
                if (CollectionUtils.isNotEmpty(specialSupplyList)) {
                    //特例产品ids
                    specialProductIds = specialSupplyList.stream().map(o -> o.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class)).filter(o->targetProductIds.contains("ALL") || targetProductIds.contains(o)).distinct().collect(Collectors.toList());
                    supplyDao.createSpecialSupplyObj(user.getTenantId(), user.getUserId(), Lists.newArrayList(transferAccountId), specialProductIds, targetDealerSupply.getId(), targetCustomerId, sourceDealerSupplyObj.getDataOwnDepartment());
                    supplyDao.batchInvalidAndDel(user, specialSupplyList);
                }
                supplyDao.batchInvalidAndDel(user, Lists.newArrayList(transferObj));
                //更新客户上字段
                supplyDao.updateShopUpSupplyFields(user.getTenantId(),transferAccountId);
            }

        }catch (Exception e){
            //打印日志 包含所有参数和错误
            log.info("转移供货关系失败，参数：{}，错误：{}",transferObj,e.getMessage());
            return Pair.of(false,specialProductIds);
        }
        return Pair.of(true,specialProductIds);
    }
    public BatchSaveDistributorOrShopSupplyArgs checkBatchSaveDistributorOrShopSupply(String tenantId, String userId, String upSupplyId, List<String> thisAccountIds, int flag, List<String> addSpecialProductIds,boolean isCoverSpecial){
        BatchSaveError resultError = new BatchSaveError();
        resultError.setExistList(Lists.newArrayList());
        resultError.setExistProductMap(Maps.newHashMap());
        resultError.setConflictMap(Maps.newHashMap());
        boolean isDistributorSupply = (flag == 1);
        String mApiName = isDistributorSupply ? DistributorSupplyObjConstants.API_NAME : SupplyStoreObjConstants.API_NAME;
        String fUpSupplyId = isDistributorSupply ? DistributorSupplyObjConstants.Field.upSupplyId.getApiName() : SupplyStoreObjConstants.Field.upSupplyId.getApiName();
        String fThisDealerId = isDistributorSupply ? DistributorSupplyObjConstants.Field.thisDealerId.getApiName() : SupplyStoreObjConstants.Field.thisDealerId.getApiName();
        String fUpDealerId = isDistributorSupply ? DistributorSupplyObjConstants.Field.upDealerId.getApiName() : SupplyStoreObjConstants.Field.upDealerId.getApiName();
        String fIsSpecial = isDistributorSupply ? DistributorSupplyObjConstants.Field.specialSupply.getApiName() : SupplyStoreObjConstants.Field.specialSupply.getApiName();

        //查询供货关系
        List<IObjectData> dealerSupplyData = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(upSupplyId), DealerSupplyObjConstants.API_NAME);
        String dealerId = dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();
        Object ownDepartment = dealerSupplyData.get(0).getDataOwnDepartment();
        //查询参数里所有的源供货数据
        List<IObjectData> sourceArgsStoreList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                        .eq(fUpSupplyId, upSupplyId).in(fThisDealerId, thisAccountIds).build()
                , mApiName, Lists.newArrayList(fThisDealerId, fIsSpecial));
        //当前参数 能取到存在的特例供货数据
        Map<String, Set<IObjectData>> sourceArgsSpecialMap = Maps.newConcurrentMap();
        boolean isSpecial = CollectionUtils.isNotEmpty(addSpecialProductIds);
        if (!isSpecial) {
            Set<String> sourceArgsStoreIds = sourceArgsStoreList.stream().map(o -> o.get(fThisDealerId, String.class)).collect(Collectors.toSet());
            //不是特例供货的 已存在的不管
            thisAccountIds.removeIf(o -> sourceArgsStoreIds.contains(o));
        } else {
            //是特例供货操作的需要把原来的特例供货数据查出来
            Set<String> existSpecialStoreIds = sourceArgsStoreList.stream().filter(o -> o.get(fIsSpecial, Boolean.class)).map(o -> o.get(fThisDealerId, String.class)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(existSpecialStoreIds)) {

                //查询 已经存在的特例关系
                List<IObjectData> tempDatas = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                                .eq(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName(), upSupplyId)
                                .eq(SpecialSupplyObjConstants.Field.dealerId.getApiName(), dealerId)
                                .in(SpecialSupplyObjConstants.Field.shopId.getApiName(), existSpecialStoreIds).build(),
                        SpecialSupplyObjConstants.API_NAME,
                        Lists.newArrayList(SpecialSupplyObjConstants.Field.shopId.getApiName(), SpecialSupplyObjConstants.Field.productId.getApiName()));
                sourceArgsSpecialMap = tempDatas.stream().collect(Collectors.groupingBy(o -> o.get(SpecialSupplyObjConstants.Field.shopId.getApiName(), String.class), Collectors.mapping(v -> v, Collectors.toSet())));
                //如果有交集 则报错 没必要
//                if (MapUtils.isNotEmpty(sourceArgsSpecialMap) && !isCoverSpecial){
//                    for (Map.Entry<String, Set<IObjectData>> shopIdAndSpecialProducts : sourceArgsSpecialMap.entrySet()) {
//                        List<String> existConflictProductList = shopIdAndSpecialProducts.getValue().stream().map(o -> o.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class))
//                                .collect(Collectors.toList());
//                        existConflictProductList.retainAll(addSpecialProductIds);
//                        if (CollectionUtils.isNotEmpty(existConflictProductList)){
//                            resultError.getExistList().add(shopIdAndSpecialProducts.getKey());
//                            resultError.getExistProductMap().put(shopIdAndSpecialProducts.getKey(),existConflictProductList);
//                            thisAccountIds.removeIf(o->o.equals(shopIdAndSpecialProducts.getKey()));
//                        }
//                    }
//                }
            }
        }
        if (CollectionUtils.isEmpty(thisAccountIds)) {
            return  BatchSaveDistributorOrShopSupplyArgs.builder()
                    .tenantId(tenantId)
                    .userId(userId)
                    .addShopIds(null)
                    .sourceArgsSpecialMap(sourceArgsSpecialMap)
                    .addSpecialProductIds(addSpecialProductIds)
                    .isCoverSpecial(isCoverSpecial)
                    .upSupplyId(upSupplyId)
                    .dealerId(dealerId)
                    .ownDepartment(ownDepartment)
                    .isDistributorSupply(isDistributorSupply)
                    .isSpecial(isSpecial)
                    .dealerSupplyData(dealerSupplyData)
                    .resultError(resultError)
                    .build();
        }

        //参数的产品范围
        Set<String> argProductIds = null;
        if (isSpecial) {
            argProductIds = Sets.newHashSet(addSpecialProductIds);
        } else {
            Supplier argSupplier = supplyService.getSupplierById(tenantId, dealerId, 1);
            argProductIds = argSupplier.getProductList().stream().map(o -> o.getProductId()).collect(Collectors.toSet());
        }

        //查询除了参数里的所有的供货门店
        List<IObjectData> sourceOtherArgsStoreList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                        .neq(fUpSupplyId, upSupplyId)
                        .in(fThisDealerId, thisAccountIds).build(), mApiName,
                Lists.newArrayList(fUpDealerId, fIsSpecial, fThisDealerId));
        List<String> addShopIds = Lists.newArrayList();
        //addShop 已经存在的供货关系
        Map<String, Set<String>> soureceOtherSupplyMap = MapUtils.EMPTY_MAP;
        //其他供货商的 产品列表
        Map<String, Set<String>> otherProductIdsMap = new HashMap<>();
        MapUtils.synchronizedMap(otherProductIdsMap);
        //如果存在其他的供货关系 则需要 取 其他的供货关系数据
        if (CollectionUtils.isNotEmpty(sourceOtherArgsStoreList)) {
            //查询门店现有的供货商
            soureceOtherSupplyMap = sourceOtherArgsStoreList.stream().collect(
                    Collectors.groupingBy(k -> k.get(fThisDealerId, String.class),
                            Collectors.mapping(v -> {
                                        if (v.get(fIsSpecial, Boolean.class)) {
                                            //特例
                                            return v.get(fThisDealerId, String.class) + "_" + v.get(fUpDealerId, String.class);
                                        } else {
                                            //非特例
                                            return v.get(fUpDealerId, String.class);
                                        }

                                    }
                                    , Collectors.toSet())));
            if (MapUtils.isNotEmpty(soureceOtherSupplyMap)) {
                Set<String> existOtherDealers = new HashSet<>();
                for (Set<String> value : soureceOtherSupplyMap.values()) {
                    //移除当前的 是其他的
                    value.removeIf(o -> dealerId.equals(o));
                    existOtherDealers.addAll(value);
                }
                existOtherDealers.parallelStream().forEach(o -> {
                    String[] subIds = o.split("_");
                    if (subIds.length == 1) {
                        //不是特例
                        otherProductIdsMap.put(o, supplyService.getSupplierById(tenantId, o, 1)
                                .getProductList().stream()
                                .map(s -> s.getProductId()).collect(Collectors.toSet()));
                    } else {
                        //特例的
                        List<IObjectData> iObjectDataList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                                        .eq(SpecialSupplyObjConstants.Field.shopId.getApiName(), subIds[0])
                                        .eq(SpecialSupplyObjConstants.Field.dealerId.getApiName(), subIds[1])
                                        .build(), SpecialSupplyObjConstants.API_NAME,
                                Lists.newArrayList(SpecialSupplyObjConstants.Field.productId.getApiName()));
                        if (CollectionUtils.isNotEmpty(iObjectDataList)) {
                            otherProductIdsMap.put(o, iObjectDataList.stream().map(i -> i.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class)).collect(Collectors.toSet()));
                        } else {
                            otherProductIdsMap.put(o, SetUtils.EMPTY_SET);
                        }

                    }
                });
            }
        }
        //判断冲突
        for (String shopId : thisAccountIds) {
            List<String> productIds = Lists.newArrayList();
            Set<String> otherSupplyUpAccountIds = soureceOtherSupplyMap.get(shopId);
            if (CollectionUtils.isNotEmpty(otherSupplyUpAccountIds)) {
                for (String o : otherSupplyUpAccountIds) {
                    Set<String> otherProductIds = otherProductIdsMap.get(o);
                    if (CollectionUtils.isNotEmpty(otherProductIds)) {
                        productIds.addAll(otherProductIds);
                    }
                }
                if (isSpecial) {
                    List<String> productIdConflicts = supplyService.getProductIdConflicts(argProductIds, productIds);
                    if (CollectionUtils.isNotEmpty(productIdConflicts)) {
                        productIdConflicts.retainAll(argProductIds);
                        resultError.getConflictMap().put(shopId, productIdConflicts);
                    } else {
                        addShopIds.add(shopId);
                    }
                } else {
                    //判断冲突
                    if (supplyService.checkForProductIdConflicts(argProductIds, productIds)) {
                        //                        冲突
                        resultError.getConflictMap().put(shopId, ListUtils.EMPTY_LIST);
                    } else {
                        addShopIds.add(shopId);
                    }
                }
            } else {
                addShopIds.add(shopId);
            }
        }
        return  BatchSaveDistributorOrShopSupplyArgs.builder()
                .tenantId(tenantId)
                .userId(userId)
                .addShopIds(addShopIds)
                .sourceArgsSpecialMap(sourceArgsSpecialMap)
                .addSpecialProductIds(addSpecialProductIds)
                .isCoverSpecial(isCoverSpecial)
                .upSupplyId(upSupplyId)
                .dealerId(dealerId)
                .ownDepartment(ownDepartment)
                .isDistributorSupply(isDistributorSupply)
                .isSpecial(isSpecial)
                .dealerSupplyData(dealerSupplyData)
                .resultError(resultError)
                .build();
    }
    /**
     * @param thisAccountIds
     * @param flag
     * @param batchSaveDistributorOrShopSupplyArgs
     * @return
     */
    public void batchSaveDistributorOrShopSupply(BatchSaveDistributorOrShopSupplyArgs batchSaveDistributorOrShopSupplyArgs){

        if (CollectionUtils.isNotEmpty(batchSaveDistributorOrShopSupplyArgs.getAddShopIds())) {
            log.info("add shopIds {}", batchSaveDistributorOrShopSupplyArgs.getAddShopIds());
            //删除移除的特例
            List<IObjectData> removeData = Lists.newArrayList();
            CompletableFuture<Boolean> task1 = CompletableFuture.supplyAsync(() -> {
                if (MapUtils.isNotEmpty(batchSaveDistributorOrShopSupplyArgs.getSourceArgsSpecialMap())) {
                    for (String addShopId : batchSaveDistributorOrShopSupplyArgs.getAddShopIds()) {
                        Set<IObjectData> tempDataSet = batchSaveDistributorOrShopSupplyArgs.getSourceArgsSpecialMap().get(addShopId);
                        if (CollectionUtils.isNotEmpty(tempDataSet)) {
                            //删除
                            Set<IObjectData> tempDelData = tempDataSet.stream().filter(o ->
                                    !batchSaveDistributorOrShopSupplyArgs.getAddSpecialProductIds().contains(o.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class))).collect(Collectors.toSet());
                            if (batchSaveDistributorOrShopSupplyArgs.isCoverSpecial()) {
                                removeData.addAll(tempDelData);
                            } else {
                                //暂时不删除 冲突的 先返回前端 不处理了
//                                resultError.getExistList().add(addShopId);
//                                resultError.getExistProductMap().put(addShopId, tempDelData.stream().map(o -> o.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class)).collect(Collectors.toList()));

                            }
                            Set<String> tempProductIds = tempDataSet.stream().map(o -> o.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class)).collect(Collectors.toSet());
                            //新增
                            List<String> tempAddSpecialProductIds = batchSaveDistributorOrShopSupplyArgs.getAddSpecialProductIds().stream().filter(o -> !tempProductIds.contains(o)).collect(Collectors.toList());
                            //创建特例
                            supplyDao.createSpecialSupplyObj(batchSaveDistributorOrShopSupplyArgs.getTenantId(), batchSaveDistributorOrShopSupplyArgs.getUserId(), Lists.newArrayList(addShopId), tempAddSpecialProductIds, batchSaveDistributorOrShopSupplyArgs.getUpSupplyId(), batchSaveDistributorOrShopSupplyArgs.getDealerId(), batchSaveDistributorOrShopSupplyArgs.getOwnDepartment());
                        } else {
                            //创建特例
                            supplyDao.createSpecialSupplyObj(batchSaveDistributorOrShopSupplyArgs.getTenantId(), batchSaveDistributorOrShopSupplyArgs.getUserId(), Lists.newArrayList(addShopId), batchSaveDistributorOrShopSupplyArgs.getAddSpecialProductIds(), batchSaveDistributorOrShopSupplyArgs.getUpSupplyId(), batchSaveDistributorOrShopSupplyArgs.getDealerId(), batchSaveDistributorOrShopSupplyArgs.getOwnDepartment());
                        }

                    }

                } else {
                    //创建特例
                    supplyDao.createSpecialSupplyObj(batchSaveDistributorOrShopSupplyArgs.getTenantId(), batchSaveDistributorOrShopSupplyArgs.getUserId(), batchSaveDistributorOrShopSupplyArgs.getAddShopIds(), batchSaveDistributorOrShopSupplyArgs.getAddSpecialProductIds(), batchSaveDistributorOrShopSupplyArgs.getUpSupplyId(), batchSaveDistributorOrShopSupplyArgs.getDealerId(), batchSaveDistributorOrShopSupplyArgs.getOwnDepartment());
                }
                if (CollectionUtils.isNotEmpty(removeData)) {
                    supplyDao.batchInvalidAndDel(User.systemUser(batchSaveDistributorOrShopSupplyArgs.getTenantId()), removeData);
                }
                return Boolean.TRUE;
            }, fmcgSyncTaskExecutor);
            CompletableFuture<Boolean> task2 = CompletableFuture.supplyAsync(() -> {
                //处理 供货门店 和供货分销
                if (batchSaveDistributorOrShopSupplyArgs.isDistributorSupply()) {
                    if (batchSaveDistributorOrShopSupplyArgs.isSpecial()) {
                        supplyDao.createDistributorSupplyObj(batchSaveDistributorOrShopSupplyArgs.getTenantId(), batchSaveDistributorOrShopSupplyArgs.getUserId(), batchSaveDistributorOrShopSupplyArgs.getAddShopIds(), batchSaveDistributorOrShopSupplyArgs.getDealerSupplyData().get(0), true);
                    } else {
                        supplyDao.createDistributorSupplyObjNoUpdate(batchSaveDistributorOrShopSupplyArgs.getTenantId(), batchSaveDistributorOrShopSupplyArgs.getUserId(), batchSaveDistributorOrShopSupplyArgs.getAddShopIds(), batchSaveDistributorOrShopSupplyArgs.getDealerSupplyData().get(0), false);
                    }
                } else {
                    if (batchSaveDistributorOrShopSupplyArgs.isSpecial()) {
                        supplyDao.createSupplyStoreObj(batchSaveDistributorOrShopSupplyArgs.getTenantId(), batchSaveDistributorOrShopSupplyArgs.getUserId(), batchSaveDistributorOrShopSupplyArgs.getAddShopIds(), batchSaveDistributorOrShopSupplyArgs.getDealerSupplyData().get(0), true);
                    } else {
                        supplyDao.createSupplyStoreObjNoUpdate(batchSaveDistributorOrShopSupplyArgs.getTenantId(), batchSaveDistributorOrShopSupplyArgs.getUserId(), batchSaveDistributorOrShopSupplyArgs.getAddShopIds(), batchSaveDistributorOrShopSupplyArgs.getDealerSupplyData().get(0), false);
                    }

                }
                return Boolean.TRUE;
            },fmcgSyncTaskExecutor);
            CompletableFuture.allOf(task2, task1).join();
            fmcgThreadPoolExecutor.execute(
                    () -> {
                        if (batchSaveDistributorOrShopSupplyArgs.isDistributorSupply()) {
                            //同步当前的经营范围
                            supplyDao.getProductCollectionObjListByAccountIds(batchSaveDistributorOrShopSupplyArgs.getTenantId(), batchSaveDistributorOrShopSupplyArgs.getAddShopIds(), false);
                        }
                        batchSaveDistributorOrShopSupplyArgs.getAddShopIds().stream().forEach(shopId -> {
                            if (batchSaveDistributorOrShopSupplyArgs.isDistributorSupply()) {
                                //重新设置客户的上级供货商字段
                                supplyDao.updateDistributorUpSupplyFields(batchSaveDistributorOrShopSupplyArgs.getTenantId(), shopId);
                            } else {
                                supplyDao.updateShopUpSupplyFields(batchSaveDistributorOrShopSupplyArgs.getTenantId(), shopId);
                            }
                            //重新同步可售范围
                            syncAvailableRangeService.syncAvailableObjByDealerSupplyObj(batchSaveDistributorOrShopSupplyArgs.getTenantId(), batchSaveDistributorOrShopSupplyArgs.getDealerSupplyData().get(0), shopId);

                        });
                    }

            );
        }
    }

    private boolean checkForProductIdConflicts(List<String> list) {
        Set<String> tempSet = Sets.newHashSet();
        boolean isAll = false;
        for (String id : list) {
            if (!tempSet.add(id)) {
                return Boolean.TRUE;
            }
            if (id.equals("ALL")) {
                isAll = Boolean.TRUE;
            }
        }
        if (isAll && tempSet.size() > 1) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
    private List<String> getProductIdConflicts(List<String> list) {
        List<String> result = Lists.newArrayList();
        Set<String> tempSet = Sets.newHashSet();
        boolean isAll = false;
        for (String id : list) {
            if (!tempSet.add(id)) {
                result.add(id);
            }
            if (id.equals("ALL")) {
                isAll = Boolean.TRUE;
            }
        }
        if (isAll && tempSet.size() > 1) {
            List<String> collect = list.stream().filter(o -> !"ALL".equals(o)).distinct().collect(Collectors.toList());
            return CollectionUtils.isEmpty(collect) ? Lists.newArrayList("ALL") : collect;
        }
        return result;
    }


}
