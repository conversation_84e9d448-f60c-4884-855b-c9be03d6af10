package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SupplyStoreObjConstants;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import org.aspectj.lang.annotation.Before;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-09-20 14:33
 **/
@Deprecated
public class DealerSupplyTransferAction  extends AbstractStandardAction<DealerSupplyTransferAction.Arg, DealerSupplyTransferAction.Result> {
    private SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);

    @Override
    protected void before(Arg arg) {
        arg.setTransferType(arg.getObjectData().getDescribeApiName().equals(SupplyStoreObjConstants.API_NAME) ? 0 : 1);
        super.before(arg);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected Result doAct(Arg arg) {
//        supplyService.trandferDealerSupply(arg.getObjectData(),arg.getTargetCustomerId(),arg.getTransferType());
        return new Result();
    }

    @Data
    public static class Arg {
        /**
         * 供货门店/配送商数据
         */
        private IObjectData objectData;

        /**
         * 转移类型 0:转移供货门店 1:转移配送商
         */
        private int transferType;

        /**
         * 转移的经销商id
         */
        private String targetCustomerId;

    }

    @Data
    public static class Result {

    }
}
