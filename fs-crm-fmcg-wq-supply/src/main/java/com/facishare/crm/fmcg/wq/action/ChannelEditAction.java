package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.service.ChannelService;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Objects;

public class ChannelEditAction extends StandardEditAction {

    ChannelService channelService = SpringUtil.getContext().getBean(ChannelService.class);

    RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);

    @Override
    protected void before(Arg arg) {
        String tenantId = actionContext.getTenantId();
        channelService.checkIsRoot(tenantId,arg);
        Integer level = channelService.calculateLevel(tenantId,arg,false);
        if(Objects.nonNull(level)) {
            arg.getObjectData().put("channel_level", level.toString());
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        redisUtils.clearChannelData(actionContext.getTenantId());
        return super.after(arg, result);
    }
}
