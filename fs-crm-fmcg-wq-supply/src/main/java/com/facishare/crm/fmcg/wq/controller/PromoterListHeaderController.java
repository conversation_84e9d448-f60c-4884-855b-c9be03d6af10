package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-09-23 17:06
 **/
public class PromoterListHeaderController extends StandardListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        //布局变动
        ILayout layout = after.getLayout().toLayout();
        try {
            List<IComponent> components = layout.getComponents();
            String reviewStatusType = objectDescribeExt.getFieldDescribe("review_status").getType();
            if ("select_one".equals(reviewStatusType)) {
                for (IComponent component : components) {
                    if (component instanceof IFormComponent) {
                        IFormComponent formComponent = (IFormComponent) component;
                        formComponent.getFieldSections().forEach(fieldSection -> {
                            fieldSection.getFields().forEach(field -> {
                                if ("review_status".equals(field.getFieldName())) {
                                    field.setRenderType("select_one");
                                }
                            });
                        });
                    } else if (component instanceof ITableComponent) {
                        ITableComponent tableComponent = (ITableComponent) component;
                        tableComponent.getFieldSections().forEach(fieldSection -> {
                            fieldSection.getFields().forEach(field -> {
                                if ("review_status".equals(field.getFieldName())) {
                                    field.setRenderType("select_one");
                                }
                            });
                        });
                    } else if (component instanceof ITabsComponent) {
                        ITabsComponent tabsComponent = (ITabsComponent) component;
                        tabsComponent.getFieldSections().forEach(fieldSection -> {
                            fieldSection.getFields().forEach(field -> {
                                if ("review_status".equals(field.getFieldName())) {
                                    field.setRenderType("select_one");
                                }
                            });
                        });
                    } else if (component instanceof ITopInfoComponent) {
                        ITopInfoComponent topInfoComponent = (ITopInfoComponent) component;
                        topInfoComponent.getFieldSections().forEach(fieldSection -> {
                            if ("review_status".equals(fieldSection.getFieldName())) {
                                fieldSection.setRenderType("select_one");
                            }
                        });
                    }
                }
                after.setLayout(LayoutDocument.of(layout));
            }
        } catch (MetadataServiceException e) {
            throw new ValidateException(e.getMessage());
        }
        return after;
    }
}
