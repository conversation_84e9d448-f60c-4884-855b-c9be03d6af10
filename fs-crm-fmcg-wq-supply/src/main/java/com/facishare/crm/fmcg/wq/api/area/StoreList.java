package com.facishare.crm.fmcg.wq.api.area;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface StoreList {
    @Data
    @ToString
    class Arg implements Serializable {

        private int offset;

        private int limit;

        @JSONField(name = "data_id")
        @JsonProperty("data_id")
        @SerializedName("data_id")
        private List<String> dataId;

        @JSONField(name = "field_projection")
        @JsonProperty("field_projection")
        @SerializedName("field_projection")
        private List<String> fieldProjection;

    }

    @Data
    @ToString
    class Result implements Serializable {

        @SerializedName("info")
        @JSONField(name = "info")
        @JsonProperty("info")
        private List<ObjectDataDocument> infos = Lists.newArrayList();

        private int total ;
    }

}
