package com.facishare.crm.fmcg.wq.service.impl;

import com.facishare.crm.fmcg.wq.service.ProductFilterService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.ProductAllCategoriesModel;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
@Service
public class ProductFilterServiceImpl implements ProductFilterService {

    private final Set<Operator> CATEGORY_NEED_TRANSFER_OPERATORS;
    private final Set<Operator> CATEGORY_NEED_TRANSFER_2_IN_OPERATORS;

    public ProductFilterServiceImpl() {
        this.CATEGORY_NEED_TRANSFER_OPERATORS = Sets.newHashSet(new Operator[]{Operator.EQ, Operator.IN, Operator.IS, Operator.N, Operator.NIN, Operator.ISN, Operator.HASANYOF, Operator.NHASANYOF});
        this.CATEGORY_NEED_TRANSFER_2_IN_OPERATORS = Sets.newHashSet(new Operator[]{Operator.EQ, Operator.IN, Operator.IS, Operator.HASANYOF});
    }

    @Autowired
    ServiceFacade serviceFacade;

    @Override
    public void handleCategoryFilters(String tenantId, String userId, List<IFilter> filters) {
        Iterator var4 = filters.iterator();

        while(true) {
            IFilter filter;
            do {
                do {
                    if (!var4.hasNext()) {
                        return;
                    }

                    filter = (IFilter)var4.next();
                } while(!"category".equals(filter.getFieldName()));
            } while(!this.CATEGORY_NEED_TRANSFER_OPERATORS.contains(filter.getOperator()));

            List<String> fieldValues = filter.getFieldValues();
            Set<String> categoryValues = Sets.newHashSet();
            if (CollectionUtils.notEmpty(fieldValues)) {
                List<ProductAllCategoriesModel.CategoryPojo> productAllCategories = this.getProductAllCategories(tenantId, userId);
                Iterator var9 = fieldValues.iterator();

                while(var9.hasNext()) {
                    String fieldValue = (String)var9.next();
                    if (!categoryValues.contains(fieldValue)) {
                        Set<String> categoryChildrenCategoryCodes = this.getChildrenCategoryContainSelf(productAllCategories, fieldValue);
                        categoryValues.addAll(categoryChildrenCategoryCodes);
                    }
                }
            }

            if (this.CATEGORY_NEED_TRANSFER_2_IN_OPERATORS.contains(filter.getOperator())) {
                filter.setOperator(Operator.IN);
            } else {
                filter.setOperator(Operator.NIN);
            }

            filter.setFieldValues(Lists.newArrayList(categoryValues));
        }
    }

    public Set<String> getChildrenCategoryContainSelf(List<ProductAllCategoriesModel.CategoryPojo> productAllCategories, String categoryCode) {
        return (Set)(!CollectionUtils.empty(productAllCategories) && categoryCode != null ? this.getCategoryChildrenCategoryCodesContainSelf(categoryCode, productAllCategories) : Sets.newHashSetWithExpectedSize(0));
    }

    public Set<String> getCategoryChildrenCategoryCodesContainSelf(String categoryCode, List<ProductAllCategoriesModel.CategoryPojo> productAllCategories) {
        if (categoryCode == null) {
            return Sets.newHashSetWithExpectedSize(0);
        } else {
            Set<String> categoryCodes = Sets.newHashSet(new String[]{categoryCode});
            ProductAllCategoriesModel.CategoryPojo targetCategory = this.getTargetCategory(productAllCategories, categoryCode);
            this.fillAllChildrenCategoryCodes(targetCategory, categoryCodes);
            return categoryCodes;
        }
    }

    public void fillAllChildrenCategoryCodes(ProductAllCategoriesModel.CategoryPojo category, Set<String> categoryCodes) {
        if (category != null) {
            categoryCodes.add(category.getCategoryCode());
            List<ProductAllCategoriesModel.CategoryPojo> children = category.getChildren();
            if (CollectionUtils.notEmpty(children)) {
                Iterator var4 = children.iterator();

                while(var4.hasNext()) {
                    ProductAllCategoriesModel.CategoryPojo child = (ProductAllCategoriesModel.CategoryPojo)var4.next();
                    this.fillAllChildrenCategoryCodes(child, categoryCodes);
                }
            }

        }
    }

    private ProductAllCategoriesModel.CategoryPojo getTargetCategory(List<ProductAllCategoriesModel.CategoryPojo> categories, String targetCategoryCode) {
        if (CollectionUtils.empty(categories)) {
            return null;
        } else {
            Iterator var3 = categories.iterator();

            ProductAllCategoriesModel.CategoryPojo targetCategory;
            do {
                if (!var3.hasNext()) {
                    return null;
                }

                ProductAllCategoriesModel.CategoryPojo category = (ProductAllCategoriesModel.CategoryPojo)var3.next();
                if (Objects.equals(targetCategoryCode, category.getCategoryCode())) {
                    return category;
                }

                targetCategory = this.getTargetCategory(category.getChildren(), targetCategoryCode);
            } while(targetCategory == null);

            return targetCategory;
        }
    }

    public List<ProductAllCategoriesModel.CategoryPojo> getProductAllCategories(String tenantId, String userId) {
        User user = new User(tenantId, userId);
        QueryResult<IObjectData> productCategoryObj = this.findAllCategory(user);
        List<ProductAllCategoriesModel.CategoryPojo> categoryObjectList = (List)productCategoryObj.getData().stream().map((o) -> {
            return new ProductAllCategoriesModel.CategoryPojo(o.getId(), (String)o.get("code", String.class), o.getName(), (String)o.get("order_field", String.class), (String)o.get("pid", String.class), Boolean.FALSE, Lists.newArrayList());
        }).collect(Collectors.toList());
        ProductAllCategoriesModel.CategoryPojo categoryObject = new ProductAllCategoriesModel.CategoryPojo();
        categoryObject.setChildren((List)categoryObjectList.stream().filter((o) -> {
            return o.getParentId() == null;
        }).collect(Collectors.toList()));
        this.dealCurrentLevel(categoryObject, categoryObjectList);
        return categoryObject.getChildren();
    }

    private void dealCurrentLevel(ProductAllCategoriesModel.CategoryPojo categoryObject, List<ProductAllCategoriesModel.CategoryPojo> categoryObjectList) {
        List<ProductAllCategoriesModel.CategoryPojo> children = categoryObject.getChildren();
        ProductAllCategoriesModel.CategoryPojo child;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(children)) {
            for(Iterator var4 = children.iterator(); var4.hasNext(); this.dealCurrentLevel(child, categoryObjectList)) {
                child = (ProductAllCategoriesModel.CategoryPojo)var4.next();
                ProductAllCategoriesModel.CategoryPojo finalChild = child;
                List<ProductAllCategoriesModel.CategoryPojo> collect = (List)categoryObjectList.stream().filter((o) -> {
                    return finalChild.getCategoryId().equals(o.getParentId());
                }).sorted(Comparator.comparingInt((o) -> {
                    return Integer.valueOf(o.getCategoryOrder());
                })).collect(Collectors.toList());
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(collect)) {
                    child.setChildren(collect);
                }
            }
        }

    }

    private QueryResult<IObjectData> findAllCategory(User user) {
        SearchTemplateQuery searchQuery = new SearchTemplateQuery();
        searchQuery.setLimit(1000);
        QueryResult<IObjectData> productCategoryObj = serviceFacade.findBySearchQuery(user, "ProductCategoryObj", searchQuery);

        for(int i = 1; productCategoryObj.getTotalNumber() > 1000 && i < productCategoryObj.getTotalNumber() / 1000 + 2; ++i) {
            searchQuery.setOffset(1000 * i);
            QueryResult<IObjectData> todoResult = serviceFacade.findBySearchQuery(user, "ProductCategoryObj", searchQuery);
            if (CollectionUtils.notEmpty(todoResult.getData())) {
                productCategoryObj.getData().addAll(todoResult.getData());
            }
        }

        return productCategoryObj;
    }
}
