package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SupplyChangeFields;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.service.RouteService;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SupplyChangeFlowCompletedAction extends StandardFlowCompletedAction {

    private RouteService routeService = SpringUtil.getContext().getBean(RouteService.class);
    SupplyApproService supplyApproService = SpringUtil.getContext().getBean(SupplyApproService.class);
    BaseDao basedao = SpringUtil.getContext().getBean(BaseDao.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        if (String.valueOf(ApprovalFlowTriggerType.CREATE.getTriggerTypeCode()).equals(arg.getTriggerType()) && arg.isPass()) {
            //新建审批通过
            supplyApproService.redoAct(actionContext.getUser(), this.data);
        }
        return after;
    }
}
