package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.service.ChannelService;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;

public class ChannelBulkInvalidAction extends StandardBulkInvalidAction {

    RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);

    ChannelService channelService = SpringUtil.getContext().getBean(ChannelService.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String tenantId = actionContext.getTenantId();
        channelService.checkIsValid(tenantId, arg.getDataIds());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        redisUtils.clearChannelData(actionContext.getTenantId());
        return super.after(arg, result);
    }
}
