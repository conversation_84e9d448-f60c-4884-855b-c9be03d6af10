package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.service.RouteService;
import com.facishare.paas.appframework.core.predef.action.StandardFlowStartCallbackAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2023 - 04 - 06  16:28
 **/
@Slf4j
public class VisitRouteFlowStartCallbackAction extends StandardFlowStartCallbackAction {

    private RouteService routeService = SpringUtil.getContext().getBean(RouteService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("VisitRouteFlowStartCallbackAction after is start!");
        IObjectData newObjectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getDataId(), objectDescribe.getApiName());
        String tenantId = newObjectData.getTenantId();
        String routeId = newObjectData.getId();
        ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(this.objectData).getLifeStatus();
        log.info("VisitRouteFlowStartCallbackAction tenantId:{},objectLifeStatus:{}", tenantId, objectLifeStatus.getCode());
        if (String.valueOf(ApprovalFlowTriggerType.CREATE.getTriggerTypeCode()).equals(arg.getTriggerType())
                || String.valueOf(ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode()).equals(arg.getTriggerType())
                || String.valueOf(ApprovalFlowTriggerType.INVALID.getTriggerTypeCode()).equals(arg.getTriggerType())
        ) {
            if (ApprovalFlowStartResult.of(arg.getCode()).equals(ApprovalFlowStartResult.SUCCESS)) {
                routeService.routeFlowStart(tenantId, routeId, objectLifeStatus.getCode());
            } else {
                if (String.valueOf(ApprovalFlowTriggerType.CREATE.getTriggerTypeCode()).equals(arg.getTriggerType())) {
                    routeService.addNoNeedTriggerApprovalFlow(tenantId, routeId);
                } else if (String.valueOf(ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode()).equals(arg.getTriggerType())) {
                    routeService.editNoNeedTriggerApprovalFlow(tenantId, routeId);
                } else if (String.valueOf(ApprovalFlowTriggerType.INVALID.getTriggerTypeCode()).equals(arg.getTriggerType())) {
                    routeService.invalidNoNeedTriggerApprovalFlow(tenantId, routeId);
                }
            }
        }
        return super.after(arg, result);
    }
}
