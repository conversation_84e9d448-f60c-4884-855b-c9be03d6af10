package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.FmcgPreActionArgs;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 促销员同意
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterPromoterAgreeAction extends FmcgAbstractStandardAction<PromoterPromoterAgreeAction.Arg, PromoterPromoterAgreeAction.Result> {
    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.PROMOTER_AGREE;
    }
    @Override
    protected PromoterPromoterAgreeAction.Result doAct(PromoterPromoterAgreeAction.Arg arg) {
        if (PromoterFields.ReviewStatus.agree.getValue().equals(objectData.get(PromoterFields.REVIEW_STATUS,String.class))) {
            throw new ValidateException("促销员已审核通过"); //ignoreI18n
        }
        //查看 描述 如果 促销员 类型角色选项不存在 则报错
        String promoterType = objectData.get(PromoterFields.PROMOTER_TYPE,String.class);
        if (StringUtils.isEmpty(promoterType)) {
            throw new ValidateException("促销员类型不存在"); //ignoreI18n
        }
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(PromoterFields.PROMOTER_TYPE);
        if(Objects.nonNull(fieldDescribe.get("options"))) {
            List<Map<String, String>> optionArray = (List<Map<String, String>>) fieldDescribe.get("options");
            if (!optionArray.stream().anyMatch(map -> map.get("value").equals(promoterType))){
                throw new ValidateException("促销员类型不存在,对应的互联角色已删除"); //ignoreI18n
            }
        }
        //修改促销员审核状态
        objectData.set(PromoterFields.REVIEW_STATUS,PromoterFields.ReviewStatus.agree.getValue());
        //在职
        objectData.set(PromoterFields.IO_STATUS,"1");
        supplyDao.update(User.systemUser(actionContext.getTenantId()), objectData);
        recodeLog();
        return new Result();
    }

    @Data
    public static class Arg extends FmcgPreActionArgs {
    }

    @Data
    public static class Result {

    }
}
