package com.facishare.crm.fmcg.wq.service;

import com.facishare.crm.fmcg.wq.api.area.DealerSupplyUpSave;
import com.facishare.crm.fmcg.wq.model.*;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.common.Pair;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @program: fs-appserver-checkins-v2
 * @description: 供货关系接口
 * @author: zhangsm
 * @create: 2021-04-20 19:27
 **/
@Component
public interface SupplyService {
//    /**
//     * 获取上级范围
//     *
//     * @param tenantId
//     * @param supplierId
//     * @return
//     */
//    @Deprecated
//    UpBusinessScope getUpBusinessScope(String tenantId, String supplierId);


//    /**
//     * DistrbutorSupply
//     * 判断是否循环引用
//     */
//
//    @Deprecated
//    List<IObjectData> distributorSupplyCircularReference(String tenantId, ObjectDataDocument distributorSupply);

//    /**
//     * 判断是否重复
//     *
//     * @param distributorSupply
//     */
//    @Deprecated
//    List<IObjectData> distributorSupplyRepetition(String tenantId, ObjectDataDocument distributorSupply);

    /**
     * 获取下级配送商对象
     *
     * @param tenantId
     * @param dataList
     * @return
     */
    List<IObjectData> getDistributorSupplyListByIds(String tenantId, List<String> dataList);

//    /**
//     * 根据ID查询供货关系设置表
//     *
//     * @param tenantId
//     * @param idList
//     * @return 供货关系
//     */
//    @Deprecated
//    List<IObjectData> distributorSupplyByIds(String tenantId, List<String> idList);
//
//    /**
//     * 判断是否存在 对应经营产品范围，
//     *
//     * @param tenantId
//     * @param dealerSupply
//     * @return
//     */
//    List<IObjectData> dealerSupplyExistProductCollection(String tenantId, ObjectDataDocument dealerSupply);

//    /**
//     * * 检查 每个范围之间的依赖关系
//     * *
//     * * 1.上级范围是否有重合  需要创建SpecialSupplyObj 对象处理
//     * * 2.依赖关系是否完整，一直能取到业务处 需要创建 DistributorSupplyObj 对象处理
//     * * 3.检查所选产品是否是在 父的范围下 需要修改 父ProductCollectionObj范围
//     * * 4.检查删除的范围是否是被下级或者 供货门店等使用
//     *
//     * @param tenantId
//     * @param objectData
//     * @param details
//     * @param dbMasterData
//     * @param dbDetailList
//     */
//    void checkProductCollectionDependencies(String tenantId,
//                                            ObjectDataDocument objectData, List<ObjectDataDocument> details,
//                                            IObjectData dbMasterData, List<IObjectData> dbDetailList);


//    /**
//     * 根据供货关系设置查询关联的供货门店
//     *
//     * @param tenantId
//     * @param idList   供货关系
//     * @return
//     */
//    List<IObjectData> getRelatedSupplyStore(String tenantId, List<String> idList);

    /**
     * 验证 门店配送商 是否与 本身的范围冲突
     * 验证 所加的数据实在 对应的范围内的
     *
     * @param tenantId
     * @param thisAccountObj
     * @param addSupplierIds
     */
    void checkAddSupplyStoreObj(String tenantId, AccountObj thisAccountObj, List<String> addSupplierIds);

    /**
     * 验证特殊供货关系对象重复
     *
     * @param tenantId
     * @param specialSupply
     */
    void checkSpecialSupplyRepetition(String tenantId, IObjectData specialSupply);

    /**
     * 增加 下级配送商对象的验证
     *
     * @param tenantId
     * @param thisAccountObj
     * @param addSupplierIds
     */
    void checkAddDistributorSupply(String tenantId, AccountObj thisAccountObj, List<String> addSupplierIds);

    /**
     * 验证产品明细子对象添加 是否会对后续的数据有影响产生冲突
     *
     * @param tenantId
     * @param productCollectionId
     * @param productIds
     */
    void checkCoveredProductsAdd(String tenantId, List<String> productIds, String productCollectionId);

    /**
     * 删除 产品明细子对象以后
     *
     * @param tenantId
     * @param productCollectionId
     * @param productIds
     */
    void afterCoveredStoreDel(String tenantId, List<String> productIds, String productCollectionId);

    /**
     * 店铺id 获取店铺数据
     *
     * @param shopId
     * @return
     */
    Shop getShopObjById(String tenantId, String shopId);

    /**
     * 店铺id 获取店铺数据
     *
     * @param shopId
     * @return
     */
    Shop getShopObjById(String tenantId, String shopId, int upQueryLevel);

//    /**
//     * 通过供货对象 获取当前经销商/分销商 对当前客户的供货产品列表
//     * @param tenantId
//     * @param objectData 供货门店/供货分销商
//     * @return
//     */
//    Set<String> getProductListByObj(String tenantId, IObjectData objectData);

    /**
     * 根据门店ID获取门店下经营的产品列表
     *
     * @param tenantId
     * @param shopId
     * @return
     */
    Set<String> getProductListByStoreId(String tenantId, String shopId);

    /**
     * 根据分销邮差商ID获取它经营的产品列表
     *
     * @param tenantId
     * @param distributionId
     * @return
     */
    Set<String> getProductListByDistributionId(String tenantId, String distributionId);

    /**
     * 根据配送商/经销商ID获取经营范围
     *
     * @param tenantId
     * @param dealerId
     * @param type     0-经销商 1-配送商
     * @return
     */
    Set<String> getProductCollectionByDealerId(String tenantId, String dealerId, int type);

    /**
     * 供应商id 获取供应商
     * 获取供货商的经营范围
     *
     * @param supplierId
     * @param upQueryLevel 99 是所有的节点 产品都查
     * @return
     */
    Supplier getSupplierById(String tenantId, String supplierId, int upQueryLevel);

    /**
     * 判断冲突的逻辑，如果冲突了 会抛runtime异常
     *
     * @param productList
     */

    void checkForProductConflicts(List<Product> productList);

    /**
     * 判断冲突的逻辑，如果冲突了 会抛runtime异常
     *
     * @param productList
     */

    void checkForProductConflicts(Collection<Product> productList, Collection<Product> otherProductList);

    /**
     * 通过 所有的下级 分销商id 获取所有的上下级关系
     *
     * @param tenantId
     * @param supplierIds
     * @return
     */
    List<DistributorSupply> getRelationListByDownIds(String tenantId, List<String> supplierIds);

    /**
     * 通过 所有的上级 分销商 或者经销商id 获取所有的上下级关系
     *
     * @param tenantId
     * @param supplierIds
     * @return
     */
    List<DistributorSupply> getRelationListByUpIds(String tenantId, List<String> supplierIds);

    /**
     * 根据服务处查询经营范围
     *
     * @param serviceName
     * @return
     */
    List<IObjectData> getProductCollectionListByService(String tenantId, String apiName, List<String> serviceName);


    /**
     * 通过关联关系 获取指定产品列表
     *
     * @param tenantId
     * @param apiName
     * @param relatedIds
     * @return
     */
    List<IObjectData> getDesignateProductListByRelatedIds(String tenantId, String apiName, List<String> relatedIds);

    /**
     * 根据经销商查询配送商
     *
     * @param dealerName
     * @return
     */
    List<IObjectData> getDistributionListByDealer(String tenantId, String apiName, String dealerName);


    /**
     * 根据经销商ID查询经营范围
     *
     * @param tenantId
     * @param apiName
     * @param dealerIds
     * @return
     */
    List<IObjectData> getProductCollectionByDealerIds(String tenantId, String apiName, List<String> dealerIds);

    /**
     * 验证 对应的供货关系是否能被删除
     *
     * @param tenantId
     * @param dataIds
     */
    void checkDelDealerSupply(String tenantId, List<String> dataIds);

    /**
     * 验证 对应的 经营范围是否能被删除
     *
     * @param tenantId
     * @param dataIds
     */
    void checkDelProductCollection(String tenantId, List<String> dataIds);

    /**
     * 异步 同步逻辑 时间间隔 10 分钟
     *
     * @param tenantId
     * @param dealerSupplyId
     */
    void addAvailableObjSyncTask(String tenantId, String dealerSupplyId);

    /**
     * 总的同步逻辑
     *
     * @param tenantId
     * @param dealerSupplyObj
     */
    void syncAvailableObjByDealerSupplyObj(String tenantId, IObjectData dealerSupplyObj, String subAccountId);

    boolean checkForProductIdConflicts(Collection<String> argProductIds, Collection<String> productIds);
    List<String> getProductIdConflicts(Collection<String> argProductIds, Collection<String> productIds);

    /**
     * 批量保存 供货门店 或者 供货分销商
     *
     * @param upSupplyId           上级供货关系id
     * @param thisAccountIds       当前客户ids
     * @param flag                 0 this 是客户 添加供货门店 1 this是 分销商 添加的是供货分销
     * @param addSpecialProductIds 特例产品 空的时候 不是特例
     * @param tenantId             企业账号
     * @param userId               用户id
     */
//    BatchSaveError batchSaveDistributorOrShopSupply(String tenantId, String userId, String upSupplyId, List<String> thisAccountIds, int flag, List<String> addSpecialProductIds);
     BatchSaveDistributorOrShopSupplyArgs checkBatchSaveDistributorOrShopSupply(String tenantId, String userId, String upSupplyId, List<String> thisAccountIds, int flag, List<String> addSpecialProductIds);
    void batchSaveDistributorOrShopSupply(BatchSaveDistributorOrShopSupplyArgs batchSaveDistributorOrShopSupplyArgs);
    void upSaveDealerSupply(String tenantId, DealerSupplyUpSave.Arg arg);

    /**
     * 转移至另一个经销商 验证
     * @param tenantId
     * @param dealerSupplyObj
     * @param targetCustomerId
     * @param transferDataList 转移的数据
     */
    Set<String> checkTransferDealerSupply(String tenantId, IObjectData dealerSupplyObj, String targetCustomerId, List<IObjectData> transferDataList);

    /**
     * 转移至另一个经销商
     *
     * @param tenantId
     * @param objectData
     * @param targetCustomerId
     * @param transferObjectList
     * @param transferType
     * @param errorIds
     */
    List<Pair<IObjectData,List<String>>> transferDealerSupply(User tenantId, IObjectData objectData, String targetCustomerId, List<IObjectData> transferObjectList, int transferType, Set<String> errorIds);


    /**
     * 预置自定义按钮
     */
    void presetCustomButton(String tenantId);


    /**
     * 去除供货关系里的按钮
     */
    LayoutDocument hideButtons(LayoutDocument layoutDocument, List<ButtonDocument> buttons, boolean hideButton);
    @Data
    class BatchSaveError {
     private List<String> existList;
     private Map<String,List<String>> existProductMap;
     private Map<String,List<String>> conflictMap;
    }

    @Data
    class UpBusinessScope {
        /**
         * 是否是全部产品
         */
        private boolean isAllProduct;
        /**
         * 产品idList
         */
        private List<String> productIds;
    }
}
