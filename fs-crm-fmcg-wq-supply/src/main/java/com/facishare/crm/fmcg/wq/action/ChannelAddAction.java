package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.service.ChannelService;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import static com.facishare.paas.metadata.exception.ErrorCode.FS_PAAS_MDS_DUPLICATE_DATA_EXCEPTION;

@Slf4j
public class ChannelAddAction extends StandardAddAction {

    /**
     * 数据同步标识
     */
    private final static String DATA_SYNC = "origin_source";

    ChannelService channelService = SpringUtil.getContext().getBean(ChannelService.class);

    RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);

    @Override
    protected void before(Arg arg) {
        String tenantId = actionContext.getTenantId();
        Integer level = channelService.calculateLevel(tenantId,arg,true);
        if(Objects.nonNull(level)) {
            arg.getObjectData().put("channel_level", level.toString());
        }
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        boolean isDataSync = false;
        if(Objects.nonNull(arg.getObjectData()) && Objects.nonNull(arg.getObjectData().get(DATA_SYNC))){
            try {
                return super.doAct(arg);
            }catch (MetaDataBusinessException e){
                if(FS_PAAS_MDS_DUPLICATE_DATA_EXCEPTION.getCode() == e.getErrorCode()) {
                    log.info("channel add from data_sync tenantId:{},dataId:{}", actionContext.getTenantId(), arg.getObjectData().getId());
                    isDataSync = true;
                }else{
                    throw e;
                }
            }
        }
        if(isDataSync){
            ChannelResult ret = new ChannelResult();
            ret.setObjectData(arg.getObjectData());
            ret.setIsDuplicate(true);
            ret.setIsDataDup(true);
            return ret;
        }
        return super.doAct(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if(result instanceof ChannelResult) {
            ChannelResult ret = (ChannelResult) result;
            if (ret.isDataDup) {
                ret.setIsDataDup(null);
                return ret;
            }
        }
        redisUtils.clearChannelData(actionContext.getTenantId());
        return super.after(arg, result);
    }

    @Data
    static class ChannelResult extends Result{
        public Boolean isDataDup;
    }

}
