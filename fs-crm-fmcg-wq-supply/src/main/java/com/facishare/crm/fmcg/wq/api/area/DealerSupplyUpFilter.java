package com.facishare.crm.fmcg.wq.api.area;


import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.appserver.checkins.api.model.BaseResult;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DealerSupplyUpFilter {
    @Data
    @ToString
    class Arg implements Serializable {

        /**
         * 当只传了客户id的时候，返回 经销商id
         */
        @NotEmpty(message = "数据为空，请填写数据")
        @JSONField(name = "object_data")
        @JsonProperty("object_data")
        @SerializedName("object_data")
        private ObjectDataDocument iObjectData;

    }
    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {
        /**
         * 列表
         */
        private List<ObjectDataDocument> infos = Lists.newArrayList();
        /**
         * 总数
         */
        private int total ;
        /**
         * 0  是 显示typeId 的模式， 1是不显示typeId的模式
         */
        private int style;
    }
}
