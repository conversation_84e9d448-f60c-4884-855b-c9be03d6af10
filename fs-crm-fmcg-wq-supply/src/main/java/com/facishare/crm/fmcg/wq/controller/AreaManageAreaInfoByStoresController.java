package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.api.area.AreaInfoByStores;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.service.OrganizationJMLService;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class AreaManageAreaInfoByStoresController extends PreDefineController<AreaInfoByStores.Arg, AreaInfoByStores.Result> {

    private static final OrganizationJMLService organizationJMLService = SpringUtil.getContext().getBean(OrganizationJMLService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected AreaInfoByStores.Result doService(AreaInfoByStores.Arg arg) {
        AreaInfoByStores.Result result = new AreaInfoByStores.Result();
        if(CollectionUtils.isEmpty(arg.getStoreIds())){
            return result;
        }
        try {
            return yinLuAreaInfo(arg);
        }catch (Exception e){
            log.error("AreaInfoByStores error arg:{},",arg.getStoreIds(),e);
            result.setErrorCode(-1);
            result.setErrorMsg("服务异常"); //ignoreI18n
        }
        return result;
    }

    private AreaInfoByStores.Result yinLuAreaInfo(AreaInfoByStores.Arg arg) {
        AreaInfoByStores.Result result = new AreaInfoByStores.Result();
        List<List<String>> subAccountList = Lists.partition(arg.getStoreIds(), 200);
        String ea = serviceFacade.getEAByEI(controllerContext.getTenantId());
        int type = 0;
        if(GrayRelease.isAllow("checkin-server-v2","areaManage",ea)){
            type = 1;
        }

        if(GrayRelease.isAllow("checkin-server-v2","isYLArea",ea)){
            type = 2;
        }
        for (List<String> storeIds : subAccountList) {
            Map<String, Set<String>> areaMap = getAreaData(storeIds);
            if(MapUtils.isNotEmpty(areaMap)) {
                List<String> areaIds = Lists.newArrayList();
                for (Set<String> value : areaMap.values()) {
                    areaIds.addAll(value);
                }
                List<IObjectData> areaInfo = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), areaIds, AreaManageConstants.AREA_MANAGE_OBJ);
                IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), AreaManageConstants.AREA_MANAGE_OBJ);
                serviceFacade.fillUserInfo(describe,areaInfo,controllerContext.getUser());
                serviceFacade.fillDepartmentInfo(describe,areaInfo,controllerContext.getUser());
                Map<String, IObjectData> areaDataMap = areaInfo.stream().collect(Collectors.toMap(o -> o.getId(), v -> v, (k1, k2) -> k2));

                Map<String,List<AreaInfoByStores.AreaInfoVO>> areaInfos = Maps.newHashMap();
                for (Map.Entry<String, Set<String>> entry : areaMap.entrySet()) {
                    for (String area : entry.getValue()) {
                        IObjectData data = areaDataMap.get(area);
                        if(Objects.nonNull(data)){
                            areaInfos.computeIfAbsent(entry.getKey(), temp -> Lists.newArrayList()).add(convertData(data,type));
                        }
                    }
                }

                result.setAreaInfos(areaInfos);
            }
            //新店审核 可选经销商list   取得是指定字段的 归属部门的下级部门
            if(type== 1) {
                List<IObjectData> storeInfo = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), arg.getStoreIds(), CommonConstants.ACCOUNT_OBJ);
                List<String> dealerAccountIds = Lists.newArrayList();
                for (IObjectData value : storeInfo) {
                    if(Objects.nonNull(value.get("field_cMb9p__c"))) {
                        dealerAccountIds.add(value.get("field_cMb9p__c").toString());
                    }
                }
                if(CollectionUtils.isNotEmpty(dealerAccountIds)) {
                    Map<String, Integer> accountIdToDepart= queryDealerOwnDepart(dealerAccountIds);
                    List<DepartmentDto> departmentDtoList = organizationJMLService.queryDepartments(Integer.valueOf(controllerContext.getTenantId()), Lists.newArrayList(accountIdToDepart.values()));
                    Map<Integer, Integer> dealerIdToParentId = departmentDtoList.stream().collect(Collectors.toMap(DepartmentDto::getDepartmentId, DepartmentDto::parentId));
                    //线所id
                    List<Integer> parentDepartmentIds = departmentDtoList.stream().map(DepartmentDto::parentId).collect(Collectors.toList());
                    //线索对应经销商
                    List<DepartmentDto> childDepts = organizationJMLService.batchQueryLowDepartment(Integer.valueOf(controllerContext.getTenantId()), parentDepartmentIds);
                    Map<Integer, List<DepartmentDto>> parentToChildDepts = childDepts.stream().collect(Collectors.groupingBy(DepartmentDto::parentId));
                    result.setDealerList(Maps.newHashMap());
                    for (IObjectData data : storeInfo) {
                        if(Objects.nonNull(data.get("field_cMb9p__c"))) {
                            Integer dealer = accountIdToDepart.get(data.get("field_cMb9p__c").toString());
                            if(Objects.nonNull(dealer)){
                                if (parentToChildDepts.containsKey(dealerIdToParentId.get(dealer))) {
                                    List<JSONObject> list = Lists.newArrayList();
                                    for (DepartmentDto departmentDto : parentToChildDepts.get(dealerIdToParentId.get(dealer))) {
                                        JSONObject object = new JSONObject();
                                        object.put("id", departmentDto.getDepartmentId());
                                        object.put("name", departmentDto.getName());
                                        list.add(object);
                                    }
                                    result.getDealerList().put(data.getId(),list);
                                }
                            }
                        }
                    }
                }
            }
        }
        return result;
    }
    private Map<String, Integer> queryDealerOwnDepart(List<String> dealerAccountIds) {
        Map<String, Integer> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(dealerAccountIds)) {
            return result;
        }
        List<IObjectData> dealsers = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(),dealerAccountIds, CommonConstants.ACCOUNT_OBJ);

        for (IObjectData object : dealsers) {
            result.put(object.getId(), Integer.valueOf(object.getDataOwnDepartment().get(0)));
        }

        return result;
    }
    private AreaInfoByStores.AreaInfoVO convertData(IObjectData data,int type) {
        AreaInfoByStores.AreaInfoVO infoVO = new AreaInfoByStores.AreaInfoVO();
        infoVO.setAreaId(data.getId());
        infoVO.setAreaName(data.getName());
        if(Objects.nonNull(data.get(AreaManageConstants.AREA_OWNER))) {
            Map<String,Object> owner = data.get("area_owner__c__r",Map.class,Maps.newHashMap());
            infoVO.setAreaOwner(owner.getOrDefault("id","").toString());
            infoVO.setAreaOwnerName(owner.getOrDefault("name","").toString());
        }
        if(type == 1 ){
            if(Objects.nonNull(data.get(AreaManageConstants.DEALER))) {
                Map<String,Object> dealer = data.get("dealer__c__r",Map.class,Maps.newHashMap());
                infoVO.setDealerId(dealer.getOrDefault("deptId","").toString());
                infoVO.setDealerName(dealer.getOrDefault("deptName","").toString());
            }
        }
        return infoVO;
    }

    private Map<String, Set<String>> getAreaData(List<String> storeIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1500);
        query.setOffset(0);

        Filter areaFilter = new Filter();
        areaFilter.setFieldName(CoveredStoresConstants.STORE);
        areaFilter.setOperator(Operator.IN);
        areaFilter.setFieldValues(storeIds);
        query.setFilters(Lists.newArrayList(areaFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), CoveredStoresConstants.COVERED_STORES_OBJ, query).getData();
        if(CollectionUtils.isNotEmpty(data)){
            Map<String, Set<String>> areaMap = data.stream().collect(Collectors.groupingBy(o->(String)o.get(CoveredStoresConstants.STORE),Collectors.mapping(a->(String)a.get(CoveredStoresConstants.BELONG_AREA),Collectors.toSet())));
            return  areaMap;
        }
        return Maps.newHashMap();

    }
}

