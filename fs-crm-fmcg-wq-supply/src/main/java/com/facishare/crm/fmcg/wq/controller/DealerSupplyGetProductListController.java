package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.supply.DealerSupply;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class DealerSupplyGetProductListController extends PreDefineController<DealerSupply.Arg, DealerSupply.Result> {

    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected DealerSupply.Result doService(DealerSupply.Arg arg) {
        DealerSupply.Result result=new DealerSupply.Result();
        Map<String,List<String>> productMap=new HashMap<>();
        for(String storeId:arg.getStoreIds()) {
            Set<String> productSet = supplyService.getProductListByStoreId(controllerContext.getTenantId(), storeId);
            productMap.put(storeId,Lists.newArrayList(productSet));
        }

        result.setProductIds(productMap);
        return result;
    }
}
