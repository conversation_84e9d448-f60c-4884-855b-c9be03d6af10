package com.facishare.crm.fmcg.wq.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.CoveredProductConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredProductObjConstants;
import com.facishare.crm.fmcg.wq.constants.ProductCollectionObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.DistributorSupply;
import com.facishare.crm.fmcg.wq.model.Product;
import com.facishare.crm.fmcg.wq.model.Supplier;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.enterprise.common.util.MD5Util;
import com.facishare.idempotent.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.stone.commons.domain.R;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 经营范围设置
 */
@Slf4j
public class ProductCollectionEditAction extends StandardEditAction {
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);

    SupplyDao supplyDao=SpringUtil.getContext().getBean(SupplyDao.class);
    private List<String> addProductIdList = Lists.newArrayList();
    private List<String> delProductIdList = Lists.newArrayList();
    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        if (GrayRelease.isAllow("checkin-server-v2", "isYLProductCollection", actionContext.getEa())) {
            IdempotentService idempotentService = serviceFacade.getBean(IdempotentService.class);
            IdempotentStore idempotentStore = serviceFacade.getBean(IdempotentStore.class);
            if (idempotentService != null) {
                String key = actionContext.getTenantId() + actionContext.getActionCode() + DigestUtils.md5Hex(JSON.toJSONString(arg));
                Result result = idempotentService.doWithIdempotent(key, () -> {
                    ylCheckBefore(arg);
                    return super.doAct(arg);
                }, IdempotentParam.builder().build(), new MetaDataBusinessException(I18N.text(I18NKey.DO_NOT_SUBMIT_REPEATEDLY)));
                idempotentStore.delete(key);
                return result;
            } else {
                ylCheckBefore(arg);
                return super.doAct(arg);
            }
        } else {
            return super.doAct(arg);
        }
    }

    private void ylCheckBefore(Arg arg) {
        ylValidateData(arg, 1);
        validateDetailData(arg);
//            checkV1(arg);
        // 增加 主对象不能变更的逻辑
        String argRecordType = arg.getObjectData().get(BaseField.recordType.getApiName()).toString();
        String argBusinessScopeType = arg.getObjectData().get(ProductCollectionObjConstants.Field.businessScopeType.getApiName()).toString();
        String dbBusinessScopeType = dbMasterData.get(ProductCollectionObjConstants.Field.businessScopeType.getApiName()).toString();
        Object depObj = arg.getObjectData().get(ProductCollectionObjConstants.Field.departmentId.getApiName());
        if (Objects.isNull(depObj) || ((List) depObj).size() == 0){
            throw new ValidateException("服务处为空"); //ignoreI18n
        }
        String dealerId = null;
        if (ProductCollectionObjConstants.Value.isDistributor(argRecordType)){
            dealerId = arg.getObjectData().get(ProductCollectionObjConstants.Field.distributorId.getApiName()).toString();
        }else if (ProductCollectionObjConstants.Value.isDealer(argRecordType)){
            dealerId = arg.getObjectData().get(ProductCollectionObjConstants.Field.dealerId.getApiName()).toString();
        }else{
            dealerId =((List) depObj).get(0).toString();
        }
        String dbLeaderId = null;
        if (ProductCollectionObjConstants.Value.isDistributor(dbMasterData.getRecordType())){
            dbLeaderId = dbMasterData.get(ProductCollectionObjConstants.Field.distributorId.getApiName()).toString();
        }else if (ProductCollectionObjConstants.Value.isDealer(dbMasterData.getRecordType())){
            dbLeaderId = dbMasterData.get(ProductCollectionObjConstants.Field.dealerId.getApiName()).toString();
        }else{
            dbLeaderId =dbMasterData.get(ProductCollectionObjConstants.Field.departmentId.getApiName(),List.class).get(0).toString();
        }

        if (
                dbMasterData.getRecordType().equals(argRecordType)
                        && dbLeaderId
                        .equals(dealerId)
        ) {

        } else {
            throw new ValidateException("主数据不能编辑"); //ignoreI18n
        }
        // 增加 子对象 数据验证逻辑
        List<IObjectData> dbDetails = dbDetailDataMap.get(CoveredProductObjConstants.API_NAME);
        //arg details productIds
        List<String> argProductIds = arg.getDetails().get(CoveredProductObjConstants.API_NAME).stream()
                .map(o -> o.get(CoveredProductObjConstants.Field.productId.getApiName()).toString()).collect(Collectors.toList());
        //增加判断产品重复
        if (argProductIds.size() != argProductIds.stream().distinct().count()) {
            //打印重复数据日志
            //重复数据
            List<String> repeatData = argProductIds.stream().filter(o -> argProductIds.indexOf(o) != argProductIds.lastIndexOf(o)).collect(Collectors.toList());
            log.info("ProductCollectionEditAction data argProductIds repeatData {} ", repeatData);
            throw new ValidateException("经营产品明细中存在相同的产品，请检查！"); //ignoreI18n
        }
        String tenantId = actionContext.getTenantId();
        // 分为3 种  所有的变成指定 指定的变成所有  指定的 变成指定
        Supplier supplierById = supplyService.getSupplierById(tenantId, dealerId, 2);
        List<Product> parentProductRange = supplierById.getParentProductRange();
        List<String> dbParentProductIds = parentProductRange.stream().map(o -> o.getProductId())
                .distinct().collect(Collectors.toList());
        List<String> dbThisProductIds = supplierById.getProductList().stream().map(o -> o.getProductId())
                .distinct().collect(Collectors.toList());

        log.info("ProductCollectionEditAction data dbParentProductIds {} ,dbThisProductIds {},argProductIds{}"
                ,dbParentProductIds,dbThisProductIds,argProductIds);
        if (ProductCollectionObjConstants.Value.isInheritUpProduct(dbBusinessScopeType)
                && !ProductCollectionObjConstants.Value.isInheritUpProduct(argBusinessScopeType)) {
            // db 是继承上级的 ，参数是不继承上级的
            // 所有变成指定
            // 范围缩小，计算出 删除的
            // 如果是服务处，或者上级产品中包含所有的，说明  之前是所有，参数是指定
            if (supplierById.getLevel() == 0 || dbParentProductIds.contains("ALL")) {
                delProductIdList.add("ALL");
                delProductIdList.addAll(argProductIds);
            } else {
                //从 上级 里删除掉 不在参数里的
                delProductIdList.addAll(dbParentProductIds.stream().filter(o->!argProductIds.contains(o)).collect(Collectors.toSet()));
            }
        } else if (!ProductCollectionObjConstants.Value.isInheritUpProduct(dbBusinessScopeType)
                && ProductCollectionObjConstants.Value.isInheritUpProduct(argBusinessScopeType)) {
            //指定 变成上级所有，，范围会扩大，

            // 如果是服务处
            if (supplierById.getLevel() == 0 || dbParentProductIds.contains("ALL")) {
                addProductIdList.add("ALL");
            } else {
                //从上级里 增加 当前db不包含的
                addProductIdList.addAll(dbParentProductIds.stream().filter(o->!dbThisProductIds.contains(o)).collect(Collectors.toSet()));
            }
        } else if (!ProductCollectionObjConstants.Value.isInheritUpProduct(dbBusinessScopeType)
                && !ProductCollectionObjConstants.Value.isInheritUpProduct(argBusinessScopeType)){
            //指定变成指定 有 增加的有删除的
            addProductIdList.addAll(argProductIds.stream().filter(o->!dbThisProductIds.contains(o)).collect(Collectors.toSet()));
            delProductIdList.addAll(dbThisProductIds.stream().filter(o->!argProductIds.contains(o)).collect(Collectors.toSet()));
        }
        if (!CollectionUtils.isEmpty(addProductIdList)){
            supplyService.checkCoveredProductsAdd(tenantId,addProductIdList,dbMasterData.getId());
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        if (!CollectionUtils.isEmpty(delProductIdList)){
            supplyService.afterCoveredStoreDel(actionContext.getTenantId(),delProductIdList,dbMasterData.getId());
        }
        if (!CollectionUtils.isEmpty(addProductIdList) || !CollectionUtils.isEmpty(delProductIdList)){
            syncAvailableRange(arg);
        }
        return super.after(arg, result);
    }

    private void checkV1(Arg arg) {
        String objectDataId = arg.getObjectData().getId();
        List<String> name = (List<String>)arg.getObjectData().get("name");
        String recordType = arg.getObjectData().get("record_type").toString();
        String scopeType = arg.getObjectData().get("business_scope_type").toString();
        //查询目前的产品明细对象
        List<IObjectData> cpData = supplyService.getDesignateProductListByRelatedIds(actionContext.getTenantId(), CoveredProductObjConstants.API_NAME, Lists.newArrayList(objectDataId));
        List<String> oldIds = Lists.newArrayList();
        List<String> newIds = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(cpData)) {
            cpData.forEach(o -> oldIds.add(o.getId()));
        }
        if (!CollectionUtils.isEmpty(arg.getDetails())) {
            List<ObjectDataDocument> details = arg.getDetails().get(CoveredProductObjConstants.API_NAME);
            if (!CollectionUtils.isEmpty(details)) {
                details.forEach(o -> newIds.add(o.getId()));
            }
        }

        if (!CollectionUtils.isEmpty(oldIds) && (!CollectionUtils.isEmpty(newIds))) {
            // 校验产品组明细是否被使用
            List<String> addList = Lists.newArrayList();
            List<String> delList = Lists.newArrayList();
            addList.addAll(newIds);
            addList.removeAll(oldIds);
            if (!CollectionUtils.isEmpty(addList)) {
                // 指定产品新增不需要处理，继承关系需要判断是否有冲突
//                addProductDetail(recordType, scopeType, objectDataId, addList);
            }

            delList.addAll(oldIds);
            delList.removeAll(newIds);
            if (!CollectionUtils.isEmpty(delList)) {
                if (checkProductIsUsing(recordType, name, delList)) {
                    throw new ValidateException("数据被使用，不能移除"); //ignoreI18n
                }
            }
        } else {
            if (!CollectionUtils.isEmpty(oldIds)) {
                // 校验产品组明细是否被使用
                if (checkProductIsUsing(recordType, name, oldIds)) {
                    throw new ValidateException("数据被使用，不能移除"); //ignoreI18n
                }
            }
            if (!CollectionUtils.isEmpty(newIds)) {
                // 指定产品新增不需要处理，继承关系需要判断是否有冲突
//                addProductDetail(recordType,scopeType,objectDataId,newIds);
            }
        }
    }

    private boolean checkProductIsUsing(String recordType, List<String> name, List<String> delList) {
        List<IObjectData> data = Lists.newArrayList();
        if ("server_center__c".equals(recordType)) {
            //查询服务处下的所有经销商
            data = supplyService.getProductCollectionListByService(actionContext.getTenantId(), ProductCollectionObjConstants.API_NAME, name);
        } else if ("dealer__c".equals(recordType)) {
            //查询经销商下的配送商
//            data = supplyService.getDistributionListByDealer(actionContext.getTenantId(), ProductCollectionObjConstants.API_NAME, name);
        }
        if (!CollectionUtils.isEmpty(data)) {
            //产品组明细-查询产品组明细关联字段in的
            List<String> ids = Lists.newArrayList();
            data.forEach(o -> ids.add(o.getId()));
            List<IObjectData> cdata = supplyService.getDesignateProductListByRelatedIds(actionContext.getTenantId(), CoveredProductObjConstants.API_NAME, ids);
            List<String> cIds = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(cdata)) {
                cdata.forEach(o -> cIds.add(o.getId()));
                cIds.retainAll(delList);
                if (!CollectionUtils.isEmpty(cIds)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * @param arg
     * @param type 0-add，1-edit
     */
    protected void ylValidateData(Arg arg, int type) {

        //查询服务处和对应的业务类型是否已经存在
        String businessType = arg.getObjectData().get("record_type").toString();
        String serviceCenterId = arg.getObjectData().get("service_center__c").toString();

        String dealerId = "";
        if (Objects.nonNull(arg.getObjectData().get("dealer"))) {
            dealerId = arg.getObjectData().get("dealer").toString();
        }
        String distributionId = "";
        if (Objects.nonNull(arg.getObjectData().get("distribution"))) {
            distributionId = arg.getObjectData().get("distribution").toString();
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(10);
        query.setOffset(0);

        Filter typeFilter = new Filter();
        typeFilter.setFieldName("record_type");
        typeFilter.setOperator(Operator.EQ);
        typeFilter.setFieldValues(Lists.newArrayList(businessType));

        if (businessType.equals("server_center__c")) {//服务处经营品项
            if (StringUtils.isEmpty(serviceCenterId)) {
                throw new ValidateException("服务处不能为空"); //ignoreI18n
            }
            //查看服务处是否已经存在
            Filter deptFilter = new Filter();
            deptFilter.setFieldName("service_center__c");
            deptFilter.setOperator(Operator.EQ);
            deptFilter.setFieldValues(Lists.newArrayList(serviceCenterId));

            query.setFilters(Lists.newArrayList(typeFilter, deptFilter));
            List<IObjectData> data = serviceFacade.findBySearchQuery(actionContext.getUser(), "ProductCollectionObj", query).getData();
            if (type == 0) {
                if (!CollectionUtils.isEmpty(data)) {
                    String serviceStr = data.get(0).get("service_center__c").toString();
                    throw new ValidateException("该服务处:" + serviceStr + "已经存在经营范围，请不要重复添加"); //ignoreI18n
                }
            } else {
                if (!CollectionUtils.isEmpty(data) && data.size() >= 2) {
                    String serviceStr = data.get(0).get("service_center__c").toString();
                    throw new ValidateException("该服务处:" + serviceStr + "已经存在经营范围，请不要重复添加"); //ignoreI18n
                }
            }


        } else if (businessType.equals("dealer__c")) {//经销商经营品项
            if (StringUtils.isEmpty(dealerId)) {
                throw new ValidateException("经销商不能为空"); //ignoreI18n
            }
            Filter dealerFilter = new Filter();
            dealerFilter.setFieldName("dealer");
            dealerFilter.setOperator(Operator.EQ);
            dealerFilter.setFieldValues(Lists.newArrayList(dealerId));

            query.setFilters(Lists.newArrayList(typeFilter, dealerFilter));
            List<IObjectData> data = serviceFacade.findBySearchQuery(actionContext.getUser(), "ProductCollectionObj", query).getData();
            if (type == 0) {
                if (!CollectionUtils.isEmpty(data)) {
                    String dealerStr = data.get(0).get("dealer").toString();
                    throw new ValidateException("经销商:" + dealerStr + "已经存在经营范围，请不要重复添加"); //ignoreI18n
                }
            } else {
                if (!CollectionUtils.isEmpty(data) && data.size() >= 2) {
                    String dealerStr = data.get(0).get("dealer").toString();
                    throw new ValidateException("经销商:" + dealerStr + "已经存在经营范围，请不要重复添加"); //ignoreI18n
                }
            }

        } else {//分销邮差商经营品项
            if (StringUtils.isEmpty(distributionId)) {
                throw new ValidateException("分销邮差商不能为空"); //ignoreI18n
            }
            Filter disFilter = new Filter();
            disFilter.setFieldName("distribution");
            disFilter.setOperator(Operator.EQ);
            disFilter.setFieldValues(Lists.newArrayList(distributionId));

            query.setFilters(Lists.newArrayList(typeFilter, disFilter));
            List<IObjectData> data = serviceFacade.findBySearchQuery(actionContext.getUser(), "ProductCollectionObj", query).getData();
            if (type == 0) {
                if (!CollectionUtils.isEmpty(data)) {
                    String disStr = data.get(0).get("distribution").toString();
                    throw new ValidateException("分销邮差商:" + disStr + "已经存在经营范围，请不要重复添加"); //ignoreI18n
                }
            } else {
                if (!CollectionUtils.isEmpty(data) && data.size() >= 2) {
                    String disStr = data.get(0).get("distribution").toString();
                    throw new ValidateException("分销邮差商:" + disStr + "已经存在经营范围，请不要重复添加"); //ignoreI18n
                }
            }
        }

    }

    //校验产品不能重复添加
    protected void validateDetailData(Arg arg) {
        //校验不能重复
        if (!CollectionUtils.isEmpty(arg.getDetails())) {
            if (!CollectionUtils.isEmpty(arg.getDetails().get(CoveredProductConstants.COVERED_PRODUCT_OBJ))) {
                List<String> coveredIds = arg.getDetails().get(CoveredProductConstants.COVERED_PRODUCT_OBJ).stream().filter(o -> Objects.nonNull(o.get(CoveredProductConstants.RELATION_PRODUCT))).map(o -> o.get(CoveredProductConstants.RELATION_PRODUCT).toString()).collect(Collectors.toList());
                long count = coveredIds.stream().distinct().count();
                if (coveredIds.size() != count) {
                    throw new ValidateException("经营产品明细中存在相同的产品，请检查！"); //ignoreI18n
                }
            }
        }
    }




    private void syncAvailableRange(Arg arg){
        String recordType = arg.getObjectData().get(BaseField.recordType.getApiName()).toString();
        List<String> serviceCenter = (List<String>)arg.getObjectData().get(ProductCollectionObjConstants.Field.departmentId.getApiName());
        List<String> dealerSupplyIds=Lists.newArrayList();
        //根据类型 判断是改的 哪一级的经营乏味
        if ("server_center__c".equals(recordType)) {
            List<String> accountIds=Lists.newArrayList();
            //获取服务处下所有经营范围，下的 经销商和分销商
            List<IObjectData> data  = supplyService.getProductCollectionListByService(actionContext.getTenantId(), ProductCollectionObjConstants.API_NAME, serviceCenter);
            data.forEach(o->{
                if(Objects.nonNull(o.get(ProductCollectionObjConstants.Field.dealerId.getApiName()))){
                    accountIds.add(o.get(ProductCollectionObjConstants.Field.dealerId.getApiName()).toString());
                }else if(Objects.nonNull(o.get(ProductCollectionObjConstants.Field.distributorId.getApiName()))){
                    accountIds.add(o.get(ProductCollectionObjConstants.Field.distributorId.getApiName()).toString());
                }
            });
            if(CollectionUtils.isEmpty(accountIds)){
                return ;
            }
            List<String> noDeleteIds = supplyDao.getNoDeleteByIdsIgnoreException(actionContext.getTenantId(), accountIds);
            accountIds.removeIf(o->!noDeleteIds.contains(o));
            //查询所有相关联的供货关系
            List<IObjectData> dealerSupplyData = supplyDao.getDealerSupplyBySuppliers(actionContext.getTenantId(),accountIds);
            dealerSupplyData.forEach(o->dealerSupplyIds.add(o.getId()));

        }else if("dealer__c".equals(recordType)){
            String dealerId = arg.getObjectData().get(ProductCollectionObjConstants.Field.dealerId.getApiName()).toString();
            List<DistributorSupply> distributorSupplyList= supplyService.getRelationListByUpIds(actionContext.getTenantId(),Lists.newArrayList(dealerId));
            //自己的供货关系
            List<IObjectData> dealerSupplyData = supplyDao.getDealerSupplyBySuppliers(actionContext.getTenantId(),Lists.newArrayList(dealerId));
            if(!CollectionUtils.isEmpty(dealerSupplyData)){
                dealerSupplyIds.add(dealerSupplyData.get(0).getId());
            }
            if(!CollectionUtils.isEmpty(distributorSupplyList)){
                // 特例的不需要处理
                distributorSupplyList.forEach(o->{
                    if(!o.isSpecialSupply()) {
                        dealerSupplyIds.add(o.getUpSupplyId());
                    }
                });
            }

        }else{
            String distributorId = arg.getObjectData().get(ProductCollectionObjConstants.Field.distributorId.getApiName()).toString();
            List<IObjectData> distributorSupplyData = supplyDao.getDealerSupplyBySuppliers(actionContext.getTenantId(),Lists.newArrayList(distributorId));
            if(!CollectionUtils.isEmpty(distributorSupplyData)){
                distributorSupplyData.forEach(o->dealerSupplyIds.add(o.getId()));
            }
        }

        //同步可售范围
        if(CollectionUtils.isEmpty(dealerSupplyIds)){
            return;
        }
        //以供货关系为索引更新 可售范围
        for(String dealerSupplyId:dealerSupplyIds) {
            supplyService.addAvailableObjSyncTask(actionContext.getTenantId(), dealerSupplyId);
        }

    }
//
//    public static void main(String[] args) {
//        //输出数组中的重复数据 ["646e5ed5711fc10001d754b5", "6564e78137b44700011e0a3d", "619c863c182f3a00011e5edd", "860376736773013504", "644960070b68c10001a5fafc", "6564e76437b44700011e0107", "619c8688d2514a000164d8b9", "619c8646182f3a00011e6a86", "619c8687d2514a000164d8a0", "619c8681d2514a000164d7fc", "6564e78b879bf00001d51f6d", "619c8687d2514a000164d88c", "619c8685d2514a000164d867", "619c8646182f3a00011e6a0a", "619c8680d2514a000164d776", "619c867fd2514a000164d75d", "619c867fd2514a000164d75b", "619c8643182f3a00011e6598", "619c8645182f3a00011e6812", "624335ba75400b000148e92a", "619c867dd2514a000164d720", "619c8647182f3a00011e6bc0", "619c8682d2514a000164d801", "64345cd40ba5c000019595d3", "64345cd20ba5c000019595c8", "622f6e813efe840001f7ca12", "619c8645182f3a00011e67e3", "619c8684d2514a000164d85b", "6564e789879bf00001d51dd1", "619c8683d2514a000164d83d", "619c8644c211110001b16b08", "619c863b182f3a00011e5d79", "619c8644182f3a00011e6713", "619c8641182f3a00011e6397", "624486098fb6df0001b3ffe9", "620d27484eef370001fa481e", "619c8643182f3a00011e6568", "619c8679d2514a000164d461", "619c8683d2514a000164d818", "620fca0b4eef370001b33909", "619c8640182f3a00011e62a6", "829645611994021888", "619c8646182f3a00011e6a09", "803728459197906944", "619c867ed2514a000164d73a", "619c8645182f3a00011e690d", "856607928472141824", "856028103491584000", "619c8684d2514a000164d844", "856028049703763968", "619c8689d2514a000164d8ba", "619c8683d2514a000164d81e", "619c8683d2514a000164d819", "619c8644c211110001b16b0c", "61d9bc598fb298000153ae23", "61d9bc5d8fb298000153ae38", "619c8682d2514a000164d80a", "619c8686d2514a000164d882", "619c8682d2514a000164d808", "619c8645182f3a00011e6821", "6597006937b4470001f99743", "65721658879bf00001f682af", "65970087efc38a0001d81b01", "6564e789879bf00001d51dfa", "6564e76037b44700011dffc4", "65b2b0113b442d00019a058b", "65c2820d0c02a20001fd3d11", "65c2820d0c02a20001fd3d10", "66070fdb437f690006921f02", "66070fdb437f690006921f0b", "65f0a68b017b180006a2de7f", "65f0a66a2b680900065748e6", "65d6488d3b442d0001f2fdb8", "65b2b0113b442d00019a055e", "65b2aff23b442d000199fe38", "65b2aff23b442d000199fe38"]
//        ArrayList<String> x = Lists.newArrayList("65b2b0113b442d00019a058b","65b2aff23b442d000199fe38","65b2b0113b442d00019a058b","65b2aff23b442d000199fe38","6564e76037b44700011dffc4","619c8686d2514a000164d887","65c2820d0c02a20001fd3d10","65c2820d0c02a20001fd3d11","619c8641182f3a00011e634a","653ffd64cb35230001ca26a7","619c8645182f3a00011e6812","6564e76437b44700011e0107","6564e78b879bf00001d51f6d","6564e78137b44700011e0a3d","6564e789879bf00001d51dd1","619c863c182f3a00011e5edd","646bbbda711fc10001ab060d","6414b8e87333550001ee8f91","646e5ed5711fc10001d754b5","860376736773013504","61d9bc4e8fb298000153adf7","64345cd20ba5c000019595c8","64345cd40ba5c000019595d3","644960070b68c10001a5fafc","619c8684d2514a000164d85c","856028103491584000","856028049703763968","619c867dd2514a000164d720","619c8687d2514a000164d8b0","619c8687d2514a000164d890","619c8683d2514a000164d816","619c8683d2514a000164d81e","620d27484eef370001fa481e","622f6e813efe840001f7ca12","619c8644c211110001b16b0c","619c8682d2514a000164d80a","619c8684d2514a000164d855","619c8644182f3a00011e66b8","619c8645182f3a00011e690d","619c8685d2514a000164d867","619c8641182f3a00011e6397","619c8129182f3a00011c1672","619c8683d2514a000164d814","619c8686d2514a000164d882","619c8645182f3a00011e6821","619c8686d2514a000164d880","619c8645182f3a00011e67e3","619c8644c211110001b16b0d","619c8682d2514a000164d808","619c8644182f3a00011e677c","619c8681d2514a000164d7fc","619c8684d2514a000164d844","61d9bc5b8fb298000153ae2f","61d9bc5d8fb298000153ae38","619c8684d2514a000164d85b","619c8683d2514a000164d83d","619c8683d2514a000164d841","619c8680d2514a000164d77a","619c8646182f3a00011e6a86","619c8680d2514a000164d776","619c8646182f3a00011e6a0a","619c867fd2514a000164d75d","619c867fd2514a000164d75b","619c8688d2514a000164d8b4","619c8689d2514a000164d8bf","619c8688d2514a000164d8b9","619c867fd2514a000164d73c","619c8689d2514a000164d8ba","619c8689d2514a000164d8bb","619c8687d2514a000164d88c","619c8687d2514a000164d8a0","619c867ed2514a000164d727");
//        System.out.println(x.stream().filter(o -> x.indexOf(o) != x.lastIndexOf(o)).collect(Collectors.toList()));
//    }
}
