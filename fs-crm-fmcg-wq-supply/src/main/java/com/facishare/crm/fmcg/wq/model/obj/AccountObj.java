package com.facishare.crm.fmcg.wq.model.obj;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 客户
 * @author: zhangsm
 * @create: 2021-04-25 15:43
 **/
@Data
public class AccountObj {


    /**
     * 归属部门
     */
    private String departmentId;
    /**
     * 跨区域部门
     */
    private List<String> otherDepartmentIds;

    private List<String> up_distributors__c;
    /**
     * 业务类型
     */
    private String recordType;

    private String id;

    private String name;

    private String owner;

    public String getDepartmentId(){
        if (StringUtils.isBlank(this.departmentId)){
            return "999999";
        }else{
            return this.departmentId;
        }
    }


}
