package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.FmcgPreActionArgs;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 促销员不同意
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterPromoterDisagreeAction extends FmcgAbstractStandardAction<PromoterPromoterAgreeAction.Arg, PromoterPromoterAgreeAction.Result> {
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);

    @Override
    protected PromoterPromoterAgreeAction.Result doAct(PromoterPromoterAgreeAction.Arg arg) {
        if ((PromoterFields.ReviewStatus.disagree.getValue()).equals(objectData.get(PromoterFields.REVIEW_STATUS,String.class))) {
            throw new ValidateException("促销员已审核不通过"); //ignoreI18n
        }
        objectData.set(PromoterFields.REVIEW_STATUS,PromoterFields.ReviewStatus.disagree.getValue());
        //未入
        objectData.set(PromoterFields.IO_STATUS,"2");
        supplyDao.update(User.systemUser(actionContext.getTenantId()), objectData);
        recodeLog();
        return new PromoterPromoterAgreeAction.Result();
    }

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.PROMOTER_DISAGREE;
    }

    @Data
    public static class Arg extends FmcgPreActionArgs {
    }

    @Data
    public static class Result {

    }
}
