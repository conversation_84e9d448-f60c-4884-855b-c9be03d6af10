package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterInvalidAction extends StandardInvalidAction {

    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);
    @Override
    protected void before(Arg arg) {
        //跳过基础校验
        this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        IObjectData promoterById = pmmService.getPromoterById(actionContext.getTenantId(), arg.getObjectDataId());
        //是否离职
        if (!pmmService.isLeave(actionContext.getTenantId(), promoterById)) {
            throw new ValidateException("促销员未离职，不能作废"); //ignoreI18n
        }
        Result result = super.doAct(arg);
        //去停用互联用户
        String outerUserId = promoterById.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class);
        if (StringUtils.isNotBlank(outerUserId)) {
            pmmService.invalidPublicEmployee(actionContext.getTenantId(), Lists.newArrayList(outerUserId));
        }
        return result;
    }
}
