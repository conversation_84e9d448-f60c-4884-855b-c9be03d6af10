package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.SupplyChangeFields;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-09-23 17:06
 **/
public class SupplyChangeListHeaderController extends StandardListHeaderController {
    private static final List<String> hideKeyList = Lists.newArrayList(SupplyChangeFields.ARGS);
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        result.getTemplates().forEach(o->{
            List<Map<String, Object>> fieldList= (List<Map<String, Object>>)o.get("field_list");
            if (CollectionUtils.isNotEmpty(fieldList)) {
                fieldList = fieldList.stream().filter(f -> {
                    if (hideKeyList.contains(f.get("field_name"))) {
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
            }
            o.put("field_list",fieldList);
        });
        result.getVisibleFields().removeAll(hideKeyList);
        result.getVisibleFieldsWidth().removeIf(p->hideKeyList.contains((String) p.get("field_name")));
        //隐藏字段 args
        if (layoutExt != null){
            layoutExt.hideFields(hideKeyList);
        }
        if (listLayout != null){
            listLayout.hideFields(hideKeyList);
        }
        return after;
    }
}
