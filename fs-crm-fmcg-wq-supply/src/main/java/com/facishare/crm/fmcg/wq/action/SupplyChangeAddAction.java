package com.facishare.crm.fmcg.wq.action;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.appserver.checkins.api.model.AreaPersonnelConfigPo;
import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.crm.fmcg.wq.model.PostInfo;
import com.facishare.crm.fmcg.wq.service.OrganizationJMLService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Slf4j
public class SupplyChangeAddAction extends FmcgSkipPermissionAddAction {
    @Override
    protected void before(Arg arg) {
        actionContext.setAttribute("triggerFlow",Boolean.TRUE);
        super.before(arg);
    }
}
