package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.AddDistributorSupplyFilter;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.ProductCollectionObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.DistributorSupply;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.SetUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description: 根据客户id获取用户能指定的上级配送商
 * @author: zhangsm
 * @create: 2021-05-08 15:25
 **/
public class DistributorSupplyAddFilterController extends PreDefineController<AddDistributorSupplyFilter.Arg, AddDistributorSupplyFilter.Result> {
    SupplyDao supplyDao =  SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected AddDistributorSupplyFilter.Result doService(AddDistributorSupplyFilter.Arg arg) {
        String tenantId = controllerContext.getTenantId();
        AccountObj thisAccountObj = supplyDao.getAccountObjById(tenantId,arg.getAccountId());
        if (AccountObjConstants.Value.default__c.toString().equals(thisAccountObj.getRecordType())){
            //门店
            throw new ValidateException(String.format("%s 业务类型是%s 无法分配上级配送商",thisAccountObj.getName() //ignoreI18n
                    ,AccountObjConstants.Value.default__c.getDisName()));
        }else  if (AccountObjConstants.Value.dealer__c.toString().equals(thisAccountObj.getRecordType())){
            //经销商
            throw new ValidateException(String.format("%s 业务类型是%s 无法分配上级配送商",thisAccountObj.getName() //ignoreI18n
                    ,AccountObjConstants.Value.dealer__c.getDisName()));
        }
        AddDistributorSupplyFilter.Result result = new AddDistributorSupplyFilter.Result();

        //1 .查询客户的所有服务处
        Set<String> departmentIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(thisAccountObj.getOtherDepartmentIds())){
            departmentIds.addAll(thisAccountObj.getOtherDepartmentIds());
        }
        // 2. 查询已经有的上级
        List<DistributorSupply> distributorSupplyList = supplyDao.getDistributorSupplyByThisIds(controllerContext.getTenantId(), Lists.newArrayList(arg.getAccountId()));
        result.setSelectedCount(distributorSupplyList.size());
        Set<String> usedUpSupplierIds = distributorSupplyList.stream().map(o->o.getUpDealerAccountId()).collect(Collectors.toSet());

        //服务处部门
        List<String> serviceCenterDepartmentIds = supplyDao.getServiceCenterDepartmentIds(tenantId, Lists.newArrayList(departmentIds));
        //通过服务处部门查经销商
        List<ObjectDataDocument> infos = supplyDao.getSupplyDealerListByServiceCenterDepartmentIds(tenantId, SetUtils.EMPTY_SET, serviceCenterDepartmentIds);
        result.setInfos(infos);
        result.setTotal(infos.size());
        //            getV1(result, thisAccountObj, usedSupplierIds);
        return result;
    }

    private void getV1(AddDistributorSupplyFilter.Arg arg, AccountObj thisAccountObj, AddDistributorSupplyFilter.Result result, Set<String> departmentIds) {
        // 2. 查询已经有的上级
        List<DistributorSupply> distributorSupplyList = supplyDao.getDistributorSupplyByThisIds(controllerContext.getTenantId(), Lists.newArrayList(arg.getAccountId()));
        result.setSelectedCount(distributorSupplyList.size());
        Set<String> usedUpSupplierIds = distributorSupplyList.stream().map(o->o.getUpDealerAccountId()).collect(Collectors.toSet());

        SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                .in(ProductCollectionObjConstants.Field.departmentId.getApiName(),departmentIds)
                .eq(BaseField.recordType.getApiName(),ProductCollectionObjConstants.Value.recordType_dealer.getValue());
        if (CollectionUtils.isNotEmpty(usedUpSupplierIds)){
            searchQueryBuilder.nin(ProductCollectionObjConstants.Field.dealerId.getApiName(),usedUpSupplierIds);
        }
        SearchQuery searchQuery = searchQueryBuilder.build();
        List<IObjectData> data =  supplyDao.getAllIObjectDataListByQuery(User.systemUser(controllerContext.getTenantId()),searchQuery, ProductCollectionObjConstants.API_NAME);

        if (CollectionUtils.isEmpty(data)){
            // 没有额外的直接返回数据
//            return result;
            throw new ValidateException(String.format("%s 没有额外的经销商经营范围，无法继续",thisAccountObj.getName())); //ignoreI18n
        }
        List<ObjectDataDocument> infos = data.stream().map(o -> {
            ObjectDataDocument obj = new ObjectDataDocument();
            obj.put(BaseField.id.getApiName(), o.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class));
            //直接用范围的名字了 暂时不用经销商名字
//            obj.put(BaseField.name.getApiName(), o.get(String.format("%s__r", ProductCollectionObjConstants.Field.dealerId.getApiName()), String.class));
            obj.put(BaseField.name.getApiName(), o.getName());
            return obj;
        }).collect(Collectors.toList());
        result.setInfos(infos);
        result.setTotal(data.size());
    }
}
