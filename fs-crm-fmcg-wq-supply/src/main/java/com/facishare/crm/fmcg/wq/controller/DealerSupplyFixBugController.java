package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.supply.FixBug;
import com.facishare.crm.fmcg.wq.service.SyncAvailableRangeService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.ListUtils;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 修复bug使用的controller
 * @author: zhangsm
 * @create: 2022-01-18 14:45
 **/
public class DealerSupplyFixBugController extends PreDefineController<FixBug.Arg, FixBug.Result> {
    SyncAvailableRangeService syncAvailableRangeService= SpringUtil.getContext().getBean(SyncAvailableRangeService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected FixBug.Result doService(FixBug.Arg arg) {
        if (arg.getFlag() == 1){
            syncAvailableRangeService.fixPriceBookObj(controllerContext.getTenantId());
        }
        return new FixBug.Result();
    }
}
