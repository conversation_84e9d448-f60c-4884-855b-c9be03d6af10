package com.facishare.crm.fmcg.wq.api.area;


import com.facishare.appserver.checkins.api.model.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AddDealerSupply {
    @Data
    @ToString
    class Arg implements Serializable {

        /**
         * 当前客户id
         */
        @NotEmpty(message = "参数为空")
        private String accountId;
        /**
         * 添加的上级供货商
         */
        @NotBlank(message = "addSupplierIds 为空")
        private List<String> addSupplierIds;

    }
    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {

    }
}
