package com.facishare.crm.fmcg.wq.api.area;

import com.facishare.crm.fmcg.wq.model.PlanRouteInfo;
import com.facishare.paas.metadata.impl.search.Filter;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface MapStoreFilterV2 {

    @Data
    @ToString
    class Arg implements Serializable {

        private List<String> county;

        private List<Filter> filterList;

        public int isShowAlloc; // 1 展示分配的

    }
    @Data
    @ToString
    class Result implements Serializable {

        List<PlanRouteInfo> dataList;

    }
}
