package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.api.store.MoveArea;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 转移片区接口
 */
@Slf4j
public class CoveredStoresMoveAreaController extends PreDefineController<MoveArea.Arg, MoveArea.Result> {

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected MoveArea.Result doService(MoveArea.Arg arg) {
        MoveArea.Result result = new MoveArea.Result();
        if(StringUtils.isEmpty(arg.getAreaId()) || CollectionUtils.isEmpty(arg.getDataId())){
            throw new ValidateException("id or dataId can not be empty");
        }
        if(GrayRelease.isAllow("checkin-server-v2","areaManage",controllerContext.getEa())){
            return jmlMoveArea(arg, result);
        }else if (GrayRelease.isAllow("checkin-server-v2", "isYLArea",controllerContext.getEa())) {
            return yinLuMoveArea(arg,result);
        }else{
            return moveArea(arg,result);
        }
    }

    private MoveArea.Result moveArea(MoveArea.Arg arg, MoveArea.Result result) {
        if(CollectionUtils.isEmpty(arg.getDataId())){
            return result;
        }
        log.info("CoveredStoresMoveAreaController arg:{}", JSON.toJSONString(arg));
        String ea = serviceFacade.getEAByEI(controllerContext.getTenantId());
        Boolean isYL = GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea);
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), CoveredStoresConstants.COVERED_STORES_OBJ);

        SearchTemplateQuery query =  SearchQuery.builder().eq(CoveredStoresConstants.BELONG_AREA, arg.getSourceAreaId()).in(CoveredStoresConstants.STORE,arg.getDataId()).limit(1000).build().getSearchTemplateQuery();
        List<IObjectData> data = ObjectUtils.queryDataSimple(serviceFacade,controllerContext.getUser(), CoveredStoresConstants.COVERED_STORES_OBJ,query,describe);
        if(CollectionUtils.isNotEmpty(data)) {
            IObjectData newAreaInfo = serviceFacade.findObjectData(controllerContext.getUser(), arg.getAreaId(), AreaManageConstants.AREA_MANAGE_OBJ);
            List<IObjectData>  deleteData = serviceFacade.bulkDeleteDirect(data,controllerContext.getUser());
            if(CollectionUtils.isNotEmpty(deleteData)) {
                List<String> storeIds = deleteData.stream().filter(o->Objects.nonNull(o.get("store"))).map(o->o.get("store",String.class)).collect(Collectors.toList());
                List<IObjectData> details = Lists.newArrayList();
                for (String account : storeIds) {
                    IObjectData detail = new ObjectData();
                    detail.set("store", account);
                    detail.setTenantId(controllerContext.getTenantId());
                    detail.setOwner(Lists.newArrayList(controllerContext.getUser().getUserId()));
                    detail.setDescribeApiName(CoveredStoresConstants.COVERED_STORES_OBJ);
                    detail.setRecordType("default__c");
                    detail.set(CoveredStoresConstants.BELONG_AREA,arg.getAreaId());
                    details.add(detail);
                }
                List<IObjectData> addData = serviceFacade.bulkSaveObjectData(details,controllerContext.getUser());
                if (CollectionUtils.isNotEmpty(addData)) {
                    List<String> changeIds = addData.stream().filter(o -> Objects.nonNull(o.get("store"))).map(o -> o.get("store", String.class)).collect(Collectors.toList());
                    supplyDao.updateAccountAreaInfo(controllerContext.getTenantId(), controllerContext.getUser().getUserIdInt(), newAreaInfo, changeIds, isYL);
                }
            }

        }
        return result;
    }

    private MoveArea.Result yinLuMoveArea(MoveArea.Arg arg, MoveArea.Result result) {
        List<IObjectData> storeData = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), arg.getDataId(), CoveredStoresConstants.COVERED_STORES_OBJ);

        IObjectData areaInfo  = serviceFacade.findObjectData(controllerContext.getUser(), (String)storeData.get(0).get(CoveredStoresConstants.BELONG_AREA), AreaManageConstants.AREA_MANAGE_OBJ);

        IObjectData newAreaInfo  = serviceFacade.findObjectData(controllerContext.getUser(), arg.getAreaId(), AreaManageConstants.AREA_MANAGE_OBJ);
        if(Objects.nonNull(areaInfo) && Objects.nonNull(newAreaInfo) && CollectionUtils.isNotEmpty(storeData)){
            for (IObjectData data : storeData) {
                data.set(CoveredStoresConstants.BELONG_AREA,newAreaInfo.getId());
            }

            log.info("yinLuMoveArea batchUpdate data:{}", storeData.stream().map(o -> o.get(CommonConstants.ID).toString()).collect(Collectors.toList()));
            serviceFacade.batchUpdate(storeData, controllerContext.getUser());

            //处理客户数据
            List<String> ownerUserId = (List<String>) newAreaInfo.get("area_owner__c");
            String areaManageId = newAreaInfo.getId();
            List<String> ids = storeData.stream().filter(o-> Strings.isNotEmpty(o.get(CoveredStoresConstants.STORE,String.class))).map(o->o.get(CoveredStoresConstants.STORE,String.class)).collect(Collectors.toList());
            if(!org.springframework.util.CollectionUtils.isEmpty(ids)) {
                List<IObjectData> data = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), ids, CommonConstants.ACCOUNT_OBJ);
                data.forEach(o->{
                    o.set("route_owner__c", ownerUserId);
                    o.set("belong_routeNew__c", areaManageId);
                });
                serviceFacade.batchUpdate(data,controllerContext.getUser());
            }


        }else{
            throw new ValidateException("选择的数据有误"); //ignoreI18n
        }
        return result;
    }

    @NotNull
    private MoveArea.Result jmlMoveArea(MoveArea.Arg arg, MoveArea.Result result) {
        List<IObjectData> storeData = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), arg.getDataId(), CoveredStoresConstants.COVERED_STORES_OBJ);

        IObjectData areaInfo  = serviceFacade.findObjectData(controllerContext.getUser(), (String)storeData.get(0).get(CoveredStoresConstants.BELONG_AREA), AreaManageConstants.AREA_MANAGE_OBJ);

        IObjectData newAreaInfo  = serviceFacade.findObjectData(controllerContext.getUser(), arg.getAreaId(), AreaManageConstants.AREA_MANAGE_OBJ);

        List<IObjectData> newAreaAccount  = serviceFacade.findDetailObjectDataListIgnoreFormula(newAreaInfo,controllerContext.getUser());

        List<String> existsAccount = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(newAreaAccount)){
            existsAccount = newAreaAccount.stream().filter(o -> Objects.nonNull(o.get(CoveredStoresConstants.STORE))).map(o -> o.get(CoveredStoresConstants.STORE).toString()).collect(Collectors.toList());
            log.info("MoveArea :{}",existsAccount);
        }

        if(Objects.nonNull(areaInfo) && Objects.nonNull(newAreaInfo)){
            if(((String)areaInfo.get(AreaManageConstants.ATTRIBUTE_NAME)).equals(newAreaInfo.get(AreaManageConstants.ATTRIBUTE_NAME))
                    && ((String)areaInfo.get(AreaManageConstants.PRODUCT_GROUP_NAME)).equals(newAreaInfo.get(AreaManageConstants.PRODUCT_GROUP_NAME))){

                List<IObjectData> deleteData = Lists.newArrayList();

                for (IObjectData data : storeData) {
                    if(existsAccount.contains(data.get(CoveredStoresConstants.STORE))){
                        deleteData.add(data);
                    }else{
                        data.set(CoveredStoresConstants.BELONG_AREA,newAreaInfo.getId());
                    }
                }
                if(CollectionUtils.isNotEmpty(deleteData)) {
                    storeData.removeAll(deleteData);
                }

                if(CollectionUtils.isNotEmpty(storeData)) {
                    log.info("jmlMoveArea batchUpdate data:{}", storeData.stream().map(o -> o.get(CommonConstants.ID).toString()).collect(Collectors.toList()));
                    serviceFacade.batchUpdate(storeData, controllerContext.getUser());
                }

                if(CollectionUtils.isNotEmpty(deleteData)) {
                    log.info("jmlMoveArea bulkInvalidAndDeleteWithSuperPrivilege data:{}", deleteData.stream().map(o -> o.get(CommonConstants.ID).toString()).collect(Collectors.toList()));
                    serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(deleteData, controllerContext.getUser());
                }
            }else{
                throw new ValidateException("门店转移失败！只能往相同品项和属性的片区转移"); //ignoreI18n
            }
        }else{
            throw new ValidateException("片区有误"); //ignoreI18n
        }

        result.setResult("执行成功！"); //ignoreI18n
        return result;
    }
}
