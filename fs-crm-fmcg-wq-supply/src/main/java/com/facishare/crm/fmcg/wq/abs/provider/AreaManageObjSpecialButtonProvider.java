package com.facishare.crm.fmcg.wq.abs.provider;

import com.facishare.crm.fmcg.wq.MCPreDefineObject;
import com.facishare.crm.fmcg.wq.abs.AbstractAreaSpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AreaManageObjSpecialButtonProvider extends AbstractAreaSpecialButtonProvider {
    @Override
    public String getApiName() {
        return MCPreDefineObject.AreaManage.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        return super.getSpecialButtons();
    }
}
