package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.crm.fmcg.wq.util.CheckinsGrayUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date : 2022/2/17  19:32
 */
public class VisitRouteWebDetailController extends StandardWebDetailController {
    CheckinsDao checkinsDao = SpringUtil.getContext().getBean(CheckinsDao.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        String ea = serviceFacade.getEAByEI(controllerContext.getTenantId());
        if (!CheckinsGrayUtils.yqGray(ea)) {
            ILayout layout = newResult.getLayout().toLayout();
            LayoutExt layoutExt = LayoutExt.of(layout);
            layoutExt.getHeadInfoComponent().ifPresent(x -> {
                x.setButtons(Lists.newArrayList());
                layoutExt.setButtons(Lists.newArrayList());

            });
            layout = layoutExt.getLayout();
            //处理终端逻辑
            if (RequestUtil.isMobileRequest()) {
                layout.setButtons(Lists.newArrayList());
            }
            newResult.setLayout(LayoutDocument.of(layout));
        } else {
            LayoutExt layoutExt = LayoutExt.of(newResult.getLayout().toLayout());
            ObjectDataDocument data = newResult.getData();
            if ("1".equals(data.get("lock_status"))) {
                layoutExt.getHeadInfoComponent().ifPresent(x -> {
                    if (x.getButtons().removeIf(o -> o.getAction().equals(ObjectAction.UNLOCK.getActionCode()))) {
                        x.setButtons(Lists.newArrayList(getButton(ObjectAction.UNLOCK)));
                    } else {
                        x.setButtons(Lists.newArrayList());
                    }

                });
                //处理终端逻辑
                if (CollectionUtils.isNotEmpty(layoutExt.getButtons()) && layoutExt.getButtons().removeIf(o -> o.getAction().equals(ObjectAction.UNLOCK.getActionCode()))) {
                    layoutExt.setButtons(Lists.newArrayList(getButton(ObjectAction.UNLOCK)));
                } else {
                    layoutExt.setButtons(Lists.newArrayList());
                }
            } else {
                Integer checkinsPermission = checkinsDao.getRouteUpdatePermission(controllerContext.getTenantId(),
                        Integer.valueOf(controllerContext.getUser().getUserId()), arg.getObjectDataId());
                List<String> showActionCodes = Lists.newArrayList(ObjectAction.UPDATE.getActionCode(),
                        ObjectAction.LOCK.getActionCode(), ObjectAction.UNLOCK.getActionCode(), ObjectAction.STORE_COPY.getActionCode(), ObjectAction.STORE_TRANSFER.getActionCode());
                //处理web端逻辑
                layoutExt.getHeadInfoComponent().ifPresent(x -> {
                    List<IButton> buttonList = x.getButtons();
                    if(GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea)){
                        if(!showActionCodes.contains(ObjectAction.CLONE.getActionCode())) {
                            showActionCodes.add(ObjectAction.CLONE.getActionCode());
                        }
                    }
                    buttonList.removeIf(o -> !showActionCodes.contains(o.getAction()));
                    if (checkinsPermission == 0) {
                    } else {
                        buttonList.removeIf(o -> o.getAction().equals(ObjectAction.UPDATE.getActionCode()));
                        buttonList.removeIf(o -> o.getAction().equals(ObjectAction.CLONE.getActionCode()));
                        if (checkinsPermission == 1) {
                            buttonList.add(getButton(ObjectAction.UPDATE));
                            if(GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea)){
                                buttonList.add(getButton(ObjectAction.CLONE));
                            }
                        }
                    }
                    x.setButtons(buttonList);
                });
                showActionCodes.remove(ObjectAction.CLONE.getActionCode());
                //处理终端逻辑
                List<IButton> buttonList = layoutExt.getButtons();
                buttonList.removeIf(o -> !showActionCodes.contains(o.getAction()));
                if (checkinsPermission == 0){
                }else{
                    buttonList.removeIf(x -> ObjectAction.UPDATE.getActionCode().equals(x.getAction()));
                    if (checkinsPermission == 1) {
                        buttonList.add(getButton(ObjectAction.UPDATE));
                    }
                }
                layoutExt.setButtons(buttonList);
            }
            newResult.setLayout(LayoutDocument.of(layoutExt));
        }
        return newResult;
    }

    private List getComponents(List components) {
        for (Object component : components) {
            Map com = (Map) component;
            //log.info("getComponents1 com:{}",com);
            if (Objects.equals(com.get("api_name"), "head_info")) {
                ArrayList buttons = (ArrayList) com.get("buttons");
                buttons.removeIf(button -> {
                    Map btn = (Map) (button);
                    //log.info("getComponents1 true:{}","StoreTransfer_button_default".equals(btn.get("api_name").toString()));
                    return "StoreTransfer_button_default".equals(btn.get("api_name").toString()) || "StoreCopy_button_default".equals(btn.get("api_name").toString());
                });
                ((Map) component).put("buttons", buttons);
            }
        }
        return components;
    }

    private void handleUpdatePermission(Arg arg, Result newResult) {
        ILayout layout = newResult.getLayout().toLayout();
        Integer checkinsPermission = checkinsDao.getRouteUpdatePermission(controllerContext.getTenantId(),
                Integer.valueOf(controllerContext.getUser().getUserId()), arg.getObjectDataId());
        if (checkinsPermission != 0) {
            //处理web端逻辑
            LayoutExt layoutExt = LayoutExt.of(layout);
            layoutExt.getHeadInfoComponent().ifPresent(x -> {
                List<IButton> buttonList = x.getButtons();
                buttonList.removeIf(o -> o.getAction().equals(ObjectAction.UPDATE.getActionCode()));
                if (checkinsPermission == 1) {
                    buttonList.add(getButton(ObjectAction.UPDATE));
                }
                layoutExt.setButtons(buttonList);

            });
            layout = layoutExt.getLayout();
            //处理终端逻辑
            if (RequestUtil.isMobileRequest()) {
                List<IButton> buttonList = layout.getButtons();
                buttonList.removeIf(x -> ObjectAction.UPDATE.getActionCode().equals(x.getAction()));
                if (checkinsPermission == 1) {
                    buttonList.add(getButton(ObjectAction.UPDATE));
                }
                layout.setButtons(buttonList);
            }
            newResult.setLayout(LayoutDocument.of(layout));
        }
    }

    private List getButtons(List buttons) {
        buttons.removeIf(button -> {
            Map btn = (Map) (button);
            return "StoreTransfer_button_default".equals(btn.get("api_name").toString()) || "StoreCopy_button_default".equals(btn.get("api_name").toString());
        });
        return buttons;
    }

    public IButton getButton(ObjectAction objectAction) {
        IButton button = new Button();
        button.setLabel(objectAction.getActionLabel());
        button.setAction(objectAction.getActionCode());
        button.setName(objectAction.getButtonApiName());
        button.setActionType("default");
        return button;
    }
}
