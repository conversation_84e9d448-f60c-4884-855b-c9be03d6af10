package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.ProductCollectionObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.service.OrganizationJMLService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;


/**
 *产品组新增校验
 */
@SuppressWarnings("Duplicates")
public class ProductCollectionAddAction extends BaseProductCollectionAction {

    public static final Logger log = LoggerFactory.getLogger(ProductCollectionAddAction.class);
    private static final OrganizationJMLService organizationJMLService = SpringUtil.getContext().getBean(OrganizationJMLService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected void before(Arg arg) {
        if (GrayRelease.isAllow("checkin-server-v2", "isYLProductCollection", actionContext.getEa())) {
            Object o = arg.getObjectData().get(ProductCollectionObjConstants.Field.departmentId.getApiName());
            if (Objects.isNull(o)) {
                throw new ValidateException("服务处不能为空"); //ignoreI18n
            }
            //自动创建 供货关系
            String recordType = arg.getObjectData().get(BaseField.recordType.getApiName()).toString();
            String tenantId = actionContext.getTenantId();

            if (recordType
                    .equals(ProductCollectionObjConstants.Value.recordType_distributor.getValue())) {
                throw new ValidateException("不能直接创建分销商的经营范围"); //ignoreI18n
            }
            List<String> departmentIds = (List<String>) arg.getObjectData().get(ProductCollectionObjConstants.Field.departmentId.getApiName());
            List<String> serviceCenterDepartmentIds = supplyDao.getServiceCenterDepartmentIds(tenantId, departmentIds);

            arg.getObjectData().put(ProductCollectionObjConstants.Field.departmentId.getApiName(),serviceCenterDepartmentIds);
            if (recordType
                    .equals(ProductCollectionObjConstants.Value.recordType_dealer.getValue())) {
                //经销商的范围 创建供货关系
                String leaderId = arg.getObjectData().get(ProductCollectionObjConstants.Field.dealerId.getApiName()).toString();
                if (StringUtils.isEmpty(leaderId)){
                    throw new ValidateException("经销商id不能为空"); //ignoreI18n
                }
                //预置 服务处的
                supplyDao.getProductCollectionObjByDepartmentIds(tenantId,serviceCenterDepartmentIds,false);
                List<IObjectData> iObjectDataList = supplyDao.queryCollectionObjByLeaderIds(tenantId, Lists.newArrayList(leaderId));
                AccountObj accountObjById = supplyDao.getAccountObjById(tenantId, leaderId);
                if (!CollectionUtils.isEmpty(iObjectDataList)){
                    throw new ValidateException(String.format("该经销商 %s 已经存在经营范围",accountObjById.getName())); //ignoreI18n
                }
                //默认归属部门是 服务处
                arg.getObjectData().put(BaseField.dataOwnDepartment.getApiName(),
                        Lists.newArrayList(accountObjById.getDepartmentId()));

                supplyDao.getDealerSupplyBySuppliers(tenantId,
                        Lists.newArrayList(leaderId));

            } else if (recordType
                    .equals(ProductCollectionObjConstants.Value.recordType_server_center.getValue())) {
                if (!new HashSet<>(departmentIds).containsAll(serviceCenterDepartmentIds)){
                    throw new ValidateException("服务处，不是服务处级别部门"); //ignoreI18n
                }
                List<IObjectData> iObjectDataList = supplyDao.queryProductCollectionByDepIds(tenantId, serviceCenterDepartmentIds);
                if (!CollectionUtils.isEmpty(iObjectDataList)){
                    throw new ValidateException("该服务处已经存在经营范围"); //ignoreI18n
                }
                arg.getObjectData().put(BaseField.dataOwnDepartment.getApiName(),
                        arg.getObjectData().get(ProductCollectionObjConstants.Field.departmentId));
            }
//            检验不能重复添加的逻辑

        } else {
            validateData(arg);
        }
        super.before(arg);
    }
    /**
     * 验证重复
     * @return
     */
//    @Override
//    protected List<DuplicateSearchResult.DuplicateData> getDuplicateData() {
//        List<DuplicateSearchResult.DuplicateData> duplicateData = super.getDuplicateData();
//        if (GrayRelease.isAllow("checkin-server-v2", "isYLProductCollection",actionContext.getEa())) {
//            //自动创建 供货关系
//            String recordType = arg.getObjectData().get(BaseField.recordType.getApiName()).toString();
//            String tenantId = actionContext.getTenantId();
//            List<IObjectData> duplicatProductCollectionObjs = null;
//            if (recordType
//                    .equals(ProductCollectionObjConstants.Value.recordType_dealer.getValue())
//                    || recordType
//                    .equals(ProductCollectionObjConstants.Value.recordType_distributor.getValue())){
//                //经销商的范围 创建供货关系
//                String leaderId = arg.getObjectData().get(ProductCollectionObjConstants.Field.dealerId.getApiName()).toString();
//                supplyDao.getDealerSupplyBySuppliers(tenantId,
//                        Lists.newArrayList(leaderId));
//
//                duplicatProductCollectionObjs = supplyDao.getDuplicatProductCollectionObjs(tenantId, arg.getObjectData().getId(), leaderId);
//            }else{
//                //业务处
//                duplicatProductCollectionObjs = supplyDao.getDuplicatProductCollectionObjs(tenantId, arg.getObjectData().getId(),
//                        ((List)arg.getObjectData().get(ProductCollectionObjConstants.Field.departmentId.getApiName())).get(0).toString()
//                        );
//            }
//            //检验不能重复添加的逻辑
//            if (!CollectionUtils.isEmpty(duplicatProductCollectionObjs)){
//                if (CollectionUtils.isEmpty(duplicateData)){
//                    duplicateData = Lists.newArrayList();
//                }
//                DuplicateSearchResult.DuplicateData newDuplicateData = new DuplicateSearchResult.DuplicateData();
//                newDuplicateData.setApiName(ProductCollectionObjConstants.API_NAME);
//                List<String> dataIds = duplicatProductCollectionObjs.stream().map(o -> o.getId()).collect(Collectors.toList());
//                newDuplicateData.setDataIds(dataIds);
//
//                if (duplicateData.stream().anyMatch(o->o.getApiName().equals(ProductCollectionObjConstants.API_NAME))){
//                    duplicateData.stream().forEach(o->{
//                        if (o.getApiName().equals(ProductCollectionObjConstants.API_NAME)){
//                            o.getDataIds().addAll(dataIds);
//                            o.setDataIds(o.getDataIds().stream().distinct().collect(Collectors.toList()));
//                        }
//                    });
//                }else{
//                    duplicateData.add(newDuplicateData);
//                }
//            }
//        }
//        return duplicateData;
//    }
//
//    /**
//     *
//     * @param arg
//     */
//    @Override
//    protected void validateData(Arg arg){
//        departmentId_name = ProductCollectionConstants.COUNTY_LEVEL_OFFICE;
//        super.validateData(arg);
//    }



}
