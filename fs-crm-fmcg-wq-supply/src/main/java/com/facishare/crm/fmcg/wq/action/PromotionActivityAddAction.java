package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromotionActivityAddAction extends FmcgSkipPermissionAddAction {

SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        // 验证活动的开始时间一定小于结束时间
        Long startDate = Long.valueOf(arg.getObjectData().get("start_date").toString());
        Long endDate = Long.valueOf(arg.getObjectData().get("end_date").toString());
        if (startDate > endDate){
            throw new ValidateException("活动开始时间不能小于结束时间"); //ignoreI18n
        }
//        String activityStatus = "closed";
//        long nowDate = DateUtils.getDateTimeFormat(System.currentTimeMillis(), DateUtils.DateFormat);
//        if (startDate > nowDate) {
//            activityStatus = "noStarted";
//        } else if (endDate < nowDate) {
//            activityStatus = "closed";
//        } else if (endDate - startDate >= 0) {
//            activityStatus = "processing";
//        }
//        arg.getObjectData().put("activity_status",activityStatus);
    }

}
