package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.AddDealerSupplyFilter;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.ProductCollectionObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.DistributorSupply;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.model.obj.ProductCollectionObj;
import com.facishare.crm.fmcg.wq.model.obj.ShopProductSupplierRelationshipObj;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description: 给店铺分配 供应商 最早旧版的逻辑 不使用了 不要启用
 * @author: zhangsm
 * @create: 2021-05-08 15:25
 **/
public class DealerSupplyAddFilterController extends PreDefineController<AddDealerSupplyFilter.Arg, AddDealerSupplyFilter.Result> {
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected AddDealerSupplyFilter.Result doService(AddDealerSupplyFilter.Arg arg) {
        AddDealerSupplyFilter.Result result = new AddDealerSupplyFilter.Result();
        String tenantId = controllerContext.getTenantId();
        AccountObj thisAccountObj = supplyDao.getAccountObjById(tenantId, arg.getAccountId());
        if (AccountObjConstants.Value.default__c.toString().equals(thisAccountObj.getRecordType())) {
            //门店
        } else {
            throw new ValidateException(String.format("%s 业务类型不是门店无法分配门店的配送商", thisAccountObj.getName())); //ignoreI18n
        }
        //查询当前客户的供货关系
        List<ShopProductSupplierRelationshipObj> supplyStoresByShopIds = supplyDao.getSupplyStoresByShopId(tenantId, arg.getAccountId());
        result.setSelectedCount(supplyStoresByShopIds.size());
        Set<String> usedSupplierIds = supplyStoresByShopIds.stream().map(o -> o.getSupplierId()).collect(Collectors.toSet());
        if (StringUtils.isNotBlank(arg.getSupplierId())) {
            // 判断 里面 如果查询不到会判断
            ProductCollectionObj productCollectionObjByAccountId =
                    supplyDao.getProductCollectionObjByAccountId(tenantId,
                            arg.getSupplierId(), false);
            //通过 经销商id 获取所有的配送商数据 再加上当前的经销商

            //通过上游查询下游分销商 和当前经销商
            List<DistributorSupply> relationListByUpIds = supplyService.getRelationListByUpIds(tenantId, Lists.newArrayList(arg.getSupplierId()));
        //去掉 filter
//            List<String> distributorIdList = relationListByUpIds.stream().filter(o -> !usedSupplierIds.contains(o.getThisSupplierId())).map(o -> o.getThisSupplierId()).collect(Collectors.toList());
            List<String> distributorIdList = relationListByUpIds.stream().map(o -> o.getThisDistributorAccountId()).collect(Collectors.toList());
            Map<String, AccountObj> map = supplyDao.getAccountObjByIdsIgnoreException(tenantId, distributorIdList)
                    .stream().collect(Collectors.toMap(o -> o.getId(), v -> v));
            // 增加
            List<ObjectDataDocument> distributorInfos = distributorIdList.stream().filter(o->map.containsKey(o)).map(o -> {
                ObjectDataDocument obj = new ObjectDataDocument();
                obj.put(BaseField.id.getApiName(), o);
                obj.put(BaseField.name.getApiName(), map.get(o).getName());
                return obj;
            }).collect(Collectors.toList());
            AccountObj dealerObj = supplyDao.getAccountObjById(tenantId, arg.getSupplierId());
//            if (!usedSupplierIds.contains(dealerObj.getId())) {
                ObjectDataDocument dealerDoc = new ObjectDataDocument();
                dealerDoc.put(BaseField.id.getApiName(), dealerObj.getId());
                dealerDoc.put(BaseField.name.getApiName(), dealerObj.getName());
                distributorInfos.add(0, dealerDoc);
//            }
            result.setDistributorInfos(distributorInfos);
            result.setTotal(distributorInfos.size());
        } else {
            //
            Set<String> departmentIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(thisAccountObj.getOtherDepartmentIds())) {
                departmentIds.addAll(thisAccountObj.getOtherDepartmentIds());
            }
            //服务处部门
            List<String> serviceCenterDepartmentIds = supplyDao.getServiceCenterDepartmentIds(tenantId, Lists.newArrayList(departmentIds));
            //通过服务处部门查经销商  不去除 已经使用的了
            List<ObjectDataDocument> infos = supplyDao.getSupplyDealerListByServiceCenterDepartmentIds(tenantId, SetUtils.EMPTY_SET, serviceCenterDepartmentIds);
            result.setDealerInfos(infos);
            result.setTotal(infos.size());
            return result;
        }
        return result;
    }


    private void getV1(AddDealerSupplyFilter.Result result, AccountObj thisAccountObj, Set<String> usedSupplierIds) {
        //获取客户的服务处，根据服务处获取所有的经销商
        Set<String> departmentIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(thisAccountObj.getOtherDepartmentIds())) {
            departmentIds.addAll(thisAccountObj.getOtherDepartmentIds());
        }
        if (StringUtils.isNotBlank(thisAccountObj.getDepartmentId())) {
            departmentIds.add(thisAccountObj.getDepartmentId());
        } else {
            throw new ValidateException(String.format("%s 未设置归属的业务处，无法继续", thisAccountObj.getName())); //ignoreI18n

        }
        SearchQuery.SearchQueryBuilder searchQueryBuilder = SearchQuery.builder()
                .in(ProductCollectionObjConstants.Field.departmentId.getApiName(), departmentIds)
                .eq(BaseField.recordType.getApiName(), ProductCollectionObjConstants.Value.recordType_dealer.getValue());
        if (CollectionUtils.isNotEmpty(usedSupplierIds)) {
            searchQueryBuilder.nin(ProductCollectionObjConstants.Field.dealerId.getApiName(), usedSupplierIds);
        }
        SearchQuery searchQuery = searchQueryBuilder.build();
        List<IObjectData> data = supplyDao.getAllIObjectDataListByQuery(User.systemUser(controllerContext.getTenantId()), searchQuery, ProductCollectionObjConstants.API_NAME);

        if (CollectionUtils.isEmpty(data)) {
            throw new ValidateException(String.format("%s 业务处经营范围未查到，无法继续", thisAccountObj.getName())); //ignoreI18n
        }
        List<ObjectDataDocument> infos = data.stream().map(o -> {
            ObjectDataDocument obj = new ObjectDataDocument();
            obj.put(BaseField.id.getApiName(), o.get(ProductCollectionObjConstants.Field.dealerId.getApiName(), String.class));
            //todo 经销商的还得查，查不到，直接用 范围的名字了
            obj.put(BaseField.name.getApiName(), o.getName());
            return obj;
        }).collect(Collectors.toList());
        result.setDealerInfos(infos);
        result.setTotal(data.size());
    }
}
