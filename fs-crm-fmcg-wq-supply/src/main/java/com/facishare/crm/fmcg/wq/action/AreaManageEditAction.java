package com.facishare.crm.fmcg.wq.action;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.appserver.checkins.api.model.AreaPersonnelConfigPo;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.crm.fmcg.wq.model.PostInfo;
import com.facishare.crm.fmcg.wq.service.OrganizationJMLService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Slf4j
public class AreaManageEditAction extends FmcgSkipPermissionEditAction {
    private static final OrganizationJMLService organizationJMLService = SpringUtil.getContext().getBean(OrganizationJMLService.class);

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    CheckinsDao checkinsDao = SpringUtil.getContext().getBean(CheckinsDao.class);

    @Override
    protected void before(Arg arg) {
        if(GrayRelease.isAllow("checkin-server-v2","areaManage",actionContext.getEa())) {
            arg.getObjectData().put(AreaManageConstants.DATA_OWN_DEPARTMENT, arg.getObjectData().get(AreaManageConstants.COUNTY__C));
            log.info("AreaManageEditAction edit !!");
        }
        if(GrayRelease.isAllow("checkin-server-v2","areaManage",actionContext.getEa()) || GrayRelease.isAllow("checkin-server-v2","isYLArea",actionContext.getEa())) {
            //同一个人只能新建一个片区
            validateData(arg);
        }else {
            buildAreaManageOwner(arg);
            LinkedHashMap<String, AreaPersonnelConfigPo> personnelApiNameAndConfig = checkinsDao.getAreaPersonnelSetting(actionContext.getTenantId()).getPersonnelApiNameAndConfig();
            if(Objects.nonNull(arg.getObjectData().get("adminMultiArea"))){
                Map<String, PostInfo> personnelAndMultiAreaMap = JSONObject.parseObject(JSONObject.toJSONString(arg.getObjectData().get("adminMultiArea")), new TypeReference<Map<String,PostInfo>>() {});
                personnelAndMultiAreaMap.forEach((k,v)->{
                    if(MapUtils.isNullOrEmpty(personnelApiNameAndConfig) || Objects.isNull(personnelApiNameAndConfig.get(k)) || !personnelApiNameAndConfig.get(k).getIsStrideArea()) {
                        v.setApiName(k);
                        validateDataNew(arg, v);
                    }
                });
            }
            arg.getObjectData().remove("adminMultiArea");
        }
        validateDetailData(arg);
        super.before(arg);
    }

    private void buildAreaManageOwner(Arg arg){
        List<String> areaOwnerList = (List<String>)arg.getObjectData().get(AreaManageConstants.AREA_OWNER_PRESER);
        if(!CollectionUtils.isEmpty(areaOwnerList)){
            arg.getObjectData().put(AreaManageConstants.OWNER,areaOwnerList);
        }
        // 先取部门负责人
        List<String> departIds = (List<String>)arg.getObjectData().get(AreaManageConstants.BUSINESS_GROUP_PRESET);
        if(!CollectionUtils.isEmpty(departIds)){
            List<IObjectData> departmentDataList = supplyDao.getDepartmentList(actionContext.getTenantId(),departIds);
            List<String> managerIds = (List<String>)departmentDataList.get(0).get("manager_id");
            if(!CollectionUtils.isEmpty(managerIds)){
                arg.getObjectData().put(AreaManageConstants.OWNER,managerIds);
            }
        }
        List<String> ownerList = (List<String>)arg.getObjectData().get(AreaManageConstants.OWNER);
        if(CollectionUtils.isEmpty(ownerList)){
            arg.getObjectData().put(AreaManageConstants.OWNER,Lists.newArrayList(actionContext.getUser().getUserId()));
        }
    }

    /**
     * 校验从对象 数据
     * @param arg
     */
    private void validateDetailData(Arg arg) {
        //校验不能重复
        if(!CollectionUtils.isEmpty(arg.getDetails())) {
            if (!CollectionUtils.isEmpty(arg.getDetails().get(CoveredStoresConstants.COVERED_STORES_OBJ))) {
                List<String> coveredIds = arg.getDetails().get(CoveredStoresConstants.COVERED_STORES_OBJ).stream().filter(o -> Objects.nonNull(o.get(CoveredStoresConstants.STORE))).map(o -> o.get(CoveredStoresConstants.STORE).toString()).collect(Collectors.toList());
                log.info("validateDetailData coveredIds:{}",coveredIds);
                long count = coveredIds.stream().distinct().count();
                if(coveredIds.size() != count){
                    throw new ValidateException("覆盖门店中含有相同的门店，请检查！"); //ignoreI18n
                }
            }
        }

//        if(!CollectionUtils.isEmpty(arg.getDetails())) {
//            String productGroup = (String) arg.getObjectData().get(AreaManageConstants.PRODUCT_GROUP);
//            String areaAttribute = (String) arg.getObjectData().get(AreaManageConstants.AREA_ATTRIBUTE);
//            //获相同片区
//            SearchTemplateQuery query = new SearchTemplateQuery();
//
//            query.setLimit(0);
//            query.setOffset(0);
//
//            Filter productGroupFilter = new Filter();
//            productGroupFilter.setFieldName(AreaManageConstants.PRODUCT_GROUP);
//            productGroupFilter.setOperator(Operator.EQ);
//            productGroupFilter.setFieldValues(Lists.newArrayList(productGroup));
//
//            Filter areaFilter = new Filter();
//            areaFilter.setFieldName(AreaManageConstants.AREA_ATTRIBUTE);
//            areaFilter.setOperator(Operator.EQ);
//            areaFilter.setFieldValues(Lists.newArrayList(areaAttribute));
//
//            query.setFilters(Lists.newArrayList(productGroupFilter, areaFilter));
//
//            List<IObjectData> areas = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
//            List<String> areaIds = areas.stream().map(o -> o.getId()).collect(Collectors.toList()); //相同片区id
//
//            if (!CollectionUtils.isEmpty(areaIds)) {
//                if (!CollectionUtils.isEmpty(arg.getDetails().get(CoveredStoresConstants.COVERED_STORES_OBJ))) {
//                    List<String> coveredIds = arg.getDetails().get(CoveredStoresConstants.COVERED_STORES_OBJ).stream().filter(o -> Objects.isNull(o.getId())).map(o -> o.get(CoveredStoresConstants.STORE).toString()).collect(Collectors.toList());
//                    if (!CollectionUtils.isEmpty(coveredIds)) {
//                        SearchTemplateQuery queryCheck = new SearchTemplateQuery();
//
//                        queryCheck.setLimit(0);
//                        queryCheck.setOffset(0);
//
//                        Filter storeFilter = new Filter();
//                        storeFilter.setFieldName(CoveredStoresConstants.STORE);
//                        storeFilter.setOperator(Operator.IN);
//                        storeFilter.setFieldValues(coveredIds);
//
//                        Filter belongAreaFilter = new Filter();
//                        belongAreaFilter.setFieldName(CoveredStoresConstants.BELONG_AREA);
//                        belongAreaFilter.setOperator(Operator.IN);
//                        belongAreaFilter.setFieldValues(areaIds);
//
//                        queryCheck.setFilters(Lists.newArrayList(storeFilter, belongAreaFilter));
//
//                        List<IObjectData> checkDatas = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), CoveredStoresConstants.COVERED_STORES_OBJ, queryCheck).getData();
//                        if (!CollectionUtils.isEmpty(checkDatas) && checkDatas.size() > 0) {
//                            throw new ValidateException("同一门店不能添加到相同的属性和品项中");
//                        }
//                    }
//                }
//            }
//        }
    }
    /**
     * 当指定人员，已经存在相同产品组和属性的片区时，不允许重复创建。
     *
     * @param arg
     */
    private void validateData(Arg arg){

        String area_number = (String) arg.getObjectData().get(AreaManageConstants.AREA_NUMBER );//片区编码
        List<String> dealerArray = (List<String>) arg.getObjectData().get(AreaManageConstants.DEALER);//经销商
        List<String> areaOwnerArray = (List<String>) arg.getObjectData().get(AreaManageConstants.AREA_OWNER);//片区负责人
//        if (CollectionUtils.isEmpty(dealerArray)) {
//            throw new ValidateException("经销商不能为空");
//        }

        //查询经销商上级部门
       /* if(!CollectionUtils.isEmpty(dealerArray)) {
            List<DepartmentDto> departmentIds = organizationJMLService.getDepartmentIds(actionContext.getUser().getTenantIdInt(), dealerArray.get(0));
            if (!CollectionUtils.isEmpty(departmentIds)) {
                if (departmentIds.size() != 6) {
                    throw new ValidateException("请选择六级经销商");
                }
                SearchTemplateQuery query = new SearchTemplateQuery();
                query.setLimit(1);
                query.setOffset(0);

                Filter productGroupFilter = new Filter();
                productGroupFilter.setFieldName(AreaManageConstants.AREA_NUMBER);
                productGroupFilter.setOperator(Operator.EQ);
                productGroupFilter.setFieldValues(Lists.newArrayList(area_number));

//            Filter areaAttributeFilter = new Filter();
//            areaAttributeFilter.setFieldName(AreaManageConstants.AREA_ATTRIBUTE);
//            areaAttributeFilter.setOperator(Operator.EQ);
//            areaAttributeFilter.setFieldValues(Lists.newArrayList(areaAttribute));
                query.setFilters(Lists.newArrayList(productGroupFilter));

                List<IObjectData> getData = serviceFacade.findBySearchQuery(actionContext.getUser(), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
                log.info("AreaManageEditAction:Uniqueness check  getData : {}, query : {}", getData, query);
//            if (!CollectionUtils.isEmpty(getData)) {
//                IObjectData objectData = getData.get(0);
//                List<String> oldDepartment = (List<String>) objectData.get(AreaManageConstants.DATA_OWN_DEPARTMENT);
//                if (!Integer.valueOf(oldDepartment.get(0)).equals(departmentIds.get(departmentIds.size() - 1).getDepartmentId())) {
//                    throw new ValidateException("请选择县级所下的经销商");
//                }
//            }
            } else {
                throw new ValidateException("请选择六级经销商");
            }
        }*/

        if (!CollectionUtils.isEmpty(areaOwnerArray)) {
//            Integer areaOwnerDepartmentId = organizationJMLService.getDepartmentId(actionContext.getUser().getTenantIdInt(), Integer.valueOf(areaOwnerArray.get(0)));
//            if (areaOwnerDepartmentId!=-1) {
//                if (!Integer.valueOf(dealerArray.get(0)).equals(areaOwnerDepartmentId)) {
//                    log.info("AreaManageEditAction:Uniqueness check  片区负责人的部门 : {}", areaOwnerDepartmentId);
//                    throw new ValidateException("请选择经销商下的片区负责人");
//                }
//            }else{
//                throw new ValidateException("请选择经销商下的片区负责人");
//            }

            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(1);
            query.setOffset(0);

            Filter productGroupFilter = new Filter();
            productGroupFilter.setFieldName(AreaManageConstants.AREA_NUMBER);
            productGroupFilter.setOperator(Operator.NEQ);
            productGroupFilter.setFieldValues(Lists.newArrayList(area_number));

            Filter ownerFilter = new Filter();
            ownerFilter.setFieldName(AreaManageConstants.AREA_OWNER);
            ownerFilter.setOperator(Operator.EQ);
            ownerFilter.setFieldValues(Lists.newArrayList(areaOwnerArray.get(0)));

            query.setFilters(Lists.newArrayList(productGroupFilter,ownerFilter));

            List<IObjectData> getData = serviceFacade.findBySearchQuery(actionContext.getUser(), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
            log.info("AreaManageEditAction:Uniqueness check  getData : {}, query : {}", getData, query);
            if (!CollectionUtils.isEmpty(getData)) {
                throw new ValidateException("该人员已规划片区，请不要重复创建！"); //ignoreI18n
            }
        }
    }

    protected void validateDataNew(Arg arg,PostInfo postInfo) {
        String area_number = (String) arg.getObjectData().get(AreaManageConstants.AREA_NUMBER );//片区编码
        List<String> areaOwnerArray = (List<String>) arg.getObjectData().get(postInfo.getApiName());//片区负责人
        if (!CollectionUtils.isEmpty(areaOwnerArray)) {
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(1);
            query.setOffset(0);
            query.setNeedReturnCountNum(false);
            Filter ownerFilter = new Filter();
            ownerFilter.setFieldName(postInfo.getApiName());
            ownerFilter.setOperator(Operator.EQ);
            ownerFilter.setFieldValues(areaOwnerArray);

            Filter productGroupFilter = new Filter();
            productGroupFilter.setFieldName(AreaManageConstants.AREA_NUMBER);
            productGroupFilter.setOperator(Operator.NEQ);
            productGroupFilter.setFieldValues(Lists.newArrayList(area_number));

            query.setFilters(Lists.newArrayList(productGroupFilter,ownerFilter));

            List<IObjectData> getData = serviceFacade.findBySearchQuery(actionContext.getUser(), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
            log.info("AreaManageAdd:Uniqueness check  getData : {}, query : {}", getData, query);
            if (!CollectionUtils.isEmpty(getData)) {
                throw new ValidateException(postInfo.getUserName() + "已有片区，不可重复分配片区，请修改。"); //ignoreI18n
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        try {
            // 查询客户数据 更新数据负责人和路线负责人

            if(!GrayRelease.isAllow("checkin-server-v2", "areaManage", actionContext.getEa())) {
                Boolean isYL = GrayRelease.isAllow("checkin-server-v2", "isYLArea", actionContext.getEa());
                List<ObjectDataDocument> iObjectDataList = arg.getDetails().get("CoveredStoresObj");
                List<String> accountIdList = Lists.newArrayList();
                if(!CollectionUtils.isEmpty(iObjectDataList)) {
                    accountIdList = iObjectDataList.stream().map(o -> String.valueOf(o.get("store"))).collect(Collectors.toList());
                    supplyDao.updateAccountAreaInfo(actionContext.getTenantId(), actionContext.getUser().getUserIdInt(), arg.getObjectData().toObjectData(), accountIdList, isYL);
                }

                if (!CollectionUtils.isEmpty(detailsToDelete)) {
                    List<String> ids = detailsToDelete.stream().filter(o -> Strings.isNotEmpty(o.get(CoveredStoresConstants.STORE, String.class))).map(o -> o.get(CoveredStoresConstants.STORE, String.class)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(ids)) {
                        if (!CollectionUtils.isEmpty(accountIdList)) {
                            ids.removeAll(accountIdList);
                        }
                        supplyDao.updateAccountAreaInfo(actionContext.getTenantId(), actionContext.getUser().getUserIdInt(), null, ids,isYL);
                    }
                }
            }
        }catch (Exception e){
            log.error("AreaManageAddAction is error",e);
        }
        return result;
    }
}
