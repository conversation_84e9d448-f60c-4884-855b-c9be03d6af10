package com.facishare.crm.fmcg.wq.api.supply;

import com.fxiaoke.api.model.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface ShopQueryDealerSupply {
    @Data
    @ToString
    class Arg implements Serializable {


        private String shopId;//

        private List<String> productIdList;

    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {
        /**
         * key是产品id
         * value 供应商对象
         */
        private Map<String,Supplier> productIdAndSupplierMap;
    }
    @Data
    class Supplier{
        private String dealerId;


        private String distributorId ;
    }

}
