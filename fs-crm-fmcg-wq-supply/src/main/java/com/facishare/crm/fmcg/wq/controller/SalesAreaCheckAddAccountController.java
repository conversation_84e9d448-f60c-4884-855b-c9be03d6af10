package com.facishare.crm.fmcg.wq.controller;

import com.facishare.appserver.checkins.model.enums.CustomGray;
import com.facishare.crm.fmcg.wq.api.special.SalesAreaCheckAddAccount;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.service.MNService;
import com.facishare.crm.fmcg.wq.service.SalesAreaService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/06/03/ 21:15
 **/
public class SalesAreaCheckAddAccountController extends PreDefineController<SalesAreaCheckAddAccount.Arg, SalesAreaCheckAddAccount.Result> {


    private ServiceFacade serviceFacade = SpringUtil.getContext().getBean(ServiceFacade .class);
    private SalesAreaService salesAreaService = SpringUtil.getContext().getBean(SalesAreaService .class);
    private MNService mnServiceImpl = SpringUtil.getContext().getBean(MNService .class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected SalesAreaCheckAddAccount.Result doService(SalesAreaCheckAddAccount.Arg arg) {
        if(arg.isSkipSalesAreaCheck){
            return new SalesAreaCheckAddAccount.Result();
        }
        String eid = controllerContext.getTenantId();
        String ea = serviceFacade.getEAByEI(eid);
        String upEi = String.valueOf(mnServiceImpl.getUpEiForMn(Integer.parseInt(eid)));
        String upEa = serviceFacade.getEAByEI(upEi);
        if(!CustomGray.EA.salesAreaForMn.grayAndEnv(upEa)){
            return new SalesAreaCheckAddAccount.Result();
        }
        boolean isOneTerminal = upEi.equals(eid);

        IObjectData accData = salesAreaService.getUpAccount(upEi,ea);
        Object controlSalesArea = Objects.nonNull(accData) ? accData.get(AccountObjConstants.Field.CONTROL_SALES_AREA.getApiName()) : null;
        boolean isControlSalesArea = isOneTerminal || (Objects.nonNull(controlSalesArea) && Boolean.parseBoolean(String.valueOf(controlSalesArea)));

        // 新建渠道客户
        Object recordType = arg.getObjectData().get(CommonConstants.RECORD_TYPE);
        List<String> noChannelRecordList = salesAreaService.getNoChannelAccountRecordTypeList(ea);

        if(!isControlSalesArea){
            // 不受销售区域控制的返回
            return new SalesAreaCheckAddAccount.Result();
        }

        // 新建特定业务类型的 需要校验销售区域
        if(Objects.nonNull(recordType) && Objects.nonNull(noChannelRecordList) && !noChannelRecordList.contains(recordType.toString())) {
            salesAreaService.checkSalesAreaOnAddOrEditAcc(controllerContext.getTenantId(), arg.getObjectData(),isOneTerminal);
        }

        // 判断新建客户是否处于销售区域内
        if(!isOneTerminal && Objects.nonNull(recordType) && Objects.nonNull(noChannelRecordList) && noChannelRecordList.contains(recordType.toString())) {
            List<String> coveringSalesAreas = (List<String>) accData.get(AccountObjConstants.Field.coveringSalesAreas.getApiName());
            if(CollectionUtils.isNotEmpty(coveringSalesAreas)){
                salesAreaService.checkOnSalesArea(Integer.parseInt(eid),(String) arg.getObjectData().get(AccountObjConstants.Field.location.getApiName()),coveringSalesAreas);
            }else{
                log.info("coveringSalesAreas is null");
            }
        }

        return new SalesAreaCheckAddAccount.Result();
    }

}
