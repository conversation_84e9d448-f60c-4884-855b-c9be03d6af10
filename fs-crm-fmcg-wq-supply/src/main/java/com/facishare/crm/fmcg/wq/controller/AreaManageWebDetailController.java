package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

public class AreaManageWebDetailController extends StandardWebDetailController {

    private static final Logger logger = LoggerFactory.getLogger(AreaManageWebDetailController.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg,result);
        ILayout layout = result.getLayout().toLayout();
        try {
            List<String> removeButtonApiNameList = ObjectUtils.buttonList.stream().map(o->o.getButtonApiName()).collect(Collectors.toList());
            List<IComponent> componentList = layout.getComponents();
            for (IComponent component : componentList) {
                if("head_info".equals(component.getName())){
                    List<IButton> buttons = component.getButtons();
                    Iterator<IButton> iterator = buttons.iterator();
                    while (iterator.hasNext()){
                        IButton iButton = iterator.next();
                        if(removeButtonApiNameList.contains(iButton.getName())){
                            iterator.remove();
                        }
                    }
                    component.setButtons(buttons);
                    break;
                }
            }
            layout.setComponents(componentList);
        } catch (Exception e) {
            logger.error("AreaManageWebDetailController remove button is error ei:{}",controllerContext.getTenantId(),e);
        }
        result.setLayout(LayoutDocument.of(layout));
        return result;
    }

}
