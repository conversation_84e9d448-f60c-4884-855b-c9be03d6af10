package com.facishare.crm.fmcg.wq.api.sync;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.api.model.BaseResult;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

public interface AvailableRange {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "supply_id")
        @JsonProperty("supply_id")
        @SerializedName("supply_id")
        private String supplyId;
        private boolean async;

        @JSONField(name = "sub_account_id")
        @JsonProperty("sub_account_id")
        @SerializedName("sub_account_id")
        private String subAccountId;
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {

    }
}
