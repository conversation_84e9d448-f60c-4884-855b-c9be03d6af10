package com.facishare.crm.fmcg.wq.model.obj;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 业务范围 对象数据
 * @author: zhangsm
 * @create: 2021-04-25 14:25
 **/
@Data
public class ProductCollectionObj {

    /**
     * 经营范围Id
     */
    private String scopeId;


    /**
     * 是否是继承的父范围所有
     */
    private boolean isAllParentProduct ;
    /**
     * 产品列表
     */
    private List<ProductObj> productList = new ArrayList<>();
    /**
     * 部门id
     */
    private String departmentId;
    /**
     * 经销商
     */
    private String dealerId;

    /**
     * 分销商
     */
    private String distributorId;
}
