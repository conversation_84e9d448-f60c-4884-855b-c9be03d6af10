package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterAsyncBulkPromoterAgreeAction extends AbstractStandardAsyncBulkAction<PromoterAsyncBulkPromoterAgreeAction.Arg, PromoterPromoterAgreeAction.Arg> {



    @Override
    protected String getDataIdByParam(PromoterPromoterAgreeAction.Arg arg) {
        return arg.getObjectDataId();
    }

    @Override
    protected List<PromoterPromoterAgreeAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(id -> {
                    PromoterPromoterAgreeAction.Arg arg = new PromoterPromoterAgreeAction.Arg();
                    arg.setObjectDataId(id);
                    return arg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.PROMOTER_AGREE.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.PROMOTER_AGREE.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.PROMOTER_AGREE.getActionCode());
    }

    @Data
    public static class Arg {
        private List<String> dataIds;
    }

}
