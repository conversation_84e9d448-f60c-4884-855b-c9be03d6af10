package com.facishare.crm.fmcg.wq.action;


import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.SpecialSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.model.obj.ShopProductSupplierRelationshipObj;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 特例供货作废
 * @author: zhangsm
 * @create: 2021-04-29 14:42
 **/
public class SpecialSupplyInvalidAction extends StandardInvalidAction {

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);

    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //初始化审批流上下文
        supplyOperaContext.setArgs(arg);
        supplyOperaContext.setResultClazz(Result.class);
        IObjectData byId = supplyDao.getById(actionContext.getTenantId(), SpecialSupplyObjConstants.API_NAME, arg.getObjectDataId());
        supplyOperaContext.setOutUpCustomerId(byId.get(SpecialSupplyObjConstants.Field.dealerId.getApiName()).toString());
        supplyOperaContext.setOutUpSupplyId(byId.get(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName()).toString());
        supplyOperaContext.setCustermerIds(Lists.newArrayList(byId.get(SpecialSupplyObjConstants.Field.shopId.getApiName()).toString()));
        supplyOperaContext.setProductIds(Lists.newArrayList(byId.get(SpecialSupplyObjConstants.Field.productId.getApiName()).toString()));
        //触发审批流程
        Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(actionContext, supplyOperaContext);
        if (booleanIObjectDataPair.first){
            throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE,SupplyOperaContext.TRIGGERAPPROCODE);
        }

    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        ObjectDataDocument objectData = after.getObjectData();
        //如果没有其他的特例供货 则删除 特殊门店 或者 供货分销邮差商
        String tenantId = actionContext.getTenantId();
        String shopId = objectData.get(SpecialSupplyObjConstants.Field.shopId.getApiName()).toString();
        List<ShopProductSupplierRelationshipObj> specialSupplyByShopId = supplyDao.getSpecialSupplyByShopId(tenantId, shopId);
        specialSupplyByShopId.removeIf(o->o.getId().equals(objectData.getId()));
        fmcgThreadPoolExecutor.execute(()->{
            if (CollectionUtils.isEmpty(specialSupplyByShopId)) {
                List<String> delDealerIds = Lists.newArrayList(shopId);
                //删除对应的特殊供货关系
                supplyDao.delSupplyStore(tenantId, delDealerIds);
                AccountObj accountObjById = supplyDao.getAccountObjById(tenantId, shopId);
                if (accountObjById.getRecordType().equals(AccountObjConstants.Value.default__c.name())) {
                    supplyDao.updateShopUpSupplyFields(tenantId, accountObjById.getId());
                } else {
                    supplyDao.updateDistributorUpSupplyFields(tenantId, accountObjById.getId());

                }
            }
        });
        supplyService.addAvailableObjSyncTask(actionContext.getTenantId(), result.getObjectData()
                .get(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName()).toString());
        return after;
    }
}
