package com.facishare.crm.fmcg.wq.controller;


import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.CoveredProductObjConstants;
import com.facishare.crm.fmcg.wq.constants.ProductCollectionObjConstants;
import com.facishare.crm.fmcg.wq.constants.SpecialSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.DistributorSupply;
import com.facishare.crm.fmcg.wq.service.ProductFilterService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.springframework.util.StringUtils;

import java.util.*;


/**
 * 经营范围新增
 * 经营产品过滤
 */
public class ProductCollectionProductFilterController extends StandardRelatedListController {

    SupplyService supplyService=SpringUtil.getContext().getBean(SupplyService.class);

    SupplyDao supplyDao =  SpringUtil.getContext().getBean(SupplyDao.class);
    ProductFilterService productFilterService = SpringUtil.getContext().getBean(ProductFilterService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        String businessType = arg.getMasterData().get("record_type").toString();

        log.info("ProductFilter server  first query :{},argQuery:{}",JSON.toJSON(query),JSON.toJSON(arg.getSearchQueryInfo()));
//        String scopeType="option2";//指定
//        String scopeType= arg.getMasterData().get("business_scope_type").toString();

//        JSONObject object = JSONObject.parseObject(arg.getSearchQueryInfo());
//        List<Filter> otherFilters = Lists.newArrayList();
//        if(CollectionUtils.notEmpty(object.getJSONArray("filters"))){
//            JSONArray array = object.getJSONArray("filters");
//            for (int i = 0; i < array.size(); i++) {
//                Filter otherProFilter = new Filter();
//                otherProFilter.setFieldName(array.getJSONObject(i).getString("field_name"));
//                otherProFilter.setOperator(Operator.valueOf(array.getJSONObject(i).getString("operator")));
//                otherProFilter.setFieldValues(array.getJSONObject(i).getJSONArray("field_values").stream().map(o->String.valueOf(o)).collect(Collectors.toList()));
//                if(Objects.nonNull(array.getJSONObject(i).get("value_type"))) otherProFilter.setValueType(array.getJSONObject(i).getInteger("value_type"));
//                if(Objects.nonNull(array.getJSONObject(i).get("is_cascade"))) otherProFilter.setIsCascade(array.getJSONObject(i).getBoolean("is_cascade"));
//                otherFilters.add(otherProFilter);
//            }
//        }

        query.getFilters().addAll(SearchQuery.convertFilter(arg.getSearchQueryInfo()));
        productFilterService.handleCategoryFilters(controllerContext.getTenantId(),controllerContext.getUser().getUserId(),query.getFilters());
        query.getFilters().removeIf(o->o.getFieldName().equals("object_describe_api_name"));
        //继承关系不需要显示产品组明细
        if (businessType.equals("server_center__c")) {//服务处业务类型
//            log.info("ProductFilter server query :{}",JSON.toJSON(query));
            QueryResult<IObjectData> result=supplyDao.getProductListByQuery(controllerContext.getTenantId(),query,false);
            log.info("ProductFilter server result :{}",JSON.toJSON(result));
            return result;

        }else if(businessType.equals("dealer__c")){//经销商
//            if(scopeType== ProductCollectionObjConstants.Value.businessScopeType_4.getValue()){//指定产品
                //查询经销商所在服务处经营范围下的产品
                if(Objects.isNull(arg.getMasterData().get("dealer"))){
                    throw new ValidateException("请先选择经销商"); //ignoreI18n
                }
                String dealerId = arg.getMasterData().get("dealer").toString();
                log.info("ProductFilter dealerId {}", dealerId);
                return  getProductListFromService(query,dealerId,false);
//            }

        }else{//分销邮差商
            //查询分销邮差商的上级经销商经营产品合集
            if(Objects.isNull(arg.getMasterData().get("distribution"))){
                throw new ValidateException("请先选择分销邮差商"); //ignoreI18n
            }
            String distribution = arg.getMasterData().get("distribution").toString();
            List<DistributorSupply> distributorSupplyList = supplyService.getRelationListByDownIds(controllerContext.getTenantId(),Lists.newArrayList(distribution));
            if(CollectionUtils.empty(distributorSupplyList)){
                throw new ValidateException("请先设置供货关系"); //ignoreI18n
            }

            List<String> delearList=Lists.newArrayList();
                if(org.apache.commons.collections.CollectionUtils.isNotEmpty(distributorSupplyList)){
                    List<String> specialIds=Lists.newArrayList();//供货关系ID
                    Set<String> productIds=new HashSet<>();
                    distributorSupplyList.forEach(o-> {
                        if(o.isSpecialSupply()){
                            specialIds.add(o.getUpDealerAccountId());
                        }else {
                            delearList.add(o.getUpDealerAccountId());
                        }
                    });//上级ID
                    if(CollectionUtils.notEmpty(specialIds)){
                        //查询特例
                        List<IObjectData> specialList= supplyDao.getSpecialSupplyByShopIds(controllerContext.getTenantId(),distribution,specialIds);
                        specialList.forEach(o->productIds.add(o.get(SpecialSupplyObjConstants.Field.productId.getApiName(),String.class)));
                    }
                    //查询所有经销商的经营范围明细
                    List<String> pcIds=Lists.newArrayList();
                    if(CollectionUtils.notEmpty(delearList)) {
                        List<IObjectData> pcData = supplyService.getProductCollectionByDealerIds(controllerContext.getTenantId(), ProductCollectionObjConstants.API_NAME, delearList);
                        for (IObjectData iObjectData : pcData) {
                            if (!iObjectData.get("business_scope_type").toString().equals(ProductCollectionObjConstants.Value.businessScopeType_2.getValue())) {//非继承
                                pcIds.add(iObjectData.get("_id").toString());
                            } else {
//                            QueryResult<IObjectData> queryResult= getProductListFromService(query,iObjectData.get("dealer").toString(),true);
//                            total.getData().addAll(queryResult.getData());
                                //查询经销商的归属部门
                                List<IObjectData> customInfo = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), Lists.newArrayList(iObjectData.get("dealer").toString()), "AccountObj");
                                //查询服务处的产品-查询经营范围
                                SearchQuery.SearchQueryBuilder pcSearchQuery = SearchQuery.builder()
                                        .eq(ProductCollectionObjConstants.Field.departmentId.getApiName(), customInfo.get(0).get("data_own_department"))
                                        .eq("record_type", "server_center__c");

                                List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ProductCollectionObjConstants.API_NAME, pcSearchQuery.build().getSearchTemplateQuery()).getData();
                                if (CollectionUtils.notEmpty(data)) {
                                    if (data.get(0).get("business_scope_type").toString().equals(ProductCollectionObjConstants.Value.businessScopeType_1.getValue())) {
                                        return supplyDao.getProductListByQuery(controllerContext.getTenantId(), query, false);
                                    } else {
                                        productIds.addAll(getProductListByProductCollectionIds(Lists.newArrayList(data.get(0).get("_id").toString())));
                                    }
                                }

                            }
                        }
                    }

                    productIds.addAll(getProductListByProductCollectionIds(pcIds));
                    Filter productFilter = new Filter();
                    productFilter.setFieldName("_id");
                    productFilter.setOperator(Operator.IN);
                    productFilter.setFieldValues(Lists.newArrayList(productIds));
                    query.getFilters().add(productFilter);
                    return serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), "ProductObj", query);



//                    total.getData().addAll(getProductListByProductCollectionIds(query,pcIds,true).getData());
//                    //total 中的数据去重
//                    List<IObjectData> totalResult=total.getData().stream().distinct().collect(Collectors.toList());
//                    total.setTotalNumber(totalResult.size());
//                    int endIndex=totalResult.size();
//                    if(totalResult.size()>query.getLimit()){
//                        endIndex=query.getOffset()+query.getLimit();
//                    }
//                    List<IObjectData> subList=Lists.newArrayList(totalResult).subList(query.getOffset(),endIndex);
//                    total.setData(subList);
//                   return total;
                }

        }
        return new QueryResult<>();
    }

    private QueryResult<IObjectData> getAllProductList(SearchTemplateQuery query) {

//        Filter giveWayFilter = new Filter();
//        giveWayFilter.setFieldName("is_giveaway");
//        giveWayFilter.setOperator(Operator.EQ);
//        giveWayFilter.setFieldValues(Lists.newArrayList("false"));
//        query.getFilters().add(giveWayFilter);
        //查询所有产品（除赠品）
        QueryResult<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), "ProductObj", query);
        return data;
    }

    private QueryResult<IObjectData> getProductListByProductCollectionIds(SearchTemplateQuery query,List<String> ids,boolean isQueryAll){
        if (CollectionUtils.notEmpty(ids)) {
            //查询产品组明细对象
            List<IObjectData> cpData=supplyService.getDesignateProductListByRelatedIds(controllerContext.getTenantId(), CoveredProductObjConstants.API_NAME,ids);
            log.info("ProductFilter cpData {}", JSON.toJSON(cpData));
            // 查询产品明细对象关联的产品数据
            Set<String> productIds = new HashSet<>();
            if (CollectionUtils.notEmpty(cpData)) {
                cpData.forEach(o -> productIds.add(o.get("relation_product").toString()));
            }
            if(CollectionUtils.empty(productIds)){
                return new QueryResult<>();
            }
            if(isQueryAll){
                QueryResult<IObjectData> result=new QueryResult<>();
                List<IObjectData> list=supplyDao.getByIds(controllerContext.getTenantId(),"ProductObj",Lists.newArrayList(productIds));
                result.setData(list);
                return result;
            }
            Filter productFilter = new Filter();
            productFilter.setFieldName("_id");
            productFilter.setOperator(Operator.IN);
            productFilter.setFieldValues(Lists.newArrayList(productIds));
            query.getFilters().add(productFilter);
            QueryResult<IObjectData> productData = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), "ProductObj", query);
            return productData;
        }else{
            return new QueryResult<>();
        }
    }


    private List<String> getProductListByProductCollectionIds(List<String> ids){
        List<String> productList=Lists.newArrayList();
        if (CollectionUtils.notEmpty(ids)) {
            //查询产品组明细对象
            List<IObjectData> cpData=supplyService.getDesignateProductListByRelatedIds(controllerContext.getTenantId(), CoveredProductObjConstants.API_NAME,ids);
            log.info("ProductFilter cpData {}", JSON.toJSON(cpData));
            // 查询产品明细对象关联的产品数据
            Set<String> productIds = new HashSet<>();
            if (CollectionUtils.notEmpty(cpData)) {
                cpData.forEach(o -> productIds.add(o.get("relation_product").toString()));
            }
            return Lists.newArrayList(productIds);
        }
        return productList;
    }

    private QueryResult<IObjectData> getProductListFromService(SearchTemplateQuery query,String dealerId,boolean isQueryAll){
        if(StringUtils.isEmpty(dealerId)){
            return new QueryResult();
        }
        //查询经销商的归属部门
        List<IObjectData> customInfo = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), Lists.newArrayList(dealerId), "AccountObj");
        if (CollectionUtils.empty(customInfo)) {
            return new QueryResult();
        }
        //查询服务处的产品-查询经营范围
        SearchQuery.SearchQueryBuilder pcSearchQuery = SearchQuery.builder()
                .eq(ProductCollectionObjConstants.Field.departmentId.getApiName(),customInfo.get(0).get("data_own_department") )
                .eq("record_type","server_center__c");
        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ProductCollectionObjConstants.API_NAME, pcSearchQuery.build().getSearchTemplateQuery()).getData();
        log.info("ProductFilter data {}", data);
        if (CollectionUtils.notEmpty(data)) {
            log.info("ProductFilter data id: {}", data.get(0).get("_id").toString());
            //查询产品组明细对象
            if(data.get(0).get("business_scope_type").toString().equals(ProductCollectionObjConstants.Value.businessScopeType_1.getValue())){
                return supplyDao.getProductListByQuery(controllerContext.getTenantId(),query,isQueryAll);
            }else {
                return getProductListByProductCollectionIds(query, Lists.newArrayList(data.get(0).get("_id").toString()),isQueryAll);
            }
        }
        return new QueryResult();
    }


//    private List<String> getProductIdsFromService(SearchTemplateQuery query,String dealerId,boolean isQueryAll){
//        List<String> productIds=Lists.newArrayList();
//        if(StringUtils.isEmpty(dealerId)){
//            return productIds;
//        }
//        //查询经销商的归属部门
//        List<IObjectData> customInfo = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), Lists.newArrayList(dealerId), "AccountObj");
//        if (CollectionUtils.empty(customInfo)) {
//            return productIds;
//        }
//        //查询服务处的产品-查询经营范围
//        SearchQuery.SearchQueryBuilder pcSearchQuery = SearchQuery.builder()
//                .eq(ProductCollectionObjConstants.Field.departmentId.getApiName(),customInfo.get(0).get("data_own_department") )
//                .eq("record_type","server_center__c");
//
//        List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), ProductCollectionObjConstants.API_NAME, pcSearchQuery.build().getSearchTemplateQuery()).getData();
//
//        if (CollectionUtils.notEmpty(data)) {
//
//            if(data.get(0).get("business_scope_type").toString().equals(ProductCollectionObjConstants.Value.businessScopeType_1.getValue())){
//                QueryResult<IObjectData> productQuery = supplyDao.getProductListByQuery(controllerContext.getTenantId(),query,isQueryAll);
//                productQuery.getData().forEach(o->productIds.add(o.getId()));
//            }else {
//                productIds.addAll(getProductListByProductCollectionIds(Lists.newArrayList(data.get(0).get("_id").toString())));
//
//            }
//        }
//        return productIds;
//    }

}
