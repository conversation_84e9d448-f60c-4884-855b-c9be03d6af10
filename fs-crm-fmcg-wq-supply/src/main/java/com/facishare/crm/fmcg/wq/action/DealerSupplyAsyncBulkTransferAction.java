package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-09-20 14:33
 **/
@Deprecated
public class DealerSupplyAsyncBulkTransferAction extends AbstractStandardAsyncBulkAction<DealerSupplyAsyncBulkTransferAction.Arg, DealerSupplyTransferAction.Arg> {


    @Override
    protected String getDataIdByParam(DealerSupplyTransferAction.Arg param) {
        return null;
    }

    @Override
    protected List<DealerSupplyTransferAction.Arg> getButtonParams() {
        return null;
    }

    @Override
    protected String getButtonApiName() {
        //供货门店	批量转移	batch_transfer_store__c	列表页批量	无条件，默认显示	默认crm管理员，可以自定义调整权限范围
        //一键转移	all_transfer_store__c	列表页批量	无条件，默认显示	默认crm管理员，可以自定义调整权限范围
        //供货分销商	批量转移	batch_transfer_distributor__c	列表页批量	无条件，默认显示	默认crm管理员，可以自定义调整权限范围
        //一键转移	all_transfer_distributor__c	列表

        return arg.getTransferType() == 0 ? (arg.isAll == 1 ? "all_transfer_store__c" : "batch_transfer_store__c") : (arg.isAll == 1 ? "all_transfer_distributor__c" : "batch_transfer_distributor__c");
    }

    @Override
    protected String getActionCode() {
        return "Transfer";
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Data
    public static class Arg {
        /**
         * 供货关系数据
         */
        private IObjectData objectData;

        /**
         * 转移类型 0:转移供货门店 1:转移配送商
         */
        private int transferType;


        /**
         * 是否全量转移 0:全量转移 1:部分转移
         */
        private int isAll;
        /**
         * 转移的客户ids
         */
        private List<String> customerIds;

        /**
         * 转移的经销商id
         */
        private String targetCustomerId;

    }

    @Data
    public static class Result {

    }
}
