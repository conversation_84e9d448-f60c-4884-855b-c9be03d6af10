package com.facishare.crm.fmcg.wq.api.area;

import com.facishare.appserver.checkins.api.model.BaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * @program: fs-crm-fmcg
 * @description:
 * @author: zhangsm
 * @create: 2021-12-23 13:03
 **/
public interface BatchUpdateDep {
    @Data
    @ToString
    class Arg implements Serializable {
      String supplyId;

    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {
    }
}
