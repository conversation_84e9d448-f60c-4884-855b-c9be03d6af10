package com.facishare.crm.fmcg.wq.api.area;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.api.model.BaseResult;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface AreaInfoByStores {
    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "store_ids")
        @JsonProperty("store_ids")
        @SerializedName("store_ids")
        private List<String> storeIds; //县级所   服务处


    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult implements Serializable {

        @SerializedName("area_info")
        @JSONField(name = "area_info")
        @JsonProperty("area_info")
        private Map<String,List<AreaInfoVO>> areaInfos = Maps.newHashMap();

        @SerializedName("dealer_list")
        @JSONField(name = "dealer_list")
        @JsonProperty("dealer_list")
        private Map<String,List<JSONObject>> dealerList = Maps.newHashMap();
    }

    @Data
    @ToString
    class AreaInfoVO implements Serializable {

        private String areaId;

        private String areaName;

        private String dealerName;

        private String dealerId;

        private String areaOwner;

        private String areaOwnerName;

    }
}
