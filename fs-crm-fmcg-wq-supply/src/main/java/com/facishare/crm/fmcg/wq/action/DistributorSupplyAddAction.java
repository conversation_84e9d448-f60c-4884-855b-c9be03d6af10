package com.facishare.crm.fmcg.wq.action;


import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.constants.DistributorSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: DistributorSupplyObj add
 * @author: zhangsm
 * @create: 2021-04-29 14:42
 **/
public class DistributorSupplyAddAction extends StandardAddAction {

    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);

        String tenantId = actionContext.getTenantId();
        String shopId = arg.getObjectData().get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()).toString();
        String dealerId = arg.getObjectData().get(DistributorSupplyObjConstants.Field.upDealerId.getApiName()).toString();
        boolean isSpecial = (Boolean) arg.getObjectData().get(DistributorSupplyObjConstants.Field.specialSupply.getApiName());

        if (shopId.equals(dealerId) || isSpecial == true){
            throw new ValidateException("参数错误"); //ignoreI18n
        }
        AccountObj thisAccountObj = supplyDao.getAccountObjById(tenantId,shopId);
        List<String> addSupplierIds = Lists.newArrayList(dealerId);
        supplyService.checkAddDistributorSupply(tenantId, thisAccountObj, addSupplierIds);
        if (CollectionUtils.isEmpty(addSupplierIds)){
            throw new ValidateException("数据重复"); //ignoreI18n
        }
        //初始化审批流上下文
        supplyOperaContext.setArgs(arg);
        supplyOperaContext.setResultClazz(Result.class);
        supplyOperaContext.setInUpCustomerId(dealerId);
        supplyOperaContext.setInUpSupplyId(arg.getObjectData().get(DistributorSupplyObjConstants.Field.upSupplyId.getApiName()).toString());
        supplyOperaContext.setCustermerIds(Lists.newArrayList(shopId));
        //触发审批流程
        Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(actionContext, supplyOperaContext);
        if (booleanIObjectDataPair.first){
            throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE,SupplyOperaContext.TRIGGERAPPROCODE);
        }

    }

    // 添加成功的话 需要更新客户对象
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        if (result.getObjectData()!=null) {
            String tenantId = actionContext.getTenantId();
            String thisDealerId = arg.getObjectData().get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()).toString();
            supplyDao.updateDistributorUpSupplyFields(tenantId,
                    thisDealerId);
            // 自动生成当前分销商的经营范围对象
            supplyService.getSupplierById(actionContext.getTenantId(), thisDealerId,0);
        }
        return after;
    }

}
