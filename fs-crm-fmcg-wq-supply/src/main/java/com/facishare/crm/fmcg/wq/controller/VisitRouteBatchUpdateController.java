package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.api.area.AccountList;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 批量更新客户字段
 */
@Slf4j
@SuppressWarnings("Duplicates")
public class VisitRouteBatchUpdateController extends PreDefineController<AccountList.Arg, AccountList.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected AccountList.Result doService(AccountList.Arg arg) {
        long start = System.currentTimeMillis();
        log.info("BatchUpdateAccountObjController start  arg:{}",JSON.toJSONString(arg));
        AccountList.Result result = new AccountList.Result();
        if (Objects.nonNull(arg.getAccountCustomerLabelMap())&&!arg.getAccountCustomerLabelMap().isEmpty()) {
            log.info("BatchUpdateAccountObjController 111  ");
            Map<String,List<String>> accountCustomerLabelMap = arg.getAccountCustomerLabelMap();
            List<String> idList = Lists.newArrayList(accountCustomerLabelMap.keySet());
            User user = User.systemUser(arg.getEid());
            //改成不触发工作流的
            IActionContext actionContext = ActionContextExt.of(user).getContext();
            ActionContextExt.of(actionContext).setTriggerWorkFlow(false);
            List<IObjectData> accountList = serviceFacade.findObjectDataByIds(arg.getEid(), idList, CommonConstants.ACCOUNT_OBJ);
            if (CollectionUtils.isNotEmpty(accountList)) {
                log.info("BatchUpdateAccountObjController accountList:{}",JSON.toJSONString(accountList.get(0)));
                List<List<IObjectData>> listArr = Lists.partition(accountList, 50);
                List<String> updateField = Lists.newArrayList("customer_label");
                for (List<IObjectData> list : listArr) {
                    for (IObjectData data : list) {
                        data.set("customer_label", accountCustomerLabelMap.get(data.getId()));
                    }
                    serviceFacade.batchUpdateByFields(actionContext, list, updateField);
                }
            }
        }
        if (Objects.nonNull(arg.getFieldsMap())&&!arg.getFieldsMap().isEmpty()) {
            log.info("BatchUpdateAccountObjController 222  ");
            Map<String,Map<String,Object>> fieldsMap = arg.getFieldsMap();
            User user = User.systemUser(arg.getEid());
            List<String> fields = Lists.newArrayList(fieldsMap.keySet());
            IActionContext actionContext = ActionContextExt.of(user).getContext();
            ActionContextExt.of(actionContext).setTriggerWorkFlow(false);
            if(CollectionUtils.isNotEmpty(fields)){
                for(String field : fields){
                    Map<String,Object> fieldsValueMap = fieldsMap.get(field);
                    if(fieldsValueMap!=null&&!fieldsValueMap.isEmpty()){
                        List<String> idList = Lists.newArrayList(fieldsValueMap.keySet());
                        //改成不触发工作流的
                        List<IObjectData> accountList = serviceFacade.findObjectDataByIds(arg.getEid(), idList,
                                StringUtils.isBlank(arg.getObjApiName()) ? CommonConstants.ACCOUNT_OBJ : arg.getObjApiName());
                        if (CollectionUtils.isNotEmpty(accountList)) {
                            log.info("BatchUpdateAccountObjController accountList:{}",accountList.size());
                            List<List<IObjectData>> listArr = Lists.partition(accountList, 50);
                            List<String> updateField = Lists.newArrayList(field);
                            for (List<IObjectData> list : listArr) {
                                for (IObjectData data : list) {
                                    data.set(field, fieldsValueMap.get(data.getId()));
                                }
                                try{
                                    serviceFacade.batchUpdateByFields(actionContext, list, updateField);
                                }catch (Exception e){
                                    log.info("batchUpdateByFields error ea:{}",arg.getEa(),e);
                                }
                            }
                        }
                    }
                }
            }
        }
        long end = System.currentTimeMillis();
        log.info("BatchUpdateAccountObjController end time:{}s",(end-start)/1000);
        return result;
    }
}
