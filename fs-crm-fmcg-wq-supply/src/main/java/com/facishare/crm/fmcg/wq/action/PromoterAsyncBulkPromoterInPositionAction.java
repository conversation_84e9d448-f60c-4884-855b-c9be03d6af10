package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAsyncBulkAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterAsyncBulkPromoterInPositionAction extends AbstractStandardAsyncBulkAction<PromoterAsyncBulkPromoterInPositionAction.Arg, PromoterPromoterInPositionAction.Arg> {


    @Override
    protected String getDataIdByParam(PromoterPromoterInPositionAction.Arg arg) {
        return arg.getObjectDataId();
    }

    @Override
    protected List<PromoterPromoterInPositionAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(id -> {
                    PromoterPromoterInPositionAction.Arg arg = new PromoterPromoterInPositionAction.Arg();
                    arg.setObjectDataId(id);
                    return arg;
                })
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.PROMOTER_IN_POSITION.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.PROMOTER_IN_POSITION.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.PROMOTER_IN_POSITION.getActionCode());
    }

    @Data
    public static class Arg {
        private List<String> dataIds;
    }

}
