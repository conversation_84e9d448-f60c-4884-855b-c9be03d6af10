package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.special.SpecialList;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

public class SpecialSupplyTestController extends PreDefineController<SpecialList.Arg, SpecialList.Result> {

    SupplyService supplyService=SpringUtil.getContext().getBean(SupplyService.class);
    SupplyDao supplyDao =  SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected SpecialList.Result doService(SpecialList.Arg arg) {
       throw  new ValidateException("test");
//        List<IObjectData> accountObj = supplyDao.getAllIObjectDataListByQuery(controllerContext.getTenantId(),
//                SearchQuery.builder().build(), "AccountObj");
//        Set<String> list = accountObj.stream().map(o->o.getId()).collect(Collectors.toSet());
//        log.info("SpecialSupplyBatchQueryController ,{}",list.size());
//        return result;
    }



}
