package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.service.OrganizationJMLService;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;


/**
 * 产品组新增校验
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class BaseAddAction extends FmcgSkipPermissionAddAction {

    private static final OrganizationJMLService organizationJMLService = SpringUtil.getContext().getBean(OrganizationJMLService.class);
    protected String departmentId_name = AreaManageConstants.DATA_OWN_DEPARTMENT;

    @Override
    protected void before(Arg arg) {
        //validateData(arg);
        super.before(arg);
    }

    /**
     * @param arg
     */
    protected void validateData(Arg arg) {

        List<String> departmentIdArray = (List<String>) arg.getObjectData().get(departmentId_name);
        if (AreaManageConstants.DATA_OWN_DEPARTMENT.equals(departmentId_name)&&CollectionUtils.isEmpty(departmentIdArray)) {
            throw new ValidateException("县级所不能为空"); //ignoreI18n
        }
        if(CollectionUtils.isEmpty(departmentIdArray)){
            return ;
        }
        //校验归县级所是5级部门
        List<DepartmentDto> departmentIds = organizationJMLService.getDepartmentIds(actionContext.getUser().getTenantIdInt(), departmentIdArray.get(0));
        if (!CollectionUtils.isEmpty(departmentIds)) {
            if (departmentIds.size() != 5) {
                log.info("AreaManageAdd:Uniqueness check  departmentIds : {}", departmentIds);
                throw new ValidateException("请选择五级县级所"); //ignoreI18n
            }
        }else {
            throw new ValidateException("请选择五级县级所"); //ignoreI18n
        }
    }

}
