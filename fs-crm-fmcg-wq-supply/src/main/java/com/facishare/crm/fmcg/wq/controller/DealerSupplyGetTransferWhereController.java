package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.Gson;
import lombok.Data;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.List;

import static com.facishare.crm.fmcg.wq.constants.AccountObjConstants.Field.otherDepartment;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 供货关系转移接口类
 * @author: zhangsm
 * @create: 2023-09-18 10:31
 **/
public class DealerSupplyGetTransferWhereController extends PreDefineController<DealerSupplyGetTransferWhereController.Arg, DealerSupplyGetTransferWhereController.Result> {
    private SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    private SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor = SpringUtil.getContext().getBean("fmcgThreadPoolExecutor", AsyncTaskExecutor.class);
    RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected Result doService(Arg arg) {
        IObjectData objectData = arg.getObjectData().toObjectData();
        //供货关系的配送商客户id
        String supplyDistributionCustomerId = objectData.get(DealerSupplyObjConstants.Field.dealerId.getApiName(),String.class);
        //获取客户
        AccountObj accountObj = supplyDao.getAccountObjById(controllerContext.getTenantId(), supplyDistributionCustomerId);
        Result result1 = new Result();
        //转成服务处级别的部门id
        List<String> accountServiceCenterIds = Lists.newArrayList(accountObj.getDepartmentId());
        accountServiceCenterIds = supplyDao.getServiceCenterDepartmentIds(controllerContext.getTenantId(),accountServiceCenterIds);
        SearchQuery.SearchQueryBuilder s1 = SearchQuery.builder().eq("record_type", accountObj.getRecordType())
                .inDepartmentAndSub("data_own_department", accountServiceCenterIds)
                .neq("_id", supplyDistributionCustomerId);
        SearchQuery.SearchQueryBuilder s2 = SearchQuery.builder().eq("record_type", accountObj.getRecordType())
                .inDepartmentAndSub(otherDepartment.getApiName(), accountServiceCenterIds)
                .neq("_id", supplyDistributionCustomerId);
        s1.addOrWheres(s2);
        List<Wheres> wheres = s1.build().getSearchTemplateQuery().getWheres();
        //转成filters 需要灰度
        List<IFilter> filters = Lists.newArrayList();
        for (int i = 0; i < wheres.size(); i++) {
            for (IFilter filter : wheres.get(i).getFilters()) {
                filter.setFilterGroup(String.valueOf(i+1));
                filters.add(filter);
            }
        }
        result1.setFilters(new Gson().toJson(filters));
        return result1;
    }

    @Data
    public static class Arg {
        /**
         * 供货关系数据
         */
        @JsonProperty("object_data")
        private ObjectDataDocument objectData;

        /**
         * 转移类型 0:转移供货门店 1:转移配送商
         */
        @JsonProperty("transfer_type")
        private int transferType;
//
//
//        /**
//         * 是否全量转移 1 是全量
//         */
//        private int isAll;
//        /**
//         * 转移的客户ids
//         */
//        private List<String> customerIds;
//
//        /**
//         * 转移的经销商id
//         */
//        private String targetCustomerId;

    }

    @Data
    public static class Result {
        String filters;

    }
}
