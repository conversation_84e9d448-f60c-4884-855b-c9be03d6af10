package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.DealerSupplyReset;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.Shop;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.service.SyncAvailableRangeService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.ListUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 重置客户上的供货关系字段
 * @author: zhangsm
 * @create: 2021-06-11 14:41
 **/
public class DealerSupplyResetController extends PreDefineController<DealerSupplyReset.Arg, DealerSupplyReset.Result> {
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);
    SyncAvailableRangeService syncAvailableRangeService= SpringUtil.getContext().getBean(SyncAvailableRangeService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }


    @Override
    protected DealerSupplyReset.Result doService(DealerSupplyReset.Arg arg) {
        result = new DealerSupplyReset.Result();
        String tenantId = controllerContext.getTenantId();
        AccountObj accountObjById = supplyDao.getAccountObjById(tenantId, arg.getAccountId());
        if (accountObjById.getRecordType().equals(AccountObjConstants.Value.default__c.name())){
            //门店
//            fmcgThreadPoolExecutor.execute(()->{
//            });
                supplyDao.updateShopUpSupplyFields(tenantId,arg.getAccountId());
        }else if (accountObjById.getRecordType().equals(AccountObjConstants.Value.distributor__c.name())){
            //分销商
//            fmcgThreadPoolExecutor.execute(()->{
//            });
                supplyDao.updateDistributorUpSupplyFields(tenantId,arg.getAccountId());
        }
        return result;
    }

    @Override
    protected DealerSupplyReset.Result after(DealerSupplyReset.Arg arg, DealerSupplyReset.Result result) {
        DealerSupplyReset.Result after = super.after(arg, result);
        fmcgThreadPoolExecutor.execute(
                () -> {
                    Shop shopObjById = supplyService.getShopObjById(controllerContext.getTenantId(), arg.getAccountId(), 1);
                    syncAvailableRangeService.updateAllAvailableDataByShop(controllerContext.getTenantId(),shopObjById);
                });
        return after;
    }
}
