package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.model.FmcgPreActionArgs;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 促销员入职
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterPromoterInPositionAction extends FmcgAbstractStandardAction<PromoterPromoterInPositionAction.Arg, PromoterPromoterInPositionAction.Result> {
    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);
 

    @Override
    protected Result doAct(Arg arg) {
        if (("1").equals(objectData.get(PromoterFields.IO_STATUS, String.class)) && StringUtils.isNotBlank(objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class))) {
            throw new ValidateException("促销员已在职"); //ignoreI18n
        }
        if (StringUtils.isNotBlank(objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class))) {
            //如果原来是离职的 才需要启用
            if (("0").equals(objectData.get(PromoterFields.IO_STATUS, String.class))) {
                //启用互联用户
                try {
                    pmmService.startPublicEmployee(actionContext.getTenantId(), objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class));
                } catch (Exception e) {
                    log.info("启用互联用户失败 {} {}", arg.getObjectDataId(), objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class), e);
                    throw new ValidateException("启用互联用户失败"); //ignoreI18n
                }
            }
            objectData.set(PromoterFields.IO_STATUS, "1");
            //审核状态改成已通过
            objectData.set(PromoterFields.REVIEW_STATUS, PromoterFields.ReviewStatus.agree.getValue());
            objectData.set(PromoterFields.IN_DATE, System.currentTimeMillis());
            pmmService.updatePromoter(actionContext.getTenantId(), actionContext.getUser().getUserId(), objectData);
            recodeLog();
            //如果审核状态是已通过 则在职状态改成 在职 否者都是 待入职
//            if ((PromoterFields.ReviewStatus.agree.getValue()).equals(objectData.get(PromoterFields.REVIEW_STATUS,String.class))){
//                //在职
//                objectData.set(PromoterFields.IO_STATUS,"1");
//                objectData.set(PromoterFields.IN_DATE,System.currentTimeMillis());
//                pmmService.updatePromoter(actionContext.getTenantId(),actionContext.getUser().getUserId(),objectData);
//                Map<String, Object> update = new HashMap<>();
//                update.put(PromoterFields.IO_STATUS, "1");
//                serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, this.objectDescribe, objectData, update, dbData);
//            }else{
//                //待入职
//                objectData.set(PromoterFields.IO_STATUS,"2");
//                pmmService.updatePromoter(actionContext.getTenantId(),actionContext.getUser().getUserId(),objectData);
//                Map<String, Object> update = new HashMap<>();
//                update.put(PromoterFields.IO_STATUS, "2");
//                serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, this.objectDescribe, objectData, update, dbData);
//            }
        } else {
            //改成审核通过
            objectData.set(PromoterFields.REVIEW_STATUS, PromoterFields.ReviewStatus.agree.getValue());
            objectData.set(PromoterFields.IO_STATUS, "1");
            //容错 重新赋值 publicUserId 走编辑io状态接口
            pmmService.startPublicEmployee(objectData);
        }
        return new Result();
    }

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.PROMOTER_IN_POSITION;
    }


    @Data
    public static class Arg extends FmcgPreActionArgs {
    }

    @Data
    public static class Result {

    }
}
