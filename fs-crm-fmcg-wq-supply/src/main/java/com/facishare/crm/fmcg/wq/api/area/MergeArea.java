package com.facishare.crm.fmcg.wq.api.area;

import com.facishare.appserver.checkins.api.model.BaseResult;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface MergeArea {

    @Data
    @ToString
    class Arg implements Serializable {

        private String sourceAreaId;

        private List<String> fromAreaId;

        private Map<String,Object> updateUser;

    }
    @Data
    @ToString
    class Result extends BaseResult {

    }
}
