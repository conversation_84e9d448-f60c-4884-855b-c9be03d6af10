package com.facishare.crm.fmcg.wq.api.area;


import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface MapStoreFilter {
    @Data
    @ToString
    class Arg implements Serializable {

//        private String areaAttributeId;

        private String areaAttributeName;

        private String productGroupId;

        private List<String> county;
    }
    @Data
    @ToString
    class Result implements Serializable {
        List<JSONObject> dataList;
    }
}
