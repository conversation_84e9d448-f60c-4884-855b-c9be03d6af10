package com.facishare.crm.fmcg.wq.api.area;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface AreaList {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "depart_ids")
        @JsonProperty("depart_ids")
        @SerializedName("depart_ids")
        private List<Integer> departIds; //县级所   服务处

        @JSONField(name = "dealer_id")
        @JsonProperty("dealer_id")
        @SerializedName("dealer_id")
        private Integer dealerId;  //经销商   业务组

        @JSONField(name = "areaId")
        @JsonProperty("areaId")
        @SerializedName("areaId")
        private String areaId;

        @JSONField(name = "mainObjectId")
        @JsonProperty("mainObjectId")
        @SerializedName("mainObjectId")
        private String mainObjectId;//效能审核 用到 优先客户创建的人的片区

    }

    @Data
    @ToString
    class Result implements Serializable {

        @SerializedName("area_info")
        @JSONField(name = "area_info")
        @JsonProperty("area_info")
        private List<AreaInfoVO> areaInfos = Lists.newArrayList();
    }

    @Data
    @ToString
    class AreaInfoVO implements Serializable {

        private String id;

        private String name;

        private String productGroupId;

        private String productGroupName;

        private String owner;

        private String dealerId;

    }

}
