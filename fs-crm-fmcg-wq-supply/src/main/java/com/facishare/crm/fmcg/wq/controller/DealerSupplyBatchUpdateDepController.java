package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.BatchUpdateDep;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @program: fs-crm-fmcg
 * @description: 批量更新归属部门
 * @author: zhangsm
 * @create: 2021-12-23 13:02
 **/
public class DealerSupplyBatchUpdateDepController extends PreDefineController<BatchUpdateDep.Arg, BatchUpdateDep.Result> {

    private static final ObjectDataProxy objectDataProxy = SpringUtil.getContext().getBean(ObjectDataProxy.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected BatchUpdateDep.Result doService(BatchUpdateDep.Arg arg) {
        String tenantId = controllerContext.getTenantId();
        IObjectData productCollectionObj = supplyDao.getAllIObjectDataListByQuery(User.systemUser(tenantId), SearchQuery.builder().eq("_id", arg.getSupplyId()).build(), DealerSupplyObjConstants.API_NAME).get(0);
        List<String> depart = productCollectionObj.getDataOwnDepartment();
        if (Objects.nonNull(depart)) {
            User user = User.systemUser(tenantId);
            IActionContext context = ActionContextExt.of(user, RequestContextManager.getContext())
                    .allowUpdateInvalid(true)
                    .setNotValidate(true)
                    .getContext();
            context.setChildObjValidateSkip(true);

            //更新
            //更新供货分销
            update(arg, tenantId, context, depart, DistributorSupplyObjConstants.API_NAME, DistributorSupplyObjConstants.Field.upSupplyId.getApiName());
            //更新供货门店
            update(arg, tenantId, context, depart, SupplyStoreObjConstants.API_NAME, SupplyStoreObjConstants.Field.upSupplyId.getApiName());
            //更新特例
            update(arg, tenantId, context, depart, SpecialSupplyObjConstants.API_NAME, SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName());
            //批量更新
        }

        return new BatchUpdateDep.Result();
    }

    private void update(BatchUpdateDep.Arg arg, String tenantId, IActionContext context, List<String> dep, String apiName, String queryField) {
        SearchQuery searchQuery = SearchQuery.builder().eq(queryField, arg.getSupplyId()).build();
        ArrayList<String> fields = Lists.newArrayList("_id", "tenant_id", "object_describe_api_name");
        try {
            //查询
            int limit = 200;
            searchQuery.getSearchTemplateQuery().setLimit(1);
            searchQuery.getSearchTemplateQuery().setOffset(0);
            SearchQuery finalSearchQuery = SearchQuery.builder().copyOf(searchQuery).build();
            searchQuery.getSearchTemplateQuery().setFindExplicitTotalNum(Boolean.TRUE);
            QueryResult<IObjectData> data = new QueryResult<>();
            QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(User.systemUser(tenantId)
                    , apiName, searchQuery.getSearchTemplateQuery());
            Integer total = queryResult.getTotalNumber();
            data.setTotalNumber(total);
            List<Integer> offsets = Lists.newArrayList();
            int times = total / limit;
            for (int i = 0; i <= times; i++) {
                offsets.add(i * limit);
            }
            offsets.parallelStream().forEach(o -> {
                SearchQuery copySearchQuery = SearchQuery.builder().copyOf(finalSearchQuery).build();
                copySearchQuery.getSearchTemplateQuery().setOffset(o);
                copySearchQuery.getSearchTemplateQuery().setLimit(limit);
                try {
                    List<IObjectData> tempData = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(User.systemUser(tenantId), apiName, copySearchQuery.getSearchTemplateQuery(), fields).getData();
                    tempData.stream().forEach(t -> t.setDataOwnDepartment(dep));
                    objectDataProxy.batchUpdateIgnoreOther(tempData, Lists.newArrayList(BaseField.dataOwnDepartment.getApiName()), context);
                } catch (MetadataServiceException e) {
                    log.info("bulkUpdateWithSearchTemplate error ,tenantId {} apiName {} ,search {}", tenantId, apiName, copySearchQuery.getSearchTemplateQuery().toJsonString(), e);
                }

            });
        } catch (Exception e) {
            log.info("bulkUpdateWithSearchTemplate error ,tenantId {} apiName {}", tenantId, apiName, e);
        }
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }
}
