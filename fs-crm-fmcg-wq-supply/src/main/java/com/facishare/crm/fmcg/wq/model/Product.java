package com.facishare.crm.fmcg.wq.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: fs-appserver-checkins-v2
 * @description:
 * @author: zhangsm
 * @create: 2021-04-20 19:22
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Product {

    /**
     *  产品Id
     */
    private String productId;

    /**
     * 产品名字
     */
    private String productName;


    /**
     * 业务id， 看情况吧
     */
    private String bizId;


    /**
     *  直接配货商 id
     */
    private String supplierId;

    /**
     * 门店
     */
    private String shopId;
    /**
     * 是门店特例供货
     */
    private boolean isShopSpecial;
    /**
     * 是配送商特例供货
     */
    private boolean isDistributorSpecial;

}
