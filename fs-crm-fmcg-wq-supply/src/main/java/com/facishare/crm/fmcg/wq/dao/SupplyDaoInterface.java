package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.common.SupplyOperateEnum;
import com.facishare.crm.fmcg.wq.model.DistributorSupply;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.model.obj.ProductCollectionObj;
import com.facishare.crm.fmcg.wq.model.obj.ProductObj;
import com.facishare.crm.fmcg.wq.model.obj.ShopProductSupplierRelationshipObj;
import com.facishare.crm.fmcg.wq.model.yldb.YLTempSupplyEntity;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.Pair;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @program: fs-crm-fmcg
 * @description: 查询数据方法
 * @author: zhangsm
 * @create: 2021-04-26 10:04
 **/
public interface SupplyDaoInterface {
    /**
     * 通过 客户id 获取经营关系  客户id 可以是 分销商的也可以是 经销商的
     * @param tenantId
     * @param supplierId
     * @return
     */
    ProductCollectionObj getProductCollectionObjByAccountId(String tenantId, String supplierId,boolean isQueryProduct);
    /**
     * 通过所有的上级查询
     * @param tenantId
     * @param supplierIds 本级的供应商
     * @return
     */
    List<DistributorSupply> getDistributorSupplyByUpIds(String tenantId, List<String> supplierIds);

    /**
     * 根据供货关系 查询下级配送商
     * @param tenantId
     * @param supplierIds 上级供货关系ID
     * @return
     */
    List<IObjectData> getDistributorSupplyByUpSupplyIds(String tenantId, List<String> supplierIds);
    /**
     * 通过所有的本级供应商查询上级
     * @param tenantId
     * @param supplierIds 本级的供应商
     * @return
     */
    List<DistributorSupply> getDistributorSupplyByThisIds(String tenantId, List<String> supplierIds);

    /**
     * 获取循环引用的数据 有数据是有问题的
     * @param tenantId
     * @param dataId 本条数据的id 编辑的时候用来排除
     * @param thisSupplierId
     * @param upSupplierId
     * @return
     */
    @Deprecated
    List<IObjectData> getCircularReferenceDistributorSupply(String tenantId, String dataId,
                                                            String thisSupplierId, String upSupplierId);

    List<IObjectData> getCircularReferenceDistributorSupply(String tenantId,
                                                            String thisSupplierId, List<String> upSupplierIds);
    /**
     * 获取重复
     * @param tenantId
     * @param dataId
     * @param thisSupplierId
     * @param upSupplierId
     * @return
     */
    List<IObjectData> getDistributorSuppliers(String tenantId, String dataId,
                                              String thisSupplierId, String upSupplierId);
    List<IObjectData> getDistributorSuppliers(String tenantId, String dataId,
                                              String thisSupplierId,List<String> upSupplierId);
    List<IObjectData> getDistributorSuppliers(String tenantId, String dataId,
                                              List<String> thisSupplierIds,String upSupplierId);
    /**
     * 根据 配送商的id 获取 供货关系的适用
     * @param tenantId
     * @param suppliers
     * @return
     */
    List<IObjectData> getDealerSupplyBySuppliers(String tenantId,List<String> suppliers);
    /**
     * 通过 客户ids 获取经营关系  客户id 可以是 分销商的也可以是 经销商的
     * @param tenantId
     * @param accountIds
     * @return
     */
    List<ProductCollectionObj> getProductCollectionObjListByAccountIds(String tenantId, List<String> accountIds,boolean isQueryProduct);

    /**
     * 获取店铺简要信息
     * @param tenantId
     * @param shopId
     * @return
     */
    AccountObj getAccountObjById(String tenantId, String shopId);
    /**
     * 获取店铺简要信息
     * @param tenantId
     * @param accountIds
     * @return
     */
    List<AccountObj> getAccountObjByIds(String tenantId, Collection<String> accountIds);
    List<AccountObj> getAccountObjByIdsIgnoreException(String tenantId, Collection<String> accountIds);

    /**
     * 通过店铺id 获取 供货门店
     * @param tenantId
     * @param shopId
     * @return
     */
    List<ShopProductSupplierRelationshipObj> getSupplyStoresByShopId(String tenantId, String shopId);

    /**
     * 通过店铺id 获取特殊供货关系
     * @param tenantId
     * @param shopId 店铺id或者经销商id
     * @return
     */
    List<ShopProductSupplierRelationshipObj> getSpecialSupplyByShopId(String tenantId, String shopId);


    List<IObjectData> getSpecialSupplyByShopIds(String tenantId,String accountId, List<String> supplierIds);
    /**
     * 从子集转换成 业务处的部门id
     */
    List<String>  getServiceCenterDepartmentIds(String tenantId,List<String> departmentIds);


    /**
     * 根据经营产品范围获取所有的产品
     * @param productCollectionId
     * @return
     */
    List<ProductObj> getProductListByProductCollectionId(String tenantId,String productCollectionId);

    /**
     * 通过部门id 获取业务组的范围
     * @param tenantId
     * @param supplierId 部门id
     * @return
     */
    ProductCollectionObj getProductCollectionObjByDepartmentId(String tenantId, String supplierId,boolean isQueryProduct);

    /**
     *
     * @param tenantId
     * @param departmentIds
     * @param isQueryProduct
     * @return
     */
    List<ProductCollectionObj> getProductCollectionObjByDepartmentIds(String tenantId, List<String> departmentIds,boolean isQueryProduct);

    /**
     * 获取 供货关系重复数据
     * @param tenantId
     * @param dataId 当前的数据id 如果是新增 则 为空
     * @param bizId 可抗是 配送商id  也可能是业务处id
     * @return
     */
    List<IObjectData> getDuplicatProductCollectionObjs(String tenantId,String dataId,String bizId);

    /**
     * 创建可售范围
     * @param tenantId
     * @param ownerId
     * @param dealerSupplyId
     * @param ownDepartment
     * @return
     */
    IObjectData createAvailableRangeObj(String tenantId, String ownerId, String dealerSupplyId, String accountId, Object ownDepartment);

    List<IObjectData> createPriceBookObj(String tenantId, String ownerId, List<String> masterObjectIds, Object ownDepartment);
    /**
     * 创建供货关系对象;
     * @param tenantId  企业id
     * @param addDealerSupplierIds 要增加的 供货商id
     * @return 供货关系对象的集合
     */
    List<IObjectData> createDealerSupplyObj(String tenantId,String ownerId, List<String> addDealerSupplierIds);

    /**
     * 创建供货门店对象
     * @param tenantId 企业id
     * @param thisAccountObj 当前店铺
     * @param dealerSupplyBySuppliers 要增加的对应的供货关系对象的配送商ids
     */
    List<IObjectData> createSupplyStoreObj(String tenantId, AccountObj thisAccountObj, List<IObjectData> dealerSupplyBySuppliers);
    /**
     * 创建供货门店对象
     * @param tenantId 企业id
     * @param accountId 当前店铺
     * @param dealerSupplyBySuppliers 要增加的对应的供货关系对象的配送商ids
     */
    List<IObjectData> createSupplyStoreObj(String tenantId, String userId, String accountId, List<IObjectData> dealerSupplyBySuppliers,boolean isSpecial);

    /**
     * 创建供货门店对象
     * @param tenantId 企业id
     * @param accountIds
     * @param dealerSupplyBySupplier 要增加的对应的供货关系对象的配送商id
     */
    List<IObjectData> createSupplyStoreObj(String tenantId, String userId, List<String> accountIds, IObjectData dealerSupplyBySupplier,boolean isSpecial);
    /**
     * 创建供货门店对象 不更新不查 直接新建 有可能重复
     * @param tenantId 企业id
     * @param accountIds
     * @param dealerSupplyBySupplier 要增加的对应的供货关系对象的配送商id
     */
    List<IObjectData> createSupplyStoreObjNoUpdate(String tenantId, String userId, List<String> accountIds, IObjectData dealerSupplyBySupplier,boolean isSpecial);



    /**
     * 创建供货门店对象
     * @param tenantId 企业id
     * @param thisAccountObj 当前店铺
     * @param dealerSupplyBySuppliers 要增加的对应的供货关系对象的配送商ids
     */
    List<IObjectData> createSupplyStoreObj4Special(String tenantId, AccountObj thisAccountObj, List<IObjectData> dealerSupplyBySuppliers);


    List<IObjectData> createDistributorSupplyObj(String tenantId, String userId, List<String> accountIds, IObjectData dealerSupplyBySupplier, boolean isSpecial) ;

    /**
     * 不更新也不查原来的 确保 不会重复
     * @param tenantId
     * @param userId
     * @param accountIds
     * @param dealerSupplyBySupplier
     * @param isSpecial
     * @return
     */
    List<IObjectData> createDistributorSupplyObjNoUpdate(String tenantId, String userId, List<String> accountIds, IObjectData dealerSupplyBySupplier, boolean isSpecial) ;
    /**
     * 创建 下级配送商对象
     * @param tenantId 企业id
     * @param accountId 当前的配送商id
     * @param ownerId 负责人id
     * @param addSupplierIds 要增加的上级配送商id
     */
    List<IObjectData> createDistributorSupplyObj(String tenantId,String ownerId, String accountId, List<String> addSupplierIds,Object ownDepartment);
    List<IObjectData> createDistributorSupplyObj4Special(String tenantId,String ownerId, String accountId, List<String> addSupplierIds,Object ownDepartment);


    /**
     * 创建 特例供货
     * @param tenantId 企业id
     * @param accountIds 门店／配送商
     * @param ownerId 负责人id
     * @param productIds 产品id
     */
    List<IObjectData> createSpecialSupplyObj(String tenantId,String ownerId, List<String> accountIds, List<String> productIds,String dealerSupplyId,String distributorId,Object ownDepartment);

    /**
     * 更新门店  配送商/经销商字段
     * @param tenantId
     * @param shopAccountId
     */
    void updateShopUpSupplyFields(String tenantId, String shopAccountId);

    /**
     * 更新分销商  配送商/经销商字段
     * @param tenantId
     * @param distributorAccountId
     */
    void updateDistributorUpSupplyFields(String tenantId, String distributorAccountId);

    /**
     * 根据业务处部门id 获取所有的 经销商 。这里的逻辑是判断 跨区域的
     * @param tenantId 企业id
     * @param usedSupplierIds 已经使用的 经销商id 会从结果里移除
     * @param serviceCenterDepartmentIds 服务处的部门Id
     * @return
     */
    List<ObjectDataDocument> getSupplyDealerListByServiceCenterDepartmentIds(String tenantId, Set<String> usedSupplierIds, List<String> serviceCenterDepartmentIds);

    /**
     * 验证 对应的经销商id checkSupplierIds  是否在业务处 服务处部门id下
     * @param tenantId 企业id
     * @param checkSupplierIds 验证的经销商ids
     * @param serviceCenterDepartmentIds 服务处部门ids
     * @return
     */
    void checkSupplierExistDepartment(String tenantId,List<String> checkSupplierIds,List<String> serviceCenterDepartmentIds);

    /**
     * 创建默认的 产品分组对象
     * @param tenantId 企业id
     * @param ids 可能是部门业务处Id  也可能是 经销商或者分销商的id
     * @return
     */
    List<IObjectData> createDefaultProductCollection(String tenantId,List<String> ids);

    /**
     * 删除经营范围对象 只能删除经销商和分销商
     * @param tenantId
     * @param dataId
     */
    void delProductCollection(String tenantId,List<String> dataId);

    /**
     * 删除 供货关系对象
     * @param tenantId
     * @param accountIds
     */
    void delDealerSupply(String tenantId,List<String> accountIds);

    /**
     * 删除 下级配送商
     * @param tenantId
     * @param accountIds 上级配送商id
     */
    void delDistributorSupplyByUpIds(String tenantId, List<String> accountIds);
    /**
     * 删除 下级配送商
     * @param tenantId
     * @param accountIds 上级配送商id
     */
    void delDistributorSupplyByDownIds(String tenantId, List<String> accountIds);

    /**
     * 删除 下级配送商
     * @param tenantId
     * @param upIds 上级配送商id
     */
    void delDistributorSupplyByDownIdAndUpIds(String tenantId,String downId, List<String> upIds);
    /**
     * 删除 供货门店
     * @param tenantId
     * @param accountIds 门店id  分销商id
     */
    void delSupplyStore(String tenantId,List<String> accountIds);
    /**
     * 删除 供货门店
     * @param tenantId
     * @param accountIds 门店id  分销商id
     */
    void delSupplyStoreBySupplierIds(String tenantId,List<String> accountIds);
    void delSupplyStoreByAccountIdAndSupplierIds(String tenantId,String accountId,List<String> supplierIds);
    /**
     * 删除 特殊门店对象
     *
     * @param tenantId
     * @param accountIds 门店id  分销商id
     */
    void delSpecialStore(String tenantId,List<String> accountIds);

    QueryResult<IObjectData> getAllProductList(String tenantId, SearchTemplateQuery query);


    QueryResult<IObjectData> getProductListByQuery(String tenantId, SearchTemplateQuery query,boolean isQueryAll);


    List<IObjectData> createAvailableAccountList(String tenantId, String mainId, List<String> acountIdList, Object ownDepartment);
    List<IObjectData> createAvailableProductList(String tenantId, String mainId, List<String> productIdList, Object ownDepartment);

    void deleteAvailableAccountList(String tenantId, String mainId, List<String> accountId);

    List<IObjectData> getAvailableAccountList(String tenantId, String mainId,List<String> accountIds);

    void deleteAvailableRange(String tenantId, List<String> dealerSupplyIds);


    /**
     * 更新门店的片区信息   银鹭
     * @param tenantId
     * @param areaInfo
     * @param accountIds
     */
    void updateAccountAreaInfo(String tenantId,int userId, IObjectData areaInfo, List<String> accountIds,Boolean isYL);

    void updateCoveredProduct(String tenantId, String thisDealerId);

    /**
     * 根据yl数据批量创建 供货门店数据
     * @param tenantId
     * @param userId
     * @param ylTempSupplyEntities
     * @return
     */
    List<YLTempSupplyEntity> createSupplyStoryObjs(String tenantId, String userId, List<YLTempSupplyEntity> ylTempSupplyEntities);
    /**
     * 根据银鹭数据批量创建供货分销对象
     * @param tenantId
     * @param userId
     * @param ylTempSupplyEntities
     */
    List<YLTempSupplyEntity> createDistributorSupplyObjs(String tenantId, String userId, List<YLTempSupplyEntity> ylTempSupplyEntities);

    List<YLTempSupplyEntity> createSpecialSupplyObjs(String tenantId, String userId, List<YLTempSupplyEntity> ylTempSupplyEntities);

    /**
     * 删除 与删除客户相关联的供货关系数据
     * @param tenantId
     * @param dataIds
     */
    void delSupplyAboutDelAccount(String tenantId, List<String> dataIds);

    /**
     * 查询特例供货门店，查询特例供货分销商
     * @param tenantId
     * @param dealerId
     * @return
     */
    List<String> getSpecialAccountIds(String tenantId, String dealerId);
    /**
     * 查询特例供货门店，查询特例供货分销商
     * @param tenantId
     * @param dealerId
     * @return
     */
    List<String> getSpecialAccountIds(String tenantId, String dealerId,String shopId);


    /**
     * 获取其他供货关系
     * @param tenantId
     * @param  下级客户ids
     * @param transferApiName 查询的数据apiName ， 供货门店、供货分销商
     * @param  excludeSupplierId 排除的配送商id
     * @return
     */
    List<IObjectData> getOtherSupply(String tenantId, Set<String> accountIds, String transferApiName, String excludeSupplierId);

    /**
     * 创建供货审批对象
     * 走本地业务方法创建，触发审批流程
     */
    Map<String,List<ObjectDataDocument>> createSupplyApproObj(User user, SupplyOperateEnum supplyOperateEnum, SupplyOperaContext supplyOperaContext);

    /**
     * 根据主对象 填充 明细对象， 一键转移使用
     */
    void fillSupplyCustomerApproObjDetail(String tenantId, IObjectData mainObj, List<String> accountIds);
    void fillSupplyProductApproObjDetail(String tenantId, IObjectData mainObj, List<Pair<IObjectData, List<String>>> supplyApproAndProducts, int transferType);

}
