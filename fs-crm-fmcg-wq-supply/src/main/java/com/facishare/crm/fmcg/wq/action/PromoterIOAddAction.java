package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterIOAddAction extends FmcgSkipPermissionAddAction {

SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

}
