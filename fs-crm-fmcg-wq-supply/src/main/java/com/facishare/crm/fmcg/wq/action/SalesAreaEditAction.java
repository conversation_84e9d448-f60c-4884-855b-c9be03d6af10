package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.service.SalesAreaService;
import com.facishare.crm.fmcg.wq.service.impl.SalesAreaServiceImpl;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/05/27/ 17:17
 **/
public class SalesAreaEditAction extends StandardEditAction {

    SalesAreaService salesAreaService = SpringUtil.getContext().getBean(SalesAreaServiceImpl.class);
    @Override
    protected void before(Arg arg) {
        String tenantId = actionContext.getTenantId();
        Integer level = salesAreaService.calculateLevel(tenantId,arg,true);
        if(Objects.nonNull(level)) {
            arg.getObjectData().put("channel_level", level.toString());
        }
        super.before(arg);
    }

}
