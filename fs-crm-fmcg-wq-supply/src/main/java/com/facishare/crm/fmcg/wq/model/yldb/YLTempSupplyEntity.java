package com.facishare.crm.fmcg.wq.model.yldb;

import lombok.Data;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.Set;

/**
 * @program: fs-appserver-checkins-v2
 * @description: 银鹭临时表  单供货关系
 * @author: zhangsm
 * @create: 2021-06-03 10:58
 **/
@Entity(value = "temp_yl_supply")
@Data
public class YLTempSupplyEntity implements Serializable {
    /**
     * 银鹭的门店id
     */
    @Id
    private String ylAccountIdAndUpId;
    @Property("yl_id")
    private String ylAccountId;
    @Property("fs_id")
    private String fsId;
    @Property("name")
    private String name;
    /**
     * this 的级别  0 服务处的  1 是经销商的 2 是分销商的 3 门店
     */
    @Property("level")
    private int level;

    @Property("up_level")
    private int upLevel;
    /**
     * 上级信息
     */
    @Property("yl_up_id")
    private String ylUpAccountId;
    /**
     * 后续计算
     */
    @Property("fs_up_id")
    private String fsUpAccountId;
    @Property("up_name")
    private String upName;
    /**
     * 0 不是特例 1 是特例
     */
    @Property("special")
    private int special;

    /**
     * 服务处的名字
     */
    @Property("dep_name")
    private String departmentName;
    /**
     * 服务处的id
     * 后续计算
     */
    @Property("dep_id")
    private String departmentId;

    /**
     * 0 初始 1 同步完成
     */
    @Property("s")
    private int status;
    /**
     * 同步到paas里的数据id
     */
    @Property("soi")
    private String syncObjectId;
    /**
     * 上游的供货关系id 。。需要反查
     */
    @Property("up_supply_soi")
    private String upSupplySyncObjectId;
    /**
     * 银鹭产品Id
     */
    @Embedded("product")
    private Set<YLTempSubProduct> ylProducts;

    @Property("ot")
    private long operationTime;
}
