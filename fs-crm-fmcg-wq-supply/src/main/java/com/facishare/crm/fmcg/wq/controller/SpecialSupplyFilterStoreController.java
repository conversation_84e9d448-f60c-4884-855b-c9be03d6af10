package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class SpecialSupplyFilterStoreController extends StandardRelatedListController {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
//        return super.getQueryResult(query);
        QueryResult<IObjectData> result=new QueryResult<IObjectData>();

        //供货关系ID DealerSupplyObj
        String dsId= arg.getObjectData().get("_id").toString();//
        List<IObjectData> dsData = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), Lists.newArrayList(dsId), "DealerSupplyObj");
        String dealerId=dsData.get(0).get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();

        //查询经销商的归属部门  跨区域
        List<IObjectData> customInfo = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), Lists.newArrayList(dealerId), "AccountObj");
        if(CollectionUtils.empty(customInfo)){
            return new QueryResult<>();
        }
        List<String> deptId=(List<String>)customInfo.get(0).get("data_own_department");

        if(Objects.nonNull(customInfo.get(0).get(AccountObjConstants.Field.otherDepartment.getApiName()))){
            List<String> otherDeptId=(List<String>)customInfo.get(0).get(AccountObjConstants.Field.otherDepartment.getApiName());
            deptId.addAll(otherDeptId);
        }
        Filter deptFilter = new Filter();
        deptFilter.setFieldName("data_own_department");
        deptFilter.setOperator(Operator.IN);
        deptFilter.setFieldValues(deptId);
        deptFilter.setIsCascade(true);
        //查询业务类型等于门店
        Filter businessTypeFilter = new Filter();
        businessTypeFilter.setFieldName("record_type");
        businessTypeFilter.setOperator(Operator.IN);
        businessTypeFilter.setFieldValues(Lists.newArrayList("default__c","KA_master_depot__c"));//门店和KA总仓
        query.getFilters().addAll(SearchQuery.convertFilter(arg.getSearchQueryInfo()));
        query.getFilters().addAll(Lists.newArrayList(deptFilter,businessTypeFilter));
//        query.setFilters(Lists.newArrayList(deptFilter,businessTypeFilter));

        query.getFilters().removeIf(o->o.getFieldName().equals("object_describe_api_name"));

        result = serviceFacade.findBySearchQuery(controllerContext.getUser(), "AccountObj",query);
        return result;
    }

}
