package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.google.common.collect.Lists;

import java.util.List;

/**
 *
 */
public class PromotionActivityInsertImportDataAction extends StandardInsertImportDataAction {

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        validateDate(dataList);
    }
    private void validateDate(List<ImportData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            Long startDate = Long.valueOf(importData.getData().get("start_date").toString());
            Long endDate = Long.valueOf(importData.getData().get("end_date").toString());
            if (startDate > endDate){
                errorList.add(new ImportError(importData.getRowNo(),"活动开始时间不能小于结束时间")); //ignoreI18n
            }
        }
        mergeErrorList(errorList);
    }
}