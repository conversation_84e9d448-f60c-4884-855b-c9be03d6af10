package com.facishare.crm.fmcg.wq.model;

import lombok.Data;

/**
 * @program: fs-crm-fmcg
 * @description: 供应商 上下级关系 从供货分销商 对象 转换过来的
 * @author: zhangsm
 * @create: 2021-04-25 13:56
 **/
@Data
public class DistributorSupply {


    //上级经销商id
    String upDealerAccountId;
//    String upSupplierName;
    //本级分销商id
    String thisDistributorAccountId;
//    String thisSupplierName;
    String upSupplyId;// 供货关系
    String id;
    String name;
    boolean specialSupply; //是否特例供货
}
