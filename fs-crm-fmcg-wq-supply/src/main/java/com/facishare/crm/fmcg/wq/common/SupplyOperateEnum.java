package com.facishare.crm.fmcg.wq.common;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.api.area.AddDealerSupply;
import com.facishare.crm.fmcg.wq.api.area.AddDistributorSupply;
import com.facishare.crm.fmcg.wq.api.special.SpecialList;
import com.facishare.crm.fmcg.wq.api.supply.SupplyStoreList;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.controller.DealerSupplyTransferController;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 供货关系操作枚举
 * @author: zhangsm
 * @create: 2023-11-09 17:16
 **/
@AllArgsConstructor
@Getter
public enum SupplyOperateEnum {

    /**
     * 通用操作：
     * 供货关系删除
     * 供货门店新增
     * 供货门店删除
     * 供货分销商新增
     * 供货分销商删除
     * 特例供货新增
     * 特例供货删除
     */
    DEALER_SUPPLY_DEL(DealerSupplyObjConstants.API_NAME, ObjectAction.INVALID, null, "供货关系删除", 1, Lists.newArrayList(LOCK.OUT_UP_CUSTOMER)),
    SUPPLY_STORE_ADD(SupplyStoreObjConstants.API_NAME, ObjectAction.CREATE, null, "供货门店新增", 2, Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SUPPLY_STORE_DEL(SupplyStoreObjConstants.API_NAME, ObjectAction.INVALID, null, "供货门店删除", 3, Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SUPPLY_DISTRIBUTOR_ADD(DistributorSupplyObjConstants.API_NAME, ObjectAction.CREATE, null, "供货分销商新增", 4, Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SUPPLY_DISTRIBUTOR_DEL(DistributorSupplyObjConstants.API_NAME, ObjectAction.INVALID, null, "供货分销商删除", 5, Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SPECIAL_SUPPLY_ADD(SpecialSupplyObjConstants.API_NAME, ObjectAction.CREATE, null, "特例供货新增", 6, Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SPECIAL_SUPPLY_DEL(SpecialSupplyObjConstants.API_NAME, ObjectAction.INVALID, null, "特例供货删除", 7, Lists.newArrayList(LOCK.SUB_CUSTOMER)),

    /**
     * 客开操作：
     * 供货门店增加
     * 特例供货门店增加
     * 供货门店批量转移
     * 供货门店一键转移
     * 供货分销增加
     * 特例供货分销
     * 供货分销批量转移
     * 供货分销一键转移
     * 客户上供货关系字段变更
     * 修改经营范围
     */
    //供货门店相关
    SUPPLY_STORE(SupplyStoreObjConstants.API_NAME, null, "BatchSaveData", "供货门店增加", 21,Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SPECIAL_SUPPLY_STORE(SpecialSupplyObjConstants.API_NAME, null, "BatchSaveData", "特例供货门店增加", 22,Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SUPPLY_STORE_TRANSFER(DealerSupplyObjConstants.API_NAME, null, "Transfer", "供货门店批量转移", 23,Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SUPPLY_STORE_TRANSFER_ALL(DealerSupplyObjConstants.API_NAME, null, "Transfer", "供货门店一键转移", 24,Lists.newArrayList(LOCK.OUT_UP_CUSTOMER)),
    //供货分销相关
    SUPPLY_DISTRIBUTOR(DistributorSupplyObjConstants.API_NAME, null, "BatchSaveData", "供货分销增加", 25,Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SPECIAL_SUPPLY_DISTRIBUTOR(SpecialSupplyObjConstants.API_NAME, null, "BatchSaveData", "特例供货分销", 26,Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SUPPLY_DISTRIBUTOR_TRANSFER(DealerSupplyObjConstants.API_NAME, null, "Transfer", "供货分销批量转移", 27,Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    SUPPLY_DISTRIBUTOR_TRANSFER_ALL(DealerSupplyObjConstants.API_NAME, null, "Transfer", "供货分销一键转移", 28,Lists.newArrayList(LOCK.OUT_UP_CUSTOMER)),
    //客户上供货关系字段变更
    CUSTOMER_SUPPLY_FIELD_CHANGE(DealerSupplyObjConstants.API_NAME, null, "Add", "门店指定配送商", 41,Lists.newArrayList(LOCK.SUB_CUSTOMER)),
    DISTRIBUTOR_SUPPLY_FIELD_CHANGE(DistributorSupplyObjConstants.API_NAME, null, "Add", "分销商指定经销商", 42,Lists.newArrayList(LOCK.SUB_CUSTOMER)),


    ;


    /**
     * 绑定的apiName
     */
    private final String bindApiName;
    /**
     *
     */
    private final ObjectAction objectAction;

    private final String methodName;
    /**
     * label
     */
    private final String operateLabel;
    /**
     * 操作码 对应对象的 options
     */
    private final int operateCode;

    private final List<LOCK> lockCodes;
    
    public  Class getArgsClass() {
        switch (this) {
            case SUPPLY_STORE:
            case SUPPLY_DISTRIBUTOR:
                return SupplyStoreList.Arg.class;
            case SPECIAL_SUPPLY_STORE:
            case SPECIAL_SUPPLY_DISTRIBUTOR:
                return SpecialList.Arg.class;
            case SUPPLY_STORE_TRANSFER:
            case SUPPLY_STORE_TRANSFER_ALL:
            case SUPPLY_DISTRIBUTOR_TRANSFER:
            case SUPPLY_DISTRIBUTOR_TRANSFER_ALL:
                return DealerSupplyTransferController.Arg.class;
            case DISTRIBUTOR_SUPPLY_FIELD_CHANGE:
                return AddDistributorSupply.Arg.class;
            case CUSTOMER_SUPPLY_FIELD_CHANGE:
                return AddDealerSupply.Arg.class;
            default:
                if (objectAction == ObjectAction.INVALID) {
                    return StandardInvalidAction.Arg.class;
                } else if (objectAction == ObjectAction.CREATE) {
                    return BaseObjectSaveAction.Arg.class;
                } else {
                    throw new ValidateException("未知的操作类型,参数类型获取失败");
                }
        }
    }
    public Class getResultClass(){
        //参考获取参数
        switch (this) {
            case SUPPLY_STORE:
            case SUPPLY_DISTRIBUTOR:
                return SupplyStoreList.Result.class;
            case SPECIAL_SUPPLY_STORE:
            case SPECIAL_SUPPLY_DISTRIBUTOR:
                return SpecialList.Result.class;
            case SUPPLY_STORE_TRANSFER:
            case SUPPLY_STORE_TRANSFER_ALL:
            case SUPPLY_DISTRIBUTOR_TRANSFER:
            case SUPPLY_DISTRIBUTOR_TRANSFER_ALL:
                return DealerSupplyTransferController.Result.class;
            case DISTRIBUTOR_SUPPLY_FIELD_CHANGE:
                return AddDistributorSupply.Result.class;
            case CUSTOMER_SUPPLY_FIELD_CHANGE:
                return AddDealerSupply.Result.class;
            default:
                if (objectAction == ObjectAction.INVALID) {
                    return StandardInvalidAction.Result.class;
                } else if (objectAction == ObjectAction.CREATE) {
                    return BaseObjectSaveAction.Result.class;
                } else {
                    throw new ValidateException("未知的操作类型,参数类型获取失败");
                }
        }
    }
    /**
     * 根据操作码获取对应的操作枚举
     */
    public static SupplyOperateEnum getOperateEnum(String operateCode) {
        for (SupplyOperateEnum value : SupplyOperateEnum.values()) {
            if (value.operateCode == Integer.valueOf(operateCode).intValue()) {
                return value;
            }
        }
        return null;
    }
    public enum LOCK{
        OUT_UP_CUSTOMER/*转出的客户*/,IN_UP_CUSTOMER/*转入的客户*/,SUB_CUSTOMER/*子对象客户*/
    }
    static Map<SupplyOperateEnum.LOCK,List<SupplyOperateEnum>> lockMap = null;
    public  static Map<LOCK,List<SupplyOperateEnum>> getLockMap() {
        if (lockMap == null) {
            lockMap = new HashMap<>();
            //枚举按照 lockcode 分组
            for (SupplyOperateEnum value : values()) {
                for (LOCK lockCode : value.lockCodes) {
                    if (lockMap.containsKey(lockCode)) {
                        lockMap.get(lockCode).add(value);
                    } else {
                        lockMap.put(lockCode, Lists.newArrayList(value));
                    }
                }
            }
        }
        return lockMap;
    }
    /**
     * 根据apiName 和 actionCode 获取对应的操作码
     *
     * @param apiName
     * @param actionCode
     * @return
     */
    public static SupplyOperateEnum getOperateCode(String apiName, ObjectAction actionCode, String methodName, Object args) {
        List<SupplyOperateEnum> list = Lists.newArrayList();
        for (SupplyOperateEnum value : SupplyOperateEnum.values()) {
            // actionCode 为空的时候 代表通用操作
            if (value.bindApiName.equalsIgnoreCase(apiName) && (value.objectAction == null || value.objectAction == actionCode) && (value.methodName == null || value.methodName.equalsIgnoreCase(methodName))) {
                list.add(value);
            }
        }
        if (list.size() == 1) {
            return list.get(0);
        } else if (list.size() > 1) {
            //不唯一需要对参数特殊处理
            if (args instanceof DealerSupplyTransferController.Arg) {
                DealerSupplyTransferController.Arg arg = (DealerSupplyTransferController.Arg) args;
                if (arg.getTransferType() == 0) {
                    //供货门店
                    return arg.getIsAll() == 1 ? SUPPLY_STORE_TRANSFER_ALL : SUPPLY_STORE_TRANSFER;
                } else {
                    //供货分销
                    return arg.getIsAll() == 1 ? SUPPLY_DISTRIBUTOR_TRANSFER_ALL : SUPPLY_DISTRIBUTOR_TRANSFER;
                }
            } else if (args instanceof SpecialList.Arg) {
                SpecialList.Arg arg = (SpecialList.Arg) args;
                return arg.getButtonType() == 0 ? SPECIAL_SUPPLY_STORE : SPECIAL_SUPPLY_DISTRIBUTOR;
            }else if (args instanceof AddDistributorSupply.Arg){
               return DISTRIBUTOR_SUPPLY_FIELD_CHANGE;
            }else if (args instanceof AddDealerSupply.Arg){
                return CUSTOMER_SUPPLY_FIELD_CHANGE;
            }

        }
        throw new ValidateException("操作码获取失败");
    }

//    public static SupplyApproArgs getSupplyApproArgs(SupplyOperateEnum supplyOperateEnum, Object args) {
//        SupplyApproArgs supplyApproArgs = new SupplyApproArgs();
//        supplyApproArgs.setSupplyOperateEnum(supplyOperateEnum);
//        switch (supplyOperateEnum) {
//            case SUPPLY_STORE: {
//                SupplyStoreList.Arg arg = (SupplyStoreList.Arg) args;
//                supplyApproArgs.setInUpCustomerId(arg.getUpCustomerId());
//                supplyApproArgs.setLockCustomerIds(arg.getStoreIds());
//            }
//            break;
//            case SUPPLY_STORE_ADD:
//                BaseObjectSaveAction.Arg arg = (BaseObjectSaveAction.Arg) args;
//                IObjectData objectData = arg.getObjectData().toObjectData();
//                supplyApproArgs.setInUpCustomerId(objectData.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class));
//                supplyApproArgs.setLockCustomerIds(Arrays.asList(objectData.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)));
//                break;
//            case SUPPLY_STORE_DEL:
//
//
//        }
//
//    }
    public static SupplyOperateEnum of(String code) {
        int iCode = Integer.valueOf(code).intValue();
        for (SupplyOperateEnum value : values()) {
            if (value.operateCode == iCode) {
                return value;
            }
        }
        return null;
    }

    /**
     * 生成options
     * label,value 的json  value = operateCode
     */
    public static String getOptions() {
        StringBuilder sb = new StringBuilder("[");
        for (SupplyOperateEnum value : SupplyOperateEnum.values()) {
            sb.append("{\"label\":\"").append(value.operateLabel).append("\",\"value\":\"").append(value.operateCode).append("\"},");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append("]");
        return sb.toString();
    }


    public static void main(String[] args) {
//        System.out.println("|操作|转入|转出|客户");
//        System.out.println("|--|--|--|--|");
//        System.out.println(Arrays.asList(values()).stream().map(o->{return "|"+o.getOperateLabel()+"|--|--|--|";}).collect(Collectors.joining("\n")));
        System.out.println(getOptions());
    }

//    @Data
//    public static class SupplyApproArgs {
//        //SupplyOperateEnum supplyOperateEnum, String outUpCustomerId, String inUpCustomerId, List<String> lockProductIds, List<String> lockCustomerIds
//        private SupplyOperateEnum supplyOperateEnum;
//        private String outUpCustomerId;
//        private String inUpCustomerId;
//        private List<String> lockProductIds;
//        private List<String> lockCustomerIds;
//
//    }
}
