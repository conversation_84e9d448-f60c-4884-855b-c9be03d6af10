package com.facishare.crm.fmcg.wq.abs;

import com.facishare.crm.fmcg.wq.MCPreDefineObject;
import com.facishare.crm.fmcg.wq.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@SuppressWarnings("Duplicates")
public abstract class AbstractAreaSpecialButtonProvider implements SpecialButtonProvider {
    @Override
    public String getApiName() {
        return MCPreDefineObject.AreaManage.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = new ArrayList<>(3);
        buttons.add(ButtonUtils.buildButton(ObjectAction.AREA_BATCH_ADD));
        buttons.add(ButtonUtils.buildButton(ObjectAction.AREA_BATCH_EDIT));
        buttons.add(ButtonUtils.buildButton(ObjectAction.AREA_MERGE));
        return buttons;
    }

}
