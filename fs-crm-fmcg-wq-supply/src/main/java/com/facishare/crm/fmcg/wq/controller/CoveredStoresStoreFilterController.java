package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.ProductCollectionConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 片区新增门店过滤
 */
@SuppressWarnings("Duplicates")
public class CoveredStoresStoreFilterController extends StandardRelatedListController {

    /**
     * 主对象的数据
     */
    private IObjectData masterObjectData;
    @Override
    protected void before(Arg arg) {
        controllerContext = new ControllerContext(controllerContext.getRequestContext(),"AccountObj",controllerContext.getMethodName());
        super.before(arg);
    }
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        if(GrayRelease.isAllow("checkin-server-v2","areaManage",controllerContext.getEa())){
            QueryResult<IObjectData> result= jmlStoreFilter(query);
            return result;
        }else if (GrayRelease.isAllow("checkin-server-v2", "isYLArea",controllerContext.getEa())){
            QueryResult<IObjectData> result= yinLuStoreFilter(query);
            return result;
        }
        return super.getQueryResult(query);
    }

    private QueryResult<IObjectData> yinLuStoreFilter(SearchTemplateQuery query) {
        if (arg.getMasterData() != null) {
            masterObjectData = arg.getMasterData().toObjectData();
        }
        QueryResult<IObjectData> result= new QueryResult<>();
        List<String> county = (List<String>)masterObjectData.get(AreaManageConstants.BUSINESS_GROUP);
        if(CollectionUtils.empty(county)){
            return result;
        }
        //业务类型
        List<Filter> filters = Lists.newArrayList();
        Filter recordFilter = new Filter();
        recordFilter.setFieldName(CommonConstants.RECORD_TYPE);
        recordFilter.setOperator(Operator.EQ);
        recordFilter.setFieldValues(Lists.newArrayList(CommonConstants.RECORD_DEFAULT));
        filters.add(recordFilter);

        //归属部门
        Filter deptFilter = new Filter();
        deptFilter.setFieldName(CommonConstants.DATA_OWN_DEPARTMENT);
        deptFilter.setOperator(Operator.IN);
        deptFilter.setFieldValues(county);
        deptFilter.setIsCascade(true);
        filters.add(deptFilter);

        //所属片区为空
        Filter areaFilter = new Filter();
        areaFilter.setFieldName("belong_routeNew__c");
        areaFilter.setOperator(Operator.IS);
        areaFilter.setFieldValues(Lists.newArrayList());
        filters.add(areaFilter);

        //生命状态
        Filter lifeStatus = new Filter();
        lifeStatus.setFieldName("life_status");
        lifeStatus.setOperator(Operator.EQ);
        lifeStatus.setFieldValues(Lists.newArrayList("normal"));
        filters.add(lifeStatus);

        SearchTemplateQuery newQuery = new SearchTemplateQuery();
        newQuery.setLimit(query.getLimit());
        newQuery.setOffset(query.getOffset());
        newQuery.setOrders(query.getOrders());
        //页面筛选
        List<Filter> otherFilters = Lists.newArrayList();
        JSONObject object = JSONObject.parseObject(arg.getSearchQueryInfo());
        if(CollectionUtils.notEmpty(object.getJSONArray("filters"))){
            JSONArray array = object.getJSONArray("filters");
            for (int i = 0; i < array.size(); i++) {
                Filter otherProFilter = new Filter();
                otherProFilter.setFieldName(array.getJSONObject(i).getString("field_name"));
                otherProFilter.setOperator(Operator.valueOf(array.getJSONObject(i).getString("operator")));
                otherProFilter.setFieldValues(array.getJSONObject(i).getJSONArray("field_values").stream().map(o->String.valueOf(o)).collect(Collectors.toList()));
                if(Objects.nonNull(array.getJSONObject(i).get("value_type"))) otherProFilter.setValueType(array.getJSONObject(i).getInteger("value_type"));
                if(Objects.nonNull(array.getJSONObject(i).get("is_cascade"))) otherProFilter.setIsCascade(array.getJSONObject(i).getBoolean("is_cascade"));
                otherFilters.add(otherProFilter);
            }
        }
        if(!CollectionUtils.empty(otherFilters)) {
            filters.addAll(otherFilters);
            log.info("findBySearchQuery query {}", JSON.toJSONString(otherFilters));
        }
        newQuery.setFilters(Lists.newArrayList(filters));

        result = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), CommonConstants.ACCOUNT_OBJ, newQuery);
        this.stopWatch.lap("findData");
        if (CollectionUtils.empty(result.getData())) {
            return result;
        } else if (this.isGetDataOnly()) {
            return result;
        } else {
            this.asyncFillFieldInfo(this.objectDescribe, result.getData());
            this.stopWatch.lap("asyncFillFieldInfo");
            return result;
        }
    }

    private QueryResult<IObjectData> jmlStoreFilter(SearchTemplateQuery query) {
        if (arg.getMasterData() != null) {
            masterObjectData = arg.getMasterData().toObjectData();
        }
        String productGroupId=(String)masterObjectData.get(AreaManageConstants.PRODUCT_GROUP);
        String areaAttributeName = (String)masterObjectData.get("Area_Attribute__c__r");
        List<String> county = (List<String>)masterObjectData.get(AreaManageConstants.COUNTY__C);
        QueryResult<IObjectData> result= new QueryResult<>();
        if(StringUtils.isEmpty(productGroupId) || StringUtils.isEmpty(areaAttributeName) || CollectionUtils.empty(county)){
            return result;
        }
        IObjectData productGroup = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()),productGroupId, ProductCollectionConstants.PRODUCT_COLLECTION_OBJ);
        String productGroupName = (String)productGroup.get(CommonConstants.NAME);
        boolean allPro = Objects.nonNull(productGroup.get(ProductCollectionConstants.ALL_PRODUCT))?(Boolean)productGroup.get(ProductCollectionConstants.ALL_PRODUCT):false;
        Wheres wheres = new Wheres();
        Wheres areaWheres = new Wheres();
        Filter recordFilter = new Filter();
        recordFilter.setFieldName(CommonConstants.RECORD_TYPE);
        recordFilter.setOperator(Operator.EQ);
        recordFilter.setFieldValues(Lists.newArrayList(CommonConstants.RECORD_DEFAULT));

        Filter deptFilter = new Filter();
        deptFilter.setFieldName(CommonConstants.DATA_OWN_DEPARTMENT);
        deptFilter.setOperator(Operator.IN);
        deptFilter.setFieldValues(county);
        deptFilter.setIsCascade(true);
        if(allPro){
            Filter filter = new Filter();
            filter.setFieldName(CommonConstants.ACCOUNT_AREA);
            filter.setOperator(Operator.IS);
            filter.setFieldValues(Lists.newArrayList());
            wheres.setFilters(Lists.newArrayList(filter,recordFilter,deptFilter));

            Filter areaFilter = new Filter();
            areaFilter.setFieldName(CommonConstants.ACCOUNT_ATTRIBUTE);
            areaFilter.setOperator(Operator.NLIKE);
            areaFilter.setFieldValues(Lists.newArrayList(areaAttributeName));
//            Filter allProFilter = new Filter();
//            allProFilter.setFieldName(CommonConstants.ACCOUNT_ALL_PRODUCT);
//            allProFilter.setOperator(Operator.EQ);
//            allProFilter.setFieldValues(Lists.newArrayList(Boolean.TRUE.toString()));

            areaWheres.setFilters(Lists.newArrayList(areaFilter,recordFilter,deptFilter));

        }else {
            String attrPro = areaAttributeName + "-" + productGroupName;
            String attrAllPro = areaAttributeName + "-全品"; //ignoreI18n

//            Filter areaFilter = new Filter();
//            areaFilter.setFieldName(CommonConstants.ACCOUNT_ATTRIBUTE);
//            areaFilter.setOperator(Operator.LIKE);
//            areaFilter.setFieldValues(Lists.newArrayList(areaAttributeName));
//
//            Filter proFilter = new Filter();
//            proFilter.setFieldName(CommonConstants.ACCOUNT_PRODUCT);
//            proFilter.setOperator(Operator.NLIKE);
//            proFilter.setFieldValues(Lists.newArrayList(attrPro));
//
//            Filter allProFilter = new Filter();
//            allProFilter.setFieldName(CommonConstants.ACCOUNT_ALL_PRODUCT);
//            allProFilter.setOperator(Operator.N);
//            allProFilter.setFieldValues(Lists.newArrayList(Boolean.TRUE.toString()));
            Filter proFilter = new Filter();
            proFilter.setFieldName(CommonConstants.ACCOUNT_PRODUCT);
            proFilter.setOperator(Operator.NLIKE);
            proFilter.setFieldValues(Lists.newArrayList(attrPro));

            Filter proAllFilter = new Filter();
            proAllFilter.setFieldName(CommonConstants.ACCOUNT_PRODUCT);
            proAllFilter.setOperator(Operator.NLIKE);
            proAllFilter.setFieldValues(Lists.newArrayList(attrAllPro));

            areaWheres.setFilters(Lists.newArrayList(proAllFilter,proFilter,recordFilter,deptFilter));

            Filter areaNonFilter = new Filter();
            areaNonFilter.setFieldName(CommonConstants.ACCOUNT_ATTRIBUTE);
            areaNonFilter.setOperator(Operator.NLIKE);
            areaNonFilter.setFieldValues(Lists.newArrayList(areaAttributeName));

            wheres.setFilters(Lists.newArrayList(areaNonFilter,recordFilter,deptFilter));

        }
        SearchTemplateQuery newQuery = new SearchTemplateQuery();
        newQuery.setLimit(query.getLimit());
        newQuery.setOffset(query.getOffset());
        newQuery.setOrders(query.getOrders());
//        query.getFilters().removeIf(o->o.getFieldName().equals("object_describe_api_name"));
        JSONObject object = JSONObject.parseObject(arg.getSearchQueryInfo());
        List<Filter> otherFilters = Lists.newArrayList();
        if(CollectionUtils.notEmpty(object.getJSONArray("filters"))){
            JSONArray array = object.getJSONArray("filters");
            for (int i = 0; i < array.size(); i++) {
                Filter otherProFilter = new Filter();
                otherProFilter.setFieldName(array.getJSONObject(i).getString("field_name"));
                otherProFilter.setOperator(Operator.valueOf(array.getJSONObject(i).getString("operator")));
                otherProFilter.setFieldValues(array.getJSONObject(i).getJSONArray("field_values").stream().map(o->String.valueOf(o)).collect(Collectors.toList()));
                if(Objects.nonNull(array.getJSONObject(i).get("value_type"))) otherProFilter.setValueType(array.getJSONObject(i).getInteger("value_type"));
                if(Objects.nonNull(array.getJSONObject(i).get("is_cascade"))) otherProFilter.setIsCascade(array.getJSONObject(i).getBoolean("is_cascade"));
                otherFilters.add(otherProFilter);
            }
        }
        log.info("findBySearchQuery query {}", JSON.toJSONString(otherFilters));
        if(!CollectionUtils.empty(otherFilters)){
            wheres.getFilters().addAll(otherFilters);
            areaWheres.getFilters().addAll(otherFilters);
        }
        newQuery.setWheres(Lists.newArrayList(wheres,areaWheres));
        log.info("findBySearchQuery query {}", JSON.toJSONString(newQuery));

        result = serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), CommonConstants.ACCOUNT_OBJ, newQuery);
        this.stopWatch.lap("findData");
        if (CollectionUtils.empty(result.getData())) {
            return result;
        } else if (this.isGetDataOnly()) {
            return result;
        } else {
            this.asyncFillFieldInfo(this.objectDescribe, result.getData());
            this.stopWatch.lap("asyncFillFieldInfo");
            return result;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        IObjectDescribe describeExt = this.serviceFacade.findObject(controllerContext.getTenantId(),CommonConstants.ACCOUNT_OBJ);
        List<ILayout> listLayouts = this.serviceFacade.getLayoutLogicService().findMobileListLayout(this.controllerContext.getUser(), describeExt, this.isListPage());
        listLayouts.forEach(this::doRender);

        List<ILayout> layout = this.serviceFacade.getLayoutLogicService().findLayoutByObjectApiName(controllerContext.getTenantId(),CommonConstants.ACCOUNT_OBJ);
        result.setListLayouts(LayoutDocument.ofList(listLayouts));
        result.setLayout(LayoutDocument.of(layout.get(0)));
        return result;
    }
//    @Override
//    protected StandardRelatedListController.Result doService(StandardRelatedListController.Arg arg) {
//        if (arg.getMasterData() != null) {
//            masterObjectData = arg.getMasterData().toObjectData();
//        }
//        String productGroupId=(String)masterObjectData.get(AreaManageConstants.PRODUCT_GROUP);
//        String areaAttributeName = (String)masterObjectData.get(AreaManageConstants.ATTRIBUTE_NAME);
//        StandardRelatedListController.Result result= null;
//        if(StringUtils.isEmpty(productGroupId) || StringUtils.isEmpty(areaAttributeName)){
//            return result;
//        }
//        IObjectData productGroup = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()),productGroupId, ProductCollectionConstants.PRODUCT_COLLECTION_OBJ);
//        String productGroupName = (String)productGroup.get(CommonConstants.NAME);
//        boolean allPro = Objects.nonNull(productGroup.get(ProductCollectionConstants.ALL_PRODUCT))?(Boolean)productGroup.get(ProductCollectionConstants.ALL_PRODUCT):false;
//        Wheres wheres = new Wheres();
//        Wheres areaWheres = new Wheres();
//        Filter recordFilter = new Filter();
//        recordFilter.setFieldName(CommonConstants.RECORD_TYPE);
//        recordFilter.setOperator(Operator.EQ);
//        recordFilter.setFieldValues(Lists.newArrayList(CommonConstants.RECORD_DEFAULT));
//        if(allPro){
//
//            Filter filter = new Filter();
//            filter.setFieldName(CommonConstants.ACCOUNT_AREA);
//            filter.setOperator(Operator.IS);
//            filter.setFieldValues(Lists.newArrayList());
//            wheres.setFilters(Lists.newArrayList(filter,recordFilter));
//
//            Filter areaFilter = new Filter();
//            areaFilter.setFieldName(CommonConstants.ACCOUNT_ATTRIBUTE);
//            areaFilter.setOperator(Operator.NLIKE);
//            areaFilter.setFieldValues(Lists.newArrayList(areaAttributeName));
//            Filter allProFilter = new Filter();
//            allProFilter.setFieldName(CommonConstants.ACCOUNT_ALL_PRODUCT);
//            allProFilter.setOperator(Operator.EQ);
//            allProFilter.setFieldValues(Lists.newArrayList(Boolean.TRUE.toString()));
//
//            areaWheres.setFilters(Lists.newArrayList(areaFilter,allProFilter,recordFilter));
//
//        }else {
//            Filter areaFilter = new Filter();
//            areaFilter.setFieldName(CommonConstants.ACCOUNT_ATTRIBUTE);
//            areaFilter.setOperator(Operator.NLIKE);
//            areaFilter.setFieldValues(Lists.newArrayList(areaAttributeName));
//
//            Filter proFilter = new Filter();
//            proFilter.setFieldName(CommonConstants.ACCOUNT_PRODUCT);
//            proFilter.setOperator(Operator.NLIKE);
//            proFilter.setFieldValues(Lists.newArrayList(productGroupName));
//
//
//            Filter allProFilter = new Filter();
//            allProFilter.setFieldName(CommonConstants.ACCOUNT_ALL_PRODUCT);
//            allProFilter.setOperator(Operator.NEQ);
//            allProFilter.setFieldValues(Lists.newArrayList(Boolean.TRUE.toString()));
//
//            areaWheres.setFilters(Lists.newArrayList(areaFilter,allProFilter,recordFilter));
//            wheres.setFilters(Lists.newArrayList(proFilter,allProFilter,recordFilter));
//
//        }
//        SearchTemplateQuery query = new SearchTemplateQuery();
//        query.setWheres(Lists.newArrayList(wheres,areaWheres));
//        log.info("findBySearchQuery query {}", JSON.toJSONString(query));

//        List<IObjectData> data = serviceFacade.findBySearchQuery(controllerContext.getUser(), CommonConstants.ACCOUNT_OBJ, query).getData();
//        if(CollectionUtils.isNotEmpty(data)){
//            log.info("data size {}",data.size());
//            result.setStoreIds(data.stream().map(DBRecord::getId).collect(Collectors.toList()));
//        }

//        arg.setSearchQueryInfo(JSON.toJSONString(query));
//
//        result = super.doService(arg);
//
//        return result;
//    }
}
