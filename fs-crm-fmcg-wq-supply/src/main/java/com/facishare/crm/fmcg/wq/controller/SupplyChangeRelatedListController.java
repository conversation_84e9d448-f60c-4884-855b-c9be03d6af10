package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-09-23 17:06
 **/
public class SupplyChangeRelatedListController extends StandardRelatedListController {
    @Override
    protected void before(Arg arg) {
        //target_related_list_in_SupplyChangeObj_DealerSupplyObj__c ,target_related_list_out_SupplyChangeObj_DealerSupplyObj__c
        if ("target_related_list_in_SupplyChangeObj_DealerSupplyObj__c".equals(arg.getRelatedListName()) || "target_related_list_out_SupplyChangeObj_DealerSupplyObj__c".equals(arg.getRelatedListName())) {
            //filter增加一个条件
            ISearchTemplateQuery iSearchTemplateQuery = SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
            SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(iSearchTemplateQuery);
            searchTemplateQueryExt.addFilter(Operator.EQ, "life_status", Lists.newArrayList("under_review"));
            arg.setSearchQueryInfo(searchTemplateQueryExt.toJsonString());
        }
        super.before(arg);
    }
}
