package com.facishare.crm.fmcg.wq.api.store;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface MoveArea {
    @Data
    @ToString
    class Arg implements Serializable {
        private String areaId;  //转移目标的areaId

        private List<String> dataId; //需要转移的dataId

        private String sourceAreaId;  //现在的目标的areaId
    }

    @Data
    @ToString
    class Result implements Serializable {
        private String result ;
    }
}
