package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterBulkInvalidAction extends StandardBulkInvalidAction {
    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected void before(Arg arg) {
        //跳过基础校验
        this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        super.before(arg);
    }
    @Override
    protected Result doAct(Arg arg) {
        List<IObjectData> promoterByIds = pmmService.findByIdsIncludeInvalid(actionContext.getTenantId(), arg.getDataIds());
        //过滤 只停用的
        promoterByIds = promoterByIds.stream().filter(o -> StringUtils.isNotBlank(o.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class)) && pmmService.isLeave(actionContext.getTenantId(),o)).distinct().collect(Collectors.toList());
        Map<String, String> idAndEmpIdMap = promoterByIds.stream().filter(o -> StringUtils.isNotBlank(o.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class))).distinct().collect(Collectors.toMap(k -> k.getId(), v -> v.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class)));
        arg.getDataList().removeIf(o->!idAndEmpIdMap.containsKey(o.getId()));
        Result result = super.doAct(arg);
        //作废状态的删除 其他状态的报错
        if (MapUtils.isNotEmpty(idAndEmpIdMap)) {
            pmmService.invalidPublicEmployee(actionContext.getTenantId(),idAndEmpIdMap.values().stream().collect(Collectors.toList()));
        }
        return result;
    }
}
