package com.facishare.crm.fmcg.wq.api.area;

import com.facishare.appserver.checkins.api.model.BaseResult;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

public interface HandleStore {
    @Data
    @ToString
    class Arg implements Serializable {

        @NotEmpty(message = "数据为空，请填写数据")
        private List<String> storeIds;

        @NotEmpty(message = "数据为空，请填写数据")
        private String areaId;
    }
    @Data
    @ToString
    class Result extends BaseResult {
        @JsonProperty("data_list")
        List<ObjectDataDocument> dataList;

    }
}
