package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.StoreList;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 根据片区id  返回客户列表  分页接口    路线规划用到
 */
@SuppressWarnings("Duplicates")
public class AreaManageStoreListController extends PreDefineController<StoreList.Arg, StoreList.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected StoreList.Result doService(StoreList.Arg arg) {
        StoreList.Result result = new StoreList.Result();
        if (CollectionUtils.isEmpty(arg.getDataId())) {
            throw new ValidateException("id can not be empty");
        }
        QueryResult<IObjectData> queryData = queryCoveredStores(controllerContext,arg);
        if(CollectionUtils.isEmpty(queryData.getData())){
            result.setInfos(Lists.newArrayList());
            result.setTotal(0);
            return result;
        }
        List<String> customIds = queryData.getData().stream().map(o->String.valueOf(o.get(CoveredStoresConstants.STORE))).collect(Collectors.toList());
        log.info("AreaManageStoreListController query customIds is :{}",customIds);

        MetaDataFindService.QueryContext queryContext = MetaDataFindService.QueryContext.builder().user(controllerContext.getUser()).build();
        if (CollectionUtils.isNotEmpty(arg.getFieldProjection())){
            queryContext.setProjectionFields(arg.getFieldProjection());
        }
        List<IObjectData> customInfo = serviceFacade.findObjectDataByIdsWithQueryContext(queryContext, customIds, CommonConstants.ACCOUNT_OBJ);

        IObjectDescribe desc = serviceFacade.findObject(controllerContext.getUser().getTenantId(),CommonConstants.ACCOUNT_OBJ);

//        serviceFacade.fillObjectDataWithRefObject(desc, customInfo, this.controllerContext.getUser());
//        log.info("AreaManageStoreListController query fillObjectDataWithRefObject is :{}",customInfo);

        serviceFacade.fillUserInfo(desc, customInfo, this.controllerContext.getUser());
//        log.info("AreaManageStoreListController query fillUserInfo is :{}",customInfo);
        result.setInfos(ObjectDataDocument.ofList(customInfo));
        result.setTotal(queryData.getTotalNumber());
        return result;
    }

    private QueryResult<IObjectData> queryCoveredStores(ControllerContext context, StoreList.Arg arg) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(arg.getLimit());
        query.setOffset(arg.getOffset());

        Filter areaFilter = new Filter();
        areaFilter.setFieldName(CoveredStoresConstants.BELONG_AREA);
        areaFilter.setOperator(Operator.IN);
        areaFilter.setFieldValues(arg.getDataId());

        OrderBy orderBy = new OrderBy();
        orderBy.setFieldName(CommonConstants.CREATE_TIME);
        orderBy.setIsAsc(true);

        query.setFilters(Lists.newArrayList(areaFilter));
        query.setOrders(Lists.newArrayList(orderBy));
        return serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), CoveredStoresConstants.COVERED_STORES_OBJ, query);
    }
}
