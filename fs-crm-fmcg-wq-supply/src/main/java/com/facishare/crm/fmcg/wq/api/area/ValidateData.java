package com.facishare.crm.fmcg.wq.api.area;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface ValidateData {

    @Data
    class Arg implements Serializable{
        public List<ObjectData> objectDataList;
    }

    @Data
    class Result implements Serializable{
        int errorCode;
        List<String> errorMessageList;
    }

    @Data
    class ObjectData{
        @JsonProperty("object_data")
        public ObjectDataDocument objectDataDocument;

        private Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
    }

}
