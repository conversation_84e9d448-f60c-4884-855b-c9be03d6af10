package com.facishare.crm.fmcg.wq.action;
import com.google.common.collect.Maps;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.appserver.checkins.api.model.AreaPersonnelConfigPo;
import com.facishare.appserver.checkins.api.model.CheckUserlimit;
import com.facishare.appserver.checkins.api.model.FuncCode;
import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.crm.fmcg.wq.model.PostInfo;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.crm.fmcg.wq.service.OrganizationJMLService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Slf4j
public class AreaManageAddAction extends BaseAddAction {

    private static final OrganizationJMLService organizationJMLService = SpringUtil.getContext().getBean(OrganizationJMLService.class);

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    CheckinsDao checkinsDao = SpringUtil.getContext().getBean(CheckinsDao.class);
    CheckinsProxy checkinsProxy = SpringUtil.getContext().getBean(CheckinsProxy.class);

    @Override
    protected void before(Arg arg) {
        String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
        List<String> areaOwnerArray = (List<String>) arg.getObjectData().get(AreaManageConstants.AREA_OWNER_PRESER);
        if (!CollectionUtils.isEmpty(areaOwnerArray)) {
            //功能权限
            CheckUserlimit.Args checkUserlimitArgs = new CheckUserlimit.Args();
            checkUserlimitArgs.setTenantId(actionContext.getTenantId());
            checkUserlimitArgs.setUserId(Integer.valueOf(areaOwnerArray.get(0)));
            checkUserlimitArgs.setFuncCodes(new FuncCode[]{FuncCode.sfaVisit_areaManage});
           if (!checkinsProxy.userLimitCheck(actionContext.getTenantId(),checkUserlimitArgs).isResult()){
               throw new FunctionException(I18N.text("oaappsrv.waiqin.msg.sfaVisit1",I18N.text("AreaManageObj.field.area_owner.label")));//您没有SFA拜访使用权限，请与管理员联系授权！
           }
        }
        if(FmcgGray.Checkins.EA.areaManage.gray(ea)) {
            arg.getObjectData().put(AreaManageConstants.DATA_OWN_DEPARTMENT, arg.getObjectData().get(AreaManageConstants.COUNTY__C));
            if (Objects.nonNull(arg.getObjectData().get(AreaManageConstants.AREA_OWNER))) {
                arg.getObjectData().put(AreaManageConstants.OWNER, arg.getObjectData().get(AreaManageConstants.AREA_OWNER));
            }
            arg.getObjectData().get(AreaManageConstants.AREA_OWNER);
            log.info("AreaManageAddAction add !!");
        }
        if(FmcgGray.Checkins.EA.areaManage.gray(ea) || GrayRelease.isAllow("checkin-server-v2","isYLArea",actionContext.getEa())) {
            //同一个人只能新建一个片区
            validateData(arg);
        }else {
            buildAreaManageOwner(arg);
            LinkedHashMap<String, AreaPersonnelConfigPo> personnelApiNameAndConfig = checkinsDao.getAreaPersonnelSetting(actionContext.getTenantId()).getPersonnelApiNameAndConfig();
            if(Objects.nonNull(arg.getObjectData().get("adminMultiArea"))){
                Map<String, PostInfo> personnelAndMultiAreaMap = JSONObject.parseObject(JSONObject.toJSONString(arg.getObjectData().get("adminMultiArea")), new TypeReference<Map<String,PostInfo>>() {});
                personnelAndMultiAreaMap.forEach((k,v)->{
                    if(MapUtils.isNullOrEmpty(personnelApiNameAndConfig) || Objects.isNull(personnelApiNameAndConfig.get(k)) || !personnelApiNameAndConfig.get(k).getIsStrideArea()) {
                        v.setApiName(k);
                        validateDataNew(arg, v);
                    }
                });
                arg.getObjectData().remove("adminMultiArea");
            }
        }
        //校验从对象数据
        validateDetailData(arg);
        super.before(arg);
    }

    /**
     * 校验从对象 数据
     * @param arg
     */
    private void validateDetailData(Arg arg) {
        //校验不能重复
        if(!CollectionUtils.isEmpty(arg.getDetails())) {
            if (!CollectionUtils.isEmpty(arg.getDetails().get(CoveredStoresConstants.COVERED_STORES_OBJ))) {
                List<String> coveredIds = arg.getDetails().get(CoveredStoresConstants.COVERED_STORES_OBJ).stream().filter(o -> Objects.nonNull(o.get(CoveredStoresConstants.STORE))).map(o -> o.get(CoveredStoresConstants.STORE).toString()).collect(Collectors.toList());
                long count = coveredIds.stream().distinct().count();
                if(coveredIds.size() != count){
                    throw new ValidateException("覆盖门店中含有相同的门店，请检查！"); //ignoreI18n
                }
            }
        }
    }

    private void buildAreaManageOwner(Arg arg){
        List<String> areaOwnerList = (List<String>)arg.getObjectData().get(AreaManageConstants.AREA_OWNER_PRESER);
        if(!CollectionUtils.isEmpty(areaOwnerList)){
            arg.getObjectData().put(AreaManageConstants.OWNER,areaOwnerList);
        }
        // 先取部门负责人
        List<String> departIds = (List<String>)arg.getObjectData().get(AreaManageConstants.BUSINESS_GROUP_PRESET);
        if(!CollectionUtils.isEmpty(departIds)){
            List<IObjectData> departmentDataList = supplyDao.getDepartmentList(actionContext.getTenantId(),departIds);
            List<String> managerIds = (List<String>)departmentDataList.get(0).get("manager_id");
            if(!CollectionUtils.isEmpty(managerIds)){
                arg.getObjectData().put(AreaManageConstants.OWNER,managerIds);
            }
        }
        List<String> ownerList = (List<String>)arg.getObjectData().get(AreaManageConstants.OWNER);
        if(CollectionUtils.isEmpty(ownerList)){
            arg.getObjectData().put(AreaManageConstants.OWNER,Lists.newArrayList(actionContext.getUser().getUserId()));
        }
    }

    /**
     * 产品组+片区属性+负责人，不可重复；举例：当一个片区产品组为“A组”，片区属性为：区域承包，则不能在创建同一个相同内容的片区
     * 字段关系校验
     *
     * @param arg
     */
    @Override
    protected void validateData(Arg arg) {
//        String productGroup = (String) arg.getObjectData().get(AreaManageConstants.PRODUCT_GROUP);
//        String areaAttribute = (String) arg.getObjectData().get(AreaManageConstants.AREA_ATTRIBUTE);
//        List<String> departmentIdArray = (List<String>) arg.getObjectData().get(departmentId_name);//县级所/归属部门
//        List<String> dealerArray = (List<String>) arg.getObjectData().get(AreaManageConstants.DEALER);//经销商
        List<String> areaOwnerArray = (List<String>) arg.getObjectData().get(AreaManageConstants.AREA_OWNER);//片区负责人
        /*if (CollectionUtils.isEmpty(departmentIdArray)) {
            throw new ValidateException("县级所不能为空");
        }
        if (!CollectionUtils.isEmpty(dealerArray)) {
            //查询经销商上级部门
            List<DepartmentDto> departmentIds = organizationJMLService.getDepartmentIds(actionContext.getUser().getTenantIdInt(), dealerArray.get(0));
            if (!CollectionUtils.isEmpty(departmentIds)) {
                if (departmentIds.size() != 6) {
                    throw new ValidateException("请选择六级经销商");
                }
//                if (!Integer.valueOf(departmentIdArray.get(0)).equals(departmentIds.get(departmentIds.size() - 1).getDepartmentId())) {
//                    throw new ValidateException("请选择县级所下的经销商");
//                }
            }else {
                throw new ValidateException("请选择六级经销商");
            }
//            if (!CollectionUtils.isEmpty(areaOwnerArray)) {
//                Integer areaOwnerDepartmentId = organizationJMLService.getDepartmentId(actionContext.getUser().getTenantIdInt(), Integer.valueOf(areaOwnerArray.get(0)));
//                if (areaOwnerDepartmentId!=-1) {
//                    if (!Integer.valueOf(dealerArray.get(0)).equals(areaOwnerDepartmentId)) {
//                        log.info("AreaManageAdd:Uniqueness check  片区负责人的部门 : {}", areaOwnerDepartmentId);
//                        throw new ValidateException("请选择经销商下的片区负责人");
//                    }
//                }else{
//                    throw new ValidateException("请选择经销商下的片区负责人");
//                }
//            }
        }else {
            //throw new ValidateException("经销商不能为空");
        }*/
        //校验归县级所是5级部门
        //super.validateData(arg);

        if (!CollectionUtils.isEmpty(areaOwnerArray)) {
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(1);
            query.setOffset(0);
            Filter ownerFilter = new Filter();
            ownerFilter.setFieldName(AreaManageConstants.AREA_OWNER);
            ownerFilter.setOperator(Operator.EQ);
            ownerFilter.setFieldValues(Lists.newArrayList(areaOwnerArray.get(0)));

            query.setFilters(Lists.newArrayList(ownerFilter));

            List<IObjectData> getData = serviceFacade.findBySearchQuery(actionContext.getUser(), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
            log.info("AreaManageAdd:Uniqueness check  getData : {}, query : {}", getData, query);
            if (!CollectionUtils.isEmpty(getData)) {
                throw new ValidateException("该人员已规划片区，请不要重复创建！"); //ignoreI18n
            }
        }

    }

    protected void validateDataNew(Arg arg,PostInfo postInfo) {
        List<String> areaOwnerArray = (List<String>) arg.getObjectData().get(postInfo.getApiName());//片区负责人
        if (!CollectionUtils.isEmpty(areaOwnerArray)) {
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(1);
            query.setOffset(0);
            query.setNeedReturnCountNum(false);
            Filter ownerFilter = new Filter();
            ownerFilter.setFieldName(postInfo.getApiName());
            ownerFilter.setOperator(Operator.EQ);
            ownerFilter.setFieldValues(areaOwnerArray);

            query.setFilters(Lists.newArrayList(ownerFilter));

            List<IObjectData> getData = serviceFacade.findBySearchQuery(actionContext.getUser(), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
            log.info("AreaManageAdd:Uniqueness check  getData : {}, query : {}", getData, query);
            if (!CollectionUtils.isEmpty(getData)) {
                throw new ValidateException(postInfo.getUserName() + "已有片区，不可重复分配片区，请修改。"); //ignoreI18n
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result){
        super.after(arg,result);
        try {
            if(!GrayRelease.isAllow("checkin-server-v2", "areaManage",actionContext.getEa())) {
                List<ObjectDataDocument> iObjectDataList = arg.getDetails().get("CoveredStoresObj");
                if(!CollectionUtils.isEmpty(iObjectDataList)) {
                    List<String> accountIdList = iObjectDataList.stream().map(o -> String.valueOf(o.get("store"))).collect(Collectors.toList());
                    // 查询客户数据 更新数据负责人和路线负责人
                    Boolean isYL = GrayRelease.isAllow("checkin-server-v2", "isYLArea", actionContext.getEa());
                    supplyDao.updateAccountAreaInfo(actionContext.getTenantId(), actionContext.getUser().getUserIdInt(), arg.getObjectData().toObjectData(), accountIdList, isYL);
                }
            }
        }catch (Exception e){
            log.error("AreaManageAddAction is error",e);
        }
        return result;
    }
}
