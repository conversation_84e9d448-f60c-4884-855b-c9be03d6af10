package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterBulkDeleteAction extends StandardBulkDeleteAction {

    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected void before(Arg arg) {
        //跳过基础校验
        this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        List<IObjectData> promoterByIds = pmmService.findByIdsIncludeInvalid(actionContext.getTenantId(), arg.getIdList());
        Map<String, String> idAndEmpIdMap = promoterByIds.stream().filter(o -> StringUtils.isNotBlank(o.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class))).distinct().collect(Collectors.toMap(k -> k.getId(), v -> v.get(PromoterFields.PUBLIC_EMPLOYEE_ID, String.class)));
        //life_status:"invalid"
       List<String> invalidList = supplyDao.getByIds(actionContext.getTenantId(), "PublicEmployeeObj", idAndEmpIdMap.values().stream().distinct().collect(Collectors.toList()))
                .stream().filter(o -> "invalid".equals(o.get("life_status", String.class))).map(o->o.getId()).collect(Collectors.toList());
        //作废状态的删除 其他状态的报错
        if (CollectionUtils.isNotEmpty(invalidList)) {
            pmmService.delPublicEmployee(actionContext.getTenantId(),actionContext.getUser().getUserId(),invalidList);
        }
        Result result = super.doAct(arg);
        return result;
    }
}
