package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.api.area.DealerSupplyUpSave;
import com.facishare.crm.fmcg.wq.api.supply.ShopQueryDealerSupply;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.Product;
import com.facishare.crm.fmcg.wq.model.Shop;
import com.facishare.crm.fmcg.wq.model.Supplier;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.service.SyncAvailableRangeService;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description: 拆单逻辑
 * @author: zhangsm
 * @create: 2021-05-08 15:25
 **/
public class DealerSupplyShopQueryController extends PreDefineController<ShopQueryDealerSupply.Arg, ShopQueryDealerSupply.Result> {
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SyncAvailableRangeService syncAvailableRangeService = SpringUtil.getContext().getBean(SyncAvailableRangeService.class);
    RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);
    AsyncTaskExecutor fmcgSyncTaskExecutor = SpringUtil.getContext().getBean("fmcgSyncTaskExecutor", AsyncTaskExecutor.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }


    /**
     * todo : add stop watch
     * todo : add error record
     * @param arg
     *
     * @return
     */
    @Override
    protected ShopQueryDealerSupply.Result doService(ShopQueryDealerSupply.Arg arg) {
        ShopQueryDealerSupply.Result result = new ShopQueryDealerSupply.Result();
        Map<String, ShopQueryDealerSupply.Supplier> resultMap = new HashMap<>();
        result.setProductIdAndSupplierMap(resultMap);
        arg.getProductIdList().removeIf(o -> StringUtils.isBlank(o) || o.toLowerCase().equals("null"));
        if (StringUtils.isBlank(arg.getShopId()) || CollectionUtils.isEmpty(arg.getProductIdList()) || arg.getShopId().toLowerCase().equals("null")) {
            return result;
        }
        String tenantId = controllerContext.getTenantId();
        String shopQueryResultByRedis = redisUtils.getShopQueryResultByRedis(tenantId, arg.getShopId());
        Shop shopObjById = null;
        IObjectData accountObjById = null;
        // todo : use findByIdIngnoreAll
        // todo : add cache

        if (StringUtils.isNotBlank(shopQueryResultByRedis)) {
            shopObjById = JSON.parseObject(shopQueryResultByRedis, Shop.class);
        } else {
            accountObjById =
//                supplyDao.getAllIObjectDataListByQuery(tenantId, SearchQuery.builder().eq("_id", arg.getShopId()).build(), "AccountObj");
                    serviceFacade.findObjectDataIgnoreAll(controllerContext.getUser(),arg.getShopId(), AccountObjConstants.API_NAME);
            if (accountObjById == null || !AccountObjConstants.Value.default__c.toString().equals(accountObjById.getRecordType())) {
                return result;
            }
            shopObjById = supplyService.getShopObjById(tenantId, arg.getShopId(), 99);
            stopWatch.lap("getShopObjById");
            redisUtils.addShopRedis(tenantId, arg.getShopId(), JSON.toJSONString(shopObjById));
        }
        List<Product> parentProduct = Lists.newArrayList();
        shopObjById.getSupplierList().stream().filter(o -> o.getLevel() != 0).forEach(o -> parentProduct.addAll(o.getParentProductRange()));
        Map<String, String> parentIdMap = Maps.newHashMap();
        for (Supplier supplier : shopObjById.getSupplierList()) {
            for (Product product : supplier.getProductList()) {
                String productId = product.getProductId();
                if (supplier.getParentSuppliers().size() == 1) {
                    parentIdMap.put(productId, supplier.getParentSuppliers().get(0).getId());
                } else {
                    for (Supplier parentSupplier : supplier.getParentSuppliers()) {
                        for (Product pProduct : parentSupplier.getProductList()) {
                            if (pProduct.getProductId().equals("ALL") || pProduct.getProductId().equals(productId)) {
                                parentIdMap.put(productId, parentSupplier.getId());
                            }
                        }
                    }

                }
            }
        }

        Map<String, String> map = shopObjById.getProductList().stream().collect(Collectors.toMap(o -> o.getProductId(), v -> v.getSupplierId()));
        List<String> errorProductIds = Lists.newArrayList();
        for (String productId : arg.getProductIdList()) {
            ShopQueryDealerSupply.Supplier supplier = new ShopQueryDealerSupply.Supplier();
            String distributorId = map.get(productId);
            if (StringUtils.isBlank(distributorId)) {
                distributorId = map.get("ALL");
            }
            supplier.setDistributorId(distributorId);
            String parentDealerId = parentIdMap.get(productId);
            if (StringUtils.isBlank(parentDealerId)) {
                parentDealerId = parentIdMap.get("ALL");
            }
            if (StringUtils.isNotBlank(parentDealerId)) {
//                supplier.setDealerId(parentDealerId.length() > 18 ? parentDealerId : supplier.getDistributorId());
                supplier.setDealerId(parentDealerId.length() < 13 ? supplier.getDistributorId() : parentDealerId);
            }
            resultMap.put(productId, supplier);
            if (StringUtils.isBlank(distributorId)) {
                errorProductIds.add(productId);
            }
        }
        stopWatch.lap("handleProduct");
        IObjectData finalAccountObjById = accountObjById;
        Shop finalShopObjById = shopObjById;
        /**
         * 发现门店有配送商  但是 门店的配送商字段为空， 需要重新同步下
         * 第一次同步就可以
         */
        if (!GrayRelease.isAllow("checkin-server-v2", "ylOldCheckCoveredProduct", tenantId)) {
            if (accountObjById != null && CollectionUtils.isNotEmpty(shopObjById.getSupplierList())) {
                //重新同步下
                fmcgSyncTaskExecutor.execute(() -> {
                    try {
                        log.info("reSync updateShopUpSupplyFields id {}",finalAccountObjById.getId());
                        List<String> upDistributors = finalAccountObjById.get("up_distributors__c", List.class);
                        if (CollectionUtils.isEmpty(upDistributors)) {
                            if (AccountObjConstants.Value.distributor__c.toString().equals(finalAccountObjById.getRecordType())) {
                                supplyDao.updateDistributorUpSupplyFields(tenantId, arg.getShopId());
                            } else {
                                supplyDao.updateShopUpSupplyFields(tenantId, arg.getShopId());
                            }
                        } else {
                            Set<String> collect = finalShopObjById.getSupplierList().stream().map(o -> o.getId()).collect(Collectors.toSet());
                            Collection disjunction = CollectionUtils.disjunction(upDistributors, collect);
                            if (CollectionUtils.isNotEmpty(disjunction)) {
                                log.info("id {} ,beforeUpIds {},afterIds {}", arg.getShopId(), upDistributors, collect);
                                if (AccountObjConstants.Value.distributor__c.toString().equals(finalAccountObjById.getRecordType())) {
                                    supplyDao.updateDistributorUpSupplyFields(tenantId, arg.getShopId());
                                } else {
                                    supplyDao.updateShopUpSupplyFields(tenantId, arg.getShopId());
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("updateAccountSupply error {}", arg.getShopId(), e);
                    } finally {
                    }
                });

            }
        }
        stopWatch.lap("updateShopUpSupplyFields");
        /**
         * 有找不到的产品的时候。尝试 同步可售范围的操作
         */
        if (CollectionUtils.isNotEmpty(errorProductIds)) {
            //如果 有供货商
            if (CollectionUtils.isNotEmpty(shopObjById.getSupplierList())) {
                /**
                 * 有配送商的话 ，重新同步一次可售范围，再下次找产品
                 */
                Long aLong = redisUtils.checkCount(tenantId, shopObjById.getId());
                if (aLong != null && aLong >= 3) {
                    List<IObjectData> iObjectDataList = supplyDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder().in("_id", errorProductIds).build()
                            , "ProductObj", Lists.newArrayList("name"));
                    throw new ValidateException(String.format("产品 %s 没有找到对应的供货关系", //ignoreI18n
                            iObjectDataList.stream().map(o -> o.get("name", String.class)).collect(Collectors.joining(","))));
                } else if (!redisUtils.syncAvaRangeDoing(tenantId, shopObjById.getId())) {
                    //重新同步 可售范围 可能没有查accountObjById
                    fmcgSyncTaskExecutor.execute(()->{
                        try {
                            log.info("reSync available id {}",finalShopObjById.getId());
                            redisUtils.syncAvaRangeAdd(tenantId, finalShopObjById.getId());
                            redisUtils.addCount(tenantId, finalShopObjById.getId());
                            syncAvailableRangeService.updateAllAvailableDataByShop(tenantId, finalShopObjById);
                            supplyDao.updateShopUpSupplyFields(tenantId, arg.getShopId());
                        } catch (Exception e) {
                            log.error("updateAllAvailableDataByShop error tenantId {},shopId {}", tenantId, finalShopObjById.getId(), e);
                        } finally {
                            redisUtils.syncAvaRangeRemove(tenantId, finalShopObjById.getId());
                            redisUtils.removeShopRedis(tenantId, arg.getShopId());
                        }
                    });
                    stopWatch.lap("reSyncAvailable");
                    throw new ValidateException("正在同步可售产品中，稍后重试"); //ignoreI18n
//                    throw new ValidateException("可售产品已经更新为最新，请刷新页面重新进入。");
                } else {
                    stopWatch.lap("reSyncAvailable");
                    throw new ValidateException("正在同步可售产品中，稍后重试"); //ignoreI18n
                }
            } else {
                /**
                 * 如果门店上有配送商字段，则 按照 门店上的配送商 字段重新同步一遍 供货关系
                 */
                redisUtils.removeShopRedis(tenantId, arg.getShopId());
                if (!GrayRelease.isAllow("checkin-server-v2", "ylOldCheckCoveredProduct", tenantId)) {
                    //一般 都是 第一次
                    if (accountObjById != null && !accountObjById.isDeleted() && CollectionUtils.isNotEmpty(accountObjById.get("up_distributors__c", List.class))) {
                        //重新同步下
                        ObjectDataExt obj = ObjectDataExt.of(accountObjById);
                        if (!obj.isLock()) {
                            fmcgSyncTaskExecutor.execute(() -> {
                                try {
                                    log.info("reSync upSaveDealerSupply id {}",finalAccountObjById.getId());
                                    DealerSupplyUpSave.Arg arg1 = new DealerSupplyUpSave.Arg();
                                    arg1.setIObjectData(ObjectDataDocument.of(obj));
                                    arg1.setNoUpdate(false);
                                    supplyService.upSaveDealerSupply(tenantId, arg1);
                                } catch (Exception e) {
                                    log.info("upsaveDearlerSupply error tenantId {} , accountId {}",tenantId,obj.getId(),e);
                                } finally {
                                    //会重新同步一下 客户上的配送商
                                    if (AccountObjConstants.Value.distributor__c.toString().equals(obj.getRecordType())) {
                                        supplyDao.updateDistributorUpSupplyFields(tenantId, arg.getShopId());
                                    } else {
                                        supplyDao.updateShopUpSupplyFields(tenantId, arg.getShopId());
                                    }

                                }
                            });
                        }

                    }
                }
                stopWatch.lap("reSyncUpSaveDealerSupply");
                throw new ValidateException("所选门店没有找到可用的供货关系，请联系管理员，检查供货关系对象。"); //ignoreI18n
//                List<IObjectData> productObjs = supplyDao.getByIds(controllerContext.getTenantId(), "ProductObj", errorProductIds);
//                throw new ValidateException(String.format("%s 未找到对应配送商"
//                        , productObjs.stream().map(o -> o.getName()).collect(Collectors.joining(","))));
            }
        }
        return result;
    }
}
