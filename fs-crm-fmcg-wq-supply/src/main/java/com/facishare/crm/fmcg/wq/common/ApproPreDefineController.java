package com.facishare.crm.fmcg.wq.common;

import com.facishare.crm.fmcg.wq.api.supply.SupplyApproArgs;
import com.facishare.paas.appframework.core.model.PreDefineController;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.units.qual.A;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-11-08 16:48
 **/
public abstract class ApproPreDefineController<A extends SupplyApproArgs, R> extends PreDefineController<A, R> {
    @Override
    protected void before(A arg) {
        super.before(arg);
    }

    @Override
    protected R doService(A a) {
        //生成审批对象日志
        if (a.isPass()) {
            //跳过 直接执行逻辑
        } else if (StringUtils.isNotBlank(a.getSupplyApproId())) {
            // 有审批id 说明是审批流程
        }else{
            //第一次执行
            //生成审批对象
            //判断审批流是否结束
        }
        return result;
    }
    abstract String pathApiName();
    abstract String pathMethod();
}
