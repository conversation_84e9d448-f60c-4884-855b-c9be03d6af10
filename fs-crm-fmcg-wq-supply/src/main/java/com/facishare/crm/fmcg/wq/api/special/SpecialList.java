package com.facishare.crm.fmcg.wq.api.special;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.wq.api.supply.SupplyApproArgs;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface SpecialList {
    @Data
    @ToString
    class Arg extends SupplyApproArgs implements Serializable {


        @JSONField(name = "supply_id")
        @JsonProperty("supply_id")
        @SerializedName("supply_id")
        private String dealerSupplyId;// 供货关系ID

        @JSONField(name = "distributor_id")
        @JsonProperty("distributor_id")
        @SerializedName("distributor_id")
        private String distributorId;// 配送商或者经销商ID

        @JSONField(name = "store_ids")
        @JsonProperty("store_ids")
        @SerializedName("store_ids")
        private List<String> storeIds;

        @JSONField(name = "product_ids")
        @JsonProperty("product_ids")
        @SerializedName("product_ids")
        private List<String> productIds;

//        @JSONField(name = "supply_type")
//        @JsonProperty("supply_type")
//        @SerializedName("supply_type")
//        private String supplyType;

        @JSONField(name = "button_type")
        @JsonProperty("button_type")
        @SerializedName("button_type")
        private int buttonType;//添加特例供货门店 -0 添加特例配送商 -1

    }

    @Data
    @ToString
    class Result implements Serializable {

        @SerializedName("exist_info")
        @JSONField(name = "exist_info")
        @JsonProperty("exist_info")
        private List<String> existInfo;

        @SerializedName("exist_map")
        @JSONField(name = "exist_map")
        @JsonProperty("exist_map")
        private Map<String,List<String>> existMap;

        @SerializedName("conflict_info")
        @JSONField(name = "conflict_info")
        @JsonProperty("conflict_info")
        private Map<String,List<String>> specialInfos;//conflictInfos


    }

}
