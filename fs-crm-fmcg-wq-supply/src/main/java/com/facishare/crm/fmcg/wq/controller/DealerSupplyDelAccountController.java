package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.api.area.DealerSupplyReset;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.ListUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 重置客户上的供货关系字段
 * @author: zhangsm
 * @create: 2021-06-11 14:41
 **/
public class DealerSupplyDelAccountController extends PreDefineController<DealerSupplyReset.Arg, DealerSupplyReset.Result> {
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }


    @Override
    protected DealerSupplyReset.Result doService(DealerSupplyReset.Arg arg) {
        result = new DealerSupplyReset.Result();
        String tenantId = controllerContext.getTenantId();
        String accountId = arg.getAccountId();
        //客户作废，删除供货关系
        List<String> dataIds = Lists.newArrayList(accountId);
        supplyDao.delSupplyAboutDelAccount(tenantId,dataIds);
        return result;
    }
}
