package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerListLayoutResourceController;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.List;
import java.util.stream.Collectors;

public class AreaManageDesignerListLayoutResourceController extends StandardDesignerListLayoutResourceController {

//    @Override
//    protected List<IButton> getNormalButtons() {
//        List<IButton> IButtonList = super.getNormalButtons();
//        List<String> buttonApiNameList = IButtonList.stream().map(o->o.getName()).collect(Collectors.toList());
//        if(!buttonApiNameList.contains(ObjectAction.AREA_BATCH_ADD.getButtonApiName())){
//            Button button = new Button();
//            button.setAction(ObjectAction.AREA_BATCH_ADD.getActionCode());
//            button.setActionType("default");
//            button.setName(ObjectAction.AREA_BATCH_ADD.getButtonApiName());
//            button.setLabel(ObjectAction.AREA_BATCH_ADD.getActionLabel());
//            button.set("page_type","list");
//            button.set("terminal","web");
//            IButtonList.add(button);
//        }
//        return IButtonList;
//    }

    @Override
    protected List<IButton> getBatchButtons() {
        List<IButton> IButtonList = super.getBatchButtons();
        if(CollectionUtils.isNotEmpty(IButtonList)) {
            List<String> buttonApiNameList = IButtonList.stream().map(o->o.getName()).collect(Collectors.toList());
            ObjectUtils.buttonList.forEach(o -> {
                if (!buttonApiNameList.contains(o.getButtonApiName()) && !ObjectAction.AREA_BATCH_ADD.getButtonApiName().equals(o.getButtonApiName())) {
                    Button button = new Button();
                    button.setAction(o.getActionCode());
                    button.setActionType("default");
                    button.setName(o.getButtonApiName());
                    button.setLabel(o.getActionLabel());
                    button.set("page_type", "list");
                    button.set("terminal", "web");
                    IButtonList.add(button);
                }
            });
        }
        return IButtonList;
    }

}
