package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 *
 */
public class CoveredStoresInsertImportDataAction extends StandardInsertImportDataAction {
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    private static final Logger log = LoggerFactory.getLogger(CoveredStoresInsertImportDataAction.class);

    @Override
    protected void before(Arg arg) {
        log.debug("before CoveredStoresInsertImportDataAction,for arg:{}", arg);
        super.before(arg);

    }

    @Override
    protected void customAfterImport(List<IObjectData> actualList) {
        super.customAfterImport(actualList);
        String ei = actionContext.getTenantId();
        log.info("CoveredStoresInsertImportDataAction ei:{},{}",ei,actualList.size());
//        String ei = actionContext.getTenantId();
        String ea = serviceFacade.getEAByEI(ei);
        if(!GrayRelease.isAllow("checkin-server-v2", "areaManage",ea)){
            Boolean isYL = GrayRelease.isAllow("checkin-server-v2", "isYLArea",ea);
            actualList.forEach(data -> {
                String storeId =(String) data.get(CoveredStoresConstants.STORE);
                String areaId= (String) data.get(CoveredStoresConstants.BELONG_AREA);
                List<String> accountIdList = Lists.newArrayList(storeId);
                IObjectData areaInfo = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),areaId, AreaManageConstants.AREA_MANAGE_OBJ);
                supplyDao.updateAccountAreaInfo(actionContext.getTenantId(),actionContext.getUser().getUserIdInt(),areaInfo,accountIdList,isYL);

            });
        }
    }
}