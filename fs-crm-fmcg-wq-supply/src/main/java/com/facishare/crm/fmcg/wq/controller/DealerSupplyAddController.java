package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.AddDealerSupply;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.Shop;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.service.SyncAvailableRangeService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description:  指定门店的配送商逻辑
 * @author: zhangsm
 * @create: 2021-05-08 15:25
 **/
public class DealerSupplyAddController extends PreDefineController<AddDealerSupply.Arg, AddDealerSupply.Result> {
    SupplyDao supplyDao =  SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyService supplyService=SpringUtil.getContext().getBean(SupplyService.class);
    SyncAvailableRangeService syncAvailableRangeService= SpringUtil.getContext().getBean(SyncAvailableRangeService.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }


    @Override
    protected AddDealerSupply.Result doService(AddDealerSupply.Arg arg) {
        AddDealerSupply.Result result = new  AddDealerSupply.Result();
        if (CollectionUtils.isEmpty(arg.getAddSupplierIds())){
            return result;
        }
        AccountObj thisAccountObj = supplyDao.getAccountObjById(controllerContext.getTenantId(),arg.getAccountId());
        supplyService.checkAddSupplyStoreObj(controllerContext.getTenantId(),thisAccountObj,arg.getAddSupplierIds());
        // 3. 验证都没毛病了 创建数据
        // 3.1 查询是否已经存在对应的供货关系数据
        List<IObjectData> dealerSupplyBySuppliers = supplyDao.getDealerSupplyBySuppliers(controllerContext.getTenantId(),
                arg.getAddSupplierIds());
        //审核
        if (CollectionUtils.isNotEmpty(dealerSupplyBySuppliers)){
            supplyOperaContext.setArgs(arg);
            supplyOperaContext.setResultClazz(AddDealerSupply.Result.class);
            supplyOperaContext.setInUpCustomerId(arg.getAddSupplierIds().get(0));
            supplyOperaContext.setInUpSupplyId(dealerSupplyBySuppliers.get(0).getId());
            supplyOperaContext.setCustermerIds(Lists.newArrayList(arg.getAccountId()));
            //触发审批流程
            Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(controllerContext, supplyOperaContext);
            if (booleanIObjectDataPair.first){
                throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE,SupplyOperaContext.TRIGGERAPPROCODE);
            }else{
                supplyDao.createSupplyStoreObj(controllerContext.getTenantId(),thisAccountObj,dealerSupplyBySuppliers);
            }
        }
        return result;
    }

    @Override
    protected AddDealerSupply.Result after(AddDealerSupply.Arg arg, AddDealerSupply.Result result) {
        AddDealerSupply.Result after = super.after(arg, result);
        fmcgThreadPoolExecutor.execute(
                () -> {
                    Shop shopObjById = supplyService.getShopObjById(controllerContext.getTenantId(), arg.getAccountId(), 99);
                    syncAvailableRangeService.updateAllAvailableDataByShop(controllerContext.getTenantId(), shopObjById);
                });
        return after;
    }
}
