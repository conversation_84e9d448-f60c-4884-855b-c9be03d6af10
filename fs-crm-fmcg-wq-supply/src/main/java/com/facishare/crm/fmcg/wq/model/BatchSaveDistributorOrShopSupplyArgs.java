package com.facishare.crm.fmcg.wq.model;

import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;
@Data
@Builder
public class BatchSaveDistributorOrShopSupplyArgs {
    private  String tenantId;
    private  String userId;
    private  List<String> addShopIds;
    private  Map<String, Set<IObjectData>> sourceArgsSpecialMap;
    private  List<String> addSpecialProductIds;
    private  boolean isCoverSpecial;
    private  String upSupplyId;
    private  String dealerId;
    private  Object ownDepartment;
    private  boolean isDistributorSupply;
    private  boolean isSpecial;
    private  List<IObjectData> dealerSupplyData;
    private SupplyService.BatchSaveError resultError;
}
