package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.service.RouteService;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2023 - 04 - 06  16:28
 **/
@Slf4j
public class VisitRouteFlowCompletedAction extends StandardFlowCompletedAction {

    private RouteService routeService = SpringUtil.getContext().getBean(RouteService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("VisitRouteFlowCompletedAction after is start!");
        IObjectData newObjectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getDataId(), objectDescribe.getApiName());
        String tenantId = newObjectData.getTenantId();
        String routeId = newObjectData.getId();
        ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(this.data).getLifeStatus();
        log.info("VisitRouteFlowCompletedAction tenantId:{},objectLifeStatus:{}",tenantId,objectLifeStatus.getCode());
        routeService.routeFlowCompleted(tenantId,routeId,arg.isPass(),objectLifeStatus.getCode());
        return super.after(arg, result);
    }
}
