package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 片区作废
 */
@Slf4j
public class AreaManageBulkInvalidAction extends StandardBulkInvalidAction {

    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        String ei = actionContext.getTenantId();
        String ea = serviceFacade.getEAByEI(ei);
        Boolean isYL = GrayRelease.isAllow("checkin-server-v2", "isYLArea",ea);
        if(!GrayRelease.isAllow("checkin-server-v2", "areaManage",ea)){
            log.info("AreaManageInvalidAction ,{}",arg.getDataIds());
//            fmcgThreadPoolExecutor.execute(() -> {
                try {
                    SearchTemplateQuery query = new SearchTemplateQuery();
                    query.setLimit(1500);
                    query.setOffset(0);
                    Filter areaFilter = new Filter();
                    areaFilter.setFieldName(isYL ? CommonConstants.Account_BelongArea : CommonConstants.Account_BelongArea_Preset);
                    areaFilter.setOperator(Operator.IN);
                    areaFilter.setFieldValues(arg.getDataIds());
                    query.setFilters(Lists.newArrayList(areaFilter));
                    query.setNeedReturnCountNum(false);
                    List<IObjectData> data = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), CommonConstants.ACCOUNT_OBJ, query).getData();
                    if (CollectionUtils.isNotEmpty(data)) {
                        List<String> ids = data.stream().map(o->o.getId()).collect(Collectors.toList());
                        supplyDao.updateAccountAreaInfo(ei, -10000, null, ids,isYL);
                    }
                } catch (Exception e) {
                    log.error("AreaManageInvalidAction handle error", e);
                }
//            });
        }
        return after;
    }
}
