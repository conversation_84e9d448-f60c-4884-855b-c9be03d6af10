package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-09-23 17:06
 **/
public class DealerSupplyListBaseHeaderController extends StandardListHeaderController {
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        //arg targetObjectApiName 为 DealerSupplyObj 不隐藏按钮
        LayoutDocument layoutDocument = after.getLayout();
        if(!controllerContext.getRequestContext().getClientInfo().startsWith("WEB") || !RequestContext.RequestSource.CEP.equals(controllerContext.getRequestSource()) || arg.getTargetObjectApiName() == null || !"DealerSupplyObj".equals(arg.getTargetObjectApiName())){
             layoutDocument = supplyService.hideButtons(layoutDocument, after.getButtons(), Boolean.TRUE);
        }else{
             layoutDocument = supplyService.hideButtons(layoutDocument, after.getButtons(), Boolean.FALSE);
        }
        after.setLayout(layoutDocument);
        return after;
    }
}
