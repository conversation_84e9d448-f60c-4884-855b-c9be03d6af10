package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.api.area.HandleStore;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
@Slf4j
public class CoveredStoresInvalidStoreController extends PreDefineController<HandleStore.Arg, HandleStore.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected HandleStore.Result doService(HandleStore.Arg arg) {
        HandleStore.Result res = new HandleStore.Result();
        log.info("CoveredStoresInvalidStoreController arg:{}", JSON.toJSONString(arg));
        String ea = serviceFacade.getEAByEI(controllerContext.getTenantId());
        Boolean isYL = GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea);

        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), CoveredStoresConstants.COVERED_STORES_OBJ);

        SearchTemplateQuery query =  SearchQuery.builder().eq(CoveredStoresConstants.BELONG_AREA, arg.getAreaId()).in(CoveredStoresConstants.STORE,arg.getStoreIds()).limit(1000).build().getSearchTemplateQuery();
        List<IObjectData> data = ObjectUtils.queryDataSimple(serviceFacade,controllerContext.getUser(), CoveredStoresConstants.COVERED_STORES_OBJ,query,describe);
        if(CollectionUtils.isNotEmpty(data) && data.size()<=arg.getStoreIds().size()){
            List<IObjectData>  objectData = serviceFacade.bulkDeleteDirect(data,controllerContext.getUser());
            if(CollectionUtils.isNotEmpty(objectData)) {
                List<String> storeIds = objectData.stream().filter(o->Objects.nonNull(o.get("store"))).map(o->o.get("store",String.class)).collect(Collectors.toList());
                supplyDao.updateAccountAreaInfo(controllerContext.getTenantId(), controllerContext.getUser().getUserIdInt(), null, storeIds, isYL);
            }
        }
        return res ;
    }
}
