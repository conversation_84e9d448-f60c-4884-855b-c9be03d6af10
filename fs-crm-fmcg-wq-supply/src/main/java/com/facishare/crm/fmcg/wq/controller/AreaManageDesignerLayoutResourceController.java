package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardDesignerLayoutResourceController;


public class AreaManageDesignerLayoutResourceController extends StandardDesignerLayoutResourceController {

    @Override
    protected Result doService(Arg arg) {
        Result result = super.doService(arg);
        if("detail".equals(arg.getLayoutType()) && CollectionUtils.isNotEmpty(result.getButtons())) {
            ObjectUtils.buttonList.forEach(o -> {
                result.getButtons().removeIf(button -> o.getButtonApiName().equals(button.get("api_name").toString()));
            });
        }
        return result;
    }

}
