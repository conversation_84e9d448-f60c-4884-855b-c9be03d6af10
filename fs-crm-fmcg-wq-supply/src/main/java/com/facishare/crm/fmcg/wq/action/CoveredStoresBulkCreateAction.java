package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkCreateAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
@Slf4j
public class CoveredStoresBulkCreateAction extends StandardBulkCreateAction {

    @Override
    protected void recordLog() {
        String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
        if(!GrayRelease.isAllow("checkin-server-v2", "isYLArea",ea)) {
            this.serviceFacade.log(this.actionContext.getUser(), EventType.ADD, ActionType.Add, this.objectDescribe, this.objectDataList);
        }else{
            masterModifyLog();
        }
    }

    protected String masterLogId;

    private Map<String, Map<String, Map<String, Object>>> getUpdatedFieldMapForLog(IObjectData objectData) {
        Map<String, Map<String, Map<String, Object>>> allUpdatedFieldMap = Maps.newHashMap();
        // 获取主对象的更新字段
        Map<String, Map<String, Object>> masterUpdateMap = Maps.newHashMap();
        masterUpdateMap.put(objectData.getId(), Maps.newHashMap());
        allUpdatedFieldMap.put(objectData.getDescribeApiName(), masterUpdateMap);
        return allUpdatedFieldMap;
    }
    private void masterModifyLog() {
        List<IObjectData> resultListToLog = this.objectDataList;
        Map<String,List<IObjectData>> handleData = resultListToLog.stream().collect(Collectors.groupingBy(o->o.get(CoveredStoresConstants.BELONG_AREA).toString()));
        log.info("masterModifyLog size:{}", this.objectDataList.size());
        if(MapUtils.isNotEmpty(handleData)) {
            for (Map.Entry<String, List<IObjectData>> entry : handleData.entrySet()) {
                Map<String, IObjectDescribe> objectDescribes = Maps.newHashMap();
                IObjectDescribe mainDesc = serviceFacade.findObject(actionContext.getTenantId(), AreaManageConstants.AREA_MANAGE_OBJ);
                IObjectDescribe detailDesc = serviceFacade.findObject(actionContext.getTenantId(), CoveredStoresConstants.COVERED_STORES_OBJ);
                IObjectData areaInfo = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),entry.getKey(), AreaManageConstants.AREA_MANAGE_OBJ);

                objectDescribes.put(AreaManageConstants.AREA_MANAGE_OBJ, mainDesc);
                objectDescribes.put(CoveredStoresConstants.COVERED_STORES_OBJ, detailDesc);
                List detailChange = Lists.newArrayList(CoveredStoresConstants.COVERED_STORES_OBJ);
                LogService.MasterLogInfo masterLog = this.serviceFacade.fillMasterModifyLog(this.actionContext.getUser(), objectDescribes, areaInfo, this.getUpdatedFieldMapForLog(areaInfo), areaInfo, detailChange);
                this.masterLogId = masterLog.getMasterLogId();
                this.serviceFacade.sendLog(masterLog.getLogList());

                Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
                describeMap.put(CoveredStoresConstants.COVERED_STORES_OBJ, detailDesc);
                this.serviceFacade.log(this.actionContext.getUser(), EventType.ADD, ActionType.Add, describeMap, entry.getValue(), this.masterLogId);
            }
        }

    }
}
