package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SpecialSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.Supplier;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

public class SpecialSupplyAddAction extends StandardAddAction {
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //判断是否已经存在 相同的数据
        //判断是否已经存在相同的  供货门店
        String lowerAccountId = arg.getObjectData().get(SpecialSupplyObjConstants.Field.shopId.getApiName()).toString();
        String dealerId = arg.getObjectData().get(SpecialSupplyObjConstants.Field.dealerId.getApiName()).toString();
        String dealerSupplyId = arg.getObjectData().get(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName()).toString();
        String productId = arg.getObjectData().get(SpecialSupplyObjConstants.Field.productId.getApiName()).toString();
        String tenantId = actionContext.getTenantId();
        AccountObj lowerAccountObj = supplyDao.getAccountObjById(tenantId, lowerAccountId);
        if (dealerId.equals(lowerAccountId)){
            throw new ValidateException("参数错误"); //ignoreI18n
        }

        //判断重复
        supplyService.checkSpecialSupplyRepetition(tenantId,arg.getObjectData().toObjectData());
        //0. 查询 新增的是否在 范围内
        ArrayList<String> addSupplierIds = Lists.newArrayList(dealerId);
        supplyDao.checkSupplierExistDepartment(tenantId, addSupplierIds, Lists.newArrayList(lowerAccountObj.getDepartmentId()));
        // 产品是否再经营范围内
        Supplier supplierById = supplyService.getSupplierById(tenantId, dealerId, 1);
        if (!supplierById.containtProduct(productId)){
            throw new ValidateException(String.format("所选产品不包含在，配货商 %s 供货列表内", //ignoreI18n
                    supplyDao.getAccountObjById(tenantId,
                            arg.getObjectData().get(SpecialSupplyObjConstants.Field.dealerId.getApiName()).toString()).getName()));
        }
        //初始化审批流上下文
        supplyOperaContext.setArgs(arg);
        supplyOperaContext.setResultClazz(Result.class);
        supplyOperaContext.setInUpCustomerId(dealerId);
        supplyOperaContext.setInUpSupplyId(arg.getObjectData().get(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName()).toString());
        supplyOperaContext.setCustermerIds(Lists.newArrayList(lowerAccountId));
        supplyOperaContext.setProductIds(Lists.newArrayList(productId));
        //触发审批流程
        Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(actionContext, supplyOperaContext);
        if (booleanIObjectDataPair.first){
            throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE,SupplyOperaContext.TRIGGERAPPROCODE);
        }

    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        if (after.getObjectData() != null){
            String lowerAccountId = arg.getObjectData().get(SpecialSupplyObjConstants.Field.shopId.getApiName()).toString();
            String dealerId = arg.getObjectData().get(SpecialSupplyObjConstants.Field.dealerId.getApiName()).toString();
            String dealerSupplyId = arg.getObjectData().get(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName()).toString();
            String productId = arg.getObjectData().get(SpecialSupplyObjConstants.Field.productId.getApiName()).toString();
            Object dep = arg.getObjectData().get(BaseField.dataOwnDepartment.getApiName());
            String tenantId = actionContext.getTenantId();
            AccountObj lowerAccountObj = supplyDao.getAccountObjById(tenantId, lowerAccountId);
            ArrayList<String> addSupplierIds = Lists.newArrayList(dealerId);
            if (lowerAccountObj.getRecordType().equals(AccountObjConstants.Value.default__c.name())){
                //门店
                List<IObjectData> dealerSupplyBySuppliers = supplyDao.getDealerSupplyBySuppliers(tenantId,
                        addSupplierIds);


                // 3.3 创建 供货门店对象
                supplyDao.createSupplyStoreObj4Special(tenantId,lowerAccountObj,dealerSupplyBySuppliers);

            }else if (lowerAccountObj.getRecordType().equals(AccountObjConstants.Value.distributor__c.name())) {
                //分销邮差

                supplyDao.createDistributorSupplyObj4Special(tenantId,lowerAccountObj.getOwner(),lowerAccountId, addSupplierIds,dep);

            }
            supplyService.addAvailableObjSyncTask(actionContext.getTenantId(), result.getObjectData()
                    .get(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName()).toString());
//        else{
//            throw new ValidateException(
//                    String.format("%s 业务类型错误",lowerAccountObj.getName())
////                            String.format("%s 配送商与当前配送商经营品项重复",
////                            supplyDao.getAccountObjById(controllerContext.getTenantId(),supplier.getId()).getName())
//            );
//        }

        }

        return after;
    }
}
