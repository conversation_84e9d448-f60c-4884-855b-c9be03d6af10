package com.facishare.crm.fmcg.wq.api.supply;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * @program: fs-crm-fmcg
 * @description: 删除银鹭数据
 * @author: zhangsm
 * @create: 2022-03-30 16:14
 **/
public interface DealerSupplyDelBySupplyId {
    @Data
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg implements Serializable {
        // 供货关系id
        private String supplyId;
    }
    @ToString
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable{
        String success = "成功"; //ignoreI18n
        Map<String, Set<String>> delData;

    }
}
