package com.facishare.crm.fmcg.wq.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.utils.EnvUtils;
import com.facishare.crm.fmcg.wq.service.MNService;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.enterpriserelation2.arg.ListUpstreamByOuterTenantIdOutArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.data.OuterTenantSimpleData;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/05/24/ 11:34
 **/
@Slf4j
@Component
public class MNServiceImpl implements MNService {


    @Autowired
    private EnterpriseRelationService enterpriseRelationService;
    String checkConnectedAppId;

    {
        ConfigFactory.getConfig("checkin-custom-config", config -> {
            checkConnectedAppId = config.get("checkConnectedAppId", "FSAID_11491079");
        });
    }
    public int getUpEiForMn(int eId){
        int upEi = eId;
        if (!GrayRelease.isAllow("webPage", "isMengNiuAdminEi",eId)) {
            List<OuterTenantSimpleData> upstreamEaList = batchListUpstreamByTenantIds(Math.toIntExact(eId));
            if(CollectionUtils.isNotEmpty(upstreamEaList)){
                for(OuterTenantSimpleData simpleData:upstreamEaList){
                    //1端企业
                    if (GrayRelease.isAllow("webPage", "isMengNiuAdminEi",simpleData.getTenantId())) {
                        upEi = simpleData.getTenantId();
                    }
                }
            }
        }
        return upEi;
    }


    private List<OuterTenantSimpleData> batchListUpstreamByTenantIds(int eId){
        ListUpstreamByOuterTenantIdOutArg arg = new ListUpstreamByOuterTenantIdOutArg();
        arg.setTenantIds(Lists.newArrayList(eId));
        HeaderObj headerObj = HeaderObj.newInstance(eId);
        headerObj.setAppId(checkConnectedAppId);
        RestResult<Map<Integer, List<OuterTenantSimpleData>>> result = enterpriseRelationService.batchListUpstreamByTenantIds(headerObj, arg);
        if(result.isSuccess()){
            return result.getData().get(eId);
        }else{
            return null;
        }
    }
}
