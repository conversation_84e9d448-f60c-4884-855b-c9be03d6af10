package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.BatchImportByCheckins;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.yldb.YLTempSupplyEntity;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description:  指定门店的配送商逻辑
 * @author: zhangsm
 * @create: 2021-05-08 15:25
 **/
public class DealerSupplyBatchImportByCheckinsController extends PreDefineController<BatchImportByCheckins.Arg, BatchImportByCheckins.Result> {
    SupplyDao supplyDao =  SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected void before(BatchImportByCheckins.Arg arg) {

    }

    @Override
    protected BatchImportByCheckins.Result doService(BatchImportByCheckins.Arg arg) {
        BatchImportByCheckins.Result result = new BatchImportByCheckins.Result();
        result.setYlTempSupplyEntityList(arg.getYlTempSupplyEntityList());
        if (CollectionUtils.isEmpty(arg.getYlTempSupplyEntityList())){
            return result;
        }
        //创建 供货分销商 和供货门店的数据
        String tenantId = controllerContext.getTenantId();
        List<YLTempSupplyEntity> noSupplyIdList = arg.getYlTempSupplyEntityList().stream().filter(o -> StringUtils.isBlank(o.getUpSupplySyncObjectId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noSupplyIdList)) {
            List<IObjectData> dealerSupplyBySuppliers = supplyDao.getDealerSupplyBySuppliers(tenantId,
                    noSupplyIdList.stream().map(o -> o.getFsUpAccountId()).collect(Collectors.toList()));
            Map<String, String> idMap = dealerSupplyBySuppliers
                    .stream().collect(Collectors.toMap(k->k.get(DealerSupplyObjConstants.Field.dealerId.getApiName(),String.class),v->v.getId()));
            for (YLTempSupplyEntity ylTempSupplyEntity : noSupplyIdList) {
                ylTempSupplyEntity.setUpSupplySyncObjectId(idMap.get(ylTempSupplyEntity.getFsUpAccountId()));
            }
        }
        if (arg.isOnlyUpdateSupplyId()){
            return result;
        }
        Map<Integer, List<YLTempSupplyEntity>> levelMap = arg.getYlTempSupplyEntityList().stream()
                .collect(Collectors.groupingBy(o -> o.getLevel()));
        levelMap.entrySet().parallelStream().forEach(
                integerListEntry -> {
                    if (integerListEntry.getKey() == 3) {
                        //供货门店
                        supplyDao.createSupplyStoryObjs(tenantId, "-10000", integerListEntry.getValue());
                        supplyDao.createSpecialSupplyObjs(tenantId,"-10000",integerListEntry.getValue());
                    } else if (integerListEntry.getKey() == 2) {
                        //供货分销
                        supplyDao.createDistributorSupplyObjs(tenantId, "-10000", integerListEntry.getValue());
                        supplyDao.createSpecialSupplyObjs(tenantId,"-10000",integerListEntry.getValue());
                    } else {
                        //其他
                    }
                }

        );
        return result;
    }

    @Override
    protected BatchImportByCheckins.Result after(BatchImportByCheckins.Arg arg, BatchImportByCheckins.Result result) {
        return result;
    }
}
