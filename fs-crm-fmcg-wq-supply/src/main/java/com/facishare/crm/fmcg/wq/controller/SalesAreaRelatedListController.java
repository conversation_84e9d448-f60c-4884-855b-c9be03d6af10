package com.facishare.crm.fmcg.wq.controller;

import com.facishare.appserver.checkins.model.enums.CustomGray;
import com.facishare.appserver.checkins.model.enums.ObjApiName;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.SalesAreaObjConstants;
import com.facishare.crm.fmcg.wq.service.SalesAreaService;
import com.facishare.crm.fmcg.wq.service.impl.SalesAreaServiceImpl;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/05/23/ 17:27
 **/
public class SalesAreaRelatedListController extends StandardRelatedListController {


    private SalesAreaService salesAreaService = SpringUtil.getContext().getBean(SalesAreaServiceImpl.class);

    @Override
    protected void before(Arg arg) {
        String tenantId = controllerContext.getTenantId();
        String ea = serviceFacade.getEAByEI(tenantId);
        if(isFromAccountOnMN(ea)) {
            List<String> parentSellerIds = getParentSellerIds(ea);
            String nParentSeller = salesAreaService.getOurEnterprise(tenantId);
            if(StringUtils.isBlank(arg.getObjectData().getId()) || (CollectionUtils.isNotEmpty(parentSellerIds) && !parentSellerIds.contains(arg.getObjectData().getId()))
                ||(StringUtils.isNotBlank(nParentSeller) && !arg.getObjectData().getId().equals(nParentSeller))){
                List<String> sellerIds = (List<String>)arg.getObjectData().get(AccountObjConstants.Field.sellers.getApiName());
                // 1.校验销售商是否填写
                if (CollectionUtils.isEmpty(sellerIds)) {
                    throw new ValidateException("请先设置销售商"); //ignoreI18n
                }
                boolean isParentSeller = CollectionUtils.isNotEmpty(parentSellerIds) && CollectionUtils.isNotEmpty(CollectionUtils.intersection(parentSellerIds,sellerIds));
                if(CollectionUtils.isEmpty(parentSellerIds) || !isParentSeller) {
                    // 只能选择销售商的所属销售区域的子集
                    Set<String> salesAreaIds = getSalesAreaIdsBySellers(sellerIds);
                    List<String> parentPathIds = getPathIdsForMn(salesAreaIds);
                    if(CollectionUtils.isNotEmpty(parentPathIds)){
                        ISearchTemplateQuery searchTemplateQuery = SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
                        List<IFilter> iFilterList = Optional.ofNullable(searchTemplateQuery.getFilters()).orElse(Lists.newArrayList());
                        searchTemplateQuery.setFilters(null);
                        List<Wheres> wheresList = Lists.newArrayList();

                        searchTemplateQuery.setWheres(wheresList);
                        List<String> parentPaths = getParentPath(parentPathIds);
                        for (String parentPath : parentPaths) {
                            Wheres wheres = new Wheres();
                            wheres.setConnector("OR");
                            List<IFilter> iFilters = Lists.newArrayList();
                            IFilter iFilter = new Filter();
                            iFilter.setFieldName(SalesAreaObjConstants.treeView);
                            iFilter.setFieldValues(Lists.newArrayList(parentPath + ".*"));
                            iFilter.setOperator(Operator.MATCH);
                            iFilters.add(iFilter);
                            iFilters.addAll(iFilterList);
                            wheres.setFilters(iFilters);
                            wheresList.add(wheres);
                        }
                        Wheres wheres = new Wheres();
                        wheres.setConnector("OR");
                        List<IFilter> iFilters = Lists.newArrayList();
                        IFilter iFilter = new Filter();
                        iFilter.setFieldName(CommonConstants.ID);
                        iFilter.setFieldValues(Lists.newArrayList(salesAreaIds));
                        iFilter.setOperator(Operator.IN);
                        iFilters.add(iFilter);
                        iFilters.addAll(iFilterList);
                        wheres.setFilters(iFilters);
                        wheresList.add(wheres);
                        arg.setSearchQueryInfo(searchTemplateQuery.toJsonString());
                    }
                }
            }
        }
        super.before(arg);
    }

    private boolean isFromAccountOnMN(String ea){
        return Objects.nonNull(arg.getObjectData()) && Objects.nonNull(arg.getObjectData().get("object_describe_api_name")) &&
                ObjApiName.AccountObj.toString().equals(arg.getObjectData().get("object_describe_api_name").toString()) && CustomGray.EA.salesAreaForMn.grayAndEnv(ea);
    }

    private List<String> getParentSellerIds(String ea){
        String parentSellerIds = ConfigFactory.getConfig("checkin-custom-config").get("parentSellerIdsForSaleArea_" + ea);
        if(StringUtils.isNotBlank(parentSellerIds)){
            return Lists.newArrayList(Arrays.asList(parentSellerIds.split(",")));
        }else{
            return null;
        }
    }

    private List<String> getParentPath(List<String> parentPathIds){
        List<String> realParentPathList = Lists.newArrayList();
        // 按照长度排序 短的在前 如果有父子关系，父一定在前
        for (String s : parentPathIds) {
            if (realParentPathList.isEmpty()) {
                realParentPathList.add(s);
            } else {
                boolean isAdd = true;
                for (String string : realParentPathList) {
                    if (s.contains(string)) {
                        isAdd = false;
                        break;
                    }
                }
                if (isAdd) {
                    realParentPathList.add(s);
                }
            }
        }
        return realParentPathList;
    }

    private Set<String> getSalesAreaIdsBySellers(List<String> sellerIds){
        // 校验销售商是否维护销售区域
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(),sellerIds,AccountObjConstants.API_NAME);
        if(CollectionUtils.isEmpty(objectDataList)){
            throw new ValidateException("销售商不存在，请重新设置！"); //ignoreI18n
        }

        Set<String> salesAreaIds = Sets.newHashSet();
        for (IObjectData iObjectData : objectDataList) {
            List<String> subSalesAreaIds = (List<String>) iObjectData.get(AccountObjConstants.Field.coveringSalesAreas.getApiName());
            if(CollectionUtils.isNotEmpty(subSalesAreaIds)){
                salesAreaIds.addAll(subSalesAreaIds);
            }
        }
        if(salesAreaIds.isEmpty()){
            throw new ValidateException("当前客户所属销售商未设置销售区域，请设置销售商的销售区域后再选择！"); //ignoreI18n
        }
        return salesAreaIds;
    }

    private List<String> getPathIdsForMn(Set<String> salesAreaIds){
        try {
            List<IObjectData> iObjectDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(salesAreaIds),SalesAreaObjConstants.OBJ_API_NAME);
            return iObjectDataList.stream().map(o->Objects.nonNull(o.get(SalesAreaObjConstants.treeView)) ? o.get(SalesAreaObjConstants.treeView) + "." + o.getId() : o.getId())
                    .sorted(Comparator.comparingInt(String::length)).collect(Collectors.toList());
        }catch (ValidateException e){
            throw e;
        }
        catch (Exception e){
            log.error("salesAreaTreeList beforeForMn is error",e);
            throw new ValidateException("系统异常，联系纷享客服"); //ignoreI18n
        }
    }

}
