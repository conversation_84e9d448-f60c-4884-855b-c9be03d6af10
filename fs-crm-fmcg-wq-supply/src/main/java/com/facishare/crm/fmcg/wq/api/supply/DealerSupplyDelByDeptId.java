package com.facishare.crm.fmcg.wq.api.supply;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @program: fs-crm-fmcg
 * @description: 删除银鹭数据
 * @author: zhangsm
 * @create: 2022-03-30 16:14
 **/
public interface DealerSupplyDelByDeptId {
    @Data
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg implements Serializable {
        // 1 是更新价目表逻辑
        private String departId;
    }
    @ToString
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable{
        String success = "成功"; //ignoreI18n

    }
}
