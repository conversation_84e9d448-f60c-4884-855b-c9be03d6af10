package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.service.ChannelService;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

public class ChannelInvalidAction extends StandardInvalidAction {

    ChannelService channelService = SpringUtil.getContext().getBean(ChannelService.class);

    RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String tenantId = actionContext.getTenantId();
        channelService.checkIsValid(tenantId, Lists.newArrayList(arg.getObjectDataId()));
    }

    @Override
    protected Result after(Arg arg, Result result) {
        redisUtils.clearChannelData(actionContext.getTenantId());
        return super.after(arg, result);
    }
}
