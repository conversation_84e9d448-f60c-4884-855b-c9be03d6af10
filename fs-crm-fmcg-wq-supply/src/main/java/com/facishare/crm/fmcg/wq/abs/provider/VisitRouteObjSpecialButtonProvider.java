package com.facishare.crm.fmcg.wq.abs.provider;

import com.facishare.crm.fmcg.wq.MCPreDefineObject;
import com.facishare.crm.fmcg.wq.abs.AbstractjmlmcSpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date : 2022/2/16  15:54
 */
@Component
public class VisitRouteObjSpecialButtonProvider extends AbstractjmlmcSpecialButtonProvider {
    @Override
    public String getApiName() {
        return MCPreDefineObject.VisitRoute.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        return super.getSpecialButtons();
    }
}
