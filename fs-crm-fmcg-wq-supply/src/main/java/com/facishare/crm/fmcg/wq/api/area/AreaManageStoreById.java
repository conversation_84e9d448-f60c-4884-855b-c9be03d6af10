package com.facishare.crm.fmcg.wq.api.area;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.appserver.checkins.api.model.BaseResult;
import com.facishare.crm.fmcg.wq.model.PlanRouteInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface AreaManageStoreById {
    @Data
    @ToString
    class Arg implements Serializable {

        @NotEmpty(message = "数据id为空，请填写数据")
        @JSONField(name = "area_ids")
        @JsonProperty("area_ids")
        @SerializedName("area_ids")
        private List<String> areaIds;


        @JSONField(name = "query_info")
        @JsonProperty("query_info")
        @SerializedName("query_info")
        private String queryInfo;

    }

    //最多2000条

    @Data
    @ToString
    class Result extends BaseResult {

        private Map<String,AreaStoreInfo> infos = Maps.newHashMap();

        Map<String,String> accountValueAndLevelMap = Maps.newHashMap();

        public boolean isShowWaterOnList;

        public String showFieldNameOnWater;

        private int total;
    }
    @Data
    class AreaStoreInfo{
        private List<PlanRouteInfo> stores;

        private String name;
    }
}
