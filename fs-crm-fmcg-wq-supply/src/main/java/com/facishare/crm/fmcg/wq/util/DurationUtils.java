package com.facishare.crm.fmcg.wq.util;

import com.facishare.appserver.utils.DateUtils;

public class DurationUtils {
    public static String getDurationSecond(Long duration) {
        String time = "";
        if (duration >= DateUtils.ONE_DAY) {
            time = duration / DateUtils.ONE_DAY + "天" + duration % DateUtils.ONE_DAY / DateUtils.ONE_HOUR + "小时" + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR / DateUtils.ONE_MINUTE + "分" + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND + "秒"; //ignoreI18n
        } else if (duration >= DateUtils.ONE_HOUR) {
            time = duration / DateUtils.ONE_HOUR + "小时" + duration % DateUtils.ONE_HOUR / DateUtils.ONE_MINUTE + "分" + duration % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND + "秒"; //ignoreI18n
        } else if (duration >= DateUtils.ONE_MINUTE) {
            time = duration / DateUtils.ONE_MINUTE + "分" + duration % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND + "秒"; //ignoreI18n
        } else if(duration >= DateUtils.ONE_SECOND){
            time = duration / DateUtils.ONE_SECOND + "秒"; //ignoreI18n
        } else {
            time = "0秒"; //ignoreI18n
        }
        return time;
    }
}
