package com.facishare.crm.fmcg.wq.api.area;


import com.facishare.appserver.checkins.api.model.BaseResult;
import com.facishare.crm.fmcg.wq.model.yldb.YLTempSupplyEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface BatchImportByCheckins {
    @Data
    @ToString
    class Arg implements Serializable {
        List<YLTempSupplyEntity> ylTempSupplyEntityList;
        boolean isOnlyUpdateSupplyId;

    }

    @Data
    @ToString
    class Result extends BaseResult {
        List<YLTempSupplyEntity> ylTempSupplyEntityList;
    }
}
