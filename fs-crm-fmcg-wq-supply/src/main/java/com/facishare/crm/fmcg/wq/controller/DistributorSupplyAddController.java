package com.facishare.crm.fmcg.wq.controller;


import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.api.area.AddDistributorSupply;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.dao.BaseDaoInterface;
import com.facishare.crm.fmcg.wq.model.Shop;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.service.SyncAvailableRangeService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 指定门店的配送商逻辑
 * @author: zhangsm
 * @create: 2021-05-08 15:25
 **/
public class DistributorSupplyAddController extends PreDefineController<AddDistributorSupply.Arg, AddDistributorSupply.Result> {
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);
    SyncAvailableRangeService syncAvailableRangeService= SpringUtil.getContext().getBean(SyncAvailableRangeService.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }


    @Override
    protected AddDistributorSupply.Result doService(AddDistributorSupply.Arg arg) {
        String tenantId = controllerContext.getTenantId();
        AccountObj thisAccountObj = supplyDao.getAccountObjById(tenantId, arg.getAccountId());
        AddDistributorSupply.Result result = new AddDistributorSupply.Result();
        supplyService.checkAddDistributorSupply(tenantId, thisAccountObj, arg.getAddSupplierIds());
        if (CollectionUtils.isNotEmpty(arg.getAddSupplierIds())) {
            supplyOperaContext.setArgs(arg);
            supplyOperaContext.setResultClazz(AddDistributorSupply.Result.class);
            supplyOperaContext.setInUpCustomerId(arg.getAddSupplierIds().get(0));
            //查供货关系
            List<IObjectData> dealerSupplyBySuppliers = supplyDao.getDealerSupplyBySuppliers(tenantId, arg.getAddSupplierIds());
            supplyOperaContext.setInUpSupplyId(dealerSupplyBySuppliers.get(0).getId());
            supplyOperaContext.setCustermerIds(Lists.newArrayList(arg.getAccountId()));
            //触发审批流程
            Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(controllerContext, supplyOperaContext);
            if (booleanIObjectDataPair.first){
                throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE,SupplyOperaContext.TRIGGERAPPROCODE);
            }else{
                // 3. 验证都没毛病了 创建数据
                supplyDao.createDistributorSupplyObj(tenantId, thisAccountObj.getOwner(), arg.getAccountId(), arg.getAddSupplierIds(), Lists.newArrayList(thisAccountObj.getDepartmentId()));
            }
        }
        return result;
    }

    @Override
    protected AddDistributorSupply.Result after(AddDistributorSupply.Arg arg, AddDistributorSupply.Result result) {
        AddDistributorSupply.Result after = super.after(arg, result);
        fmcgThreadPoolExecutor.execute(
                () -> {
                    supplyDao.getProductCollectionObjByAccountId(controllerContext.getTenantId(), arg.getAccountId(), false);
                    arg.getAddSupplierIds().add(arg.getAccountId());
                    supplyDao.getDealerSupplyBySuppliers(controllerContext.getTenantId(), arg.getAddSupplierIds());
                    Shop shopObjById = supplyService.getShopObjById(controllerContext.getTenantId(), arg.getAccountId(), 99);
                    syncAvailableRangeService.updateAllAvailableDataByShop(controllerContext.getTenantId(), shopObjById);
                }

        );
        return after;
    }
}
