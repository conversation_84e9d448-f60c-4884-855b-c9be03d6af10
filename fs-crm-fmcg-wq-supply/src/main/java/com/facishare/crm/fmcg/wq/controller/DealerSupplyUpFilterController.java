package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.DealerSupplyUpFilter;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.DistributorSupply;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.util.FieldUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description: 客户上级数据筛选
 * @author: zhangsm
 * @create: 2021-06-10 11:38
 **/
public class DealerSupplyUpFilterController extends PreDefineController<DealerSupplyUpFilter.Arg, DealerSupplyUpFilter.Result> {

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected DealerSupplyUpFilter.Result doService(DealerSupplyUpFilter.Arg arg) {
        result = new DealerSupplyUpFilter.Result();
        IObjectData iObjectData = arg.getIObjectData().toObjectData();
        String recordType = iObjectData.getRecordType();
        String tenantId = controllerContext.getTenantId();
        List<String> dataOwnDepartment = iObjectData.getDataOwnDepartment();
        if (StringUtils.isBlank(recordType) || !CollectionUtils.isNotEmpty(dataOwnDepartment)) {
            throw new ValidateException("业务类型或者归属部门未填写"); //ignoreI18n
        }

        //选择经销商
        if (recordType.equals(AccountObjConstants.Value.default__c.name()) || recordType.equals(AccountObjConstants.Value.distributor__c.name())) {
            //门店选择经销商
            Set<String> departmentIds = new HashSet<>();
            departmentIds.addAll(dataOwnDepartment);
            List<String> otherDepartment = FieldUtils.getStringList(iObjectData, AccountObjConstants.Field.otherDepartment.getApiName());
            if (CollectionUtils.isNotEmpty(otherDepartment)) {
                departmentIds.addAll(otherDepartment);
            }
            //服务处部门
            List<String> serviceCenterDepartmentIds = supplyDao.getServiceCenterDepartmentIds(tenantId, Lists.newArrayList(departmentIds));
            //通过服务处部门查经销商
            List<ObjectDataDocument> infos = supplyDao.getSupplyDealerListByServiceCenterDepartmentIds(tenantId, null, serviceCenterDepartmentIds);
            result.setInfos(infos);
            result.setTotal(infos.size());
            result.setStyle(1);
            if (recordType.equals(AccountObjConstants.Value.default__c.name()) && CollectionUtils.isNotEmpty(infos)) {
                result.setStyle(0);
                //门店选择配送商
                //1 获取经销商ids 如果未填写 则报错
                List<String> supplierIds = infos.stream().map(o->o.getId()).collect(Collectors.toList());
                //通过上游查询下游分销商 和当前经销商
                List<DistributorSupply> relationListByUpIds = supplyService.getRelationListByUpIds(tenantId, supplierIds);

                List<String> distributorIdList = relationListByUpIds.stream()
                        .map(o -> o.getThisDistributorAccountId()).collect(Collectors.toList());
                //增加经销商的id
                Map<String, AccountObj> map = supplyDao.getAccountObjByIdsIgnoreException(tenantId, distributorIdList)
                        .stream().collect(Collectors.toMap(o -> o.getId(), v -> v));
                // distributorMap
                Map<String, List<DistributorSupply>> distributorMap = relationListByUpIds.stream().collect(Collectors.groupingBy(o->o.getUpDealerAccountId()));
                for (ObjectDataDocument info : infos) {
                    List<ObjectDataDocument> distributors = (List) info.get("distributors");
                    List<DistributorSupply> tempDisList = distributorMap.get(info.getId());
                    if (CollectionUtils.isNotEmpty(tempDisList)) {
                        for (DistributorSupply tempDis : tempDisList) {
                            AccountObj accountObj = map.get(tempDis.getThisDistributorAccountId());
                            if (Objects.nonNull(accountObj)){
                                ObjectDataDocument tempDoc = new ObjectDataDocument();
                                tempDoc.put(BaseField.id.getApiName(), tempDis.getThisDistributorAccountId());
                                tempDoc.put(BaseField.name.getApiName(), accountObj.getName());
                                distributors.add(tempDoc);
                            }
                        }
                    }
                }
            }
        } else {
            throw new ValidateException("该业务类型不支持"); //ignoreI18n
        }
        return result;
    }
}
