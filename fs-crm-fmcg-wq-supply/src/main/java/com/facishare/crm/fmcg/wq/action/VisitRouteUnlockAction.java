package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.paas.appframework.core.predef.action.StandardUnlockAction;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * @program: fs-crm-fmcg
 * @description: 路线锁定接口
 * @author: zhangsm
 * @create: 2022-01-19 14:58
 **/
public class VisitRouteUnlockAction extends StandardUnlockAction {

    CheckinsDao checkinsDao = SpringUtil.getContext().getBean(CheckinsDao.class);

    @Override
    protected Result doAct(Arg arg) {
        Result result = super.doAct(arg);
        for (String dataId : arg.getDataIds()) {
            checkinsDao.unLockRoute(actionContext.getTenantId(), dataId);
        }
        return result;
    }
}
