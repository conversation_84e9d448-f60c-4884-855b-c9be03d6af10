package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @date : 2022/2/17  19:32
 */
public class StorePhotoWallInspectWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result newResult = super.after(arg, result);
        LayoutExt layoutExt = LayoutExt.of(newResult.getLayout());
        layoutExt.getHeadInfoComponent().ifPresent(x -> {
            x.setButtons(Lists.newArrayList());
            layoutExt.setButtons(Lists.newArrayList());

        });
        //处理终端逻辑
        layoutExt.setButtons(Lists.newArrayList());
        newResult.setLayout(LayoutDocument.of(layoutExt));
        return newResult;
    }
}
