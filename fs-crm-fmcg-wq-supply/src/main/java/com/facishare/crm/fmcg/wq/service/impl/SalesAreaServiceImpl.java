package com.facishare.crm.fmcg.wq.service.impl;

import com.facishare.appserver.checkins.api.model.BatchFilterPointInSalesAreaArg;
import com.facishare.appserver.checkins.model.enums.ObjApiName;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.OtherObjConstants;
import com.facishare.crm.fmcg.wq.constants.SalesAreaObjConstants;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.crm.fmcg.wq.service.MNService;
import com.facishare.crm.fmcg.wq.service.SalesAreaService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/05/24/ 11:36
 **/
@Slf4j
@Component
public class SalesAreaServiceImpl implements SalesAreaService {

    @Autowired
    private BaseDao baseDao;
    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    private MNService mnServiceImpl;

    @Autowired
    private CheckinsProxy checkinsProxy;
    public void checkSalesAreaOnAddOrEditAcc(String eid, ObjectDataDocument objectData,boolean isOneTerminal){
        log.info("checkSalesAreaOnAddOrEditAcc is start eid:{}",eid);
        List<String> coveringSalesAreas = (List<String>) objectData.get(AccountObjConstants.Field.coveringSalesAreas.getApiName());
        List<String> sellerIds = (List<String>) objectData.get(AccountObjConstants.Field.sellers.getApiName());
        if(CollectionUtils.isEmpty(coveringSalesAreas)){
            return;
        }
        // 判断销售区域是否重复
        String outEaAccId = getOurEnterprise(eid,isOneTerminal);
        Set<String> repetitiveSalesArea = getRepetitiveSalesArea(eid,sellerIds,coveringSalesAreas, objectData.getId(),outEaAccId);
        if(!repetitiveSalesArea.isEmpty()){
            StringBuffer stringBuffer = new StringBuffer("重复的客户有「"); //ignoreI18n
            repetitiveSalesArea.forEach(o->stringBuffer.append(o).append("、"));
            stringBuffer.deleteCharAt(stringBuffer.length() - 1);
            stringBuffer.append("」");
            throw new ValidateException(stringBuffer.toString(),-8000097);
        }
        log.info("checkSalesAreaOnAddOrEditAcc is end eid:{}",eid);
    }

    public List<String> getParentSellerIds(String ea){
        String parentSellerIds = ConfigFactory.getConfig("checkin-custom-config").get("parentSellerIdsForSaleArea_" + ea);
        if(StringUtils.isNotBlank(parentSellerIds)){
            return Lists.newArrayList(Arrays.asList(parentSellerIds.split(",")));
        }else{
            return null;
        }
    }

    public List<String> getNoChannelAccountRecordTypeList(String ea){
        String property = System.getProperty("process.profile");
        String recordTypeStr = ConfigFactory.getConfig("checkin-custom-config").get("noChannelAccountRecordType_ " + ea, CommonConstants.RECORD_DEFAULT);
        if(StringUtils.isBlank(recordTypeStr)){
            recordTypeStr = ConfigFactory.getConfig("checkin-custom-config").get("noChannelAccountRecordType_ " + property,"");
        }
        return StringUtils.isNotBlank(recordTypeStr) ? Arrays.asList(recordTypeStr.split(",")) : null;
    }

    @Override
    public void checkOnSalesArea(int eid,String location,List<String> coveringSalesAreas) {
        // 校验定位是否在所属销售区域内
        if(StringUtils.isBlank(location)){
            throw new ValidateException("请填写门店定位"); //ignoreI18n
        }
        String[] locationArr = location.split("#%[$]");
        BatchFilterPointInSalesAreaArg arg = new BatchFilterPointInSalesAreaArg();
        arg.setEid(eid);
        arg.setSalesAreaIds(coveringSalesAreas);
        arg.setGeos(Lists.newArrayList(locationArr[0] + "," + locationArr[1]));
        if(CollectionUtils.isEmpty(checkinsProxy.batchFilterPointInSalesArea(String.valueOf(eid),arg))){
            throw new ValidateException("该门店不在贵司销售区域范围，无法创建"); //ignoreI18n
        }
    }


    @Override
    public Integer calculateLevel(String tenantId, BaseObjectSaveAction.Arg arg, boolean isAdd) {
        String parentAreaId = "";
        if(Objects.isNull(arg.getObjectData().get(SalesAreaObjConstants.parentArea))){
            if(Objects.nonNull(arg.getObjectData().getId()) && !isAdd){
                String dataId = arg.getObjectData().getId();
                SearchQuery disSearchQuery = SearchQuery.builder()
                        .in("_id", Lists.newArrayList(dataId))
                        .build();
                List<IObjectData> resultList = baseDao.getQueryDataListByQuery(User.systemUser(tenantId), disSearchQuery, SalesAreaObjConstants.OBJ_API_NAME, false).getData();
                if(CollectionUtils.isNotEmpty(resultList)){
                    if(Objects.nonNull(resultList.get(0).get(SalesAreaObjConstants.parentArea))){
                        parentAreaId = resultList.get(0).get(SalesAreaObjConstants.parentArea).toString();
                    }
                }
            }
        }else{
            parentAreaId = arg.getObjectData().get(SalesAreaObjConstants.parentArea).toString();
        }
        if(StringUtils.isBlank(parentAreaId)){
            return 1;
        }
        SearchQuery disSearchQuery = SearchQuery.builder()
                .in("_id", Lists.newArrayList(parentAreaId))
                .build();
        List<IObjectData> resultList = baseDao.getQueryDataListByQuery(User.systemUser(tenantId), disSearchQuery, SalesAreaObjConstants.OBJ_API_NAME, false).getData();
        if(CollectionUtils.isEmpty(resultList)){
            return 1;
        }
        if(Objects.nonNull(resultList.get(0).get(SalesAreaObjConstants.areaLevel))){
            return Integer.parseInt(resultList.get(0).get(SalesAreaObjConstants.areaLevel).toString()) + 1;
        }
        return 1;
    }


    private Set<String> getRepetitiveSalesArea(String eid,List<String> sellerIds,List<String> coveringSalesAreas,String currentId,String outEaAccId){
        SearchQuery searchQuery = null;
        if(sellerIds.size() > 1){
            searchQuery = SearchQuery.builder()
                    .hasAnyOf(AccountObjConstants.Field.sellers.getApiName(), sellerIds).nNull(AccountObjConstants.Field.coveringSalesAreas.getApiName()).build();
        }else{
            searchQuery = SearchQuery.builder()
                    .eq(AccountObjConstants.Field.sellers.getApiName(), sellerIds).nNull(AccountObjConstants.Field.coveringSalesAreas.getApiName()).build();
        }
        // 查询 当前销售商 销售区域不为空的其他客户
        List<IObjectData> objectDataList = baseDao.getAllIObjectDataListByQueryWithFields(User.systemUser(eid),
                searchQuery,AccountObjConstants.API_NAME, Lists.newArrayList(AccountObjConstants.Field.coveringSalesAreas.getApiName()));
        String ea = serviceFacade.getEAByEI(eid);
        List<String> parentIds = getParentSellerIds(ea);
        List<String> noChannelAccountRecordTypeList = getNoChannelAccountRecordTypeList(ea);
        // N端对应的客户
        objectDataList.removeIf(o->{
            if(CollectionUtils.isNotEmpty(parentIds) && parentIds.contains(o.getId())){
                return true;
            }
            if(o.getId().equals(currentId) || o.getId().equals(outEaAccId)){
                return true;
            }
            return CollectionUtils.isNotEmpty(noChannelAccountRecordTypeList) && noChannelAccountRecordTypeList.contains(o.getRecordType());
        });
        // 拿到归属这些销售商客户的销售区域 查询到所有上级
        Set<String> allSalesAreaIds = Sets.newHashSet();
        for (IObjectData iObjectData : objectDataList) {
            allSalesAreaIds.addAll((List<String>) iObjectData.get(AccountObjConstants.Field.coveringSalesAreas.getApiName()));
        }
        // 查询当前客户的所有所属销售区域
        allSalesAreaIds.addAll(coveringSalesAreas);
        Map<String,String> salesAreaIdAndParentsMap = baseDao.getByIds(eid,SalesAreaObjConstants.OBJ_API_NAME,Lists.newArrayList(allSalesAreaIds)).
                stream().collect(Collectors.toMap(DBRecord::getId,k2->Optional.ofNullable(k2.get(SalesAreaObjConstants.treeView,String.class)).orElse("")));
        Set<String> accountNames = Sets.newHashSet();

        for (IObjectData iObjectData : objectDataList) {
            List<String> subSalesAreaIdsInSys = (List<String>) iObjectData.get(AccountObjConstants.Field.coveringSalesAreas.getApiName());
            String accountName = iObjectData.getName();
            // 1.系统内的父 在参数中的销售区域中 视为重复
            boolean isInter = false;
            for (String subSalesAreaIdInSys : subSalesAreaIdsInSys) {
                if(coveringSalesAreas.stream().anyMatch(o-> StringUtils.isNotBlank(salesAreaIdAndParentsMap.get(subSalesAreaIdInSys)) && salesAreaIdAndParentsMap.get(subSalesAreaIdInSys).contains(o))){
                    isInter = true;
                    break;
                }
            }
            // 2.参数中的父 在系统内的销售区域中 视为重复
            for (String coveringSalesArea : coveringSalesAreas) {
                if(subSalesAreaIdsInSys.stream().anyMatch(o-> StringUtils.isNotBlank(salesAreaIdAndParentsMap.get(coveringSalesArea)) && salesAreaIdAndParentsMap.get(coveringSalesArea).contains(o))){
                    isInter = true;
                    break;
                }
            }
            // 3.参数中的销售区域包含系统内的销售区域 视为重复
            if(subSalesAreaIdsInSys.stream().anyMatch(coveringSalesAreas::contains)){
                isInter = true;
            }
            if(!accountNames.contains(accountName) && isInter){
                accountNames.add(accountName);
            }
        }
        return accountNames;
    }

    @Override
    public String getOurEnterprise(String eid){
        return getOurEnterprise(eid,getIsOneTerminal(eid));
    }

    @Override
    public IObjectData getUpAccount(String upEi,String ea){
        IObjectData iObjectData = baseDao.getEaRelaObjByDownEa(upEi,ea);
        if(Objects.isNull(iObjectData)){
            log.info("getEaRelaObjByDownEi obj is null upEi:{},ea:{}",upEi,ea);
            return null;
        }
        if(Objects.isNull(iObjectData.get(OtherObjConstants.ENTERPRISE_RELATION_MAPPER_ACCOUNT_ID))){
            log.info("getEaRelaObjByDownEi mapper_account_id is null upEi:{},ea:{}",upEi,ea);
            return null;
        }
        String accId = iObjectData.get(OtherObjConstants.ENTERPRISE_RELATION_MAPPER_ACCOUNT_ID).toString();
        return baseDao.getById(upEi, ObjApiName.AccountObj.toString(),accId);
    }

    private String getOurEnterprise(String eid,boolean isOneTerminal){
        if(isOneTerminal){
            return null;
        }
        List<IObjectData> dataList = baseDao.getAllIObjectDataListByQueryWithFields(User.systemUser(eid), SearchQuery.builder()
                .eq(AccountObjConstants.Field.isOurEnterprise.getApiName(), Lists.newArrayList("YES"))
                .build(), AccountObjConstants.API_NAME,Lists.newArrayList("_id"));
        return CollectionUtils.isNotEmpty(dataList) ? dataList.get(0).getId() : null;
    }

    private boolean getIsOneTerminal(String eid){
        String upEi = String.valueOf(mnServiceImpl.getUpEiForMn(Integer.parseInt(eid)));
        return upEi.equals(eid);
    }

}
