package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.service.RouteService;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2022 - 08 - 11  18:01
 **/
@Slf4j
public class VisitRouteEditAction extends StandardEditAction {

    private RouteService routeService = SpringUtil.getContext().getBean(RouteService.class);

    @Override
    protected void before(Arg arg) {
        this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("VisitRouteEditAction after is start!");
        IObjectData newObjectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getObjectData().getId(), objectDescribe.getApiName());
        String tenantId = newObjectData.getTenantId();
        String routeId = newObjectData.getId();
        if (isApprovalFlowStartSuccessOrAsynchronous(routeId)){
            log.info("VisitRouteEditAction isApprovalFlowStartSuccessOrAsynchronous tenantId:{},routeId:{}",tenantId,routeId);
            ObjectLifeStatus objectLifeStatus = ObjectDataExt.of(this.objectData).getLifeStatus();
            routeService.routeFlowStart(tenantId,routeId,objectLifeStatus.getCode());
        }else {
            log.info("VisitRouteEditAction no isApprovalFlowStartSuccessOrAsynchronous tenantId:{},routeId:{}",tenantId,routeId);
            routeService.editNoNeedTriggerApprovalFlow(tenantId,routeId);
        }
        return super.after(arg, result);
    }
}