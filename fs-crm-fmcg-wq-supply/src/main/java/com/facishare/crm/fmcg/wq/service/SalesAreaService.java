package com.facishare.crm.fmcg.wq.service;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/05/24/ 11:36
 **/
public interface SalesAreaService {

    /**
     * 新建or编辑 check
     */
    void checkSalesAreaOnAddOrEditAcc(String eid, ObjectDataDocument objectData,boolean isOneTerminal);
    List<String> getParentSellerIds(String ea);
    List<String> getNoChannelAccountRecordTypeList(String ea);

    void checkOnSalesArea(int eid,String location,List<String> coveringSalesAreas) ;
    /**
     * 计算渠道等级
     */
    Integer calculateLevel(String tenantId, BaseObjectSaveAction.Arg arg, boolean isAdd);

    String getOurEnterprise(String eid);
    IObjectData getUpAccount(String upEi, String ea);

}
