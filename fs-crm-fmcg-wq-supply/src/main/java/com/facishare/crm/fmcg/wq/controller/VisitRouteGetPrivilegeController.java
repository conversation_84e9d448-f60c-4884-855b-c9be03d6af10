package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.route.VisitRouteGetPrivilege;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 获取功能 权限 数据，
 */
@Slf4j
public class VisitRouteGetPrivilegeController extends PreDefineController<VisitRouteGetPrivilege.Arg, VisitRouteGetPrivilege.Result> {
    FunctionPrivilegeService functionPrivilegeService = SpringUtil.getContext().getBean(FunctionPrivilegeService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected VisitRouteGetPrivilege.Result doService(VisitRouteGetPrivilege.Arg arg) {
        VisitRouteGetPrivilege.Result result = new VisitRouteGetPrivilege.Result();
        Map<String, Boolean> stringBooleanMap = functionPrivilegeService.funPrivilegeCheck(controllerContext.getUser(), arg.getApiName(), arg.getFuncPrivilegeCodes());
        result.setCheckMap(stringBooleanMap);
        return result;
    }
}
