package com.facishare.crm.fmcg.wq.action;


import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 供货关系作废
 * @author: zhangsm
 * @create: 2021-04-29 14:42
 **/
public class DealerSupplyInvalidAction extends StandardInvalidAction {

    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        if (actionContext.getUser().getUserId().equals("20015") && actionContext.getTenantId().equals("721787")){

        }else{
            //初始化审批流上下文
            supplyOperaContext.setArgs(arg);
            supplyOperaContext.setResultClazz(Result.class);
            supplyOperaContext.setOutUpSupplyId(arg.getObjectDataId());
            IObjectData byId = supplyDao.getById(actionContext.getTenantId(), DealerSupplyObjConstants.API_NAME, arg.getObjectDataId());
            supplyOperaContext.setOutUpCustomerId(byId.get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString());
            //供货门店
            supplyService.checkDelDealerSupply(actionContext.getTenantId(),Lists.newArrayList(arg.getObjectDataId()));
            //触发审批流程
            Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(actionContext, supplyOperaContext);
            if (booleanIObjectDataPair.first){
                throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE,SupplyOperaContext.TRIGGERAPPROCODE);
            }
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        ObjectDataDocument objectData = after.getObjectData();
        List<String> delDealerIds = Lists.newArrayList(objectData.get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString());
        //删除对应的供货门店
        supplyDao.delSupplyStoreBySupplierIds(actionContext.getTenantId(),delDealerIds);
        //删除对应的下级供货商
        supplyDao.delDistributorSupplyByUpIds(actionContext.getTenantId(),delDealerIds);
        //删除可售范围
        supplyDao.deleteAvailableRange(actionContext.getTenantId(),Lists.newArrayList(objectData.getId()));
        return after;
    }
}
