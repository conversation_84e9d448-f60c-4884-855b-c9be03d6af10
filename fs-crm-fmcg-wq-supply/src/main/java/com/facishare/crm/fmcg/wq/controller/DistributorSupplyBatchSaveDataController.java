package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.supply.SupplyStoreList;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.model.BatchSaveDistributorOrShopSupplyArgs;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;

import java.util.List;

public class DistributorSupplyBatchSaveDataController extends PreDefineController<SupplyStoreList.Arg,SupplyStoreList.Result> {

    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected SupplyStoreList.Result doService(SupplyStoreList.Arg arg) {
        SupplyStoreList.Result result=new SupplyStoreList.Result();
        BatchSaveDistributorOrShopSupplyArgs batchSaveDistributorOrShopSupplyArgs = supplyService.checkBatchSaveDistributorOrShopSupply(controllerContext.getTenantId(), controllerContext.getUser().getUserId()
                , arg.getDealerSupplyId(), arg.getStoreIds(), 1, null);
        if (CollectionUtils.notEmpty(batchSaveDistributorOrShopSupplyArgs.getAddShopIds())){
            //审核
            supplyOperaContext.setArgs(arg);
            supplyOperaContext.setResultClazz(SupplyStoreList.Result.class);
            supplyOperaContext.setInUpCustomerId(batchSaveDistributorOrShopSupplyArgs.getDealerId());
            supplyOperaContext.setInUpSupplyId(batchSaveDistributorOrShopSupplyArgs.getUpSupplyId());
            supplyOperaContext.setCustermerIds(batchSaveDistributorOrShopSupplyArgs.getAddShopIds());
            supplyOperaContext.setProductIds(batchSaveDistributorOrShopSupplyArgs.getAddSpecialProductIds());
            Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(controllerContext, supplyOperaContext);
            if (booleanIObjectDataPair.first){
                result.setApprovalTriggered(true);
//                throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE,SupplyOperaContext.TRIGGERAPPROCODE);
            }else {
                supplyService.batchSaveDistributorOrShopSupply(batchSaveDistributorOrShopSupplyArgs);
            }
        }
        result.setStoreIds(Lists.newArrayList(batchSaveDistributorOrShopSupplyArgs.getResultError().getConflictMap().keySet()));
        return result;
        //已经添加过的分销邮差不允许添加-分销邮差商已在供货列表，请不要重复添加

//        SupplyStoreList.Result result=new SupplyStoreList.Result();
//        List<IObjectData> dealerSupplyData=serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(arg.getDealerSupplyId()), DealerSupplyObjConstants.API_NAME);
//        //查询供货分销邮差
//        List<IObjectData>  distributorSupplyInfo=supplyDao.getDistributorSupplyByUpSupplyIds(controllerContext.getTenantId(),Lists.newArrayList(arg.getDealerSupplyId()));
//        List<String> accountIds=Lists.newArrayList();
//        if(CollectionUtils.isNotEmpty(distributorSupplyInfo)){
//            distributorSupplyInfo.forEach(o->accountIds.add(o.get("store").toString()));
//        }
//        accountIds.retainAll(arg.getStoreIds());
//        if(CollectionUtils.isNotEmpty(accountIds)){
//            StringBuffer str=new StringBuffer();
//            List<IObjectData> storeDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), accountIds,"AccountObj");
//            Map<String, String> existStoreMaps = storeDataList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (key1, key2) -> key2));
//            for (Map.Entry<String, String> m : existStoreMaps.entrySet()) {
//                str.append(m.getValue()).append(",");
//            }
//            throw new ValidateException(str+"分销邮差商已在供货列表，请不要重复添加");
//        }
//
//        //查询所有门店名称
//        List<IObjectData> storeDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), arg.getStoreIds(),"AccountObj");
//        Map<String, String> storeMaps = storeDataList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (key1, key2) -> key2));
//
//
//        String dealerId=dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();
//        String supplyType=dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.supplyType.getApiName()).toString();
//        Object dep=dealerSupplyData.get(0).getDataOwnDepartment();
//        //经销商或者配送商的经营范围
//        Set<String> dealerProductIds= supplyService.getProductCollectionByDealerId(controllerContext.getTenantId(),dealerId,supplyType.equals("distributor__c")?1:0);
//
//        Map<String,String> conflictStoreMap=new HashMap<>();
//        List<String> addShopIds=Lists.newArrayList();
//
//        if (GrayRelease.isAllow("checkin-server-v2", "batchSaveData", controllerContext.getTenantId())) {
//            arg.getStoreIds().parallelStream().forEach(distributorId -> {
//                //分销邮差的经营范围
//                Set<String> productIds = supplyService.getProductListByDistributionId(controllerContext.getTenantId(), distributorId);
//
//                if (supplyService.checkForProductIdConflicts(productIds,dealerProductIds)) {
//                    conflictStoreMap.put(distributorId, storeMaps.get(distributorId));
//                } else {
//                    addShopIds.add(distributorId);
//                }
//            });
//        }else {
//            for (String distributorId : arg.getStoreIds()) {
//                //分销邮差的经营范围
//                Set<String> productIds = supplyService.getProductListByDistributionId(controllerContext.getTenantId(), distributorId);
////                productIds.retainAll(dealerProductIds);
//                if (supplyService.checkForProductIdConflicts(productIds,dealerProductIds)) {
//                    conflictStoreMap.put(distributorId, storeMaps.get(distributorId));
//                } else {
//                    addShopIds.add(distributorId);
//                }
//            }
//        }
//        if(CollectionUtils.isNotEmpty(addShopIds)) {
//            supplyDao.createDistributorSupplyObj(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), addShopIds, dealerSupplyData.get(0), false);
//            supplyDao.getProductCollectionObjListByAccountIds(controllerContext.getTenantId(), addShopIds, false);
//            for(String shopId:addShopIds) {
//                supplyDao.updateAccountUpDistributors(controllerContext.getTenantId(), shopId);
//            }
//        }
//        int size=arg.getStoreIds().size()-conflictStoreMap.size();
//        StringBuffer failStr=new StringBuffer();
//        for (Map.Entry<String, String> m : conflictStoreMap.entrySet()) {
//            failStr.append(m.getValue()).append("\n");
//        }
//        if(CollectionUtils.isNotEmpty(conflictStoreMap.keySet())) {
//            result.setStoreIds(Lists.newArrayList(conflictStoreMap.keySet()));
//        }
//        IObjectData dealerSupplyObj = supplyDao.getById(controllerContext.getTenantId(), DealerSupplyObjConstants.API_NAME, arg.getDealerSupplyId());
//        for (String storeId : arg.getStoreIds()) {
//            supplyService.syncAvailableObjByDealerSupplyObj(controllerContext.getTenantId(), dealerSupplyObj,storeId);
//        }
//        return result;

    }
//    SupplyStoreList.Result batchSaveDataV2(SupplyStoreList.Arg arg){
////        supplyService.batchSaveDistributorOrShopSupply(arg.getDealerSupplyId(),arg.getStoreIds(),1,null);
//        SupplyStoreList.Result result = new SupplyStoreList.Result();
//        result.setStoreIds(Lists.newArrayList());
//        if (CollectionUtils.isEmpty(arg.getStoreIds())) {
//            return result;
//        }
//        List<IObjectData> storeList = supplyDao.parallelGetDatasByQueryWithFields(controllerContext.getTenantId(), SearchQuery.builder()
//                .eq(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), arg.getDealerSupplyId()).in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), arg.getStoreIds()).build()
//                , DistributorSupplyObjConstants.API_NAME, Lists.newArrayList(DistributorSupplyObjConstants.Field.thisDealerId.getApiName()));
//        Set<String> existShopIds = storeList.stream().map(o -> o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class)).collect(Collectors.toSet());
//        arg.getStoreIds().removeIf(o -> existShopIds.contains(o));
//        if (CollectionUtils.isEmpty(arg.getStoreIds())) {
//            return result;
//        }
//        //查询供货关系
//        List<IObjectData> dealerSupplyData = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(arg.getDealerSupplyId()), DealerSupplyObjConstants.API_NAME);
//        String dealerId = dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();
//        //获取当前经销商的经营范围
//        Supplier argSupplier = supplyService.getSupplierById(controllerContext.getTenantId(), dealerId, 1);
//        Set<String> argProductIds = argSupplier.getProductList().stream().map(o -> o.getProductId()).collect(Collectors.toSet());
//
//        List<IObjectData> existOtherStoreList = supplyDao.parallelGetDatasByQueryWithFields(controllerContext.getTenantId(), SearchQuery.builder()
//                .neq(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(), arg.getDealerSupplyId()).in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), arg.getStoreIds()).build(), DistributorSupplyObjConstants.API_NAME,
//                Lists.newArrayList(DistributorSupplyObjConstants.Field.upDealerId.getApiName(),DistributorSupplyObjConstants.Field.specialSupply.getApiName(), DistributorSupplyObjConstants.Field.thisDealerId.getApiName()));
//        List<String> addShopIds = Lists.newArrayList();
//        //addShop 已经存在的供货关系
//        Map<String, Set<String>> existSupplyIdsMap = MapUtils.EMPTY_MAP;
//        //其他供货商的 产品列表
//        Map<String, Set<String>> otherProductIdsMap = new HashMap<>();
//        MapUtils.synchronizedMap(otherProductIdsMap);
//        //如果存在其他的供货关系 则需要 取 其他的供货关系数据
//        if (CollectionUtils.isNotEmpty(existOtherStoreList)) {
//            //查询门店现有的供货商
//            existSupplyIdsMap = existOtherStoreList.stream().collect(
//                    Collectors.groupingBy(k -> k.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class),
//                            Collectors.mapping(v -> {
//                                        if (v.get(DistributorSupplyObjConstants.Field.specialSupply.getApiName(), Boolean.class)) {
//                                            //特例
//                                            return v.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class) + "_" + v.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), String.class);
//                                        } else {
//                                            //非特例
//                                            return v.get(DistributorSupplyObjConstants.Field.upDealerId.getApiName(), String.class);
//                                        }
//
//                                    }
//                                    , Collectors.toSet())));
//            if (MapUtils.isNotEmpty(existSupplyIdsMap)) {
//                Set<String> existOtherDealers = new HashSet<>();
//                for (Set<String> value : existSupplyIdsMap.values()) {
//                    existOtherDealers.addAll(value);
//                }
//                existOtherDealers.parallelStream().forEach(o -> {
//                    String[] subIds = o.split("_");
//                    if (subIds.length == 1) {
//                        otherProductIdsMap.put(o, supplyService.getSupplierById(controllerContext.getTenantId(), dealerId, 1)
//                                .getProductList().stream().map(s -> s.getProductId()).collect(Collectors.toSet()));
//                    } else {
//                        List<IObjectData> iObjectDataList = supplyDao.parallelGetDatasByQueryWithFields(controllerContext.getTenantId(), SearchQuery.builder()
//                                        .eq(SpecialSupplyObjConstants.Field.shopId.getApiName(), subIds[0])
//                                        .eq(SpecialSupplyObjConstants.Field.dealerId.getApiName(), subIds[1])
//                                        .build(), SpecialSupplyObjConstants.API_NAME,
//                                Lists.newArrayList(SpecialSupplyObjConstants.Field.productId.getApiName()));
//                        if (CollectionUtils.isNotEmpty(iObjectDataList)){
//                            otherProductIdsMap.put(o,iObjectDataList.stream().map(i->i.get(SpecialSupplyObjConstants.Field.productId.getApiName(),String.class)).collect(Collectors.toSet()));
//                        }else{
//                            otherProductIdsMap.put(o, SetUtils.EMPTY_SET);
//                        }
//
//                    }
//                });
//            }
//        }
//        //判断冲突
//        for (String shopId : arg.getStoreIds()) {
//            List<String> productIds = Lists.newArrayList();
//            Set<String> supplyAccountIds = existSupplyIdsMap.get(shopId);
//            if (CollectionUtils.isNotEmpty(supplyAccountIds)) {
//                for (String o : supplyAccountIds) {
//                    Set<String> otherProductIds = otherProductIdsMap.get(o);
//                    if (CollectionUtils.isNotEmpty(otherProductIds)) {
//                        productIds.addAll(otherProductIds);
//                    }
//                }
//                //判断冲突
//                if (supplyService.checkForProductIdConflicts(argProductIds, productIds)) {
////                        冲突
//                    result.getStoreIds().add(shopId);
//                } else {
//                    addShopIds.add(shopId);
//                }
//            } else {
//                addShopIds.add(shopId);
//            }
//
//
//        }
//        if (CollectionUtils.isNotEmpty(addShopIds)) {
//            log.info("add shopIds {}",addShopIds);
//            supplyDao.createDistributorSupplyObj(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), addShopIds, dealerSupplyData.get(0), false);
//            fmcgThreadPoolExecutor.execute(
//                    () ->{
//                        //同步当前的经营范围
//                        supplyDao.getProductCollectionObjListByAccountIds(controllerContext.getTenantId(), addShopIds, false);
//                        addShopIds.parallelStream().forEach(shopId -> {
//                            //重新设置客户的上级供货商字段
//                            supplyDao.updateAccountUpDistributors(controllerContext.getTenantId(), shopId);
//                            //重新同步可售范围
//                            syncAvailableRangeService.syncAvailableObjByDealerSupplyObj(controllerContext.getTenantId(), dealerSupplyData.get(0), shopId);
//
//                        });
//                    }
//
//            );
//        }
//        //获取客户的其他经营范围
//        return result;
//    }
}
