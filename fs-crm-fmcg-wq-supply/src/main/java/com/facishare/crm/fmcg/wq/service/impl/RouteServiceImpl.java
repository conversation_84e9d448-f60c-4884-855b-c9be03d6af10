package com.facishare.crm.fmcg.wq.service.impl;

import com.facishare.appserver.checkins.api.service.ObjUpdateService;
import com.facishare.crm.fmcg.wq.service.RouteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2023 - 04 - 06  16:32
 **/
@Service
public class RouteServiceImpl implements RouteService {

    @Autowired
    ObjUpdateService objUpdateService;

    @Override
    public boolean routeFlowStart(String tenantId, String routeId,String objectLifeStatus) {
        return objUpdateService.routeFlowStart(tenantId,routeId,objectLifeStatus);
    }

    @Override
    public boolean routeFlowCompleted(String tenantId, String routeId,Boolean isPass,String objectLifeStatus) {
        return objUpdateService.routeFlowCompleted(tenantId, routeId, isPass, objectLifeStatus);
    }

    @Override
    public boolean addNoNeedTriggerApprovalFlow(String tenantId, String routeId) {
        return objUpdateService.addNoNeedTriggerApprovalFlow(tenantId,routeId);
    }

    @Override
    public boolean editNoNeedTriggerApprovalFlow(String tenantId, String routeId) {
        return objUpdateService.editNoNeedTriggerApprovalFlow(tenantId, routeId);
    }

    @Override
    public boolean invalidNoNeedTriggerApprovalFlow(String tenantId, String routeId) {
        return objUpdateService.invalidNoNeedTriggerApprovalFlow(tenantId, routeId);
    }


}
