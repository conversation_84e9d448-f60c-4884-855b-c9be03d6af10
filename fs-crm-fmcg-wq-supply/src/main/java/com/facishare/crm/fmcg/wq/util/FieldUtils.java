package com.facishare.crm.fmcg.wq.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.SelectMany;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: fs-crm-fmcg
 * @description:
 * @author: zhangsm
 * @create: 2021-06-10 16:34
 **/
public class FieldUtils {
    public static List<String> getStringList(IObjectData iObjectData , String apiName){
        Object value = iObjectData.get(apiName);
        if (null == value) {
            return Lists.newArrayList();
        } else {
            String str;
            if (value instanceof String) {
                str = (String)value;
            } else {
                str = JSON.toJSONString(value);
            }

            return (List) JSONObject.parseObject(str, List.class);
        }
    }

    public static String getShowStr(IObjectData data, IFieldDescribe fieldDescribe){
        if(Objects.isNull(fieldDescribe) || Objects.isNull(data)){
            return "";
        }
        Object value = data.get(fieldDescribe.getApiName());
        if (null == value) {
            return "";
        } else {
            switch (fieldDescribe.getType()){
                case "time":
                case "date_time":
                case "date":
                    if (value instanceof Double) {
                        return DateUtils.getStringFromTime(((Double) value).longValue(), fieldDescribe.get("date_format",String.class,"yyyy-MM-dd HH:mm"));
                    } else if (value instanceof Long){
                        return DateUtils.getStringFromTime((long) value, fieldDescribe.get("date_format",String.class,"yyyy-MM-dd HH:mm"));
                    }
                    break;
                case "select_one":
                case "true_or_false":
                    SelectOne one = (SelectOne) fieldDescribe;
                    for (ISelectOption selectOption : one.getSelectOptions()) {
                        if(selectOption.getValue().equals(value)){
                            return selectOption.getLabel();
                        }
                    }
                    break;
                case "select_many":
                    StringBuffer sb = new StringBuffer();
                    boolean isMatched = false;
                    List<Object> values = (List) value;
                    if(CollectionUtils.isNotEmpty(values)) {
                        SelectMany many = (SelectMany) fieldDescribe;
                        for (Object v : values) {
                            for (ISelectOption selectOption : many.getSelectOptions()) {
                                if (selectOption.getValue().equals(v)) {
                                    sb.append(selectOption.getLabel()).append(",");
                                    isMatched=true;
                                }
                            }
                        }
                    }
                    if (isMatched) {
                        sb.deleteCharAt(sb.length() - 1);
                    }
                    return sb.toString();
                case "employee":
                    Map<String,Object> owner = data.get(fieldDescribe.getApiName()+"__r",Map.class, Maps.newHashMap());
                    return owner.getOrDefault("name","").toString();
                case "department":
                    Map<String,Object> dept = data.get(fieldDescribe.getApiName()+"__r",Map.class,Maps.newHashMap());
                    return dept.getOrDefault("deptName","").toString();
                case "object_reference":
                    return data.get(fieldDescribe.getApiName()+"__r", String.class, "");
                case "object_reference_many":
                    StringBuffer stringBuffer = new StringBuffer();
                    boolean flag = false;
                    List<Map<String,Object>> referenceValues =  data.get(fieldDescribe.getApiName()+"__r",List.class,Lists.newArrayList());
                    if(CollectionUtils.isNotEmpty(referenceValues)) {
                        for (Map<String,Object> v : referenceValues) {
                            stringBuffer.append(v.get("name")).append(",");
                            flag=true;
                        }
                    }
                    if (flag) {
                        stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                    }
                    return stringBuffer.toString();
                default:
                    return value.toString();
            }
        }
        return "";
    }
}
