package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.VerifyStore;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 附近客户 是否有权限拜访   已经废弃不用
 */
@SuppressWarnings("Duplicates")
public class AreaManageVerifyStoreController extends PreDefineController<VerifyStore.Arg, VerifyStore.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Collections.emptyList();
    }

    @Override
    protected VerifyStore.Result doService(VerifyStore.Arg arg) {
        VerifyStore.Result result = new VerifyStore.Result();
        if(CollectionUtils.isEmpty(arg.getStoreIds())){
            return result;
        }
        IObjectData area = queryAreaManage(controllerContext);
        if(Objects.isNull(area)){
            for (String storeId : arg.getStoreIds()) {
                result.getStoreInfo().put(storeId,-1);
            }
            return result;
        }
        String areaId = (String)area.get(CommonConstants.ID);
        String productGroup = (String)area.get(AreaManageConstants.PRODUCT_GROUP);
        List<IObjectData> data = queryStoreMap(controllerContext,arg.getStoreIds());
        if(CollectionUtils.isEmpty(data)){
            for (String storeId : arg.getStoreIds()) {
                result.getStoreInfo().put(storeId,0);
            }
            return result;
        }
        Map<String,List<IObjectData>> storeAreaMap = data.stream().collect(Collectors.groupingBy(k ->(String)k.get(CoveredStoresConstants.STORE)));
        Set<String> areaIds = data.stream().map(o->(String)o.get(CoveredStoresConstants.BELONG_AREA)).collect(Collectors.toSet());
        areaIds.removeIf(o->areaId.equals(o));
        List<String> conflictIds = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(areaIds)){
            List<IObjectData> areaInfos = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(),areaIds.stream().collect(Collectors.toList()), AreaManageConstants.AREA_MANAGE_OBJ);
            for (IObjectData areaInfo : areaInfos) {
                if(productGroup.equals(areaInfo.get(AreaManageConstants.PRODUCT_GROUP))){
                    conflictIds.add((String)areaInfo.get(CommonConstants.ID));
                }
            }
        }
        for (String storeId : arg.getStoreIds()) {
            List<IObjectData> temp = storeAreaMap.get(storeId);
            if(CollectionUtils.isNotEmpty(temp)){
                boolean end = false;
                for (IObjectData covered : temp) {
                    if(!end&&areaId.equals(covered.get(CoveredStoresConstants.BELONG_AREA))){
                        result.getStoreInfo().put(storeId,1);
                        end = true;
                    }
                }
                if(!end) {
                    if (CollectionUtils.isNotEmpty(conflictIds)) {
                        for (IObjectData objectData : temp) {
                            if (conflictIds.contains(objectData.get(CoveredStoresConstants.BELONG_AREA))) {
                                result.getStoreInfo().put(storeId, 3);
                                break;
                            }
                        }
                    } else {
                        result.getStoreInfo().put(storeId, 2);
                    }
                }

            }else {
                result.getStoreInfo().put(storeId,0);
            }
        }
        return result;
    }

    private List<IObjectData> queryStoreMap(ControllerContext controllerContext,List<String> storeIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(500);
        query.setOffset(0);

        Filter areaFilter = new Filter();
        areaFilter.setFieldName(CoveredStoresConstants.STORE);
        areaFilter.setOperator(Operator.IN);
        areaFilter.setFieldValues(storeIds);

        query.setFilters(Lists.newArrayList(areaFilter));
        return serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), CoveredStoresConstants.COVERED_STORES_OBJ, query).getData();
//        return data.stream().collect(Collectors.groupingBy(k ->(String)k.get(CoveredStoresConstants.STORE)));
    }

    private IObjectData queryAreaManage(ControllerContext context) {

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(500);
        query.setOffset(0);

        Filter areaFilter = new Filter();
        areaFilter.setFieldName(AreaManageConstants.AREA_OWNER);
        areaFilter.setOperator(Operator.IN);
        areaFilter.setFieldValues(Lists.newArrayList(context.getUser().getUserId()));

        query.setFilters(Lists.newArrayList(areaFilter));
        List<IObjectData> datas = serviceFacade.findBySearchQuery(User.systemUser(context.getTenantId()), AreaManageConstants.AREA_MANAGE_OBJ, query).getData();
        return CollectionUtils.isNotEmpty(datas)?datas.get(0):null;
    }
}
