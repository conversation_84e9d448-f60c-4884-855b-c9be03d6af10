package com.facishare.crm.fmcg.wq.service;


import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.search.IFilter;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface ChannelService {

    /**
     * 渠道对象 启用状态不可以作废
     */
    void checkIsValid(String tenantId, List<String> dataIds);

    /**
     * 构建渠道对象filter
     */
    List<IFilter> buildFilter(List<IFilter> filters);

    /**
     * 计算渠道等级
     */
    Integer calculateLevel(String tenantId,BaseObjectSaveAction.Arg arg,boolean isAdd);

    /**
     * 根目录不允许禁用
     */
    void checkIsRoot(String tenantId, BaseObjectSaveAction.Arg arg);

}
