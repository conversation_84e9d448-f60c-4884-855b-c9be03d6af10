package com.facishare.crm.fmcg.wq.action;


import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.constants.ProductCollectionObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.model.obj.ProductCollectionObj;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 供货关系 add
 * @author: zhangsm
 * @create: 2021-04-29 14:42
 **/
public class DealerSupplyAddAction extends StandardAddAction {

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        String tenantId = actionContext.getTenantId();
        String dealerId = (String) arg.getObjectData().get(DealerSupplyObjConstants.Field.dealerId.getApiName());

        //校验是否已经存在
        List<IObjectData> iObjectData=supplyDao.getDealerSupplyBySuppliersNOPreCreate(tenantId,Lists.newArrayList(dealerId));
        if(CollectionUtils.isNotEmpty(iObjectData)){
            throw new ValidateException("此配送商已经存在，请勿重复添加"); //ignoreI18n
        }


        arg.getObjectData().put(BaseField.dataOwnDepartment.getApiName(),
                Lists.newArrayList(supplyDao.getAccountObjById(tenantId,dealerId).getDepartmentId()));
        // 会自动创建 经营范围
        List<ProductCollectionObj> productCollectionObjListByAccountIds = supplyDao.getProductCollectionObjListByAccountIds(tenantId, Lists.newArrayList(dealerId), false);

        if (CollectionUtils.isEmpty(productCollectionObjListByAccountIds)){
            AccountObj accountObj = supplyDao.getAccountObjById(tenantId, dealerId);
            throw new ValidateException(String.format("%s 配送商，未设置对应的 %s", //ignoreI18n
                    accountObj.getName(),
                    ProductCollectionObjConstants.DISPLAY_NAME));
        }

    }
}
