package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.special.SpecialList;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.dao.BaseDaoInterface;
import com.facishare.crm.fmcg.wq.model.BatchSaveDistributorOrShopSupplyArgs;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public class SpecialSupplyBatchSaveDataController extends PreDefineController<SpecialList.Arg, SpecialList.Result> {

    SupplyService supplyService=SpringUtil.getContext().getBean(SupplyService.class);
    SupplyDao supplyDao =  SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected SpecialList.Result doService(SpecialList.Arg arg) {
        SpecialList.Result result=new SpecialList.Result();
        if(CollectionUtils.isEmpty(arg.getStoreIds())||CollectionUtils.isEmpty(arg.getProductIds())){
            throw new ValidateException("所选门店或者产品均不能为空"); //ignoreI18n
        }
        BatchSaveDistributorOrShopSupplyArgs batchSaveDistributorOrShopSupplyArgs = supplyService.checkBatchSaveDistributorOrShopSupply(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), arg.getDealerSupplyId(), arg.getStoreIds(), arg.getButtonType(), arg.getProductIds());
        if (CollectionUtils.isNotEmpty(batchSaveDistributorOrShopSupplyArgs.getAddShopIds())){
            supplyOperaContext.setArgs(arg);
            supplyOperaContext.setResultClazz(SpecialList.Result.class);
            supplyOperaContext.setInUpCustomerId(batchSaveDistributorOrShopSupplyArgs.getDealerId());
            supplyOperaContext.setInUpSupplyId(batchSaveDistributorOrShopSupplyArgs.getUpSupplyId());
            supplyOperaContext.setCustermerIds(batchSaveDistributorOrShopSupplyArgs.getAddShopIds());
            supplyOperaContext.setProductIds(batchSaveDistributorOrShopSupplyArgs.getAddSpecialProductIds());
            Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(controllerContext, supplyOperaContext);
            if (booleanIObjectDataPair.first){
                throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE, SupplyOperaContext.TRIGGERAPPROCODE);
            }else{
                supplyService.batchSaveDistributorOrShopSupply(batchSaveDistributorOrShopSupplyArgs);
            }
        }
        result.setSpecialInfos(batchSaveDistributorOrShopSupplyArgs.getResultError().getConflictMap());
        result.setExistInfo(batchSaveDistributorOrShopSupplyArgs.getResultError().getExistList());
        result.setExistMap(batchSaveDistributorOrShopSupplyArgs.getResultError().getExistProductMap());
//        if (arg.getButtonType() == 0){
//            result.set
//        }else if (arg.getButtonType() == 1){
//
//        }
//        String distributorId=arg.getDistributorId();
////        重复校验 以下门店已经在特例门店列表中了，不要重复添加
//        SearchQuery.SearchQueryBuilder query = SearchQuery.builder()
//                .eq(SpecialSupplyObjConstants.Field.dealerId.getApiName(), (distributorId))
//                .in(SpecialSupplyObjConstants.Field.shopId.getApiName(),arg.getStoreIds());
//        List<IObjectData> data=serviceFacade.findBySearchQuery(User.systemUser(controllerContext.getTenantId()), SpecialSupplyObjConstants.API_NAME,query.build().getSearchTemplateQuery()).getData();
//        if(CollectionUtils.isNotEmpty(data)){
//
////            List<String> existProducts=Lists.newArrayList();
////            data.forEach(o->existProducts.add(o.get(SpecialSupplyObjConstants.Field.productId.getApiName()).toString()));
////            existProducts.retainAll(arg.getProductIds());
////            result.setExistInfo(existProducts);
////            if(CollectionUtils.isNotEmpty(existProducts)){
////                return result;
////            }
//            Map<String,List<String>> existMap=new HashMap<>();
//            Map<String,List<String>> map = data.stream().collect(Collectors.groupingBy(k ->(String)k.get(SpecialSupplyObjConstants.Field.shopId.getApiName()),Collectors.mapping(o->o.get("product").toString(),Collectors.toList())));
//            for (Map.Entry<String, List<String>> m : map.entrySet()) {
//                m.getValue().retainAll(arg.getProductIds());
//                if(CollectionUtils.isNotEmpty(m.getValue())){
//                    existMap.put(m.getKey(),m.getValue());
//                }
//            }
//            result.setExistMap(existMap);
//            result.setExistInfo(Lists.newArrayList(existMap.keySet()));
//            if(MapUtils.isNotEmpty(existMap)){
//                return result;
//            }
//        }
//
//        //经营范围冲突校验-以下供货产品与其他配送商供货产品有冲突
//
//        Map<String,List<String>> conflictMap=new HashMap<>();
//        //查询所有门店名称
//        List<IObjectData> storeDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), arg.getStoreIds(),"AccountObj");
//        Map<String, String> storeMaps = storeDataList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (key1, key2) -> key2));
//
//        List<String> addShopIds=Lists.newArrayList();
//        for(String shopId:arg.getStoreIds()) {
//            Set<String> productIds=new HashSet<>();
//            if(arg.getButtonType()==0) {//门店
//                // 判断门店是否已经在供货门店中且不是特例时，不需要校验
//                SearchQuery searchQuery = SearchQuery.builder()
//                        .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), Lists.newArrayList(shopId))
//                        .eq(SupplyStoreObjConstants.Field.upSupplyId.getApiName(),arg.getDealerSupplyId())
//                        .build();
//                List<IObjectData> supplyStoreData = supplyDao.getAllIObjectDataListByQuery(controllerContext.getTenantId(), searchQuery, SupplyStoreObjConstants.API_NAME);
//                if(CollectionUtils.isNotEmpty(supplyStoreData)){
//                    if(supplyStoreData.get(0).get(SupplyStoreObjConstants.Field.specialSupply.getApiName()).equals(false)){
//
//                    }else{
//                        productIds = supplyService.getProductListByStoreId(controllerContext.getTenantId(), shopId);
//                    }
//                }else{
//                    productIds = supplyService.getProductListByStoreId(controllerContext.getTenantId(), shopId);
//                }
//
//            }else{// 分销邮差商
//                SearchQuery searchQuery = SearchQuery.builder()
//                        .in(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), Lists.newArrayList(shopId))
//                        .eq(DistributorSupplyObjConstants.Field.upSupplyId.getApiName(),arg.getDealerSupplyId())
//                        .build();
//                List<IObjectData> distributorData = supplyDao.getAllIObjectDataListByQuery(controllerContext.getTenantId(), searchQuery, DistributorSupplyObjConstants.API_NAME);
//                if(CollectionUtils.isNotEmpty(distributorData)){
//                    if(distributorData.get(0).get(SupplyStoreObjConstants.Field.specialSupply.getApiName()).equals(false)){
//
//                    }else{
//                        productIds = supplyService.getProductListByDistributionId(controllerContext.getTenantId(), shopId);
//                    }
//                }else{
//                    productIds = supplyService.getProductListByDistributionId(controllerContext.getTenantId(), shopId);
//                }
//            }
//            productIds.retainAll(arg.getProductIds());
//            //查询productIds的产品名称
//            if(CollectionUtils.isNotEmpty(productIds)) {
////                    List<IObjectData> productDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(productIds), "ProductObj");
////                    Map<String, String> productMaps = productDataList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (key1, key2) -> key2));
////                    productIds.forEach(o->conflictMap.put(storeMaps.get(shopId),productMaps.get(o)));
//                conflictMap.put(shopId,Lists.newArrayList(productIds));
//            }else{
//                addShopIds.add(shopId);
//            }
//        }
//
//        if(MapUtils.isNotEmpty(conflictMap)){
////            throw new ValidateException("以下供货产品与其他配送商供货产品有冲突,请调整");
////            result.setSpecialInfos(Lists.newArrayList(conflictMap.keySet()));
//            result.setSpecialInfos(conflictMap);
//        }
//
//
//        //查询供货关系
//        if(CollectionUtils.isNotEmpty(addShopIds)) {
//            List<IObjectData> dealerSupplyData = supplyDao.getDealerSupplyBySuppliers(controllerContext.getTenantId(), Lists.newArrayList(distributorId));
//            if (arg.getButtonType() == 0) {
//                supplyDao.createSupplyStoreObj(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), addShopIds, dealerSupplyData.get(0), true);
//                for(String shopId:addShopIds) {
//                    supplyDao.updateAccountSupply(controllerContext.getTenantId(),shopId);
//                }
//            } else {
//                supplyDao.createDistributorSupplyObj(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), addShopIds, dealerSupplyData.get(0), true);
//                for(String shopId:addShopIds) {
//                    supplyDao.updateAccountUpDistributors(controllerContext.getTenantId(),shopId);
//                }
//            }
//            //保存特例
//            supplyDao.createSpecialSupplyObj(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), addShopIds, arg.getProductIds(), arg.getDealerSupplyId(),distributorId,dealerSupplyData.get(0).getDataOwnDepartment());
//        }
//
//        //同步可售范围
//        supplyService.addAvailableObjSyncTask(controllerContext.getTenantId(), arg.getDealerSupplyId());
        return result;
    }



}
