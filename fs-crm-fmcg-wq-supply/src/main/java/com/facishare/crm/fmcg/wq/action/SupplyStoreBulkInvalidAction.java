package com.facishare.crm.fmcg.wq.action;


import com.facishare.crm.fmcg.wq.constants.DistributorSupplyObjConstants;
import com.facishare.crm.fmcg.wq.constants.SupplyStoreObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description: DistributorSupplyObj add
 * @author: zhangsm
 * @create: 2021-04-29 14:42
 **/
public class SupplyStoreBulkInvalidAction extends StandardBulkInvalidAction {

    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected void before(Arg arg) {
        throw new ValidateException("不支持批量作废"); //ignoreI18n
//        super.before(arg);
//        String tenantId = actionContext.getTenantId();
//        List<IObjectData> distributorSupplyListByIds = supplyService.getDistributorSupplyListByIds(tenantId, arg.getDataIds());
//        Set<String> supplierIds = distributorSupplyListByIds.stream().map(o ->
//                o.get(DistributorSupplyObjConstants.Field.thisDealerId.getApiName(), String.class)).collect(Collectors.toSet());
//        List<IObjectData> dealerSupplyBySuppliers = baseDao.getDealerSupplyBySuppliers(tenantId, Lists.newArrayList(supplierIds));
//        if (CollectionUtils.isNotEmpty(dealerSupplyBySuppliers)) {
//            throw new ValidateException(String.format("数据 %s 正在被供货关系 %s 使用，无法作废",
//                    distributorSupplyListByIds.stream().map(o -> o.getName()).collect(Collectors.joining(",")),
//                    dealerSupplyBySuppliers.stream().map(o -> o.getName()).collect(Collectors.joining(","))
//            ));
//        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        List<ObjectDataDocument> delList = after.getObjectDataList();
        List<String> delDealerIds = delList.stream().map(o ->
                o.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName()).toString()).distinct().collect(Collectors.toList());
        //删除对应的供货关系
        String tenantId = actionContext.getTenantId();
        supplyDao.delSpecialStore(tenantId, delDealerIds);
        for (String delDealerId : delDealerIds) {
            supplyDao.updateShopUpSupplyFields(tenantId, delDealerId);
        }
        delList.stream().map(o -> o.get(SupplyStoreObjConstants.Field.upSupplyId.getApiName()).toString()).distinct()
                .forEach(dealerSupplyId -> {
                    supplyService.addAvailableObjSyncTask(tenantId, dealerSupplyId);
                });
        return after;
    }
}
