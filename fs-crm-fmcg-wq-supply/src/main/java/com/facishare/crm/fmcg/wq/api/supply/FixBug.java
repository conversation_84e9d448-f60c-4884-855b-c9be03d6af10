package com.facishare.crm.fmcg.wq.api.supply;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @program: fs-crm-fmcg
 * @description: fixbug
 * @author: zhangsm
 * @create: 2022-01-18 14:47
 **/
public interface FixBug {
    @Data
    @ToString
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg implements Serializable {
        // 1 是更新价目表逻辑
      private int flag;
    }
    @ToString
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result implements Serializable{
        String success = "成功"; //ignoreI18n

    }

}
