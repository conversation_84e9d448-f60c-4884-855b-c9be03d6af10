package com.facishare.crm.fmcg.wq.action;


import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.SpecialSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.obj.AccountObj;
import com.facishare.crm.fmcg.wq.model.obj.ShopProductSupplierRelationshipObj;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 供货关系批量作废
 * @author: zhangsm
 * @create: 2021-04-29 14:42
 **/
public class SpecialSupplyBulkInvalidAction extends StandardBulkInvalidAction {

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    @Override
    protected void before(Arg arg) {
        throw new ValidateException("不支持批量作废"); //ignoreI18n
//        super.before(arg);
        //供货门店
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        List<ObjectDataDocument> delList = after.getObjectDataList();
        String tenantId = actionContext.getTenantId();
        delList.parallelStream().forEach(objectData -> {
            String shopId = objectData.get(SpecialSupplyObjConstants.Field.shopId.getApiName()).toString();
            // 没有其他的了
            List<ShopProductSupplierRelationshipObj> specialSupplyByShopId = supplyDao.getSpecialSupplyByShopId(tenantId, shopId);
            specialSupplyByShopId.removeIf(o->o.getId().equals(objectData.getId()));
            if (CollectionUtils.isEmpty(specialSupplyByShopId)) {
                List<String> delDealerIds = Lists.newArrayList(shopId);
                //删除对应的供货门店关系
                supplyDao.delSupplyStore(tenantId, delDealerIds);
                AccountObj accountObjById = supplyDao.getAccountObjById(tenantId, shopId);
                if (accountObjById.getRecordType().equals(AccountObjConstants.Value.default__c.name())) {
                    supplyDao.updateShopUpSupplyFields(tenantId, accountObjById.getId());
                } else {
                    supplyDao.updateDistributorUpSupplyFields(tenantId, accountObjById.getId());

                }
            }
            supplyService.addAvailableObjSyncTask(actionContext.getTenantId(), objectData
                    .get(SpecialSupplyObjConstants.Field.dealerSupplyId.getApiName()).toString());


        });
        return after;
    }

}
