package com.facishare.crm.fmcg.wq.action;


import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg
 * @description: 供货关系批量作废
 * @author: zhangsm
 * @create: 2021-04-29 14:42
 **/
public class DealerSupplyBulkInvalidAction extends StandardBulkInvalidAction {

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    @Override
    protected void before(Arg arg) {
        if (actionContext.getUser().getUserId().equals("20015") && actionContext.getTenantId().equals("721787")){

        }else {
            throw new ValidateException("暂不支持批量作废"); //ignoreI18n
        }
//        super.before(arg);
//        //供货关系是否能被删除
//        supplyService.checkDelDealerSupply(actionContext.getTenantId(),arg.getDataIds());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        List<ObjectDataDocument> delList = after.getObjectDataList();
        List<String> delDealerIds = delList.stream().map(o ->
                o.get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString()).collect(Collectors.toList());
        //删除对应的供货门店
        supplyDao.delSupplyStore(actionContext.getTenantId(),delDealerIds);
        //删除对应的下级供货商
        supplyDao.delDistributorSupplyByUpIds(actionContext.getTenantId(),delDealerIds);
        //删除可售范围
        supplyDao.deleteAvailableRange(actionContext.getTenantId(),delList.stream().map(o->o.getId()).collect(Collectors.toList()));
        return after;
    }

}
