package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.crmrestapi.common.contants.AccountFieldContants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 促销员编辑审批流 同意以后 需要 更新促销员
 */
@Slf4j
public class PromoterFlowCompletedAction extends StandardFlowCompletedAction {
    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        if (String.valueOf(ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode()).equals(arg.getTriggerType()) && arg.isPass()) {
            //编辑审批通过
            if (updatedFieldMap.containsKey(PromoterFields.MOBILE) || updatedFieldMap.containsKey(BaseField.name.getApiName())) {
                pmmService.updatePublicEmployeeMobile(actionContext.getTenantId(),actionContext.getUser().getUserId(), data.get(PromoterFields.PUBLIC_EMPLOYEE_ID).toString(), data.get(PromoterFields.MOBILE).toString(), data.get(BaseField.name.getApiName()).toString());
            }
            if (updatedFieldMap.containsKey(PromoterFields.PROMOTER_TYPE) && data.get(PromoterFields.REVIEW_STATUS).equals(PromoterFields.ReviewStatus.agree.getValue())) {
                pmmService.changeRolesWithPromoter(actionContext.getTenantId(),data);
            }
            //修改相关团队逻辑
            if (updatedFieldMap.containsKey(PromoterFields.RESPONSIBLE_ACCOUNTS)) {
                List<String> beforeIds = dbData.get(PromoterFields.RESPONSIBLE_ACCOUNTS, List.class);
                List<String> afterIds = data.get(PromoterFields.RESPONSIBLE_ACCOUNTS, List.class);
                //del before 判空
                List<String> del = CollectionUtils.isEmpty(beforeIds) ? null : beforeIds.stream().filter(s -> CollectionUtils.isEmpty(afterIds) || !afterIds.contains(s)).collect(Collectors.toList());
                //add after 判空
                List<String> add = CollectionUtils.isEmpty(afterIds) ? null : afterIds.stream().filter(s -> CollectionUtils.isEmpty(beforeIds) || !beforeIds.contains(s)).collect(Collectors.toList());
                //修改相关团队
                if (CollectionUtils.isNotEmpty(del)) {
                    pmmService.removePromoterFromTeam(actionContext.getTenantId(),data.get(PromoterFields.ENTERPRISE_RELATION_ID,String.class), data.get(PromoterFields.PUBLIC_EMPLOYEE_ID).toString(), del, AccountFieldContants.API_NAME);
                }
                if (CollectionUtils.isNotEmpty(add)) {
                    pmmService.addPromoterToTeam(actionContext.getTenantId(),data.get(PromoterFields.ENTERPRISE_RELATION_ID,String.class), data.get(PromoterFields.PUBLIC_EMPLOYEE_ID).toString(), add,AccountFieldContants.API_NAME);
                }
            }
        }
        return after;
    }
}
