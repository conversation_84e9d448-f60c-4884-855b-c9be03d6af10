package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.api.area.HandleStore;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class CoveredStoresAddStoreController extends PreDefineController<HandleStore.Arg, HandleStore.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected HandleStore.Result doService(HandleStore.Arg arg) {
        HandleStore.Result result = new HandleStore.Result();
        log.info("CoveredStoresAddStoreController arg:{}", JSON.toJSONString(arg));
        String ea = serviceFacade.getEAByEI(controllerContext.getTenantId());
        Boolean isYL = GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea);

        arg.getStoreIds().removeIf(Objects::isNull);

        IObjectData areaInfo = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()),arg.getAreaId(), AreaManageConstants.AREA_MANAGE_OBJ);

        if(isYL) {
            log.info("CoveredStoresAddStoreController yl check start");
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(arg.getStoreIds().size() + 1);
            query.setOffset(0);
            Filter keywordFilter = new Filter();
            keywordFilter.setFieldName(CoveredStoresConstants.STORE);
            keywordFilter.setOperator(Operator.IN);
            keywordFilter.setFieldValues(arg.getStoreIds());
            query.setFilters(Lists.newArrayList(keywordFilter));
            query.setSearchSource("db");
            List<IObjectData> storeData = serviceFacade.findBySearchQuery(controllerContext.getUser(), CoveredStoresConstants.COVERED_STORES_OBJ, query).getData();
            List<String> exists = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(storeData)) {
                exists.addAll(storeData.stream().filter(o -> Objects.nonNull(o.get("store"))).map(o -> o.get("store").toString()).collect(Collectors.toList()));
            }

            if (CollectionUtils.isNotEmpty(exists)) {
                log.info("CoveredStoresAddStoreController has storeIds:{}", exists);
                arg.getStoreIds().removeAll(exists);
            }
        }
        if(Objects.nonNull(areaInfo) && CollectionUtils.isNotEmpty(arg.getStoreIds())){
            List<IObjectData> details = Lists.newArrayList();
            for (String account : arg.getStoreIds()) {
                IObjectData detail = new ObjectData();
                detail.set("store", account);
                detail.setTenantId(controllerContext.getTenantId());
                detail.setOwner(areaInfo.getOwner());
                detail.setDescribeApiName(CoveredStoresConstants.COVERED_STORES_OBJ);
                detail.setRecordType("default__c");
                detail.setDataOwnDepartment(areaInfo.getDataOwnDepartment());
                detail.set(CoveredStoresConstants.BELONG_AREA,arg.getAreaId());
                details.add(detail);
            }
            List<IObjectData> data = serviceFacade.bulkSaveObjectData(details,controllerContext.getUser());

            masterModifyLog(areaInfo,data);

            if(CollectionUtils.isNotEmpty(data)) {
                supplyDao.updateAccountAreaInfo(controllerContext.getTenantId(), controllerContext.getUser().getUserIdInt(), areaInfo, arg.getStoreIds(), isYL);
            }
            result.setDataList(ObjectDataDocument.ofList(data));
        }

        return result;
    }
    protected String masterLogId;

    private Map<String, Map<String, Map<String, Object>>> getUpdatedFieldMapForLog(IObjectData objectData) {
        Map<String, Map<String, Map<String, Object>>> allUpdatedFieldMap = Maps.newHashMap();
        // 获取主对象的更新字段
        Map<String, Map<String, Object>> masterUpdateMap = Maps.newHashMap();
        masterUpdateMap.put(objectData.getId(), Maps.newHashMap());
        allUpdatedFieldMap.put(objectData.getDescribeApiName(), masterUpdateMap);
        return allUpdatedFieldMap;
    }
    private void masterModifyLog(IObjectData cpObjectData, List<IObjectData> dataList) {
        Map<String, IObjectDescribe> objectDescribes = Maps.newHashMap();
        IObjectDescribe mainDesc = serviceFacade.findObject(controllerContext.getTenantId(), AreaManageConstants.AREA_MANAGE_OBJ);
        IObjectDescribe detailDesc = serviceFacade.findObject(controllerContext.getTenantId(), CoveredStoresConstants.COVERED_STORES_OBJ);

        objectDescribes.put(AreaManageConstants.AREA_MANAGE_OBJ, mainDesc);
        objectDescribes.put(CoveredStoresConstants.COVERED_STORES_OBJ, detailDesc);
        List detailChange = Lists.newArrayList(CoveredStoresConstants.COVERED_STORES_OBJ);
        LogService.MasterLogInfo masterLog = this.serviceFacade.fillMasterModifyLog(this.controllerContext.getUser(),objectDescribes, cpObjectData, this.getUpdatedFieldMapForLog(cpObjectData),cpObjectData, detailChange);
        this.masterLogId = masterLog.getMasterLogId();
        this.serviceFacade.sendLog(masterLog.getLogList());

        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(CoveredStoresConstants.COVERED_STORES_OBJ, detailDesc);
        this.serviceFacade.log(this.controllerContext.getUser(), EventType.ADD, ActionType.Add, describeMap, dataList, this.masterLogId);

    }

}
