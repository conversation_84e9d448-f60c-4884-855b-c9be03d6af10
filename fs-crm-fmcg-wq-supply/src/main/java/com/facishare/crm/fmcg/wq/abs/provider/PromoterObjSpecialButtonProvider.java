package com.facishare.crm.fmcg.wq.abs.provider;

import com.facishare.crm.fmcg.wq.MCPreDefineObject;
import com.facishare.crm.fmcg.wq.abs.AbstractAreaSpecialButtonProvider;
import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.facishare.crm.fmcg.wq.util.ButtonUtils.buildButton;

@Component
public class PromoterObjSpecialButtonProvider implements SpecialButtonProvider {
    @Override
    public String getApiName() {
        return PromoterFields.API_NAME;
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = new ArrayList<>(5);
        buttons.add(buildButton(ObjectAction.PROMOTER_RESIGNED));
        buttons.add(buildButton(ObjectAction.PROMOTER_IN_POSITION));
        buttons.add(buildButton(ObjectAction.PROMOTER_AGREE));
        buttons.add(buildButton(ObjectAction.PROMOTER_DISAGREE));
        buttons.add(buildButton(ObjectAction.PROMOTER_QRCODE_INVITE));
        return buttons;
    }
}
