package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.predef.action.StandardAsyncBulkChangeOwnerAction;
import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction;

/**
 * <AUTHOR>
 * @create 2022 - 12 - 29  18:42
 **/
public class VisitRouteAsyncBulkChangeOwnerAction extends StandardAsyncBulkChangeOwnerAction {

    @Override
    protected void before(StandardChangeOwnerAction.Arg arg) {
        this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        super.before(arg);
    }
}