package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.MCPreDefineObject;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.model.PlanRouteInfo;
import com.facishare.crm.fmcg.wq.service.AreaService;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class CoveredStoresMapStoreFilterV2Controller extends StandardRelatedListController {

    AreaService areaService = SpringUtil.getContext().getBean(AreaService.class);
    @Override
    protected void before(Arg arg) {
        controllerContext = new ControllerContext(controllerContext.getRequestContext(),"AccountObj",controllerContext.getMethodName());
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        return super.doService(arg);
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> result = new QueryResult();

        int areaDataLimit = ConfigFactory.getConfig("CheckInService").getInt("areaDataLimit_" + controllerContext.getTenantId(),2000);

        List<String> showFieldApiNameList = areaService.buildShowFieldApiName(controllerContext.getTenantId());// 显示的字段
        String showFieldNameOnWater = areaService.getFieldNameOnWaterDrop(controllerContext.getTenantId()); // 水滴上的字段
        Boolean isShowWaterOnList = false; // 水滴上的是否显示在客户移动端列表中
        if(StringUtils.isNotBlank(showFieldNameOnWater)){
            if(showFieldApiNameList.contains(showFieldNameOnWater)) {
                isShowWaterOnList = true;
            }else{
                showFieldApiNameList.add(showFieldNameOnWater);
            }
        }

        query = buildCoveredStoresFilter(controllerContext.getTenantId(),query).getSearchTemplateQuery();
        super.buildSearchTemplateQuery();
        query.setLimit(areaDataLimit);// 最多查两千

        IObjectDescribe describeExt = this.serviceFacade.findObject(controllerContext.getTenantId(),CommonConstants.ACCOUNT_OBJ);
        List<IObjectData> data = ObjectUtils.queryDataWithFieldInfo(serviceFacade,User.systemUser(controllerContext.getTenantId()), controllerContext.getRequestContext(),describeExt,query,showFieldApiNameList);
        serviceFacade.fillUserInfo(describeExt, data,User.systemUser(controllerContext.getTenantId()));

        Map<String, IFieldDescribe> fieldDescribeMap = describeExt.getFieldDescribeMap();
        List<PlanRouteInfo> planData = areaService.convertPlanRouteInfo(data,fieldDescribeMap,showFieldApiNameList);
        List<IObjectData> iObjectDataList = Lists.newArrayList();
        IObjectData objectDataSpecial = new ObjectData();
        Map<String,String> accountValueAndLevelMap = ObjectUtils.getValueAndColorByDesc(fieldDescribeMap.get(ObjectUtils.CUSTOMER_LEVEL));
        objectDataSpecial.set("accountValueAndLevelMap",accountValueAndLevelMap);
        objectDataSpecial.set("isShowWaterOnList",isShowWaterOnList);
        objectDataSpecial.set("showFieldNameOnWater",showFieldNameOnWater);
        objectDataSpecial.set("_id","62ce4114f968446e3e91d4f8");
        iObjectDataList.add(objectDataSpecial);
        planData.forEach(o->{
            IObjectData objectData = new ObjectData();
            objectData.set("_id",o.get_id());
            objectData.set("locationID",o.getLocationID());
            objectData.set("high_seas_id",o.getHigh_seas_id());
            objectData.set("areaId",o.getAreaId());
            objectData.set("areaName",o.getAreaName());
            if(CollectionUtils.isNotEmpty(o.getShowFiledList())){
                o.getShowFiledList().forEach(k->{
                    objectData.set(k.getApiName(),k);
                });
            }
            iObjectDataList.add(objectData);
        });

        result.setData(iObjectDataList);
        return result;
    }
    public SearchQuery buildCoveredStoresFilter(String eid, SearchTemplateQuery query) {
        SearchQuery searchQuery = SearchQuery.builder().build();
        if(CollectionUtils.isNotEmpty(query.getFilters())){
            // 前端filter
            searchQuery.getSearchTemplateQuery().setFilters(query.getFilters());
        }
        // 解析描述 拼接配置的where
        IObjectDescribe describe = serviceFacade.findObject(eid, MCPreDefineObject.CoveredStores.getApiName());
        IFieldDescribe iFieldDescribe = describe.getFieldDescribe(AreaManageConstants.STORE);
        if(Objects.nonNull(iFieldDescribe.get("wheres"))) {
            searchQuery.getSearchTemplateQuery().setWheres((List<Wheres>) iFieldDescribe.get("wheres"));
        }
        SearchQuery.SearchQueryBuilder builder = SearchQuery.builder().copyOf(searchQuery);
        // 内置条件 不展示已分配的片区
        return builder.build();
    }
}
