package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.fxiaoke.common.release.GrayRelease;

public class ProductCollectionBulkInvalidAction extends StandardBulkInvalidAction {
    @Override
    protected void before(Arg arg) {
        //如果是银鹭企业
        if (GrayRelease.isAllow("checkin-server-v2", "isYLProductCollection",actionContext.getEa())) {

            throw new ValidateException("暂不支持批量作废"); //ignoreI18n
        }else{
            super.before(arg);
        }
    }
}
