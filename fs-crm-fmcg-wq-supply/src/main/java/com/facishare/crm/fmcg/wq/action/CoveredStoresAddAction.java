package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("Duplicates")
public class CoveredStoresAddAction  extends FmcgSkipPermissionAddAction{
    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected void before(Arg arg) {
        String storeId =(String) arg.getObjectData().get(CoveredStoresConstants.STORE);
        String areaId= (String) arg.getObjectData().get(CoveredStoresConstants.BELONG_AREA);
        if(StringUtils.isEmpty(storeId) || StringUtils.isEmpty(areaId)){
            throw new ValidateException("片区或门店为空"); //ignoreI18n
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(20);
        query.setOffset(0);

        Filter keywordFilter = new Filter();
        keywordFilter.setFieldName(CoveredStoresConstants.STORE);
        keywordFilter.setOperator(Operator.EQ);
        keywordFilter.setFieldValues(Lists.newArrayList(storeId));
        query.setFilters(Lists.newArrayList(keywordFilter));

        List<IObjectData> data = serviceFacade.findBySearchQuery(actionContext.getUser(), CoveredStoresConstants.COVERED_STORES_OBJ, query).getData();

        if (CollectionUtils.isNotEmpty(data) && StringUtils.isNotEmpty(areaId)) {
            String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
            if(GrayRelease.isAllow("checkin-server-v2", "isYLArea",ea)){
                throw new ValidateException("客户存在片区"); //ignoreI18n
            }else {
                List<String> areaIds = data.stream().map(record -> (String) record.get(CoveredStoresConstants.BELONG_AREA)).distinct().collect(Collectors.toList());
                if (areaIds.contains(areaId)) {
                    throw new ValidateException("客户已在该片区下"); //ignoreI18n
                }
            }
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result){
        super.after(arg,result);
        try {
            // 查询客户数据 更新数据负责人和路线负责人
            String ei = actionContext.getTenantId();
            String ea = serviceFacade.getEAByEI(ei);
            Boolean isYL = GrayRelease.isAllow("checkin-server-v2", "isYLArea", actionContext.getEa());
            if(!GrayRelease.isAllow("checkin-server-v2", "areaManage",ea)){
                String storeId =(String) arg.getObjectData().get(CoveredStoresConstants.STORE);
                String areaId= (String) arg.getObjectData().get(CoveredStoresConstants.BELONG_AREA);
                List<String> accountIdList =Lists.newArrayList(storeId);
                IObjectData areaInfo = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),areaId, AreaManageConstants.AREA_MANAGE_OBJ);
                supplyDao.updateAccountAreaInfo(actionContext.getTenantId(),actionContext.getUser().getUserIdInt(),areaInfo,accountIdList,isYL);
            }
        }catch (Exception e){
            log.error("CoveredStoresAddAction is error",e);
        }
        return result;
    }

    @Override
    protected Result doAct(Arg arg) {
        return super.doAct(arg);
    }
}
