package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.wq.MCPreDefineObject;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;

public class CoveredStoresMapStoreCountController extends StandardRelatedListController {

    @Override
    protected void before(Arg arg) {
        controllerContext = new ControllerContext(controllerContext.getRequestContext(),"AccountObj",controllerContext.getMethodName());
        super.before(arg);
    }
    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        QueryResult<IObjectData> result = new QueryResult();
        int areaDataLimit = ConfigFactory.getConfig("CheckInService").getInt("areaDataLimit_" + controllerContext.getTenantId(),2000);
        query = buildCoveredStoresFilter(controllerContext.getTenantId(),query).getSearchTemplateQuery();
        super.buildSearchTemplateQuery();
        query.setFindExplicitTotalNum(true);
        Boolean isQueryAllocArea = false;
        if(CollectionUtils.isNotEmpty(query.getFilters())){
            Iterator<IFilter> iterator = query.getFilters().iterator();
            while (iterator.hasNext()){
                if(CoveredStoresConstants.BELONG_AREA.equals(iterator.next().getFieldName())){
                    isQueryAllocArea = true;
                    iterator.remove();
                }
            }
        }
        int dataTotal = ObjectUtils.getDataTotal(serviceFacade,controllerContext.getUser(), CommonConstants.ACCOUNT_OBJ,query);
        if(CollectionUtils.isEmpty(query.getFilters())){
            query.setFilters(Lists.newArrayList());
        }
        IFilter iFilter = new Filter();
        iFilter.setFieldName(AreaManageConstants.BELONG_AREA_ACCOUNT);
        iFilter.setFieldValues(Lists.newArrayList(""));
        iFilter.setOperator(Operator.ISN);
        query.getFilters().add(iFilter);
        int allocated = ObjectUtils.getDataTotal(serviceFacade,controllerContext.getUser(), CommonConstants.ACCOUNT_OBJ,query);
        List<IObjectData> iObjectDataList = Lists.newArrayList();
        IObjectData objectData = new ObjectData();
        objectData.set("total",dataTotal);
        objectData.set("_id","62ce4114f968446e3e91d4f8");
        objectData.set("allocated",allocated);
        iObjectDataList.add(objectData);
        result.setData(iObjectDataList);
        if(!isQueryAllocArea && dataTotal - allocated > areaDataLimit){
            throw new ValidateException("数据量过多，请精确筛选范围！"); //ignoreI18n
        }
        if(isQueryAllocArea && dataTotal > areaDataLimit){
            throw new ValidateException("数据量过多，请精确筛选范围！"); //ignoreI18n
        }
        return result;
    }

    @Override
    protected boolean needButtonInfo() {
        return false;
    }

    @Override
    protected boolean needTagInfo() {
        return false;
    }

    protected boolean needSummaryData(SearchTemplateQuery query) {
        return false;
    }

    @Override
    protected boolean needLayout(ISearchTemplateQuery query) {
        return false;
    }
    public SearchQuery buildCoveredStoresFilter(String eid, SearchTemplateQuery query) {
        SearchQuery searchQuery = SearchQuery.builder().build();
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(query.getFilters())){
            // 前端filter
            searchQuery.getSearchTemplateQuery().setFilters(query.getFilters());
        }
        // 解析描述 拼接配置的where
        IObjectDescribe describe = serviceFacade.findObject(eid, MCPreDefineObject.CoveredStores.getApiName());
        IFieldDescribe iFieldDescribe = describe.getFieldDescribe(AreaManageConstants.STORE);
        if(Objects.nonNull(iFieldDescribe.get("wheres"))) {
            searchQuery.getSearchTemplateQuery().setWheres((List<Wheres>) iFieldDescribe.get("wheres"));
        }
        SearchQuery.SearchQueryBuilder builder = SearchQuery.builder().copyOf(searchQuery);
        // 内置条件 不展示已分配的片区
        return builder.build();
    }
}
