package com.facishare.crm.fmcg.wq.action;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.idempotent.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterAddAction extends FmcgSkipPermissionAddAction {

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    @Override
    protected void before(Arg arg) {
        if (arg.getObjectData().get(PromoterFields.IO_STATUS) == null) {
            arg.getObjectData().put(PromoterFields.IO_STATUS, "2");
        }
        if (arg.getObjectData().get(PromoterFields.IN_DATE) == null) {
            arg.getObjectData().put(PromoterFields.IN_DATE, System.currentTimeMillis());
        }

        // 参数转换：如果REVIEW_STATUS是boolean类型，转换成字符串
        Object reviewStatus = arg.getObjectData().get(PromoterFields.REVIEW_STATUS);
        if (reviewStatus instanceof Boolean) {
            Boolean boolValue = (Boolean) reviewStatus;
            String stringValue = boolValue ? "true" : "false";
            arg.getObjectData().put(PromoterFields.REVIEW_STATUS, stringValue);
        }

        super.before(arg);

    }

    @Override
    protected Result doAct(Arg arg) {
        IdempotentService idempotentService = serviceFacade.getBean(IdempotentService.class);
        IdempotentStore idempotentStore = serviceFacade.getBean(IdempotentStore.class);
        if (idempotentService != null) {
            String key = actionContext.getTenantId() + actionContext.getActionCode() + arg.getObjectData().get("mobile") + arg.getObjectData().get("enterprise_relation_id");
            Result result = idempotentService.doWithIdempotent(key, () -> getResult(arg), IdempotentParam.builder().build(),
                    new MetaDataBusinessException(I18N.text(I18NKey.DO_NOT_SUBMIT_REPEATEDLY)));
            idempotentStore.delete(key);
            return result;
        }
        return getResult(arg);
    }

    private Result getResult(Arg arg) {
        //验证 互联用户是否重复， 手机号和企业账号重复
        {
            SearchQuery build = SearchQuery.builder()
                    .eq("mobile", arg.getObjectData().get("mobile"))
                    .eq("enterprise_relation_id", arg.getObjectData().get("enterprise_relation_id"))
                    .build();
            int promoterObjTotal = supplyDao.getTotal(User.systemUser(actionContext.getTenantId()), build, "PromoterObj");
            if (promoterObjTotal > 0) {
                throw new ValidateException("同互联企业手机号码重复"); //ignoreI18n
            }
        }
        //增加判断，如果 有互联用户则不做名称判重
        if (arg.getObjectData().get(PromoterFields.PUBLIC_EMPLOYEE_ID) == null || "".equals((String) (arg.getObjectData().get(PromoterFields.PUBLIC_EMPLOYEE_ID)))) {
            SearchQuery build = SearchQuery.builder()
                    .eq("name", arg.getObjectData().get("name"))
                    .eq("enterprise_relation_id", arg.getObjectData().get("enterprise_relation_id"))
                    .build();
            int promoterObjTotal = supplyDao.getTotal(User.systemUser(actionContext.getTenantId()), build, "PromoterObj");
            if (promoterObjTotal > 0) {
                throw new ValidateException("同互联企业互联用户名称重复"); //ignoreI18n
            }
        }
        return super.doAct(arg);
    }
}
