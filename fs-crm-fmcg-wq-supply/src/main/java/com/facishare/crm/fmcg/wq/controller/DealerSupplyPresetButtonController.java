package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 预置自定义按钮的逻辑
 * @author: zhangsm
 * @create: 2023-09-23 15:23
 **/
public class DealerSupplyPresetButtonController extends PreDefineController<DealerSupplyPresetButtonController.Arg, DealerSupplyPresetButtonController.Result> {
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);




    @Override
    protected Result doService(Arg arg) {
        supplyService.presetCustomButton(controllerContext.getTenantId());
        return new Result();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    public static class Arg {
    }

    public static class Result {
    }
}
