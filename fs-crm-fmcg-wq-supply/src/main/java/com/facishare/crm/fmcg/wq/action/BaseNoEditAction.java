package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;

/**
 * @program: fs-crm-fmcg
 * @description: 禁止编辑逻辑
 * @author: zhangsm
 * @create: 2021-05-13 10:07
 **/
public class BaseNoEditAction extends StandardEditAction {
    @Override
    protected void before(Arg arg) {
        throw new ValidateException("暂不支持编辑,仅支持新建,删除"); //ignoreI18n
    }
}
