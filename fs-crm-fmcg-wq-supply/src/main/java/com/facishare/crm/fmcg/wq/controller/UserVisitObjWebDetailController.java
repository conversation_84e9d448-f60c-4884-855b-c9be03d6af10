package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.util.DurationUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;

import java.util.Objects;

public class UserVisitObjWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        if(Objects.nonNull(result.getData().get("working_hours__c"))){
            result.getData().put("working_hours__c", DurationUtils.getDurationSecond(Long.valueOf(result.getData().get("working_hours__c").toString())));
        }
        if(Objects.nonNull(result.getData().get("visit_hours__c"))){
            result.getData().put("visit_hours__c", DurationUtils.getDurationSecond(Long.valueOf(result.getData().get("visit_hours__c").toString())));
        }
        if(Objects.nonNull(result.getData().get("working_hours"))){
            result.getData().put("working_hours", DurationUtils.getDurationSecond(Long.valueOf(result.getData().get("working_hours").toString())));
        }
        if(Objects.nonNull(result.getData().get("visit_hours"))){
            result.getData().put("visit_hours", DurationUtils.getDurationSecond(Long.valueOf(result.getData().get("visit_hours").toString())));
        }
        result = super.after(arg, result);
        return result;
    }

}
