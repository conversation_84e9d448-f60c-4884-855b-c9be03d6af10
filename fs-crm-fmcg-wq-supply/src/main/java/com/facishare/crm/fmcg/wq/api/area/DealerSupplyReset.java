package com.facishare.crm.fmcg.wq.api.area;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.appserver.checkins.api.model.BaseResult;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * @program: fs-crm-fmcg
 * @description:
 * @author: zhangsm
 * @create: 2021-06-11 14:42
 **/
public interface DealerSupplyReset {
    @Data
    @ToString
    class Arg implements Serializable {
        @JSONField(name = "account_id")
        @JsonProperty("account_id")
        @SerializedName("account_id")
        private String accountId;
    }
    @Data
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {

    }
}