package com.facishare.crm.fmcg.wq.controller;


import com.facishare.crm.fmcg.wq.api.sync.AvailableRange;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.service.SyncAvailableRangeService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;

public class DealerSupplySyncAvailableRangeController extends PreDefineController<AvailableRange.Arg, AvailableRange.Result> {
    SyncAvailableRangeService syncAvailableRangeService = SpringUtil.getContext().getBean(SyncAvailableRangeService.class);

    BaseDao baseDao =  SpringUtil.getContext().getBean(BaseDao.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected AvailableRange.Result doService(AvailableRange.Arg arg) {
        AvailableRange.Result result=new AvailableRange.Result();
        if (arg.isAsync()){
            syncAvailableRangeService.addAvailableObjSyncTask(controllerContext.getTenantId(),arg.getSupplyId());
        }else{
            IObjectData iObjectData= baseDao.getById(controllerContext.getTenantId(), DealerSupplyObjConstants.API_NAME,arg.getSupplyId());
            syncAvailableRangeService.syncAvailableObjByDealerSupplyObj(controllerContext.getTenantId(),iObjectData,arg.getSubAccountId());

        }

        return result;
    }

}
