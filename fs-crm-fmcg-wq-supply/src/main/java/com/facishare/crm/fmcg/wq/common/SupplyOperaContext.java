package com.facishare.crm.fmcg.wq.common;

import com.facishare.appserver.utils.FcpUtils;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 供货关系审核上下文
 * @author: zhangsm
 * @create: 2023-11-30 14:39
 **/
@Data
@Slf4j
public class SupplyOperaContext {
    public static final String TRIGGERAPPROMESSAGE = "该操作触发审批流程成功，请等待审批完成后再进行操作"; //ignoreI18n
    public static final int TRIGGERAPPROCODE = 20231130;
//    private Map<Object, Object> attributes;
//
//    private SupplyOperaContext() {
//        this.attributes = Maps.newHashMap();
//    }
//
//
//    public Object getAttribute(Object key) {
//        return this.attributes.get(key);
//    }
//
//    //生成根据key获取元素并转换成对应的泛型类型的方法
//    public <T> T getAttribute(Object key, Class<T> t) {
//        Object o = this.attributes.get(key);
//        return o != null ? (T) o : null;
//    }
//
//    public void setAttribute(Object key, Object value) {
//        this.attributes.put(key, value);
//    }

    public static SupplyOperaContext get() {
        SupplyOperaContext c = new SupplyOperaContext();
        return c;
    }

    private String inUpSupplyId;
    private String outUpSupplyId;
    private String inUpCustomerId;
    private String outUpCustomerId;
    private List<String> custermerIds;
    private List<String> productIds;

    private Object args;
    private Class resultClazz;
}
