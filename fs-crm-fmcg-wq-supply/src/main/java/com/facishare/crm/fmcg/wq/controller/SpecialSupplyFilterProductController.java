package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.constants.SpecialSupplyObjConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.ProductFilterService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class SpecialSupplyFilterProductController extends StandardRelatedListController {

    @Override
    protected void before(Arg arg) {
        controllerContext = new ControllerContext(controllerContext.getRequestContext(),"ProductObj",controllerContext.getMethodName());
        super.before(arg);
    }
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    ProductFilterService productFilterService = SpringUtil.getContext().getBean(ProductFilterService.class);
    SupplyDao supplyDao =  SpringUtil.getContext().getBean(SupplyDao.class);
    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        //配送商类型
        String dsId= arg.getObjectData().get("_id").toString();//
        List<IObjectData> dsData = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), Lists.newArrayList(dsId), "DealerSupplyObj");
//        String supplyType = dsData.get(0).get("supply_type").toString();
        //配送商或者经销商ID
        String dealerId = dsData.get(0).get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();
        List<IObjectData> customInfo = serviceFacade.findObjectDataByIds(controllerContext.getUser().getTenantId(), Lists.newArrayList(dealerId), "AccountObj");
        String recordType=(String)customInfo.get(0).get(BaseField.recordType.getApiName());
        //经销商或者配送商的经营范围
        int isDis = recordType.equals("distributor__c") ? 1 : 0;
        Set<String> dealerProductIds = Sets.newHashSet();
        if (isDis == 1){
            dealerProductIds= supplyService.getProductListByDistributionId(controllerContext.getTenantId(),dealerId);
        }else{

          dealerProductIds= supplyService.getProductCollectionByDealerId(controllerContext.getTenantId(),dealerId, isDis);
        }
//        //分页查询产品
//        QueryResult<IObjectData> data = new QueryResult<>();
//        int endIndex=dealerProductIds.size();
//        if(dealerProductIds.size()>query.getLimit()){
//            endIndex=query.getOffset()+query.getLimit();
//        }
//        List<String> subList=Lists.newArrayList(dealerProductIds).subList(query.getOffset(),endIndex);
//        data.setData(serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), subList,"ProductObj"));
//        return data;
        if(CollectionUtils.isEmpty(dealerProductIds)) {
            return new QueryResult<>();
        }
        JSONObject searchJson = JSON.parseObject(arg.getSearchQueryInfo());
        Integer fromSpecial = searchJson.getInteger("fromSpecial");
        JSONArray idArray = searchJson.getJSONArray("selectIds");
        if ( idArray != null && idArray.size() ==1) {
            //去除已经存在的特例产品
            List<IObjectData> specialSupplyProductsByUpAndThis = supplyDao.getSpecialSupplyProductsByUpAndThis(controllerContext.getTenantId(), dealerId, idArray.getString(0));
            if (CollectionUtils.isNotEmpty(specialSupplyProductsByUpAndThis)) {
                dealerProductIds.removeAll(specialSupplyProductsByUpAndThis.stream().map(o -> o.get(SpecialSupplyObjConstants.Field.productId.getApiName(), String.class)).collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(dealerProductIds)) {
                    return new QueryResult<>();
                }
            }
        }
        Filter productFilter = new Filter();
        productFilter.setFieldName("_id");
        productFilter.setOperator(Operator.IN);
        productFilter.setFieldValues(Lists.newArrayList(dealerProductIds));
        query.getFilters().addAll(SearchQuery.convertFilter(arg.getSearchQueryInfo()));
        query.getFilters().add(productFilter);
        query.getFilters().removeIf(o->o.getFieldName().equals("object_describe_api_name"));
//        log.info("findBySearchQuery test- query {}",query.toJsonString());

        //处理产品分类问题
        productFilterService.handleCategoryFilters(controllerContext.getTenantId(),controllerContext.getUser().getUserId(),query.getFilters());
        return serviceFacade.findBySearchQuery(controllerContext.getUser(), "ProductObj", query);


    }


//    @Resource(name = "httpSupport")
//    private OkHttpSupport client;
//    public static MediaType media_type_json = MediaType.parse("application/json; charset=utf-8");
//    private  QueryResult getProductList(SearchTemplateQuery query){
//
//
//        try {
//            Map<String, Object> map = new HashMap<>();
//            String url ="";
//            Request request = new Request.Builder()
//                    .url(url)
//                    .header("x-fs-employee-id", controllerContext.getUser().getUserId())
//                    .header("x-fs-userinfo", controllerContext.getUser().getUserId())
//                    .header("x-fs-enterprise-id", controllerContext.getUser().getTenantId())
//                    .header("x-fs-ei", controllerContext.getUser().getTenantId())
//                    .post(RequestBody.create(media_type_json, JSON.toJSON(query).toString()))
//                    .build();
//
//            String responseString = (String) client.syncExecute(request, new SyncCallback() {
//                @Override
//                public String response(Response response) throws IOException {
//                    return Objects.requireNonNull(response.body()).string();
//                }
//            });
//
//            JSONArray jsonArray = JSON.parseArray(responseString);
//            if (CollectionUtils.isNotEmpty(jsonArray)) {
//            }
//        }catch (Exception e){
//
//        }
//
//        QueryResult queryResult = new QueryResult();
//        queryResult.setData(objectDataList);
//    }

}
