package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.supply.SupplyApproArgs;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.constants.DistributorSupplyObjConstants;
import com.facishare.crm.fmcg.wq.constants.SupplyChangeFields;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 供货关系转移接口类
 * @author: zhangsm
 * @create: 2023-09-18 10:31
 **/
public class DealerSupplyTransferController extends PreDefineController<DealerSupplyTransferController.Arg, DealerSupplyTransferController.Result> {
    private SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    private SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor = SpringUtil.getContext().getBean("fmcgThreadPoolExecutor", AsyncTaskExecutor.class);
    RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected Result doService(Arg arg) {
        if (redisUtils.checkTransfer(controllerContext.getTenantId(), arg.getObjectData().getId(), arg.getTransferType())) {
            throw new ValidateException("供货关系转移中，请稍后再试"); //ignoreI18n
        }
        List<IObjectData> transferObjectList = null;
        if (arg.getTransferType() == 0) {
            //查询需要转移的供货门店
            transferObjectList = supplyDao.getSupplyStoreBySupplyId(controllerContext.getTenantId(), arg.getObjectData().getId(), arg.getIsAll(), arg.getCustomerIds());
        } else {
            //转移配送商
            transferObjectList = supplyDao.getSupplyDistributionBySupplyId(controllerContext.getTenantId(), arg.getObjectData().getId(), arg.getIsAll(), arg.getCustomerIds());
        }
        if (CollectionUtils.isNotEmpty(arg.getCustomerIds()) && arg.isAll != 1 && transferObjectList.size() < arg.getCustomerIds().size()) {
            //聚合 transferObjectList 所有的 thisDealerId
            Set<String> thisDealerIds = transferObjectList.stream().map(o -> o.get(arg.getTransferType() == 1? DistributorSupplyObjConstants.Field.thisDealerId.getApiName() : DistributorSupplyObjConstants.Field.thisDealerId.getApiName(),String.class)).collect(Collectors.toSet());
            Set<String> delCustomerIds = arg.getCustomerIds().stream().filter(id -> !thisDealerIds.contains(id)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(delCustomerIds)) {
                //客户 xxx，xxx，的供货门店/供货分销 数据已经被修改，请刷新后重试
                throw new ValidateException(String.format("客户%s的【%s】数据已经被修改，请刷新后重试", supplyDao.getAccountObjByIds(controllerContext.getTenantId(), delCustomerIds).stream().map(o -> o.getName()).collect(Collectors.joining(",")), arg.getTransferType() == 1? "供货分销" : "供货门店")); //ignoreI18n
            }
        }
        //check supply
        IObjectData objectData = arg.getObjectData().toObjectData();
        Set<String> errorIds = supplyService.checkTransferDealerSupply(controllerContext.getTenantId(), objectData, arg.getTargetCustomerId(), transferObjectList);
        if (errorIds.size() > 0) {
            throw new ValidateException(String.format("供货关系转移失败，数据%s存在冲突", supplyDao.getAccountObjByIds(controllerContext.getTenantId(), errorIds).stream().map(o -> o.getName()).collect(Collectors.joining(",")))); //ignoreI18n
        }
        supplyOperaContext.setArgs(arg);
        supplyOperaContext.setResultClazz(Result.class);
        supplyOperaContext.setOutUpCustomerId(objectData.get(DealerSupplyObjConstants.Field.dealerId.getApiName(), String.class));
        supplyOperaContext.setOutUpSupplyId(objectData.getId());
        supplyOperaContext.setInUpCustomerId(arg.getTargetCustomerId());
        //查目标个供货关系
        IObjectData targetObjectData = supplyDao.getDealerSupplyBySuppliersNOPreCreate(controllerContext.getTenantId(), Lists.newArrayList(arg.getTargetCustomerId())).get(0);
        supplyOperaContext.setInUpSupplyId(targetObjectData.getId());
        supplyOperaContext.setCustermerIds(arg.getCustomerIds());
        //审批
        Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(controllerContext, supplyOperaContext);
        if (booleanIObjectDataPair.first) {
            throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE, SupplyOperaContext.TRIGGERAPPROCODE);
        }else {
            //触发审批成功，设置redis
            List<IObjectData> finalTransferObjectList = transferObjectList;
            fmcgThreadPoolExecutor.execute(() -> {
                List<Pair<IObjectData, List<String>>> pairs = supplyService.transferDealerSupply(controllerContext.getUser(), objectData, arg.getTargetCustomerId(), finalTransferObjectList, arg.getTransferType(), errorIds);
                //todo 供货关系一键转移审批成功后，填充供货关系变更门店/分销商详情 和 供货关系变更产品详情
                IObjectData approMainObj = booleanIObjectDataPair.second;
                if (approMainObj == null){
                    approMainObj = supplyapproService.getSupplyChangeByContext(controllerContext.getTenantId(),controllerContext.getRequestContext());
                }
                if (approMainObj != null){
                    if (arg.isAll == 1){
                        supplyDao.fillSupplyCustomerApproObjDetail(controllerContext.getTenantId(), approMainObj,pairs.stream().map(o -> o.first.get(arg.getTransferType() == 1? DistributorSupplyObjConstants.Field.thisDealerId.getApiName() : DistributorSupplyObjConstants.Field.thisDealerId.getApiName(),String.class)).distinct().collect(Collectors.toList()));
                    }
                    supplyDao.fillSupplyProductApproObjDetail(controllerContext.getTenantId(), approMainObj,pairs.stream().filter(o->o.second != null && !o.second.contains("ALL")).collect(Collectors.toList()),arg.getTransferType());
                }
            });
        }

        return new Result();
    }

    @Data
    public static class Arg  extends SupplyApproArgs {
        /**
         * 供货关系数据
         */
        @JsonProperty("object_data")
        private ObjectDataDocument objectData;

        /**
         * 转移类型 0:转移供货门店 1:转移配送商
         */
        @JsonProperty("transfer_type")
        private int transferType;


        /**
         * 是否全量转移 1 是全量
         */
        @JsonProperty("is_all")
        private int isAll;
        /**
         * 转移的客户ids 从供货门店/供货分销里解析出来 id
         */
        @JsonProperty("customer_ids")
        private List<String> customerIds;

        /**
         * 转移的经销商id
         */
        @JsonProperty("target_customer_id")
        private String targetCustomerId;

    }

    @Data
    public static class Result {

    }
}
