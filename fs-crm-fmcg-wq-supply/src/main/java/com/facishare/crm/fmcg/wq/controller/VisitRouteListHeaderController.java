package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.crm.fmcg.wq.util.CheckinsGrayUtils;
import com.facishare.feeds.common.utils.I18nClientUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class VisitRouteListHeaderController extends StandardListHeaderController {
    CheckinsDao checkinsDao = SpringUtil.getContext().getBean(CheckinsDao.class);

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        Integer checkinsPermission = checkinsDao.getRouteUpdatePermission(controllerContext.getTenantId(),
                Integer.valueOf(controllerContext.getUser().getUserId()), null);
        List<String> buttonKeys = Lists.newArrayList("Lock_button_default", "Unlock_button_default");
        String ea = serviceFacade.getEAByEI(controllerContext.getTenantId());
        if (CollectionUtils.isNotEmpty(result.getButtons())) {
            if (CheckinsGrayUtils.yqGray(ea)) {
                result.setButtons(result.getButtons().stream().filter(o -> buttonKeys.contains((String) o.get("api_name"))).collect(Collectors.toList()));
            } else {
                result.setButtons(Lists.newArrayList());
            }
        }
        log.info("VisitRouteListHeaderController getButtons().size():{}", result.getButtons().size());
        ArrayList<Map> newButtons = Lists.newArrayList();
        Map<String, Boolean> stringBooleanMap = serviceFacade.funPrivilegeCheck(controllerContext.getUser(), arg.getApiName(), Lists.newArrayList(ObjectAction.BATCH_EXPORT.getActionCode()
                ,ObjectAction.CREATE.getActionCode()));
        if (    //有外勤权限
                checkinsPermission == 1 ||
                        //有paas权限
                        (checkinsPermission == 0 && stringBooleanMap.getOrDefault(ObjectAction.CREATE.getActionCode(),Boolean.FALSE))) {

            Map map = Maps.newHashMap();
            map.put("action", "Add");
            map.put("action_type", "default");
            map.put("api_name", "Add_button_default");
            map.put("label", I18nClientUtils.get("fs.wq.action.create","新建", TraceContext.get().getLocale())); //ignoreI18n
            newButtons.add(map);
        }
        //暂时不权限控制 因为之前也没控制
            Map map1 = Maps.newHashMap();
            map1.put("action", "Export");
            map1.put("action_type", "default");
            map1.put("api_name", "Export_button_default");
            map1.put("label", I18nClientUtils.get("fs.wq.action.export","导出", TraceContext.get().getLocale())); //ignoreI18n
            newButtons.add(map1);
        result.getLayout().put("buttons", newButtons);
        log.info("VisitRouteListHeaderController getLayout().size():{}", result.getLayout().size());
        return result;
    }
}
