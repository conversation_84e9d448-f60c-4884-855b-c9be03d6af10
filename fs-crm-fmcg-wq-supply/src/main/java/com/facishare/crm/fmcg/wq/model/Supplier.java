package com.facishare.crm.fmcg.wq.model;

import com.google.common.collect.Lists;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: fs-appserver-checkins-v2
 * @description: 经销商
 * @author: zhangsm
 * @create: 2021-04-20 19:20
 **/

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Supplier  {
    /**
     * 分销商级别 0是服务处 1 是经销商 >=2 是二级分销商
     * 99 未知的 ，反正不是服务处。。。
     */
    @Setter
    private int level;
    /**
     * 范围id
     */
    @Setter
    private String businessScopeId;


//    /**
//     *  是否初始化了 父供货商
//     */
//    private boolean initParent;
    /**
     * 上级供应商
     */
    private List<Supplier> parentSuppliers;
//    /**
//     * 是否初始化了 经营产品列表
//     */
//    private boolean initProduct;
    /**
     * 当前经营范围
     */
    private List<Product> productList;
    /**
     * 部门ids 非必填
     */
//    protected List<Integer> departmentIds;

//    protected String parentId;
    /**
     * 是否是所有的范围
     */
    private boolean isAllRange;
    /**
     * level 0 的 id  服务处 的部门id
     */
    private String departmentId;


    /**
     * 下级的id
     */
//    private String sonId;
    /**
     *  customId,
     */
    private String id;

    public boolean isParentAllRange() {
        return this.isAllRange;
    }

    /**
     * 查询上一级的最大范围
     * @return
     */
    public List<Product> getParentProductRange() {
        ArrayList<Product> products = Lists.newArrayList();
        if (level == 0 && isParentAllRange()) {
            products.add(Product.builder()
                    .productId("ALL")
                    .build());
        } else if (CollectionUtils.isNotEmpty((this.parentSuppliers))) {
            this.parentSuppliers.forEach(
                    o -> {
                        List<Product> productRange = o.getProductList();
                        if (CollectionUtils.isNotEmpty(productRange)) {
                            products.addAll(productRange);
                        }
                    }
            );
        }
        return products;
    }

    /**
     * 获取本级别的范围
     * @return
     */
    public List<Product> getProductList() {
        if (this.productList == null){
            return ListUtils.EMPTY_LIST;
        }
        return this.productList;
    }

    public List<Product> resetProductList() {
        if (isParentAllRange()) {
            if (this.level != 0) {
                List<Product> parentProductRange = getParentProductRange();
                List<Product> productList = parentProductRange.stream().map(o -> {
                    if (!o.getSupplierId().equals(this.getId())) {
                        return Product.builder().supplierId(this.getId())
                                .productId(o.getProductId())
                                .productName(o.getProductName())
                                .build();
                    }
                    return o;
                }).collect(Collectors.toList());
                this.productList = productList;
            } else {
                ArrayList<Product> all = Lists.newArrayList();
                Product allProduct = Product.builder()
                        .productId("ALL")
                        .productName("所有产品") //ignoreI18n
                        .supplierId(this.getId())
                        .build();
                all.add(allProduct);
                this.productList = all;
            }
        }
        return this.productList;
    }




//    @Override
//    public void removeProductRange(List<ProductObj> removeProductObjList) {
//        if (isParentAllRange()){
//            throw new BizException("增加产品范围，需要增加父节点范围",-1,null);
//        }
//        Set<String> removeProductIds = removeProductObjList.stream().map(ProductObj::getProductId).collect(Collectors.toSet());
//        this.getProductRange().removeIf(productObj -> removeProductIds.contains(productObj.getProductId()));
//    }
//
//    @Override
//    public void addProductRange(List<ProductObj> addProductObjList) {
//        if (isParentAllRange()){
//            throw new BizException("增加产品范围，需要增加父节点范围",-1,null);
//        }
//        this.getProductRange().addAll(addProductObjList);
//    }
//
//    @Override
//    public Set<ProductObj> getIntersectionProducts(BusinessScopeService other) {
//        Set<String> otherProductIds = other.getProductRange().stream().map(obj -> obj.getProductId()).collect(Collectors.toSet());
//        return this.getProductRange().stream().filter(o -> otherProductIds.contains(o.getProductId())).collect(Collectors.toSet());
//    }
//
//    @Override
//    public Set<ProductObj> getUnionProducts(BusinessScopeService other) {
//        Set<ProductObj> newSet = new HashSet<>();
//        newSet.addAll(this.getProductRange());
//        newSet.addAll(other.getProductRange());
//        newSet = newSet.stream().map(o->{
//            ProductObj productObj = new ProductObj();
//            productObj.setProductId(o.getProductId());
//            productObj.setProductName(o.getProductName());
//            return productObj;
//        }).collect(Collectors.toSet());
//        return newSet;
//    }
   public boolean containtProduct(String productId){
       if(CollectionUtils.isEmpty(this.getProductList())){
           return false;
       }
       for (Product product : this.productList) {
           if ("ALL".equals(product.getProductId()) || productId.equals(product.getProductId())){
               return true;
           }
       }
       return false;
    }
}
