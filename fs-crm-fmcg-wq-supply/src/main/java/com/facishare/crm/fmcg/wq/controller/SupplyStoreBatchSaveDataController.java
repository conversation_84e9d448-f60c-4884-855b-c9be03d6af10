package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.supply.SupplyStoreList;
import com.facishare.crm.fmcg.wq.common.SupplyOperaContext;
import com.facishare.crm.fmcg.wq.model.BatchSaveDistributorOrShopSupplyArgs;
import com.facishare.crm.fmcg.wq.service.SupplyApproService;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Lists;
import org.apache.commons.collections.ListUtils;

import java.util.List;

/**
 * 批量添加供货门店
 */
public class SupplyStoreBatchSaveDataController extends PreDefineController<SupplyStoreList.Arg, SupplyStoreList.Result> {


    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SupplyOperaContext supplyOperaContext = SupplyOperaContext.get();
    SupplyApproService supplyapproService = SpringUtil.getContext().getBean(SupplyApproService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected void before(SupplyStoreList.Arg arg) {
        super.before(arg);
    }

    @Override
    protected SupplyStoreList.Result doService(SupplyStoreList.Arg arg) {
        SupplyStoreList.Result result=new SupplyStoreList.Result();
        BatchSaveDistributorOrShopSupplyArgs batchSaveDistributorOrShopSupplyArgs = supplyService.checkBatchSaveDistributorOrShopSupply(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), arg.getDealerSupplyId(), arg.getStoreIds(), 0, null);
        if (CollectionUtils.notEmpty(batchSaveDistributorOrShopSupplyArgs.getAddShopIds())) {
            //审核
            supplyOperaContext.setArgs(arg);
            supplyOperaContext.setResultClazz(SupplyStoreList.Result.class);
            supplyOperaContext.setInUpCustomerId(batchSaveDistributorOrShopSupplyArgs.getDealerId());
            supplyOperaContext.setInUpSupplyId(batchSaveDistributorOrShopSupplyArgs.getUpSupplyId());
            supplyOperaContext.setCustermerIds(batchSaveDistributorOrShopSupplyArgs.getAddShopIds());
            supplyOperaContext.setProductIds(batchSaveDistributorOrShopSupplyArgs.getAddSpecialProductIds());
            Pair<Boolean, IObjectData> booleanIObjectDataPair = supplyapproService.triggerSupplyAppro(controllerContext, supplyOperaContext);
            if (booleanIObjectDataPair.first) {
                result.setApprovalTriggered(true);
//                throw new ValidateException(SupplyOperaContext.TRIGGERAPPROMESSAGE, SupplyOperaContext.TRIGGERAPPROCODE);
            }else{
                supplyService.batchSaveDistributorOrShopSupply(batchSaveDistributorOrShopSupplyArgs);
            }
        }
        result.setStoreIds(Lists.newArrayList(batchSaveDistributorOrShopSupplyArgs.getResultError().getConflictMap().keySet()));
        return result;
//
//        SupplyStoreList.Result result = new SupplyStoreList.Result();
//
//
//        //已经添加过的门店不允许添加
//        List<IObjectData> supplyStoreList = supplyService.getRelatedSupplyStore(controllerContext.getTenantId(), com.beust.jcommander.internal.Lists.newArrayList(arg.getDealerSupplyId()));
//        List<String> accountIds = Lists.newArrayList();
//        if (CollectionUtils.isNotEmpty(supplyStoreList)) {
//            supplyStoreList.forEach(o -> accountIds.add(o.get("store").toString()));
//        }
//        accountIds.retainAll(arg.getStoreIds());
//        if (CollectionUtils.isNotEmpty(accountIds)) {
//            StringBuffer str = new StringBuffer();
//            List<IObjectData> storeDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), accountIds, "AccountObj");
//            Map<String, String> existStoreMaps = storeDataList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (key1, key2) -> key2));
//            for (Map.Entry<String, String> m : existStoreMaps.entrySet()) {
//                str.append(m.getValue()).append(",");
//            }
//            throw new ValidateException(str + "门店已在供货列表，请不要重复添加");
//        }
//
//        //查询所有门店名称
//        List<IObjectData> storeDataList = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), arg.getStoreIds(), "AccountObj");
//        Map<String, String> storeMaps = storeDataList.stream().collect(Collectors.toMap(IObjectData::getId, IObjectData::getName, (key1, key2) -> key2));
//
//        //查询供货关系
//        List<IObjectData> dealerSupplyData = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(arg.getDealerSupplyId()), DealerSupplyObjConstants.API_NAME);
//        String dealerId = dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();
//        String supplyType = dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.supplyType.getApiName()).toString();
//        //经销商或者配送商的经营范围
//        Set<String> dealerProductIds = supplyService.getProductCollectionByDealerId(controllerContext.getTenantId(), dealerId, supplyType.equals("distributor__c") ? 1 : 0);
//
//        Map<String, String> conflictStoreMap = new HashMap<>();
//        List<String> addShopIds = Lists.newArrayList();
//
//        if (GrayRelease.isAllow("checkin-server-v2", "batchSaveData", controllerContext.getTenantId())) {
//            arg.getStoreIds().parallelStream().forEach(shopId -> {
//                //门店的可售商品
//                Set<String> productIds = supplyService.getProductListByStoreId(controllerContext.getTenantId(), shopId);
//                productIds.retainAll(dealerProductIds);
//                if (CollectionUtils.isNotEmpty(productIds)) {
//                    conflictStoreMap.put(shopId, storeMaps.get(shopId));
//                } else {
//                    addShopIds.add(shopId);
//                }
//            });
//        } else {
//            for (String shopId : arg.getStoreIds()) {
//                //门店的可售商品
//                Set<String> productIds = supplyService.getProductListByStoreId(controllerContext.getTenantId(), shopId);
//                productIds.retainAll(dealerProductIds);
//                if (CollectionUtils.isNotEmpty(productIds)) {
//                    conflictStoreMap.put(shopId, storeMaps.get(shopId));
//                } else {
//                    addShopIds.add(shopId);
//                }
//            }
//        }
//        if (CollectionUtils.isNotEmpty(addShopIds)) {
//            supplyDao.createSupplyStoreObj(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), addShopIds, dealerSupplyData.get(0), false);
//            for (String shopId : addShopIds) {
//                supplyDao.updateAccountSupply(controllerContext.getTenantId(), shopId);
//            }
//        }
//        int size = arg.getStoreIds().size() - conflictStoreMap.size();
//        StringBuffer failStr = new StringBuffer();
//        for (Map.Entry<String, String> m : conflictStoreMap.entrySet()) {
//            failStr.append(m.getValue()).append("\n");
//        }
////        if(MapUtils.isNotEmpty(conflictStoreMap)){
////           throw new ValidateException("已成功添加"+size+"家门店！\n"+"有以下"+conflictStoreMap.size()+"家门店存在多配送上经营范围重复，添加失败！可到配送商下指定【特例供货产品】" +
////                   "\n"+failStr.toString());
////        }
//        if (CollectionUtils.isNotEmpty(conflictStoreMap.keySet())) {
//            result.setStoreIds(Lists.newArrayList(conflictStoreMap.keySet()));//返回ID
//        }
//
//        //同步可售范围
//        supplyService.addAvailableObjSyncTask(controllerContext.getTenantId(), arg.getDealerSupplyId());
//        return result;
    }

    /**
     * 优化2.0
     * @param arg
     * @return
     */
//    SupplyStoreList.Result batchSaveDataV2(SupplyStoreList.Arg arg) {
//        SupplyStoreList.Result result = new SupplyStoreList.Result();
//        result.setStoreIds(Lists.newArrayList());
//        if (CollectionUtils.isEmpty(arg.getStoreIds())) {
//            return result;
//        }
//        List<IObjectData> storeList = supplyDao.parallelGetDatasByQueryWithFields(controllerContext.getTenantId(), SearchQuery.builder()
//                .eq(SupplyStoreObjConstants.Field.upSupplyId.getApiName(), arg.getDealerSupplyId()).in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), arg.getStoreIds()).build(), SupplyStoreObjConstants.API_NAME, Lists.newArrayList(SupplyStoreObjConstants.Field.thisDealerId.getApiName()));
//        Set<String> existShopIds = storeList.stream().map(o -> o.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class)).collect(Collectors.toSet());
//        arg.getStoreIds().removeIf(o -> existShopIds.contains(o));
//        if (CollectionUtils.isEmpty(arg.getStoreIds())) {
//            return result;
//        }
//        //查询供货关系
//        List<IObjectData> dealerSupplyData = serviceFacade.findObjectDataByIds(controllerContext.getTenantId(), Lists.newArrayList(arg.getDealerSupplyId()), DealerSupplyObjConstants.API_NAME);
//        String dealerId = dealerSupplyData.get(0).get(DealerSupplyObjConstants.Field.dealerId.getApiName()).toString();
//        //获取当前经销商的经营范围
//        Supplier argSupplier = supplyService.getSupplierById(controllerContext.getTenantId(), dealerId, 1);
//        Set<String> argProductIds = argSupplier.getProductList().stream().map(o -> o.getProductId()).collect(Collectors.toSet());
//        //所选门店的其他供货关系
//        List<IObjectData> existOtherStoreList = supplyDao.parallelGetDatasByQueryWithFields(controllerContext.getTenantId(), SearchQuery.builder()
//                        .neq(SupplyStoreObjConstants.Field.upSupplyId.getApiName(), arg.getDealerSupplyId())
//                        .in(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), arg.getStoreIds())
//                        .build(), SupplyStoreObjConstants.API_NAME,
//                Lists.newArrayList(SupplyStoreObjConstants.Field.upDealerId.getApiName(), SupplyStoreObjConstants.Field.specialSupply.getApiName(), SupplyStoreObjConstants.Field.thisDealerId.getApiName()));
//        List<String> addShopIds = Lists.newArrayList();
//        //addShop 已经存在的供货关系
//        Map<String, Set<String>> existSupplyIdsMap = MapUtils.EMPTY_MAP;
//        //其他供货商的 产品列表
//        Map<String, Set<String>> otherProductIdsMap = new HashMap<>();
//        MapUtils.synchronizedMap(otherProductIdsMap);
//        //如果存在其他的供货关系 则需要 取 其他的供货关系数据
//        if (CollectionUtils.isNotEmpty(existOtherStoreList)) {
//            //查询门店现有的供货商
//            existSupplyIdsMap = existOtherStoreList.stream().collect(
//                    Collectors.groupingBy(k -> k.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class),
//                            Collectors.mapping(v -> {
//                                        if (v.get(SupplyStoreObjConstants.Field.specialSupply.getApiName(), Boolean.class)) {
//                                            //特例
//                                            return v.get(SupplyStoreObjConstants.Field.thisDealerId.getApiName(), String.class) + "_" + v.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class);
//                                        } else {
//                                            //非特例
//                                            return v.get(SupplyStoreObjConstants.Field.upDealerId.getApiName(), String.class);
//                                        }
//
//                                    }
//                                    , Collectors.toSet())));
//            if (MapUtils.isNotEmpty(existSupplyIdsMap)) {
//                Set<String> existOtherDealers = new HashSet<>();
//                for (Set<String> value : existSupplyIdsMap.values()) {
//                    existOtherDealers.addAll(value);
//                }
//                existOtherDealers.parallelStream().forEach(o -> {
//                    String[] subIds = o.split("_");
//                    if (subIds.length == 1) {
//                        otherProductIdsMap.put(o, supplyService.getSupplierById(controllerContext.getTenantId(), dealerId, 1)
//                                .getProductList().stream().map(s -> s.getProductId()).collect(Collectors.toSet()));
//                    } else {
//                        List<IObjectData> iObjectDataList = supplyDao.parallelGetDatasByQueryWithFields(controllerContext.getTenantId(), SearchQuery.builder()
//                                        .eq(SpecialSupplyObjConstants.Field.shopId.getApiName(), subIds[0])
//                                        .eq(SpecialSupplyObjConstants.Field.dealerId.getApiName(), subIds[1])
//                                        .build(), SpecialSupplyObjConstants.API_NAME,
//                                Lists.newArrayList(SpecialSupplyObjConstants.Field.productId.getApiName()));
//                        if (CollectionUtils.isNotEmpty(iObjectDataList)){
//                            otherProductIdsMap.put(o,iObjectDataList.stream().map(i->i.get(SpecialSupplyObjConstants.Field.productId.getApiName(),String.class)).collect(Collectors.toSet()));
//                        }else{
//                            otherProductIdsMap.put(o, SetUtils.EMPTY_SET);
//                        }
//
//                    }
//                });
//            }
//        }
//        //判断冲突
//        for (String shopId : arg.getStoreIds()) {
//            List<String> productIds = Lists.newArrayList();
//            Set<String> supplyAccountIds = existSupplyIdsMap.get(shopId);
//            if (CollectionUtils.isNotEmpty(supplyAccountIds)) {
//                for (String o : supplyAccountIds) {
//                    Set<String> otherProductIds = otherProductIdsMap.get(o);
//                    if (CollectionUtils.isNotEmpty(otherProductIds)) {
//                        productIds.addAll(otherProductIds);
//                    }
//                }
//                //判断冲突
//                if (supplyService.checkForProductIdConflicts(argProductIds, productIds)) {
////                        冲突
//                    result.getStoreIds().add(shopId);
//                } else {
//                    addShopIds.add(shopId);
//                }
//            } else {
//                addShopIds.add(shopId);
//            }
//
//
//        }
//        if (CollectionUtils.isNotEmpty(addShopIds)) {
//            log.info("add shopIds {}", addShopIds);
//            supplyDao.createSupplyStoreObj(controllerContext.getTenantId(), controllerContext.getUser().getUserId(), addShopIds, dealerSupplyData.get(0), false);
//            fmcgThreadPoolExecutor.execute(
//                    () ->
//                            addShopIds.parallelStream().forEach(shopId -> {
//                                //重新设置客户的上级供货商字段
//                                supplyDao.updateAccountSupply(controllerContext.getTenantId(), shopId);
//                                //重新同步可售范围
//                                syncAvailableRangeService.syncAvailableObjByDealerSupplyObj(controllerContext.getTenantId(), dealerSupplyData.get(0), shopId);
//
//                            })
//            );
//        }
//        //获取客户的其他经营范围
//        return result;
//    }
}
