package com.facishare.crm.fmcg.wq.service;


import com.facishare.crm.fmcg.wq.model.PlanRouteInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;
import java.util.Map;

public interface AreaService {
    /**
     * 规划数据整理
     */
    List<PlanRouteInfo> convertPlanRouteInfo(List<IObjectData> data, Map<String, IFieldDescribe> fieldDescribeMap, List<String> showFiledApiNameFromListLayout);

    List<String> buildShowFieldApiName(String tenantId);

    String getFieldNameOnWaterDrop(String tenantId);

}
