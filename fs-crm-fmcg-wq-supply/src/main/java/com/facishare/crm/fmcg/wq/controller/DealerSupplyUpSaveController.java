package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.area.DealerSupplyUpSave;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.model.Shop;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.service.SyncAvailableRangeService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.ListUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.List;

/**
 * @program: fs-crm-fmcg
 * @description: 客户上级数据保存 支持 新建编辑更新 用户变更以后函数触发的逻辑
 * @author: zhangsm
 * @create: 2021-06-10 11:38
 **/
public class DealerSupplyUpSaveController extends PreDefineController<DealerSupplyUpSave.Arg, DealerSupplyUpSave.Result> {

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    SupplyService supplyService = SpringUtil.getContext().getBean(SupplyService.class);
    SyncAvailableRangeService syncAvailableRangeService= SpringUtil.getContext().getBean(SyncAvailableRangeService.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected DealerSupplyUpSave.Result doService(DealerSupplyUpSave.Arg arg) {
//        //验证
//        arg.setNoUpdate(true);
        supplyService.upSaveDealerSupply(controllerContext.getTenantId(),arg);
//        //验证成功的话  保存数据 触发审批
//        serviceFacade.updateObjectData(controllerContext.getUser(),arg.getIObjectData().toObjectData());
        result = new DealerSupplyUpSave.Result();
        return result;
    }

    @Override
    protected DealerSupplyUpSave.Result after(DealerSupplyUpSave.Arg arg, DealerSupplyUpSave.Result result) {
        DealerSupplyUpSave.Result after = super.after(arg, result);
        if (!arg.isNoUpdate()){
            fmcgThreadPoolExecutor.execute(
                    () -> {
                        Shop shopObjById = supplyService.getShopObjById(controllerContext.getTenantId(), arg.getIObjectData().getId(), 1);
                        syncAvailableRangeService.updateAllAvailableDataByShop(controllerContext.getTenantId(), shopObjById);
                    });
        }
        return after;
    }
}
