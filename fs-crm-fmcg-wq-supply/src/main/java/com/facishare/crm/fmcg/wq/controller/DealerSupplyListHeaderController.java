package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.util.SpringUtil;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-09-23 17:06
 **/
public class DealerSupplyListHeaderController extends DealerSupplyListBaseHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }
}
