package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.PMMService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.crmrestapi.common.contants.AccountFieldContants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Slf4j
public class PromoterEditAction extends FmcgSkipPermissionEditAction {

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);
    PMMService pmmService = SpringUtil.getContext().getBean(PMMService.class);

    @Override
    protected void before(Arg arg) {
        //在super.before 之前需要自己取checkins_flag 值
        if (StringUtils.isNotBlank(Optional.ofNullable(arg.getObjectData().get("checkins_flag")).orElse("").toString())) {
            // 如果没有传手机号的话补充一下置为null
            if (arg.getObjectData().get(PromoterFields.MOBILE) == null){
                arg.getObjectData().put(PromoterFields.MOBILE, null);
            }
        }

        // 参数转换：如果REVIEW_STATUS是boolean类型，转换成字符串
        Object reviewStatus = arg.getObjectData().get(PromoterFields.REVIEW_STATUS);
        if (reviewStatus instanceof Boolean) {
            Boolean boolValue = (Boolean) reviewStatus;
            String stringValue = boolValue ? "true" : "false";
            arg.getObjectData().put(PromoterFields.REVIEW_STATUS, stringValue);
        }
        super.before(arg);
        String reviewStatusStr = Optional.ofNullable(arg.getObjectData().get(PromoterFields.REVIEW_STATUS)).orElse("").toString();
        if (StringUtils.isNotBlank(checkinsFlag)) {

        }else{
            //如果用户选择了
            if (updatedFieldMap.containsKey("enterprise_relation_id")) {
                throw new ValidateException("归属客户字段不支持编辑"); //ignoreI18n
            }
            if (updatedFieldMap.containsKey(PromoterFields.IO_STATUS)) {
                throw new ValidateException("在职状态字段不支持编辑，从互联用户同步"); //ignoreI18n
            }
            if (objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID) == null) {
                if (updatedFieldMap.containsKey("promoter_type") && PromoterFields.ReviewStatus.agree.getValue().equals(reviewStatusStr)) {
                    throw new ValidateException("促销员类型字段审核同意以后不支持编辑，从互联角色同步"); //ignoreI18n
                }
            }else if (updatedFieldMap.containsKey(PromoterFields.PUBLIC_EMPLOYEE_ID)) {
                throw new ValidateException("互联用户ID字段不支持编辑"); //ignoreI18n
            }
        }
        if (arg.getObjectData().get("mobile") != null && !"".equals(arg.getObjectData().get("mobile"))) {
            //验证 互联用户是否重复， 手机号和企业账号重复
            SearchQuery build = SearchQuery.builder()
                    .eq("mobile", arg.getObjectData().get("mobile"))
                    .eq("enterprise_relation_id", arg.getObjectData().get("enterprise_relation_id"))
                    .neq("_id", arg.getObjectData().getId())
                    .build();
            int promoterObjTotal = supplyDao.getTotal(User.systemUser(actionContext.getTenantId()), build, "PromoterObj");
            if (promoterObjTotal > 0) {
                throw new ValidateException("同互联企业手机号码重复"); //ignoreI18n
            }
        }
//        if (arg.getObjectData().get("name") != null && !"".equals(arg.getObjectData().get("name"))) {
//            //验证 互联用户是否重复， 手机号和企业账号重复
//            SearchQuery build = SearchQuery.builder()
//                    .eq("name", arg.getObjectData().get("name"))
//                    .eq("enterprise_relation_id", arg.getObjectData().get("enterprise_relation_id"))
//                    .neq("_id", arg.getObjectData().getId())
//                    .build();
//            int promoterObjTotal = supplyDao.getTotal(User.systemUser(actionContext.getTenantId()), build, "PromoterObj");
//            if (promoterObjTotal > 0) {
//                throw new ValidateException("同互联企业互联用户名称重复");
//            }
//        }
    }

    @Override
    protected Result doAct(Arg arg) {
//        if (updatedFieldMap.containsKey(PromoterFields.IO_STATUS)) {
//            //如果修改了促销员类型，需要同步修改互联角色
//            String ioStatus = Optional.ofNullable(arg.getObjectData().get(PromoterFields.IO_STATUS)).orElse("").toString();
//            if (ioStatus.equals("0")) {
//                //停用互联用户
//                pmmService.stopPublicEmployee(actionContext.getTenantId(),arg.getObjectData().get(PromoterFields.PUBLIC_EMPLOYEE_ID).toString());
//            } else if (ioStatus.equals("1")) {
//                //启用互联用户
//                pmmService.startPublicEmployee(actionContext.getTenantId(),arg.getObjectData().get(PromoterFields.PUBLIC_EMPLOYEE_ID).toString());
//            }
//        }
        Result result = super.doAct(arg);
        return result;
    }

    /**
     * 需要重新 ，先更新 互联用户的数据 再修改互联角色
     */
    @Override
    protected void doUpdateData() {
        if (StringUtils.isBlank(checkinsFlag) && objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID) != null) {
            //修改手机号名词
            if (updatedFieldMap.containsKey(PromoterFields.MOBILE) || updatedFieldMap.containsKey(BaseField.name.getApiName())) {
                pmmService.updatePublicEmployeeMobile(actionContext.getTenantId(),actionContext.getUser().getUserId(), objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID).toString(), objectData.get(PromoterFields.MOBILE).toString(), objectData.get(BaseField.name.getApiName()).toString());
            }
            //修改业务类型
            if (updatedFieldMap.containsKey(PromoterFields.PROMOTER_TYPE) && objectData.get(PromoterFields.REVIEW_STATUS).equals(PromoterFields.ReviewStatus.agree.getValue())) {
                pmmService.changeRolesWithPromoter(actionContext.getTenantId(),objectData);
            }
            //修改相关团队逻辑
            if (updatedFieldMap.containsKey(PromoterFields.RESPONSIBLE_ACCOUNTS)) {
                List<String> beforeIds = dbMasterData.get(PromoterFields.RESPONSIBLE_ACCOUNTS, List.class);
                List<String> afterIds = objectData.get(PromoterFields.RESPONSIBLE_ACCOUNTS, List.class);
                //del before 判空
                List<String> del = CollectionUtils.isEmpty(beforeIds) ? null : beforeIds.stream().filter(s -> CollectionUtils.isEmpty(afterIds) || !afterIds.contains(s)).collect(Collectors.toList());
                //add after 判空
                List<String> add = CollectionUtils.isEmpty(afterIds) ? null : afterIds.stream().filter(s -> CollectionUtils.isEmpty(beforeIds) || !beforeIds.contains(s)).collect(Collectors.toList());
                //修改相关团队
                if (CollectionUtils.isNotEmpty(del)) {
                    pmmService.removePromoterFromTeam(actionContext.getTenantId(),objectData.get(PromoterFields.ENTERPRISE_RELATION_ID,String.class), objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID).toString(), del, AccountFieldContants.API_NAME);
                }
                if (CollectionUtils.isNotEmpty(add)) {
                    pmmService.addPromoterToTeam(actionContext.getTenantId(),objectData.get(PromoterFields.ENTERPRISE_RELATION_ID,String.class), objectData.get(PromoterFields.PUBLIC_EMPLOYEE_ID).toString(), add,AccountFieldContants.API_NAME);
                }
            }
        }
        super.doUpdateData();
    }
}
