package com.facishare.crm.fmcg.wq.api.area;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface VerifyStore {
    @Data
    @ToString
    class Arg implements Serializable {

        @SerializedName("area_id")
        @JSONField(name = "area_id")
        @JsonProperty("area_id")
        private List<String> area_id;

        @SerializedName("store_id")
        @JSONField(name = "store_id")
        @JsonProperty("store_id")
        private List<String> storeIds;


    }

    @Data
    @ToString
    class Result implements Serializable {

        private Map<String,Integer> storeInfo = Maps.newHashMap();// storeId   0 不属于任何片区  1 在该片区  2 不在该片区产品组不冲突  3 不在该片区产品组冲突

    }

}
