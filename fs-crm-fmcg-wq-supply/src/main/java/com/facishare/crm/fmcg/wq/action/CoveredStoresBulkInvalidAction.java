package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.facade.AuditLogServiceFacade;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class CoveredStoresBulkInvalidAction extends StandardBulkInvalidAction {
    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);

    SupplyDao supplyDao = SpringUtil.getContext().getBean(SupplyDao.class);

    AuditLogServiceFacade auditLogServiceFacade = SpringUtil.getContext().getBean("auditLogServiceFacade",AuditLogServiceFacade.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);

    }
    @Override
    protected Result doAct(Arg arg) {
//        return super.doAct(arg);
        List<IObjectData> invalidedMasterData = this.dealMasterData();
        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(this.needTriggerInvalidAfterActionDataList) && com.facishare.paas.appframework.common.util.CollectionUtils.empty(invalidedMasterData)) {
            throw new ValidateException(I18N.text("paas.udobj.data_invalid_failed", new Object[]{this.bulkOpResult.getFailReason()}));
        } else {
            if (invalidedMasterData.size() == 1) {
                Map<String, Object> args = Maps.newHashMap();
                args.put("args", this.getArgs());
                this.infraServiceFacade.doCurrentAction(args, this.actionContext, this.objectDescribe, (IObjectData)invalidedMasterData.get(0), Maps.newHashMap());
            }

            this.dealWithDetail(invalidedMasterData);
            this.dealGdpr(invalidedMasterData);
            //修改记录放到主对象上一份
            String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
            if(!GrayRelease.isAllow("checkin-server-v2", "isYLArea",ea)) {
                recordLog();
            }else{
                recordMasterLog();
            }
            return this.generateResult();
        }

    }

    private void recordLog() {
        List<IObjectData> resultListToLog = this.bulkOpResult.getSuccessObjectDataList();
        this.auditLogServiceFacade.recordInvalidLog(this.actionContext.getUser(), resultListToLog, this.detailObjectData, this.objectDescribeMap);
    }
    protected String masterLogId;
    private void recordMasterLog() {
        List<IObjectData> resultListToLog = this.bulkOpResult.getSuccessObjectDataList();
        Map<String,List<IObjectData>> handleData = resultListToLog.stream().collect(Collectors.groupingBy(o->o.get(CoveredStoresConstants.BELONG_AREA).toString()));
        if(MapUtils.isNotEmpty(handleData)) {
            for (Map.Entry<String, List<IObjectData>> entry : handleData.entrySet()) {
                Map<String, IObjectDescribe> objectDescribes = Maps.newHashMap();
                IObjectDescribe mainDesc = serviceFacade.findObject(actionContext.getTenantId(), AreaManageConstants.AREA_MANAGE_OBJ);
                IObjectDescribe detailDesc = serviceFacade.findObject(actionContext.getTenantId(), CoveredStoresConstants.COVERED_STORES_OBJ);

                IObjectData areaInfo = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()),entry.getKey(), AreaManageConstants.AREA_MANAGE_OBJ);

                objectDescribes.put(AreaManageConstants.AREA_MANAGE_OBJ, mainDesc);
                objectDescribes.put(CoveredStoresConstants.COVERED_STORES_OBJ, detailDesc);
                List detailChange = Lists.newArrayList(CoveredStoresConstants.COVERED_STORES_OBJ);
                LogService.MasterLogInfo masterLog = this.serviceFacade.fillMasterModifyLog(this.actionContext.getUser(), objectDescribes, areaInfo, this.getUpdatedFieldMapForLog(areaInfo), areaInfo, detailChange);
                this.masterLogId = masterLog.getMasterLogId();
                this.serviceFacade.sendLog(masterLog.getLogList());

                Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
                describeMap.put(CoveredStoresConstants.COVERED_STORES_OBJ, detailDesc);
                this.serviceFacade.log(this.actionContext.getUser(), EventType.MODIFY, ActionType.Invalid, describeMap, entry.getValue(), this.masterLogId);
            }
        }

    }
    private Map<String, Map<String, Map<String, Object>>> getUpdatedFieldMapForLog(IObjectData objectData) {
        Map<String, Map<String, Map<String, Object>>> allUpdatedFieldMap = Maps.newHashMap();
        // 获取主对象的更新字段
        Map<String, Map<String, Object>> masterUpdateMap = Maps.newHashMap();
        masterUpdateMap.put(objectData.getId(), Maps.newHashMap());
        allUpdatedFieldMap.put(objectData.getDescribeApiName(), masterUpdateMap);
        return allUpdatedFieldMap;
    }
    private void dealGdpr(List<IObjectData> dataList) {
        List<String> ids = (List)dataList.stream().map(DBRecord::getId).collect(Collectors.toList());
        ParallelUtils.createBackgroundTask().submit(() -> {
            this.infraServiceFacade.bulkInvalidGdprLegalBase(this.actionContext.getUser(), this.objectDescribe.getApiName(), ids);
        }).run();
    }
    @Override
    protected Result after(Arg arg, Result result) {
       Result after = super.after(arg, result);

        //异步方法取不到 ea  批量删除异步方法中取不到 ea  只能转一下
        String ei = actionContext.getTenantId();
        String ea = serviceFacade.getEAByEI(ei);
        if(!GrayRelease.isAllow("checkin-server-v2", "areaManage",ea)){
            Boolean isYL = GrayRelease.isAllow("checkin-server-v2", "isYLArea",ea);
            log.info("CoveredStoresBulkInvalidAction ,{}",arg.getDataIds());

//            fmcgThreadPoolExecutor.execute(() -> {
                try {
                    List<IObjectData> data = serviceFacade.findObjectDataByIdsIncludeDeleted(User.systemUser(actionContext.getTenantId()), arg.getDataIds(), CoveredStoresConstants.COVERED_STORES_OBJ);
                    if (CollectionUtils.isNotEmpty(data)) {
                        List<String> ids = data.stream().filter(o-> Objects.nonNull(o.get(CoveredStoresConstants.STORE))).map(o->o.get(CoveredStoresConstants.STORE).toString()).collect(Collectors.toList());
                        supplyDao.updateAccountAreaInfo(ei,-10000,null,ids,isYL);
                    }
                } catch (Exception e) {
                    log.error("AreaManageInvalidAction handle error", e);
                }
//            });
        }
        return after;
    }
}
