package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date : 2022/2/15  15:24
 */
public class VisitRouteStoreCopyAction extends PreDefineAction<VisitRouteStoreCopyAction.Arg, VisitRouteStoreCopyAction.Result> {
    private IObjectData objectData;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("StoreCopy");
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        /*String bizStatus = objectData.get("biz_status", String.class);
        if ("allocated".equals(bizStatus)) {
            throw new ValidateException("已分配客户不能被分配！");
        }
        */
    }

    /**
     *
     *
     * @param arg
     * @return
     */
    @Override
    protected Result doAct(Arg arg) {
        Map<String, Object> updateMap = Maps.newHashMap();
        //updateMap.put("biz_status", "allocated");
        IObjectData data = serviceFacade.updateWithMap(actionContext.getUser(), objectData, updateMap);
        return Result.of(data);
    }

    @Data
    public static class Arg {
        private String dataId;
    }

    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
