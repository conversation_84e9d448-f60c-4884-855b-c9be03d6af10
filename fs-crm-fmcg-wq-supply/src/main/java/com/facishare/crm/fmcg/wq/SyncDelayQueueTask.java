package com.facishare.crm.fmcg.wq;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.task.AsyncTaskExecutor;

import javax.annotation.Resource;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

import static java.util.concurrent.TimeUnit.NANOSECONDS;
import static java.util.concurrent.TimeUnit.SECONDS;

/**
 * @program: fs-crm-fmcg
 * @description: 同步任务
 * @author: zhangsm
 * @create: 2021-05-28 10:49
 **/
@Data
@Slf4j
public class SyncDelayQueueTask<T extends SyncDelayQueueTask.DelayedTask> implements Runnable {
    @Resource
    AsyncTaskExecutor fmcgThreadPoolExecutor;

    private boolean running = false;


    DelayQueue<T> queue = new DelayQueue();
    @Override
    public void run() {
        log.info("SyncDelayQueueTask run started -----"+Thread.currentThread().getName());
        while (!queue.isEmpty()) {
            try {
                queue.take().run();
            } catch (Exception e) {
                log.error("SyncDelayQueueTask take run error ----",e);
            }
        }
        running = false;
        log.info("SyncDelayQueueTask run finished -----"+Thread.currentThread().getName());

    }

    public void  addTask(T task){
        queue.add(task);
        if (!isRunning()){
            fmcgThreadPoolExecutor.execute(this);
        }
    }
    public boolean removeTaskIf(Predicate<? super DelayedTask> filter){
        return queue.removeIf(filter);
    }


    public static class DelayedTask implements Delayed, Runnable{
        long triggerTime;

        public DelayedTask(long triggerTime) {
            this.triggerTime = System.nanoTime() + NANOSECONDS.convert(triggerTime, SECONDS);
        }

        @Override
        public void run() {
            log.info("DelayedTask started triggerTime {}",triggerTime);
        }


        @Override
        public long getDelay(@NotNull TimeUnit unit) {
            return unit.convert(triggerTime - System.nanoTime(), TimeUnit.NANOSECONDS);
        }

        @Override
        public int compareTo(@NotNull Delayed o) {
            DelayedTask that = (DelayedTask) o;
            if (triggerTime < that.triggerTime) {
                return -1;
            }
            if (triggerTime > that.triggerTime) {
                return 1;
            }
            return 0;

        }
    }


}
