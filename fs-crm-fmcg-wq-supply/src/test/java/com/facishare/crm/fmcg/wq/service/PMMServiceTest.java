package com.facishare.crm.fmcg.wq.service;

import com.facishare.crm.fmcg.wq.constants.PromoterFields;
import com.facishare.crm.fmcg.wq.dao.BaseDaoInterface;
import com.facishare.crm.fmcg.wq.dao.PublicEmployeeDao;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.metadata.api.IObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PMMService测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("PMMService服务类测试")
class PMMServiceTest {

    @Mock
    private BaseDaoInterface baseDao;

    @Mock
    private CheckinsProxy checkinsProxy;

    @Mock
    private PublicEmployeeDao publicEmployeeDao;

    @Mock
    private ServiceFacade serviceFacade;

    @InjectMocks
    private PMMService pmmService;

    private final String tenantId = "test-tenant";
    private final String userId = "test-user";

    @BeforeEach
    void setUp() {
        // 通用设置
    }

    @Test
    @DisplayName("测试PMMService类存在")
    void testPMMServiceExists() {
        // 验证PMMService接口存在
        assertNotNull(PMMService.class);
    }

    @Test
    @DisplayName("测试stopPublicEmployee方法 - 正常情况")
    void testStopPublicEmployee_Success() {
        // 模拟checkinsProxy的行为
        when(checkinsProxy.stopPublicEmployee(tenantId, tenantId, userId))
                .thenReturn(true);

        // 执行测试
        assertDoesNotThrow(() -> {
            pmmService.stopPublicEmployee(tenantId, userId);
        });

        // 验证方法调用
        verify(checkinsProxy).stopPublicEmployee(tenantId, tenantId, userId);
    }

    @Test
    @DisplayName("测试stopPublicEmployee方法 - 参数为空")
    void testStopPublicEmployee_WithEmptyParams() {
        // 执行测试 - 应该抛出ValidateException
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            pmmService.stopPublicEmployee("", "");
        });

        // 验证异常消息
        assertTrue(exception.getMessage().contains("停用互联用户参数异常"));

        // 验证方法调用
        verify(checkinsProxy, never()).stopPublicEmployee(anyString(), anyString(), anyString());
    }

    @Test
    @DisplayName("测试getPromoterById方法")
    void testGetPromoterById() {
        // 准备测试数据
        String promoterId = "promoter-id";
        IObjectData mockPromoter = mock(IObjectData.class);
        when(mockPromoter.getId()).thenReturn(promoterId);

        // 模拟baseDao的行为
        when(baseDao.getById(tenantId, PromoterFields.API_NAME, promoterId))
                .thenReturn(mockPromoter);

        // 执行测试
        IObjectData result = pmmService.getPromoterById(tenantId, promoterId);

        // 验证结果
        assertNotNull(result);
        assertEquals(promoterId, result.getId());

        // 验证方法调用
        verify(baseDao).getById(tenantId, PromoterFields.API_NAME, promoterId);
    }

    @Test
    @DisplayName("测试getPromoterByIds方法")
    void testGetPromoterByIds() {
        // 准备测试数据
        List<String> promoterIds = Arrays.asList("promoter-id-1", "promoter-id-2");
        List<IObjectData> mockPromoters = new ArrayList<>();

        IObjectData promoter1 = mock(IObjectData.class);
        when(promoter1.getId()).thenReturn("promoter-id-1");
        mockPromoters.add(promoter1);

        IObjectData promoter2 = mock(IObjectData.class);
        when(promoter2.getId()).thenReturn("promoter-id-2");
        mockPromoters.add(promoter2);

        // 模拟baseDao的行为
        when(baseDao.getByIds(tenantId, PromoterFields.API_NAME, promoterIds))
                .thenReturn(mockPromoters);

        // 执行测试
        List<IObjectData> result = pmmService.getPromoterByIds(tenantId, promoterIds);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("promoter-id-1", result.get(0).getId());
        assertEquals("promoter-id-2", result.get(1).getId());

        // 验证方法调用
        verify(baseDao).getByIds(tenantId, PromoterFields.API_NAME, promoterIds);
    }

    @Test
    @DisplayName("测试isLeave方法 - 使用promoterId")
    void testIsLeave_WithPromoterId() {
        // 准备测试数据
        String promoterId = "promoter-id";
        IObjectData mockPromoter = mock(IObjectData.class);
        when(mockPromoter.get(PromoterFields.IO_STATUS)).thenReturn("0");

        // 模拟baseDao的行为
        when(baseDao.getById(tenantId, PromoterFields.API_NAME, promoterId))
                .thenReturn(mockPromoter);

        // 执行测试
        boolean result = pmmService.isLeave(tenantId, promoterId);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(baseDao).getById(tenantId, PromoterFields.API_NAME, promoterId);
    }
}
