package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.model.PlanRouteInfo;
import com.facishare.crm.fmcg.wq.service.AreaService;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CoveredStoresMapStoreFilterV2ControllerTest {

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private RequestContext requestContext;

    @Mock
    private AreaService areaService;

    @InjectMocks
    private CoveredStoresMapStoreFilterV2Controller controller;

    private final String tenantId = "test-tenant";

    @BeforeEach
    void setUp() {
        // 设置控制器上下文
        ControllerContext controllerContext = mock(ControllerContext.class);
        when(controllerContext.getTenantId()).thenReturn(tenantId);
        when(controllerContext.getRequestContext()).thenReturn(requestContext);
        when(controllerContext.getMethodName()).thenReturn("getQueryResult");
        when(controllerContext.getUser()).thenReturn(User.systemUser(tenantId));

        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field field = CoveredStoresMapStoreFilterV2Controller.class.getDeclaredField("controllerContext");
            field.setAccessible(true);
            field.set(controller, controllerContext);

            field = CoveredStoresMapStoreFilterV2Controller.class.getDeclaredField("serviceFacade");
            field.setAccessible(true);
            field.set(controller, serviceFacade);
        } catch (Exception e) {
            fail("Failed to set controllerContext: " + e.getMessage());
        }

        // 注入 areaService
        ReflectionTestUtils.setField(controller, "areaService", areaService);
    }

    @Test
    void testGetQueryResult_Success() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(new ArrayList<>());

        // 模拟areaService的行为
        List<String> showFieldApiNameList = Lists.newArrayList("name", "location", "owner");
        when(areaService.buildShowFieldApiName(tenantId)).thenReturn(showFieldApiNameList);
        when(areaService.getFieldNameOnWaterDrop(tenantId)).thenReturn("name");

        // 模拟serviceFacade的行为
        IObjectDescribe objectDescribe = mock(IObjectDescribe.class);
        when(serviceFacade.findObject(eq(tenantId), eq(CommonConstants.ACCOUNT_OBJ))).thenReturn(objectDescribe);

        List<IObjectData> mockData = new ArrayList<>();
        IObjectData mockObjectData = mock(IObjectData.class);
        mockData.add(mockObjectData);

        // 模拟ObjectUtils的行为
        try (MockedStatic<ObjectUtils> mockedObjectUtils = Mockito.mockStatic(ObjectUtils.class)) {
            mockedObjectUtils.when(() -> ObjectUtils.queryDataWithFieldInfo(
                    eq(serviceFacade),
                    any(User.class),
                    eq(requestContext),
                    eq(objectDescribe),
                    any(SearchTemplateQuery.class),
                    eq(showFieldApiNameList)
            )).thenReturn(mockData);

            // 模拟convertPlanRouteInfo的行为
            List<PlanRouteInfo> planRouteInfos = new ArrayList<>();
            PlanRouteInfo planRouteInfo = new PlanRouteInfo();
            planRouteInfo.set_id("test-id");
            planRouteInfo.setAreaId("test-area-id");
            planRouteInfo.setAreaName("Test Area");
            planRouteInfos.add(planRouteInfo);

            when(areaService.convertPlanRouteInfo(eq(mockData), any(), eq(showFieldApiNameList)))
                    .thenReturn(planRouteInfos);

            // 执行测试
            QueryResult<IObjectData> result = controller.getQueryResult(query);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());

            // 验证方法调用
            verify(areaService).buildShowFieldApiName(tenantId);
            verify(areaService).getFieldNameOnWaterDrop(tenantId);
            verify(serviceFacade).findObject(eq(tenantId), eq(CommonConstants.ACCOUNT_OBJ));
            verify(serviceFacade).fillUserInfo(eq(objectDescribe), eq(mockData), any(User.class));
        }
    }

    @Test
    void testBuildCoveredStoresFilter() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(new ArrayList<>());

        // 模拟serviceFacade的行为
        IObjectDescribe objectDescribe = mock(IObjectDescribe.class);
        IFieldDescribe fieldDescribe = mock(IFieldDescribe.class);

        when(serviceFacade.findObject(eq(tenantId), anyString())).thenReturn(objectDescribe);
        when(objectDescribe.getFieldDescribe(eq(AreaManageConstants.STORE))).thenReturn(fieldDescribe);
        when(fieldDescribe.get("wheres")).thenReturn(null);

        // 执行测试
        SearchQuery result = controller.buildCoveredStoresFilter(tenantId, query);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getSearchTemplateQuery());
        assertEquals(query.getFilters(), result.getSearchTemplateQuery().getFilters());

        // 验证方法调用
        verify(serviceFacade).findObject(eq(tenantId), anyString());
        verify(objectDescribe).getFieldDescribe(eq(AreaManageConstants.STORE));
    }

    @Test
    void testBefore() {
        // 准备测试数据
        CoveredStoresMapStoreFilterV2Controller.Arg arg = new CoveredStoresMapStoreFilterV2Controller.Arg();

        // 执行测试
        controller.before(arg);

        // 验证方法执行成功
        assertNotNull(arg);
    }
}
