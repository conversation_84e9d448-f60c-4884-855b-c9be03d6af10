package com.facishare.crm.fmcg.wq.service.impl;

import com.facishare.crm.fmcg.wq.constants.ChannelObjConstants;
import com.facishare.crm.fmcg.wq.dao.BaseDaoInterface;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ChannelServiceImpl测试")
class ChannelServiceImplTest {

    @Mock
    private BaseDaoInterface baseDao;

    @InjectMocks
    private ChannelServiceImpl channelService;

    private final String tenantId = "test-tenant";

    @BeforeEach
    void setUp() {
        // 通用设置
    }

    @Test
    @DisplayName("测试checkIsValid方法 - 当渠道对象有效时")
    void testCheckIsValid_WhenChannelIsValid() {
        // 准备测试数据
        List<String> dataIds = Collections.singletonList("channel-id");
        List<IObjectData> mockChannels = new ArrayList<>();

        IObjectData channel = mock(IObjectData.class);
        when(channel.get(ChannelObjConstants.Field.isValid.getApiName())).thenReturn(1);
        mockChannels.add(channel);

        // 模拟baseDao的行为
        when(baseDao.getAllIObjectDataListByQuery(any(User.class), any(SearchQuery.class), eq(ChannelObjConstants.API_NAME)))
                .thenReturn(mockChannels);

        // 执行测试 - 应该抛出ValidateException
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            channelService.checkIsValid(tenantId, dataIds);
        });

        // 验证异常消息
        assertTrue(exception.getMessage().contains("渠道对象启用状态不可以作废"));

        // 验证方法调用
        verify(baseDao).getAllIObjectDataListByQuery(any(User.class), any(SearchQuery.class), eq(ChannelObjConstants.API_NAME));
    }

    @Test
    @DisplayName("测试checkIsValid方法 - 当渠道对象无效时")
    void testCheckIsValid_WhenChannelIsInvalid() {
        // 准备测试数据
        List<String> dataIds = Collections.singletonList("channel-id");
        List<IObjectData> mockChannels = new ArrayList<>();

        IObjectData channel = mock(IObjectData.class);
        when(channel.get(ChannelObjConstants.Field.isValid.getApiName())).thenReturn(0);
        mockChannels.add(channel);

        // 模拟baseDao的行为
        when(baseDao.getAllIObjectDataListByQuery(any(User.class), any(SearchQuery.class), eq(ChannelObjConstants.API_NAME)))
                .thenReturn(mockChannels);

        // 执行测试 - 不应该抛出异常
        assertDoesNotThrow(() -> {
            channelService.checkIsValid(tenantId, dataIds);
        });

        // 验证方法调用
        verify(baseDao).getAllIObjectDataListByQuery(any(User.class), any(SearchQuery.class), eq(ChannelObjConstants.API_NAME));
    }

    @Test
    @DisplayName("测试buildFilter方法")
    void testBuildFilter() {
        // 准备测试数据
        List<IFilter> filters = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFieldName("test-field");
        // 由于 Operator 枚举和 setFieldValue 方法不可用，我们使用反射设置字段值
        try {
            java.lang.reflect.Field operatorField = Filter.class.getDeclaredField("operator");
            operatorField.setAccessible(true);
            operatorField.set(filter, "=");

            java.lang.reflect.Field fieldValueField = Filter.class.getDeclaredField("fieldValue");
            fieldValueField.setAccessible(true);
            fieldValueField.set(filter, "test-value");
        } catch (Exception e) {
            fail("反射设置字段值失败: " + e.getMessage());
        }
        filters.add(filter);

        // 执行测试
        List<IFilter> result = channelService.buildFilter(filters);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个过滤器是原始过滤器
        assertEquals(filter, result.get(0));

        // 验证第二个过滤器是新添加的过滤器
        IFilter addedFilter = result.get(1);
        assertEquals(ChannelObjConstants.Field.parentId.getApiName(), addedFilter.getFieldName());
        // 由于 Operator.IS_NULL 不可用，我们只验证字段名
    }

    @Test
    @DisplayName("测试calculateLevel方法 - 添加操作")
    void testCalculateLevel_Add() {
        // 准备测试数据
        BaseObjectSaveAction.Arg arg = new BaseObjectSaveAction.Arg();
        ObjectDataDocument objectData = new ObjectDataDocument();
        objectData.put(ChannelObjConstants.Field.parentId.getApiName(), "parent-id");
        arg.setObjectData(objectData);

        IObjectData parentChannel = mock(IObjectData.class);
        when(parentChannel.get(ChannelObjConstants.Field.level.getApiName())).thenReturn(1);

        // 模拟baseDao的行为
        when(baseDao.getById(eq(tenantId), eq(ChannelObjConstants.API_NAME), eq("parent-id")))
                .thenReturn(parentChannel);

        // 执行测试
        Integer result = channelService.calculateLevel(tenantId, arg, true);

        // 验证结果
        assertEquals(2, result);

        // 验证方法调用
        verify(baseDao).getById(eq(tenantId), eq(ChannelObjConstants.API_NAME), eq("parent-id"));
    }
}
