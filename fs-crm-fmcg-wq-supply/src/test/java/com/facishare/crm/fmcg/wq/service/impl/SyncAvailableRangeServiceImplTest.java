package com.facishare.crm.fmcg.wq.service.impl;

import com.facishare.crm.fmcg.wq.SyncDelayQueueTask;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SyncAvailableRangeServiceImplTest {

    @Mock
    private SupplyDao supplyDao;

    @Mock
    private BaseDao baseDao;

    @Mock
    private SupplyService supplyService;

    @Mock
    private RedisUtils redisUtils;

    @Mock
    private SyncDelayQueueTask<SyncDelayQueueTask.DelayedTask> syncAvailableRangeDelayQueueTask;

    @InjectMocks
    private SyncAvailableRangeServiceImpl syncAvailableRangeService;

    private final String tenantId = "test-tenant";
    private final String dealerSupplyId = "dealer-supply-id";

    @Before
    public void setUp() {
        // 通用设置
    }

    @Test
    public void testAddAvailableObjSyncTask_WhenTaskInProgress() {
        // 模拟redisUtils的行为
        when(redisUtils.syncAvaRangeDoing(tenantId, dealerSupplyId)).thenReturn(true);

        // 模拟syncAvailableRangeDelayQueueTask的行为
        when(syncAvailableRangeDelayQueueTask.removeTaskIf(any())).thenReturn(true);

        // 执行测试
        syncAvailableRangeService.addAvailableObjSyncTask(tenantId, dealerSupplyId);

        // 验证方法调用
        verify(redisUtils).syncAvaRangeDoing(tenantId, dealerSupplyId);
        verify(syncAvailableRangeDelayQueueTask).removeTaskIf(any());
        verify(syncAvailableRangeDelayQueueTask).addTask(any());
        verify(redisUtils).syncAvaRangeAdd(tenantId, dealerSupplyId);
    }

    @Test
    public void testAddAvailableObjSyncTask_WhenTaskNotInProgress() {
        // 模拟redisUtils的行为
        when(redisUtils.syncAvaRangeDoing(tenantId, dealerSupplyId)).thenReturn(false);

        // 执行测试
        syncAvailableRangeService.addAvailableObjSyncTask(tenantId, dealerSupplyId);

        // 验证方法调用
        verify(redisUtils).syncAvaRangeDoing(tenantId, dealerSupplyId);
        verify(syncAvailableRangeDelayQueueTask, never()).removeTaskIf(any());
        verify(syncAvailableRangeDelayQueueTask).addTask(any());
        verify(redisUtils).syncAvaRangeAdd(tenantId, dealerSupplyId);
    }
}
