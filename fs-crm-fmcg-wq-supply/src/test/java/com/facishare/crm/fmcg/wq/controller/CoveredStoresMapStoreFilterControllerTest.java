package com.facishare.crm.fmcg.wq.controller;


import com.facishare.crm.fmcg.wq.api.area.MapStoreFilter;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.release.GrayRelease;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CoveredStoresMapStoreFilterControllerTest {

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private RequestContext requestContext;

    @InjectMocks
    private CoveredStoresMapStoreFilterController controller;

    private final String tenantId = "test-tenant";
    private final String productGroupId = "test-product-group-id";
    private final String areaAttributeName = "test-area-attribute";
    private final List<String> county = Arrays.asList("county1", "county2");

    @BeforeEach
    public void setUp() {
        // 设置控制器上下文
        ControllerContext controllerContext = mock(ControllerContext.class);
        when(controllerContext.getTenantId()).thenReturn(tenantId);
        when(controllerContext.getEa()).thenReturn(tenantId);
        when(controllerContext.getRequestContext()).thenReturn(requestContext);

        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field field = CoveredStoresMapStoreFilterController.class.getDeclaredField("controllerContext");
            field.setAccessible(true);
            field.set(controller, controllerContext);
        } catch (Exception e) {
            fail("Failed to set controllerContext: " + e.getMessage());
        }
    }

    @Test
    public void testDoService_WithYLAreaGrayRelease() {
        // 准备测试数据
        MapStoreFilter.Arg arg = new MapStoreFilter.Arg();
        arg.setCounty(county);
        arg.setProductGroupId(productGroupId);
        arg.setAreaAttributeName(areaAttributeName);

        // 模拟GrayRelease的行为
        try (MockedStatic<GrayRelease> mockedStatic = Mockito.mockStatic(GrayRelease.class)) {
            mockedStatic.when(() -> GrayRelease.isAllow("checkin-server-v2", "areaManage", tenantId))
                    .thenReturn(false);
            mockedStatic.when(() -> GrayRelease.isAllow("checkin-server-v2", "isYLArea", tenantId))
                    .thenReturn(true);

            // 模拟serviceFacade的行为
            IObjectDescribe objectDescribe = mock(IObjectDescribe.class);
            when(serviceFacade.findObject(eq(tenantId), eq(CommonConstants.ACCOUNT_OBJ)))
                    .thenReturn(objectDescribe);

            QueryResult<IObjectData> queryResult = new QueryResult<>();
            List<IObjectData> resultData = new ArrayList<>();
            queryResult.setData(resultData);
            // 由于 setTotalNum 方法不可用，我们使用反射设置 totalNum 字段
            try {
                java.lang.reflect.Field totalNumField = QueryResult.class.getDeclaredField("totalNum");
                totalNumField.setAccessible(true);
                totalNumField.set(queryResult, 0);
            } catch (Exception e) {
                fail("反射设置 totalNum 失败: " + e.getMessage());
            }

            when(serviceFacade.findBySearchQuery(any(User.class), eq(CommonConstants.ACCOUNT_OBJ), any(SearchTemplateQuery.class)))
                    .thenReturn(queryResult);

            // 执行测试
            MapStoreFilter.Result result = controller.doService(arg);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDataList());
            assertTrue(result.getDataList().isEmpty());

            // 验证方法调用
            verify(serviceFacade).findObject(eq(tenantId), eq(CommonConstants.ACCOUNT_OBJ));
        }
    }

    @Test
    public void testDoService_WithJMLGrayRelease() {
        // 准备测试数据
        MapStoreFilter.Arg arg = new MapStoreFilter.Arg();
        arg.setCounty(county);
        arg.setProductGroupId(productGroupId);
        arg.setAreaAttributeName(areaAttributeName);

        // 模拟GrayRelease的行为
        try (MockedStatic<GrayRelease> mockedStatic = Mockito.mockStatic(GrayRelease.class)) {
            mockedStatic.when(() -> GrayRelease.isAllow("checkin-server-v2", "areaManage", tenantId))
                    .thenReturn(true);

            // 模拟serviceFacade的行为
            IObjectDescribe objectDescribe = mock(IObjectDescribe.class);
            when(serviceFacade.findObject(eq(tenantId), eq(CommonConstants.ACCOUNT_OBJ)))
                    .thenReturn(objectDescribe);

            QueryResult<IObjectData> queryResult = new QueryResult<>();
            List<IObjectData> resultData = new ArrayList<>();
            queryResult.setData(resultData);
            // 由于 setTotalNum 方法不可用，我们使用反射设置 totalNum 字段
            try {
                java.lang.reflect.Field totalNumField = QueryResult.class.getDeclaredField("totalNum");
                totalNumField.setAccessible(true);
                totalNumField.set(queryResult, 0);
            } catch (Exception e) {
                fail("反射设置 totalNum 失败: " + e.getMessage());
            }

            when(serviceFacade.findBySearchQuery(any(User.class), eq(CommonConstants.ACCOUNT_OBJ), any(SearchTemplateQuery.class)))
                    .thenReturn(queryResult);

            // 执行测试
            MapStoreFilter.Result result = controller.doService(arg);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDataList());
            assertTrue(result.getDataList().isEmpty());
        }
    }

    @Test
    public void testDoService_WithNoGrayRelease() {
        // 准备测试数据
        MapStoreFilter.Arg arg = new MapStoreFilter.Arg();
        arg.setCounty(county);
        arg.setProductGroupId(productGroupId);
        arg.setAreaAttributeName(areaAttributeName);

        // 模拟GrayRelease的行为
        try (MockedStatic<GrayRelease> mockedStatic = Mockito.mockStatic(GrayRelease.class)) {
            mockedStatic.when(() -> GrayRelease.isAllow("checkin-server-v2", "areaManage", tenantId))
                    .thenReturn(false);
            mockedStatic.when(() -> GrayRelease.isAllow("checkin-server-v2", "isYLArea", tenantId))
                    .thenReturn(false);

            // 执行测试
            MapStoreFilter.Result result = controller.doService(arg);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDataList());
            assertTrue(result.getDataList().isEmpty());
        }
    }

    @Test
    public void testDoService_WithEmptyCounty() {
        // 准备测试数据
        MapStoreFilter.Arg arg = new MapStoreFilter.Arg();
        arg.setCounty(new ArrayList<>());
        arg.setProductGroupId(productGroupId);
        arg.setAreaAttributeName(areaAttributeName);

        // 模拟GrayRelease的行为
        try (MockedStatic<GrayRelease> mockedStatic = Mockito.mockStatic(GrayRelease.class)) {
            mockedStatic.when(() -> GrayRelease.isAllow("checkin-server-v2", "areaManage", tenantId))
                    .thenReturn(false);
            mockedStatic.when(() -> GrayRelease.isAllow("checkin-server-v2", "isYLArea", tenantId))
                    .thenReturn(true);

            // 执行测试
            MapStoreFilter.Result result = controller.doService(arg);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDataList());
            assertTrue(result.getDataList().isEmpty());
        }
    }
}
