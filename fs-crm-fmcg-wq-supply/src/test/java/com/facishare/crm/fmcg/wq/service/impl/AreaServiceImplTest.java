package com.facishare.crm.fmcg.wq.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.model.PlanRouteInfo;
import com.facishare.crm.fmcg.wq.util.FieldUtils;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;

import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AreaServiceImplTest {

    @Mock
    private ServiceFacade serviceFacade;

    @InjectMocks
    private AreaServiceImpl areaService;

    private final String tenantId = "test-tenant";

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    @Test
    void testConvertPlanRouteInfo_WithEmptyData() {
        // 准备测试数据
        List<IObjectData> data = Collections.emptyList();
        Map<String, IFieldDescribe> fieldDescribeMap = new HashMap<>();
        List<String> showFiledApiNameFromListLayout = new ArrayList<>();

        // 执行测试
        List<PlanRouteInfo> result = areaService.convertPlanRouteInfo(data, fieldDescribeMap, showFiledApiNameFromListLayout);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testConvertPlanRouteInfo_WithData() {
        // 准备测试数据
        List<IObjectData> data = new ArrayList<>();
        IObjectData objectData = mock(IObjectData.class);
        when(objectData.getId()).thenReturn("test-id");
        when(objectData.get("belong_area", String.class, null)).thenReturn("test-area-id");
        data.add(objectData);

        Map<String, IFieldDescribe> fieldDescribeMap = new HashMap<>();
        IFieldDescribe belongAreaField = mock(IFieldDescribe.class);
        // 不再设置未使用的存根
        fieldDescribeMap.put("belong_area", belongAreaField);

        IFieldDescribe routeIdsField = mock(IFieldDescribe.class);
        // 不再设置未使用的存根
        fieldDescribeMap.put("route_ids", routeIdsField);

        IFieldDescribe nameField = mock(IFieldDescribe.class);
        // 不再设置未使用的存根
        fieldDescribeMap.put("name", nameField);

        List<String> showFiledApiNameFromListLayout = new ArrayList<>();

        // 模拟 FieldUtils.getShowStr 方法
        try (MockedStatic<FieldUtils> mockedFieldUtils = Mockito.mockStatic(FieldUtils.class)) {
            mockedFieldUtils.when(() -> FieldUtils.getShowStr(eq(objectData), eq(belongAreaField)))
                    .thenReturn("Test Area");
            mockedFieldUtils.when(() -> FieldUtils.getShowStr(eq(objectData), eq(routeIdsField)))
                    .thenReturn("Test Route");
            mockedFieldUtils.when(() -> FieldUtils.getShowStr(eq(objectData), eq(nameField)))
                    .thenReturn("Test Name");

            // 执行测试
            List<PlanRouteInfo> result = areaService.convertPlanRouteInfo(data, fieldDescribeMap, showFiledApiNameFromListLayout);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
            PlanRouteInfo planRouteInfo = result.get(0);
            assertEquals("test-id", planRouteInfo.get_id());
            assertEquals("test-area-id", planRouteInfo.getAreaId());
            assertEquals("Test Area", planRouteInfo.getAreaName());
            assertEquals("Test Route", planRouteInfo.getRouteId());
            assertNotNull(planRouteInfo.getShowFiledList());
        }
    }

    @Test
    void testBuildShowFieldApiName() {
        // 准备测试数据
        ILayout layout = mock(ILayout.class);
        when(serviceFacade.getLayoutLogicService().findListLayoutByApiName(any(User.class), eq(CommonConstants.Account_Mobile_Layout), eq(CommonConstants.ACCOUNT_OBJ)))
                .thenReturn(layout);

        List<String> listFields = new ArrayList<>(Arrays.asList("name", "location", "custom_field"));

        // 模拟 ObjectUtils.getShowFiledApiNameFromListLayout 方法
        try (MockedStatic<ObjectUtils> mockedObjectUtils = Mockito.mockStatic(ObjectUtils.class)) {
            mockedObjectUtils.when(() -> ObjectUtils.getShowFiledApiNameFromListLayout(layout))
                    .thenReturn(listFields);
            // 执行测试
            List<String> result = areaService.buildShowFieldApiName(tenantId);

            // 验证结果
            assertNotNull(result);
            // 验证我们的测试数据字段存在于结果中
            assertTrue(result.contains("name"));
            assertTrue(result.contains("location"));
            assertTrue(result.contains("custom_field"));
            // 不验证确切的大小，因为它包含 ObjectUtils.fixFields 中的字段

            // 验证方法调用
            verify(serviceFacade).getLayoutLogicService().findListLayoutByApiName(any(User.class), eq(CommonConstants.Account_Mobile_Layout), eq(CommonConstants.ACCOUNT_OBJ));
        }
    }

    @SneakyThrows
    @Test
    void testGetFieldNameOnWaterDrop_WithValidLayout() {
        // 准备测试数据
        ILayout layout = mock(ILayout.class);
        when(serviceFacade.getLayoutLogicService().findListLayoutByApiName(any(User.class), eq(CommonConstants.Account_List_Layout), eq(CommonConstants.ACCOUNT_OBJ)))
                .thenReturn(layout);

        List<IComponent> components = new ArrayList<>();
        IComponent component = mock(IComponent.class);
        components.add(component);
        when(layout.getComponents()).thenReturn(components);

        JSONArray viewInfo = new JSONArray();
        JSONObject viewInfoItem = new JSONObject();
        viewInfoItem.put("name", "map_view");

        // 创建 bubble_info 对象
        JSONObject bubbleInfo = new JSONObject();
        bubbleInfo.put("field", "name");
        viewInfoItem.put("bubble_info", bubbleInfo);

        viewInfo.add(viewInfoItem);

        when(component.get("view_info")).thenReturn(viewInfo);

        // 执行测试
        String result = areaService.getFieldNameOnWaterDrop(tenantId);

        // 验证结果
        assertEquals("name", result);

        // 验证方法调用
        verify(serviceFacade).getLayoutLogicService().findListLayoutByApiName(any(User.class), eq(CommonConstants.Account_List_Layout), eq(CommonConstants.ACCOUNT_OBJ));
    }

    @Test
    void testGetFieldNameOnWaterDrop_WithNullLayout() {
        // 准备测试数据
        when(serviceFacade.getLayoutLogicService().findListLayoutByApiName(any(User.class), eq(CommonConstants.Account_List_Layout), eq(CommonConstants.ACCOUNT_OBJ)))
                .thenReturn(null);

        // 执行测试
        String result = areaService.getFieldNameOnWaterDrop(tenantId);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(serviceFacade).getLayoutLogicService().findListLayoutByApiName(any(User.class), eq(CommonConstants.Account_List_Layout), eq(CommonConstants.ACCOUNT_OBJ));
    }

    @SneakyThrows
    @Test
    void testGetFieldNameOnWaterDrop_WithEmptyComponents() {
        // 准备测试数据
        ILayout layout = mock(ILayout.class);
        when(serviceFacade.getLayoutLogicService().findListLayoutByApiName(any(User.class), eq(CommonConstants.Account_List_Layout), eq(CommonConstants.ACCOUNT_OBJ)))
                .thenReturn(layout);
        when(layout.getComponents()).thenReturn(Collections.emptyList());

        // 执行测试
        String result = areaService.getFieldNameOnWaterDrop(tenantId);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(serviceFacade).getLayoutLogicService().findListLayoutByApiName(any(User.class), eq(CommonConstants.Account_List_Layout), eq(CommonConstants.ACCOUNT_OBJ));
        verify(layout).getComponents();
    }
}
