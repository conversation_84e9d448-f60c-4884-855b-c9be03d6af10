package com.facishare.crm.fmcg.wq.action;

import com.facishare.appserver.checkins.api.model.CheckUserlimit;
import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CoveredStoresConstants;
import com.facishare.crm.fmcg.wq.dao.CheckinsDao;
import com.facishare.crm.fmcg.wq.dao.SupplyDao;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.crm.fmcg.wq.service.OrganizationJMLService;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AreaManageAddActionTest {

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private SupplyDao supplyDao;

    @Mock
    private CheckinsDao checkinsDao;

    @Mock
    private CheckinsProxy checkinsProxy;

    @Mock
    private OrganizationJMLService organizationJMLService;

    @Mock
    private ApplicationContext applicationContext;

    @InjectMocks
    private AreaManageAddAction areaManageAddAction;

    private final String tenantId = "test-tenant";
    private final String ea = "test-ea";

    @BeforeEach
    void setUp() {
        // 设置 ActionContext
        ActionContext actionContext = mock(ActionContext.class);
        when(actionContext.getTenantId()).thenReturn(tenantId);
        when(actionContext.getEa()).thenReturn(ea);
        User user = mock(User.class);
        when(user.getUserId()).thenReturn("test-user-id");
        when(user.getUserIdInt()).thenReturn(123);
        when(actionContext.getUser()).thenReturn(user);

        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field field = StandardAddAction.class.getDeclaredField("actionContext");
            field.setAccessible(true);
            field.set(areaManageAddAction, actionContext);

            field = StandardAddAction.class.getDeclaredField("serviceFacade");
            field.setAccessible(true);
            field.set(areaManageAddAction, serviceFacade);
        } catch (Exception e) {
            fail("Failed to set actionContext: " + e.getMessage());
        }

        // 模拟 SpringUtil.getContext()
        try (MockedStatic<SpringUtil> mockedSpringUtil = Mockito.mockStatic(SpringUtil.class)) {
            mockedSpringUtil.when(SpringUtil::getContext).thenReturn(applicationContext);
            when(applicationContext.getBean(SupplyDao.class)).thenReturn(supplyDao);
            when(applicationContext.getBean(CheckinsDao.class)).thenReturn(checkinsDao);
            when(applicationContext.getBean(CheckinsProxy.class)).thenReturn(checkinsProxy);
            when(applicationContext.getBean(OrganizationJMLService.class)).thenReturn(organizationJMLService);
        }
    }

    @Test
    void testBefore_WithAreaOwner_NoPermission() {
        // 准备测试数据
        StandardAddAction.Arg arg = mock(StandardAddAction.Arg.class);
        ObjectDataDocument objectData = mock(ObjectDataDocument.class);
        when(arg.getObjectData()).thenReturn(objectData);

        List<String> areaOwnerArray = new ArrayList<>();
        areaOwnerArray.add("123");
        when(objectData.get(AreaManageConstants.AREA_OWNER_PRESER)).thenReturn(areaOwnerArray);

        when(serviceFacade.getEAByEI(tenantId)).thenReturn(ea);

        // 模拟 checkinsProxy.userLimitCheck 方法返回无权限
        CheckUserlimit.Result checkUserlimitResult = new CheckUserlimit.Result();
        checkUserlimitResult.setResult(false);
        when(checkinsProxy.userLimitCheck(eq(tenantId), any(CheckUserlimit.Args.class))).thenReturn(checkUserlimitResult);

        // 执行测试并验证异常
        assertThrows(FunctionException.class, () -> {
            areaManageAddAction.before(arg);
        });

        // 验证方法调用
        verify(serviceFacade).getEAByEI(tenantId);
        verify(checkinsProxy).userLimitCheck(eq(tenantId), any(CheckUserlimit.Args.class));
    }

    @Test
    void testBefore_WithAreaOwner_HasPermission_YLAreaGray() {
        // 准备测试数据
        StandardAddAction.Arg arg = mock(StandardAddAction.Arg.class);
        ObjectDataDocument objectData = mock(ObjectDataDocument.class);
        when(arg.getObjectData()).thenReturn(objectData);

        List<String> areaOwnerArray = new ArrayList<>();
        areaOwnerArray.add("123");
        when(objectData.get(AreaManageConstants.AREA_OWNER_PRESER)).thenReturn(areaOwnerArray);
        when(objectData.get(AreaManageConstants.AREA_OWNER)).thenReturn(areaOwnerArray);
        when(objectData.get(AreaManageConstants.COUNTY__C)).thenReturn("test-county");

        when(serviceFacade.getEAByEI(tenantId)).thenReturn(ea);

        // 模拟 checkinsProxy.userLimitCheck 方法返回有权限
        CheckUserlimit.Result checkUserlimitResult = new CheckUserlimit.Result();
        checkUserlimitResult.setResult(true);
        when(checkinsProxy.userLimitCheck(eq(tenantId), any(CheckUserlimit.Args.class))).thenReturn(checkUserlimitResult);

        // 模拟 GrayRelease.isAllow 方法
        try (MockedStatic<GrayRelease> mockedGrayRelease = Mockito.mockStatic(GrayRelease.class);
             MockedStatic<FmcgGray.Checkins.EA> mockedFmcgGray = Mockito.mockStatic(FmcgGray.Checkins.EA.class)) {

            mockedFmcgGray.when(() -> FmcgGray.Checkins.EA.areaManage.gray(ea)).thenReturn(false);
            mockedGrayRelease.when(() -> GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea)).thenReturn(true);

            // 模拟查询结果为空
            QueryResult<IObjectData> queryResult = new QueryResult<>();
            queryResult.setData(new ArrayList<>());
            when(serviceFacade.findBySearchQuery(any(User.class), eq(AreaManageConstants.AREA_MANAGE_OBJ), any(SearchTemplateQuery.class)))
                    .thenReturn(queryResult);

            // 执行测试
            areaManageAddAction.before(arg);

            // 验证方法调用
            verify(serviceFacade).getEAByEI(tenantId);
            verify(checkinsProxy).userLimitCheck(eq(tenantId), any(CheckUserlimit.Args.class));
            verify(serviceFacade).findBySearchQuery(any(User.class), eq(AreaManageConstants.AREA_MANAGE_OBJ), any(SearchTemplateQuery.class));
            verify(objectData, never()).put(eq(AreaManageConstants.DATA_OWN_DEPARTMENT), any());
        }
    }

    @Test
    void testValidateDetailData_WithDuplicateStores() {
        // 由于 validateDetailData 是私有方法，我们需要使用反射来测试
        try {
            // 准备测试数据
            StandardAddAction.Arg arg = mock(StandardAddAction.Arg.class);

            Map<String, List<ObjectDataDocument>> details = new HashMap<>();
            List<ObjectDataDocument> coveredStores = new ArrayList<>();

            ObjectDataDocument store1 = mock(ObjectDataDocument.class);
            when(store1.get(CoveredStoresConstants.STORE)).thenReturn("store-1");
            coveredStores.add(store1);

            ObjectDataDocument store2 = mock(ObjectDataDocument.class);
            when(store2.get(CoveredStoresConstants.STORE)).thenReturn("store-1"); // 重复的门店
            coveredStores.add(store2);

            details.put(CoveredStoresConstants.COVERED_STORES_OBJ, coveredStores);
            when(arg.getDetails()).thenReturn(details);

            // 使用反射调用私有方法
            java.lang.reflect.Method method = AreaManageAddAction.class.getDeclaredMethod("validateDetailData", StandardAddAction.Arg.class);
            method.setAccessible(true);

            // 执行测试并验证异常
            try {
                method.invoke(areaManageAddAction, arg);
                fail("应该抛出异常");
            } catch (java.lang.reflect.InvocationTargetException e) {
                // 获取目标异常
                Throwable targetException = e.getTargetException();
                assertTrue(targetException instanceof ValidateException);
                assertEquals("覆盖门店中含有相同的门店，请检查！", targetException.getMessage());
            }
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }
}
