package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.AreaManageConstants;
import com.facishare.crm.fmcg.wq.constants.CommonConstants;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CoveredStoresMapStoreCountControllerTest {

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private RequestContext requestContext;

    @InjectMocks
    private CoveredStoresMapStoreCountController controller;

    private final String tenantId = "test-tenant";

    @BeforeEach
    void setUp() {
        // 设置控制器上下文
        ControllerContext controllerContext = mock(ControllerContext.class);
        when(controllerContext.getTenantId()).thenReturn(tenantId);
        when(controllerContext.getRequestContext()).thenReturn(requestContext);
        when(controllerContext.getMethodName()).thenReturn("getQueryResult");
        when(controllerContext.getUser()).thenReturn(User.systemUser(tenantId));

        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field field = CoveredStoresMapStoreCountController.class.getDeclaredField("controllerContext");
            field.setAccessible(true);
            field.set(controller, controllerContext);

            field = CoveredStoresMapStoreCountController.class.getDeclaredField("serviceFacade");
            field.setAccessible(true);
            field.set(controller, serviceFacade);
        } catch (Exception e) {
            fail("Failed to set controllerContext: " + e.getMessage());
        }
    }

    @Test
    void testGetQueryResult_Success() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setFilters(new ArrayList<>());

        try (MockedStatic<ObjectUtils> mockedObjectUtils = Mockito.mockStatic(ObjectUtils.class)) {
            // 模拟ObjectUtils.getDataTotal的行为
            mockedObjectUtils.when(() -> ObjectUtils.getDataTotal(eq(serviceFacade), any(User.class), eq(CommonConstants.ACCOUNT_OBJ), any(SearchTemplateQuery.class)))
                    .thenReturn(1000);

            // 执行测试
            QueryResult<IObjectData> result = controller.getQueryResult(query);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());

            IObjectData objectData = result.getData().get(0);
            assertEquals(1000, objectData.get("total"));
            assertEquals(1000, objectData.get("allocated"));
            assertEquals("62ce4114f968446e3e91d4f8", objectData.get("_id"));

            // 验证方法调用
            mockedObjectUtils.verify(() -> ObjectUtils.getDataTotal(eq(serviceFacade), any(User.class), eq(CommonConstants.ACCOUNT_OBJ), any(SearchTemplateQuery.class)), times(2));
        }
    }

    @Test
    void testGetQueryResult_WithAreaFilter() {
        // 准备测试数据
        SearchTemplateQuery query = new SearchTemplateQuery();
        List<IFilter> filters = new ArrayList<>();
        Filter filter = new Filter();
        filter.setFieldName("belong_area");
        filters.add(filter);
        query.setFilters(filters);

        try (MockedStatic<ObjectUtils> mockedObjectUtils = Mockito.mockStatic(ObjectUtils.class)) {
            // 模拟ObjectUtils.getDataTotal的行为
            mockedObjectUtils.when(() -> ObjectUtils.getDataTotal(eq(serviceFacade), any(User.class), eq(CommonConstants.ACCOUNT_OBJ), any(SearchTemplateQuery.class)))
                    .thenReturn(500);

            // 执行测试
            QueryResult<IObjectData> result = controller.getQueryResult(query);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());

            IObjectData objectData = result.getData().get(0);
            assertEquals(500, objectData.get("total"));
            assertEquals(500, objectData.get("allocated"));
        }
    }

    @Test
    void testNeedButtonInfo() {
        assertFalse(controller.needButtonInfo());
    }

    @Test
    void testNeedTagInfo() {
        assertFalse(controller.needTagInfo());
    }

    @Test
    void testNeedSummaryData() {
        assertFalse(controller.needSummaryData(new SearchTemplateQuery()));
    }

    @Test
    void testNeedLayout() {
        assertFalse(controller.needLayout(new SearchTemplateQuery()));
    }
}
