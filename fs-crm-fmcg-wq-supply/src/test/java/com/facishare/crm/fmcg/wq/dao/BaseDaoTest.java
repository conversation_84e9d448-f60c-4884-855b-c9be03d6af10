package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BaseDaoTest {

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private AsyncTaskExecutor fmcgSyncTaskExecutor;

    @Mock
    private AsyncTaskExecutor fmcgThreadPoolExecutor;

    @InjectMocks
    private BaseDao baseDao;

    private final String tenantId = "test-tenant";
    private final String apiName = "TestObject";
    private final String objectId = "test-object-id";

    @Before
    public void setUp() {
        // 通用设置
    }

    @Test
    public void testGetById_WithValidId() {
        // 准备测试数据
        IObjectData mockObjectData = mock(IObjectData.class);
        List<IObjectData> mockObjectDataList = Collections.singletonList(mockObjectData);
        
        // 模拟serviceFacade的行为
        when(serviceFacade.findObjectDataByIds(eq(tenantId), eq(Collections.singletonList(objectId)), eq(apiName)))
                .thenReturn(mockObjectDataList);
        
        // 执行测试
        IObjectData result = baseDao.getById(tenantId, apiName, objectId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(mockObjectData, result);
        
        // 验证方法调用
        verify(serviceFacade).findObjectDataByIds(eq(tenantId), eq(Collections.singletonList(objectId)), eq(apiName));
    }

    @Test
    public void testGetById_WithNullId() {
        // 执行测试
        IObjectData result = baseDao.getById(tenantId, apiName, null);
        
        // 验证结果
        assertNull(result);
        
        // 验证方法调用
        verify(serviceFacade, never()).findObjectDataByIds(anyString(), anyList(), anyString());
    }

    @Test
    public void testGetById_WithEmptyId() {
        // 执行测试
        IObjectData result = baseDao.getById(tenantId, apiName, "");
        
        // 验证结果
        assertNull(result);
        
        // 验证方法调用
        verify(serviceFacade, never()).findObjectDataByIds(anyString(), anyList(), anyString());
    }

    @Test
    public void testGetByIds_WithValidIds() {
        // 准备测试数据
        List<String> ids = Arrays.asList("id1", "id2");
        List<IObjectData> mockObjectDataList = new ArrayList<>();
        IObjectData mockObjectData1 = mock(IObjectData.class);
        IObjectData mockObjectData2 = mock(IObjectData.class);
        mockObjectDataList.add(mockObjectData1);
        mockObjectDataList.add(mockObjectData2);
        
        // 模拟serviceFacade的行为
        when(serviceFacade.findObjectDataByIds(eq(tenantId), eq(ids), eq(apiName)))
                .thenReturn(mockObjectDataList);
        
        // 执行测试
        List<IObjectData> result = baseDao.getByIds(tenantId, apiName, ids);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(mockObjectDataList, result);
        
        // 验证方法调用
        verify(serviceFacade).findObjectDataByIds(eq(tenantId), eq(ids), eq(apiName));
    }

    @Test
    public void testGetByIds_WithEmptyIds() {
        // 执行测试
        List<IObjectData> result = baseDao.getByIds(tenantId, apiName, Collections.emptyList());
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证方法调用
        verify(serviceFacade, never()).findObjectDataByIds(anyString(), anyList(), anyString());
    }

    @Test
    public void testGetByIds_WithNullIds() {
        // 执行测试
        List<IObjectData> result = baseDao.getByIds(tenantId, apiName, null);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证方法调用
        verify(serviceFacade, never()).findObjectDataByIds(anyString(), anyList(), anyString());
    }
}
