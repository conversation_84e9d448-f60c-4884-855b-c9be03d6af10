package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardChangeOwnerAction;
import com.fxiaoke.common.release.GrayRelease;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AreaManageChangeOwnerActionTest {

    @Mock
    private ServiceFacade serviceFacade;

    @InjectMocks
    private AreaManageChangeOwnerAction areaManageChangeOwnerAction;

    private final String tenantId = "test-tenant";
    private final String ea = "test-ea";

    @BeforeEach
    void setUp() {
        // 设置 ActionContext
        ActionContext actionContext = mock(ActionContext.class);
        when(actionContext.getTenantId()).thenReturn(tenantId);
        when(actionContext.getEa()).thenReturn(ea);
        User user = mock(User.class);
        when(user.getUserId()).thenReturn("test-user-id");
        when(actionContext.getUser()).thenReturn(user);

        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field field = StandardChangeOwnerAction.class.getDeclaredField("actionContext");
            field.setAccessible(true);
            field.set(areaManageChangeOwnerAction, actionContext);

            field = StandardChangeOwnerAction.class.getDeclaredField("serviceFacade");
            field.setAccessible(true);
            field.set(areaManageChangeOwnerAction, serviceFacade);
        } catch (Exception e) {
            fail("Failed to set actionContext: " + e.getMessage());
        }
    }

    @Test
    void testBefore_WithGrayRelease() {
        // 准备测试数据
        StandardChangeOwnerAction.Arg arg = mock(StandardChangeOwnerAction.Arg.class);

        // 模拟 GrayRelease.isAllow 方法
        try (MockedStatic<GrayRelease> mockedGrayRelease = Mockito.mockStatic(GrayRelease.class)) {
            mockedGrayRelease.when(() -> GrayRelease.isAllow("checkin-server-v2", "areaManage", ea)).thenReturn(true);
            mockedGrayRelease.when(() -> GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea)).thenReturn(false);

            // 使用反射调用 before 方法
            try {
                // 由于 before 方法是 protected 的，我们需要使用反射
                java.lang.reflect.Method beforeMethod = StandardChangeOwnerAction.class.getDeclaredMethod("before", StandardChangeOwnerAction.Arg.class);
                beforeMethod.setAccessible(true);
                beforeMethod.invoke(areaManageChangeOwnerAction, arg);

                // 如果没有异常，测试通过
                assertTrue(true, "成功调用 before 方法");
            } catch (Exception e) {
                fail("反射调用失败: " + e.getMessage());
            }
        }
    }

    @Test
    void testGrayReleaseLogic() {
        // 测试灰度发布逻辑
        try (MockedStatic<GrayRelease> mockedGrayRelease = Mockito.mockStatic(GrayRelease.class)) {
            // 测试 areaManage 为 true 的情况
            mockedGrayRelease.when(() -> GrayRelease.isAllow("checkin-server-v2", "areaManage", ea)).thenReturn(true);
            mockedGrayRelease.when(() -> GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea)).thenReturn(false);

            assertTrue(GrayRelease.isAllow("checkin-server-v2", "areaManage", ea));
            assertFalse(GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea));

            // 测试 isYLArea 为 true 的情况
            mockedGrayRelease.when(() -> GrayRelease.isAllow("checkin-server-v2", "areaManage", ea)).thenReturn(false);
            mockedGrayRelease.when(() -> GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea)).thenReturn(true);

            assertFalse(GrayRelease.isAllow("checkin-server-v2", "areaManage", ea));
            assertTrue(GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea));

            // 测试两者都为 false 的情况
            mockedGrayRelease.when(() -> GrayRelease.isAllow("checkin-server-v2", "areaManage", ea)).thenReturn(false);
            mockedGrayRelease.when(() -> GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea)).thenReturn(false);

            assertFalse(GrayRelease.isAllow("checkin-server-v2", "areaManage", ea));
            assertFalse(GrayRelease.isAllow("checkin-server-v2", "isYLArea", ea));
        }
    }
}
