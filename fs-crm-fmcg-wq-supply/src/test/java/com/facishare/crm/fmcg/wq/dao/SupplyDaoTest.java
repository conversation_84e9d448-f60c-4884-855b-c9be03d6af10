package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.DealerSupplyObjConstants;
import com.facishare.crm.fmcg.wq.constants.ProductCollectionObjConstants;
import com.facishare.crm.fmcg.wq.model.obj.ProductCollectionObj;
import com.facishare.crm.fmcg.wq.service.SupplyService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("SupplyDao测试")
class SupplyDaoTest {

    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private SupplyService supplyService;

    @InjectMocks
    private SupplyDao supplyDao;

    private final String tenantId = "test-tenant";
    private final String supplierId = "test-supplier";

    @BeforeEach
    void setUp() {
        // 通用设置
    }

    @Test
    @DisplayName("测试getProductCollectionObjByAccountId方法")
    void testGetProductCollectionObjByAccountId() {
        // 准备测试数据
        ProductCollectionObj expectedObj = new ProductCollectionObj();

        // 模拟getProductCollectionObjListByAccountIds方法的行为
        List<ProductCollectionObj> mockList = Collections.singletonList(expectedObj);

        // 使用spy来部分模拟supplyDao
        SupplyDao spyDao = spy(supplyDao);
        doReturn(mockList).when(spyDao).getProductCollectionObjListByAccountIds(
                eq(tenantId), eq(Lists.newArrayList(supplierId)), anyBoolean());

        // 执行测试
        ProductCollectionObj result = spyDao.getProductCollectionObjByAccountId(tenantId, supplierId, true);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedObj, result);

        // 验证方法调用
        verify(spyDao).getProductCollectionObjListByAccountIds(
                tenantId, Lists.newArrayList(supplierId), true);
    }

    @Test
    @DisplayName("测试getProductCollectionObjListByAccountIds方法")
    void testGetProductCollectionObjListByAccountIds() {
        // 准备测试数据
        List<String> supplierIds = Lists.newArrayList(supplierId, "supplier2");
        List<IObjectData> mockProductCollections = new ArrayList<>();

        IObjectData productCollection1 = mock(IObjectData.class);
        when(productCollection1.get(ProductCollectionObjConstants.Field.dealerId.getApiName())).thenReturn(supplierId);
        when(productCollection1.getId()).thenReturn("pc1");
        mockProductCollections.add(productCollection1);

        IObjectData productCollection2 = mock(IObjectData.class);
        when(productCollection2.get(ProductCollectionObjConstants.Field.dealerId.getApiName())).thenReturn("supplier2");
        when(productCollection2.getId()).thenReturn("pc2");
        mockProductCollections.add(productCollection2);

        // 模拟serviceFacade的行为
        QueryResult<IObjectData> queryResult = new QueryResult<>();
        queryResult.setData(mockProductCollections);
        when(serviceFacade.findBySearchQuery(any(User.class), eq(ProductCollectionObjConstants.API_NAME), any(SearchTemplateQuery.class)))
                .thenReturn(queryResult);

        // 执行测试
        List<ProductCollectionObj> result = supplyDao.getProductCollectionObjListByAccountIds(tenantId, supplierIds, false);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证方法调用
        verify(serviceFacade).findBySearchQuery(any(User.class), eq(ProductCollectionObjConstants.API_NAME), any(SearchTemplateQuery.class));
    }

    @Test
    @DisplayName("测试getById方法")
    void testGetById() {
        // 准备测试数据
        String objectId = "test-object-id";
        IObjectData mockObjectData = mock(IObjectData.class);

        // 由于 findById 方法已弃用，我们使用更通用的方式模拟
        // 使用 doReturn...when 语法，避免参数类型问题
        // 注意：在实际项目中应该使用推荐的替代方法
        doReturn(mockObjectData).when(serviceFacade).findById(any(), any(), any());

        // 执行测试
        IObjectData result = supplyDao.getById(tenantId, DealerSupplyObjConstants.API_NAME, objectId);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockObjectData, result);
    }
}
