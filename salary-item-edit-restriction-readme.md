# 工资项编辑限制功能说明

## 功能概述

当工资项被员工固定工资表选为从对象时，为了保证数据一致性和业务逻辑的完整性，系统将限制工资项的编辑权限，只允许编辑名称和工资项说明字段。

## 编辑规则

### 1. 完全可编辑场景
当工资项满足以下条件时，支持编辑全部字段：
- 未被任何工资规则使用
- 未被任何员工固定工资表使用

### 2. 限制编辑场景
当工资项满足以下任一条件时，仅可编辑「工资项名称」和「工资项说明」：
- 被工资规则使用
- 被员工固定工资表使用
- 同时被工资规则和员工固定工资表使用

### 3. 允许编辑的字段
在限制编辑场景下，只允许编辑以下字段：
- **工资项名称** (`name`)
- **工资项说明** (`SALARY_ITEM_DESCRIPTION`)

### 4. 禁止编辑的字段
在限制编辑场景下，以下字段将被自动重置，无法修改：
- 增减属性 (`INCREMENT_DECREMENT_ATTRIB`)
- 计算公式 (`CALCULATION_FORMULA`)
- 计算类型 (`CALCULATION_TYPE`)
- 工资项类型 (`SALARY_ITEM_TYPE`)
- 其他所有业务字段

## 技术实现

### 1. 使用情况检查
系统会检查两个维度的使用情况：

#### 工资规则使用检查
```java
List<IObjectData> usingSalaryRules = salaryRuleDao.getSalaryRulesBySalaryItemId(tenantId, salaryItemId);
boolean isUsedBySalaryRules = CollectionUtils.isNotEmpty(usingSalaryRules);
```

#### 员工固定工资表使用检查
```java
List<IObjectData> usingEmployeeFixedSalaryDetails = employeeFixedSalaryDetailDao.getBySalaryItemId(tenantId, salaryItemId);
boolean isUsedByEmployeeFixedSalary = CollectionUtils.isNotEmpty(usingEmployeeFixedSalaryDetails);
```

### 2. 字段重置逻辑
```java
if (isUsedBySalaryRules || isUsedByEmployeeFixedSalary) {
    // 检查并重置不允许编辑的字段
    for (String fieldName : objectData.getChangedFields()) {
        if (!EDITABLE_FIELDS_WHEN_USED.contains(fieldName)) {
            objectData.resetField(fieldName);
            log.info("工资项 {} 字段 {} 被重置，因为工资项已被使用", salaryItemId, fieldName);
        }
    }
}
```

### 3. 新增DAO方法
在 `EmployeeFixedSalaryDetailDao` 中新增方法：
```java
/**
 * 根据工资项ID查询使用该工资项的员工固定工资明细
 */
public List<IObjectData> getBySalaryItemId(String tenantId, String salaryItemId) {
    return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
            .eq(EmployeeFixedSalaryDetailFields.SALARY_ITEM, salaryItemId)
            .build(), EmployeeFixedSalaryDetailFields.API_NAME, employeeFixedSalaryDetailFields);
}
```

## 使用场景示例

### 场景1：工资项未被使用
**情况**：新创建的工资项，未被任何对象引用
**结果**：可以编辑所有字段
**日志**：`工资项 xxx 未被工资规则或员工固定工资表使用，允许编辑全部字段`

### 场景2：工资项被工资规则使用
**情况**：工资项被某个工资规则引用
**结果**：只能编辑名称和说明
**日志**：`工资项 xxx 已被 1 个工资规则和 0 个员工固定工资表使用，限制编辑字段`

### 场景3：工资项被员工固定工资表使用
**情况**：工资项被员工固定工资明细引用
**结果**：只能编辑名称和说明
**日志**：`工资项 xxx 已被 0 个工资规则和 3 个员工固定工资表使用，限制编辑字段`

### 场景4：工资项被多个对象使用
**情况**：工资项同时被工资规则和员工固定工资表引用
**结果**：只能编辑名称和说明
**日志**：`工资项 xxx 已被 2 个工资规则和 5 个员工固定工资表使用，限制编辑字段`

## 详细使用信息记录

系统会记录工资项的详细使用情况：

### 使用信息格式
```
工资项 xxx 被以下对象使用: 工资规则: 基础工资规则、销售工资规则；员工固定工资表: 15条记录
```

### 日志级别
- **INFO级别**：记录编辑限制的基本信息
- **DEBUG级别**：记录详细的字段重置信息

## 用户体验

### 1. 前端表现
- 被限制的字段在编辑时会被自动重置
- 用户看到的是最终允许修改的字段值
- 不会显示错误提示，而是静默处理

### 2. 操作流程
1. 用户打开工资项编辑页面
2. 修改各种字段（包括被限制的字段）
3. 点击保存
4. 系统自动重置被限制的字段
5. 只保存允许修改的字段变更

### 3. 数据一致性
- 确保工资项的核心属性不会因为编辑而影响已有的业务数据
- 保证工资规则和员工固定工资表的计算逻辑不受影响
- 维护系统数据的完整性和一致性

## 注意事项

### 1. 性能考虑
- 每次编辑都会查询使用情况，对于大量数据可能有性能影响
- 建议在必要时添加缓存机制

### 2. 业务影响
- 名称修改会影响显示，但不影响业务逻辑
- 说明修改只影响用户理解，不影响计算

### 3. 扩展性
- 如需增加允许编辑的字段，修改 `EDITABLE_FIELDS_WHEN_USED` 常量
- 如需增加使用情况检查维度，在 `before` 方法中添加相应逻辑

## 测试建议

### 1. 功能测试
- 测试未被使用的工资项编辑
- 测试被工资规则使用的工资项编辑
- 测试被员工固定工资表使用的工资项编辑
- 测试同时被多个对象使用的工资项编辑

### 2. 边界测试
- 测试空的使用列表
- 测试null的使用列表
- 测试DAO查询异常情况

### 3. 性能测试
- 测试大量使用记录时的查询性能
- 测试并发编辑时的表现

## 相关文件

### 1. 主要实现文件
- `SalaryItemEditAction.java` - 主要编辑逻辑
- `EmployeeFixedSalaryDetailDao.java` - 新增查询方法

### 2. 测试文件
- `SalaryItemEditActionEmployeeFixedSalaryTest.java` - 员工固定工资表场景测试

### 3. 常量文件
- `SalaryItemFields.java` - 工资项字段常量
- `EmployeeFixedSalaryDetailFields.java` - 员工固定工资明细字段常量

## 版本历史

### v1.0 (当前版本)
- 支持工资规则使用情况检查
- 新增员工固定工资表使用情况检查
- 完善日志记录和使用信息统计
- 添加完整的单元测试覆盖

### 未来规划
- 考虑添加缓存机制提升性能
- 考虑添加前端提示机制
- 考虑支持更细粒度的编辑权限控制
