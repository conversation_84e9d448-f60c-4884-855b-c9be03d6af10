# 工资系统Action操作限制实现文档

## 概述

本文档描述了工资系统中工资条、工资条明细、工资发放单的新增、编辑、作废、批量作废操作的限制实现。所有这些操作都在before方法中抛出异常，阻止前端直接执行。

## 实现的Action限制

### 1. 工资条 (SalaryData) 操作限制

#### SalaryDataAddAction - 工资条新增限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryDataAddAction.java`

**限制原因**: 工资条应该通过工资发放单自动生成，不允许前端直接新增
**错误消息**: "不允许直接新增工资条，请通过工资发放单生成"

#### SalaryDataEditAction - 工资条编辑限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryDataEditAction.java`

**限制原因**: 工资条数据应该通过工资条明细修正功能进行修改
**错误消息**: "不允许直接编辑工资条，请通过工资条明细修正功能进行修改"

#### SalaryDataInvalidAction - 工资条作废限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryDataInvalidAction.java`

**限制原因**: 工资条的作废应该通过工资发放单管理
**错误消息**: "不允许直接作废工资条，请通过工资发放单管理"

#### SalaryDataBulkInvalidAction - 工资条批量作废限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryDataBulkInvalidAction.java`

**限制原因**: 工资条的批量作废应该通过工资发放单管理
**错误消息**: "不允许直接批量作废工资条，请通过工资发放单管理"

### 2. 工资条明细 (SalaryDetailData) 操作限制

#### SalaryDetailDataAddAction - 工资条明细新增限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryDetailDataAddAction.java`

**限制原因**: 工资条明细应该通过工资发放单自动生成
**错误消息**: "不允许直接新增工资条明细，请通过工资发放单生成"

#### SalaryDetailDataEditAction - 工资条明细编辑限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryDetailDataEditAction.java`

**限制原因**: 工资条明细的编辑应该通过专门的修正数据功能
**错误消息**: "不允许直接编辑工资条明细，请使用修正数据功能"

#### SalaryDetailDataInvalidAction - 工资条明细作废限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryDetailDataInvalidAction.java`

**限制原因**: 工资条明细的作废应该通过工资发放单管理
**错误消息**: "不允许直接作废工资条明细，请通过工资发放单管理"

#### SalaryDetailDataBulkInvalidAction - 工资条明细批量作废限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryDetailDataBulkInvalidAction.java`

**限制原因**: 工资条明细的批量作废应该通过工资发放单管理
**错误消息**: "不允许直接批量作废工资条明细，请通过工资发放单管理"

### 3. 工资发放单 (SalaryPaymentSlip) 操作限制

#### SalaryPaymentSlipAddAction - 工资发放单新增限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryPaymentSlipAddAction.java`

**限制原因**: 工资发放单应该由系统根据工资规则自动生成
**错误消息**: "不允许直接新增工资发放单，请通过系统自动生成"

#### SalaryPaymentSlipEditAction - 工资发放单编辑限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryPaymentSlipEditAction.java`

**限制原因**: 工资发放单的编辑应该通过系统管理功能
**错误消息**: "不允许直接编辑工资发放单，请通过系统管理功能进行操作"

#### SalaryPaymentSlipInvalidAction - 工资发放单作废限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryPaymentSlipInvalidAction.java`

**限制原因**: 工资发放单的作废应该通过系统管理功能
**错误消息**: "不允许直接作废工资发放单，请通过系统管理功能进行操作"

#### SalaryPaymentSlipBulkInvalidAction - 工资发放单批量作废限制
**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryPaymentSlipBulkInvalidAction.java`

**限制原因**: 工资发放单的批量作废应该通过系统管理功能
**错误消息**: "不允许直接批量作废工资发放单，请通过系统管理功能进行操作"

## 技术实现

### 实现方式

所有限制都通过在Action的`before`方法中抛出`ValidateException`异常来实现：

```java
@Override
protected void before(Arg arg) {
    log.error("操作被阻止的日志信息");
    throw new ValidateException("用户友好的错误消息"); //ignoreI18n
}
```

### 继承关系

- **新增Action**: 继承自 `FmcgSkipPermissionAddAction`
- **编辑Action**: 继承自 `FmcgSkipPermissionEditAction`
- **作废Action**: 继承自 `StandardInvalidAction`
- **批量作废Action**: 继承自 `StandardBulkInvalidAction`

### 异常处理

- **异常类型**: `ValidateException`
- **日志级别**: ERROR
- **国际化**: 所有错误消息都标记了 `//ignoreI18n` 注释

## 业务逻辑

### 数据流控制

1. **工资发放单** → **工资条** → **工资条明细**
   - 数据应该按照这个顺序由系统自动生成
   - 不允许前端直接操作任何环节

2. **修正流程**
   - 工资条明细的修正通过专门的 `SalaryDetailDataCorrectionAction`
   - 其他修改通过系统管理功能

3. **作废流程**
   - 所有作废操作都应该通过上级对象管理
   - 工资条和工资条明细通过工资发放单管理
   - 工资发放单通过系统管理功能

### 权限控制

- 阻止前端用户直接操作核心工资数据
- 确保数据一致性和业务规则遵循
- 防止误操作和数据损坏

## 用户体验

### 错误提示

所有错误消息都提供了：
1. **明确的禁止原因**
2. **正确的操作指引**
3. **用户友好的语言**

### 操作指引

- 新增操作 → 指向系统自动生成
- 编辑操作 → 指向专门的修正功能或系统管理
- 作废操作 → 指向上级对象管理或系统管理

## 日志记录

### 日志内容

每个被阻止的操作都会记录：
- **操作类型**: 新增/编辑/作废/批量作废
- **对象类型**: 工资条/工资条明细/工资发放单
- **阻止原因**: 不允许前端直接操作

### 日志格式

```
log.error("{对象}_{操作}操作被阻止，不允许前端直接{操作}{对象}");
```

## 扩展性

### 新增限制

如果需要添加新的操作限制：
1. 创建对应的Action类
2. 继承适当的基类
3. 在before方法中抛出ValidateException
4. 添加适当的日志记录

### 修改限制

如果需要修改现有限制：
1. 修改错误消息内容
2. 调整日志记录
3. 更新文档说明

## 测试建议

### 功能测试

1. **前端操作测试**
   - 验证所有被限制的操作都会抛出异常
   - 验证错误消息的正确性

2. **日志测试**
   - 验证错误日志的记录
   - 验证日志内容的准确性

3. **异常处理测试**
   - 验证异常类型的正确性
   - 验证异常消息的用户友好性

### 集成测试

1. **业务流程测试**
   - 验证正确的操作流程仍然可用
   - 验证系统自动生成功能正常

2. **权限测试**
   - 验证不同用户角色的操作限制
   - 验证管理员权限的正确性

## 总结

通过实现这些Action操作限制，系统能够：

1. **保护数据完整性**: 防止前端直接操作核心工资数据
2. **规范操作流程**: 引导用户使用正确的操作方式
3. **提高系统安全性**: 减少误操作和数据损坏风险
4. **改善用户体验**: 提供清晰的错误提示和操作指引
5. **便于维护管理**: 集中控制数据操作权限

所有限制都遵循统一的实现模式，便于维护和扩展。错误消息提供了明确的操作指引，帮助用户理解正确的操作方式。
