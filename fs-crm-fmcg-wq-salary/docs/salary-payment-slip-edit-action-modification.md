# 工资发放单编辑Action修改文档

## 修改概述

本次修改对 `SalaryPaymentSlipEditAction` 类进行了重构，从完全阻止编辑改为允许有限制的编辑操作。

## 修改内容

### 1. 继承关系变更

**修改前：**
```java
public class SalaryPaymentSlipEditAction extends FmcgUnsupportedExceptionAction
```

**修改后：**
```java
public class SalaryPaymentSlipEditAction extends StandardEditAction
```

### 2. 编辑权限调整

**修改前：**
- 完全阻止工资发放单的编辑操作
- 任何编辑尝试都会抛出异常

**修改后：**
- 允许编辑发放说明字段（`PAY_DESCRIPTION`）
- 不受发放状态限制
- 其他字段仍然不允许修改

### 3. 字段验证逻辑

#### 允许编辑的字段
```java
private static final Set<String> ALLOWED_EDIT_FIELDS = Sets.newHashSet(
    SalaryPaymentSlipFields.PAY_DESCRIPTION // 发放说明
);
```

#### 系统字段（自动忽略）
```java
private static final Set<String> SYSTEM_FIELDS = Sets.newHashSet(
    "_id", "tenant_id", "owner", "created_by", "last_modified_by", 
    "create_time", "last_modified_time", "life_status", "record_type",
    "data_own_department", "owner_department", "relevant_team", "name"
);
```

#### 禁止编辑的字段
- `PAY_STATUS`（发放状态）
- `START_DATE`（开始时间）
- `END_DATE`（结束时间）
- `SALARY_RULE`（工资规则）
- `PAY_PERIOD`（发薪期间）
- `SALARY_STATEMENT_HEADER`（工资条表头）
- 其他所有非系统字段和非允许字段

### 4. 验证流程

1. **获取原始数据**：从数据库查询当前工资发放单的原始数据
2. **字段遍历验证**：遍历所有传入的字段
3. **字段分类处理**：
   - 系统字段：跳过验证
   - 允许编辑字段：跳过验证
   - 其他字段：使用 `FieldValidationUtil.validateFieldNotChanged` 验证是否被修改
4. **异常抛出**：如果检测到不允许的字段被修改，抛出 `ValidateException`

### 5. 错误信息

当用户尝试修改不被允许的字段时，会收到以下错误信息：
```
工资发放单编辑时只允许修改发放说明字段
```

## 技术实现细节

### 核心方法

#### `before(Arg arg)`
- 主要的验证入口点
- 获取租户ID和工资发放单ID
- 查询原始数据
- 调用字段验证方法

#### `validateOnlyPayDescriptionCanBeModified(Arg arg, IObjectData originalData)`
- 核心验证逻辑
- 遍历所有字段并进行分类验证
- 使用工具类进行字段值比较

#### `getAllFieldNames()`
- 获取所有需要验证的字段名
- 包括预定义的工资发放单字段和前端传入的字段

### 依赖注入

```java
private SalaryPaymentSlipDao salaryPaymentSlipDao = SpringUtil.getContext().getBean(SalaryPaymentSlipDao.class);
```

### 工具类使用

```java
FieldValidationUtil.validateFieldNotChanged(objectDescribe, fieldName, newValue, originalValue, reason);
```

## 状态支持

### 支持的发放状态
- 生成异常（`PAY_STATUS_Options_ERROR`）
- 正在生成（`PAY_STATUS_Options_0`）
- 未发放（`PAY_STATUS_Options_1`）
- 发放中（`PAY_STATUS_Options_2`）
- 发放异常（`PAY_STATUS_Options_3`）
- 已发放（`PAY_STATUS_Options_4`）

**注意：** 与其他操作不同，编辑发放说明不受发放状态限制，即使在已发放状态下也可以修改发放说明。

## 测试覆盖

创建了 `SalaryPaymentSlipEditActionTest` 测试类，覆盖以下场景：
1. 允许编辑发放说明字段
2. 阻止编辑其他字段
3. 忽略系统字段的修改
4. 处理不存在的工资发放单
5. 验证字段配置的正确性

## 兼容性

### 向后兼容性
- 保持了原有的安全性，不允许修改关键业务字段
- 只开放了发放说明字段的编辑权限
- 不影响其他工资发放单相关的操作

### API兼容性
- 继承自 `StandardEditAction`，保持标准的编辑Action接口
- 使用标准的 `before(Arg arg)` 方法进行验证
- 错误处理机制与其他编辑Action保持一致

## 使用示例

### 允许的操作
```json
{
  "object_data_id": "salary_payment_slip_id",
  "pay_description": "更新后的发放说明"
}
```

### 不允许的操作
```json
{
  "object_data_id": "salary_payment_slip_id",
  "pay_status": "4",  // 尝试修改发放状态 - 会被阻止
  "pay_description": "更新后的发放说明"
}
```

## 日志记录

- 开始处理时记录工资发放单ID
- 验证通过时记录成功信息
- 使用 `@Slf4j` 注解进行日志管理

## 注意事项

1. **数据一致性**：修改发放说明不会影响工资计算和发放流程
2. **权限控制**：仍然受到标准的权限验证机制约束
3. **审计跟踪**：所有修改操作都会被记录在系统审计日志中
4. **并发安全**：使用标准的编辑Action框架，具备并发控制能力
