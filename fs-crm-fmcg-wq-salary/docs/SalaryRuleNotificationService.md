# 工资规则消息通知服务

## 概述

工资规则消息通知服务（SalaryRuleNotificationService）用于处理工资规则相关的消息推送，包括三种主要场景：

1. **新建工资规则后通知**：对适用范围中没有创建员工固定工资表的员工进行消息推送
2. **组织架构新增员工通知**：当部门/角色有新增人员时，推送消息提醒创建固定工资表
3. **定薪方式不一致通知**：当员工固定工资表与工资规则定薪方式不一致时发送提醒

## 核心组件

### 1. SalaryRuleNotificationService 接口

主要方法：
- `notifyMissingEmployeeFixedSalaryForNewRule()` - 新建工资规则后通知
- `notifyNewEmployeeNeedsFixedSalary()` - 新增员工通知
- `notifySalaryMethodMismatch()` - 定薪方式不一致通知
- `batchNotifySalaryMethodMismatch()` - 批量检查定薪方式不一致
- `checkSalaryMethodConsistency()` - 检查定薪方式一致性

### 2. SalaryRuleNotificationServiceImpl 实现类

负责具体的通知逻辑实现，包括：
- 消息内容构建
- 消息发送（支持内部员工和外部员工）
- 员工信息查询
- 部门角色信息获取

### 3. OrganizationChangeListener 监听器

处理组织架构变更事件：
- `handleNewEmployeeAdded()` - 处理新增员工事件
- `handleDepartmentEmployeeAdded()` - 处理部门人员变更
- `handleRoleEmployeeAdded()` - 处理角色人员变更
- `triggerNewEmployeeCheck()` - 手动触发检查

### 4. SalaryRuleNotificationController 控制器

提供消息发送接口，供其他服务调用（只负责发送消息，不包含规则适配逻辑）：
- `TriggerMissingFixedSalaryNotification` - 触发工资规则通知
- `TriggerNewEmployeeNotification` - 触发新员工通知
- `BatchCheckSalaryMethodMismatch` - 批量检查定薪方式不一致

### 5. SalaryNotificationMessageUtil 国际化工具

提供消息模板的国际化支持：
- 支持中英文消息模板
- 消息参数化替换
- 用户语言环境获取

## 消息推送文案

### 消息接收人
- **接收人**：内部CRM管理员和PMM管理员
- **发送方式**：文本卡片消息，支持跳转到处理页面

### 国际化支持
消息内容支持中英文国际化：
- 中文（zh_CN）：默认语言
- 英文（en_US）：英文环境

### 情况一：新建工资规则后通知
**中文**：
```
××工资规则适用范围"××部/××角色"员工××未创建员工固定工资表，请尽快创建，创建后方可参与工资计算
```
**英文**：
```
Employee {2} in the scope "{1}" of salary rule {0} has not created an employee fixed salary table. Please create it as soon as possible to participate in salary calculation
```

### 情况二：组织架构新增员工通知
**中文**：
```
××部/××角色新增了员工××，因该部门/角色有生效的工资规则，请尽快为新员工创建固定工资表，方可参与工资计算
```
**英文**：
```
{0} has added employee {1}. Since this department/role has effective salary rules, please create a fixed salary table for the new employee as soon as possible to participate in salary calculation
```

### 情况三：定薪方式不一致通知
**中文**：
```
员工××【员工固定工资表】与适用的【工资规则】定薪方式不一致，将无法参与工资计算，请尽快调整
```
**英文**：
```
Employee {0}'s [Employee Fixed Salary Table] is inconsistent with the applicable [Salary Rule] salary method and will not be able to participate in salary calculation. Please adjust as soon as possible
```

## 集成方式

### 1. 自动集成

#### 新建工资规则自动通知
在 `SalaryRuleAddAction.processNewSalaryRuleApplicableScope()` 方法中已集成，新建工资规则后会自动发送通知。

#### 组织架构变更监听
需要在组织架构变更的地方调用 `OrganizationChangeListener` 的相关方法。

### 2. 手动触发

#### 通过控制器接口
```java
// 触发新建工资规则通知
TriggerMissingFixedSalaryNotification.Arg arg = new TriggerMissingFixedSalaryNotification.Arg();
arg.setTenantId("123456");
arg.setSalaryRuleId("rule001");
// 调用控制器方法

// 触发新员工通知
TriggerNewEmployeeNotification.Arg arg = new TriggerNewEmployeeNotification.Arg();
arg.setTenantId("123456");
arg.setEmployeeId("emp001");
arg.setExternal(false);
// 调用控制器方法
```

#### 直接调用服务
```java
@Autowired
private SalaryRuleNotificationService salaryRuleNotificationService;

// 发送新建工资规则通知
int count = salaryRuleNotificationService.notifyMissingEmployeeFixedSalaryForNewRule(tenantId, salaryRuleId);

// 发送新员工通知
boolean sent = salaryRuleNotificationService.notifyNewEmployeeNeedsFixedSalary(tenantId, employeeId, isExternal);

// 检查定薪方式不一致
boolean consistent = salaryRuleNotificationService.checkSalaryMethodConsistency(tenantId, employeeId, isExternal);
if (!consistent) {
    salaryRuleNotificationService.notifySalaryMethodMismatch(tenantId, employeeId);
}
```

## 消息发送机制

### 接收人机制
- **接收人**：内部CRM管理员和PMM管理员（不再发送给具体员工）
- **获取方式**：通过 `getAdminUserIds()` 方法获取管理员ID列表
- **发送方式**：统一使用文本卡片消息发送给管理员

### 消息格式
- **消息类型**：文本卡片消息（SendTextCardMessageArg）
- **发送方法**：`RestSendMessageService.sendTextCardMessageWithRetry()`
- **消息结构**：标题 + 内容 + 按钮（支持国际化）
- **跳转链接**：点击后跳转到员工固定工资表创建页面

## 异步处理

为避免阻塞主流程，大批量通知采用异步处理：
```java
CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
    return sendMissingFixedSalaryNotifications(tenantId, salaryRule, missingEmployeeIds);
});
```

## 错误处理

- 所有方法都包含完善的异常处理
- 消息发送失败不会影响主业务流程
- 详细的日志记录便于问题排查
- 支持重试机制（通过RestSendMessageService）

## 配置项

### 消息跳转URL配置
通过 `CheckInService` 配置中心进行配置：

```properties
# Web端跳转URL模板（支持{apiName}占位符）
salaryNotificationWebUrl=https://www.fxiaoke.com/XV/UI/Home#crm/list/=/{apiName}

# H5端跳转URL模板（支持{apiName}占位符）
salaryNotificationH5Url=https://www.fxiaoke.com/hcrm/avah5?_menuType=5&apiName={apiName}&recordType=&beforeHash=object_list/pages/list/list&hash=uipaas_custom/pages/list/list

# 目标对象API名称（默认跳转到员工固定工资表列表）
salaryNotificationTargetApiName=EmployeeFixedSalaryObj
```

**URL占位符说明**：
- `{apiName}`: 会被替换为 `salaryNotificationTargetApiName` 配置的值
- Web端和H5端使用不同的URL模板，系统会根据访问端自动选择

**默认配置**：
- Web端：跳转到员工固定工资表列表页面
- H5端：跳转到移动端员工固定工资表列表页面
- 目标对象：`EmployeeFixedSalaryObj`

### 消息发送应用ID
使用 `RestSendMessageService.PMM_APPID` 作为应用标识。

## 测试

提供了完整的单元测试类 `SalaryRuleNotificationServiceImplTest`，覆盖主要功能场景。

## 扩展建议

1. **消息模板化**：可以将消息文案配置化，支持多语言
2. **消息去重**：添加消息去重机制，避免重复发送
3. **发送统计**：添加消息发送统计和监控
4. **定时检查**：可以添加定时任务定期检查定薪方式不一致的情况
5. **消息中心集成**：与统一消息中心集成，提供更丰富的消息管理功能

## 注意事项

1. 确保相关的DAO和Service已正确注入
2. 消息发送依赖网络，需要考虑网络异常情况
3. 大批量通知时注意性能影响
4. 外部员工ID的判断逻辑可能需要根据实际情况调整
5. 部门和角色名称的获取目前使用简单实现，可能需要完善
