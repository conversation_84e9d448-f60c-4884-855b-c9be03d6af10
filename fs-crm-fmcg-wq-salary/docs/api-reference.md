# 薪资查询API文档

## 1. 日工资数据查询接口

**接口说明**：获取日工资数据，返回日历数据和当天的详细薪资数据

**控制器**：`DailySalaryQueryController`

### CURL请求示例

```bash
curl --location --request POST 'http://[服务器地址]/API/v1/rest/object/SalaryDataObj/controller/DailySalaryQuery' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "employee_id": "E.12345.67890",
  "query_date": "2025-05-20",
  "month": "2025-05"
}'
```

### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| employee_id | String | 否 | 员工ID，不填则使用当前登录用户ID |
| query_date | String | 否 | 查询日期，格式：yyyy-MM-dd，如果提供则返回指定日期的详细数据 |
| month | String | 否 | 月份，格式：yyyy-MM，如果提供则返回指定月份的整月总计数据 |

### 响应结果示例

```json
{
  "calendar_days": [
    {
      "date": "2025-05-01",
      "day_display": "1",
      "week_day": 4,
      "has_salary_data": true,
      "amount": "120.50",
      "is_selected": false,
      "is_today": false
    },
    {
      "date": "2025-05-02",
      "day_display": "2",
      "week_day": 5,
      "has_salary_data": true,
      "amount": "130.00",
      "is_selected": false,
      "is_today": false
    }
  ],
  "current_day_detail": {
    "date": "2025-05-20",
    "salary_items": [
      {
        "id": "item_001",
        "name": "基本工资",
        "amount": "100.00",
        "unit": "元"
      },
      {
        "id": "item_002",
        "name": "绩效奖金",
        "amount": "50.00",
        "unit": "元"
      }
    ],
    "total_amount": "150.00"
  }
}
```

## 2. 员工薪资类型和发放周期查询接口

**接口说明**：获取员工薪资类型和发放周期信息

**控制器**：`SalaryDataTypeQueryController`

### CURL请求示例

```bash
curl --location --request POST 'http://[服务器地址]/API/v1/rest/object/SalaryDataObj/controller/SalaryDataTypeQuery' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "employee_id": "E.12345.67890",
  "month": "2025-05"
}'
```

### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| employee_id | String | 否 | 员工ID，不填则使用当前登录用户ID |
| month | String | 否 | 月份，格式：yyyy-MM |

### 响应结果示例

```json
{
  "salary_method": "日薪制",
  "distribution_cycle": "每周",
  "available_periods": [
    {
      "period_id": "202505_w1",
      "period_name": "2025年05月第1周",
      "start_date": "2025-05-01",
      "end_date": "2025-05-07",
      "status": "已发放"
    },
    {
      "period_id": "202505_w2",
      "period_name": "2025年05月第2周",
      "start_date": "2025-05-08",
      "end_date": "2025-05-14",
      "status": "待发放"
    }
  ]
}
```

## 3. 薪资周期详情查询接口

**接口说明**：获取日/周/月薪资数据，返回总薪资金额和薪资明细项目列表

**控制器**：`SalaryDataTotalQueryController`

### CURL请求示例

```bash
curl --location --request POST 'http://[服务器地址]/API/v1/rest/object/SalaryDataObj/controller/TotalQuery' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "employee_id": "E.12345.67890",
  "period_id": "202505_w1",
  "start_date": "2025-05-01",
  "end_date": "2025-05-07",
  "salary_data_id": "SD12345"
}'
```

### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| employee_id | String | 否 | 员工ID，不填则使用当前登录用户ID |
| period_id | String | 是 | 周期ID |
| start_date | String | 否 | 开始日期，格式：yyyy-MM-dd |
| end_date | String | 否 | 结束日期，格式：yyyy-MM-dd |
| salary_data_id | String | 否 | 薪资数据ID |

### 响应结果示例

```json
{
  "total_amount": "850.50",
  "salary_items": [
    {
      "id": "item_001",
      "name": "基本工资",
      "amount": "700.00",
      "unit": "元"
    },
    {
      "id": "item_002",
      "name": "绩效奖金",
      "amount": "150.50",
      "unit": "元"
    }
  ]
}
```

## 4. 薪资规则员工适用范围查询接口

**接口说明**：查询指定薪资规则的员工适用范围，支持强制适用、强制不适用人员分类，以及无固定工资表员工处理

**控制器**：`SalaryRuleEmployeeScopeQueryController`

### CURL请求示例

```bash
curl --location --request POST 'http://[服务器地址]/API/v1/rest/object/SalaryRuleObj/controller/EmployeeScopeQuery' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "salaryRule": {
    "id": "rule_001",
    "name": "基础薪资规则",
    "salary_method": "MONTHLY",
    "mandatory_applicable_personnel": ["emp_001", "emp_002"],
    "non_applicable_personnel": ["emp_003", "emp_004"]
  },
  "includeEmployeesWithoutFixedSalary": true,
  "autoApplyRuleForNoRuleEmployees": true
}'
```

### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| salaryRule | Object | 是 | 薪资规则对象，包含修改后的适用范围等信息 |
| salaryRule.id | String | 是 | 薪资规则ID |
| salaryRule.name | String | 否 | 薪资规则名称 |
| salaryRule.salary_method | String | 否 | 定薪方式 |
| salaryRule.mandatory_applicable_personnel | Array[String] | 否 | 强制适用人员ID列表 |
| salaryRule.non_applicable_personnel | Array[String] | 否 | 强制不适用人员ID列表 |
| includeEmployeesWithoutFixedSalary | Boolean | 否 | 是否包含无固定工资表的员工，默认false |
| autoApplyRuleForNoRuleEmployees | Boolean | 否 | 无规则的员工是否自动适用当前规则，默认true |

### 响应结果示例

```json
{
  "employees": [
    {
      "employeeId": "emp_005",
      "employeeName": "张三",
      "employeeType": "default__c",
      "salaryRuleId": "rule_001",
      "salaryRuleName": "基础薪资规则",
      "fixedSalaryId": "fs_001",
      "salaryMethod": "MONTHLY",
      "autoApplied": false
    }
  ],
  "mandatoryApplicableEmployees": ["emp_001", "emp_002"],
  "nonApplicableEmployees": ["emp_003", "emp_004"],
  "employeesWithoutFixedSalary": [
    {
      "employeeId": "emp_006",
      "employeeName": "李四",
      "employeeType": "default__c",
      "salaryRuleId": "rule_001",
      "salaryRuleName": "基础薪资规则",
      "fixedSalaryId": null,
      "salaryMethod": null,
      "autoApplied": true
    }
  ],
  "statistics": {
    "currentRuleCount": 5,
    "otherRuleCount": 3,
    "noRuleCount": 2,
    "totalCount": 12,
    "noFixedSalaryCount": 2,
    "mandatoryApplicableCount": 2,
    "nonApplicableCount": 2
  }
}
```

### 返回字段说明

#### 主要数据结构
| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| employees | Array[EmployeeInfo] | 有固定工资表的所有员工信息列表（完整数据） |
| mandatoryApplicableEmployees | Array[String] | 强制适用当前规则的员工ID列表 |
| nonApplicableEmployees | Array[String] | 强制不适用当前规则的员工ID列表 |
| employeesWithoutFixedSalary | Array[EmployeeInfo] | 无固定工资表的所有员工信息列表（完整数据） |
| statistics | StatisticsInfo | 统计信息 |

#### EmployeeInfo 对象结构
| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| employeeId | String | 员工ID |
| employeeName | String | 员工姓名 |
| employeeType | String | 员工类型（default__c=内部员工，record_external__c=外部员工） |
| salaryRuleId | String | 关联的薪资规则ID |
| salaryRuleName | String | 关联的薪资规则名称 |
| fixedSalaryId | String | 固定工资表ID（无固定工资表时为null） |
| salaryMethod | String | 定薪方式 |
| autoApplied | Boolean | 是否为自动适用（当无规则员工自动适用当前规则时为true） |

#### StatisticsInfo 统计信息
| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| currentRuleCount | Integer | 适用当前规则的员工数量 |
| otherRuleCount | Integer | 适用其他规则的员工数量 |
| noRuleCount | Integer | 无规则的员工数量 |
| totalCount | Integer | 员工总数量 |
| noFixedSalaryCount | Integer | 无固定工资表的员工数量 |
| mandatoryApplicableCount | Integer | 强制适用当前规则的员工数量 |
| nonApplicableCount | Integer | 强制不适用当前规则的员工数量 |

### 业务场景说明

#### 1. 规则预览场景
用于在保存薪资规则前预览影响的员工范围
```bash
curl --location --request POST 'http://[服务器地址]/API/v1/rest/object/SalaryRuleObj/controller/EmployeeScopeQuery' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "salaryRule": {
    "id": "rule_002",
    "name": "高管薪资规则"
  },
  "includeEmployeesWithoutFixedSalary": true,
  "autoApplyRuleForNoRuleEmployees": false
}'
```

#### 2. 强制适用人员管理
用于为特定员工强制适用某个薪资规则
```bash
curl --location --request POST 'http://[服务器地址]/API/v1/rest/object/SalaryRuleObj/controller/EmployeeScopeQuery' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "salaryRule": {
    "id": "rule_003",
    "name": "项目奖金规则",
    "mandatory_applicable_personnel": ["emp_101", "emp_102"],
    "non_applicable_personnel": ["emp_201", "emp_202"]
  },
  "includeEmployeesWithoutFixedSalary": false,
  "autoApplyRuleForNoRuleEmployees": false
}'
```

## 通用说明

1. 所有接口的请求头都需要包含企业ID(`x-fs-ei`)和用户ID(`x-fs-userInfo`)
2. 所有接口都会记录详细的日志信息
3. 当参数中未提供员工ID时，默认使用当前登录用户的ID
4. 当查询结果为空时，会返回空的结果对象并进行适当的初始化
5. 所有金额字段均为字符串类型，以保证精度
6. 薪资规则员工适用范围查询接口使用批量处理优化，大幅提升查询性能

## 接口调用顺序与依赖关系

### 调用流程图

```
┌─────────────────────┐
│                     │
│  客户端应用初始化    │
│                     │
└──────────┬──────────┘
           │
           ▼
┌─────────────────────┐
│                     │
│  获取薪资类型和周期  │◄────────────┐
│  SalaryDataTypeQuery│            │
│                     │            │
└──────────┬──────────┘            │
           │                       │
           ▼                       │
┌─────────────────────┐            │
│                     │            │
│  获取日历数据        │            │
│  DailySalaryQuery   │            │
│                     │            │
└──────────┬──────────┘            │
           │                       │
           ▼                       │
┌─────────────────────┐            │
│                     │            │
│  用户选择日期/周期   │────────────┘
│                     │
└──────────┬──────────┘
           │
           ▼
┌─────────────────────┐
│                     │
│  获取薪资详情        │
│  TotalQuery         │
│                     │
└─────────────────────┘
```

### 接口依赖关系

#### 薪资查询流程

1. **初始化流程**：
   - 首先调用 `SalaryDataTypeQuery` 接口获取员工的薪资类型和可用周期
   - 根据返回的薪资类型决定后续界面展示和交互逻辑

2. **日历数据查询**：
   - 调用 `DailySalaryQuery` 接口获取月度日历数据
   - 可以同时获取指定日期的详细薪资数据
   - 依赖于 `SalaryDataTypeQuery` 返回的薪资类型信息

3. **详情数据查询**：
   - 当用户选择特定日期或周期后，调用 `TotalQuery` 接口获取详细薪资数据
   - 依赖于前两个接口返回的信息（周期ID或日期）

#### 薪资规则管理流程

4. **薪资规则员工适用范围查询**：
   - 在薪资规则配置界面，调用 `SalaryRuleEmployeeScopeQuery` 接口查询规则影响的员工范围
   - 支持规则预览、强制适用人员管理、无固定工资表员工处理等场景
   - 独立接口，不依赖其他薪资查询接口

### 数据流向

1. **SalaryDataTypeQuery**:
   - 输入：员工ID、月份
   - 输出：薪资方式、发放周期、可用周期列表
   - 用途：确定薪资查询的基础参数和可选范围

2. **DailySalaryQuery**:
   - 输入：员工ID、查询日期、月份
   - 输出：日历数据列表、当日详细数据
   - 用途：展示月度薪资概览和单日详情

3. **TotalQuery**:
   - 输入：员工ID、周期ID、日期范围、薪资数据ID
   - 输出：总金额、薪资项目列表
   - 用途：展示特定周期或日期范围的详细薪资构成

4. **SalaryRuleEmployeeScopeQuery**:
   - 输入：薪资规则对象、是否包含无固定工资表员工、是否自动适用规则
   - 输出：员工分类列表、强制适用/不适用人员ID、统计信息
   - 用途：查询薪资规则影响的员工范围，支持规则预览和人员管理

### 典型使用场景

1. **薪资概览查询**：
   ```
   SalaryDataTypeQuery → DailySalaryQuery
   ```
   获取薪资类型后，查询月度日历数据，展示薪资概览

2. **薪资详情查询**：
   ```
   SalaryDataTypeQuery → 选择周期 → TotalQuery
   ```
   获取可用周期后，用户选择特定周期，查询详细薪资数据

3. **单日薪资查询**：
   ```
   DailySalaryQuery → 选择日期 → TotalQuery
   ```
   查看日历数据后，用户选择特定日期，查询该日详细薪资数据

4. **周期切换**：
   ```
   SalaryDataTypeQuery → 选择新周期 → TotalQuery
   ```
   用户切换查看不同周期的薪资数据

#### 薪资规则管理场景

5. **规则预览**：
   ```
   SalaryRuleEmployeeScopeQuery
   ```
   在保存薪资规则前，预览规则会影响哪些员工

6. **强制适用人员管理**：
   ```
   配置强制适用人员 → SalaryRuleEmployeeScopeQuery
   ```
   为特定员工强制适用某个薪资规则，查看影响范围

7. **无固定工资表员工处理**：
   ```
   SalaryRuleEmployeeScopeQuery (includeEmployeesWithoutFixedSalary=true)
   ```
   处理新员工或临时员工的薪资规则适用

8. **规则影响分析**：
   ```
   SalaryRuleEmployeeScopeQuery → 统计分析
   ```
   分析薪资规则的覆盖情况和影响范围

### 性能优化说明

#### 薪资规则员工适用范围查询优化
- **批量处理**: 员工姓名查询从N次数据库查询优化为最多2次
- **内存计算**: 员工类型判断基于ID规则进行内存计算
- **响应时间**:
  - 小规模（<100员工）: < 500ms
  - 中等规模（100-1000员工）: < 2s
  - 大规模（>1000员工）: < 5s