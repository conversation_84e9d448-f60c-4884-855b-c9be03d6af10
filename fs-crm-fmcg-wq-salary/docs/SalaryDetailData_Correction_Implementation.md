# 工资条明细修正数据功能实现文档

## 概述

本文档描述了工资条明细修正数据功能的实现，包括修正数据按钮、修正Action和相关的编辑功能。

## 实现的功能

### 1. SalaryDetailDataCorrectionAction - 工资条明细修正数据Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryDetailDataCorrectionAction.java`

**继承关系**: 继承自 `FmcgAbstractStandardAction<Arg, Result>`

**功能特性**:
1. 允许修改工资条明细的金额
2. 自动将发放状态改为"已修正"(DISTRIBUTION_STATUS_Options_4)
3. 记录修正操作日志
4. 支持自定义参数传递

**参数结构**:
```java
@Data
@EqualsAndHashCode(callSuper = true)
public static class Arg extends FmcgPreActionArgs {
    private BigDecimal amount;  // 修正后的金额
    private String reason;      // 修正原因（可选）
}
```

**返回结果**:
```java
@Data
public static class Result {
    private boolean success;    // 是否成功
    private String message;     // 消息
}
```

### 2. SalaryDetailDataWebDetailController - 工资条明细详情页面控制器

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\controller\SalaryDetailDataWebDetailController.java`

**功能**:
1. 在详情页面添加"修正数据"按钮
2. 根据发放状态控制按钮显示
3. 支持所有发放状态下的修正操作

**按钮配置**:
- Action: "SalaryDetailDataCorrection"
- 类型: "custom"
- 标签: "修正数据"
- 需要确认操作
- 支持成功/失败消息提示

### 3. SalaryDetailDataEditAction - 工资条明细编辑Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryDetailDataEditAction.java`

**编辑规则**:
1. 允许修改金额字段
2. 修改金额时自动将状态改为"已修正"
3. 限制其他关键字段的修改

**不允许修改的字段**:
- 工资条 (SALARY_DATA)
- 工资项 (SALARY_ITEM)
- 工资规则 (SALARY_RULE)
- 员工信息 (EMPLOYEE/EMPLOYEE_EXTERNAL)
- 取值方式 (VALUE_TYPE)
- 计算公式 (CALCULATION_FORMULA)
- 公式赋值 (FORMULA_ASSIGNMENT)

### 4. SalaryRuleBulkInvalidAction - 工资规则批量作废Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryRuleBulkInvalidAction.java`

**作废规则**:
1. 工资规则生效日期前，支持批量作废
2. 当工资生效日期后，不允许作废
3. 批量作废时，如果有任何一个规则已生效，则整个批量操作失败

## 核心验证逻辑

### 修正数据验证

1. **金额验证**:
   - 金额不能为空
   - 金额不能为负数
   - 金额最多保留2位小数

2. **状态自动更新**:
   - 修正金额时自动设置状态为"已修正"
   - 保持其他字段不变

### 批量作废验证

1. **生效状态检查**:
   - 检查所有选中的工资规则
   - 如果任何一个已生效，整个批量操作失败
   - 提供详细的错误信息，列出已生效的规则名称

2. **权限控制**:
   - 只有未生效的规则可以批量作废
   - 支持空生效日期的规则作废

## 数据库支持

### SalaryDetailDataDao 新增方法

**方法**: `getById(String tenantId, String salaryDetailDataId)`
- 功能：根据ID获取单个工资条明细数据
- 参数：租户ID、工资条明细ID
- 返回：工资条明细数据对象

### SalaryRuleDao 现有方法

**方法**: `getByIds(String tenantId, List<String> salaryRuleIds)`
- 功能：根据ID列表批量获取工资规则数据
- 用于批量作废时的数据验证

## 用户界面

### 修正数据按钮

1. **显示位置**：工资条明细详情页面头部
2. **显示条件**：所有发放状态下都显示
3. **操作确认**：点击时需要用户确认
4. **权限要求**：需要工资条明细对象的编辑权限

### 按钮样式

- 图标：edit
- 类型：primary
- 确认消息：确定要修正此工资条明细的数据吗？
- 成功消息：数据修正成功
- 失败消息：数据修正失败

## 业务流程

### 修正数据流程

1. **用户操作**：
   - 在工资条明细详情页面点击"修正数据"按钮
   - 输入新的金额和修正原因
   - 确认修正操作

2. **系统处理**：
   - 验证输入的金额格式和范围
   - 更新工资条明细的金额字段
   - 自动设置发放状态为"已修正"
   - 记录修改日志

3. **结果反馈**：
   - 显示修正成功或失败消息
   - 刷新页面显示最新数据

### 批量作废流程

1. **用户操作**：
   - 在工资规则列表页面选择多个规则
   - 点击批量作废按钮

2. **系统处理**：
   - 检查所有选中规则的生效状态
   - 如果都未生效，执行批量作废
   - 如果有已生效的规则，显示错误信息

## 错误处理

### 修正数据错误

- `金额不能为空`
- `金额不能为负数`
- `金额最多保留2位小数`
- `工资条明细不存在`
- `不允许修改{字段名称}`

### 批量作废错误

- `请选择要作废的工资规则`
- `未找到要作废的工资规则`
- `以下工资规则已生效，不可作废：{规则名称列表}`

## 日志记录

所有关键操作都有详细的日志记录：

1. **修正操作日志**：
   - 修正开始和完成
   - 金额变更信息
   - 状态变更记录

2. **批量作废日志**：
   - 批量操作开始和完成
   - 生效状态检查结果
   - 作废规则数量统计

## 性能优化

1. **精确查询**：只查询需要的字段和数据
2. **批量处理**：批量作废时一次性检查所有规则
3. **早期验证**：在操作前进行所有验证，避免部分成功的情况
4. **详细错误信息**：提供足够的信息用于问题排查

## 使用说明

### 修正工资条明细

1. 进入工资条明细详情页面
2. 点击"修正数据"按钮
3. 输入新的金额（必填）和修正原因（可选）
4. 确认修正操作
5. 系统自动更新金额和状态

### 批量作废工资规则

1. 进入工资规则列表页面
2. 选择要作废的工资规则（多选）
3. 点击批量作废按钮
4. 系统检查生效状态并执行作废操作

## 技术特点

1. **继承体系**：使用FmcgAbstractStandardAction提供标准化的Action处理
2. **参数化设计**：支持自定义参数传递和结果返回
3. **状态管理**：自动管理工资条明细的发放状态
4. **权限控制**：基于对象权限进行操作控制
5. **日志记录**：完整的操作日志和审计跟踪

## 扩展性

1. **修正原因**：支持添加修正原因字段
2. **审批流程**：可以集成审批流程控制修正操作
3. **通知机制**：可以添加修正操作的通知功能
4. **历史记录**：可以扩展修正历史记录功能

## 总结

通过实现工资条明细修正数据功能，系统能够：
- 提供灵活的数据修正能力
- 自动管理数据状态变更
- 保护关键业务字段不被误改
- 提供完整的操作日志和审计跟踪
- 支持批量操作提高工作效率
- 确保数据一致性和业务规则遵循
