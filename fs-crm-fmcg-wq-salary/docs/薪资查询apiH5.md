# 薪资查询API文档

## 接口调用顺序与依赖关系

### 调用流程图

```
┌─────────────────────┐
│                     │
│  客户端应用初始化    │
│  (已知月份)          │
│                     │
└──────────┬──────────┘
           │
           ▼
┌─────────────────────┐
│                     │
│  获取薪资类型和周期  │◄────────────┐
│  TypeQuery│            │
│                     │            │
└──────────┬──────────┘            │
           │                       │
           ▼                       │
┌─────────────────────┐            │
│  如果是日薪:         │            │
│  获取日历数据        │            │
│  DailyQuery   │            │
│                     │            │
└──────────┬──────────┘            │
           │                       │
           ▼                       │
┌─────────────────────┐            │
│                     │            │
│  用户选择日期/周期   │────────────┘
│                     │
└──────────┬──────────┘
           │
           ▼
┌─────────────────────┐
│                     │
│  获取薪资详情        │
│  TotalQuery         │
│                     │
└─────────────────────┘
```

### 接口依赖关系

1. **初始化流程**：
   - 首先调用 `TypeQuery` 接口获取员工的薪资类型和可用周期
   - 根据返回的薪资类型决定后续界面展示和交互逻辑
   - 如果是日薪制(`salary_method="1"`)，则需要调用 `DailyQuery` 获取日历视图

2. **日历数据查询**：
   - 仅当薪资类型为日薪时，调用 `DailyQuery` 接口获取月度日历数据
   - 可以同时获取指定日期的详细薪资数据
   - 依赖于 `TypeQuery` 返回的薪资类型信息

3. **详情数据查询**：
   - 当用户选择特定日期或周期后，调用 `TotalQuery` 接口获取详细薪资数据
   - 依赖于前两个接口返回的信息（周期ID或日期）

### 数据流向

1. **TypeQuery**:
   - 输入：员工ID(默认"1000")、月份
   - 输出：薪资方式、发放周期、可用周期列表
   - 用途：确定薪资查询的基础参数和可选范围

2. **DailyQuery**:
   - 输入：员工ID(默认"1000")、查询日期、月份
   - 输出：日历数据列表、当日详细数据
   - 用途：展示月度薪资概览和单日详情

3. **TotalQuery**:
   - 输入：员工ID(默认"1000")、周期ID、日期范围、薪资数据ID
   - 输出：总金额、薪资项目列表
   - 用途：展示特定周期或日期范围的详细薪资构成

### 典型使用场景

1. **薪资概览查询**：
   ```
   TypeQuery → 如果是日薪 → DailyQuery
   ```
   获取薪资类型后，如果是日薪则查询月度日历数据，展示薪资概览

2. **薪资详情查询**：
   ```
   TypeQuery → 选择周期 → TotalQuery
   ```
   获取可用周期后，用户选择特定周期，查询详细薪资数据

3. **单日薪资查询**：
   ```
   TypeQuery → DailyQuery → 选择日期 → TotalQuery
   ```
   对于日薪，查看日历数据后，用户选择特定日期，查询该日详细薪资数据

4. **周期切换**：
   ```
   TypeQuery → 选择新周期 → TotalQuery
   ```
   用户切换查看不同周期的薪资数据

## 1. 员工薪资类型和发放周期查询接口

**接口说明**：获取员工薪资类型和发放周期信息

**控制器**：`TypeQueryController`

### CURL请求示例

```bash
curl --location --request POST 'http://[服务器地址]/API/v1/rest/object/SalaryDataObj/controller/TypeQuery' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "employee_id": "1000",
  "month": "2025-05"
}'
```

### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| employee_id | String | 否 | 员工ID，不填则使用当前登录用户ID，默认值为"1000" |
| month | String | 是 | 月份，格式：yyyy-MM |

### 响应结果示例

```json
{
  "salary_method": "1",
  "salary_method_desc": "日薪制",
  "distribution_cycle": "2",
  "distribution_cycle_desc": "每周",
  "available_periods": [
    {
      "period_id": "202505_w1",
      "period_title": "2025年05月第1周",
      "start_date": "2025-05-01",
      "end_date": "2025-05-07",
      "is_current": false,
      "distribution_status": "1",
      "salary_data_id": "SD12345"
    },
    {
      "period_id": "202505_w2",
      "period_title": "2025年05月第2周",
      "start_date": "2025-05-08",
      "end_date": "2025-05-14",
      "is_current": true,
      "distribution_status": "0",
      "salary_data_id": "SD12346"
    }
  ]
}
```

## 2. 日工资数据查询接口

**接口说明**：获取日工资数据，返回日历数据和当天的详细薪资数据

**控制器**：`DailyQueryController`

### CURL请求示例

```bash
curl --location --request POST 'http://[服务器地址]/API/v1/rest/object/SalaryDataObj/controller/DailyQuery' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "employee_id": "1000",
  "query_date": "2025-05-20",
  "month": "2025-05"
}'
```

### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| employee_id | String | 否 | 员工ID，不填则使用当前登录用户ID，默认值为"1000" |
| query_date | String | 否 | 查询日期，格式：yyyy-MM-dd，如果提供则返回指定日期的详细数据 |
| month | String | 是 | 月份，格式：yyyy-MM，如果提供则返回指定月份的整月总计数据 |

### 响应结果示例

```json
{
  "calendar_days": [
    {
      "date": "2025-05-01",
      "day_display": "1",
      "week_day": 4,
      "has_salary_data": true,
      "amount": "120.50",
      "is_selected": false,
      "is_today": false
    },
    {
      "date": "2025-05-02",
      "day_display": "2",
      "week_day": 5,
      "has_salary_data": true,
      "amount": "130.00",
      "is_selected": false,
      "is_today": false
    }
  ],
  "current_day_detail": {
    "date": "2025-05-20",
    "salary_items": [
      {
        "id": "item_001",
        "name": "基本工资",
        "amount": "100.00",
        "unit": "元"
      },
      {
        "id": "item_002",
        "name": "绩效奖金",
        "amount": "50.00",
        "unit": "元"
      }
    ],
    "total_amount": "150.00"
  }
}
```

## 3. 薪资周期详情查询接口

**接口说明**：获取日/周/月薪资数据，返回总薪资金额和薪资明细项目列表

**控制器**：`SalaryDataTotalQueryController`

### CURL请求示例

```bash
curl --location --request POST 'http://[服务器地址]/API/v1/rest/object/SalaryDataObj/controller/TotalQuery' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "employee_id": "1000",
  "period_id": "202505_w1",
  "start_date": "2025-05-01",
  "end_date": "2025-05-07",
  "salary_data_id": "SD12345"
}'
```

### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| employee_id | String | 否 | 员工ID，不填则使用当前登录用户ID，默认值为"1000" |
| period_id | String | 是 | 周期ID |
| start_date | String | 否 | 开始日期，格式：yyyy-MM-dd |
| end_date | String | 否 | 结束日期，格式：yyyy-MM-dd |
| salary_data_id | String | 否 | 薪资数据ID |

### 响应结果示例

```json
{
  "total_amount": "850.50",
  "salary_items": [
    {
      "id": "item_001",
      "name": "基本工资",
      "amount": "700.00",
      "unit": "元"
    },
    {
      "id": "item_002",
      "name": "绩效奖金",
      "amount": "150.50",
      "unit": "元"
    }
  ]
}
```

## 通用说明

1. 所有接口的请求头都需要包含企业ID(`x-fs-ei`)和用户ID(`x-fs-userInfo`)
2. 所有接口都会记录详细的日志信息
3. 当参数中未提供员工ID时，默认使用当前登录用户的ID，或使用默认值"1000"
4. 当查询结果为空时，会返回空的结果对象并进行适当的初始化
5. 所有金额字段均为字符串类型，以保证精度
