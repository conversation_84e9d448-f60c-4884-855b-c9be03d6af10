# 工资规则通知服务国际化配置

## 概述

本文档列出了工资规则通知服务需要在系统国际化配置中添加的键值对。

## 国际化键值配置

### 中文配置 (zh_CN)

```properties
# 员工固定工资表缺失通知
fmcg.salary.notification.missing.fixed.salary.title=员工固定工资表创建提醒
fmcg.salary.notification.missing.fixed.salary.message={0}{1}适用范围下员工{3}未创建{4}，请尽快创建，创建后方可参与工资计算

# 新增员工通知
fmcg.salary.notification.new.employee.title=新员工固定工资表创建提醒
fmcg.salary.notification.new.employee.message="{0}"新增了员工{1}，因该员工有生效的{2}，请尽快为新员工创建{3}，方可参与工资计算

# 定薪方式不一致通知
fmcg.salary.notification.salary.method.mismatch.title=定薪方式不一致提醒
fmcg.salary.notification.salary.method.mismatch.message=员工{0}【{1}】与适用的【{2}】定薪方式不一致，将无法参与工资计算，请尽快调整

# 多规则适配通知
fmcg.salary.notification.multiple.rules.match.title=多规则适配提醒
fmcg.salary.notification.multiple.rules.match.message="{0}"新增了{1}，因该员工有多个生效的{2}，请尽快为该{3}调整规则，方可参与工资计算

# 规则被移出通知
fmcg.salary.notification.rule.removed.title=规则被移出提醒
fmcg.salary.notification.rule.removed.message={0}{1},{2}规则被移出,请尽快为该{3}调整规则，方可参与工资计算

# 对象名称国际化
fmcg.salary.notification.object.salary.rule=工资规则
fmcg.salary.notification.object.employee.fixed.salary=员工固定工资表

# 按钮文本
fmcg.salary.notification.process.button.text=立即处理
```

### 英文配置 (en_US)

```properties
# Employee Fixed Salary Missing Notification
fmcg.salary.notification.missing.fixed.salary.title=Employee Fixed Salary Creation Reminder
fmcg.salary.notification.missing.fixed.salary.message=Employee {3} in the scope "{2}" of {0} {1} has not created {4}. Please create it as soon as possible to participate in salary calculation

# New Employee Notification
fmcg.salary.notification.new.employee.title=New Employee Fixed Salary Creation Reminder
fmcg.salary.notification.new.employee.message="{0}" has added employee {1}. Since this employee has effective {2}, please create {3} for the new employee as soon as possible to participate in salary calculation

# Salary Method Mismatch Notification
fmcg.salary.notification.salary.method.mismatch.title=Salary Method Mismatch Reminder
fmcg.salary.notification.salary.method.mismatch.message=Employee {0}'s [{1}] is inconsistent with the applicable [{2}] salary method and will not be able to participate in salary calculation. Please adjust as soon as possible

# Multiple Rules Match Notification
fmcg.salary.notification.multiple.rules.match.title=Multiple Rules Match Reminder
fmcg.salary.notification.multiple.rules.match.message="{0}" has added {1}, because this employee has multiple effective {2}, please adjust the rules for the {3} as soon as possible to participate in salary calculation

# Rule Removed Notification
fmcg.salary.notification.rule.removed.title=Rule Removed Reminder
fmcg.salary.notification.rule.removed.message={0} {1}, {2} rule has been removed, please adjust the rules for the {3} as soon as possible to participate in salary calculation

# Object Name Internationalization
fmcg.salary.notification.object.salary.rule=Salary Rule
fmcg.salary.notification.object.employee.fixed.salary=Employee Fixed Salary Table

# Button Text
fmcg.salary.notification.process.button.text=Process Now
```

## 参数说明

### 员工固定工资表缺失消息参数
- `{0}`: 工资规则名称 (ruleName)
- `{1}`: "#I18N#fmcg.salary.notification.object.salary.rule" (工资规则国际化)
- `{3}`: 员工姓名或员工I18n参数 (employeeName/employeeI18nParams) - 如："张三" 或 "{{E.tenant.1010,E.tenant.1011}}"
- `{4}`: "#I18N#fmcg.salary.notification.object.employee.fixed.salary" (员工固定工资表国际化)

**注意**: 模板中的 `{2}` 参数已移除，部门和角色信息现在包含在规则名称中。

### 新增员工消息参数
- `{0}`: 触发原因 (triggerReason) - 如："销售部"、"经理角色"等
- `{1}`: 员工姓名 (employeeName)
- `{2}`: "#I18N#fmcg.salary.notification.object.salary.rule" (工资规则国际化)
- `{3}`: "#I18N#fmcg.salary.notification.object.employee.fixed.salary" (员工固定工资表国际化)

### 定薪方式不一致消息参数
- `{0}`: 员工姓名 (employeeName)
- `{1}`: "#I18N#fmcg.salary.notification.object.employee.fixed.salary" (员工固定工资表国际化)
- `{2}`: "#I18N#fmcg.salary.notification.object.salary.rule" (工资规则国际化)

### 多规则适配消息参数
- `{0}`: 触发原因 (triggerReason) - 如："销售部"、"经理角色"等
- `{1}`: "#I18N#fmcg.salary.notification.object.employee.fixed.salary" (员工固定工资表国际化)
- `{2}`: "#I18N#fmcg.salary.notification.object.salary.rule" (工资规则国际化)
- `{3}`: "#I18N#fmcg.salary.notification.object.employee.fixed.salary" (员工固定工资表国际化)

### 规则被移出消息参数
- `{0}`: "#I18N#fmcg.salary.notification.object.employee.fixed.salary" (员工固定工资表国际化)
- `{1}`: 员工固定工资表名称 (employeeFixedSalaryName)
- `{2}`: 被移出的规则名称 (removedRuleName)
- `{3}`: "#I18N#fmcg.salary.notification.object.employee.fixed.salary" (员工固定工资表国际化)

## 使用方式

在代码中通过以下方式使用：

```java
// 创建国际化项目
InternationalItem titleItem = SalaryNotificationMessageUtil.createInternationalItem(
    SalaryNotificationMessageUtil.MISSING_FIXED_SALARY_TITLE_KEY
);

InternationalItem contentItem = SalaryNotificationMessageUtil.createInternationalItem(
    SalaryNotificationMessageUtil.MISSING_FIXED_SALARY_MESSAGE_KEY, 
    ruleName, departmentAndRole, employeeName
);

// 在TextCardElement中使用
TextCardElement element = new TextCardElement();
element.setTextInfo(titleItem);
```

## 配置位置

这些国际化键值需要添加到系统的国际化资源文件中，具体位置可能是：
- 数据库国际化配置表
- 配置文件（如 messages.properties）
- 国际化管理系统

请根据项目的国际化实现方式，将上述键值对添加到相应的配置中。

## 注意事项

1. **键值一致性**：确保代码中使用的键值与配置文件中的键值完全一致
2. **参数顺序**：消息模板中的参数占位符 `{0}`, `{1}`, `{2}` 的顺序必须与代码中传入的参数顺序一致
3. **特殊字符**：消息内容中的引号、括号等特殊字符需要根据国际化系统的要求进行转义
4. **测试验证**：添加配置后需要测试各种语言环境下的消息显示效果

## 扩展支持

如需支持更多语言，可以按照相同的键值结构添加对应语言的配置，例如：
- 日文 (ja_JP)
- 韩文 (ko_KR)
- 德文 (de_DE)
- 法文 (fr_FR)

每种语言都需要提供完整的键值对配置。
