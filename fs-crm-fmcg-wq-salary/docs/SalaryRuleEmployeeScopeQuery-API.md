# 薪资规则员工适用范围查询接口

## 接口说明

根据修改后的薪资规则对象，查询符合适用范围条件的所有员工固定工资表记录。

## 接口信息

- **Controller**: `SalaryRuleEmployeeScopeQueryController`
- **请求方式**: POST
- **接口路径**: `/API/v1/rest/object/SalaryRuleObj/controller/SalaryRuleEmployeeScopeQuery`

## 请求参数

### 基础参数（必填）

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| salaryRule | IObjectData | 是 | 薪资规则对象，包含修改后的适用范围等信息 |

## 请求示例

### 基本查询

```json
{
  "salaryRule": {
    "id": "rule123",
    "name": "销售部月薪规则",
    "salaryMethod": "3",
    "applicable_scope": {
      "departments": ["dept001", "dept002"],
      "roles": ["SALES", "MANAGER"],
      "employees": ["emp001", "emp002"]
    }
  }
}
```

## 响应结果

### 成功响应

```json
{
  "employees": [
    {
      "employeeId": "emp001",
      "employeeName": "张三",
      "employeeType": "internal",
      "salaryRuleId": "rule123",
      "salaryRuleName": "销售部月薪规则",
      "fixedSalaryId": "salary001",
      "salaryMethod": "3"
    },
    {
      "employeeId": "emp002",
      "employeeName": "李四",
      "employeeType": "internal",
      "salaryRuleId": "rule456",
      "salaryRuleName": "技术部月薪规则",
      "fixedSalaryId": "salary002",
      "salaryMethod": "3"
    }
  ],
  "statistics": {
    "currentRuleCount": 1,
    "otherRuleCount": 1,
    "noRuleCount": 0,
    "totalCount": 2
  }
}
```

### 响应字段说明

#### employees 字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| employeeId | String | 员工ID |
| employeeName | String | 员工姓名 |
| employeeType | String | 员工类型：来自 recordtype 字段值（default__c-内部员工，external__c-外部员工） |
| salaryRuleId | String | 所属薪资规则ID |
| salaryRuleName | String | 所属薪资规则名称 |
| fixedSalaryId | String | 固定薪资记录ID |
| salaryMethod | String | 定薪方式：1-日薪，2-周薪，3-月薪 |

#### statistics 字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| currentRuleCount | Integer | 属于当前规则的员工数量 |
| otherRuleCount | Integer | 属于其他规则的员工数量 |
| noRuleCount | Integer | 无规则的员工数量 |
| totalCount | Integer | 总员工数量 |

### 错误响应

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "薪资规则ID不能为空"
  }
}
```

## 业务逻辑

### 查询流程

1. **参数校验**：验证薪资规则对象不为空
2. **获取适用范围**：从薪资规则对象中获取适用范围配置
3. **获取员工列表**：根据适用范围获取符合条件的员工ID列表
4. **查询固定薪资**：直接根据员工ID列表查询员工固定工资表记录

### 核心逻辑

#### 适用范围解析
- 从传入的薪资规则对象中提取 `applicable_scope` 字段
- 调用 `SalaryService.getEmployeeIdsByApplicableScope()` 方法获取员工ID列表

#### 直接查询
- 不再进行员工类型过滤，直接使用获取到的员工ID列表
- 调用 `EmployeeFixedSalaryDao.getByEmployeeIds()` 方法查询员工固定工资表

## 使用场景

### 1. 薪资规则管理
查看某个薪资规则影响的员工范围，用于规则配置验证。

### 2. 员工薪资分析
分析不同定薪方式下的员工分布情况。

### 3. 数据审计
检查薪资规则配置的正确性和覆盖范围。

### 4. 报表生成
为薪资报表提供基础数据支持。

## 注意事项

1. **权限控制**：需要有薪资规则查看权限
2. **数据范围**：只返回当前租户下的数据
3. **性能考虑**：大量员工时建议分页查询
4. **过滤优先级**：指定员工ID > 部门/企业过滤 > 角色过滤

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| VALIDATION_ERROR | 参数校验失败 |
| RULE_NOT_FOUND | 薪资规则不存在 |
| NO_APPLICABLE_SCOPE | 薪资规则未配置适用范围 |
| QUERY_FAILED | 查询失败 |

## 版本信息

- **版本**: v1.0
- **创建时间**: 2025-01-27
- **最后更新**: 2025-01-27
