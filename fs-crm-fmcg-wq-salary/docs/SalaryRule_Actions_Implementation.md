# 工资规则Action实现文档

## 概述

本文档描述了工资规则对象的编辑(edit)和作废(invalid)action的实现，根据工资规则生效日期来控制可编辑字段和作废权限。

## 实现的功能

### 1. SalaryRuleEditAction 增强 - 工资规则编辑Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryRuleEditAction.java`

**编辑规则**:
1. 任何时机都可以编辑
2. 工资规则生效日期前，支持编辑全部字段
3. 当工资规则生效日期后，仅支持编辑「工资规则名称」和「适用范围」

**生效日期后可编辑的字段**:
- 工资规则名称 (name)
- 适用企业(外部) (APPLICABLE_OUT_TENANT)
- 适用部门(内部) (APPLICABLE_DEPARTMENT_INTE)
- 适用角色(内部) (APPLICABLE_ROLE_INTE)
- 适用角色(外部) (APPLICABLE_OUT_ROLE)
- 适用人员(外部) (APPLICABLE_OUT_USER)
- 适用人员(内部) (APPLICABLE_PERSON_INTE)

### 2. SalaryRuleInvalidAction - 工资规则作废Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryRuleInvalidAction.java`

**作废规则**:
1. 工资规则生效日期前，支持作废
2. 当工资生效日期后，不允许作废

## 核心验证逻辑

### 生效日期检查

工资规则的生效状态通过以下逻辑判断：
```java
Long effectiveDate = originalSalaryRule.get(SalaryRuleFields.EFFECTIVE_DATE, Long.class);
long currentTime = System.currentTimeMillis();
boolean isEffective = effectiveDate != null && effectiveDate <= currentTime;
```

### 编辑字段限制

当工资规则已生效时：
1. 遍历所有要修改的字段
2. 检查字段是否在允许编辑的字段列表中
3. 对于不允许编辑的字段，比较新旧值是否相同
4. 如果发现不允许的字段被修改，抛出验证异常

### 作废权限控制

当工资规则已生效时：
1. 检查生效日期是否小于等于当前时间
2. 如果已生效，直接阻止作废操作
3. 如果未生效或无生效日期，允许作废

## 字段验证逻辑

### 字段值比较

实现了精确的字段值比较逻辑：
```java
private boolean isFieldValueEqual(Object newValue, Object originalValue) {
    if (newValue == null && originalValue == null) {
        return true;
    }
    
    if (newValue == null || originalValue == null) {
        return false;
    }
    
    return newValue.toString().equals(originalValue.toString());
}
```

### 字段显示名称映射

为常用字段提供了友好的显示名称：
- SALARY_METHOD → "定薪方式"
- EFFECTIVE_DATE → "生效日期"
- SALARY_ITEM → "工资项"
- DISTRIBUTION_CYCLE → "发放周期"
- AUTO_CREATED_PAYROLLVOUCHE → "发放单自动创建时机"

## 错误处理

### 编辑限制错误
```
工资规则已生效，不允许修改{字段显示名称}
```

### 作废限制错误
```
工资规则已生效，不可作废
```

### 数据不存在错误
```
工资规则不存在
```

## 测试用例

### SalaryRuleActionTest
包含以下测试场景：

1. **编辑测试**：
   - 生效前允许编辑全部字段
   - 生效后限制编辑字段
   - 生效后允许编辑名称和适用范围

2. **作废测试**：
   - 生效前允许作废
   - 生效后阻止作废
   - 无生效日期时允许作废
   - 工资规则不存在时抛出异常

## 日志记录

所有关键操作都有详细的日志记录：
- 编辑操作开始和字段限制状态
- 作废操作的生效状态检查
- 成功和失败的操作结果
- 生效时间和当前时间的对比信息

## 业务场景

### 编辑场景

1. **规则制定阶段**（生效前）：
   - 可以修改所有字段
   - 包括定薪方式、工资项、发放周期等核心配置

2. **规则执行阶段**（生效后）：
   - 只能修改规则名称和适用范围
   - 保护核心业务逻辑不被误改
   - 允许调整适用人员范围以适应组织变化

### 作废场景

1. **规则制定阶段**（生效前）：
   - 可以作废不需要的规则
   - 支持规则的灵活调整

2. **规则执行阶段**（生效后）：
   - 不允许作废已生效的规则
   - 保护正在执行的工资计算逻辑
   - 避免影响已发放的工资数据

## 性能优化

1. **单次数据库查询**：获取原始工资规则数据
2. **早期验证**：在操作前进行所有验证，避免部分成功的情况
3. **精确字段比较**：只检查实际修改的字段
4. **详细日志**：提供足够的信息用于问题排查

## 使用说明

1. **编辑工资规则**：
   - 系统会自动检查生效状态
   - 生效前可以修改所有字段
   - 生效后只能修改名称和适用范围

2. **作废工资规则**：
   - 系统会检查生效状态
   - 只有未生效的规则可以作废
   - 已生效的规则会被保护

3. **错误处理**：
   - 所有操作都有详细的错误提示
   - 明确指出不允许修改的字段
   - 提供生效状态的详细信息

## 适用范围字段说明

适用范围包括以下字段，这些字段在工资规则生效后仍可编辑：

1. **适用企业(外部)**：外部企业的适用范围
2. **适用部门(内部)**：内部部门的适用范围
3. **适用角色(内部)**：内部角色的适用范围
4. **适用角色(外部)**：外部角色的适用范围
5. **适用人员(外部)**：外部人员的适用范围
6. **适用人员(内部)**：内部人员的适用范围

这些字段的可编辑性确保了工资规则在执行过程中仍能灵活调整适用人员范围，以适应组织结构的变化。

## 总结

通过实现基于生效日期的编辑和作废控制，工资规则系统能够：
- 在规则制定阶段提供完全的灵活性
- 在规则执行阶段保护核心业务逻辑
- 允许必要的适用范围调整
- 提供清晰的操作反馈和错误提示
- 确保数据一致性和业务连续性
