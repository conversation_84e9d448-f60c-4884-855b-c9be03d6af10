# 工资项Action实现文档

## 概述

本文档描述了工资项对象的新建(add)、编辑(edit)和作废(invalid)action的实现，包括字段显示时机、必填逻辑和使用情况检查。

## 实现的功能

### 1. SalaryItemAddAction - 工资项新建Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryItemAddAction.java`

**新建规则**:
- 不支持导入新建数据
- 验证字段显示时机和必填逻辑
- 根据取值方式自动设置相关字段

**字段验证逻辑**:
1. **当取值方式=计算公式时**:
   - 舍位方式必填
   - 当舍位方式=四舍五入时，小数位数必填
   - 计算公式必填

2. **当取值方式=固定值时**:
   - 自动清空计算公式相关字段（计算公式、舍位方式、小数位数）

### 2. SalaryItemEditAction - 工资项编辑Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryItemEditAction.java`

**编辑规则**:
- 任何时机都可以编辑
- 当工资项禁用或启用后未被工资规则使用时，支持编辑全部字段
- 当工资项被工资规则使用后，仅可编辑「工资项名称」「工资项说明」

**限制编辑的字段**（当被工资规则使用时）:
- 取值方式
- 增减属性
- 定薪方式
- 启用状态
- 舍位方式
- 小数位数
- 计算公式

### 3. SalaryItemInvalidAction - 工资项作废Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryItemInvalidAction.java`

**作废规则**:
- 当工资项禁用或启用后未被工资规则使用时，允许作废
- 当工资项被工资规则使用后，不可作废

**作废验证逻辑**:
1. 检查工资项是否被工资规则使用
2. 如果被使用，显示详细的使用信息并阻止作废
3. 检查工资项的启用状态

## 支持的DAO方法

### SalaryRuleDao 新增方法

**方法**: `getSalaryRulesBySalaryItemId(String tenantId, String salaryItemId)`
- 功能：根据工资项ID查询使用该工资项的工资规则
- 参数：租户ID、工资项ID
- 返回：使用该工资项的工资规则列表

## 字段显示时机和依赖关系

### 舍位方式字段
- **显示时机**: 当"取值方式=计算公式"时显示
- **必填**: 是

### 小数位数字段
- **显示时机**: 当"取值方式=计算公式"且"舍位方式=四舍五入"时显示
- **必填**: 是

### 计算公式字段
- **显示时机**: 当"取值方式=计算公式"时需要设置
- **存储作用**: 字段不在新建页中显示，在详情页展示该字段

## 验证逻辑

### 新建验证
1. 根据取值方式验证必填字段
2. 自动清理不相关字段
3. 提取计算公式中的KPI指标ID

### 编辑验证
1. 检查工资项是否被工资规则使用
2. 如果被使用，验证修改的字段是否在允许范围内
3. 如果未被使用，执行完整的字段验证

### 作废验证
1. 检查工资项是否被工资规则使用
2. 如果被使用，阻止作废并显示详细信息
3. 记录启用状态信息

## 错误处理

所有action都包含详细的错误信息：
- 字段必填验证失败
- 工资项被工资规则使用的详细信息
- 不允许修改的字段提示

## 日志记录

所有关键操作都有详细的日志记录：
- 操作开始和结束
- 验证结果
- 使用情况检查结果
- 错误信息

## 测试用例

### SalaryItemActionTest
包含以下测试场景：
1. **新建测试**:
   - 计算公式类型工资项新建成功
   - 固定值类型工资项新建成功
   - 缺少必填字段时抛出异常

2. **编辑测试**:
   - 未被工资规则使用时允许编辑全部字段
   - 被工资规则使用时限制编辑字段

3. **作废测试**:
   - 未被工资规则使用时允许作废
   - 被工资规则使用时阻止作废

## 性能优化

1. **批量查询**: 使用批量查询减少数据库访问
2. **早期验证**: 在操作前进行所有验证，避免部分成功的情况
3. **详细日志**: 提供足够的信息用于问题排查

## 使用说明

1. **新建工资项**: 系统会根据取值方式自动验证和设置相关字段
2. **编辑工资项**: 系统会检查使用情况并限制可编辑字段
3. **作废工资项**: 系统会检查使用情况并阻止不当操作
4. **所有操作**: 都有详细的错误提示和日志记录

## 字段提示信息

- **取值方式为固定值时**: 需在【员工固定工资表】中按员工填写金额
- **当工资项被引用后**: 不可禁用，选项直接置灰处理

## 关联对象说明

- **工资规则**: 通过 `salary_item` 字段关联工资项
- **员工固定工资表**: 存储固定值类型工资项的员工金额
- **KPI指标**: 通过计算公式关联，存储在 `formula_includes_kpi` 字段中
