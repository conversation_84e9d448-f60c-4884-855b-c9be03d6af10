# 工资规则通知服务配置示例

## 配置中心配置

### CheckInService 配置

在配置中心的 `CheckInService` 配置中添加以下配置项：

```properties
# 工资规则通知相关配置

# Web端跳转URL模板
# 支持{apiName}占位符，会被替换为salaryNotificationTargetApiName的值
salaryNotificationWebUrl=https://www.fxiaoke.com/XV/UI/Home#crm/list/=/{apiName}

# H5端跳转URL模板  
# 支持{apiName}占位符，用于移动端访问
salaryNotificationH5Url=https://www.fxiaoke.com/hcrm/avah5?_menuType=5&apiName={apiName}&recordType=&beforeHash=object_list/pages/list/list&hash=uipaas_custom/pages/list/list

# 目标对象API名称
# 默认跳转到员工固定工资表列表，可以根据需要修改为其他对象
salaryNotificationTargetApiName=EmployeeFixedSalaryObj
```

## 配置说明

### URL模板配置

#### Web端URL模板
- **配置项**: `salaryNotificationWebUrl`
- **默认值**: `https://www.fxiaoke.com/XV/UI/Home#crm/list/=/{apiName}`
- **说明**: 用于PC端浏览器访问的URL模板
- **占位符**: `{apiName}` 会被替换为目标对象的API名称

#### H5端URL模板
- **配置项**: `salaryNotificationH5Url`
- **默认值**: `https://www.fxiaoke.com/hcrm/avah5?_menuType=5&apiName={apiName}&recordType=&beforeHash=object_list/pages/list/list&hash=uipaas_custom/pages/list/list`
- **说明**: 用于移动端访问的URL模板
- **占位符**: `{apiName}` 会被替换为目标对象的API名称

#### 目标对象API名称
- **配置项**: `salaryNotificationTargetApiName`
- **默认值**: `EmployeeFixedSalaryObj`
- **说明**: 消息点击后跳转的目标对象，默认为员工固定工资表

### 配置示例

#### 示例1：跳转到员工固定工资表（默认）
```properties
salaryNotificationWebUrl=https://www.fxiaoke.com/XV/UI/Home#crm/list/=/{apiName}
salaryNotificationH5Url=https://www.fxiaoke.com/hcrm/avah5?_menuType=5&apiName={apiName}&recordType=&beforeHash=object_list/pages/list/list&hash=uipaas_custom/pages/list/list
salaryNotificationTargetApiName=EmployeeFixedSalaryObj
```

#### 示例2：跳转到工资规则列表
```properties
salaryNotificationWebUrl=https://www.fxiaoke.com/XV/UI/Home#crm/list/=/{apiName}
salaryNotificationH5Url=https://www.fxiaoke.com/hcrm/avah5?_menuType=5&apiName={apiName}&recordType=&beforeHash=object_list/pages/list/list&hash=uipaas_custom/pages/list/list
salaryNotificationTargetApiName=SalaryRuleObj
```

#### 示例3：自定义跳转页面
```properties
salaryNotificationWebUrl=https://www.fxiaoke.com/custom/salary/management/{apiName}
salaryNotificationH5Url=https://www.fxiaoke.com/h5/custom/salary/{apiName}
salaryNotificationTargetApiName=CustomSalaryObj
```

## 实际生成的URL示例

### 使用默认配置时：
- **Web端URL**: `https://www.fxiaoke.com/XV/UI/Home#crm/list/=/EmployeeFixedSalaryObj`
- **H5端URL**: `https://www.fxiaoke.com/hcrm/avah5?_menuType=5&apiName=EmployeeFixedSalaryObj&recordType=&beforeHash=object_list/pages/list/list&hash=uipaas_custom/pages/list/list`

### 跳转到工资规则时：
- **Web端URL**: `https://www.fxiaoke.com/XV/UI/Home#crm/list/=/SalaryRuleObj`
- **H5端URL**: `https://www.fxiaoke.com/hcrm/avah5?_menuType=5&apiName=SalaryRuleObj&recordType=&beforeHash=object_list/pages/list/list&hash=uipaas_custom/pages/list/list`

## 配置更新

### 动态配置更新
配置支持动态更新，修改配置后无需重启服务即可生效。

### 配置验证
系统会在启动时验证配置的有效性：
- 检查URL模板格式是否正确
- 验证占位符是否存在
- 确保目标对象API名称不为空

### 配置回退
如果配置无效或获取失败，系统会使用内置的默认配置：
- Web端默认跳转到员工固定工资表列表
- H5端使用标准的移动端URL格式
- 目标对象默认为 `EmployeeFixedSalaryObj`

## 注意事项

1. **占位符格式**: 必须使用 `{apiName}` 格式，大小写敏感
2. **URL编码**: 如果URL中包含特殊字符，需要进行适当的URL编码
3. **配置同步**: 确保所有服务实例都能获取到最新的配置
4. **测试验证**: 修改配置后建议进行完整的功能测试
5. **权限检查**: 确保跳转的目标页面用户有访问权限

## 故障排查

### 常见问题

1. **跳转链接无效**
   - 检查URL模板配置是否正确
   - 验证占位符是否被正确替换
   - 确认目标对象API名称是否存在

2. **移动端跳转异常**
   - 检查H5端URL模板配置
   - 验证移动端页面是否支持该对象

3. **配置不生效**
   - 确认配置中心配置是否正确
   - 检查服务是否能正常获取配置
   - 验证配置更新是否已同步

### 日志查看
系统会记录URL构建和配置获取的相关日志：
```
构建跳转URL失败
获取配置失败，使用默认配置
URL占位符替换完成: {原始URL} -> {最终URL}
```
