# 薪资系统Action API文档

## 1. 工资发放单更新工资数据接口

**接口说明**：重新触发工资计算，更新工资条数据

**Action**：`SalaryPaymentSlipUpdateSalaryDataAction`

### CURL请求示例

```bash
curl --location --request POST 'http://[服务器地址]/API/v1/object/SalaryPaymentSlipObj/action/UpdateSalaryData' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "object_data_id": "SPS12345",
  "force_update": false,
  "update_reason": "工资规则调整，需要重新计算"
}'
```

### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| object_data_id | String | 是 | 工资发放单ID |
| force_update | Boolean | 否 | 是否强制更新，默认false |
| update_reason | String | 否 | 更新说明 |

### 响应结果示例

```json
{
  "success": true,
  "message": "工资数据更新中，完成后状态将更新为生成完成",
  "updated_salary_data_count": 0,
  "updated_detail_count": 0
}
```

### 业务规则

1. 只有发放状态为"未发放"或"生成异常"的工资发放单才能更新数据
2. 已发放的工资发放单不能更新数据
3. 更新过程为异步执行，完成后会自动更新状态为"生成完成"
4. 如果更新失败，状态会更新为"生成失败"

## 2. 工资发放单下发工资条接口

**接口说明**：下发工资条给员工，发送通知消息

**Action**：`SalaryPaymentSlipDistributeAction`

### CURL请求示例

```bash
curl --location --request POST 'http://[服务器地址]/API/v1/object/SalaryPaymentSlipObj/action/Distribute' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "object_data_id": "SPS12345",
  "send_sms_notification": false,
  "custom_message": "您的工资条已生成，请查看"
}'
```

### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| object_data_id | String | 是 | 工资发放单ID |
| send_sms_notification | Boolean | 否 | 是否发送短信通知，默认false |
| custom_message | String | 否 | 自定义通知消息 |

### 响应结果示例

```json
{
  "success": true,
  "message": "工资条下发中，成功后会更新状态为已发放",
  "notified_employee_count": 0,
  "distribute_time": "2025-06-18T10:30:00.000Z"
}
```

### 业务规则

1. 只有发放状态为"生成完成"的工资发放单才能下发
2. 必须存在工资条数据才能下发
3. 下发过程为异步执行，会给员工发送消息通知
4. 内部员工使用CRM通知，外部员工使用互联通知公告
5. 下发成功后状态更新为"已发放"，失败则更新为"发放失败"

## 3. 工资条明细修正数据接口

**接口说明**：修正工资条明细的金额数据

**Action**：`SalaryDetailDataCorrectionAction`

### CURL请求示例

```bash
curl --location --request POST 'http://[服务器地址]/API/v1/object/SalaryDetailDataObj/action/Correction' \
--header 'Content-Type: application/json' \
--header 'x-fs-ei: [企业ID]' \
--header 'x-fs-userInfo: [用户ID]' \
--data-raw '{
  "object_data_id": "SDD12345",
  "amount": 1500.50,
  "reason": "绩效调整"
}'
```

### 请求参数说明

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| object_data_id | String | 是 | 工资条明细ID |
| amount | BigDecimal | 是 | 修正后的金额 |
| reason | String | 否 | 修正原因 |

### 响应结果示例

```json
{
  "success": true,
  "message": "工资条明细修正成功"
}
```

### 业务规则

1. 修正后自动将发放状态改为"已修正"
2. 会自动同步更新对应工资条的总金额
3. 记录修正操作日志
4. 修正金额必须为有效的数值

## 通用说明

1. 所有接口的请求头都需要包含企业ID(`x-fs-ei`)和用户ID(`x-fs-userInfo`)
2. 所有接口都会记录详细的操作日志
3. Action接口都需要传入`object_data_id`参数指定操作的对象
4. 所有金额字段均支持小数，建议使用BigDecimal类型保证精度
5. 异步操作的接口会立即返回，实际处理结果通过状态字段反映

## 接口调用顺序与依赖关系

### 工资发放流程

```
┌─────────────────────┐
│                     │
│  创建工资发放单      │
│                     │
└──────────┬──────────┘
           │
           ▼
┌─────────────────────┐
│                     │
│  更新工资数据        │◄────────────┐
│  UpdateSalaryData   │            │
│                     │            │
└──────────┬──────────┘            │
           │                       │
           ▼                       │
┌─────────────────────┐            │
│                     │            │
│  检查工资条数据      │            │
│                     │            │
└──────────┬──────────┘            │
           │                       │
           ▼                       │
┌─────────────────────┐            │
│                     │            │
│  修正明细数据        │────────────┘
│  CorrectionAction   │
│                     │
└──────────┬──────────┘
           │
           ▼
┌─────────────────────┐
│                     │
│  下发工资条          │
│  DistributeAction   │
│                     │
└─────────────────────┘
```

### 典型使用场景

1. **工资数据重新计算**：
   ```
   UpdateSalaryData → 检查状态 → 必要时修正明细 → DistributeAction
   ```

2. **工资条明细修正**：
   ```
   CorrectionAction → 自动同步工资条总额
   ```

3. **工资条下发**：
   ```
   检查发放单状态 → DistributeAction → 发送员工通知
   ```

## 错误码说明

### 工资发放单更新工资数据接口错误码

| 错误码 | 错误信息 | 说明 |
| ------ | -------- | ---- |
| PAYMENT_SLIP_NOT_FOUND | 工资发放单不存在 | 指定的工资发放单ID不存在 |
| INVALID_PAY_STATUS | 只有未发放和发放中的工资发放单才能更新工资数据 | 发放状态不允许更新 |
| UPDATE_FAILED | 更新工资数据失败 | 系统内部错误导致更新失败 |

### 工资发放单下发工资条接口错误码

| 错误码 | 错误信息 | 说明 |
| ------ | -------- | ---- |
| PAYMENT_SLIP_NOT_FOUND | 工资发放单不存在 | 指定的工资发放单ID不存在 |
| INVALID_DISTRIBUTE_STATUS | 只有生成完成的工资发放单才能下发 | 发放状态不允许下发 |
| NO_SALARY_DATA | 该工资发放单下没有工资条数据，无法下发 | 没有可下发的工资条 |
| DISTRIBUTE_FAILED | 下发工资条失败 | 系统内部错误导致下发失败 |

### 工资条明细修正数据接口错误码

| 错误码 | 错误信息 | 说明 |
| ------ | -------- | ---- |
| DETAIL_NOT_FOUND | 工资条明细不存在 | 指定的明细ID不存在 |
| INVALID_AMOUNT | 修正金额无效 | 金额格式不正确或为负数 |
| AMOUNT_NOT_CHANGED | 修正金额与原金额相同，无需修正 | 新金额与原金额一致 |
| CORRECTION_FAILED | 工资条明细修正失败 | 系统内部错误导致修正失败 |

## 状态字段说明

### 工资发放单状态 (PAY_STATUS)

| 状态值 | 状态名称 | 说明 |
| ------ | -------- | ---- |
| 1 | 生成完成 | 工资数据已生成完成，可以下发 |
| 2 | 发放中 | 正在下发工资条给员工 |
| 3 | 生成失败/发放失败 | 生成或下发过程中出现错误 |
| 4 | 已发放 | 工资条已成功下发给员工 |
| ERROR | 生成异常 | 工资数据生成过程中出现异常 |

### 工资条明细发放状态 (DISTRIBUTION_STATUS)

| 状态值 | 状态名称 | 说明 |
| ------ | -------- | ---- |
| -2 | 没数据 | 该周期没有工资条数据 |
| -1 | 未发放 | 工资条已生成但未发放 |
| 0 | 已发放 | 工资条已发放给员工 |
| 1 | 已结算 | 工资条已完成结算 |
| 4 | 已修正 | 工资条明细已被修正 |

## 接口权限说明

### 权限要求

1. **工资发放单更新工资数据**：
   - 需要工资发放单的编辑权限
   - 需要薪资管理相关功能权限

2. **工资发放单下发工资条**：
   - 需要工资发放单的操作权限
   - 需要员工通知发送权限

3. **工资条明细修正数据**：
   - 需要工资条明细的编辑权限
   - 需要薪资数据修正权限

### 数据权限

- 所有接口都会根据当前用户的数据权限过滤可操作的对象
- 只能操作当前用户有权限访问的工资发放单和工资条明细
- 跨企业的外部员工数据需要相应的互联权限

## 性能说明

### 接口性能特点

1. **工资发放单更新工资数据**：
   - 异步执行，响应时间快（通常<1秒）
   - 实际计算时间取决于工资条数量和复杂度
   - 建议定期检查状态确认完成情况

2. **工资发放单下发工资条**：
   - 异步执行，响应时间快（通常<1秒）
   - 通知发送时间取决于员工数量
   - 支持批量通知，性能较好

3. **工资条明细修正数据**：
   - 同步执行，响应时间中等（通常<3秒）
   - 包含工资条总额重新计算
   - 性能稳定，不受数据量影响

### 并发处理

- 所有接口都支持并发调用
- 工资发放单状态更新使用乐观锁机制
- 工资条明细修正支持多用户同时操作不同明细

## 监控与日志

### 日志记录

所有Action都会记录详细的操作日志，包括：

1. **操作前日志**：
   - 操作用户信息
   - 操作对象ID
   - 操作参数
   - 前置验证结果

2. **操作过程日志**：
   - 关键步骤执行情况
   - 数据变更详情
   - 异步任务状态

3. **操作结果日志**：
   - 操作成功/失败状态
   - 影响的数据数量
   - 执行耗时

### 监控指标

建议监控以下指标：

- 接口调用频率和成功率
- 异步任务执行时间和成功率
- 工资条下发通知成功率
- 明细修正操作频率

## 最佳实践

### 调用建议

1. **批量操作**：
   - 避免频繁调用更新工资数据接口
   - 建议在业务规则变更后统一更新

2. **状态检查**：
   - 异步操作后定期检查状态
   - 建议使用轮询或事件通知机制

3. **错误处理**：
   - 实现重试机制处理临时性错误
   - 记录详细的错误信息用于问题排查

4. **数据一致性**：
   - 明细修正后验证工资条总额
   - 关键操作前后进行数据校验
