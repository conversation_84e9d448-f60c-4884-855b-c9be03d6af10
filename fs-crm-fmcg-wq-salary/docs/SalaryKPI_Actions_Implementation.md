# 工资绩效指标Action实现文档

## 概述

本文档描述了工资绩效指标对象的编辑和作废action的实现，包括单个作废和批量作废功能。

## 实现的功能

### 1. SalaryKPIEditAction - 工资绩效指标编辑Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryKPIEditAction.java`

**编辑规则**:
- 任何时机都可以编辑
- 当未被工资项使用时，支持编辑全部字段
- 当指标被工资项使用后，仅可编辑「指标名称」「指标说明」

**实现逻辑**:
1. 检查指标是否被工资项使用
2. 如果被使用，验证修改的字段是否在允许范围内
3. 不允许修改的字段包括：指标计算方式、聚合对象、聚合字段、聚合人员字段、聚合日期字段、聚合函数、APL信息、月度考勤表字段、单位等

### 2. SalaryKPIInvalidAction - 工资绩效指标作废Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryKPIInvalidAction.java`

**作废规则**:
- 当未被工资项使用时，支持作废
- 当指标被工资项使用后，不可作废
- 特殊地，对于预置的月度考勤表相关的指标，不支持作废

**实现逻辑**:
1. 检查是否为预置的月度考勤表相关指标
2. 检查指标是否被工资项使用
3. 如果满足作废条件，允许作废操作

### 3. SalaryKPIBulkInvalidAction - 工资绩效指标批量作废Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryKPIBulkInvalidAction.java`

**批量作废规则**:
- 与单个作废规则相同
- 批量验证所有指标是否满足作废条件
- 如果任何一个指标不满足条件，整个批量操作失败

**实现逻辑**:
1. 批量获取指标数据
2. 验证每个指标是否可以作废
3. 如果所有指标都满足条件，执行批量作废

## 支持的DAO方法

### SalaryItemDao 新增方法

**方法**: `getSalaryItemsByKpiId(String tenantId, String kpiId)`
- 功能：根据KPI ID查询使用该指标的工资项
- 参数：租户ID、KPI指标ID
- 返回：使用该指标的工资项列表

### SalaryKPIDao 新增方法

1. **方法**: `getById(String tenantId, String kpiId)`
   - 功能：根据ID获取单个KPI指标
   - 参数：租户ID、KPI指标ID
   - 返回：KPI指标对象

2. **方法**: `isPresetMonthlyAttendanceKpi(IObjectData kpiData)`
   - 功能：检查是否为预置的月度考勤表相关指标
   - 参数：KPI指标数据
   - 返回：true表示是预置的月度考勤表指标，不可作废

## 测试用例

### SalaryKPIActionTest
- 测试编辑action在不同场景下的行为
- 测试作废action在不同场景下的行为

### SalaryKPIBulkInvalidActionTest
- 测试批量作废action在不同场景下的行为
- 包括成功场景和各种失败场景

## 验证逻辑

### 编辑验证
1. 查询指标是否被工资项使用
2. 如果被使用，比较新旧数据的关键字段
3. 如果修改了不允许的字段，抛出验证异常

### 作废验证
1. 检查是否为预置月度考勤表指标
2. 检查是否被工资项使用
3. 如果不满足作废条件，抛出验证异常

### 批量作废验证
1. 批量获取指标数据
2. 逐个验证每个指标
3. 如果任何一个不满足条件，整个操作失败

## 错误处理

所有action都包含详细的错误信息：
- 指标不存在
- 预置指标不可作废
- 被工资项使用的指标限制编辑/作废
- 批量操作中部分指标不存在

## 日志记录

所有关键操作都有详细的日志记录：
- 操作开始和结束
- 验证结果
- 错误信息
- 使用该指标的工资项信息

## 性能优化

1. 批量查询：批量作废action使用批量查询减少数据库访问
2. 早期验证：在操作前进行所有验证，避免部分成功的情况
3. 详细日志：提供足够的信息用于问题排查

## 使用说明

1. 编辑指标时，系统会自动检查使用情况并限制可编辑字段
2. 作废指标时，系统会检查预置状态和使用情况
3. 批量作废时，所有指标必须都满足作废条件
4. 所有操作都有详细的错误提示和日志记录
