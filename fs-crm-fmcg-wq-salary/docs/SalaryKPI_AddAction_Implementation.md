# 工资绩效指标新建Action实现文档

## 概述

本文档描述了工资绩效指标对象的新建(add)action的实现，特别是考勤类型选项不允许重复的逻辑。

## 实现的功能

### 1. SalaryKPIAddAction - 工资绩效指标新建Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryKPIAddAction.java`

**新建规则**:
1. 当指标计算方式为"月度考勤表"时，考勤类型选项不允许重复
2. 验证必填字段和字段依赖关系
3. 根据计算方式自动清理不相关字段

### 2. SalaryKPIEditAction 增强 - 工资绩效指标编辑Action

**文件位置**: `fs-crm-fmcg-wq-salary\src\main\java\com\facishare\crm\fmcg\wq\action\SalaryKPIEditAction.java`

**编辑增强**:
- 在未被工资项使用时，增加考勤类型重复性检查
- 确保编辑时也遵循考勤字段唯一性规则

## 核心验证逻辑

### 考勤类型重复性检查

当指标计算方式为"月度考勤表"时：
1. 检查月度考勤表字段是否为必填
2. 查询是否已有其他指标使用相同的考勤字段
3. 如果存在重复，抛出详细的验证异常

### 字段依赖关系验证

根据不同的指标计算方式，验证相应的必填字段：

#### 1. 对象计算方式 (INDICATOR_CALC_METHOD_Options_1)
- **必填字段**：
  - 聚合对象 (AGGREGATED_OBJECT)
  - 聚合字段 (AGGREGATED_FIELD)
  - 聚合函数 (AGGREGATION_FUNCTION)
- **清空字段**：
  - 月度考勤表字段 (MONTHLY_ATTENDANCE_FIELD)
  - APL信息 (APL_INFO)

#### 2. 月度考勤表计算方式 (INDICATOR_CALC_METHOD_Options_2)
- **必填字段**：
  - 月度考勤表字段 (MONTHLY_ATTENDANCE_FIELD)
- **唯一性检查**：
  - 确保考勤字段在租户内唯一

#### 3. APL函数计算方式 (INDICATOR_CALC_METHOD_Options_3)
- **必填字段**：
  - APL信息 (APL_INFO)
- **清空字段**：
  - 聚合对象相关字段
  - 月度考勤表字段

## 支持的DAO方法

### SalaryKPIDao 新增方法

1. **方法**: `getKpisByAttendanceField(String tenantId, String attendanceField)`
   - 功能：根据月度考勤表字段查询使用该字段的KPI指标
   - 参数：租户ID、月度考勤表字段值
   - 返回：使用该考勤字段的KPI指标列表

2. **方法**: `getKpisByAttendanceFieldExcludeId(String tenantId, String attendanceField, String excludeKpiId)`
   - 功能：根据月度考勤表字段查询使用该字段的KPI指标（排除指定ID）
   - 参数：租户ID、月度考勤表字段值、要排除的KPI指标ID
   - 返回：使用该考勤字段的KPI指标列表（用于编辑时的重复性检查）

## 考勤字段映射

系统支持以下考勤字段类型，每种类型在租户内只能被一个指标使用：

| 字段值 | 显示名称 | 说明 |
|--------|----------|------|
| ruleDaysNum | 应出勤天数（天） | 员工应该出勤的天数 |
| checkDayNum | 正常出勤（天） | 员工实际正常出勤的天数 |
| absentDays | 旷工（天） | 员工旷工的天数 |
| waiQinDaysNum | 外勤（天） | 员工外勤的天数 |
| checkWorkTime | 实际工作时长（小时） | 员工实际工作的小时数 |
| overTime | 加班时长（小时） | 员工加班的小时数 |
| laterNum | 迟到（次） | 员工迟到的次数 |
| laterTime | 迟到（分钟） | 员工迟到的总分钟数 |
| earlyNum | 早退（次） | 员工早退的次数 |
| earlyTime | 早退（分钟） | 员工早退的总分钟数 |
| missNum | 未打卡（次） | 员工未打卡的次数 |
| locationExNum | 不在考勤范围（次） | 员工不在考勤范围的次数 |

## 错误处理

### 考勤字段重复错误
```
考勤类型选项[应出勤天数（天）]已被其他指标使用，不允许重复。使用该考勤字段的指标: 现有指标名称
```

### 必填字段验证错误
- `指标计算方式为必填字段`
- `指标计算方式为月度考勤表时，月度考勤表字段为必填字段`
- `指标计算方式为对象时，聚合对象为必填字段`
- `指标计算方式为APL函数时，APL信息为必填字段`

## 测试用例

### SalaryKPIAddActionTest
包含以下测试场景：

1. **考勤类型指标新建成功**：
   - 考勤字段未被使用时允许新建

2. **考勤类型指标重复字段异常**：
   - 考勤字段已被使用时抛出异常

3. **对象计算方式指标新建成功**：
   - 验证必填字段和字段清理

4. **对象计算方式缺少必填字段异常**：
   - 缺少聚合对象时抛出异常

5. **APL函数计算方式指标新建成功**：
   - 验证APL信息必填和字段清理

6. **缺少指标计算方式异常**：
   - 未指定计算方式时抛出异常

7. **考勤类型缺少考勤字段异常**：
   - 月度考勤表方式但未指定考勤字段时抛出异常

## 日志记录

所有关键操作都有详细的日志记录：
- 新建操作开始和结束
- 考勤字段重复性检查结果
- 字段验证结果
- 错误信息和使用情况

## 性能优化

1. **精确查询**：只查询特定计算方式和考勤字段的指标
2. **早期验证**：在操作前进行所有验证，避免部分成功的情况
3. **详细错误信息**：提供足够的信息用于问题排查

## 使用说明

1. **新建考勤类型指标**：
   - 选择"月度考勤表"计算方式
   - 选择考勤字段（系统会检查唯一性）
   - 系统自动验证并阻止重复

2. **新建对象计算方式指标**：
   - 选择"对象"计算方式
   - 填写聚合对象、聚合字段、聚合函数
   - 系统自动清空考勤相关字段

3. **新建APL函数指标**：
   - 选择"APL函数"计算方式
   - 填写APL信息
   - 系统自动清空其他计算方式字段

4. **编辑指标**：
   - 未被工资项使用时，系统会检查考勤字段唯一性
   - 被工资项使用时，仅允许编辑名称和说明

## 业务规则总结

1. **考勤字段唯一性**：每个考勤字段在租户内只能被一个指标使用
2. **计算方式依赖**：不同计算方式有不同的必填字段要求
3. **字段自动清理**：切换计算方式时自动清理不相关字段
4. **编辑限制**：被工资项使用的指标限制编辑范围
5. **详细错误提示**：提供具体的错误信息和解决建议
