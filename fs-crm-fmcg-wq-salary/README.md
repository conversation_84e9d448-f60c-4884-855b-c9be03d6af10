# 薪资系统对象常量及关系梳理

## 1. 核心对象

薪资系统主要包含以下核心对象：

| 对象名称 | API名称 | 描述 |
|---------|---------|------|
| 员工绩效指标 | EmployeeKPIObj | 定义员工绩效指标，用于薪资计算 |
| 工资条 | SalaryDataObj | 员工工资条主表 |
| 工资发放单 | SalaryPaymentSlipObj | 工资发放单据 |
| 工资条明细 | SalaryDetailDataObj | 工资条的明细项目 |
| 员工固定工资明细 | EmployeeFixedSalaryDetailObj | 员工固定工资的明细项 |
| 员工固定工资表 | EmployeeFixedSalaryObj | 员工固定工资主表 |
| 工资规则 | SalaryRuleObj | 定义薪资计算规则 |
| 工资项 | SalaryItemObj | 定义工资组成项目 |

## 2. 字段常量

### 2.1 员工绩效指标 (EmployeeKPIFields)

```java
// 指标计算方式
public static final String INDICATOR_CALC_METHOD = "indicator_calc_method";
    // 对象
    public static final String INDICATOR_CALC_METHOD_Options_1 = "1";
    // 月度考勤表
    public static final String INDICATOR_CALC_METHOD_Options_2 = "2";
    // APL函数
    public static final String INDICATOR_CALC_METHOD_Options_3 = "3";

// 聚合对象
public static final String AGGREGATED_OBJECT = "aggregated_object";
// 聚合人员字段
public static final String AGGREGATED_PERSON_FIELD = "aggregated_person_field";
// 聚合字段
public static final String AGGREGATED_FIELD = "aggregated_field";
// APL信息
public static final String APL_INFO = "apl_info";
// 指标描述
public static final String INDICATOR_DESC = "indicator_desc";
// 月度考勤表字段
public static final String MONTHLY_ATTENDANCE_FIELD = "monthly_attendance_field";
```

### 2.2 工资项 (SalaryItemFields)

```java
// 取值方式
public static final String VALUE_TYPE = "value_type";
    // 计算公式
    public static final String VALUE_TYPE_Options_2 = "2";
    // 固定值
    public static final String VALUE_TYPE_Options_1 = "1";

// 工资项说明
public static final String SALARY_ITEM_DESCRIPTION = "salary_item_description";
// 计算公式
public static final String CALCULATION_FORMULA = "calculation_formula";
// 增减属性
public static final String INCREMENT_DECREMENT_ATTRIB = "increment_decrement_attrib";
    // 扣减项
    public static final String INCREMENT_DECREMENT_ATTRIB_Options_2 = "2";
    // 应发项
    public static final String INCREMENT_DECREMENT_ATTRIB_Options_1 = "1";

// 公式包含指标
public static final String FORMULA_INCLUDES_KPI = "formula_includes_kpi";
```

### 2.3 工资规则 (SalaryRuleFields)

```java
// 发放单自动创建时机
public static final String DISTRIBUTION_TIME = "distribution_time";
// 工资项
public static final String SALARY_ITEM = "salary_item";
// 计算公式
public static final String CALCULATION_FORMULA = "calculation_formula";
// 发放周期
public static final String DISTRIBUTION_CYCLE = "distribution_cycle";
    // 按日发放
    public static final String DISTRIBUTION_CYCLE_Options_1 = "1";
    // 按周发放
    public static final String DISTRIBUTION_CYCLE_Options_2 = "2";
    // 按月发放
    public static final String DISTRIBUTION_CYCLE_Options_3 = "3";
```

### 2.4 工资条明细 (SalaryDetailDataFields)

```java
// 金额
public static final String AMOUNT = "amount";
// 工资规则
public static final String SALARY_RULE = "salary_rule";
// 公式赋值
public static final String FORMULA_ASSIGNMENT = "formula_assignment";
// 工资项
public static final String SALARY_ITEM = "salary_item";
// 计算公式
public static final String CALCULATION_FORMULA = "calculation_formula";
// 员工固定工资表
public static final String EMPLOYEE_FIXED_SALARY = "employee_fixed_salary";
// 发放状态
public static final String DISTRIBUTION_STATUS = "distribution_status";
```

### 2.5 员工固定工资表 (EmployeeFixedSalaryFields)

```java
// 工资规则
public static final String SALARY_RULE = "salary_rule";
// 员工(内部)
public static final String EMPLOYEE = "employee";
// 员工(外部)
public static final String EMPLOYEE_EXTERNAL = "employee_external";
// 定薪方式
public static final String SALARY_METHOD = "salary_method";
    // 日薪
    public static final String SALARY_METHOD_Options_1 = "1";
    // 周薪
    public static final String SALARY_METHOD_Options_2 = "2";
    // 月薪
    public static final String SALARY_METHOD_Options_3 = "3";
```

## 3. 枚举类型

### 3.1 KPI计算类型 (KPICalculateType)

```java
public enum KPICalculateType {
    OBJECT("object"),        // 对象聚合计算
    KAOQIN_STAT("kaoqin_stat"), // 考勤统计
    APL("apl");              // APL函数计算
}
```

### 3.2 考勤字段类型 (KaoQinFieldType)

```java
public enum KaoQinFieldType {
    ruleDaysNum("ruleDaysNum", "应出勤天数（天）"),
    checkDayNum("checkDayNum", "正常出勤（天）"),
    absentDays("absentDays", "旷工（天）"),
    waiQinDaysNum("waiQinDaysNum", "外勤（天）"),
    checkWorkTime("checkWorkTime", "实际工作时长（小时）"),
    overTime("overTime", "加班时长（小时）"),
    laterNum("laterNum", "迟到（次）"),
    laterTime("laterTime", "迟到（分钟）"),
    earlyNum("earlyNum", "早退（次）"),
    earlyTime("earlyTime", "早退（分钟）"),
    missNum("missNum", "未打卡（次）"),
    locationExNum("locationExNum", "不在考勤范围（次）")
}
```

### 3.3 聚合函数 (AggregateFunction)

```java
public enum AggregateFunction {
    COUNT("count", "计数"),
    SUM("sum", "求和"),
    MAX("max", "最大值"),
    MIN("min", "最小值"),
    AVG("avg", "平均值")
}
```

## 4. 对象关系图

```
+---------------------+       +-------------------+       +-------------------+
| EmployeeFixedSalary |------>| SalaryRule        |------>| SalaryItem        |
+---------------------+       +-------------------+       +-------------------+
         |                           |                           |
         |                           |                           |
         v                           |                           v
+---------------------+              |             +-------------------+
| EmployeeFixedSalary |              |             | EmployeeKPI       |
| Detail              |              |             +-------------------+
+---------------------+              |                      ^
         |                           |                      |
         |                           v                      |
         |                    +-------------------+         |
         +-------------------->| SalaryDetailData |<--------+
                              +-------------------+
                                       |
                                       |
                                       v
                              +-------------------+       +-------------------+
                              | SalaryData        |------>| SalaryPaymentSlip |
                              +-------------------+       +-------------------+
```

## 5. 计算流程

1. 从`EmployeeFixedSalary`获取员工固定薪资信息和关联的`SalaryRule`
2. 从`SalaryRule`获取关联的`SalaryItem`列表
3. 对每个`SalaryItem`：
   - 如果是固定值类型，从`EmployeeFixedSalaryDetail`获取金额
   - 如果是计算公式类型：
     - 获取公式中包含的`EmployeeKPI`列表
     - 根据KPI类型(OBJECT/KAOQIN_STAT/APL)使用对应的计算器计算KPI值
     - 将KPI值代入公式计算最终金额
4. 生成`SalaryDetailData`记录
5. 汇总生成`SalaryData`(工资条)
6. 关联到`SalaryPaymentSlip`(工资发放单)进行发放

## 6. 设计模式应用

### 6.1 命令模式

通过`SalaryCommand`接口和具体命令类实现：
- `CalculateSalaryCommand`: 计算薪资
- `PaySalaryCommand`: 发放薪资
- `BatchPaySalaryCommand`: 批量发放薪资
- `ExportSalaryDetailsCommand`: 导出薪资明细

### 6.2 装饰器模式

通过`SalaryKPICalculatorDecorator`及其子类实现：
- `LoggingSalaryKPICalculator`: 为KPI计算添加日志功能
- `CachingSalaryKPICalculator`: 为KPI计算添加缓存功能

### 6.3 工厂模式

通过`SalaryKPIFactory`实现，根据KPI类型创建不同的KPI对象和计算器：
- `AggregateSalaryKPI` + `AggregateSalaryKPICalculator`
- `KaoQinStatSalaryKPI` + `KaoQinStatSalaryKPICalculator`
- `AplSalaryKPI` + `AplSalaryKPICalculator`
