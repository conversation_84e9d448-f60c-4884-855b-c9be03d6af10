package com.facishare.crm.fmcg.wq.factory;

import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.crm.fmcg.wq.model.KPICalculateType;
import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.kpi.AggregateSalaryKPI;
import com.facishare.crm.fmcg.wq.model.kpi.AplSalaryKPI;
import com.facishare.crm.fmcg.wq.model.kpi.KaoQinStatSalaryKPI;
import com.facishare.crm.fmcg.wq.model.kpi.SalaryKPI;
import com.facishare.crm.fmcg.wq.service.SalaryKPICalculator;
import com.facishare.crm.fmcg.wq.service.decorator.CachingSalaryKPICalculator;
import com.facishare.crm.fmcg.wq.service.decorator.LoggingSalaryKPICalculator;
import com.facishare.crm.fmcg.wq.service.decorator.RetrySalaryKPICalculator;
import com.facishare.crm.fmcg.wq.service.decorator.SalaryKPICalculatorDecorator;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 薪资KPI工厂类，用于创建不同类型的KPI对象和装饰计算器
 * @author: dev
 * @create: 2024-07-10
 */
@Slf4j
@Component
public class SalaryKPIFactory {

    @Autowired
    private ApplicationContext applicationContext;

    // 缓存已装饰的计算器
    private final Map<KPICalculateType, SalaryKPICalculator> decoratedCalculators = new HashMap<>();

    @PostConstruct
    public void init() {
        // 初始化装饰后的计算器
        for (KPICalculateType type : KPICalculateType.values()) {
            decoratedCalculators.put(type, createDecoratedCalculator(type));
        }
        log.info("SalaryKPIFactory 初始化完成，已创建装饰计算器: {}", decoratedCalculators.keySet());
    }

    /**
     * 创建并装饰KPI计算器
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private SalaryKPICalculator createDecoratedCalculator(KPICalculateType type) {
        // 获取基础计算器
        SalaryKPICalculator baseCalculator = applicationContext.getBean(type.getBeanName(), SalaryKPICalculator.class);

        // 应用装饰器（按照从内到外的顺序）
        SalaryKPICalculator decoratedCalculator = baseCalculator;

        // 1. 添加重试装饰器（最内层，直接包装基础计算器）
        decoratedCalculator = new RetrySalaryKPICalculator(decoratedCalculator);

        // 2. 添加日志装饰器（中间层，记录重试过程）
        decoratedCalculator = new LoggingSalaryKPICalculator<>(decoratedCalculator);

        // 3. 添加缓存装饰器（最外层，缓存成功的计算结果）
        decoratedCalculator = new CachingSalaryKPICalculator<>(decoratedCalculator);

        log.info("创建装饰计算器: {}，装饰器链: 缓存 -> 日志 -> 重试 -> 基础计算器", type);
        return decoratedCalculator;
    }

    /**
     * 计算KPI值
     */
    public MetricCalculateResult calculate(SalaryContext context, IObjectData kpiObj) {
        // 获取KPI类型
        KPICalculateType type = KPICalculateType.of(kpiObj.get(SalaryKPIFields.INDICATOR_CALC_METHOD, String.class));

        // 创建正确类型的KPI对象
        SalaryKPI kpi;
        switch (type) {
            case OBJECT:
                kpi = AggregateSalaryKPI.of(kpiObj);
                break;
            case APL:
                kpi = AplSalaryKPI.of(kpiObj);
                break;
            case KAOQIN_STAT:
                kpi = KaoQinStatSalaryKPI.of(kpiObj);
                break;
            default:
                throw new ValidateException("不支持的KPI类型: " + type); //ignoreI18n
        }

        // 获取装饰后的计算器并计算
        SalaryKPICalculator calculator = decoratedCalculators.get(type);
        if (calculator == null) {
            throw new ValidateException("未找到KPI计算器: " + type); //ignoreI18n
        }

        return calculator.calculateWithKPI(context, kpi);
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCaches() {
        decoratedCalculators.values().forEach(calculator -> {
            CachingSalaryKPICalculator<?> cachingCalculator = findCachingDecorator(calculator);
            if (cachingCalculator != null) {
                cachingCalculator.clearCache();
            }
        });
        log.info("已清除所有KPI计算器缓存");
    }

    /**
     * 查找缓存装饰器
     */
    private CachingSalaryKPICalculator<?> findCachingDecorator(SalaryKPICalculator calculator) {
        if (calculator instanceof CachingSalaryKPICalculator) {
            return (CachingSalaryKPICalculator<?>) calculator;
        } else if (calculator instanceof SalaryKPICalculatorDecorator) {
            return findCachingDecorator(((SalaryKPICalculatorDecorator<?>) calculator).getDecorated());
        }
        return null;
    }

    /**
     * 创建KPI对象
     */
    public static SalaryKPI createKPI(IObjectData data) {
        if (data == null) {
            throw new ValidateException("KPI数据不能为空"); //ignoreI18n
        }

        try {
            KPICalculateType type = KPICalculateType.of(data.get(SalaryKPIFields.INDICATOR_CALC_METHOD, String.class));

            switch (type) {
                case OBJECT:
                    return AggregateSalaryKPI.of(data);
                case APL:
                    return AplSalaryKPI.of(data);
                case KAOQIN_STAT:
                    return KaoQinStatSalaryKPI.of(data);
                default:
                    throw new ValidateException("不支持的KPI类型: " + type); //ignoreI18n
            }
        } catch (Exception e) {
            log.error("创建KPI对象失败", e);
            throw new ValidateException("创建KPI对象失败: " + e.getMessage()); //ignoreI18n
        }
    }
}
