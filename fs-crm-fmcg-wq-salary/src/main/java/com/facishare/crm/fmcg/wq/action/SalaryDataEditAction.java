package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;

/**
 * 工资条编辑Action
 * 
 * 功能：阻止前端直接编辑工资条
 */
@Slf4j
public class SalaryDataEditAction extends FmcgUnsupportedExceptionAction {

//    @Override
//    protected void before(Arg arg) {
//        log.error("工资条编辑操作被阻止，不允许前端直接编辑工资条");
//        throw new ValidateException("不允许直接编辑工资条，请通过工资条明细修正功能进行修改"); //ignoreI18n
//    }
}
