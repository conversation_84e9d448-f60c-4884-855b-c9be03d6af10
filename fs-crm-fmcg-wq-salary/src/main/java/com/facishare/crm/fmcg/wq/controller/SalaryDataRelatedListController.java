package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.dao.SalaryPaymentSlipDao;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2024-03-19 16:18
 **/
public class SalaryDataRelatedListController extends StandardRelatedListController {
    SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);
    SalaryPaymentSlipDao salaryPaymentSlipDao = SpringUtil.getContext().getBean(SalaryPaymentSlipDao.class);
    
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);

        //补充 额外数据
        if (arg.getTargetObjectApiName() != null && SalaryPaymentSlipFields.API_NAME.equals(arg.getTargetObjectApiName())) {
            String salaryPaymentSlipId = arg.getTargetObjectDataId();
            if (CollectionUtils.isNotEmpty(result.getDataList())) {
                // 查表头，补数据
                IObjectData salaryPaymentSlipObj = salaryPaymentSlipDao.getById(controllerContext.getTenantId(), salaryPaymentSlipId);
                List<String> salaryTableHeaders = salaryService.getSalaryTableHeaders(salaryPaymentSlipObj);
                for (ObjectDataDocument objectDataDocument : result.getDataList()) {
                    //工资条ID
                    String salaryDataId = objectDataDocument.getId();
                    
                    // 使用服务方法获取聚合数据
                    Map<String, String> aggregatedValues = salaryService.getSalaryDataAggregatedValues(
                        controllerContext.getTenantId(), 
                        salaryDataId);
                    
                    // 设置聚合结果到objectDataDocument
                    if (aggregatedValues != null && !aggregatedValues.isEmpty()) {
                        objectDataDocument.putAll(aggregatedValues);
                    }
                    //根据表头再便利补数据，因为key 有xxxxkpi_xxx 来代表
                    for (String salaryTableHeader : salaryTableHeaders) {
                        if(objectDataDocument.containsKey(salaryTableHeader) || !salaryTableHeader.contains("kpi_")){
                            continue;
                        }
                        objectDataDocument.put(salaryTableHeader, aggregatedValues.get(salaryTableHeader.substring(salaryTableHeader.indexOf("kpi_"))));
                    }
                }
            }
        }
        return result;
    }
}
