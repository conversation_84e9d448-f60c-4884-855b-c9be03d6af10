package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;

/**
 * 工资条明细新增Action
 * 
 * 功能：阻止前端直接新增工资条明细
 */
@Slf4j
public class SalaryDetailDataAddAction extends FmcgUnsupportedExceptionAction {

//    @Override
//    protected void before(Arg arg) {
//        log.error("工资条明细新增操作被阻止，不允许前端直接新增工资条明细");
//        throw new ValidateException("不允许直接新增工资条明细，请通过工资发放单生成"); //ignoreI18n
//    }
}
