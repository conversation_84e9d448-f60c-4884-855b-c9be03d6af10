
package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryDetailFields;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.crm.fmcg.wq.util.SearchQuery.SearchQueryBuilder;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class EmployeeFixedSalaryDao extends AbstractDao{
    final static List<String> employeeFixedSalaryFields = ConfigUtils.getFields(EmployeeFixedSalaryFields.class);



    public List<IObjectData> getByIds(String tenantId, List<String> kpiIds) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), kpiIds)
                .build(), EmployeeFixedSalaryFields.API_NAME, employeeFixedSalaryFields);
    }
    public IObjectData getById(String tenantId, String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        List<IObjectData> byIds = getByIds(tenantId, Lists.newArrayList(id));
        if (CollectionUtils.isNotEmpty(byIds)) {
            return byIds.get(0);
        }
        return null;
    }
    /**
     * 通过规则id 查所有的员工固定工资
     */
    public List<IObjectData> getBySalaryRuleId(String tenantId, String salaryRuleId) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(EmployeeFixedSalaryFields.SALARY_RULE, salaryRuleId)
                .build(), EmployeeFixedSalaryFields.API_NAME, employeeFixedSalaryFields);
    }
    /**
     * 通过员工id 查员工固定工资信息
     */
    public IObjectData getByEmployeeId(String tenantId, String employeeId) {
        if (employeeId == null) {
            return null;
        }
        boolean isOuter = false;
        //判断员工是内部还是外部
        if (Integer.valueOf(employeeId).intValue() > 100000000) {
            isOuter = true;
        }
        // 内外部部员工都要匹配
        SearchQueryBuilder searchQuery = SearchQuery.builder();
        if (isOuter) {
            searchQuery.eq(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, employeeId);
        }else{
            searchQuery.eq(EmployeeFixedSalaryFields.EMPLOYEE, employeeId);
        }
        List<IObjectData> iObjectDataList = getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), searchQuery.build(), EmployeeFixedSalaryFields.API_NAME, employeeFixedSalaryFields);
        if (iObjectDataList.size() > 1) {
            log.error("employeeId:{} 查询到多个固定工资信息", employeeId);
        }
        return iObjectDataList.isEmpty() ? null : iObjectDataList.get(0);

    }

    /**
     * 批量查询员工固定工资信息
     * @param tenantId 租户ID
     * @param employeeIds 员工ID列表
     * @return 员工固定工资信息列表
     */
    public List<IObjectData> getByEmployeeIds(String tenantId, List<String> employeeIds) {
        if (employeeIds == null || employeeIds.isEmpty()) {
            return Lists.newArrayList();
        }

        // 分离内部员工和外部员工ID
        List<String> internalEmployeeIds = employeeIds.stream()
                .filter(id -> Integer.valueOf(id).intValue() <= 100000000)
                .collect(Collectors.toList());

        List<String> externalEmployeeIds = employeeIds.stream()
                .filter(id -> Integer.valueOf(id).intValue() > 100000000)
                .collect(Collectors.toList());

        List<IObjectData> result = Lists.newArrayList();

        // 查询内部员工固定工资信息
        if (!internalEmployeeIds.isEmpty()) {
            List<IObjectData> internalResults = getAllIObjectDataListByQueryWithFields(
                    User.systemUser(tenantId),
                    SearchQuery.builder()
                            .in(EmployeeFixedSalaryFields.EMPLOYEE, internalEmployeeIds)
                            .build(),
                    EmployeeFixedSalaryFields.API_NAME,
                    employeeFixedSalaryFields);
            result.addAll(internalResults);
        }

        // 查询外部员工固定工资信息
        if (!externalEmployeeIds.isEmpty()) {
            List<IObjectData> externalResults = getAllIObjectDataListByQueryWithFields(
                    User.systemUser(tenantId),
                    SearchQuery.builder()
                            .in(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, externalEmployeeIds)
                            .build(),
                    EmployeeFixedSalaryFields.API_NAME,
                    employeeFixedSalaryFields);
            result.addAll(externalResults);
        }

        log.info("批量查询员工固定工资信息，查询员工数: {}, 返回结果数: {}", employeeIds.size(), result.size());
        return result;
    }

    /**
     * 批量查询员工固定工资信息（包含关联对象）
     * @param tenantId 租户ID
     * @param employeeIds 员工ID列表
     * @return 员工固定工资信息列表（包含关联对象）
     */
    public List<IObjectData> getByEmployeeIdsWithRefObjects(String tenantId, List<String> employeeIds) {
        List<IObjectData> resultList = getByEmployeeIds(tenantId, employeeIds);
        // 填充关联对象信息
        return fillRefObject(User.systemUser(tenantId), EmployeeFixedSalaryFields.API_NAME, resultList);
    }

    /**
     * 批量查询员工固定工资信息（按定薪方式过滤）
     * @param tenantId 租户ID
     * @param employeeIds 员工ID列表
     * @param salaryMethod 定薪方式
     * @return 员工固定工资信息列表
     */
    public List<IObjectData> getByEmployeeIdsAndSalaryMethod(String tenantId, List<String> employeeIds, String salaryMethod) {
        if (employeeIds == null || employeeIds.isEmpty()) {
            return Lists.newArrayList();
        }

        if (StringUtils.isBlank(salaryMethod)) {
            log.warn("定薪方式为空，使用普通查询");
            return getByEmployeeIds(tenantId, employeeIds);
        }

        // 分离内部员工和外部员工ID
        List<String> internalEmployeeIds = Lists.newArrayList();
        List<String> externalEmployeeIds = Lists.newArrayList();

        for (String employeeId : employeeIds) {
            if (StringUtils.isNotBlank(employeeId)) {
                try {
                    int id = Integer.parseInt(employeeId);
                    if (id < 100000000) {
                        internalEmployeeIds.add(employeeId);
                    } else {
                        externalEmployeeIds.add(employeeId);
                    }
                } catch (NumberFormatException e) {
                    log.warn("员工ID格式错误: {}", employeeId);
                }
            }
        }

        List<IObjectData> resultList = Lists.newArrayList();

        // 查询内部员工
        if (!internalEmployeeIds.isEmpty()) {
            SearchQuery.SearchQueryBuilder queryBuilder = SearchQuery.builder()
                    .in(EmployeeFixedSalaryFields.EMPLOYEE, internalEmployeeIds)
                    .eq(EmployeeFixedSalaryFields.SALARY_METHOD, salaryMethod);

            List<IObjectData> internalResults = getAllIObjectDataListByQuery(
                    User.systemUser(tenantId),
                    queryBuilder.build(),
                    EmployeeFixedSalaryFields.API_NAME
            );
            resultList.addAll(internalResults);
        }

        // 查询外部员工
        if (!externalEmployeeIds.isEmpty()) {
            SearchQuery.SearchQueryBuilder queryBuilder = SearchQuery.builder()
                    .in(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, externalEmployeeIds)
                    .eq(EmployeeFixedSalaryFields.SALARY_METHOD, salaryMethod);

            List<IObjectData> externalResults = getAllIObjectDataListByQuery(
                    User.systemUser(tenantId),
                    queryBuilder.build(),
                    EmployeeFixedSalaryFields.API_NAME
            );
            resultList.addAll(externalResults);
        }

        log.info("按定薪方式查询员工固定工资信息，查询员工数: {}, 定薪方式: {}, 返回结果数: {}",
                employeeIds.size(), salaryMethod, resultList.size());
        return resultList;
    }
}
