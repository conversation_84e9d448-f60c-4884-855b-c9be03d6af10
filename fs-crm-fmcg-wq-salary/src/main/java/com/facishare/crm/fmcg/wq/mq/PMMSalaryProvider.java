package com.facishare.crm.fmcg.wq.mq;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.rocketmq.producer.AutoConfMQProducer;
import com.fxiaoke.rocketmq.producer.DefaultTopicMessage;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * PMM 薪资同步 计算
 * checkins-v2-task 调度使用
 */
@Component
@Slf4j
public class PMMSalaryProvider {

    @Autowired
    private SalaryRuleDao salaryRuleDao;

    @Autowired
    private EIEAConverter eieaConverter;

    private AutoConfMQProducer autoConfRocketMQSender;
    @PostConstruct
    private void init() {
        log.info("start init rocketmq PMMSalaryProvider!");
        autoConfRocketMQSender = new AutoConfMQProducer("rocketmq-consumer.ini", "common,name_server_17,producer_fmcg_salary");
        log.info("finish init rocketmq PMMSalaryProvider!");
    }

    public void enqueue(SalaryTaskMessage message) {
        // 确保 MQ 已初始化
        if (autoConfRocketMQSender == null) {
            init();
        }
        DefaultTopicMessage messageExt = new DefaultTopicMessage(message.toProto());
        autoConfRocketMQSender.send(messageExt);
        log.info("PMMSalaryProvider enqueue:{}, completed!", message.toString());
    }

    /**
     * 处理指定租户的工资规则，发送消息
     *
     * @param tenantId 租户ID
     * @param flag 0-生成工资明细，1-生成发放单
     * @param currentDate 当前日期
     * @return 处理结果统计 [成功数量, 失败数量]
     */
    public int[] processSalaryRulesForTenant(String tenantId, int flag, LocalDate currentDate) {
        try {
            log.info("开始处理租户 {} 的工资规则，flag={}", tenantId, flag);
            //灰度开关
            if (!FmcgGray.Checkins.EI.salaryTask.gray(tenantId)) {
                log.info("租户 {} 未开启薪资规则，跳过处理", tenantId);
                return new int[]{0, 0};
            }

            // 查询该租户的工资规则
            List<IObjectData> salaryRules = getSalaryRulesForTenant(tenantId, flag);

            if (CollectionUtils.isEmpty(salaryRules)) {
                log.info("租户 {} 未找到有效的工资规则，跳过处理", tenantId);
                return new int[]{0, 0};
            }

            log.info("租户 {} 找到 {} 个有效工资规则，开始处理", tenantId, salaryRules.size());

            int successCount = 0;
            int failCount = 0;

            // 为每个工资规则发送消息
            for (IObjectData salaryRule : salaryRules) {
                try {
                    String salaryRuleId = salaryRule.getId();

                    // 根据工资规则的薪资方式计算日期范围
                    String[] dateRange = calculateDateRange(salaryRule, currentDate);
                    String startDateStr = dateRange[0];
                    String endDateStr = dateRange[1];

                    // 创建薪资任务消息
                    SalaryTaskMessage salaryTaskMessage = new SalaryTaskMessage();
                    salaryTaskMessage.setTenantId(tenantId);
                    salaryTaskMessage.setEa(eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId)));
                    salaryTaskMessage.setSalaryRuleId(salaryRuleId);
                    salaryTaskMessage.setStartDateStr(startDateStr);
                    salaryTaskMessage.setDateStr(currentDate.toString());
                    salaryTaskMessage.setEndDateStr(endDateStr);
                    salaryTaskMessage.setFlag(flag);

                    // 在 traceId 后追加规则 ID
                    String originalTraceId = TraceContext.get().getTraceId();
                    String ruleTraceId = originalTraceId + "-" + salaryRuleId;
                    TraceContext.get().setTraceId(ruleTraceId);

                    try {
                        // 发送消息
                        enqueue(salaryTaskMessage);

                        log.info("成功发送薪资任务消息: tenantId={}, salaryRuleId={}, flag={}, startDate={}, endDate={}, traceId={}",
                                tenantId, salaryRuleId, flag, startDateStr, endDateStr, ruleTraceId);
                    } finally {
                        // 恢复原始 traceId
                        TraceContext.get().setTraceId(originalTraceId);
                    }

                    successCount++;
                } catch (Exception e) {
                    log.error("发送薪资任务消息失败: tenantId={}, salaryRuleId={}, flag={}",
                            tenantId, salaryRule.getId(), flag, e);
                    failCount++;
                }
            }

            log.info("租户 {} 薪资任务消息发送完成: 成功={}, 失败={}, flag={}", tenantId, successCount, failCount, flag);
            return new int[]{successCount, failCount};

        } catch (Exception e) {
            log.error("处理租户 {} 工资规则异常: flag={}", tenantId, flag, e);
            throw e;
        }
    }

    /**
     * 获取指定租户的工资规则
     *
     * @param tenantId 租户ID
     * @param flag 0-查询明细规则，1-查询发放单规则
     * @return 工资规则列表
     */
    private List<IObjectData> getSalaryRulesForTenant(String tenantId, int flag) {
        try {
            Date currentDate = new Date();

            if (flag == 0) {
                // 查询需要生成明细的规则
                return salaryRuleDao.queryRulesByDateForDetail(tenantId, currentDate);
            } else {
                // 查询需要生成发放单的规则
                return salaryRuleDao.queryRulesByDate(tenantId, currentDate);
            }
        } catch (Exception e) {
            log.error("查询租户 {} 的工资规则失败 (flag={})", tenantId, flag, e);
            throw e;
        }
    }

    /**
     * 根据工资规则的薪资方式计算日期范围
     *
     * @param salaryRule 工资规则
     * @param currentDate 当前日期
     * @return 日期范围数组 [开始日期, 结束日期]
     */
    private String[] calculateDateRange(IObjectData salaryRule, LocalDate currentDate) {
        try {
            // 获取薪资方式
            String salaryMethod = (String) salaryRule.get(SalaryRuleFields.SALARY_METHOD);

            LocalDate startDate;
            LocalDate endDate;

            if (SalaryRuleFields.SALARY_METHOD_Options_1.equals(salaryMethod)) {
                // 日薪 - 上一天
                startDate = currentDate.minusDays(1);
                endDate = currentDate.minusDays(1);

            } else if (SalaryRuleFields.SALARY_METHOD_Options_2.equals(salaryMethod)) {
                // 周薪 - 上一周（周一到周日）
                LocalDate lastWeekEnd = currentDate.minusDays(1); // 昨天
                LocalDate lastWeekStart = lastWeekEnd.minusDays(6); // 上周一

                // 调整到标准的周一到周日
                int dayOfWeek = lastWeekEnd.getDayOfWeek().getValue(); // 1=周一, 7=周日
                if (dayOfWeek != 7) { // 如果不是周日结束
                    lastWeekEnd = lastWeekEnd.minusDays(dayOfWeek); // 调整到上周日
                    lastWeekStart = lastWeekEnd.minusDays(6); // 对应的周一
                }

                startDate = lastWeekStart;
                endDate = lastWeekEnd;

            } else if (SalaryRuleFields.SALARY_METHOD_Options_3.equals(salaryMethod)) {
                // 月薪 - 上一个月
                LocalDate lastMonth = currentDate.minusMonths(1);
                startDate = lastMonth.withDayOfMonth(1); // 上月1号
                endDate = lastMonth.withDayOfMonth(lastMonth.lengthOfMonth()); // 上月最后一天

            } else {
                // 默认按月薪处理
                log.warn("未知的薪资方式: {}, 默认按月薪处理", salaryMethod);
                LocalDate lastMonth = currentDate.minusMonths(1);
                startDate = lastMonth.withDayOfMonth(1);
                endDate = lastMonth.withDayOfMonth(lastMonth.lengthOfMonth());
            }

            String startDateStr = startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endDateStr = endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            log.debug("工资规则 {} 薪资方式: {}, 计算日期范围: {} 到 {}",
                    salaryRule.getId(), salaryMethod, startDateStr, endDateStr);

            return new String[]{startDateStr, endDateStr};

        } catch (Exception e) {
            log.error("计算工资规则 {} 的日期范围失败，使用默认月薪范围", salaryRule.getId(), e);
            return null;
        }
    }
    @PreDestroy
    public void close() throws IOException {
        autoConfRocketMQSender.close();
    }
}
