package com.facishare.crm.fmcg.wq.service;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;

/**
 * 角色查询服务接口
 * 提供内部业务角色和外部角色的查询功能
 * 
 * <AUTHOR>
 */
public interface RoleQueryService {
    List<String> ADMIN_ROLE_CODES = Lists.newArrayList("pmmAppAdminRole","00000000000000000000000000000006");

    /**
     * 获取当前企业的所有内部业务角色选项
     * 
     * @param tenantId 租户ID
     * @return 角色选项列表，格式为 [{label: "角色名称", value: "角色ID"}]
     */
    List<Map<String, Object>> getInternalRoleOptions(String tenantId);

    /**
     * 获取当前企业的所有外部角色选项
     * 
     * @param tenantId 租户ID
     * @return 角色选项列表，格式为 [{label: "角色名称", value: "角色ID"}]
     */
    @Deprecated //有问题 外部的角色获取不到。先这样 不使用了后续再改
    List<Map<String, Object>> getExternalRoleOptions(String tenantId);

    /**
     * 为字段描述添加角色选项
     * 
     * @param fieldDescribe 字段描述
     * @param fieldApiName 字段API名称
     * @param tenantId 租户ID
     */
    void addRoleOptionsToFieldDescribe(IFieldDescribe fieldDescribe, String fieldApiName, String tenantId);

    /**
     * 根据角色ID列表获取员工ID列表
     */
    List<String> getEmployeeIdsByRoles(String tenantId, List<String> roleIds, boolean isExternal,
                                              boolean isOnlyMainRole);
}
