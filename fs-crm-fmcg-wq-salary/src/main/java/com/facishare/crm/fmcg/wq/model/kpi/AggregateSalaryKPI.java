package com.facishare.crm.fmcg.wq.model.kpi;

import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.crm.fmcg.wq.model.AggregateFunction;
import com.facishare.crm.fmcg.wq.model.KPICalculateType;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class AggregateSalaryKPI extends SalaryKPI {

    private String objectApiName;

    private String aggregateFieldApiName;

    private String aggregateTimeFieldApiName;

    private String aggregateUserFieldApiName;

    private AggregateFunction aggregateFunction;

    private String wheres;

    @SuppressWarnings("Duplicates")
    public static AggregateSalaryKPI of(IObjectData data) {
        AggregateSalaryKPI metric = new AggregateSalaryKPI();

        metric.setTenantId(data.getTenantId());
        metric.setId(data.getId());
        metric.setName(data.getName());
        metric.setDescription(data.get(SalaryKPIFields.INDICATOR_DESC, String.class));
        metric.setKpiCalculateType(KPICalculateType.of(data.get(SalaryKPIFields.INDICATOR_CALC_METHOD, String.class)));

        metric.setObjectApiName(data.get(SalaryKPIFields.AGGREGATED_OBJECT, String.class));
        metric.setAggregateFieldApiName(data.get(SalaryKPIFields.AGGREGATED_FIELD, String.class));
        metric.setAggregateTimeFieldApiName(data.get(SalaryKPIFields.AGGREGATED_DATE_FIELD, String.class));
        metric.setAggregateUserFieldApiName(data.get(SalaryKPIFields.AGGREGATED_PERSON_FIELD, String.class));
        metric.setAggregateFunction(AggregateFunction.fromValue(data.get(SalaryKPIFields.AGGREGATION_FUNCTION, String.class)));
        /**
         * where条件
         */
        metric.setWheres(data.get(SalaryKPIFields.WHERES, String.class));

        return metric;
    }
}