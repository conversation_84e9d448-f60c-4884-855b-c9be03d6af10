package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 员工固定工资表新建导入模板
 * 控制导入模板中包括的列
 *
 * 扩展点：
 *
 * 1 com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction#customHeader（）：在通用的处理逻辑后，控制模板展示的列
 * @author: system
 * @create: 2025-07-11
 **/
@Slf4j
public class EmployeeFixedSalaryInsertImportTemplateAction extends StandardInsertImportTemplateAction {
  
  @Override
  protected void customHeader(List<IFieldDescribe> headerFieldList) {
    super.customHeader(headerFieldList);
    
    // 检查是否缺少owner字段，如果缺少则添加
    boolean hasOwnerField = headerFieldList.stream()
        .anyMatch(field -> BaseField.owner.getApiName().equals(field.getApiName()));
    
    if (!hasOwnerField) {
      log.info("员工固定工资表导入模板缺少owner字段，正在添加");
      addOwnerField(headerFieldList);
    }
    
    log.info("员工固定工资表导入模板字段处理完成，字段数量: {}", headerFieldList.size());
  }
  
  /**
   * 添加owner字段到导入模板
   * 
   * @param headerFieldList 字段列表
   */
  private void addOwnerField(List<IFieldDescribe> headerFieldList) {
    try {
      if (objectDescribe != null) {
        IFieldDescribe ownerFieldDescribe = objectDescribe.getFieldDescribe(BaseField.owner.getApiName());
        
        if (ownerFieldDescribe != null) {
          // 如果对象描述中有owner字段，直接使用
          headerFieldList.add(ownerFieldDescribe);
          log.info("从对象描述中获取owner字段并添加到导入模板");
        }
      }
    } catch (Exception e) {
      log.error("从对象描述中获取owner字段失败: {}", e.getMessage(), e);
    }
  }

}
