package com.facishare.crm.fmcg.wq.mq;

import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.checkins.model.common.RouteTaskMessage;
import com.facishare.appserver.utils.AccountUtils;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDetailDao;
import com.facishare.crm.fmcg.wq.dao.SalaryDetailDataDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.fxiaoke.rocketmq.util.MessageHelper;
import com.github.trace.TraceContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * #快消 pmm 工资计算mq
 *
 * [consumer_fmcg_salary]
 * consumer.group = salary-message-consumer
 * consume.topic = salary-message
 * consume.thread.min = 1
 * consume.thread.max= 10
 * rate.limiter=10
 *
 * [producer_fmcg_salary]
 * producer.group = salary-message-provider
 * producer.default.topic = salary-message
 */
@Slf4j
@Component
public class PMMSalaryConsumer implements ApplicationListener<ContextRefreshedEvent> {

    private AutoConfMQPushConsumer consumer;

    @Autowired
    private SalaryService salaryService;

    @Autowired
    private SalaryRuleDao salaryRuleDao;
    @Autowired
    private EmployeeFixedSalaryDao employeeFixedSalaryDao;
    @Autowired
    private SalaryDetailDataDao salaryDetailDataDao;
    @PostConstruct
    public void init() {
        consumer = new AutoConfMQPushConsumer("rocketmq-consumer.ini", "common,name_server_17,consumer_fmcg_salary",
                (MessageListenerConcurrently) (msgs, context) -> {
                    if (!msgs.isEmpty()) {
                        for (MessageExt msg : msgs) {
                            // 取出traceContext
                            MessageHelper.fillContextFromMessage(TraceContext.get(), msg);
                            try {
                                handleTaskMessage(msg);
                            } catch (Exception e) {
                                log.error("handleTaskMessage error msgId -{}", msg.getMsgId(), e);
                                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                            } finally {
                                TraceContext.remove();
                            }
                        }
                    }
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                });
    }

    @SneakyThrows
    private void handleTaskMessage(MessageExt msg) {
        String msgId = msg.getMsgId();
        // 转成SalaryTaskMessage
        SalaryTaskMessage salaryTaskMessage = new SalaryTaskMessage();
        salaryTaskMessage.fromProto(msg.getBody());
        log.info("msgId:{} handleTaskMessage:{}", msgId, JSONObject.toJSONString(salaryTaskMessage));

        // 调用公共处理方法
        handleSalaryTask(salaryTaskMessage);
    }

    /**
     * 处理薪资任务的公共方法
     * 可被Controller和Consumer共同调用
     * 现在委托给SalaryService处理，保持接口兼容性
     */
    public void handleSalaryTask(SalaryTaskMessage salaryTaskMessage) throws Exception {
        // 委托给SalaryService处理业务逻辑
        salaryService.handleSalaryTask(salaryTaskMessage);
    }

    // 注意：shouldGenerateSalaryData 方法已移动到 SalaryService 中

    // 注意：generateSalaryDataAndPaymentSlip 方法已移动到 SalaryService 中

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (consumer != null && event.getApplicationContext().getParent() == null) {
            consumer.start();
        }
    }
}