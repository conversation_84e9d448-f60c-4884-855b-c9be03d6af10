package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.wq.mq.PMMSalaryConsumer;
import com.facishare.crm.fmcg.wq.mq.SalaryTaskMessage;
import com.facishare.crm.fmcg.wq.schedule.SalaryScheduleTask;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 薪资任务处理Controller
 * 用于手动触发薪资计算任务，调用handleTaskMessage中的逻辑
 */
@Slf4j
public class SalaryDataTaskAllController extends AbstractStandardController<SalaryDataTaskAllController.Arg, SalaryDataTaskAllController.Result> {

    private SalaryScheduleTask salaryScheduleTask = SpringUtil.getContext().getBean(SalaryScheduleTask.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doService(Arg arg) {
        LocalDate taskCurrentDate;
        if (arg.getTaskFlag() == null){
            return new Result(Boolean.FALSE,"taskFlag 不能为空");//ignoreI18n
        }
        if (StringUtils.isBlank(arg.getTaskCurrentDateStr())) {
            taskCurrentDate = LocalDate.now();
        } else {
            taskCurrentDate = LocalDate.parse(arg.getTaskCurrentDateStr());
        }
        //异步任务
        ParallelUtils.createBackgroundTask().submit(() -> {
            salaryScheduleTask.processSalaryRules(arg.getTaskFlag(), taskCurrentDate);
        }).run();
        return new Result(Boolean.TRUE,"开始异步执行 定时任务 flag:" + arg.getTaskFlag() + " 任务日期：" + taskCurrentDate);//ignoreI18n
    }


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    /**
     * Controller 请求参数
     */
    @Data
    public static class Arg implements Serializable {
        /**
         * 跑任务的日期
         */
        private String taskCurrentDateStr;

        /**
         * 0 生成工资条明细
         * 1 生成工资条
         * null 不会跑定时任务
         */
        private Integer taskFlag;
    }

    /**
     * 返回结果
     */
    @Data
    public static class Result implements Serializable {
        /**
         * 处理是否成功
         */
        private Boolean success;

        /**
         * 处理结果消息
         */
        private String message;

        public Result() {
        }

        public Result(Boolean success, String message) {
            this.success = success;
            this.message = message;
        }
    }
}
