package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.constants.SupplyChangeFields;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 工资条下发审批流程完成后的回调处理类
 *
 * 功能：
 * 1. 当工资条下发审批流程完成后，自动触发实际的下发操作
 * 2. 处理审批通过后的后续业务逻辑
 * 3. 确保审批流程与业务操作的正确衔接
 */
@Slf4j
public class SalaryPaymentSlipFlowCompletedAction extends StandardFlowCompletedAction {

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);

        log.info("工资发放单审批流程完成，开始处理回调，数据ID: {}, 审批流程触发类型: {}, 是否通过: {}",
                arg.getDataId(), arg.getTriggerType(), arg.isPass());

        if (arg.isPass()) {
            // 检查是否是「新建」审批流程且审批通过
            if (String.valueOf(ApprovalFlowTriggerType.CREATE.getTriggerTypeCode()).equals(arg.getTriggerType())) {
                doCreateApprovalPassedAction(arg);
            }
            // 检查是否是「下发」审批流程且审批通过
            else if (String.valueOf(ApprovalFlowTriggerType.DISTRIBUTE.getTriggerTypeCode()).equals(arg.getTriggerType())) {
                doDistributeAction(arg);
            }
            else {
                log.info("其他类型的审批流程，暂不处理，审批流程触发类型: {}", arg.getTriggerType());
            }
        } else {
            log.info("审批未通过，跳过处理，审批流程触发类型: {}, 是否通过: {}", arg.getTriggerType(), arg.isPass());
        }

        return after;
    }

    /**
     * 处理新建审批通过后的操作
     * 参考 SalaryPaymentSlipAddAction 的 after 逻辑
     */
    private void doCreateApprovalPassedAction(Arg arg) {
        try {
            String salaryPaymentSlipId = arg.getDataId();
            String tenantId = actionContext.getTenantId();

            log.info("工资发放单新建审批通过，开始更新状态并异步生成工资条，ID: {}", salaryPaymentSlipId);

            // 1. 更新工资发放单状态为"正在生成"
            IObjectData salaryPaymentSlip = serviceFacade.findObjectData(
                actionContext.getUser(), salaryPaymentSlipId, objectDescribe.getApiName());

            if (salaryPaymentSlip == null) {
                log.error("工资发放单不存在，ID: {}", salaryPaymentSlipId);
                return;
            }

            // 更新状态为正在生成
            salaryPaymentSlip.set(SalaryPaymentSlipFields.PAY_STATUS, SalaryPaymentSlipFields.PAY_STATUS_Options_0);
            serviceFacade.updateObjectData(actionContext.getUser(), salaryPaymentSlip);

            log.info("工资发放单状态已更新为正在生成，ID: {}", salaryPaymentSlipId);

            // 2. 异步生成工资条数据（参考 SalaryPaymentSlipAddAction 的逻辑）
            SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);
            ParallelUtils.createBackgroundTask().submit(() -> {
                try {
                    salaryService.generateSalaryDataForNewPaymentSlip(tenantId, salaryPaymentSlip);
                    log.info("新建审批通过后异步生成工资条数据完成，工资发放单ID: {}", salaryPaymentSlipId);
                } catch (Exception e) {
                    log.error("新建审批通过后异步生成工资条数据失败，工资发放单ID: {}", salaryPaymentSlipId, e);
                }
            }).run();

        } catch (Exception e) {
            log.error("处理新建审批通过操作失败，工资发放单ID: {}", arg.getDataId(), e);
            throw new RuntimeException("处理新建审批通过操作失败: " + e.getMessage(), e); //ignoreI18n
        }
    }

    /**
     * 执行工资条下发操作
     * 审批通过后，触发实际的工资条下发业务逻辑
     */
    private void doDistributeAction(Arg arg) {
        try {
            log.info("开始执行工资条下发操作，工资发放单ID: {}", arg.getDataId());

            // 验证工资发放单状态
            validateSalaryPaymentSlipStatus(arg);

            // 构建下发操作的参数
            SalaryPaymentSlipDistributeAction.Arg distributeArg = buildDistributeActionArg(arg);
            ActionContext distributeContent = new ActionContext(RequestContextManager.getContext(), SalaryPaymentSlipFields.API_NAME, ObjectAction.DISTRIBUTE.getActionCode());
            // 触发工资条下发操作
            serviceFacade.triggerAction(distributeContent, distributeArg, SalaryPaymentSlipDistributeAction.Result.class);

            log.info("工资条下发操作触发成功，工资发放单ID: {}", arg.getDataId());

        } catch (Exception e) {
            log.error("执行工资条下发操作失败，工资发放单ID: {}", arg.getDataId(), e);
            throw new RuntimeException("工资条下发操作失败: " + e.getMessage(), e); //ignoreI18n
        }
    }

    /**
     * 验证工资发放单状态
     * 确保工资发放单处于可下发状态
     */
    private void validateSalaryPaymentSlipStatus(Arg arg) {
        // 这里可以添加状态验证逻辑
        // 例如：检查工资发放单是否存在、状态是否正确等
        log.debug("验证工资发放单状态，ID: {}", arg.getDataId());

        // 可以通过 serviceFacade 获取工资发放单数据进行验证
        // IObjectData salaryPaymentSlip = serviceFacade.findObjectDataById(
        //     actionContext.getUser(), arg.getDataId(), SalaryPaymentSlipFields.API_NAME);
        //
        // if (salaryPaymentSlip == null) {
        //     throw new ValidateException("工资发放单不存在，ID: " + arg.getDataId());
        // }
        //
        // String payStatus = salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class);
        // if (!SalaryPaymentSlipFields.PAY_STATUS_Options_1.equals(payStatus)) {
        //     throw new ValidateException("工资发放单状态不正确，当前状态: " + payStatus);
        // }
    }

    /**
     * 构建工资条下发操作的参数
     *
     * @param arg 审批流程参数
     * @return 下发操作参数
     */
    private SalaryPaymentSlipDistributeAction.Arg buildDistributeActionArg(Arg arg) {
        SalaryPaymentSlipDistributeAction.Arg distributeArg = new SalaryPaymentSlipDistributeAction.Arg();

        // 设置工资发放单ID
        distributeArg.setObjectDataId(arg.getDataId());

        // 跳过审批流程触发（因为审批已经完成）
        distributeArg.setSkipTriggerApprovalFlow(true);

        // 可以根据需要设置其他参数
        // distributeArg.setSendSmsNotification(true); // 是否发送短信通知

        log.debug("构建工资条下发操作参数完成，工资发放单ID: {}", arg.getDataId());

        return distributeArg;
    }


}
