package com.facishare.crm.fmcg.wq.model.kpi;

import com.facishare.crm.fmcg.wq.model.KPICalculateType;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 薪资kpi
 */
@Data
@ToString
public abstract class SalaryKPI implements Serializable {

    private String tenantId;

    /**
     * metric identity
     * simple       : 66966efbb4ec502f2da615e7
     * data format  : mongo db id
     */
    private String id;

    /**
     * metric display name
     * simple       : 月度销售金额
     */
    private String name;

    /**
     * metric description
     * data format  : long text
     */
    private String description;


    /**
     * metric calculate type
     * APL计算方式还是对象（聚合）计算方式
     */
    private KPICalculateType kpiCalculateType;
    public static SalaryKPI of(IObjectData iObjectData){
        return null;
    }
}