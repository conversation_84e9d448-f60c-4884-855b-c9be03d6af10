package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.SalaryPaymentSlipDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.util.SalaryRecordTypeUtil;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;

/**
 * 工资发放单新增Action
 *
 * 新增规则：
 * 1. 只有手动创建的工资规则才能手动新建发放单
 * 2. 对相同规则在同一发薪周期内，不允许创建两个工资发放单
 * 3. 验证必填字段和数据完整性
 */
@Slf4j
public class SalaryPaymentSlipAddAction extends FmcgSkipPermissionAddAction {

    private SalaryPaymentSlipDao salaryPaymentSlipDao = SpringUtil.getContext().getBean(SalaryPaymentSlipDao.class);
    private SalaryRuleDao salaryRuleDao = SpringUtil.getContext().getBean(SalaryRuleDao.class);
    private SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        String tenantId = actionContext.getTenantId();
        ObjectDataExt objectData = ObjectDataExt.of(arg.getObjectData().toObjectData());

        log.info("开始处理工资发放单新增");

        // 1. 验证必填字段
        validateRequiredFields(objectData);

        // 2. 提前查询所有需要的数据
        String salaryRuleId = objectData.getStringValue(SalaryPaymentSlipFields.SALARY_RULE);
        IObjectData salaryRule = salaryRuleDao.getById(tenantId, salaryRuleId);

        // 3. 验证工资规则是否支持手动创建
        validateManualCreationAllowed(salaryRule, salaryRuleId);

        // 4. 从工资规则获取发放周期
        String distributionCycle = salaryRule.get(SalaryRuleFields.DISTRIBUTION_CYCLE, String.class);
        if (StringUtils.isBlank(distributionCycle)) {
            throw new ValidateException("工资规则的发放周期不能为空"); //ignoreI18n
        }

        // 5. 验证和调整时间范围（传入发放周期）
        validateAndAdjustTimeRange(objectData, distributionCycle);

        // 5.5. 根据工资规则生效日期调整开始时间
        adjustStartTimeByEffectiveDate(objectData, salaryRule);

        // 6. 重新获取调整后的时间用于重复性检查
        Long adjustedStartDate = objectData.get(SalaryPaymentSlipFields.START_DATE, Long.class);
        Long adjustedEndDate = objectData.get(SalaryPaymentSlipFields.END_DATE, Long.class);

        // 7. 验证是否存在重复的发放单（使用调整后的时间）
        List<IObjectData> adjustedExistingSlips = salaryPaymentSlipDao.getByRangeAndRule(
            tenantId, adjustedStartDate, adjustedEndDate, salaryRuleId);
        validateDuplicatePaymentSlip(adjustedExistingSlips, salaryRule, adjustedStartDate, adjustedEndDate);

        // 8. 设置业务类型：根据工资规则类型设置工资发放单的业务类型
        String recordType = SalaryRecordTypeUtil.getSalaryPaymentSlipRecordType(salaryRule);
        objectData.setRecordType(recordType);
        log.debug("手动创建工资发放单，设置业务类型为: {}, 工资规则ID: {}", recordType, salaryRuleId);

        // 9. 设置发放状态为正在生成
        objectData.set(SalaryPaymentSlipFields.PAY_STATUS, SalaryPaymentSlipFields.PAY_STATUS_Options_0);
        // 10. 工资表表头重写
        objectData.set(SalaryPaymentSlipFields.SALARY_STATEMENT_HEADER, salaryRule.get(SalaryRuleFields.SALARY_STATEMENT_HEADER));

        log.info("工资发放单新增前处理完成，发放周期: {}", distributionCycle);
    }

    /**
     * 验证必填字段
     *
     * @param objectData 工资发放单数据
     */
    private void validateRequiredFields(ObjectDataExt objectData) {
        // 验证工资规则
        String salaryRuleId = objectData.getStringValue(SalaryPaymentSlipFields.SALARY_RULE);
        if (StringUtils.isBlank(salaryRuleId)) {
            throw new ValidateException("请选择工资规则"); //ignoreI18n
        }



        // 验证开始时间
        Long startDate = objectData.get(SalaryPaymentSlipFields.START_DATE, Long.class);
        if (startDate == null) {
            throw new ValidateException("请选择开始时间"); //ignoreI18n
        }

        // 验证结束时间
        Long endDate = objectData.get(SalaryPaymentSlipFields.END_DATE, Long.class);
        if (endDate == null) {
            throw new ValidateException("请选择结束时间"); //ignoreI18n
        }

        // 验证时间范围合理性
        if (startDate > endDate) {
            throw new ValidateException("开始时间不能晚于结束时间"); //ignoreI18n
        }

        log.debug("必填字段验证通过");
    }

    /**
     * 验证工资规则是否支持手动创建发放单
     *
     * @param salaryRule 工资规则对象
     * @param salaryRuleId 工资规则ID
     */
    private void validateManualCreationAllowed(IObjectData salaryRule, String salaryRuleId) {
        if (salaryRule == null) {
            throw new ValidateException("工资规则不存在"); //ignoreI18n
        }

        // 检查是否为手动创建模式
        String autoCreated = salaryRule.get(SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE, String.class);
        if (SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE_Options_true.equals(autoCreated)) {
            throw new ValidateException("该工资规则设置为自动创建发放单，不支持手动创建"); //ignoreI18n
        }

        log.debug("工资规则支持手动创建发放单验证通过，规则ID: {}", salaryRuleId);
    }



    /**
     * 验证和调整时间范围
     * 根据发薪周期调整开始和结束时间，并验证时间范围的合理性
     *
     * @param objectData 工资发放单数据
     * @param distributionCycle 从工资规则获取的发放周期
     */
    private void validateAndAdjustTimeRange(ObjectDataExt objectData, String distributionCycle) {
        Long startDate = objectData.get(SalaryPaymentSlipFields.START_DATE, Long.class);
        Long endDate = objectData.get(SalaryPaymentSlipFields.END_DATE, Long.class);

        // 转换为东八区的LocalDate
        LocalDate startLocalDate = java.time.Instant.ofEpochMilli(startDate)
            .atZone(ZoneId.of("Asia/Shanghai"))
            .toLocalDate();
        LocalDate endLocalDate = java.time.Instant.ofEpochMilli(endDate)
            .atZone(ZoneId.of("Asia/Shanghai"))
            .toLocalDate();
        // 验证开始时间不能大于结束时间
        if (startLocalDate.isAfter(endLocalDate)) {
            throw new ValidateException("开始时间不能大于结束时间"); //ignoreI18n
        }

        // 验证只能创建昨天以前的数据
        LocalDate yesterday = LocalDate.now(ZoneId.of("Asia/Shanghai")).minusDays(1);
        if (endLocalDate.isAfter(yesterday)) {
            throw new ValidateException("只能创建昨天以前的工资发放单"); //ignoreI18n
        }

        // 根据发薪周期验证和调整时间范围
        if (SalaryRuleFields.DISTRIBUTION_CYCLE_Options_1.equals(distributionCycle)) {
            // 按日发放：开始时间和结束时间应该是同一天
            if (!startLocalDate.equals(endLocalDate)) {
                throw new ValidateException("按日发放时，开始时间和结束时间必须是同一天"); //ignoreI18n
            }
        } else if (SalaryRuleFields.DISTRIBUTION_CYCLE_Options_2.equals(distributionCycle)) {
            // 按周发放：开始时间应该是周一，结束时间应该是周日
            if (startLocalDate.getDayOfWeek().getValue() != 1) {
                throw new ValidateException("按周发放时，开始时间必须是周一"); //ignoreI18n
            }
            if (endLocalDate.getDayOfWeek().getValue() != 7) {
                throw new ValidateException("按周发放时，结束时间必须是周日"); //ignoreI18n
            }
            // 验证是否是完整的一周
            if (!endLocalDate.equals(startLocalDate.plusDays(6))) {
                throw new ValidateException("按周发放时，必须选择完整的一周（周一到周日）"); //ignoreI18n
            }
        } else if (SalaryRuleFields.DISTRIBUTION_CYCLE_Options_3.equals(distributionCycle)) {
            // 按月发放：开始时间必须是月初1号，结束时间必须是月末
            if (startLocalDate.getDayOfMonth() != 1) {
                throw new ValidateException("按月发放时，开始时间必须是月初1号"); //ignoreI18n
            }
            LocalDate monthEnd = startLocalDate.with(TemporalAdjusters.lastDayOfMonth());
            if (!endLocalDate.equals(monthEnd)) {
                throw new ValidateException("按月发放时，结束时间必须是月末"); //ignoreI18n
            }
        }

        // 将调整后的时间转换回东八区0点对应的时间戳
        long adjustedStartTime = startLocalDate.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();
        long adjustedEndTime = endLocalDate.atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();

        // 更新时间戳
        objectData.set(SalaryPaymentSlipFields.START_DATE, adjustedStartTime);
        objectData.set(SalaryPaymentSlipFields.END_DATE, adjustedEndTime);

        log.debug("时间范围验证和调整完成，发薪周期: {}, 开始时间: {}, 结束时间: {}",
                distributionCycle, startLocalDate, endLocalDate);
    }

    /**
     * 验证是否存在重复的发放单
     * 对相同工资规则在同一发薪周期内，不允许创建两个工资发放单
     *
     * @param existingSlips 已存在的发放单列表
     * @param salaryRule 工资规则对象
     * @param startDate 开始时间
     * @param endDate 结束时间
     */
    private void validateDuplicatePaymentSlip(List<IObjectData> existingSlips, IObjectData salaryRule,
                                            Long startDate, Long endDate) {
        if (CollectionUtils.isNotEmpty(existingSlips)) {
            // 获取工资规则名称用于错误提示
            String ruleName = salaryRule != null ? salaryRule.getName() : "未知规则"; //ignoreI18n

            // 格式化日期用于错误提示
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String startDateStr = dateFormat.format(new Date(startDate));
            String endDateStr = dateFormat.format(new Date(endDate));

            throw new ValidateException(String.format("%s工资规则，在%s至%s期间内已存在工资发放单，不可重复创建", //ignoreI18n
                    ruleName, startDateStr, endDateStr));
        }

        log.debug("重复发放单验证通过，规则名称: {}, 时间范围: {} - {}",
                salaryRule != null ? salaryRule.getName() : "未知规则", //ignoreI18n
                new Date(startDate), new Date(endDate));
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        //如果触发审批流
        if (isApprovalFlowStartSuccess(result.getObjectData().getId())) {
            return after;
        }
        String salaryPaymentSlipId = result.getObjectData().getId();
        String tenantId = actionContext.getTenantId();

        log.info("工资发放单新增成功，开始异步生成工资条，ID: {}", salaryPaymentSlipId);

        // 异步生成工资条数据
        ParallelUtils.createBackgroundTask().submit(() -> {
            try {
                salaryService.generateSalaryDataForNewPaymentSlip(tenantId, result.getObjectData().toObjectData());
            } catch (Exception e) {
                log.error("异步生成工资条数据失败，工资发放单ID: {}", salaryPaymentSlipId, e);
            }
        }).run();

        return after;
    }

    /**
     * 根据工资规则的生效日期调整工资发放单的开始时间
     * 确保工资发放单的开始时间不早于规则的生效时间
     *
     * @param objectData 工资发放单数据
     * @param salaryRule 工资规则对象
     */
    private void adjustStartTimeByEffectiveDate(ObjectDataExt objectData, IObjectData salaryRule) {
        Long effectiveDate = salaryRule.get(SalaryRuleFields.EFFECTIVE_DATE, Long.class);
        if (effectiveDate == null) {
            log.debug("工资规则没有设置生效日期，不需要调整开始时间");
            return;
        }

        Long startDate = objectData.get(SalaryPaymentSlipFields.START_DATE, Long.class);
        if (startDate == null) {
            log.warn("工资发放单开始时间为空，无法调整");
            return;
        }

        // 如果规则生效时间晚于开始时间，则调整开始时间
        if (effectiveDate > startDate) {
            Long endDate = objectData.get(SalaryPaymentSlipFields.END_DATE, Long.class);

            log.info("根据工资规则生效日期调整开始时间，原始开始时间: {}, 规则生效时间: {}, 调整后开始时间: {}",
                    new Date(startDate), new Date(effectiveDate), new Date(effectiveDate));

            objectData.set(SalaryPaymentSlipFields.START_DATE, effectiveDate);

            // 验证调整后的时间范围合理性
            if (endDate != null && effectiveDate > endDate) {
                throw new ValidateException("工资规则生效时间晚于发放单结束时间，请调整时间范围"); //ignoreI18n
            }
        }
    }
}
