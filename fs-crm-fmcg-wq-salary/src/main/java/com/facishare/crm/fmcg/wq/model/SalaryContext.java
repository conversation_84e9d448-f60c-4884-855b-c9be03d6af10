package com.facishare.crm.fmcg.wq.model;

import com.facishare.paas.appframework.common.util.StopWatch;
import lombok.*;

import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-07 18:14
 **/
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SalaryContext {
    @Getter
    @Setter
    private long startTime;
    @Getter
    @Setter
    private long endTime;
    @Getter
    @Setter
    private String owner;
    @Getter
    @Setter
    private String tenantId;
    /**
     * 额外数据，目前用来存考勤数据的，查一次当前考勤数据 然后把需要的部分数据都放到者后续省着查了
     */
    @Getter
    @Setter
    private Map<String,Object> extDataMap;
    /**
     * 额外数据名称映射
     */
    @Getter
    @Setter
    private Map<String,Object> extDataNameMap;

    private StopWatch stopWatch = StopWatch.create(this.getClass().getSimpleName());

    public StopWatch watch(){
        if (stopWatch == null){
            return StopWatch.create(this.getClass().getSimpleName());
        }
        return stopWatch;
    }
}
