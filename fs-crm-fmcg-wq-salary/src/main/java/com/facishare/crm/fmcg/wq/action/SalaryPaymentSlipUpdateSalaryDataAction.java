package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.dao.SalaryPaymentSlipDao;
import com.facishare.crm.fmcg.wq.model.FmcgPreActionArgs;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 工资发放单更新工资数据Action
 * 
 * 功能：
 * 1. 重新触发工资计算，更新工资条数据
 * 2. 只有在发放状态为"未发放"或"发放中"时才能操作
 * 3. 已发放的工资发放单不能更新数据
 */
@Slf4j
public class SalaryPaymentSlipUpdateSalaryDataAction extends FmcgAbstractStandardAction<SalaryPaymentSlipUpdateSalaryDataAction.Arg, SalaryPaymentSlipUpdateSalaryDataAction.Result> {

    private SalaryPaymentSlipDao salaryPaymentSlipDao = SpringUtil.getContext().getBean(SalaryPaymentSlipDao.class);
    private SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.UPDATE_SALARY_DATA; // 自定义action
    }

    @Override
    protected IObjectData getPreObjectData() {
        return dbData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.UPDATE_SALARY_DATA.getButtonApiName();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("SalaryPaymentSlipUpdateSalaryDataAction after method called for ID: {}", arg.getObjectDataId());
        return super.after(arg, result);
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        String tenantId = actionContext.getTenantId();
        String salaryPaymentSlipId = arg.getObjectDataId();

        log.info("开始处理工资发放单更新工资数据，发放单ID: {}", salaryPaymentSlipId);

        // 获取工资发放单数据
        if (objectData == null) {
            objectData = salaryPaymentSlipDao.getById(tenantId, salaryPaymentSlipId);
        }

        if (objectData == null) {
            throw new ValidateException("工资发放单不存在"); //ignoreI18n
        }

        // 验证发放状态
        validatePayStatus(objectData);

        log.info("工资发放单更新工资数据前处理完成，发放单ID: {}", salaryPaymentSlipId);
    }

    @Override
    protected Result doAct(Arg arg) {
        String tenantId = actionContext.getTenantId();
        String salaryPaymentSlipId = arg.getObjectDataId();

        try {
            log.info("开始重新计算工资数据，发放单ID: {}", salaryPaymentSlipId);
            String slipPayStatus = objectData.get(SalaryPaymentSlipFields.PAY_STATUS, String.class);
            if (!SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR.equals(slipPayStatus)
                    && !SalaryPaymentSlipFields.PAY_STATUS_Options_1.equals(slipPayStatus)) {
                log.info("工资发放单状态不正确，无法重新计算工资，当前状态: {}, ID: {}", slipPayStatus, salaryPaymentSlipId);
                if (SalaryPaymentSlipFields.PAY_STATUS_Options_0.equals(slipPayStatus)) {
                    throw new ValidateException("工资发放单正在生成中"); //ignoreI18n
                }
                throw new ValidateException("只有未发放的工资发放单才能更新工资数据"); //ignoreI18n
            }
            // 1. 更新状态为"正在生成"（表示正在重新计算）
            salaryPaymentSlipDao.updatePayStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_0);

            // 2. 异步执行工资数据重新计算
            ParallelUtils.createBackgroundTask().submit(() -> {
                try {
                    // 调用工资服务重新计算工资数据
                    int updatedCount = salaryService.recalculateSalaryDataForPaymentSlip(tenantId, salaryPaymentSlipId);

                    // 3. 异步执行成功后，更新状态为"生成完成"
                    salaryPaymentSlipDao.updatePayStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_1);

                    log.info("工资发放单更新工资数据完成，发放单ID: {}, 更新工资条数量: {}", salaryPaymentSlipId, updatedCount);

                } catch (Exception e) {
                    // 4. 异步执行失败，更新状态为"生成失败"
                    log.error("工资数据重新计算失败，更新状态为生成失败，发放单ID: {}", salaryPaymentSlipId, e);
                    salaryPaymentSlipDao.updatePayStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_3);
                }
            }).run();

            // 5. 记录操作日志
            recodeLog();

            Result result = new Result();
            result.setSuccess(true);
            result.setMessage("工资数据更新中，完成后状态将更新为生成完成"); //ignoreI18n
            return result;

        } catch (Exception e) {
            log.error("工资发放单更新工资数据失败，发放单ID: {}", salaryPaymentSlipId, e);
            throw new ValidateException("更新工资数据失败: " + e.getMessage()); //ignoreI18n
        }
    }

    /**
     * 验证发放状态
     * 只有未发放和发放中状态才能更新工资数据
     * 
     * @param salaryPaymentSlip 工资发放单
     */
    private void validatePayStatus(IObjectData salaryPaymentSlip) {
        String payStatus = salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class);

        if (StringUtils.isBlank(payStatus)) {
            throw new ValidateException("工资发放单状态异常"); //ignoreI18n
        }

        // 只有未发放和生成异常的工资发放单才能更新数据
        if (!SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR.equals(payStatus)
                && !SalaryPaymentSlipFields.PAY_STATUS_Options_1.equals(payStatus)) {
            throw new ValidateException("只有未发放和发放中的工资发放单才能更新工资数据"); //ignoreI18n
        }

        log.debug("发放状态验证通过，当前状态: {}", payStatus);
    }

    /**
     * 更新工资数据参数
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends FmcgPreActionArgs {
        /**
         * 是否强制更新（可选）
         */
        private Boolean forceUpdate = false;

        /**
         * 更新说明（可选）
         */
        private String updateReason;
    }

    /**
     * 更新工资数据结果
     */
    @Data
    public static class Result {
        /**
         * 是否成功
         */
        private boolean success;

        /**
         * 消息
         */
        private String message;

        /**
         * 更新的工资条数量
         */
        private int updatedSalaryDataCount;

        /**
         * 更新的明细数量
         */
        private int updatedDetailCount;
    }
}
