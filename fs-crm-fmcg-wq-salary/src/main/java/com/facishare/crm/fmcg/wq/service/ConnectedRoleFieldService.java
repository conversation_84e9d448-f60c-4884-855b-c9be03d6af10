package com.facishare.crm.fmcg.wq.service;

import com.facishare.crm.fmcg.wq.constants.PublicEmployeeFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;


import com.facishare.crm.fmcg.wq.util.RoleNameConverter;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 互联角色字段处理服务
 * 
 * 功能：
 * 1. 在列表页隐藏互联角色字段
 * 2. 在详情页重新查询互联角色信息并转换为角色名称显示
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
@Service
public class ConnectedRoleFieldService {

  @Autowired
  private EmployeeDao employeeDao;
  
  @Autowired
  private RoleClient roleClient;


  /**
   * 在详情页重新查询并设置互联角色虚拟字段
   *
   * @param tenantId 租户ID
   * @param dataExt 数据扩展对象
   * @param dataDocument 数据文档
   * @param employeeField 员工字段名称
   * @param employeeExternalField 外部员工字段名称
   * @param connectedRoleDisplayField 互联角色显示虚拟字段名称
   */
  public void setConnectedRoleDisplayField(String tenantId, ObjectDataExt dataExt,
                                          ObjectDataDocument dataDocument,
                                          String employeeField,
                                          String employeeExternalField,
                                          String connectedRoleDisplayField) {
    try {
      // 获取外部员工ID（互联角色只针对外部员工）
      String employeeExternalId = dataExt.get(employeeExternalField, String.class);

      if (StringUtils.isBlank(employeeExternalId)) {
        // 如果没有外部员工ID，设置为空字符串
        dataDocument.put(connectedRoleDisplayField, "");
        log.debug("外部员工ID为空，互联角色显示字段设置为空: {}", connectedRoleDisplayField);
        return;
      }

      // 查询外部员工的互联角色信息
      List<String> roleNames = queryExternalEmployeeRoles(tenantId, employeeExternalId);

      if (CollectionUtils.isNotEmpty(roleNames)) {
        // 将角色名称列表转换为显示字符串
        String roleDisplayValue = String.join(", ", roleNames);

        // 设置虚拟字段值
        dataDocument.put(connectedRoleDisplayField, roleDisplayValue);

        log.info("成功设置互联角色显示字段: employeeExternalId={}, roleNames={}",
            employeeExternalId, roleDisplayValue);
      } else {
        // 如果没有查询到角色，设置为空字符串
        dataDocument.put(connectedRoleDisplayField, "");
        log.debug("未查询到外部员工角色信息: employeeExternalId={}", employeeExternalId);
      }

    } catch (Exception e) {
      log.warn("设置互联角色显示字段失败: {}", connectedRoleDisplayField, e);
      // 查询失败时设置为空字符串
      dataDocument.put(connectedRoleDisplayField, "");
    }
  }

//  /**
//   * 在详情页重新查询并覆盖互联角色字段（保持向后兼容）
//   *
//   * @param tenantId 租户ID
//   * @param dataExt 数据扩展对象
//   * @param dataDocument 数据文档
//   * @param employeeField 员工字段名称
//   * @param employeeExternalField 外部员工字段名称
//   * @param connectedRoleField 互联角色字段名称
//   */
//  @Deprecated
//  public void refreshConnectedRoleField(String tenantId, ObjectDataExt dataExt,
//                                       ObjectDataDocument dataDocument,
//                                       String employeeField,
//                                       String employeeExternalField,
//                                       String connectedRoleField) {
//    try {
//      // 获取外部员工ID（互联角色只针对外部员工）
//      String employeeExternalId = dataExt.get(employeeExternalField, String.class);
//
//      if (StringUtils.isBlank(employeeExternalId)) {
//        // 如果没有外部员工ID，设置为空字符串
//        dataDocument.put(connectedRoleField, "");
//        log.debug("外部员工ID为空，互联角色字段设置为空: {}", connectedRoleField);
//        return;
//      }
//
//      // 查询外部员工的互联角色信息
//      List<String> roleNames = queryExternalEmployeeRoles(tenantId, employeeExternalId);
//
//      if (CollectionUtils.isNotEmpty(roleNames)) {
//        // 将角色名称列表转换为显示字符串
//        String roleDisplayValue = String.join(", ", roleNames);
//
//        // 覆盖互联角色字段值
//        dataDocument.put(connectedRoleField, roleDisplayValue);
//
//        log.info("成功更新互联角色字段: employeeExternalId={}, roleNames={}",
//            employeeExternalId, roleDisplayValue);
//      } else {
//        // 如果没有查询到角色，设置为空字符串
//        dataDocument.put(connectedRoleField, "");
//        log.debug("未查询到外部员工角色信息: employeeExternalId={}", employeeExternalId);
//      }
//
//    } catch (Exception e) {
//      log.warn("重新查询互联角色字段失败: {}", connectedRoleField, e);
//      // 查询失败时设置为空字符串，避免显示原始角色ID
//      dataDocument.put(connectedRoleField, "");
//    }
//  }

  /**
   * 查询外部员工的互联角色信息
   * 
   * @param tenantId 租户ID
   * @param employeeExternalId 外部员工ID
   * @return 角色名称列表
   */
  private List<String> queryExternalEmployeeRoles(String tenantId, String employeeExternalId) {
    try {
      // 查询外部员工信息
      IObjectData employeeObj = employeeDao.getExternalEmployeeById(tenantId, employeeExternalId);
      if (employeeObj == null || employeeObj.get(PublicEmployeeFields.OUTER_TENANT_ID) == null) {
        log.debug("未找到外部员工或外部租户ID为空: employeeExternalId={}", employeeExternalId);
        return Lists.newArrayList();
      }

      // 查询外部员工的角色ID列表
      List<String> roleIds = employeeDao.getEmployeeRoles(tenantId, employeeExternalId,
          employeeObj.get(PublicEmployeeFields.OUTER_TENANT_ID).toString(), true);

      if (CollectionUtils.isNotEmpty(roleIds)) {
        // 将角色ID转换为角色名称（外部角色）
        return RoleNameConverter.convertRoleIdsToNames(roleClient, tenantId, roleIds);
      }

      return Lists.newArrayList();
    } catch (Exception e) {
      log.warn("查询外部员工角色信息失败: tenantId={}, employeeExternalId={}", 
          tenantId, employeeExternalId, e);
      return Lists.newArrayList();
    }
  }
}
