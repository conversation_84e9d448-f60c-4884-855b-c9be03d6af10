package com.facishare.crm.fmcg.wq.util;

import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.RolePojo;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 角色名称转换工具类
 * 提供角色ID到角色名称的转换功能
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class RoleNameConverter {


  /**
   * 将角色ID列表转换为角色名称列表（支持内部和外部角色）
   *
   * @param roleClient 角色客户端
   * @param tenantId 租户ID
   * @param roleIds 角色ID列表
   * @param isExternal 是否为外部角色
   * @return 角色名称列表
   */
  public static List<String> convertRoleIdsToNames(RoleClient roleClient, String tenantId, List<String> roleIds) {
    List<String> roleNames = Lists.newArrayList();
    
    if (roleClient == null) {
      log.warn("RoleClient为空，无法转换角色名称");
      return roleNames;
    }
    
    if (StringUtils.isBlank(tenantId)) {
      log.warn("租户ID为空，无法转换角色名称");
      return roleNames;
    }
    
    if (CollectionUtils.isEmpty(roleIds)) {
      log.debug("角色ID列表为空，返回空的角色名称列表");
      return roleNames;
    }

    try {
      // 构建AuthContext
      AuthContext authContext = AuthContext.builder()
          .tenantId(tenantId)
          .userId("-10000") // 使用系统用户
          .appId("CRM")
          .build();

      // 将List转换为Set，去重
      Set<String> roleIdSet = roleIds.stream()
          .filter(StringUtils::isNotBlank)
          .collect(Collectors.toSet());

      if (roleIdSet.isEmpty()) {
        log.debug("过滤后的角色ID集合为空，返回空的角色名称列表");
        return roleNames;
      }

      log.debug("开始查询角色信息，租户ID: {}, 角色ID数量: {}", tenantId, roleIdSet.size());

      // 调用RoleClient查询角色信息
      List<RolePojo>  roleInfoList = roleClient.queryAllRoleInfo(authContext, roleIdSet, Boolean.TRUE);

      if (CollectionUtils.isNotEmpty(roleInfoList)) {
        // 按照原始roleIds的顺序构建角色名称列表
        for (RolePojo rolePojo : roleInfoList) {
          if (roleIds.contains(rolePojo.getRoleCode())){
            roleNames.add(rolePojo.getRoleName());
          }
        }

        log.info("角色ID转换完成，原始数量: {}, 转换后数量: {}", roleIds.size(), roleNames.size());
      } else {
        log.warn("查询角色信息返回空结果，租户ID: {}, 角色ID: {}", tenantId, roleIdSet);
        // 如果查询失败，返回原始ID列表作为备选
        roleNames.addAll(roleIds.stream()
            .filter(StringUtils::isNotBlank)
            .map(id -> id + "(查询失败)") //ignoreI18n
            .collect(Collectors.toList()));
      }

    } catch (Exception e) {
      log.error("转换角色ID到名称时发生异常，租户ID: {}, 角色ID: {}", tenantId, roleIds, e);
      // 异常时返回原始ID列表作为备选
      roleNames.addAll(roleIds.stream()
          .filter(StringUtils::isNotBlank)
          .map(id -> id + "(转换异常)") //ignoreI18n
          .collect(Collectors.toList()));
    }

    return roleNames;
  }

  /**
   * 将单个角色ID转换为角色名称
   * 
   * @param roleClient 角色客户端
   * @param tenantId 租户ID
   * @param roleId 角色ID
   * @return 角色名称，如果转换失败则返回原ID
   */
  public static String convertRoleIdToName(RoleClient roleClient, String tenantId, String roleId) {
    if (StringUtils.isBlank(roleId)) {
      return "";
    }

    List<String> roleIds = Lists.newArrayList(roleId);
    List<String> roleNames = convertRoleIdsToNames(roleClient, tenantId, roleIds);
    
    return roleNames.isEmpty() ? roleId : roleNames.get(0);
  }
}
