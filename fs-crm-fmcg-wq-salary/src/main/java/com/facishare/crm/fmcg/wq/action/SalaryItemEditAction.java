package com.facishare.crm.fmcg.wq.action;


import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDetailDao;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryKPIDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.service.SalaryExpressionCalcService;
import com.facishare.crm.fmcg.wq.util.FieldValidationUtil;
import com.facishare.crm.fmcg.wq.util.SalaryItemFormulaUtil;
import com.facishare.crm.fmcg.wq.util.SalaryItemUsageValidator;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.ObjectDataExt;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;

/**
 * 工资项编辑Action
 *
 * 编辑规则：
 * 1. 任何时机都可以编辑
 * 2. 当工资项禁用或启用后未被工资规则或员工固定工资表使用时，支持编辑全部字段
 * 3. 当工资项被工资规则或员工固定工资表使用后，仅可编辑「工资项名称」「工资项说明」
 */
@Slf4j
public class SalaryItemEditAction extends FmcgSkipPermissionEditAction {

    private SalaryExpressionCalcService salaryExpressionCalcService = SpringUtil.getContext()
            .getBean(SalaryExpressionCalcService.class);
    private SalaryRuleDao salaryRuleDao = SpringUtil.getContext().getBean(SalaryRuleDao.class);
    private SalaryItemDao salaryItemDao = SpringUtil.getContext().getBean(SalaryItemDao.class);
    private SalaryKPIDao salaryKPIDao = SpringUtil.getContext().getBean(SalaryKPIDao.class);
    private EmployeeFixedSalaryDetailDao employeeFixedSalaryDetailDao = SpringUtil.getContext()
            .getBean(EmployeeFixedSalaryDetailDao.class);


    @Override
    protected void before(Arg arg) {
        super.before(arg);

        String tenantId = actionContext.getTenantId();
        String salaryItemId = (String) arg.getObjectData().getId();

        log.info("开始处理工资项编辑，工资项ID: {}", salaryItemId);

        // 使用工具类检查工资项使用状态
        SalaryItemUsageValidator.SalaryItemUsageInfo usageInfo = SalaryItemUsageValidator.checkSalaryItemUsage(
                tenantId, salaryItemId, salaryRuleDao, employeeFixedSalaryDetailDao);

        if (usageInfo.isUsed()) {
            log.info("工资项 {} 已被 {} 个工资规则和 {} 个员工固定工资表使用，限制编辑字段",
                    salaryItemId, usageInfo.getUsingSalaryRules().size(), usageInfo.getUsingEmployeeFixedSalaryDetails().size());

            // 获取原始数据
            IObjectData originalSalaryItem = salaryItemDao.getById(tenantId, salaryItemId);
            if (originalSalaryItem == null) {
                throw new ValidateException("工资项不存在"); // ignoreI18n
            }

            // 获取使用信息文案
            String usageMessage = usageInfo.getUsageMessage();
            log.info("工资项 {} 被以下对象使用: {}", salaryItemId, usageMessage);

            // 验证编辑的字段是否被允许，将使用信息作为异常消息
            validateEditableFields(arg.getObjectData(), originalSalaryItem, usageMessage);
        } else {
            log.info("工资项 {} 未被工资规则或员工固定工资表使用，允许编辑全部字段", salaryItemId);

            // 验证字段显示时机和必填逻辑
            validateFieldDisplayAndRequired(arg.getObjectData());
        }

        // 设置 指标
        Object calculationFormula = arg.getObjectData().get(SalaryItemFields.CALCULATION_FORMULA);
        if (calculationFormula == null) {
            arg.getObjectData().put(SalaryItemFields.FORMULA_INCLUDES_KPI, Lists.newArrayList());
        } else {
            List<String> strings = salaryExpressionCalcService
                    .extractKpiIdsFromExpression(calculationFormula.toString());
            arg.getObjectData().put(SalaryItemFields.FORMULA_INCLUDES_KPI, strings);
        }

        // 设置计算公式描述字段
//        setCalculationFormulaDescription(arg.getObjectData());

        log.info("工资项编辑前处理完成");
    }



    /**
     * 验证编辑的字段是否被允许
     * 当工资项被使用时，只允许编辑名称和说明，其他字段不允许修改
     *
     * @param newData 新数据
     * @param originalData 原始数据
     * @param usageMessage 使用信息文案
     */
    private void validateEditableFields(ObjectDataDocument newData, IObjectData originalData, String usageMessage) {
        // 检查关键字段是否被修改
        validateFieldNotChanged(newData, originalData, SalaryItemFields.VALUE_TYPE, usageMessage);
        validateFieldNotChanged(newData, originalData, SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, usageMessage);
        validateFieldNotChanged(newData, originalData, SalaryItemFields.SALARY_METHOD, usageMessage);
        validateFieldNotChanged(newData, originalData, SalaryItemFields.ENABLED_STATUS, usageMessage);
        validateFieldNotChanged(newData, originalData, SalaryItemFields.ROUNDING_METHOD, usageMessage);
        validateFieldNotChanged(newData, originalData, SalaryItemFields.DECIMAL_PLACES, usageMessage);
        validateFieldNotChanged(newData, originalData, SalaryItemFields.CALCULATION_FORMULA, usageMessage);
    }

    /**
     * 验证字段是否被修改
     *
     * @param newData 新数据
     * @param originalData 原始数据
     * @param fieldName 字段名
     * @param usageMessage 使用信息文案
     */
    private void validateFieldNotChanged(ObjectDataDocument newData, IObjectData originalData,
            String fieldName, String usageMessage) {
        Object newValue = newData.get(fieldName);
        Object originalValue = originalData.get(fieldName);

        String reason = String.format("工资项已被使用。%s", usageMessage); // ignoreI18n
        FieldValidationUtil.validateFieldNotChanged(objectDescribe, fieldName, newValue, originalValue, reason);
    }

    /**
     * 获取字段的显示名称
     * 从字段描述中动态获取标签信息
     *
     * @param fieldName 字段名
     * @return 显示名称
     */
    private String getFieldDisplayName(String fieldName) {
        return FieldValidationUtil.getFieldDisplayName(objectDescribe, fieldName);
    }

    /**
     * 验证字段显示时机和必填逻辑
     * 
     * @param salaryItemData 工资项数据
     */
    private void validateFieldDisplayAndRequired(ObjectDataDocument salaryItemData) {
        String valueType = (String) salaryItemData.get(SalaryItemFields.VALUE_TYPE);

        // 当"取值方式=计算公式"时的字段验证
        if (SalaryItemFields.VALUE_TYPE_Options_2.equals(valueType)) {
            // 舍位方式必填
            String roundingMethod = (String) salaryItemData.get(SalaryItemFields.ROUNDING_METHOD);
            if (StringUtils.isBlank(roundingMethod)) {
                String valueTypeDisplayName = getFieldDisplayName(SalaryItemFields.VALUE_TYPE);
                String roundingMethodDisplayName = getFieldDisplayName(SalaryItemFields.ROUNDING_METHOD);
                throw new ValidateException(String.format("%s为计算公式时，%s为必填字段", valueTypeDisplayName, roundingMethodDisplayName)); // ignoreI18n
            }

            // 当"舍位方式=四舍五入"时，小数位数必填
//            if (SalaryItemFields.ROUNDING_METHOD_Options_1.equals(roundingMethod)) {
                String decimalPlaces = (String) salaryItemData.get(SalaryItemFields.DECIMAL_PLACES);
                if (StringUtils.isBlank(decimalPlaces)) {
                    String roundingMethodDisplayName = getFieldDisplayName(SalaryItemFields.ROUNDING_METHOD);
                    String decimalPlacesDisplayName = getFieldDisplayName(SalaryItemFields.DECIMAL_PLACES);
                    throw new ValidateException(String.format("%s为四舍五入时，%s为必填字段", roundingMethodDisplayName, decimalPlacesDisplayName)); // ignoreI18n
                }
//            }

            // 计算公式必填
            String calculationFormula = (String) salaryItemData.get(SalaryItemFields.CALCULATION_FORMULA);
            if (StringUtils.isBlank(calculationFormula)) {
                String valueTypeDisplayName = getFieldDisplayName(SalaryItemFields.VALUE_TYPE);
                String calculationFormulaDisplayName = getFieldDisplayName(SalaryItemFields.CALCULATION_FORMULA);
                throw new ValidateException(String.format("%s为计算公式时，必须设置%s", valueTypeDisplayName, calculationFormulaDisplayName)); // ignoreI18n
            }
        } else if (SalaryItemFields.VALUE_TYPE_Options_1.equals(valueType)) {
            // 取值方式为固定值时，清空计算公式相关字段
            salaryItemData.put(SalaryItemFields.CALCULATION_FORMULA, null);
            salaryItemData.put(SalaryItemFields.ROUNDING_METHOD, null);
            salaryItemData.put(SalaryItemFields.DECIMAL_PLACES, null);
        }
    }

    /**
     * 设置计算公式描述字段
     * 根据配置的计算公式构建公式描述
     *
     * @param objectData 工资项数据
     */
    private void setCalculationFormulaDescription(ObjectDataDocument objectData) {
        try {
            String tenantId = actionContext.getTenantId();

            // 使用工具类构建计算公式描述
            String calculationFormulaDesc = SalaryItemFormulaUtil.buildFormulaDescription(
                tenantId, ObjectDataExt.of(objectData), salaryKPIDao);

            if (StringUtils.isNotBlank(calculationFormulaDesc)) {
                objectData.put(SalaryItemFields.CALCULATION_FORMULA_DESC, calculationFormulaDesc);
                log.info("工资项编辑时设置计算公式描述: {}", calculationFormulaDesc);
            } else {
                log.debug("工资项未配置计算公式或无法构建公式描述，跳过设置");
            }

        } catch (Exception e) {
            log.error("设置工资项计算公式描述时发生异常", e);
            // 不影响主流程，只记录错误日志
        }
    }

}
