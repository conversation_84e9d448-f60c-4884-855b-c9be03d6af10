package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields;
import com.facishare.crm.fmcg.wq.service.ConnectedRoleFieldService;
import com.facishare.crm.fmcg.wq.util.DescribeUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 工资条明细列表头部控制器
 * 
 * 功能：隐藏互联角色字段，避免在列表页显示原始角色ID
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class SalaryDetailDataListHeaderController extends StandardListHeaderController {


  @Override
  protected Result after(Arg arg, Result result) {
    result = super.after(arg, result);
    result.setObjectDescribe(DescribeUtils.addConnectedRoleVirtualFieldToObjectDescribe(result.getObjectDescribe(),SalaryDetailDataFields.CONNECTED_ROLE_DISPLAY));
    return result;
  }
}
