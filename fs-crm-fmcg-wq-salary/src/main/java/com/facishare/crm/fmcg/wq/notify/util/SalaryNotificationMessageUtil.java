package com.facishare.crm.fmcg.wq.notify.util;

import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.fxiaoke.model.InternationalItem;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工资规则通知消息国际化工具类
 * <p>
 * 使用InternationalItem实现真正的国际化支持
 * </p>
 */
@Slf4j
public class SalaryNotificationMessageUtil {

    // 国际化键值常量

    // 员工固定工资表创建提醒
    public static final String MISSING_FIXED_SALARY_TITLE_KEY = "fmcg.salary.notification.missing.fixed.salary.title";
    // {0}{1}适用范围下员工{3}未创建{4}，请尽快创建，创建后方可参与工资计算
    // 完整文案：【工资规则】基本工资规则适用范围下员工张三未创建【员工固定工资表】，请尽快创建，创建后方可参与工资计算
    public static final String MISSING_FIXED_SALARY_MESSAGE_KEY = "fmcg.salary.notification.missing.fixed.salary.message";

    // 新员工固定工资表创建提醒
    public static final String NEW_EMPLOYEE_TITLE_KEY = "fmcg.salary.notification.new.employee.title";
    // 新增了员工{0}，因该员工有生效的{1}，请尽快为新员工创建{2}，方可参与工资计算
    // 完整文案：新增了员工张三，因该员工有生效的【工资规则】，请尽快为新员工创建【员工固定工资表】，方可参与工资计算
    public static final String NEW_EMPLOYEE_MESSAGE_KEY = "fmcg.salary.notification.new.employee.message";

    // 定薪方式不一致提醒
    public static final String SALARY_METHOD_MISMATCH_TITLE_KEY = "fmcg.salary.notification.salary.method.mismatch.title";
    // 员工{0}【{1}】与适用的【{2}】定薪方式不一致，将无法参与工资计算，请尽快调整
    // 完整文案：员工张三【员工固定工资表】与适用的【工资规则】定薪方式不一致，将无法参与工资计算，请尽快调整
    public static final String SALARY_METHOD_MISMATCH_MESSAGE_KEY = "fmcg.salary.notification.salary.method.mismatch.message";

    // 多规则适配提醒
    public static final String MULTIPLE_RULES_MATCH_TITLE_KEY = "fmcg.salary.notification.multiple.rules.match.title";
    // 新增了员工{0}，因该员工有多个生效的{1}，请尽快为该{2}调整规则，方可参与工资计算
    // 完整文案：新增了员工张三，因该员工有多个生效的【工资规则】，请尽快为该【员工固定工资表】调整规则，方可参与工资计算
    public static final String MULTIPLE_RULES_MATCH_MESSAGE_KEY = "fmcg.salary.notification.multiple.rules.match.message";

    // 规则被移出提醒
    public static final String RULE_REMOVED_TITLE_KEY = "fmcg.salary.notification.rule.removed.title";
    // {0}{1},{2}规则被移出,请尽快为该{3}调整规则，方可参与工资计算
    // 完整文案：【员工固定工资表】张三，基本工资规则被移出,请尽快为该【员工固定工资表】调整规则，方可参与工资计算
    public static final String RULE_REMOVED_MESSAGE_KEY = "fmcg.salary.notification.rule.removed.message";

    // 立即处理
    public static final String PROCESS_BUTTON_TEXT_KEY = "fmcg.salary.notification.process.button.text";

    // 国际化对象名称常量（包含#I18N#前缀）
    // 工资规则
    public static final String SALARY_RULE_I18N_KEY = "#I18N#SalaryRuleObj.attribute.self.display_name";
    // 员工固定工资表
    public static final String EMPLOYEE_FIXED_SALARY_I18N_KEY = "#I18N#EmployeeFixedSalaryObj.attribute.self.display_name";

    /**
     * 创建国际化项目
     *
     * @param internationalKey 国际化键
     * @param parameters 参数列表
     * @return InternationalItem对象
     */
    public static InternationalItem createInternationalItem(String internationalKey, String... parameters) {
        InternationalItem item = new InternationalItem();
        item.setInternationalKey(internationalKey);
        if (parameters != null && parameters.length > 0) {
            item.setInternationalParameters(Arrays.asList(parameters));
        }
        return item;
    }



    /**
     * 构建新增员工消息的国际化项目
     *
     * @param triggerReason 触发原因（如："销售部"、"经理角色"等）
     * @param employeeName 员工姓名
     * @return 消息内容
     */
    public static MessageContent buildNewEmployeeMessage(String triggerReason, String employeeName) {
        InternationalItem titleItem = createInternationalItem(NEW_EMPLOYEE_TITLE_KEY);
        // 参数：triggerReason, employeeName, salaryRuleI18nKey, employeeFixedSalaryI18nKey
        // 模板："{0}"新增了员工{1}，因该员工有生效的{2}，请尽快为新员工创建{3}，方可参与工资计算
        InternationalItem contentItem = createInternationalItem(NEW_EMPLOYEE_MESSAGE_KEY,
                triggerReason, employeeName, SALARY_RULE_I18N_KEY, EMPLOYEE_FIXED_SALARY_I18N_KEY);
        InternationalItem buttonItem = createInternationalItem(PROCESS_BUTTON_TEXT_KEY);

        return new MessageContent(titleItem, contentItem, buttonItem);
    }

    /**
     * 构建新增员工消息的国际化项目（兼容旧版本）
     */
    public static MessageContent buildNewEmployeeMessage(String departmentAndRole, String employeeName, String actionReason) {
        // 兼容旧版本调用，使用部门角色信息作为触发原因
        return buildNewEmployeeMessage(departmentAndRole, employeeName);
    }





    /**
     * 构建规则被移出消息的国际化项目
     *
     * @param employeeFixedSalaryName 员工固定工资表名称（实际是员工姓名）
     * @param ruleName 被移出的规则名称
     * @return 消息内容
     */
    public static MessageContent buildRuleRemovedMessage(String employeeFixedSalaryName, String ruleName) {
        InternationalItem titleItem = createInternationalItem(RULE_REMOVED_TITLE_KEY);
        // 参数：employeeFixedSalaryI18nKey, employeeFixedSalaryName, removedRuleName, employeeFixedSalaryI18nKey2
        // 模板：{0}{1},{2}规则被移出,请尽快为该{3}调整规则，方可参与工资计算
        InternationalItem contentItem = createInternationalItem(RULE_REMOVED_MESSAGE_KEY,
                EMPLOYEE_FIXED_SALARY_I18N_KEY, employeeFixedSalaryName, ruleName, EMPLOYEE_FIXED_SALARY_I18N_KEY);
        InternationalItem buttonItem = createInternationalItem(PROCESS_BUTTON_TEXT_KEY);

        return new MessageContent(titleItem, contentItem, buttonItem);
    }

    /**
     * 构建带操作原因的新增员工消息
     *
     * @param departmentAndRole 部门和角色信息
     * @param employeeName 员工姓名
     * @param actionReason 操作原因（如：部门xx新增员工、角色xx新增员工等）
     * @return 消息内容
     */
    public static MessageContent buildNewEmployeeMessageWithReason(String departmentAndRole, String employeeName, String actionReason) {
        InternationalItem titleItem = createInternationalItem(NEW_EMPLOYEE_TITLE_KEY);
        InternationalItem contentItem = createInternationalItem(NEW_EMPLOYEE_MESSAGE_KEY, departmentAndRole, employeeName, actionReason);
        InternationalItem buttonItem = createInternationalItem(PROCESS_BUTTON_TEXT_KEY);

        return new MessageContent(titleItem, contentItem, buttonItem);
    }

    /**
     * 构建批量员工固定工资表缺失消息的国际化项目
     *
     * @param ruleName 工资规则名称
     * @param departmentAndRole 部门和角色信息（已包含在规则名称中，此参数保留兼容性）
     * @param employeeI18nParams 员工I18n参数，格式：{{E.tenant.1010,E.tenant.1011}}
     * @return 消息内容
     */
    public static MessageContent buildBatchMissingFixedSalaryMessage(String ruleName, String departmentAndRole, String employeeI18nParams) {
        InternationalItem titleItem = createInternationalItem(MISSING_FIXED_SALARY_TITLE_KEY);
        // 参数：ruleName, salaryRuleI18nKey, employeeI18nParams, employeeFixedSalaryI18nKey
        // 模板：{0}{1}适用范围下员工{3}未创建{4}，请尽快创建，创建后方可参与工资计算
        InternationalItem contentItem = createInternationalItem(MISSING_FIXED_SALARY_MESSAGE_KEY,
                ruleName, SALARY_RULE_I18N_KEY, employeeI18nParams, EMPLOYEE_FIXED_SALARY_I18N_KEY);
        InternationalItem buttonItem = createInternationalItem(PROCESS_BUTTON_TEXT_KEY);

        return new MessageContent(titleItem, contentItem, buttonItem);
    }

    /**
     * 构建批量新增员工消息的国际化项目
     *
     * @param triggerReason 触发原因（如："销售部"、"经理角色"等）
     * @param employeeI18nParams 员工I18n参数，格式：{{E.tenant.1010,E.tenant.1011}}
     * @return 消息内容
     */
    public static MessageContent buildBatchNewEmployeeMessage(String triggerReason, String employeeI18nParams) {
        InternationalItem titleItem = createInternationalItem(NEW_EMPLOYEE_TITLE_KEY);
        // 参数：triggerReason, employeeI18nParams, salaryRuleI18nKey, employeeFixedSalaryI18nKey
        // 模板："{0}"新增了员工{1}，因该员工有生效的{2}，请尽快为新员工创建{3}，方可参与工资计算
//        InternationalItem contentItem = createInternationalItem(NEW_EMPLOYEE_MESSAGE_KEY,
//                triggerReason, employeeI18nParams, SALARY_RULE_I18N_KEY, EMPLOYEE_FIXED_SALARY_I18N_KEY);
        InternationalItem contentItem = createInternationalItem(NEW_EMPLOYEE_MESSAGE_KEY,
                 employeeI18nParams, SALARY_RULE_I18N_KEY, EMPLOYEE_FIXED_SALARY_I18N_KEY);
        InternationalItem buttonItem = createInternationalItem(PROCESS_BUTTON_TEXT_KEY);

        return new MessageContent(titleItem, contentItem, buttonItem);
    }
    public static MessageContent buildBatchMultipleRulesMatchMessage(String triggerReason, String employeeI18nParams) {
        InternationalItem titleItem = createInternationalItem(MULTIPLE_RULES_MATCH_TITLE_KEY);
        // 参数：triggerReason, employeeI18nParams, salaryRuleI18nKey, employeeFixedSalaryI18nKey
        InternationalItem contentItem = createInternationalItem(MULTIPLE_RULES_MATCH_MESSAGE_KEY,
                employeeI18nParams, SALARY_RULE_I18N_KEY, EMPLOYEE_FIXED_SALARY_I18N_KEY);
        InternationalItem buttonItem = createInternationalItem(PROCESS_BUTTON_TEXT_KEY);

        return new MessageContent(titleItem, contentItem, buttonItem);
    }

    /**
     * 构建批量定薪方式不一致消息的国际化项目
     *
     * @param employeeI18nParams 员工I18n参数，格式：{{E.tenant.1010,E.tenant.1011}}
     * @return 消息内容
     */
    public static MessageContent buildBatchSalaryMethodMismatchMessage(String employeeI18nParams) {
        InternationalItem titleItem = createInternationalItem(SALARY_METHOD_MISMATCH_TITLE_KEY);
        // 参数：employeeI18nParams, employeeFixedSalaryI18nKey, salaryRuleI18nKey
        // 模板：员工{0}【{1}】与适用的【{2}】定薪方式不一致，将无法参与工资计算，请尽快调整
        InternationalItem contentItem = createInternationalItem(SALARY_METHOD_MISMATCH_MESSAGE_KEY,
                employeeI18nParams, EMPLOYEE_FIXED_SALARY_I18N_KEY, SALARY_RULE_I18N_KEY);
        InternationalItem buttonItem = createInternationalItem(PROCESS_BUTTON_TEXT_KEY);

        return new MessageContent(titleItem, contentItem, buttonItem);
    }

    /**
     * 构建员工I18n参数字符串
     *
     * @param tenantId 租户ID（如：tenant001）
     * @param employeeIds 员工ID列表
     * @return I18n参数字符串，格式：{{E.tenant001.1010,E.tenant001.1011}}
     */
    public static String buildEmployeeI18nParams(EmployeeDao employeeDao, String tenantId, List<String> employeeIds) {

//        if (employeeIds == null || employeeIds.isEmpty()) {
//            return "";
//        }
//
//        StringBuilder sb = new StringBuilder("{{");
//        for (int i = 0; i < employeeIds.size(); i++) {
//            if (i > 0) {
//                sb.append(",");
//            }
//            sb.append("E.").append(tenantId).append(".").append(employeeIds.get(i));
//        }
//        sb.append("}}");
//
//        return sb.toString();
        Map<String, String> userNameByStringIds = employeeDao.getUserNameByStringIds(tenantId, employeeIds);
        return userNameByStringIds.values().stream().collect(Collectors.joining(","));
    }

    /**
     * 消息内容封装类（使用国际化项目）
     */
    public static class MessageContent {
        private final InternationalItem titleItem;
        private final InternationalItem contentItem;
        private final InternationalItem buttonItem;
        private final String text; // 默认文本，用于不支持国际化的场景

        public MessageContent(InternationalItem titleItem, InternationalItem contentItem, InternationalItem buttonItem) {
            this.titleItem = titleItem;
            this.contentItem = contentItem;
            this.buttonItem = buttonItem;
            // 设置默认文本值，使用contentItem的国际化键作为默认值
            this.text = contentItem != null ? contentItem.getInternationalKey() : "工资相关通知"; //ignoreI18n
        }

        public InternationalItem getTitleItem() {
            return titleItem;
        }

        public InternationalItem getContentItem() {
            return contentItem;
        }

        public InternationalItem getButtonItem() {
            return buttonItem;
        }

        public String getText() {
            return text;
        }
    }


}
