package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.util.FieldValidationUtil;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * 工资发放单编辑Action
 *
 * 功能：允许编辑发放说明字段，其他字段不允许修改
 */
@Slf4j
public class SalaryPaymentSlipEditAction extends StandardEditAction {
  //不可修改的字段
  final static Set<String> NON_EDITABLE_FIELDS = ImmutableSet.of(
      SalaryPaymentSlipFields.PAY_STATUS,
      SalaryPaymentSlipFields.START_DATE,
      SalaryPaymentSlipFields.END_DATE,
      SalaryPaymentSlipFields.SALARY_RULE,
      SalaryPaymentSlipFields.PAY_PERIOD,
      SalaryPaymentSlipFields.SALARY_STATEMENT_HEADER
  );

  @Override
  protected void before(Arg arg) {
    super.before(arg);
    // 验证只有发放说明字段可以修改，其他字段都不能修改
    validateOnlyPayDescriptionCanBeModified(arg, dbMasterData);
  }

  /**
   * 验证只有发放说明字段可以修改，其他字段都不能修改
   */
  private void validateOnlyPayDescriptionCanBeModified(Arg arg, IObjectData originalData) {
    // 检查每个字段是否被修改
    for (String fieldName : NON_EDITABLE_FIELDS) {
      // 对于不允许修改的字段，使用工具类验证
      Object newValue = arg.getObjectData().get(fieldName);
      Object originalValue = originalData.get(fieldName);

      String reason = "工资发放单编辑时只允许修改发放说明字段"; // ignoreI18n
      FieldValidationUtil.validateFieldNotChanged(objectDescribe, fieldName, newValue, originalValue, reason);
    }

    log.info("字段修改验证通过，只有允许的字段被修改");
  }

}
