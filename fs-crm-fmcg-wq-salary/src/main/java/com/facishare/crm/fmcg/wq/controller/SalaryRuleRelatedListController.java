package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.facishare.crm.fmcg.wq.dao.SalaryDetailDataDao;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.fcp.exception.ValidationException;
import com.facishare.paas.appframework.common.service.DepartmentService;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 *               1. 特殊处理 新建编辑员工固定工资表的时候，选择规则
 * @author: zhangsm
 * @create: 2024-03-19 16:18
 **/
public class SalaryRuleRelatedListController extends StandardRelatedListController {
    private static final Logger log = LoggerFactory.getLogger(SalaryRuleRelatedListController.class);
    SalaryDetailDataDao salaryDetailDataDao = SpringUtil.getContext().getBean(SalaryDetailDataDao.class);
    SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);
    DepartmentService departmentService = SpringUtil.getContext().getBean(DepartmentService.class);
    EmployeeDao employeeDao = SpringUtil.getContext().getBean(EmployeeDao.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        // 处理员工固定工资表的薪资规则选择
        if (arg.getObjectData() != null
                && EmployeeFixedSalaryFields.API_NAME.equals(arg.getObjectData().toObjectData().getDescribeApiName())) {
            try {
                String tenantId = controllerContext.getTenantId();
                ObjectDataExt employeeFixedSalaryExt = ObjectDataExt.of(arg.getObjectData().toObjectData());

                // 调用服务方法获取适用的规则ID列表
                List<IObjectData> ruleObjs = salaryService.getApplicableSalaryRuleIdsByEmployeeFixedSalary(
                        tenantId, employeeFixedSalaryExt);
                List<String> ruleIds = ruleObjs.stream().map(IObjectData::getId).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(ruleIds)) {
                    // 将规则ID拼接到查询条件中
                    addRuleIdsToQuery(arg, ruleIds);
                    log.info("为员工固定工资表找到 {} 个适用的薪资规则", ruleIds.size());
                } else {
                    log.info("为员工固定工资表未找到适用的薪资规则");
                    // 如果没有找到适用规则，设置一个不存在的ID，使查询结果为空
                    addRuleIdsToQuery(arg, Lists.newArrayList("NO_APPLICABLE_RULES"));
                }
            } catch (Exception e) {
                log.error("处理员工薪资规则查询时发生异常", e);
            }
        }else if(arg.getObjectData() != null && SalaryPaymentSlipFields.API_NAME.equals(arg.getObjectData().toObjectData().getDescribeApiName())){
            //如果是工资发放单入口的 需要过滤业务类型
            if (SalaryPaymentSlipFields.RECORD_TYPE_EXTERNAL.equals(arg.getObjectData().toObjectData().getRecordType())){
                addRecordTypeToQuery(arg,SalaryRuleFields.RECORD_TYPE_EXTERNAL);
            }else {
                addRecordTypeToQuery(arg, SalaryRuleFields.RECORD_TYPE_INTERNAL);
            }
        }
    }

    private void addRecordTypeToQuery(Arg arg,String recordType) {
        try {
            ISearchTemplateQuery iSearchTemplateQuery = SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
            Filter filter = SearchQuery.filter(BaseField.recordType.getApiName(), Operator.EQ, recordType);
            iSearchTemplateQuery.addFilters(Lists.newArrayList(filter));
            arg.setSearchQueryInfo(iSearchTemplateQuery.toJsonString());
        } catch (Exception e) {
            log.error("添加规则ID到查询条件时发生异常", e);
        }
    }

    /**
     * 将规则ID添加到查询条件中
     * 
     * @param arg     参数对象
     * @param ruleIds 规则ID列表
     */
    private void addRuleIdsToQuery(Arg arg, List<String> ruleIds) {
        try {
            ISearchTemplateQuery iSearchTemplateQuery = SearchTemplateQuery.fromJsonString(arg.getSearchQueryInfo());
            Filter filter;
            if (CollectionUtils.isEmpty(ruleIds)) {
                filter = SearchQuery.filter(BaseField.id.getApiName(), Operator.IN, Lists.newArrayList());
            } else {
                filter = SearchQuery.filter(BaseField.id.getApiName(), Operator.IN, ruleIds);
            }
            iSearchTemplateQuery.addFilters(Lists.newArrayList(filter));
            arg.setSearchQueryInfo(iSearchTemplateQuery.toJsonString());
        } catch (Exception e) {
            log.error("添加规则ID到查询条件时发生异常", e);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        return result;
    }
}
