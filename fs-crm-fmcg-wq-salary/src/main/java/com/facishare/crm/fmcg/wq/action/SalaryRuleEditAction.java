package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.util.DataUtils;
import com.facishare.crm.fmcg.wq.util.FieldValidationUtil;
import com.facishare.crm.fmcg.wq.util.SalaryRuleMandatoryPersonnelUtil;
import com.facishare.crm.fmcg.wq.util.SalaryRuleFormulaUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 薪资规则编辑Action
 *
 * 编辑规则：
 * 1. 任何时机都可以编辑
 * 2. 工资规则生效日期前，支持编辑全部字段
 * 3. 当工资规则生效日期后，仅支持编辑「工资规则名称」和「适用范围」
 */
@Slf4j
public class SalaryRuleEditAction extends FmcgSkipPermissionEditAction {

  private SalaryItemDao salaryItemDao = SpringUtil.getContext().getBean(SalaryItemDao.class);
  private SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);
  private SalaryRuleDao salaryRuleDao = SpringUtil.getContext().getBean(SalaryRuleDao.class);

  // 当工资规则生效日期后，不允许编辑的字段（黑名单）
  // 使用 ImmutableSet 确保线程安全的并发访问
  private static final Set<String> NON_EDITABLE_FIELDS_AFTER_EFFECTIVE = ImmutableSet.of(
          SalaryRuleFields.SALARY_METHOD, // 定薪方式
          SalaryRuleFields.EFFECTIVE_DATE, // 生效日期
          SalaryRuleFields.SALARY_ITEM, // 工资项
          SalaryRuleFields.DISTRIBUTION_CYCLE, // 发放周期
          SalaryRuleFields.DISTRIBUTION_TIME, // 发放时机
          SalaryRuleFields.NEXT_WEEK_DISTRIBUTION_OP, // 次周发放时机
          SalaryRuleFields.NEXT_MONTH_DISTRIBUTION_OP, // 次月发放时机
          SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE // 发放单自动创建时机
//          SalaryRuleFields.CALCULATION_FORMULA // 计算公式
  );

  @Override
  protected void before(Arg arg) {
    super.before(arg);
    log.info("开始处理薪资规则编辑，数据: {}", arg.getObjectData());

    String tenantId = actionContext.getTenantId();
    String salaryRuleId = arg.getObjectData().getId();
    if (dbMasterData == null) {
        throw new ValidateException("工资规则不存在"); //ignoreI18n
    }

    // 检查工资规则是否已生效
    Long effectiveDate = dbMasterData.get(SalaryRuleFields.EFFECTIVE_DATE, Long.class);
    long currentTime = System.currentTimeMillis();
    boolean isEffective = effectiveDate != null && effectiveDate <= currentTime;

    if (isEffective) {
        log.info("工资规则 {} 已生效，限制编辑字段", salaryRuleId);
        // 验证编辑的字段是否在允许范围内
        validateEditableFieldsAfterEffective(arg.getObjectData(), dbMasterData);
    } else {
      // 3. 验证生效时间
      long todayMilli = LocalDate.now().atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli();

      if (effectiveDate != null) {
        if (effectiveDate < todayMilli) {
          throw new ValidateException("生效时间不能在当日以前"); //ignoreI18n
        }
      }else{
        objectData.set(SalaryRuleFields.EFFECTIVE_DATE, todayMilli);
      }
        log.info("工资规则 {} 未生效，允许编辑全部字段", salaryRuleId);
    }

    ObjectDataExt objectData = ObjectDataExt.of(arg.getObjectData().toObjectData());
    String dbSalaryMethod = dbMasterData.get(SalaryRuleFields.SALARY_METHOD).toString();

    // 1. 验证定薪方式和发放周期的关系
    String salaryMethod = objectData.getStringValue(SalaryRuleFields.SALARY_METHOD);
    String distributionCycle = objectData.getStringValue(SalaryRuleFields.DISTRIBUTION_CYCLE);
    if (StringUtils.isNotBlank(salaryMethod)) {
      if (SalaryRuleFields.SALARY_METHOD_Options_2.equals(salaryMethod)) {
        // 周薪只能选择按周发放
        if (!SalaryRuleFields.DISTRIBUTION_CYCLE_Options_2.equals(distributionCycle)) {
          throw new ValidateException("周薪只能选择按周发放"); //ignoreI18n
        }
      } else if (SalaryRuleFields.SALARY_METHOD_Options_3.equals(salaryMethod)) {
        // 月薪只能选择按月发放
        if (!SalaryRuleFields.DISTRIBUTION_CYCLE_Options_3.equals(distributionCycle)) {
          throw new ValidateException("月薪只能选择按月发放"); //ignoreI18n
        }
      }
    }
    
    // 2. 验证发放单自动创建时机与发放周期的关系
    String autoCreated = objectData.getStringValue(SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE);
    if (SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE_Options_true.equals(autoCreated)) {
      if (StringUtils.isBlank(distributionCycle)) {
        throw new ValidateException("请选择发放周期"); //ignoreI18n
      }
      String distributionTime = objectData.getStringValue(SalaryRuleFields.DISTRIBUTION_TIME);
      
      // 按日发放只能选择次日
      if (SalaryRuleFields.DISTRIBUTION_CYCLE_Options_1.equals(distributionCycle) 
          && !SalaryRuleFields.DISTRIBUTION_TIME_Options_1.equals(distributionTime)) {
        throw new ValidateException("按日发放只能选择次日创建发放单"); //ignoreI18n
      }
      
      // 按周发放只能选择次周
      if (SalaryRuleFields.DISTRIBUTION_CYCLE_Options_2.equals(distributionCycle) 
          && !SalaryRuleFields.DISTRIBUTION_TIME_Options_2.equals(distributionTime)) {
        throw new ValidateException("按周发放只能选择次周创建发放单"); //ignoreI18n
      }
      
      // 按月发放只能选择次月
      if (SalaryRuleFields.DISTRIBUTION_CYCLE_Options_3.equals(distributionCycle) 
          && !SalaryRuleFields.DISTRIBUTION_TIME_Options_3.equals(distributionTime)) {
        throw new ValidateException("按月发放只能选择次月创建发放单"); //ignoreI18n
      }
      
      // 验证次周创建发放单时机
      if (SalaryRuleFields.DISTRIBUTION_TIME_Options_2.equals(distributionTime) 
          && objectData.get(SalaryRuleFields.NEXT_WEEK_DISTRIBUTION_OP) == null) {
        throw new ValidateException("请选择次周创建发放单时机"); //ignoreI18n
      }
      
      // 验证次月创建发放单时机
      if (SalaryRuleFields.DISTRIBUTION_TIME_Options_3.equals(distributionTime) 
          && objectData.get(SalaryRuleFields.NEXT_MONTH_DISTRIBUTION_OP) == null) {
        throw new ValidateException("请选择次月创建发放单时机"); //ignoreI18n
      }
    }
    

    
    // 4. 验证工资项是否存在且与定薪方式匹配
    Object salaryItems = objectData.get(SalaryRuleFields.SALARY_ITEM);
    if (salaryItems != null) {
      // 解析工资项ID列表（支持数组和字符串格式）
      List<String> salaryItemIds = parseSalaryItemIds(salaryItems);

      if (!salaryItemIds.isEmpty()) {

        List<IObjectData> salaryItemObjs = salaryItemDao.getbyIds(actionContext.getTenantId(), salaryItemIds);

        if (salaryItemObjs.size() != salaryItemIds.size()) {
          throw new ValidateException("部分薪资项目不存在"); //ignoreI18n
        }

        // 验证工资项的定薪方式是否与规则的定薪方式匹配
        for (IObjectData salaryItemObj : salaryItemObjs) {
          ObjectDataExt salaryItemObjectData = ObjectDataExt.of(salaryItemObj);
          String itemSalaryMethod = salaryItemObjectData.getStringValue(SalaryItemFields.SALARY_METHOD);
          if (!salaryMethod.equals(itemSalaryMethod)) {
            throw new ValidateException("工资项[" + salaryItemObj.getName() + "]的定薪方式与规则不匹配"); //ignoreI18n
          }
        }
      }
    }

    // 设置计算公式字段
    setCalculationFormula(objectData);

    // 验证强制适用和强制不适用人员
    validateMandatoryPersonnel(objectData, salaryRuleId);

    log.info("薪资规则编辑前处理完成");
  }

  /**
   * 验证强制适用和强制不适用人员
   */
  private void validateMandatoryPersonnel(ObjectDataExt objectData, String currentRuleId) {
    // 获取强制适用人员和强制不适用人员
    Object mandatoryApplicablePersonnel = objectData.get(SalaryRuleFields.MANDATORY_APPLICABLE_PERSONNEL);
    Object nonApplicablePersonnel = objectData.get(SalaryRuleFields.NON_APPLICABLE_PERSONNEL);

    // 使用工具类验证重复（只验证重复，不验证冲突）
    SalaryRuleMandatoryPersonnelUtil.validateMandatoryPersonnelDuplicates(
            mandatoryApplicablePersonnel, nonApplicablePersonnel);
  }



  /**
   * 设置计算公式字段
   * 根据配置的工资项构建计算公式
   *
   * @param objectData 工资规则数据
   */
  private void setCalculationFormula(ObjectDataExt objectData) {
    try {
      String tenantId = actionContext.getTenantId();

      // 使用工具类构建计算公式
      String calculationFormula = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
          tenantId, objectData, salaryItemDao);

      if (StringUtils.isNotBlank(calculationFormula)) {
        objectData.set(SalaryRuleFields.CALCULATION_FORMULA, calculationFormula);
        log.info("薪资规则编辑时设置计算公式: {}", calculationFormula);
      } else {
        log.debug("薪资规则未配置工资项或无法构建计算公式，跳过设置");
      }

    } catch (Exception e) {
      log.error("设置薪资规则计算公式时发生异常", e);
      // 不影响主流程，只记录错误日志
    }
  }
  
  @Override
  protected Result after(Arg arg, Result result) {
    Result after = super.after(arg, result);
    log.info("薪资规则编辑成功，ID: {}", result.getObjectData().getId());

    // 处理强制适用人员冲突
    processMandatoryPersonnelConflictsAfterSave(arg, result);

    // 处理适用范围变更
    processApplicableScopeChanges(arg, result);

    return after;
  }

  /**
   * 处理强制适用人员冲突（保存后处理）
   * 从其他规则中移除当前规则的强制适用人员
   */
  private void processMandatoryPersonnelConflictsAfterSave(Arg arg, Result result) {
    String tenantId = actionContext.getTenantId();
    String currentRuleId = result.getObjectData().getId();
    Object mandatoryApplicablePersonnel = result.getObjectData().get(SalaryRuleFields.MANDATORY_APPLICABLE_PERSONNEL);

    // 使用工具类处理冲突
    salaryService.processMandatoryPersonnelConflicts(
            tenantId, currentRuleId, DataUtils.convertToStringList(mandatoryApplicablePersonnel));
  }

  /**
   * 验证工资规则生效后可编辑的字段（使用黑名单机制）
   * @param newData 新数据
   * @param originalData 原始数据
   */
  private void validateEditableFieldsAfterEffective(ObjectDataDocument newData, IObjectData originalData) {
    // 检查黑名单中的字段是否被修改
    for (String fieldName : newData.keySet()) {
      if (!NON_EDITABLE_FIELDS_AFTER_EFFECTIVE.contains(fieldName)) {
        continue; // 不在黑名单中的字段允许编辑，跳过检查
      }

      // 检查黑名单字段是否被修改
      Object newValue = newData.get(fieldName);
      Object originalValue = originalData.get(fieldName);

      if (!FieldValidationUtil.isFieldValueEqual(newValue, originalValue)) {
        String fieldDisplayName = FieldValidationUtil.getFieldDisplayName(objectDescribe, fieldName);
        throw new ValidateException(String.format("工资规则已生效，不允许修改%s", fieldDisplayName)); //ignoreI18n
      }
    }
  }



  /**
   * 处理适用范围变更
   */
  private void processApplicableScopeChanges(Arg arg, Result result) {
    try {
      String tenantId = actionContext.getTenantId();
      String salaryRuleId = result.getObjectData().getId();

      log.info("处理薪资规则适用范围变更，tenantId: {}, salaryRuleId: {}", tenantId, salaryRuleId);

      // 获取旧的适用范围数据（编辑前的数据）
      IObjectData oldObjectData = dbMasterData;

      // 获取新的适用范围数据（编辑后的数据）
      IObjectData newObjectData = result.getObjectData().toObjectData();

      // 调用 SalaryService 处理适用范围变更
      salaryService.processApplicableScopeChanges(tenantId, salaryRuleId, oldObjectData, newObjectData);

    } catch (Exception e) {
      log.error("处理薪资规则适用范围变更时发生异常", e);
      // 这里可以选择抛出异常或者记录错误日志
      // 根据业务需求决定是否影响主流程
    }
  }

  /**
   * 解析工资项ID列表，支持多种数据格式
   *
   * @param salaryItems 工资项数据，可能是数组、List或逗号分隔的字符串
   * @return 工资项ID列表
   */
  private List<String> parseSalaryItemIds(Object salaryItems) {
    List<String> result = new ArrayList<>();

    if (salaryItems == null) {
      return result;
    }

    try {
      if (salaryItems instanceof List) {
        // 如果是List类型
        List<?> itemList = (List<?>) salaryItems;
        for (Object item : itemList) {
          if (item != null && StringUtils.isNotBlank(item.toString())) {
            result.add(item.toString().trim());
          }
        }
      } else if (salaryItems instanceof Object[]) {
        // 如果是数组类型
        Object[] itemArray = (Object[]) salaryItems;
        for (Object item : itemArray) {
          if (item != null && StringUtils.isNotBlank(item.toString())) {
            result.add(item.toString().trim());
          }
        }
      } else if (salaryItems instanceof String) {
        // 如果是字符串类型（逗号分隔）
        String itemsStr = (String) salaryItems;
        if (StringUtils.isNotBlank(itemsStr)) {
          String[] itemIds = itemsStr.split(",");
          for (String itemId : itemIds) {
            if (StringUtils.isNotBlank(itemId)) {
              result.add(itemId.trim());
            }
          }
        }
      } else {
        // 其他类型，尝试转换为字符串处理
        String itemsStr = salaryItems.toString();
        if (StringUtils.isNotBlank(itemsStr)) {
          // 尝试按逗号分割
          if (itemsStr.contains(",")) {
            String[] itemIds = itemsStr.split(",");
            for (String itemId : itemIds) {
              if (StringUtils.isNotBlank(itemId)) {
                result.add(itemId.trim());
              }
            }
          } else {
            // 单个值
            result.add(itemsStr.trim());
          }
        }
      }

      log.debug("解析工资项ID列表: 原始数据类型={}, 解析结果={}",
              salaryItems.getClass().getSimpleName(), result);

    } catch (Exception e) {
      log.error("解析工资项ID列表失败: {}", salaryItems, e);
      throw new ValidateException("工资项数据格式错误"); //ignoreI18n
    }

    return result;
  }
}

