package com.facishare.crm.fmcg.wq.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.*;
import com.facishare.crm.fmcg.wq.enums.SalaryRuleOperation;
import com.facishare.crm.fmcg.wq.factory.SalaryKPIFactory;
import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.SalaryItem;
import com.facishare.crm.fmcg.wq.model.api.DailySalaryQuery;
import com.facishare.crm.fmcg.wq.model.api.EmployeeSalaryTypeQuery;
import com.facishare.crm.fmcg.wq.model.api.SalaryPeriodTotalQuery;
import com.facishare.crm.fmcg.wq.mq.SalaryTaskMessage;
import com.facishare.crm.fmcg.wq.notify.SalaryMessageService;
import com.facishare.crm.fmcg.wq.service.RoleQueryService;
import com.facishare.crm.fmcg.wq.service.SalaryExpressionCalcService;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.service.decorator.RetrySalaryExpressionCalcService;
import com.facishare.crm.fmcg.wq.session.FmcgPushSession;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.crm.fmcg.wq.util.SalaryAmountCalculator;
import com.facishare.crm.fmcg.wq.util.SalaryRecordTypeUtil;
import com.facishare.organization.paas.service.PaaSPermissionService;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.DepartmentService;
import com.facishare.paas.appframework.common.service.EmployeeService;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.QuoteFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.Pair;
import com.fxiaoke.enterpriserelation2.service.AppOuterRoleService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import org.redisson.api.RLock;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 薪资服务实现类
 * @author: dev
 * @create: 2024-06-23
 */
@Service
@Slf4j
public class SalaryServiceImpl implements SalaryService {

    @Autowired
    private SalaryRuleDao salaryRuleDao;
    @Autowired
    private SalaryItemDao salaryItemDao;
    @Autowired
    private SalaryDataDao salaryDataDao;
    @Autowired
    private SalaryKPIDao salaryKPIDao;
    @Autowired
    private SalaryDetailDataDao salaryDetailDataDao;
    @Autowired
    private EmployeeFixedSalaryDetailDao employeeFixedSalaryDetailDao;
    @Autowired
    private EmployeeFixedSalaryDao employeeFixedSalaryDao;

    @Autowired
    private SalaryPaymentSlipDao salaryPaymentSlipDao;

    @Autowired
    private SalaryExpressionCalcService salaryExpressionCalcService;

    @Autowired
    private SalaryKPIFactory salaryKPIFactory;

    @Autowired
    private FmcgPushSession fmcgPushSession;

    @Autowired
    public CRMNotificationService crmNotificationService;

    @Autowired
    private SalaryMessageService salaryMessageService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private EmployeeDao employeeDao;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private PaaSPermissionService paaSPermissionService;

    @Autowired
    private AppOuterRoleService appOuterRoleService;

    @Autowired
    private BaseDao baseDao;

    @Autowired
    private RedissonService redissonService;
    @Autowired
    private RoleQueryService roleQueryService;

    // Redis锁的前缀
    private static final String SALARY_TASK_LOCK_PREFIX = "fs-crm-fmcg-wq-salary-task-lock-";

    // 锁的过期时间（秒）- 30分钟，单个任务处理时间
    private static final long LOCK_EXPIRE_TIME = 1800L;

    /**
     * 初始化方法，用重试装饰器包装salaryExpressionCalcService
     */
    @PostConstruct
    public void init() {
        // 使用重试装饰器包装工资项表达式计算服务，提供三次重试机制
        this.salaryExpressionCalcService = new RetrySalaryExpressionCalcService(this.salaryExpressionCalcService);
        log.info("已使用重试装饰器包装工资项表达式计算服务，提供3次重试机制");
    }

    @Override
    public List<IObjectData> calculateSalaryDetailDatas(IObjectData employeeFixedSalaryObj, IObjectData salaryRuleObj,
            long startTime, long endTime, boolean recalc) {
        List<IObjectData> result = Lists.newArrayList();
        ObjectDataExt employeeFixedSalaryObjExt = ObjectDataExt.of(employeeFixedSalaryObj);
        String tenantId = employeeFixedSalaryObjExt.getTenantId();

        // 创建固定工资明细缓存，避免重复查询
        Map<String, List<IObjectData>> fixedSalaryDetailCache = new HashMap<>();
        String employeeId = employeeFixedSalaryObjExt.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE);
        if (StringUtils.isBlank(employeeId)) {
            employeeId = employeeFixedSalaryObjExt.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);
        }
        if (StringUtils.isBlank(employeeId)) {
            throw new ValidateException("员工id为空"); // ignoreI18n
        }
        log.info("计算员工薪资，employeeId: {}, cycleStart: {}, cycleEnd: {}, recalc: {}", employeeId, startTime, endTime,
                recalc);
        if (salaryRuleObj == null) {
            // 1.查薪资规则
            String salaryRuleId = employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.SALARY_RULE);
            if (StringUtils.isBlank(salaryRuleId)) {
                throw new ValidateException("薪资规则id为空"); // ignoreI18n
            }
            salaryRuleObj = salaryRuleDao.getById(tenantId, salaryRuleId);
        }
        if (salaryRuleObj == null) {
            throw new ValidateException("薪资规则不存在"); // ignoreI18n
        }
        ObjectDataExt salaryRuleExt = ObjectDataExt.of(salaryRuleObj);
        // 2.查工资项
        List<String> salaryItemIds = salaryRuleExt.getDimensionValues(SalaryRuleFields.SALARY_ITEM);
        if (salaryItemIds == null || salaryItemIds.isEmpty()) {
            throw new ValidateException("薪资规则中未配置工资项");// ignoreI18n
        }
        List<IObjectData> salaryItems = salaryItemDao.getbyIds(tenantId, salaryItemIds);
        if (salaryItems == null || salaryItems.isEmpty()) {
            throw new ValidateException("薪资规则中配置的工资项不存在");// ignoreI18n
        }

        // 查询已存在的薪资明细数据
        List<IObjectData> existingSalaryDetailDatas = salaryDetailDataDao.getExistSalaryDetailData(tenantId,
                employeeFixedSalaryObjExt.getId(), startTime, endTime);
        log.info("查询到已存在的薪资明细数据，数量: {}", existingSalaryDetailDatas != null ? existingSalaryDetailDatas.size() : 0);

        // 将已存在的薪资明细按工资项ID分组，便于后续查找
        Map<String, IObjectData> existingSalaryDetailMap = new HashMap<>();
        if (existingSalaryDetailDatas != null && !existingSalaryDetailDatas.isEmpty()) {
            for (IObjectData detailData : existingSalaryDetailDatas) {
                String salaryItemId = detailData.get(SalaryDetailDataFields.SALARY_ITEM, String.class);
                if (salaryItemId != null && !salaryItemId.isEmpty()) {
                    existingSalaryDetailMap.put(salaryItemId, detailData);
                }
            }
        }

        // 创建一个集合来存储当前薪资项的ID，用于后续处理不在当前薪资项列表中的数据
        Set<String> currentSalaryItemIds = salaryItems.stream()
                .map(IObjectData::getId)
                .collect(Collectors.toSet());

        // 处理已存在但不在当前薪资项列表中的数据
        if (!existingSalaryDetailMap.isEmpty()) {
            for (Map.Entry<String, IObjectData> entry : existingSalaryDetailMap.entrySet()) {
                String salaryItemId = entry.getKey();
                IObjectData detailData = entry.getValue();

                // 如果该薪资项不在当前薪资项列表中
                if (!currentSalaryItemIds.contains(salaryItemId)) {
                    String distributionStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS,
                            String.class);

                    // 如果是已修正状态，保留该数据
                    if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_4.equals(distributionStatus)) {
                        log.info("发现已修正但不在当前薪资项列表中的明细，将保留其数据，明细ID: {}, 工资项ID: {}",
                                detailData.getId(), salaryItemId);
                        result.add(detailData);
                    } else {
                        log.info("发现不在当前薪资项列表中且未修正的明细，将忽略该数据，明细ID: {}, 工资项ID: {}",
                                detailData.getId(), salaryItemId);
                        // 删除
                        salaryDetailDataDao.batchInvalidAndDel(User.systemUser(tenantId),Lists.newArrayList(detailData));
                    }
                }
            }
        }

        SalaryContext salaryContext = SalaryContext.builder()
                .endTime(endTime)
                .startTime(startTime)
                .owner(employeeId) // 这里是员工ID，用于KPI计算
                .tenantId(tenantId)
                .extDataMap(Maps.newHashMap())
                .extDataNameMap(Maps.newHashMap())
                .stopWatch(StopWatch.create(this.getClass().getSimpleName()))
                .build();

        // 3.计算每个薪资项目
        for (IObjectData salaryItem : salaryItems) {
            String salaryItemId = salaryItem.getId();
            IObjectData existingDetailData = null;

            // 检查是否存在已有的薪资明细
            if (existingSalaryDetailMap.containsKey(salaryItemId)) {
                existingDetailData = existingSalaryDetailMap.get(salaryItemId);
                String distributionStatus = existingDetailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS,
                        String.class);

                // 如果是已修正状态，直接使用该数据，不重新计算
                if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_4.equals(distributionStatus)) {
                    log.info("使用已修正的薪资明细数据，不重新计算，明细ID: {}, 工资项ID: {}",
                            existingDetailData.getId(), salaryItemId);
                    result.add(existingDetailData);
                    continue;
                }
                // 如果是生成异常状态，强制重新计算
                else if (SalaryDetailDataFields.PAY_STATUS_Options_ERROR.equals(distributionStatus)) {
                    log.info("发现生成异常的薪资明细，强制重新计算，明细ID: {}, 工资项ID: {}",
                            existingDetailData.getId(), salaryItemId);
                    // 继续执行重新计算逻辑
                }
                // 如果不需要重新计算，直接使用已有数据
                else if (!recalc) {
                    log.info("使用已有的薪资明细数据，不需要重新计算，明细ID: {}, 工资项ID: {}",
                            existingDetailData.getId(), salaryItemId);
                    result.add(existingDetailData);
                    continue;
                }
                // 需要重新计算，使用已有对象但更新其值
                else {
                    log.info("重新计算已有的薪资明细数据，明细ID: {}, 工资项ID: {}",
                            existingDetailData.getId(), salaryItemId);
                }
            }

            // 如果不存在已有明细或需要重新计算，则创建新的薪资明细或更新已有明细
            IObjectData salaryDetailData = existingDetailData != null ? existingDetailData
                    : salaryDetailDataDao.createSalaryDetailDataByEmployeeFixedSalaryObjExt(employeeFixedSalaryObjExt,
                            startTime, endTime);

            // 设置基本信息
            setupDetailBasicInfo(salaryDetailData, salaryItem, employeeId);

            // 使用公共方法处理明细计算
            ProcessResult processResult = processSingleSalaryDetail(salaryDetailData, salaryItem, salaryContext,
                    employeeFixedSalaryObjExt.getId(), fixedSalaryDetailCache);

            // 添加明细到结果中
            result.add(salaryDetailData);
        }

        // 区分需要保存的新数据和需要更新的已有数据
        List<IObjectData> newDetailDatas = new ArrayList<>();
        List<IObjectData> updateDetailDatas = new ArrayList<>();

        for (IObjectData detailData : result) {
            if (detailData.getId() == null || detailData.getId().isEmpty()) {
                newDetailDatas.add(detailData);
            } else {
                updateDetailDatas.add(detailData);
            }
        }

        // 保存新创建的薪资明细数据
        if (!newDetailDatas.isEmpty()) {
            log.info("保存新创建的薪资明细数据，数量: {}", newDetailDatas.size());
            salaryDetailDataDao.batchSave(User.systemUser(tenantId), newDetailDatas);
        }

        // 更新已有的薪资明细数据
        if (!updateDetailDatas.isEmpty()) {
            log.info("批量更新已有的薪资明细数据，数量: {}", updateDetailDatas.size());
            // 使用批量更新替代for循环单条更新，提高性能
            salaryDetailDataDao.batchUpdate(updateDetailDatas, User.systemUser(tenantId));
        }
        return result;
    }

    /**
     * 创建工资发放单
     *
     * @param tenantId       租户ID
     * @param salaryRuleId   工资规则ID
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param payCycle       发薪周期
     * @param payDescription 发放说明
     * @return 创建的工资发放单对象
     */
    @Override
    public IObjectData createSalaryPaymentSlip(IObjectData salaryRuleObj, long startTime, long endTime,
            String payDescription) {
        try {
            ObjectDataExt salaryRuleExt = ObjectDataExt.of(salaryRuleObj);
            String tenantId = salaryRuleExt.getTenantId();
            String salaryRuleId = salaryRuleObj.getId();

            log.info("开始创建工资发放单，租户ID: {}, 工资规则ID: {}, 开始时间: {}, 结束时间: {}",
                    tenantId, salaryRuleId, startTime, endTime);
            // 创建工资发放单对象
            IObjectData salaryPaymentSlip = null;
            // 检查是否已存在相同规则的工资发放单
            List<IObjectData> existingSlips = salaryPaymentSlipDao.getByRangeAndRule(tenantId, startTime, endTime,
                    salaryRuleId);
            if (existingSlips != null && !existingSlips.isEmpty()) {
                // 过滤出相同规则 且时间相同的发放单

                if (!existingSlips.isEmpty()) {
                    // 检查是否有时间完全相同的发放单
                    long finalStartTime = startTime;
                    Optional<IObjectData> exactTimeMatch = existingSlips.stream()
                            .filter(slip -> slip.get(SalaryPaymentSlipFields.START_DATE, Long.class) == finalStartTime
                                    && slip.get(SalaryPaymentSlipFields.END_DATE, Long.class) == endTime)
                            .findFirst();

                    if (exactTimeMatch.isPresent()) {
                        // 时间完全相同，走更新逻辑
                        salaryPaymentSlip = exactTimeMatch.get();
                        log.info("发现时间完全相同的工资发放单，ID: {}，走更新逻辑", salaryPaymentSlip.getId());

                    } else if (!existingSlips.isEmpty()) {
                        throw new ValidateException(MessageFormat.format("已存在相同规则且时间重叠的工资发放单 {1}:{0}，请检查时间范围 ", //ignoreI18n
                                existingSlips.get(0).getId(), existingSlips.get(0).getName()));
                    }

                }
            }

            // 规则里的生效时间和开始时间取最大值，确保工资发放单的开始时间不早于规则生效时间
            long originalStartTime = startTime;
            long ruleStartTime = salaryRuleObj.get(SalaryRuleFields.EFFECTIVE_DATE, Long.class);
            if (ruleStartTime > startTime) {
                startTime = ruleStartTime;
                log.info("工资发放单开始时间已调整，原始时间: {}, 规则生效时间: {}, 调整后时间: {}",
                        formatBeijingTime(originalStartTime), formatBeijingTime(ruleStartTime), formatBeijingTime(startTime));
            }
            if (endTime < startTime) {
                log.warn("跳过创建工资发放单，结束时间小于规则生效时间 - 开始时间: {}, 结束时间: {}, 规则生效时间: {}, 工资规则ID: {}",
                        formatBeijingTime(startTime), formatBeijingTime(endTime), formatBeijingTime(ruleStartTime), salaryRuleId);
                return null;
            }
            if (salaryPaymentSlip == null) {
                // 根据工资规则类型设置工资发放单的业务类型
                String recordType = SalaryRecordTypeUtil.getSalaryPaymentSlipRecordType(salaryRuleObj);
                salaryPaymentSlip = salaryPaymentSlipDao.createSalaryPaymentSlip(tenantId, recordType);
                log.debug("创建工资发放单，业务类型: {}, 工资规则ID: {}", recordType, salaryRuleId);
            }

            // 设置工资规则
            salaryPaymentSlip.set(SalaryPaymentSlipFields.SALARY_RULE, salaryRuleId);

            // 设置工资条表头（从工资规则中获取）
            Object salaryStatementHeader = salaryRuleObj.get(SalaryRuleFields.SALARY_STATEMENT_HEADER);
            if (salaryStatementHeader != null) {
                salaryPaymentSlip.set(SalaryPaymentSlipFields.SALARY_STATEMENT_HEADER, salaryStatementHeader);
            }

            // 设置时间范围
            salaryPaymentSlip.set(SalaryPaymentSlipFields.START_DATE, startTime);
            salaryPaymentSlip.set(SalaryPaymentSlipFields.END_DATE, endTime);


            // 设置发放说明
            if (StringUtils.isNotBlank(payDescription)) {
                salaryPaymentSlip.set(SalaryPaymentSlipFields.PAY_DESCRIPTION, payDescription);
            }

            // 设置发放状态为"正在生成"
            salaryPaymentSlip.set(SalaryPaymentSlipFields.PAY_STATUS, SalaryPaymentSlipFields.PAY_STATUS_Options_0);

            // 保存工资发放单
            User systemUser = User.systemUser(tenantId);
            if (salaryPaymentSlip.getId() == null || salaryPaymentSlip.getId().isEmpty()) {
                salaryPaymentSlip.setId(new ObjectId().toHexString());
                salaryPaymentSlipDao.saveSalaryPaymentSlip(systemUser, salaryPaymentSlip);
            } else {
                salaryPaymentSlipDao.updateSalaryPaymentSlip(systemUser, salaryPaymentSlip);
            }

            log.info("工资发放单创建成功，ID: {}", salaryPaymentSlip.getId());

            return salaryPaymentSlip;
        } catch (Exception e) {
            log.error("创建工资发放单失败", e);
            throw  e;
//            throw new RuntimeException("创建工资发放单失败: " + e.getMessage(), e); //ignoreI18n
        }
    }

    /**
     * 更新工资发放单状态
     *
     * @param tenantId            租户ID
     * @param salaryPaymentSlipId 工资发放单ID
     * @param payStatus           发放状态
     * @param payDescription      发放说明
     * @return 更新后的工资发放单对象
     */
    @Override
    public IObjectData updateSalaryPaymentSlipStatus(String tenantId, String salaryPaymentSlipId,
            String payStatus, String payDescription) {
        if (StringUtils.isBlank(tenantId)) {
            throw new ValidateException("租户ID不能为空"); // ignoreI18n
        }

        if (StringUtils.isBlank(salaryPaymentSlipId)) {
            throw new ValidateException("工资发放单ID不能为空"); // ignoreI18n
        }

        try {
            // 获取工资发放单对象
            IObjectData salaryPaymentSlip = salaryPaymentSlipDao.getById(tenantId, salaryPaymentSlipId);
            if (salaryPaymentSlip == null) {
                throw new ValidateException("工资发放单不存在，ID: " + salaryPaymentSlipId); // ignoreI18n
            }

            // 更新发放状态
            if (StringUtils.isNotBlank(payStatus)) {
                salaryPaymentSlip.set(SalaryPaymentSlipFields.PAY_STATUS, payStatus);
            }

            // 更新发放说明
            if (StringUtils.isNotBlank(payDescription)) {
                salaryPaymentSlip.set(SalaryPaymentSlipFields.PAY_DESCRIPTION, payDescription);
            }

            // 保存更新后的工资发放单
            User systemUser = User.systemUser(tenantId);
            IObjectData updatedSalaryPaymentSlip = salaryPaymentSlipDao.updateSalaryPaymentSlip(systemUser,
                    salaryPaymentSlip);

            log.info("工资发放单状态更新成功，ID: {}, 新状态: {}", updatedSalaryPaymentSlip.getId(), payStatus);

            return updatedSalaryPaymentSlip;
        } catch (Exception e) {
            log.error("更新工资发放单状态失败", e);
            throw new RuntimeException("更新工资发放单状态失败: " + e.getMessage(), e); //ignoreI18n
        }
    }

    @Override
    public IObjectData generateSalaryData(IObjectData employeeFixedSalaryObj, IObjectData salaryRuleObj, long startTime,
            long endTime, IObjectData salaryPaymentSlipObj) {
        if (employeeFixedSalaryObj == null) {
            throw new ValidateException("员工固定薪资对象为空"); // ignoreI18n
        }

        try {

            // 1. 从employeeFixedSalaryObj获取员工信息
            ObjectDataExt employeeFixedSalaryObjExt = ObjectDataExt.of(employeeFixedSalaryObj);
            String tenantId = employeeFixedSalaryObjExt.getTenantId();
            String employeeId = employeeFixedSalaryObjExt.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE);
            String employeeExternalId = employeeFixedSalaryObjExt
                    .getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);
            // 1.1 工资条明细
            List<IObjectData> salaryDetailDatas = salaryDetailDataDao.getExistSalaryDetailDataByTimeRange(tenantId,
                    employeeFixedSalaryObj.getId(), startTime, endTime);

            // 检查是否有生成异常的工资明细，如果有则需要重新计算
            boolean hasErrorDetails = false;
            List<IObjectData> errorDetailsList = new ArrayList<>();
            if (salaryDetailDatas != null && !salaryDetailDatas.isEmpty()) {
                for (IObjectData detailData : salaryDetailDatas) {
                    String distributionStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);
                    if (SalaryDetailDataFields.PAY_STATUS_Options_ERROR.equals(distributionStatus)) {
                        hasErrorDetails = true;
                        errorDetailsList.add(detailData);
                        log.info("发现生成异常的工资明细，将重新计算，明细ID: {}", detailData.getId());
                    }
                }
            }

            // 如果发现有生成异常的明细，使用公共方法重新计算这些明细
            if (hasErrorDetails) {
                log.info("检测到生成异常的工资明细，使用公共方法重新计算异常明细，数量: {}", errorDetailsList.size());

                // 创建工资项缓存，避免重复查询
                Map<String, IObjectData> salaryItemCache = new HashMap<>();
                // 创建固定工资明细缓存，避免重复查询
                Map<String, List<IObjectData>> fixedSalaryDetailCache = new HashMap<>();

                // 使用公共方法重新计算异常明细
                Pair<BigDecimal, Boolean> recalculationResult = recalculateSalaryDetails(tenantId, errorDetailsList,
                        employeeFixedSalaryObj, startTime, endTime, salaryItemCache, fixedSalaryDetailCache);

                // 更新hasErrorDetails状态
                hasErrorDetails = recalculationResult.getValue();

                log.info("重新计算异常明细完成，当前是否还有异常明细: {}", hasErrorDetails);
            }
            // 2. 查询已存在的工资条数据
            User systemUser = User.systemUser(tenantId);
            List<IObjectData> existingSalaryDatas = salaryDataDao.getByEmployeeFixedSalaryIdAndTimeEq(tenantId,
                    employeeFixedSalaryObj.getId(), startTime, endTime);

            // 用于存储工资条对象
            IObjectData salaryData = null;

            if (existingSalaryDatas != null && !existingSalaryDatas.isEmpty()) {
                log.info("发现已存在的工资条数据，数量: {}", existingSalaryDatas.size());
                // 使用第一个已存在的工资条进行更新
                salaryData = existingSalaryDatas.get(0);
                log.info("将使用已存在的工资条进行更新，ID: {}", salaryData.getId());
                //
                // // 获取关联的薪资明细
                // List<IObjectData> existingDetailDatas =
                // salaryDetailDataDao.getBySalaryDataId(tenantId, salaryData.getId());
                //
                // // 检查是否有已发放的薪资明细
                // boolean hasDistributedDetails = false;
                //
                // if (existingDetailDatas != null && !existingDetailDatas.isEmpty()) {
                // log.info("发现已存在的薪资明细数据，数量: {}", existingDetailDatas.size());
                //
                // for (IObjectData detailData : existingDetailDatas) {
                // String distributionStatus =
                // detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);
                //
                // // 检查是否为已发放状态
                // if
                // (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3.equals(distributionStatus))
                // {
                // if (!allowModify) {
                // log.error("存在已发放的薪资明细，不允许修改，明细ID: {}", detailData.getId());
                // throw new ValidateException("存在已发放的薪资明细，不允许修改"); //ignoreI18n
                // }
                // hasDistributedDetails = true;
                // }
                // }
                // }

                // 如果有多个工资条，记录日志但不处理
                if (existingSalaryDatas.size() > 1) {
                    log.warn("发现多个工资条数据，将只更新第一个，其他工资条将被忽略");
                }
            } else {
                // 3. 如果不存在工资条，则创建新的工资条对象
                log.info("未发现已存在的工资条数据，将创建新的工资条");

                // 设置业务类型：根据是否为外部员工设置不同的业务类型
                String recordType = SalaryRecordTypeUtil.getSalaryDataRecordType(employeeId, employeeExternalId);
                salaryData = salaryDataDao.createSalaryData(tenantId, recordType);
                log.debug("创建工资条，业务类型: {}, 员工ID: {}, 外部员工ID: {}", recordType, employeeId, employeeExternalId);

                // 设置owner
                if (StringUtils.isNotBlank(employeeId)) {
                    salaryData.setOwner(Lists.newArrayList(employeeId));
                } else {
                    salaryData.setOutOwner(Lists.newArrayList(employeeExternalId));
                }
            }

            // 4. 设置员工信息
            String mainDepartment = employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.MAIN_DEPARTMENT);
            String phoneNumber = employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.PHONE_NUMBER);
            String mobileNumberExternal = employeeFixedSalaryObjExt
                    .getStringValue(EmployeeFixedSalaryFields.MOBILE_NUMBER_EXTERNAL);
            String connectedRole = employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.CONNECTED_ROLE);

            // 设置员工相关字段
            salaryData.set(SalaryDataFields.EMPLOYEE, employeeId);
            salaryData.set(SalaryDataFields.EMPLOYEE_EXTERNAL, employeeExternalId);
            salaryData.set(SalaryDataFields.MAIN_DEPARTMENT, mainDepartment);
            salaryData.set(SalaryDataFields.PHONE_NUMBER, phoneNumber);
            salaryData.set(SalaryDataFields.MOBILE_NUMBER_EXTERNAL, mobileNumberExternal);
            salaryData.set(SalaryDataFields.CONNECTED_ROLE, connectedRole);

            // 5. 设置工资条的开始和结束时间
            salaryData.set(SalaryDataFields.START_DATE, startTime);
            salaryData.set(SalaryDataFields.END_DATE, endTime);

            // 6. 关联员工固定薪资对象
            salaryData.set(SalaryDataFields.EMPLOYEE_FIXED_SALARY, employeeFixedSalaryObj.getId());

            // 7. 计算薪资总额（应付工资），同时检查是否有已修正的明细
            boolean hasCorrectedDetails = false;

            // 查询所有工资项，用于分类计算
            List<String> salaryItemIds = new ArrayList<>();
            if (salaryDetailDatas != null && !salaryDetailDatas.isEmpty()) {
                for (IObjectData detailData : salaryDetailDatas) {
                    String salaryItemId = detailData.get(SalaryDetailDataFields.SALARY_ITEM, String.class);
                    if (salaryItemId != null && !salaryItemId.isEmpty()) {
                        salaryItemIds.add(salaryItemId);
                    }

                    // 检查是否有已修正的明细
                    String distributionStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);
                    if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_4.equals(distributionStatus)) {
                        hasCorrectedDetails = true;
                    }
                }
            }
            // 包含工资项
            salaryData.set(SalaryDataFields.INCLUDE_PAY_ITEMS, salaryItemIds);

            // 使用工具类计算应付工资总额
            BigDecimal totalSalary = SalaryAmountCalculator.calculatePayableSalary(salaryDetailDatas);

            // 8. 检查工资条是否已修正，如果已修正则不更新金额和状态
            String currentDistributionStatus = salaryData.get(SalaryDataFields.DISTRIBUTION_STATUS, String.class);
            boolean isSalaryDataCorrected = SalaryDataFields.DISTRIBUTION_STATUS_Options_4.equals(currentDistributionStatus);

            if (isSalaryDataCorrected) {
                // 如果工资条已修正，保持原有金额和状态不变
                String currentPayableSalary = salaryData.get(SalaryDataFields.PAYABLE_SALARY, String.class);
                log.info("工资条已修正，保持原有金额和状态不变，当前金额: {}, 重新计算金额: {}",
                        currentPayableSalary, SalaryAmountCalculator.formatAmount(totalSalary));
            } else {
                // 工资条未修正，更新为重新计算的金额（使用3位小数格式）
                String formattedAmount = SalaryAmountCalculator.formatAmount(totalSalary);
                salaryData.set(SalaryDataFields.PAYABLE_SALARY, formattedAmount);
                log.info("工资条未修正，更新为重新计算的金额: {}", formattedAmount);
            }
            // 8.1.工资发放单
            salaryData.set(SalaryDataFields.SALARY_PAYMENT_SLIP, salaryPaymentSlipObj.getId());

            // 设置薪资规则
            String salaryRuleId = employeeFixedSalaryObjExt.getStringValue(EmployeeFixedSalaryFields.SALARY_RULE);
            if (StringUtils.isNotBlank(salaryRuleId)) {
                salaryData.set(SalaryDataFields.SALARY_RULE, salaryRuleId);
            }

            // 设置工资条发放状态 - 如果工资条已修正则不更新状态
            if (!isSalaryDataCorrected) {
                if (hasErrorDetails) {
                    salaryData.set(SalaryDataFields.DISTRIBUTION_STATUS, SalaryDataFields.DISTRIBUTION_STATUS_Options_ERROR);
                    log.warn("工资条包含生成异常的明细，状态设置为生成异常，员工ID: {}",
                            employeeId != null ? employeeId : employeeExternalId);
                } else {
                    salaryData.set(SalaryDataFields.DISTRIBUTION_STATUS, SalaryDataFields.DISTRIBUTION_STATUS_Options_0);
                }
            } else {
                log.info("工资条已修正，保持原有状态不变，员工ID: {}",
                        employeeId != null ? employeeId : employeeExternalId);
            }

            // 保存工资条对象
            IObjectData savedSalaryData = salaryData.getId() == null || salaryData.getId().isEmpty()
                    ? salaryDataDao.saveSalaryData(systemUser, salaryData)
                    : salaryDataDao.updateSalaryData(systemUser, salaryData);

            log.info("工资条已保存，ID: {}", savedSalaryData.getId());

            // 9. 更新薪资明细，关联到工资条
            if (salaryDetailDatas != null && !salaryDetailDatas.isEmpty()) {
                for (IObjectData detailData : salaryDetailDatas) {
                    // 先取 原始关联
                    String originalSalaryDataId = detailData.get(SalaryDetailDataFields.SALARY_DATA, String.class);
                    if (savedSalaryData.getId().equals(originalSalaryDataId)
                            && SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_0
                                    .equals(detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class))) {
                        continue;
                    }
                    // 关联到工资条
                    detailData.set(SalaryDetailDataFields.SALARY_DATA, savedSalaryData.getId());
                    // 关联到工资发放单
                    detailData.set(SalaryDetailDataFields.SALARY_PAYMENT_SLIP, salaryPaymentSlipObj.getId());

                    // 如果没有设置发放状态，设置为未发放
                    if (detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS) == null) {
                        detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS,
                                SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_0);
                    }
                    salaryDetailDataDao.updateSalaryDetailData(systemUser, detailData);
                }
            }

            log.info("工资条生成成功，ID: {}, 员工ID: {}, 应付工资: {}",
                    savedSalaryData.getId(), employeeId != null ? employeeId : employeeExternalId, totalSalary);

            return savedSalaryData;
        } catch (Exception e) {
            log.error("生成工资条失败", e);
            throw new RuntimeException("生成工资条失败: " + e.getMessage(), e); //ignoreI18n
        }
    }

    private String formatFormula(String formulaStr, Map<String, Object> extDataNameMap) {
        if (formulaStr == null || extDataNameMap == null) {
            return formulaStr;
        }

        for (Map.Entry<String, Object> entry : extDataNameMap.entrySet()) {
            // 使用正则表达式中的特殊字符需要转义
            String key = Pattern.quote("$" + entry.getKey() + "$");
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            formulaStr = formulaStr.replaceAll(key, value);
        }
        return formulaStr;
    }









    /**
     * 根据工资发放单发放工资
     * <p>
     * 该方法将执行以下操作：
     * <ol>
     * <li>查询与工资发放单关联的所有工资条</li>
     * <li>查询每个工资条关联的薪资明细</li>
     * <li>更新薪资明细的发放状态为“已发放”</li>
     * <li>更新工资发放单的状态为“已发放”</li>
     * </ol>
     *
     * @param salaryPaymentSlipObj 工资发放单对象
     * @return 更新后的工资发放单对象
     * @throws RuntimeException 如果发放工资过程中发生错误
     */
    @Override
    public IObjectData paySalaryBySalaryPaymentSlip(IObjectData salaryPaymentSlipObj) {
        log.info("根据工资发放单发放工资，ID: {}", salaryPaymentSlipObj.getId());

        // 1. 从工资发放单获取信息
        ObjectDataExt salaryPaymentSlipObjExt = ObjectDataExt.of(salaryPaymentSlipObj);
        String tenantId = salaryPaymentSlipObjExt.getTenantId();
        User systemUser = User.systemUser(tenantId);

        try {
            // 2. 检查工资发放单状态
            String payStatus = salaryPaymentSlipObjExt.getStringValue(SalaryPaymentSlipFields.PAY_STATUS);
            if (SalaryPaymentSlipFields.PAY_STATUS_Options_4.equals(payStatus)) {
                log.warn("工资发放单已经是已发放状态，不需要重复发放，ID: {}", salaryPaymentSlipObj.getId());
                return salaryPaymentSlipObj;
            }

            if (!SalaryPaymentSlipFields.PAY_STATUS_Options_1.equals(payStatus)) {
                log.error("工资发放单状态不正确，无法发放工资，当前状态: {}, ID: {}", payStatus, salaryPaymentSlipObj.getId());
                throw new ValidateException("工资发放单状态不正确，无法发放工资"); // ignoreI18n
            }

            // 3. 查询与工资发放单关联的工资条
            List<IObjectData> salaryDataList = salaryDataDao.getAllIObjectDataListByQueryWithFields(
                    systemUser,
                    com.facishare.crm.fmcg.wq.util.SearchQuery.builder()
                            .eq(SalaryDataFields.SALARY_PAYMENT_SLIP, salaryPaymentSlipObj.getId())
                            .build(),
                    SalaryDataFields.API_NAME,
                    null);

            log.info("查询到与工资发放单关联的工资条数量: {}", salaryDataList.size());

            if (salaryDataList.isEmpty()) {
                log.warn("未找到与工资发放单关联的工资条，ID: {}", salaryPaymentSlipObj.getId());
                throw new ValidateException("未找到与工资发放单关联的工资条"); // ignoreI18n
            }

            // 4. 处理每个工资条及其关联的薪资明细
            int totalDetailCount = 0;
            int updatedDetailCount = 0;

            for (IObjectData salaryData : salaryDataList) {
                // 查询工资条关联的薪资明细
                List<IObjectData> detailDataList = salaryDetailDataDao.getBySalaryDataId(tenantId, salaryData.getId());
                totalDetailCount += detailDataList.size();

                log.info("工资条ID: {}, 关联的薪资明细数量: {}", salaryData.getId(), detailDataList.size());

                // 更新每个薪资明细的发放状态
                for (IObjectData detailData : detailDataList) {
                    String distributionStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS,
                            String.class);

                    // 如果已经是已发放状态，则跳过
                    if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3.equals(distributionStatus)) {
                        log.info("薪资明细已经是已发放状态，跳过更新，明细ID: {}", detailData.getId());
                        updatedDetailCount++;
                        continue;
                    }

                    // 更新为已发放状态
                    detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS,
                            SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3);
                    salaryDetailDataDao.updateSalaryDetailData(systemUser, detailData);
                    updatedDetailCount++;

                    log.info("更新薪资明细为已发放状态，明细ID: {}", detailData.getId());
                }
            }

            log.info("工资发放单关联的薪资明细总数: {}, 已更新数量: {}", totalDetailCount, updatedDetailCount);

            // 5. 更新工资发放单状态为已发放
            salaryPaymentSlipObj.set(SalaryPaymentSlipFields.PAY_STATUS, SalaryPaymentSlipFields.PAY_STATUS_Options_4);
            salaryPaymentSlipObj = salaryPaymentSlipDao.updateSalaryPaymentSlip(systemUser, salaryPaymentSlipObj);

            log.info("工资发放单发放成功，ID: {}, 关联工资条数量: {}, 薪资明细总数: {}",
                    salaryPaymentSlipObj.getId(), salaryDataList.size(), totalDetailCount);

            return salaryPaymentSlipObj;
        } catch (Exception e) {
            // 发生异常时，更新工资发放单状态为发放异常
            salaryPaymentSlipObj.set(SalaryPaymentSlipFields.PAY_STATUS, SalaryPaymentSlipFields.PAY_STATUS_Options_3);
            salaryPaymentSlipObj = salaryPaymentSlipDao.updateSalaryPaymentSlip(systemUser, salaryPaymentSlipObj);

            log.error("发放工资失败，工资发放单ID: {}", salaryPaymentSlipObj.getId(), e);
            throw new RuntimeException("发放工资失败: " + e.getMessage(), e); //ignoreI18n
        }
    }



    @Override
    public List<IObjectData> getSalaryDataByEmployeeId(String tenantId, String employeeId) {
        log.info("根据员工ID查询工资条，tenantId: {}, employeeId: {}", tenantId, employeeId);

        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeId)) {
            log.warn("查询工资条参数无效，tenantId: {}, employeeId: {}", tenantId, employeeId);
            return Lists.newArrayList();
        }

        try {
            // 使用SalaryDataDao查询员工的工资条
            List<IObjectData> salaryDataList = salaryDataDao.getByEmployeeId(tenantId, employeeId);
            log.info("查询到员工工资条数量: {}, employeeId: {}", salaryDataList.size(), employeeId);
            return salaryDataList;
        } catch (Exception e) {
            log.error("查询员工工资条失败，employeeId: {}", employeeId, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<IObjectData> getSalaryDataByEmployeeExternalId(String tenantId, String employeeExternalId) {
        log.info("根据外部员工ID查询工资条，tenantId: {}, employeeExternalId: {}", tenantId, employeeExternalId);

        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeExternalId)) {
            log.warn("查询工资条参数无效，tenantId: {}, employeeExternalId: {}", tenantId, employeeExternalId);
            return Lists.newArrayList();
        }

        try {
            // 使用SalaryDataDao查询外部员工的工资条
            List<IObjectData> salaryDataList = salaryDataDao.getByEmployeeExternalId(tenantId, employeeExternalId);
            log.info("查询到外部员工工资条数量: {}, employeeExternalId: {}", salaryDataList.size(), employeeExternalId);
            return salaryDataList;
        } catch (Exception e) {
            log.error("查询外部员工工资条失败，employeeExternalId: {}", employeeExternalId, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public int sendSalaryDataMessages(String tenantId, List<IObjectData> salaryDataList) {
        log.info("批量发送工资条消息，tenantId: {}, 工资条数量: {}", tenantId, salaryDataList.size());

        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(salaryDataList)) {
            log.warn("批量发送工资条消息参数无效，tenantId: {}, salaryDataList为空", tenantId);
            return 0;
        }

        int successCount = 0;

        // 分组处理内部员工和外部员工的工资条
        Map<Boolean, List<IObjectData>> groupedSalaryData = salaryDataList.stream()
                .collect(Collectors.partitioningBy(data -> {
                    String employeeId = data.get(SalaryDataFields.EMPLOYEE, String.class);
                    return StringUtils.isNotBlank(employeeId);
                }));

        // 处理内部员工的工资条（使用文本卡片消息）
        List<IObjectData> internalEmployeeSalaryData = groupedSalaryData.get(true);
        if (!CollectionUtils.isEmpty(internalEmployeeSalaryData)) {
            successCount += salaryMessageService.batchSendInternalEmployeeMessages(tenantId,
                    internalEmployeeSalaryData);
        }

        // 处理外部员工的工资条（使用通知公告方式）
        List<IObjectData> externalEmployeeSalaryData = groupedSalaryData.get(false);
        if (!CollectionUtils.isEmpty(externalEmployeeSalaryData)) {
            successCount += salaryMessageService.batchSendExternalEmployeeMessages(tenantId,
                    externalEmployeeSalaryData);
        }

        log.info("批量发送工资条消息完成，总数: {}, 成功数: {}", salaryDataList.size(), successCount);
        return successCount;
    }

    @Override
    public boolean sendSalaryDataMessage(String tenantId, String employeeId, String salaryDataId) {
        log.info("发送工资条消息给员工，tenantId: {}, employeeId: {}, salaryDataId: {}", tenantId, employeeId, salaryDataId);

        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeId) || StringUtils.isBlank(salaryDataId)) {
            log.warn("发送工资条消息参数无效，tenantId: {}, employeeId: {}, salaryDataId: {}", tenantId, employeeId, salaryDataId);
            return false;
        }

        try {
            // 1. 获取工资条数据
            IObjectData salaryData = salaryDataDao.getById(tenantId, salaryDataId);
            if (salaryData == null) {
                log.warn("未找到工资条数据，salaryDataId: {}", salaryDataId);
                return false;
            }

            // 2. 验证工资条是否属于该员工
            String dataEmployeeId = salaryData.get(SalaryDataFields.EMPLOYEE, String.class);
            String dataEmployeeExternalId = salaryData.get(SalaryDataFields.EMPLOYEE_EXTERNAL, String.class);

            if (!employeeId.equals(dataEmployeeId) && !employeeId.equals(dataEmployeeExternalId)) {
                log.warn("工资条不属于该员工，employeeId: {}, dataEmployeeId: {}, dataEmployeeExternalId: {}",
                        employeeId, dataEmployeeId, dataEmployeeExternalId);
                return false;
            }

            // 3. 根据员工类型选择不同的发送方式
            boolean isInternalEmployee = StringUtils.isNotBlank(dataEmployeeId);

            if (isInternalEmployee) {
                // 内部用户：使用文本卡片消息
                return salaryMessageService.sendInternalEmployeeMessage(tenantId, dataEmployeeId, salaryData);
            } else {
                // 外部用户：使用通知公告方式
                return salaryMessageService.sendExternalEmployeeMessage(tenantId, dataEmployeeExternalId, salaryData);
            }
        } catch (Exception e) {
            log.error("发送工资条消息失败，employeeId: {}, salaryDataId: {}", employeeId, salaryDataId, e);
            return false;
        }
    }

    @Override
    public List<String> getSalaryTableHeaders(IObjectData salaryPaymentSlipObj) {
        List<String> headers = Lists.newArrayList();

        try {
            // 获取薪资规则对象
            if (salaryPaymentSlipObj == null) {
                log.warn("工资发放单对象不存在");
                return headers;
            }

            String headerStr = salaryPaymentSlipObj.get(SalaryPaymentSlipFields.SALARY_STATEMENT_HEADER, String.class);
            if (StringUtils.isBlank(headerStr)) {
                log.warn("薪资规则中未配置工资表表头，salaryPaymentSlipId: {}", salaryPaymentSlipObj.getId());
                return headers;
            }

            // 将字符串转换为数组
            List<String> headerList = JSON.parseArray(headerStr, String.class);
            if (CollectionUtils.isEmpty(headerList)) {
                log.warn("薪资规则中工资表表头格式无效，salaryPaymentSlipId: {}", salaryPaymentSlipObj.getId());
                return headers;
            }

            log.info("获取工资表表头成功，表头数量: {}", headerList.size());
            return headerList;
        } catch (Exception e) {
            log.error("获取工资表表头失败，salaryRuleId: {}", salaryPaymentSlipObj != null ? salaryPaymentSlipObj.getId() : null,
                    e);
            return headers;
        }
    }

    /**
     * 处理工资表表头描述
     * headers key si_xxxxxx 格式为工资项
     * headers key kpi_xxxxxx 格式为kpi指标
     * 转成注释描述格式 然后返回
     * name 取 数据的name
     * 类型是 引用
     * // "si_xxxxxx"
     * // "kpi_xxxxxx":{
     * // "describe_api_name":"SalaryDetailDataObj",
     * // "is_index":false,
     * // "is_active":true,
     * // "is_encrypted":false,
     * // "auto_adapt_places":false,
     * // "quote_field_type":"text",
     * // "remove_mask_roles":{},
     * // "is_unique":false,
     * // "label":"name",
     * // "type":"quote",
     * // "is_required":false,
     * // "api_name":"value_type",
     * // "define_type":"package",
     * // "is_single":false,
     * // "is_show_mask":false,
     * // "help_text":"",
     * // "status":"new"
     * // }
     * 
     * @param tenantId 租户ID
     * @param headers  表头列表
     * @return 处理后的表头字段描述映射
     */
    @Override
    public Map<String, IFieldDescribe> processSalaryTableHeaderDescriptions(String tenantId, List<String> headers) {
        log.info("处理工资表表头描述，tenantId: {}, headers数量: {}", tenantId, headers != null ? headers.size() : 0);

        Map<String, IFieldDescribe> result = new HashMap<>();

        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(headers)) {
            log.warn("处理工资表表头描述参数无效，tenantId: {}, headers为空", tenantId);
            return result;
        }

        try {
            // 解析表头，收集需要查询的ID
            HeaderParseResult parseResult = parseHeaders(headers);

            // 批量查询数据
            Map<String, IObjectData> salaryItemMap = buildSalaryItemCache(tenantId, parseResult.salaryItemIds);
            Map<String, IObjectData> kpiMap = buildKpiCache(tenantId, parseResult.kpiIds);

            // 为每个表头创建字段描述
            for (String header : headers) {
                IFieldDescribe fieldDescribe = createFieldDescribe(header, salaryItemMap, kpiMap);
                if (fieldDescribe != null) {
                    result.put(header, fieldDescribe);
                }
            }

            log.info("处理工资表表头描述成功，结果数量: {}", result.size());
            return result;
        } catch (Exception e) {
            log.error("处理工资表表头描述失败", e);
            return result;
        }
    }

    /**
     * 转换工资表场景表头顺序
     * 基础数据结构
     * {
     * "field_name": "name",
     * "is_show": true
     * },
     * {
     * "field_name": "owner",
     * "is_show": true
     * },
     * 1.构造新的 List<Map<String, Object>> fieldsList
     * 2.遍历headers，将headers中的字段添加到fieldsList中
     * 3.遍历sourceFieldList，将不在fieldsList中的字段添加到fieldsList中
     *
     * @param tenantId        租户ID
     * @param headers         表头列表
     * @param sourceFieldList 原始字段列表
     * @return 处理后的字段列表
     */
    @Override
    public List<Map<String, Object>> convertSalaryTableHeaderOrder(String tenantId, List<String> headers,
            List<Map<String, Object>> sourceFieldList) {
        log.info("转换工资表场景表头顺序，tenantId: {}, headers数量: {}, sourceFieldList数量: {}",
                tenantId, headers != null ? headers.size() : 0, sourceFieldList != null ? sourceFieldList.size() : 0);

        // 构造新的字段列表
        List<Map<String, Object>> fieldsList = new ArrayList<>();

        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(headers)) {
            log.warn("转换工资表场景表头顺序参数无效，tenantId: {}, headers为空", tenantId);
            return sourceFieldList; // 如果headers为空，直接返回原始字段列表
        }

        if (CollectionUtils.isEmpty(sourceFieldList)) {
            log.warn("原始字段列表为空，tenantId: {}", tenantId);
            return fieldsList; // 如果原始字段列表为空，返回空列表
        }

        try {
            // 创建一个映射，用于快速查找sourceFieldList中的字段
            Map<String, Map<String, Object>> sourceFieldMap = new HashMap<>();
            for (Map<String, Object> sourceField : sourceFieldList) {
                String fieldName = (String) sourceField.get("field_name");
                if (StringUtils.isNotBlank(fieldName)) {
                    sourceFieldMap.put(fieldName, sourceField);
                }
            }

            // 创建一个集合来存储已处理的字段名
            Set<String> processedFields = new HashSet<>();

            // 1. 先处理headers中的字段，按headers的顺序添加到结果中
            for (String header : headers) {
                if (StringUtils.isBlank(header)) {
                    continue;
                }

                // 检查sourceFieldList中是否有对应的字段
                if (sourceFieldMap.containsKey(header)) {
                    // 使用sourceFieldList中的字段
                    fieldsList.add(sourceFieldMap.get(header));
                } else {
                    // 创建新的字段
                    Map<String, Object> newField = new HashMap<>();
                    newField.put("field_name", header);
                    newField.put("is_show", true);
                    fieldsList.add(newField);
                }

                // 标记为已处理
                processedFields.add(header);
            }

//             2. 将sourceFieldList中未处理的字段追加到结果的最后
            for (Map<String, Object> sourceField : sourceFieldList) {
                String fieldName = (String) sourceField.get("field_name");
                if (StringUtils.isNotBlank(fieldName) && !processedFields.contains(fieldName)) {
                    fieldsList.add(sourceField);
                    processedFields.add(fieldName);
                }
            }

            log.info("转换工资表场景表头顺序成功，结果数量: {}", fieldsList.size());
            return fieldsList;
        } catch (Exception e) {
            log.error("转换工资表场景表头顺序失败", e);
            return sourceFieldList; // 发生异常时返回原始字段列表
        }
    }

    /**
     * 根据日期获取应该生成工资发放单的工资规则
     * 
     * @param tenantId 租户ID
     * @param date     参考日期
     * @return 符合条件的工资规则列表
     */
    @Override
    public List<IObjectData> getSalaryRulesByDate(String tenantId, Date date) {
        if (date == null) {
            date = new Date();
        }

        // 直接调用SalaryRuleDao中的方法
        List<IObjectData> rules = salaryRuleDao.queryRulesByDate(tenantId, date);

        return rules;
    }

    /**
     * 根据日期获取应该生成工资条明细的规则
     * 1.日薪规则 月发
     * 2.周薪规则 月发
     * 3.日薪规则 周发
     * 
     * @param tenantId 租户ID
     * @param date     参考日期
     * @return 符合条件的工资规则列表
     */
    @Override
    public List<IObjectData> getSalaryRulesByDateForDetail(String tenantId, Date date) {
        if (date == null) {
            date = new Date();
        }

        // 调用SalaryRuleDao中的方法
        List<IObjectData> rules = salaryRuleDao.queryRulesByDateForDetail(tenantId, date);
        return rules;
    }

    @Override
    public EmployeeSalaryTypeQuery.Result getEmployeeSalaryType(String employeeId, String month, String salaryDataId,
            ServiceContext context) {
        EmployeeSalaryTypeQuery.Result result = new EmployeeSalaryTypeQuery.Result();

        try {
            IObjectData employeeFixedSalary = null;
            IObjectData salaryRule = null;
            String actualMonth = month;
            LocalDate currentEndDate = null;
            String currentEndDateStr = null;
            // 如果传入了工资条ID，优先从工资条获取规则信息
            if (StringUtils.isNotBlank(salaryDataId)) {
                log.info("从工资条获取薪资规则信息, salaryDataId: {}", salaryDataId);

                // 1. 根据工资条ID获取工资条信息
                IObjectData salaryData = salaryDataDao.getById(context.getTenantId(), salaryDataId);
                if (salaryData == null) {
                    throw new ValidateException("工资条不存在");//ignoreI18n
                }

                //工资条和当前人不一致 直接报错
                if (!employeeId.equals(salaryData.get(SalaryDataFields.EMPLOYEE, String.class))
                        && !employeeId.equals(salaryData.get(SalaryDataFields.EMPLOYEE_EXTERNAL, String.class))) {
                    throw new ValidateException("工资条不属于当前员工");//ignoreI18n
                }
                Long endDate = salaryData.get(SalaryDataFields.END_DATE, Long.class);
                currentEndDate = Instant.ofEpochMilli(endDate)
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                currentEndDateStr = currentEndDate
                        .format(DateTimeFormatter.ISO_LOCAL_DATE);
                // 2. 从工资条获取员工固定薪资ID
                String employeeFixedSalaryId = salaryData.get(SalaryDataFields.EMPLOYEE_FIXED_SALARY, String.class);
                if (StringUtils.isNotBlank(employeeFixedSalaryId)) {
                    employeeFixedSalary = employeeFixedSalaryDao.getById(context.getTenantId(),
                            EmployeeFixedSalaryFields.API_NAME, employeeFixedSalaryId);
                }

                // 3. 从工资条获取薪资规则ID
                String salaryRuleId = salaryData.get(SalaryDataFields.SALARY_RULE, String.class);
                if (StringUtils.isNotBlank(salaryRuleId)) {
                    salaryRule = salaryRuleDao.getById(context.getTenantId(), salaryRuleId);
                }

                if (salaryRule == null) {
                    throw new ValidateException("薪资规则不存在"); //ignoreI18n
                }
                // 4. 通过工资条周期计算开始时间，然后确定月份
                if (StringUtils.isBlank(actualMonth)) {
                    // 获取工资规则的发放周期和定薪方式
                    String distributionCycleForMonth = salaryRule.get(SalaryRuleFields.DISTRIBUTION_CYCLE, String.class);
                    String salaryMethodForMonth = salaryRule.get(SalaryRuleFields.SALARY_METHOD, String.class);
                    LocalDate cycleStartDate = calculateCycleStartDate(currentEndDate, distributionCycleForMonth, salaryMethodForMonth);
                    // 使用调整后的开始时间确定月份
                    actualMonth = cycleStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                    log.info("通过工资条周期计算确定月份: {}, 周期开始时间: {}", actualMonth, cycleStartDate);
                }
            } else {
                log.info("从员工固定薪资获取薪资规则信息, employeeId: {}", employeeId);

                // 1. 查询员工的薪资规则信息
                employeeFixedSalary = employeeFixedSalaryDao.getByEmployeeId(context.getTenantId(), employeeId);
                if (employeeFixedSalary == null) {
                    throw new ValidateException("员工没有固定薪资信息"); //ignoreI18n
                }

                // 2. 获取薪资规则ID
                String salaryRuleId = employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_RULE, String.class);
                if (StringUtils.isBlank(salaryRuleId)) {
                    throw new ValidateException("员工没有关联薪资规则"); //ignoreI18n
                }

                // 3. 查询薪资规则详情
                salaryRule = salaryRuleDao.getById(context.getTenantId(), salaryRuleId);
                if (salaryRule == null) {
                    throw new ValidateException("薪资规则不存在"); //ignoreI18n
                }

                // 4. 如果没有传入月份参数，需要根据工资条周期计算确定月份
                if (StringUtils.isBlank(actualMonth)) {
                    // 获取工资规则的发放周期和定薪方式
                    String distributionCycleForMonth = salaryRule.get(SalaryRuleFields.DISTRIBUTION_CYCLE, String.class);
                    String salaryMethodForMonth = salaryRule.get(SalaryRuleFields.SALARY_METHOD, String.class);
                    LocalDate cycleStartDate = calculateCycleStartDate(currentEndDate, distributionCycleForMonth, salaryMethodForMonth);
                    // 使用调整后的开始时间确定月份
                    actualMonth = cycleStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                    log.info("通过工资条周期计算确定月份: {}, 周期开始时间: {}", actualMonth, cycleStartDate);
                }
            }

            // 检查必要的对象是否获取成功
            if (employeeFixedSalary == null) {
                log.warn("无法获取员工固定薪资信息");
                return result;
            }

            if (salaryRule == null) {
                log.warn("无法获取薪资规则信息");
                return result;
            }

            // 4. 设置薪资类型和发放周期信息
            String salaryMethod = salaryRule.get(SalaryRuleFields.SALARY_METHOD, String.class);
            String distributionCycle = salaryRule.get(SalaryRuleFields.DISTRIBUTION_CYCLE, String.class);

            result.setSalaryMethod(salaryMethod);
            result.setSalaryMethodDesc(getSalaryMethodDesc(salaryMethod));
            result.setDistributionCycle(distributionCycle);
            result.setDistributionCycleDesc(getDistributionCycleDesc(distributionCycle));
            result.setSalaryRuleId(salaryRule.getId());
            result.setSalaryRuleName(salaryRule.getName());

            // 设置月份信息
            result.setMonth(actualMonth);

            // 5. 查询员工的工资条数据

            // 计算查询时间范围（指定月份的第一天到最后一天）
            YearMonth yearMonth;
            if (StringUtils.isNotBlank(actualMonth)) {
                try {
                    // 解析指定的月份，格式：yyyy-MM
                    yearMonth = YearMonth.parse(actualMonth, DateTimeFormatter.ofPattern("yyyy-MM"));
                } catch (Exception e) {
                    log.warn("解析月份参数失败, actualMonth: {}, 使用当前月份", actualMonth, e);
                    yearMonth = YearMonth.now();
                }
            } else {
                yearMonth = YearMonth.now();
            }

            // 获取月份的第一天和最后一天
            LocalDate firstDayOfMonth = yearMonth.atDay(1);
            LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

            // 转换为毫秒时间戳
            long startTimeMillis = firstDayOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
            long endTimeMillis = lastDayOfMonth.with(DayOfWeek.SUNDAY).atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant()
                    .toEpochMilli();

            log.info("查询工资条时间范围: {} 至 {}", firstDayOfMonth, lastDayOfMonth);
            // 查询员工的工资条数据 - 只查询已发放状态且薪资规则相同的工资条
            List<IObjectData> salaryDataList = salaryDataDao.getDistributedSalaryDataBySalaryRule(
                    context.getTenantId(), employeeFixedSalary.getId(), startTimeMillis, endTimeMillis, salaryRule.getId());
            String ruleEffectiveDate = salaryRule.get(SalaryRuleFields.EFFECTIVE_DATE, String.class);
            LocalDate effectiveDate = Instant.ofEpochMilli(Long.parseLong(ruleEffectiveDate))
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            // 6. 生成可查询的薪资周期列表
            List<EmployeeSalaryTypeQuery.SalaryPeriod> periods = generateAvailablePeriods(
                    context.getTenantId(),
                    employeeId,
                    salaryMethod,
                    distributionCycle,
                    salaryDataList,
                    actualMonth, salaryDataId, effectiveDate);
            result.setAvailablePeriods(periods);

        } catch (Exception e) {
            log.error("获取员工薪资类型和发放周期信息异常, employeeId: {}", employeeId, e);
        }

        return result;
    }

    /**
     * 获取定薪方式描述
     */
    private String getSalaryMethodDesc(String salaryMethod) {
        if ("1".equals(salaryMethod)) {
            return "日薪"; //ignoreI18n
        } else if ("2".equals(salaryMethod)) {
            return "周薪"; //ignoreI18n
        } else if ("3".equals(salaryMethod)) {
            return "月薪"; //ignoreI18n
        }
        return "";
    }

    /**
     * 获取发放周期描述
     */
    private String getDistributionCycleDesc(String distributionCycle) {
        if ("1".equals(distributionCycle)) {
            return "按日发放"; //ignoreI18n
        } else if ("2".equals(distributionCycle)) {
            return "按周发放"; //ignoreI18n
        } else if ("3".equals(distributionCycle)) {
            return "按月发放"; //ignoreI18n
        }
        return "";
    }

    /**
     * 生成可查询的薪资周期列表
     *
     * @param tenantId          租户ID
     * @param employeeId        员工ID
     * @param salaryMethod      定薪方式：1-日薪，2-周薪，3-月薪
     * @param distributionCycle 发放周期：1-按日发放，2-按周发放，3-按月发放
     * @param salaryDataList    员工工资条数据列表
     * @param month             指定月份，格式：yyyy-MM，如果为空则使用当前月份
     * @param currentSalaryDataId 当前工资条ID，用于标识当前周期
     * @param effectiveDate 规则生效日期
     * @return 可查询的薪资周期列表
     */
    private List<EmployeeSalaryTypeQuery.SalaryPeriod> generateAvailablePeriods(
            String tenantId,
            String employeeId,
            String salaryMethod,
            String distributionCycle,
            List<IObjectData> salaryDataList,
            String month, String currentSalaryDataId, LocalDate effectiveDate) {
        List<EmployeeSalaryTypeQuery.SalaryPeriod> periods = new ArrayList<>();

        // 创建工资条映射，用于快速查找特定时间段的工资条 key 是 periodId
        Map<String, IObjectData> salaryDataMap = new HashMap<>();
        if (salaryDataList != null && !salaryDataList.isEmpty()) {
            for (IObjectData salaryData : salaryDataList) {
                Long startDate = salaryData.get(SalaryDataFields.START_DATE, Long.class);
                Long endDate = salaryData.get(SalaryDataFields.END_DATE, Long.class);

                if (startDate != null && endDate != null) {
                    LocalDate start = Instant.ofEpochMilli(startDate).atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate end = Instant.ofEpochMilli(endDate).atZone(ZoneId.systemDefault()).toLocalDate();
                    // 生成工资条的 periodId，考虑生效时间
                    String salaryDataPeriodId = generatePeriodId(start, end, effectiveDate);
                    salaryDataMap.put(salaryDataPeriodId, salaryData);
                }
            }
        }

        // 确定生成周期的月份
        YearMonth yearMonth;
        if (StringUtils.isNotBlank(month)) {
            try {
                // 解析指定的月份，格式：yyyy-MM
                yearMonth = YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyy-MM"));
            } catch (Exception e) {
                log.warn("解析月份参数失败, month: {}, 使用当前月份", month);
                yearMonth = YearMonth.now();
            }
        } else {
            yearMonth = YearMonth.now();
        }

        // 获取当前日期，用于判断是否是当前周期
        LocalDate today = LocalDate.now();
        boolean isCurrentMonth = today.getYear() == yearMonth.getYear()
                && today.getMonthValue() == yearMonth.getMonthValue();

        // 根据周期生成不同的周期数据
        if ("3".equals(distributionCycle)) {
            // 月薪 - 只生成一个月周期
            EmployeeSalaryTypeQuery.SalaryPeriod period = createMonthPeriod(yearMonth.atDay(1), isCurrentMonth, effectiveDate);

            // 查找对应的工资条数据，使用 periodId 作为 key
            IObjectData salaryData = salaryDataMap.get(period.getPeriodId());

            // 设置工资条ID和发放状态
            if (salaryData != null) {
                period.setSalaryDataId(salaryData.getId());
                // 通过工资条ID判断是否是当前周期
                if (StringUtils.isNotBlank(currentSalaryDataId) && currentSalaryDataId.equals(salaryData.getId())) {
                    period.setIsCurrent(true);
                }
                // String distributionStatus = salaryData.get(SalaryDataFields.DISTRIBUTION_STATUS, String.class);
                period.setDistributionStatus("0");
            } else {
                period.setSalaryDataId(null);
                period.setDistributionStatus("-2"); // 没有数据
            }

            periods.add(period);
        } else if ("2".equals(distributionCycle)) {
            // 周薪 - 从月份第一个周一开始到最后一个周一
            LocalDate firstDayOfMonth = yearMonth.atDay(1);
            LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

            // 找到月份第一个周一
            LocalDate firstMonday = firstDayOfMonth;
            while (firstMonday.getDayOfWeek() != DayOfWeek.MONDAY
                    && firstMonday.getMonthValue() == yearMonth.getMonthValue()) {
                firstMonday = firstMonday.plusDays(1);

            }

            // 生成周周期
            LocalDate weekStart = firstMonday;
            int weekIndex = 1;

            while (!weekStart.isAfter(lastDayOfMonth)) {
                EmployeeSalaryTypeQuery.SalaryPeriod period = new EmployeeSalaryTypeQuery.SalaryPeriod();

                // 设置开始日期和结束日期
                LocalDate weekEnd = weekStart.plusDays(6);

                // 设置周期标题，格式：M.d-M.d，跨年时结束日期带年份
                String startFormat = weekStart.format(DateTimeFormatter.ofPattern("M.d"));
                String endFormat;
                if (weekStart.getYear() != weekEnd.getYear()) {
                    // 跨年情况，结束日期带年份
                    endFormat = weekEnd.format(DateTimeFormatter.ofPattern("yyyy.M.d"));
                } else {
                    // 同年情况，只显示月日
                    endFormat = weekEnd.format(DateTimeFormatter.ofPattern("M.d"));
                }
                String periodTitle = startFormat + "-" + endFormat; //ignoreI18n
                period.setPeriodTitle(periodTitle);

                period.setStartDate(weekStart.format(DateTimeFormatter.ISO_LOCAL_DATE));
                period.setEndDate(weekEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));

                // 使用新的方法生成周期ID，考虑生效时间
                String periodId = generatePeriodId(weekStart, weekEnd, effectiveDate);
                period.setPeriodId(periodId);

                // 查找对应的工资条数据，使用 periodId 作为 key
                IObjectData salaryData = salaryDataMap.get(period.getPeriodId());

                // 设置工资条ID和发放状态
                if (salaryData != null) {
                    period.setSalaryDataId(salaryData.getId());
                    // 通过工资条ID判断是否是当前周期
                    if (StringUtils.isNotBlank(currentSalaryDataId) && currentSalaryDataId.equals(salaryData.getId())) {
                        period.setIsCurrent(true);
                    }
                    // String distributionStatus = salaryData.get(SalaryDataFields.DISTRIBUTION_STATUS, String.class);
                    period.setDistributionStatus("0");
                } else {
                    period.setSalaryDataId(null);
                    period.setDistributionStatus("-2"); // 没有数据
                }

                periods.add(period);

                // 移动到下一周
                weekStart = weekStart.plusDays(7);
                weekIndex++;

                // 如果下一周的周一已经超出了当月，则结束循环
                if (weekStart.getMonthValue() != yearMonth.getMonthValue()) {
                    break;
                }
            }
        } else {
            // 日薪 - 从1号到月末每天一个周期
            LocalDate firstDayOfMonth = yearMonth.atDay(1);
            LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

            // 生成日周期
            LocalDate currentDay = firstDayOfMonth;

            while (!currentDay.isAfter(lastDayOfMonth)) {
                EmployeeSalaryTypeQuery.SalaryPeriod period = new EmployeeSalaryTypeQuery.SalaryPeriod();

                // 设置周期标题，格式：yyyy年MM月dd日
                String periodTitle = currentDay.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")); //ignoreI18n
                period.setPeriodTitle(periodTitle);

                // 设置开始日期和结束日期（日周期开始和结束日期相同）
                period.setStartDate(currentDay.format(DateTimeFormatter.ISO_LOCAL_DATE));
                period.setEndDate(currentDay.format(DateTimeFormatter.ISO_LOCAL_DATE));

                // 使用新的方法生成周期ID，考虑生效时间
                String periodId = generatePeriodId(currentDay, currentDay, effectiveDate);
                period.setPeriodId(periodId);

                // 查找对应的工资条数据，使用 periodId 作为 key
                IObjectData salaryData = salaryDataMap.get(period.getPeriodId());

                // 设置工资条ID和发放状态
                if (salaryData != null) {
                    period.setSalaryDataId(salaryData.getId());
                    // 通过工资条ID判断是否是当前周期
                    if (StringUtils.isNotBlank(currentSalaryDataId) && currentSalaryDataId.equals(salaryData.getId())) {
                        period.setIsCurrent(true);
                    }
                    // String distributionStatus = salaryData.get(SalaryDataFields.DISTRIBUTION_STATUS, String.class);
                    period.setDistributionStatus("0");
                } else {
                    period.setSalaryDataId(null);
                    period.setDistributionStatus("-2"); // 没有数据
                }

                periods.add(period);

                // 移动到下一天
                currentDay = currentDay.plusDays(1);
            }
        }

        return periods;
    }

    /**
     * 创建月周期
     */
    private EmployeeSalaryTypeQuery.SalaryPeriod createMonthPeriod(LocalDate date, boolean isCurrent, LocalDate effectiveDate) {
        EmployeeSalaryTypeQuery.SalaryPeriod period = new EmployeeSalaryTypeQuery.SalaryPeriod();

        // 设置周期标题，格式：yyyy年MM月
        String periodTitle = date.format(DateTimeFormatter.ofPattern("yyyy年MM月")); //ignoreI18n
        period.setPeriodTitle(periodTitle);

        // 设置开始日期和结束日期
        LocalDate firstDayOfMonth = date.withDayOfMonth(1);
        LocalDate lastDayOfMonth = date.withDayOfMonth(date.lengthOfMonth());

        period.setStartDate(firstDayOfMonth.format(DateTimeFormatter.ISO_LOCAL_DATE));
        period.setEndDate(lastDayOfMonth.format(DateTimeFormatter.ISO_LOCAL_DATE));

        // 使用新的方法生成周期ID，考虑生效时间
        String periodId = generatePeriodId(firstDayOfMonth, lastDayOfMonth, effectiveDate);
        period.setPeriodId(periodId);

        return period;
    }

    @Override
    public List<IObjectData> getApplicableSalaryRuleIdsByEmployeeFixedSalary(String tenantId,
            IObjectData employeeFixedSalaryObj) {
        if (employeeFixedSalaryObj == null) {
            log.warn("员工固定工资表对象为空");
            return Lists.newArrayList();
        }

        try {
            ObjectDataExt employeeFixedSalaryExt = ObjectDataExt.of(employeeFixedSalaryObj);

            // 获取员工信息
            String employeeId = employeeFixedSalaryExt.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE);
            String employeeExternalId = employeeFixedSalaryExt
                    .getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);
            String outTeantId = employeeFixedSalaryExt.getStringValue(EmployeeFixedSalaryFields.CONNECTED_COMPANY);
            String connectedRole = employeeFixedSalaryExt.getStringValue(EmployeeFixedSalaryFields.CONNECTED_ROLE);

            boolean isExternal = StringUtils.isBlank(employeeId);
            String actualEmployeeId = isExternal ? employeeExternalId : employeeId;

            // 如果是外部员工且没有企业ID，尝试从PublicEmployeeObj中获取
            if (isExternal && StringUtils.isBlank(outTeantId) && StringUtils.isNotBlank(actualEmployeeId)) {
                IObjectData publicEmployeeObj = employeeDao.getExternalEmployeeById(tenantId, actualEmployeeId);
                if (publicEmployeeObj != null) {
                    Object outerTenantId = publicEmployeeObj.get(PublicEmployeeFields.OUTER_TENANT_ID);
                    if (outerTenantId != null) {
                        outTeantId = outerTenantId.toString();
                    }
                }
            }

            if (StringUtils.isBlank(actualEmployeeId)) {
                log.warn("员工ID和外部员工ID都为空");
                return Lists.newArrayList();
            }

            // 获取员工的所有部门信息
            List<String> departmentIds = employeeDao.getEmployeeDepartments(tenantId, actualEmployeeId, isExternal);

            // 获取员工的所有角色信息
            List<String> roleIds = employeeDao.getEmployeeRoles(tenantId, actualEmployeeId, outTeantId, isExternal);

            // 获取定薪方式
            String salaryMethod = employeeFixedSalaryExt.getStringValue(EmployeeFixedSalaryFields.SALARY_METHOD);

            // 调用queryApplicableSalaryRules方法获取匹配的规则
            List<IObjectData> applicableRules = queryApplicableSalaryRules(
                    tenantId, actualEmployeeId, departmentIds, outTeantId, roleIds, isExternal, salaryMethod);

            if (CollectionUtils.isNotEmpty(applicableRules)) {
                // 提取规则ID列表
//                List<String> ruleIds = applicableRules.stream()
//                        .map(IObjectData::getId)
//                        .collect(Collectors.toList());

                log.info("为员工 {} 找到 {} 个适用的薪资规则", actualEmployeeId, applicableRules.size());
                return applicableRules;
            } else {
                log.info("为员工 {} 未找到适用的薪资规则", actualEmployeeId);
                return Lists.newArrayList();
            }
        } catch (Exception e) {
            log.error("获取适用的薪资规则ID时发生异常", e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<IObjectData> getApplicableSalaryRulesForEmployee(String tenantId, String employeeId, boolean isExternal) {
        {
            try {
                // 获取员工的部门和角色信息
                List<String> departmentIds = employeeDao.getEmployeeDepartments(tenantId, employeeId, isExternal);
                List<String> roleIds = employeeDao.getEmployeeRoles(tenantId, employeeId, null, isExternal);

                // 查询适用的工资规则
                List<IObjectData> applicableRules = queryApplicableSalaryRules(
                        tenantId, employeeId, departmentIds, null, roleIds, isExternal, null);

                return applicableRules;
            } catch (Exception e) {
                log.error("获取员工适用工资规则失败，employeeId: {}", employeeId, e);
                return Lists.newArrayList();
            }
        }
    }

//    /**
//     * 创建周周期列表
//     */
//    private List<EmployeeSalaryTypeQuery.SalaryPeriod> createWeekPeriods(LocalDate date, boolean isCurrentMonth) {
//        List<EmployeeSalaryTypeQuery.SalaryPeriod> periods = new ArrayList<>();
//
//        // 获取当月第一天和最后一天
//        LocalDate firstDayOfMonth = date.withDayOfMonth(1);
//        LocalDate lastDayOfMonth = date.withDayOfMonth(date.lengthOfMonth());
//
//        // 获取当月第一天所在的周的周一
//        LocalDate firstMonday = firstDayOfMonth;
//        while (firstMonday.getDayOfWeek() != DayOfWeek.MONDAY) {
//            firstMonday = firstMonday.minusDays(1);
//        }
//
//        // 生成周周期
//        LocalDate weekStart = firstMonday;
//        int weekIndex = 1;
//
//        while (!weekStart.isAfter(lastDayOfMonth)) {
//            EmployeeSalaryTypeQuery.SalaryPeriod period = new EmployeeSalaryTypeQuery.SalaryPeriod();
//
//            // 设置周期ID，格式：W_yyyyMMww
//            String periodId = "W_" + date.format(DateTimeFormatter.ofPattern("yyyyMM"))
//                    + String.format("%02d", weekIndex);
//            period.setPeriodId(periodId);
//
//            // 设置周期标题，格式：yyyy年MM月第w周
//            String periodTitle = date.format(DateTimeFormatter.ofPattern("yyyy年MM月")) + "第" + weekIndex + "周";
//            period.setPeriodTitle(periodTitle);
//
//            // 设置开始日期和结束日期
//            LocalDate weekEnd = weekStart.plusDays(6);
//
//            period.setStartDate(weekStart.format(DateTimeFormatter.ISO_LOCAL_DATE));
//            period.setEndDate(weekEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));
//
//            // 设置是否是当前周期
//            LocalDate now = LocalDate.now();
//
//            periods.add(period);
//
//            // 移动到下一周
//            weekStart = weekStart.plusDays(7);
//            weekIndex++;
//        }
//
//        return periods;
//    }

    // /**
    //  * 创建日周期列表
    //  */
    // private List<EmployeeSalaryTypeQuery.SalaryPeriod> createDayPeriods(LocalDate date, boolean isCurrentMonth) {
    //     List<EmployeeSalaryTypeQuery.SalaryPeriod> periods = new ArrayList<>();

    //     // 获取当月第一天和最后一天
    //     LocalDate firstDayOfMonth = date.withDayOfMonth(1);
    //     LocalDate lastDayOfMonth = date.withDayOfMonth(date.lengthOfMonth());

    //     // 生成日周期
    //     LocalDate currentDay = firstDayOfMonth;

    //     while (!currentDay.isAfter(lastDayOfMonth)) {
    //         EmployeeSalaryTypeQuery.SalaryPeriod period = new EmployeeSalaryTypeQuery.SalaryPeriod();

    //         // 设置周期ID，格式：D_yyyyMMdd
    //         String periodId = "D_" + currentDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    //         period.setPeriodId(periodId);

    //         // 设置周期标题，格式：yyyy年MM月dd日
    //         String periodTitle = currentDay.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
    //         period.setPeriodTitle(periodTitle);

    //         // 设置开始日期和结束日期（日周期开始和结束日期相同）
    //         period.setStartDate(currentDay.format(DateTimeFormatter.ISO_LOCAL_DATE));
    //         period.setEndDate(currentDay.format(DateTimeFormatter.ISO_LOCAL_DATE));

    //         periods.add(period);

    //         // 移动到下一天
    //         currentDay = currentDay.plusDays(1);
    //     }

    //     return periods;
    // }

    /**
     * 根据工资条支付状态获取发放状态
     * 
     * @param payStatus 工资条支付状态
     * @return 发放状态：-2 没数据，-1 未发放，0 已发放，1 已结算
     */
    private String getDistributionStatusFromPayStatus(String payStatus) {
        if (StringUtils.isBlank(payStatus)) {
            return "-1"; // 未发放
        }

        // 根据实际的支付状态值映射到发放状态
        // 这里假设支付状态的值与发放状态的映射关系
        switch (payStatus) {
            case "0": // 假设0表示未发放
                return "-1";
            case "1": // 假设1表示已发放
                return "0";
            case "2": // 假设2表示已结算
                return "1";
            default:
                return "-1";
        }
    }

    @Override
    public SalaryPeriodTotalQuery.Result getSalaryPeriodTotal(
            String employeeId,
            String periodId,
            String startDate,
            String endDate,
            String salaryDataId,
            ServiceContext context) {
        SalaryPeriodTotalQuery.Result result = new SalaryPeriodTotalQuery.Result();

        try {
            // 1. 查询员工的薪资规则信息
            IObjectData employeeFixedSalary = employeeFixedSalaryDao.getByEmployeeId(context.getTenantId(), employeeId);
            if (employeeFixedSalary == null) {
                log.warn("员工没有固定薪资信息, employeeId: {}", employeeId);
                return result;
            }

            // // 2. 获取薪资规则ID
            // String salaryRuleId =
            // employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_RULE, String.class);
            // if (StringUtils.isBlank(salaryRuleId)) {
            // log.warn("员工没有关联薪资规则, employeeId: {}", employeeId);
            // return result;
            // }

            // // 3. 查询薪资规则详情
            // IObjectData salaryRule = salaryRuleDao.getById(context.getTenantId(),
            // salaryRuleId);
            // if (salaryRule == null) {
            // log.warn("薪资规则不存在, salaryRuleId: {}", salaryRuleId);
            // return result;
            // }

            // // 4. 设置薪资规则信息
            // String salaryMethod =
            // employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_METHOD,
            // String.class);
            // String distributionCycle =
            // salaryRule.get(SalaryRuleFields.DISTRIBUTION_CYCLE, String.class);

            // result.setSalaryMethod(salaryMethod);
            // result.setDistributionCycle(distributionCycle);
            // result.setSalaryRuleId(salaryRuleId);
            // result.setSalaryRuleName(salaryRule.getName());

            // 5. 获取工资条数据
            IObjectData salaryData = null;

            // 如果提供了工资条ID，直接查询
            if (StringUtils.isNotBlank(salaryDataId)) {
                salaryData = salaryDataDao.getById(context.getTenantId(), salaryDataId);
            }
            // 否则，根据周期ID和日期范围查询
            else if (StringUtils.isNotBlank(periodId)) {
                // 转换日期为时间戳
                long startTimeMillis = LocalDate.parse(startDate).atStartOfDay(ZoneId.systemDefault()).toInstant()
                        .toEpochMilli();
                long endTimeMillis = LocalDate.parse(endDate).atTime(23, 59, 59).atZone(ZoneId.systemDefault())
                        .toInstant().toEpochMilli();
                // 查询工资条
                List<IObjectData> salaryDataList = salaryDataDao.getByEmployeeFixedSalaryIdAndTimeEq(
                        context.getTenantId(), employeeFixedSalary.getId(), startTimeMillis, endTimeMillis);
                if (!salaryDataList.isEmpty()) {
                    salaryData = salaryDataList.get(0);
                }
            }

            // 6. 如果找到工资条，设置结果
            if (salaryData != null) {
                // 获取工资发放单
                IObjectData salaryPaymentSlipObj = salaryPaymentSlipDao.getById(context.getTenantId(),
                        salaryData.get(SalaryDataFields.SALARY_PAYMENT_SLIP, String.class));
                if (salaryPaymentSlipObj == null) {
                    log.warn("未找到工资发放单, salaryDataId: {}", salaryData.getId());
                    return result;
                }
                // 获取工资表头
                List<String> headers = getSalaryTableHeaders(salaryPaymentSlipObj);
                // 获取表头描述
                Map<String, IFieldDescribe> headerDescriptions = processSalaryTableHeaderDescriptions(
                        context.getTenantId(), headers);
                // 设置周期信息
                result.setPeriodId(periodId);
                result.setStartDate(startDate);
                result.setEndDate(endDate);

                // 设置总薪资金额
                String totalAmount = salaryData.get(SalaryDataFields.PAYABLE_SALARY, String.class);
                //去第三位小数的0
                result.setTotalAmount(SalaryAmountCalculator.formatAmountWithoutTrailingZeros(totalAmount));
                // 薪资明细值 getSalaryDataAggregatedValues
                Map<String, String> aggregatedValues = getSalaryDataAggregatedValues(context.getTenantId(),
                        salaryData.getId());

                // 使用公共方法处理薪资项目
                List<SalaryItem> salaryItems = processSalaryItems(
                    context.getTenantId(),
                    headers,
                    aggregatedValues,
                    headerDescriptions
                );

                result.setSalaryItems(salaryItems);
            } else {
                log.warn("未找到工资条数据, employeeId: {}, periodId: {}, startDate: {}, endDate: {}, salaryDataId: {}",
                        employeeId, periodId, startDate, endDate, salaryDataId);

                // 设置空的薪资项目列表
                result.setSalaryItems(new ArrayList<>());
            }

        } catch (Exception e) {
            log.error("获取薪资周期详情异常, employeeId: {}, periodId: {}", employeeId, periodId, e);
            result.setSalaryItems(new ArrayList<>());
        }

        return result;
    }

    @Override
    public Map<String, List<String>> buildExtHeaderMap(List<String> salaryTableHeaders) {
        log.debug("构建扩展表头映射, 表头数量: {}", salaryTableHeaders != null ? salaryTableHeaders.size() : 0);

        Map<String, List<String>> extHeaderMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(salaryTableHeaders)) {
            salaryTableHeaders.forEach(header -> {
                if (!header.startsWith("kpi_") && header.contains("kpi_")) {
                    // xxxkpi_xxxxxx 格式的表头
                    String kpiId = header.substring(header.indexOf("kpi_") + 4);
                    if (StringUtils.isNotBlank(kpiId)) {
                        extHeaderMap.computeIfAbsent(kpiId, k -> new ArrayList<>()).add(header);
                    }
                }
            });
        }

        log.debug("构建扩展表头映射完成, 映射数量: {}", extHeaderMap.size());
        return extHeaderMap;
    }

    @Override
    public Map<String, String> getSalaryDataAggregatedValues(String tenantId, String salaryDataId) {
        log.info("获取工资条的额外聚合数据, tenantId: {}, salaryDataId: {}", tenantId, salaryDataId);

        Map<String, String> aggregatedResult = new HashMap<>();

        try {
            // 根据工资条ID查询工资条明细
            List<IObjectData> salaryDetailDatas = salaryDetailDataDao.getBySalaryDataId(tenantId, salaryDataId);

            if (CollectionUtils.isNotEmpty(salaryDetailDatas)) {
                // 按周期结束时间去重KPI变量数据，保证同一个周期变量只计算一次
                Map<Long, Map<String, Object>> uniqueVariablesByEndTime = deduplicateVariablesByEndTime(salaryDetailDatas);

                // 用于存储聚合后的变量值
                Map<String, BigDecimal> aggregatedVariables = new HashMap<>();
                // 用于存储工资项金额聚合
                Map<String, BigDecimal> aggregatedSalaryItems = new HashMap<>();

                // 处理去重后的KPI变量数据进行聚合
                for (Map.Entry<Long, Map<String, Object>> endTimeEntry : uniqueVariablesByEndTime.entrySet()) {
                    Map<String, Object> formulaVariables = endTimeEntry.getValue();

                    if (formulaVariables != null) {
                        // 对每个变量进行聚合
                        for (Map.Entry<String, Object> entry : formulaVariables.entrySet()) {
                            String originalKey = entry.getKey();
                            Object valueObj = entry.getValue();

                            // 只处理以 EXT#KPI# 开头的键
                            if (originalKey.startsWith("EXT#KPI#")) {
                                // 提取KPI ID作为key，格式：kpi_ + KPI ID
                                String kpiId = originalKey.substring("EXT#KPI#".length());
                                String key = "kpi_" + kpiId;

                                if (valueObj != null) {
                                    String valueStr = valueObj.toString();
                                    if (StringUtils.isNotBlank(valueStr)) {
                                        try {
                                            // 将字符串转换为BigDecimal进行精确计算
                                            BigDecimal value = new BigDecimal(valueStr);

                                            // 累加到聚合Map中
                                            if (aggregatedVariables.containsKey(key)) {
                                                aggregatedVariables.put(key, aggregatedVariables.get(key).add(value));
                                            } else {
                                                aggregatedVariables.put(key, value);
                                            }
                                        } catch (NumberFormatException e) {
                                            log.warn("无法解析公式变量值为数字: originalKey={}, kpiId={}, value={}", originalKey, kpiId, valueStr);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 处理工资项金额聚合（工资项金额不需要按周期去重，直接累加）
                for (IObjectData detailData : salaryDetailDatas) {
                    String salaryItemId = detailData.get(SalaryDetailDataFields.SALARY_ITEM, String.class);
                    String amountStr = detailData.get(SalaryDetailDataFields.AMOUNT, String.class);

                    if (StringUtils.isNotBlank(salaryItemId) && StringUtils.isNotBlank(amountStr)) {
                        try {
                            // 构建工资项key
                            String salaryItemKey = "si_" + salaryItemId;
                            BigDecimal amount = new BigDecimal(amountStr);

                            // 累加到工资项金额聚合Map中
                            if (aggregatedSalaryItems.containsKey(salaryItemKey)) {
                                aggregatedSalaryItems.put(salaryItemKey,
                                        aggregatedSalaryItems.get(salaryItemKey).add(amount));
                            } else {
                                aggregatedSalaryItems.put(salaryItemKey, amount);
                            }
                        } catch (NumberFormatException e) {
                            log.warn("无法解析工资项金额为数字: salaryItemId={}, amount={}", salaryItemId, amountStr);
                        }
                    }
                }

                // 将聚合后的变量值和工资项金额聚合结果转换为字符串
                for (Map.Entry<String, BigDecimal> entry : aggregatedVariables.entrySet()) {
                    aggregatedResult.put(entry.getKey(), entry.getValue().toString());
                }
                for (Map.Entry<String, BigDecimal> entry : aggregatedSalaryItems.entrySet()) {
                    aggregatedResult.put(entry.getKey(), entry.getValue().toString());
                }

                log.debug("KPI变量聚合完成, 去重前明细数量: {}, 去重后周期数量: {}, 聚合变量数量: {}, 工资项数量: {}",
                    salaryDetailDatas.size(), uniqueVariablesByEndTime.size(),
                    aggregatedVariables.size(), aggregatedSalaryItems.size());
            }
        } catch (Exception e) {
            log.error("获取工资条的额外聚合数据失败, tenantId: {}, salaryDataId: {}", tenantId, salaryDataId, e);
        }

        return aggregatedResult;
    }

    /**
     * 按周期结束时间去重KPI变量数据，保证同一个周期变量只计算一次
     *
     * @param salaryDetailDatas 薪资明细数据列表
     * @return 按周期结束时间去重后的变量Map，key为周期结束时间，value为该周期的变量Map
     */
    private Map<Long, Map<String, Object>> deduplicateVariablesByEndTime(List<IObjectData> salaryDetailDatas) {
        Map<Long, Map<String, Object>> uniqueVariablesByEndTime = new HashMap<>();

        for (IObjectData detailData : salaryDetailDatas) {
            // 获取周期结束时间
            Long endTime = detailData.get(SalaryDetailDataFields.END_DATE, Long.class);
            if (endTime == null) {
                log.warn("薪资明细数据缺少周期结束时间，跳过该条记录，明细ID: {}", detailData.getId());
                continue;
            }

            // 获取公式变量
            String formulaVariablesStr = detailData.get(SalaryDetailDataFields.FORMULA_VARIABLE, String.class);
            if (StringUtils.isBlank(formulaVariablesStr)) {
                continue;
            }

            try {
                Map<String, Object> formulaVariables = JSON.parseObject(formulaVariablesStr, Map.class);
                if (formulaVariables != null && !formulaVariables.isEmpty()) {
                    // 如果该周期结束时间已存在，则合并变量（后面的覆盖前面的，保证唯一性）
                    if (uniqueVariablesByEndTime.containsKey(endTime)) {
                        Map<String, Object> existingVariables = uniqueVariablesByEndTime.get(endTime);
                        existingVariables.putAll(formulaVariables);
                    } else {
                        uniqueVariablesByEndTime.put(endTime, new HashMap<>(formulaVariables));
                    }
                }
            } catch (Exception e) {
                log.warn("解析公式变量失败，明细ID: {}, formulaVariables: {}", detailData.getId(), formulaVariablesStr, e);
            }
        }

        log.debug("按周期结束时间去重KPI变量完成，原始明细数量: {}, 去重后周期数量: {}",
            salaryDetailDatas.size(), uniqueVariablesByEndTime.size());

        return uniqueVariablesByEndTime;
    }

    @Override
    public DailySalaryQuery.Result getDailySalary(
            String employeeId,
            String queryDate,
            String month,
            ServiceContext context) {
        DailySalaryQuery.Result result = new DailySalaryQuery.Result();

        try {
            // 1. 查询员工的薪资规则信息
            IObjectData employeeFixedSalary = employeeFixedSalaryDao.getByEmployeeId(context.getTenantId(), employeeId);
            if (employeeFixedSalary == null) {
                log.warn("员工没有固定薪资信息, employeeId: {}", employeeId);
                return result; // 返回空结果
            }

            // 2. 获取薪资规则ID
            String salaryRuleId = employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_RULE, String.class);
            if (StringUtils.isBlank(salaryRuleId)) {
                log.warn("员工没有关联薪资规则, employeeId: {}", employeeId);
                return result; // 返回空结果
            }

            // 3. 查询薪资规则详情
            IObjectData salaryRuleObj = salaryRuleDao.getById(context.getTenantId(), salaryRuleId);
            if (salaryRuleObj == null) {
                log.warn("薪资规则不存在, salaryRuleId: {}", salaryRuleId);
                return result; // 返回空结果
            }

            // 处理日历数据（只有当month不为空时）
            if (StringUtils.isNotBlank(month)) {
                // 解析月份
                YearMonth yearMonth = YearMonth.parse(month, DateTimeFormatter.ofPattern("yyyy-MM"));

                // 获取月份的第一天和最后一天
                LocalDate firstDayOfMonth = yearMonth.atDay(1);
                LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

                // 查询月份内的所有工资条数据
                long startTimeMillis = firstDayOfMonth.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()
                        .toEpochMilli();
                long endTimeMillis = lastDayOfMonth.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant()
                        .toEpochMilli();

                List<IObjectData> salaryDataList = salaryDataDao.getByEmployeeFixedSalaryIdAndTimeRange(
                        context.getTenantId(), employeeFixedSalary.getId(), startTimeMillis, endTimeMillis);

                // 6. 查询月份内的所有工资条明细数据
                List<IObjectData> salaryDetailDataList = salaryDetailDataDao.getExistSalaryDetailDataByTimeRange(
                        context.getTenantId(), employeeFixedSalary.getId(), startTimeMillis, endTimeMillis);

                Map<LocalDate, IObjectData> dateToSalaryData = new HashMap<>();
                // 构建日期到工资条的映射
                for (IObjectData salaryData : salaryDataList) {
                    Long startDate = salaryData.get(SalaryDataFields.START_DATE, Long.class);
                    Long endDate = salaryData.get(SalaryDataFields.END_DATE, Long.class);
                    if (startDate != null && endDate != null) {
                        LocalDate start = Instant.ofEpochMilli(startDate).atZone(ZoneId.systemDefault()).toLocalDate();
                        LocalDate end = Instant.ofEpochMilli(endDate).atZone(ZoneId.systemDefault()).toLocalDate();
                        // 相等 日薪
                        if (start.equals(end)) {
                            dateToSalaryData.put(start, salaryData);
                        }
                    }
                }
                Map<LocalDate, List<IObjectData>> dateToSalaryDetailData = new HashMap<>();
                // 构建日期到工资条明细的映射
                for (IObjectData detailData : salaryDetailDataList) {
                    Long startDate = detailData.get(SalaryDetailDataFields.START_DATE, Long.class);
                    Long endDate = detailData.get(SalaryDetailDataFields.END_DATE, Long.class);
                    if (startDate != null && endDate != null) {
                        LocalDate start = Instant.ofEpochMilli(startDate).atZone(ZoneId.systemDefault()).toLocalDate();
                        LocalDate end = Instant.ofEpochMilli(endDate).atZone(ZoneId.systemDefault()).toLocalDate();
                        // 相等 日薪
                        if (start.equals(end)) {
                            dateToSalaryDetailData.computeIfAbsent(start, k -> new ArrayList<>()).add(detailData);
                        }
                    }
                }

                // 创建日历数据
                LocalDate selectedDate = StringUtils.isNotBlank(queryDate)
                        ? LocalDate.parse(queryDate, DateTimeFormatter.ISO_LOCAL_DATE)
                        : null;

                List<DailySalaryQuery.CalendarDay> calendarDays = createCalendarDaysWithDetails(
                        yearMonth, dateToSalaryData, dateToSalaryDetailData, selectedDate);
                result.setCalendarDays(calendarDays);
            }

            // 处理当前选中日期的详细薪资数据（只有当queryDate不为空时）
            if (StringUtils.isNotBlank(queryDate)) {
                LocalDate queryLocalDate = LocalDate.parse(queryDate, DateTimeFormatter.ISO_LOCAL_DATE);

                // 查询指定日期的工资条和工资条明细
                long startTimeMillis = queryLocalDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()
                        .toEpochMilli();
                long endTimeMillis = startTimeMillis;

                // 查询工资条数据
                List<IObjectData> salaryDataList = salaryDataDao.getByEmployeeFixedSalaryIdAndTimeEq(
                        context.getTenantId(), employeeFixedSalary.getId(), startTimeMillis, endTimeMillis);

                // 如果找到了工资条，创建详细数据
                if (!salaryDataList.isEmpty()) {
                    IObjectData salaryData = salaryDataList.get(0);

                    // 获取工资发放单
                    String salaryPaymentSlipId = salaryData.get(SalaryDataFields.SALARY_PAYMENT_SLIP, String.class);
                    IObjectData salaryPaymentSlipObj = null;
                    if (StringUtils.isNotBlank(salaryPaymentSlipId)) {
                        salaryPaymentSlipObj = salaryPaymentSlipDao.getById(context.getTenantId(), salaryPaymentSlipId);
                    }

                    DailySalaryQuery.DailySalaryDetail currentDayDetail = createDailySalaryDetail(
                            context.getTenantId(),
                            salaryData,
                            queryLocalDate,
                            salaryRuleObj,
                            salaryPaymentSlipObj);
                    result.setCurrentDayDetail(currentDayDetail);
                } else {
                    // 如果没有找到工资条，查询指定日期的工资条明细
                    List<IObjectData> salaryDetailDataList = salaryDetailDataDao.getByEmployeeFixedSalaryIdAndTimeEq(
                            context.getTenantId(), employeeFixedSalary.getId(), startTimeMillis, endTimeMillis);

                    // 如果找到了工资条明细，创建详细数据
                    if (!salaryDetailDataList.isEmpty()) {
                        DailySalaryQuery.DailySalaryDetail currentDayDetail = createDailySalaryDetailFromDetailData(
                                context.getTenantId(),
                                salaryDetailDataList,
                                queryLocalDate,
                                salaryRuleObj);
                        result.setCurrentDayDetail(currentDayDetail);
                    }
                }
            }

        } catch (Exception e) {
            log.error("获取日工资数据异常, employeeId: {}, queryDate: {}, month: {}", employeeId, queryDate, month, e);
        }

        return result;
    }

    /**
     * 创建空的结果对象
     */
    private DailySalaryQuery.Result createEmptyResult(YearMonth yearMonth) {
        DailySalaryQuery.Result result = new DailySalaryQuery.Result();

        // 创建空的日历数据
        List<DailySalaryQuery.CalendarDay> calendarDays = new ArrayList<>();

        // 获取月份的天数
        int daysInMonth = yearMonth.lengthOfMonth();
        LocalDate today = LocalDate.now();

        // 为月份中的每一天创建日历数据
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate date = yearMonth.atDay(day);

            DailySalaryQuery.CalendarDay calendarDay = new DailySalaryQuery.CalendarDay();
            calendarDay.setDate(date.format(DateTimeFormatter.ISO_LOCAL_DATE));
            calendarDay.setDayDisplay(String.valueOf(day));
            calendarDay.setWeekDay(date.getDayOfWeek().getValue() % 7); // 0-6表示周日到周六
            calendarDay.setHasSalaryData(false);
            calendarDay.setAmount("0");
            calendarDay.setIsSelected(date.equals(today));
            calendarDay.setIsToday(date.equals(today));

            calendarDays.add(calendarDay);
        }

        result.setCalendarDays(calendarDays);
        return result;
    }

    /**
     * 创建日历数据，考虑工资条和工资条明细
     */
    private List<DailySalaryQuery.CalendarDay> createCalendarDaysWithDetails(
            YearMonth yearMonth,
            Map<LocalDate, IObjectData> dateToSalaryData,
            Map<LocalDate, List<IObjectData>> dateToSalaryDetailData,
            LocalDate selectedDate) {
        List<DailySalaryQuery.CalendarDay> calendarDays = new ArrayList<>();

        // 获取月份的天数
        int daysInMonth = yearMonth.lengthOfMonth();
        LocalDate today = LocalDate.now();

        // 为月份中的每一天创建日历数据
        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate date = yearMonth.atDay(day);

            DailySalaryQuery.CalendarDay calendarDay = new DailySalaryQuery.CalendarDay();
            calendarDay.setDate(date.format(DateTimeFormatter.ISO_LOCAL_DATE));
            calendarDay.setDayDisplay(String.valueOf(day));
            calendarDay.setWeekDay(date.getDayOfWeek().getValue() % 7); // 0-6表示周日到周六

            // 首先检查是否有工资条数据
            IObjectData salaryData = dateToSalaryData.get(date);
            if (salaryData != null) {
                calendarDay.setHasSalaryData(true);
                String amount = salaryData.get(SalaryDataFields.PAYABLE_SALARY, String.class);
                calendarDay.setAmount(SalaryAmountCalculator.formatAmountWithoutTrailingZeros(StringUtils.isNotBlank(amount) ? amount : "0"));
            } else {
                // 如果没有工资条，检查是否有工资条明细数据
                List<IObjectData> detailDataList = dateToSalaryDetailData.get(date);
                if (detailDataList != null && !detailDataList.isEmpty()) {
                    calendarDay.setHasSalaryData(true);
                    // 计算工资条明细的总金额
                    BigDecimal totalAmount = BigDecimal.ZERO;
                    for (IObjectData detailData : detailDataList) {
                        String amountStr = detailData.get(SalaryDetailDataFields.AMOUNT, String.class);
                        String incrementDecrementAttrib = detailData.get(SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB, String.class);

                        if (StringUtils.isNotBlank(amountStr)) {
                            try {
                                BigDecimal amount = new BigDecimal(amountStr);

                                // 根据增减属性判断是加还是减
                                if (SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
                                    // 减少
                                    totalAmount = totalAmount.subtract(amount);
                                } else {
                                    // 增加（默认为增加，包括null和"1"的情况）
                                    totalAmount = totalAmount.add(amount);
                                }
                            } catch (NumberFormatException e) {
                                log.warn("无法解析工资条明细金额: {}", amountStr);
                            }
                        }
                    }
                    calendarDay.setAmount(SalaryAmountCalculator.formatAmountWithoutTrailingZeros(totalAmount.toString()));
                } else {
                    calendarDay.setHasSalaryData(false);
                    calendarDay.setAmount("0.00");
                }
            }

            calendarDay.setIsSelected(date.equals(selectedDate));
            calendarDay.setIsToday(date.equals(today));

            calendarDays.add(calendarDay);
        }

        return calendarDays;
    }

    /**
     * 创建日工资详细数据
     */
    private DailySalaryQuery.DailySalaryDetail createDailySalaryDetail(
            String tenantId,
            IObjectData salaryData,
            LocalDate date,
            IObjectData salaryRuleObj,
            IObjectData salaryPaymentSlipObj) {
        DailySalaryQuery.DailySalaryDetail detail = new DailySalaryQuery.DailySalaryDetail();
        detail.setDate(date.format(DateTimeFormatter.ISO_LOCAL_DATE));

        // 获取工资条总金额
        String totalAmount = salaryData.get(SalaryDataFields.PAYABLE_SALARY, String.class);
        detail.setTotalAmount(StringUtils.isNotBlank(totalAmount) ? totalAmount : "0");

        // 获取表头
        List<String> headers = getCommonSalaryHeaders(salaryPaymentSlipObj, salaryRuleObj);

        // 获取表头描述
        Map<String, IFieldDescribe> headerDescriptions = processSalaryTableHeaderDescriptions(tenantId, headers);
        // 获取工资条的额外聚合数据
        Map<String, String> aggregatedValues = getSalaryDataAggregatedValues(tenantId, salaryData.getId());

        // 使用公共方法处理薪资项目
        List<SalaryItem> salaryItems = processSalaryItems(
            tenantId,
            headers,
            aggregatedValues,
            headerDescriptions
        );

        detail.setSalaryItems(salaryItems);

        return detail;
    }

    /**
     * 从工资条明细创建日工资详细数据
     */
    private DailySalaryQuery.DailySalaryDetail createDailySalaryDetailFromDetailData(
            String tenantId,
            List<IObjectData> salaryDetailDataList,
            LocalDate date,
            IObjectData salaryRuleObj) {
        DailySalaryQuery.DailySalaryDetail detail = new DailySalaryQuery.DailySalaryDetail();
        detail.setDate(date.format(DateTimeFormatter.ISO_LOCAL_DATE));

        // 计算工资条明细的总金额
        BigDecimal totalAmount = BigDecimal.ZERO;

        // 获取表头 - 使用公共方法
        List<String> headers = getCommonSalaryHeaders(null, salaryRuleObj);

        // 获取表头描述
        Map<String, IFieldDescribe> headerDescriptions = processSalaryTableHeaderDescriptions(tenantId, headers);

        // 用于存储工资项ID到金额的映射
        Map<String, BigDecimal> salaryItemAmounts = new HashMap<>();
        // 用于存储公式变量的映射
        Map<String, BigDecimal> formulaVariables = new HashMap<>();

        // 处理每个工资条明细
        for (IObjectData detailData : salaryDetailDataList) {
            // 获取工资项信息
            String salaryItemId = detailData.get(SalaryDetailDataFields.SALARY_ITEM, String.class);
            if (StringUtils.isNotBlank(salaryItemId)) {
                // 获取金额和增减属性
                String amountStr = detailData.get(SalaryDetailDataFields.AMOUNT, String.class);
                String incrementDecrementAttrib = detailData.get(SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB, String.class);

                if (StringUtils.isNotBlank(amountStr)) {
                    try {
                        BigDecimal amount = new BigDecimal(amountStr);

                        // 根据增减属性判断是加还是减
                        if (SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
                            // 减少
                            totalAmount = totalAmount.subtract(amount);
                            amount = amount.negate(); // 工资项金额也要变成负数
                        } else {
                            // 增加（默认为增加，包括null和"1"的情况）
                            totalAmount = totalAmount.add(amount);
                        }

                        // 累加到工资项金额映射中
                        String salaryItemKey = "si_" + salaryItemId;
                        if (salaryItemAmounts.containsKey(salaryItemKey)) {
                            salaryItemAmounts.put(salaryItemKey, salaryItemAmounts.get(salaryItemKey).add(amount));
                        } else {
                            salaryItemAmounts.put(salaryItemKey, amount);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("无法解析工资条明细金额: {}", amountStr);
                    }
                }

                // 获取公式变量
                Map<String, String> detailFormulaVariables = detailData.get(SalaryDetailDataFields.FORMULA_VARIABLE,
                        Map.class);
                if (detailFormulaVariables != null) {
                    for (Map.Entry<String, String> entry : detailFormulaVariables.entrySet()) {
                        String key = "kpi_" + entry.getKey();
                        String valueStr = entry.getValue();

                        if (StringUtils.isNotBlank(valueStr)) {
                            try {
                                BigDecimal value = new BigDecimal(valueStr);

                                // 累加到公式变量映射中
                                if (formulaVariables.containsKey(key)) {
                                    formulaVariables.put(key, formulaVariables.get(key).add(value));
                                } else {
                                    formulaVariables.put(key, value);
                                }
                            } catch (NumberFormatException e) {
                                log.warn("无法解析公式变量值为数字: key={}, value={}", key, valueStr);
                            }
                        }
                    }
                }
            }
        }

        // 设置总金额
        detail.setTotalAmount(totalAmount.toString());

        // 创建聚合值映射
        Map<String, String> aggregatedValues = new HashMap<>();

        // 将公式变量添加到聚合值映射
        for (Map.Entry<String, BigDecimal> entry : formulaVariables.entrySet()) {
            aggregatedValues.put(entry.getKey(), entry.getValue().toString());
        }

        // 将工资项金额添加到聚合值映射
        for (Map.Entry<String, BigDecimal> entry : salaryItemAmounts.entrySet()) {
            aggregatedValues.put(entry.getKey(), entry.getValue().toString());
        }

        // 使用公共方法处理薪资项目
        List<SalaryItem> salaryItems = processSalaryItems(
            tenantId,
            headers,
            aggregatedValues,
            headerDescriptions
        );

        detail.setSalaryItems(salaryItems);

        return detail;
    }

    @Override
    public List<IObjectData> queryApplicableSalaryRules(String tenantId, String employeeId,
            List<String> departmentIds, String outTenantId,
            List<String> roleIds, boolean isExternal, String salaryMethod) {
        log.info(
                "查询适用的薪资规则，tenantId: {}, employeeId: {}, departmentIds: {}, enterpriseId: {}, roleIds: {}, isExternal: {}", //ignoreI18n
                tenantId, employeeId, departmentIds, outTenantId, roleIds, isExternal);

        if (StringUtils.isBlank(tenantId)) {
            log.warn("查询适用的薪资规则参数无效，tenantId为空");
            return Lists.newArrayList();
        }
        // salaryMethod 为空报错
//        if (StringUtils.isBlank(salaryMethod)) {
//            log.warn("查询适用的薪资规则参数无效，salaryMethod为空");
//            throw new ValidateException("薪资方式不能为空");// ignoreI18n
//        }

        try {
            // 1. 获取部门及其所有上级部门ID
            List<String> allDepartmentIds = Lists.newArrayList();
            if (departmentIds != null && !departmentIds.isEmpty()) {
                allDepartmentIds.addAll(departmentIds);
                // 获取所有上级部门ID
                Map<String, List<String>> deptIds = departmentService.batchGetUpperDepartmentIds(tenantId,
                        departmentIds, Boolean.TRUE);
                allDepartmentIds
                        .addAll(deptIds.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
                // 去重
                allDepartmentIds = allDepartmentIds.stream().distinct().collect(Collectors.toList());
            }

            // 2. 构建查询条件
            SearchQuery.SearchQueryBuilder queryBuilder = SearchQuery.builder();

            // 2.1 基本条件：规则必须是启用状态且生效日期已到
            // 注意：根据实际的SalaryRuleFields定义，这里需要调整字段名
            // 如果没有STATUS字段，可能需要使用其他字段来判断规则是否有效

            // 2.2 构建适用范围条件 - 基于现有字段重新构造逻辑，区分内外部员工
            if (!isExternal) {
                // 内部员工查询逻辑
                List<SearchQuery.SearchQueryBuilder> internalQueries = Lists.newArrayList();

                // 2.2.1 内部员工条件
                if (StringUtils.isNotBlank(employeeId)) {
                    SearchQuery.SearchQueryBuilder internalEmployeeQuery = SearchQuery.builder()
                            .hasAnyOf(SalaryRuleFields.APPLICABLE_PERSON_INTE, Lists.newArrayList(employeeId));
                    if (StringUtils.isNotBlank(salaryMethod)) {
                        internalEmployeeQuery.eq(SalaryRuleFields.SALARY_METHOD, salaryMethod);
                    }
                    internalQueries.add(internalEmployeeQuery);
                }

                // 2.2.2 内部部门条件
                if (!allDepartmentIds.isEmpty()) {
                    SearchQuery.SearchQueryBuilder internalDepartmentQuery = SearchQuery.builder()
                            .inDepartmentAndSub(SalaryRuleFields.APPLICABLE_DEPARTMENT_INTE, allDepartmentIds);
                    if (StringUtils.isNotBlank(salaryMethod)) {
                        internalDepartmentQuery.eq(SalaryRuleFields.SALARY_METHOD, salaryMethod);
                    }
                    internalQueries.add(internalDepartmentQuery);
                }

                // 2.2.3 内部角色条件
                if (roleIds != null && !roleIds.isEmpty()) {
                    SearchQuery.SearchQueryBuilder internalRoleQuery = SearchQuery.builder()
                            .hasAnyOf(SalaryRuleFields.APPLICABLE_ROLE_INTE, roleIds);
                    if (StringUtils.isNotBlank(salaryMethod)) {
                        internalRoleQuery.eq(SalaryRuleFields.SALARY_METHOD, salaryMethod);
                    }
                    internalQueries.add(internalRoleQuery);
                }

                // 将内部员工的所有条件用OR连接
                if (!internalQueries.isEmpty()) {
                    queryBuilder.addOrWheres(internalQueries.toArray(new SearchQuery.SearchQueryBuilder[0]));
                } else {
                    log.info("内部员工没有找到适用的查询条件，返回空列表");
                    return Lists.newArrayList();
                }
            } else {
                // 外部员工查询逻辑
                List<SearchQuery.SearchQueryBuilder> externalQueries = Lists.newArrayList();

                // 2.2.4 外部员工条件
                if (StringUtils.isNotBlank(employeeId)) {
                    SearchQuery.SearchQueryBuilder externalEmployeeQuery = SearchQuery.builder()
                            .hasAnyOf(SalaryRuleFields.APPLICABLE_OUT_USER, Lists.newArrayList(employeeId));
                    if (StringUtils.isNotBlank(salaryMethod)) {
                        externalEmployeeQuery.eq(SalaryRuleFields.SALARY_METHOD, salaryMethod);
                    }
                    externalQueries.add(externalEmployeeQuery);
                }

                // 2.2.5 外部企业条件
                if (StringUtils.isNotBlank(outTenantId)) {
                    SearchQuery.SearchQueryBuilder externalEnterpriseQuery = SearchQuery.builder()
                            .hasAnyOf(SalaryRuleFields.APPLICABLE_OUT_TENANT, Lists.newArrayList(outTenantId));
                    if (StringUtils.isNotBlank(salaryMethod)) {
                        externalEnterpriseQuery.eq(SalaryRuleFields.SALARY_METHOD, salaryMethod);
                    }
                    externalQueries.add(externalEnterpriseQuery);
                }

                // 2.2.6 外部角色条件
                if (roleIds != null && !roleIds.isEmpty()) {
                    SearchQuery.SearchQueryBuilder externalRoleQuery = SearchQuery.builder()
                            .hasAnyOf(SalaryRuleFields.APPLICABLE_OUT_ROLE, roleIds);
                    if (StringUtils.isNotBlank(salaryMethod)) {
                        externalRoleQuery.eq(SalaryRuleFields.SALARY_METHOD, salaryMethod);
                    }
                    externalQueries.add(externalRoleQuery);
                }

                // 将外部员工的所有条件用OR连接
                if (!externalQueries.isEmpty()) {
                    queryBuilder.addOrWheres(externalQueries.toArray(new SearchQuery.SearchQueryBuilder[0]));
                } else {
                    log.info("外部员工没有找到适用的查询条件，返回空列表");
                    return Lists.newArrayList();
                }
            }

            // 3. 执行查询
            List<IObjectData> rules = salaryRuleDao.getAllIObjectDataListByQuery(
                    User.systemUser(tenantId),
                    queryBuilder.build(),
                    SalaryRuleFields.API_NAME);

            log.info("查询到初步适用的薪资规则数量: {}", rules.size());

            // 4. 过滤强制不适用人员和处理强制适用人员
            List<IObjectData> filteredRules = filterPersonnelRules(rules, employeeId);

            log.info("过滤人员规则后的薪资规则数量: {}", filteredRules.size());
            return filteredRules;

        } catch (Exception e) {
            log.error("查询适用的薪资规则异常", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 过滤人员规则（处理强制适用人员和强制不适用人员）
     *
     * @param rules 初步查询到的工资规则列表
     * @param employeeId 员工ID
     * @return 过滤后的工资规则列表
     */
    private List<IObjectData> filterPersonnelRules(List<IObjectData> rules, String employeeId) {
        if (CollectionUtils.isEmpty(rules) || StringUtils.isBlank(employeeId)) {
            return rules;
        }

        List<IObjectData> filteredRules = Lists.newArrayList();
        List<IObjectData> mandatoryRules = Lists.newArrayList();

        for (IObjectData rule : rules) {
            try {
                // 1. 检查强制适用人员
                Object mandatoryPersonnel = rule.get(SalaryRuleFields.MANDATORY_APPLICABLE_PERSONNEL);
                List<String> mandatoryPersonnelList = convertToStringList(mandatoryPersonnel);

                if (CollectionUtils.isNotEmpty(mandatoryPersonnelList) && mandatoryPersonnelList.contains(employeeId)) {
                    // 员工在强制适用人员列表中，该规则必须适用
                    mandatoryRules.add(rule);
                    log.info("工资规则 {} 强制适用于员工 {}，员工在强制适用人员列表中", rule.getId(), employeeId);
                    continue;
                }

                // 2. 检查强制不适用人员
                Object nonApplicablePersonnel = rule.get(SalaryRuleFields.NON_APPLICABLE_PERSONNEL);
                List<String> nonApplicablePersonnelList = convertToStringList(nonApplicablePersonnel);

                if (CollectionUtils.isNotEmpty(nonApplicablePersonnelList) && nonApplicablePersonnelList.contains(employeeId)) {
                    // 员工在强制不适用人员列表中，排除该规则
                    log.info("工资规则 {} 不适用于员工 {}，员工在强制不适用人员列表中", rule.getId(), employeeId);
                    continue;
                }

                // 3. 既不在强制适用也不在强制不适用列表中，按常规逻辑处理
                filteredRules.add(rule);
                log.debug("工资规则 {} 按常规逻辑适用于员工 {}", rule.getId(), employeeId);

            } catch (Exception e) {
                log.error("处理工资规则人员过滤时发生异常，规则ID: {}, 员工ID: {}", rule.getId(), employeeId, e);
                // 异常情况下，保守处理，认为规则适用
                filteredRules.add(rule);
            }
        }

        // 强制适用的规则优先级最高，放在最前面
        List<IObjectData> result = Lists.newArrayList();
        result.addAll(mandatoryRules);
        result.addAll(filteredRules);

        return result;
    }



    /**
     * 检查员工是否为指定规则的强制适用人员
     *
     * @param salaryRuleObj 工资规则对象
     * @param employeeId 员工ID
     * @return 是否为强制适用人员
     */
    private boolean isMandatoryApplicableEmployee(IObjectData salaryRuleObj, String employeeId) {
        if (salaryRuleObj == null || StringUtils.isBlank(employeeId)) {
            return false;
        }

        try {
            Object mandatoryPersonnel = salaryRuleObj.get(SalaryRuleFields.MANDATORY_APPLICABLE_PERSONNEL);
            List<String> mandatoryPersonnelList = convertToStringList(mandatoryPersonnel);

            boolean isMandatory = CollectionUtils.isNotEmpty(mandatoryPersonnelList) && mandatoryPersonnelList.contains(employeeId);
            log.debug("检查员工 {} 是否为规则 {} 的强制适用人员: {}", employeeId, salaryRuleObj.getId(), isMandatory);

            return isMandatory;

        } catch (Exception e) {
            log.error("检查强制适用人员失败，规则ID: {}, 员工ID: {}", salaryRuleObj.getId(), employeeId, e);
            return false;
        }
    }

    /**
     * 从员工ID集合中过滤强制不适用人员
     *
     * @param employeeIds 员工ID集合
     * @param salaryRule 工资规则对象
     * @return 过滤后的员工ID集合
     */
    private Set<String> filterNonApplicablePersonnelFromEmployeeIds(Set<String> employeeIds, IObjectData salaryRule) {
        if (CollectionUtils.isEmpty(employeeIds) || salaryRule == null) {
            return employeeIds;
        }

        try {
            // 获取强制不适用人员字段
            Object nonApplicablePersonnel = salaryRule.get(SalaryRuleFields.NON_APPLICABLE_PERSONNEL);

            if (nonApplicablePersonnel == null) {
                // 如果没有设置强制不适用人员，则返回原集合
                return employeeIds;
            }

            // 转换为字符串列表
            List<String> nonApplicablePersonnelList = convertToStringList(nonApplicablePersonnel);

            if (CollectionUtils.isEmpty(nonApplicablePersonnelList)) {
                // 如果强制不适用人员列表为空，则返回原集合
                return employeeIds;
            }

            // 创建新的集合，排除强制不适用人员
            Set<String> filteredEmployeeIds = Sets.newHashSet();
            int excludedCount = 0;

            for (String employeeId : employeeIds) {
                if (!nonApplicablePersonnelList.contains(employeeId)) {
                    filteredEmployeeIds.add(employeeId);
                } else {
                    excludedCount++;
                    log.debug("员工 {} 在工资规则 {} 的强制不适用人员列表中，已排除", employeeId, salaryRule.getId());
                }
            }

            if (excludedCount > 0) {
                log.info("工资规则 {} 排除了 {} 个强制不适用人员", salaryRule.getId(), excludedCount);
            }

            return filteredEmployeeIds;

        } catch (Exception e) {
            log.error("处理工资规则强制不适用人员过滤时发生异常，规则ID: {}", salaryRule.getId(), e);
            // 异常情况下，保守处理，返回原集合
            return employeeIds;
        }
    }





    @Override
    public void processApplicableScopeChanges(String tenantId, String salaryRuleId,
            IObjectData oldSalaryRule, IObjectData newSalaryRule) {
        log.info("处理薪资规则适用范围变更，tenantId: {}, salaryRuleId: {}", tenantId, salaryRuleId);

        try {
            // 获取旧适用范围的员工ID列表
            List<String> oldEmployeeIds = getEmployeeIdsBySalaryRule(tenantId, oldSalaryRule);

            // 获取新适用范围的员工ID列表
            List<String> newEmployeeIds = getEmployeeIdsBySalaryRule(tenantId, newSalaryRule);

            // 计算需要删除的员工ID（在旧范围内但不在新范围内）
            List<String> employeesToRemove = Lists.newArrayList(oldEmployeeIds);
            employeesToRemove.removeAll(newEmployeeIds);

            // 计算需要添加的员工ID（在新范围内但不在旧范围内）
            List<String> employeesToAdd = Lists.newArrayList(newEmployeeIds);
            employeesToAdd.removeAll(oldEmployeeIds);

            // 获取薪资规则的定薪方式
            String salaryMethod = newSalaryRule.get(SalaryRuleFields.SALARY_METHOD, String.class);
            if (StringUtils.isBlank(salaryMethod)) {
                log.warn("薪资规则的定薪方式为空，无法处理适用范围变更，规则ID: {}", salaryRuleId);
                return;
            }

            // 删除不再适用的员工规则
            if (CollectionUtils.isNotEmpty(employeesToRemove)) {
                log.info("删除不再适用的员工规则，员工数量: {}", employeesToRemove.size());
                updateEmployeeFixedSalaryRules(tenantId, employeesToRemove, oldSalaryRule,
                        SalaryRuleOperation.REMOVE);
            }

            // 添加新适用的员工规则
            if (CollectionUtils.isNotEmpty(employeesToAdd)) {
                log.info("添加新适用的员工规则，员工数量: {}", employeesToAdd.size());
                updateEmployeeFixedSalaryRules(tenantId, employeesToAdd, newSalaryRule,
                        SalaryRuleOperation.ADD);
            }

            log.info("薪资规则适用范围变更处理完成");
        } catch (Exception e) {
            log.error("处理薪资规则适用范围变更时发生异常", e);
            throw new RuntimeException("处理薪资规则适用范围变更失败: " + e.getMessage(), e); //ignoreI18n
        }
    }

    @Override
    public List<String> getEmployeeIdsBySalaryRule(String tenantId, IObjectData salaryRule) {
        log.info("根据薪资规则获取员工ID列表，tenantId: {}", tenantId);

        if (salaryRule == null) {
            log.warn("薪资规则对象为空");
            return Lists.newArrayList();
        }

        try {
            Set<String> employeeIds = Sets.newHashSet();
            ObjectDataExt applicableScopeExt = ObjectDataExt.of(salaryRule);

            // 处理内部员工适用范围
            processInternalEmployeeScope(tenantId, applicableScopeExt, employeeIds);

            // 处理外部员工适用范围
            processExternalEmployeeScope(tenantId, applicableScopeExt, employeeIds);

            log.info("根据薪资规则获取到初步员工ID数量: {}", employeeIds.size());

//            // 排除强制不适用人员
//            Set<String> filteredEmployeeIds = filterNonApplicablePersonnelFromEmployeeIds(employeeIds, salaryRule);
//
//            log.info("排除强制不适用人员后的员工ID数量: {}", filteredEmployeeIds.size());
//            return Lists.newArrayList(filteredEmployeeIds);
            return Lists.newArrayList(employeeIds);
        } catch (Exception e) {
            log.error("根据薪资规则获取员工ID列表时发生异常", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 处理内部员工适用范围
     */
    private void processInternalEmployeeScope(String tenantId, ObjectDataExt applicableScopeExt,
            Set<String> employeeIds) {
        // 处理适用人员（内部）
        Object applicablePersonInte = applicableScopeExt.get(SalaryRuleFields.APPLICABLE_PERSON_INTE);
        if (applicablePersonInte != null) {
            List<String> personIds = convertToStringList(applicablePersonInte);
            employeeIds.addAll(personIds);
        }

        // 处理适用部门（内部）
        Object applicableDepartmentInte = applicableScopeExt.get(SalaryRuleFields.APPLICABLE_DEPARTMENT_INTE);
        if (applicableDepartmentInte != null) {
            List<String> departmentIds = convertToStringList(applicableDepartmentInte);
            List<String> employeeIdsFromDepartments = getEmployeeIdsByDepartments(tenantId, departmentIds, false);
            employeeIds.addAll(employeeIdsFromDepartments);
        }

        // 处理适用角色（内部）
        Object applicableRoleInte = applicableScopeExt.get(SalaryRuleFields.APPLICABLE_ROLE_INTE);
        if (applicableRoleInte != null) {
            List<String> roleIds = convertToStringList(applicableRoleInte);
            List<String> employeeIdsFromRoles = roleQueryService.getEmployeeIdsByRoles(tenantId, roleIds, false, false);
            employeeIds.addAll(employeeIdsFromRoles);
        }
    }

    /**
     * 处理外部员工适用范围
     */
    private void processExternalEmployeeScope(String tenantId, ObjectDataExt applicableScopeExt,
            Set<String> employeeIds) {
        // 处理适用人员（外部）
        Object applicableOutUser = applicableScopeExt.get(SalaryRuleFields.APPLICABLE_OUT_USER);
        if (applicableOutUser != null) {
            List<String> personIds = convertToStringList(applicableOutUser);
            employeeIds.addAll(personIds);
        }

        // 处理适用企业（外部）
        Object applicableOutTenant = applicableScopeExt.get(SalaryRuleFields.APPLICABLE_OUT_TENANT);
        if (applicableOutTenant != null) {
            List<String> tenantIds = convertToStringList(applicableOutTenant);
            List<String> employeeIdsFromTenants = getEmployeeIdsByOutTenants(tenantId, tenantIds);
            employeeIds.addAll(employeeIdsFromTenants);
        }

        // 处理适用角色（外部）
        Object applicableOutRole = applicableScopeExt.get(SalaryRuleFields.APPLICABLE_OUT_ROLE);
        if (applicableOutRole != null) {
            List<String> roleIds = convertToStringList(applicableOutRole);
            List<String> employeeIdsFromRoles = roleQueryService.getEmployeeIdsByRoles(tenantId, roleIds, true, false);
            employeeIds.addAll(employeeIdsFromRoles);
        }
    }

    /**
     * 将对象转换为字符串列表
     */
    private List<String> convertToStringList(Object obj) {
        if (obj == null) {
            return Lists.newArrayList();
        }

        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            return list.stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.toList());
        } else if (obj instanceof String) {
            String str = (String) obj;
            if (StringUtils.isNotBlank(str)) {
                return Lists.newArrayList(str.split(","));
            }
        }

        return Lists.newArrayList();
    }

    /**
     * 根据部门ID列表获取员工ID列表
     */
    private List<String> getEmployeeIdsByDepartments(String tenantId, List<String> departmentIds, boolean isExternal) {
        Set<String> employeeIds = Sets.newHashSet();

        try {
            if (isExternal) {
                // 外部员工：批量查询PublicEmployeeObj，避免for循环单条查询
                if (!departmentIds.isEmpty()) {
                    List<IObjectData> publicEmployees = baseDao.getAllIObjectDataListByQueryWithFields(
                            User.systemUser(tenantId),
                            SearchQuery.builder()
                                    .in(PublicEmployeeFields.DEPARTMENT, departmentIds)
                                    .build(),
                            PublicEmployeeFields.API_NAME,
                            Lists.newArrayList(PublicEmployeeFields.OUTER_UID));

                    for (IObjectData employee : publicEmployees) {
                        Object outerUid = employee.get(PublicEmployeeFields.OUTER_UID);
                        if (outerUid != null) {
                            employeeIds.add(outerUid.toString());
                        }
                    }
                }
            } else {
                // 内部员工：
                List<String> employeeIdsFromDepartments = employeeService.batchGetEmployeeIdsByDeptIds(tenantId,
                        departmentIds, null, true);
                employeeIds.addAll(employeeIdsFromDepartments);
            }
        } catch (Exception e) {
            log.error("根据部门ID获取员工ID时发生异常", e);
        }

        return Lists.newArrayList(employeeIds);
    }

    public static void main(String[] args) {
        System.out.println(SalaryAmountCalculator.formatAmountWithoutTrailingZeros("107.440"));
        System.out.println(SalaryAmountCalculator.formatAmountWithoutTrailingZeros("107.444"));
        System.out.println(SalaryAmountCalculator.formatAmountWithoutTrailingZeros("107.01"));
        System.out.println(SalaryAmountCalculator.formatAmountWithoutTrailingZeros("107.00"));
        System.out.println(SalaryAmountCalculator.formatAmountWithoutTrailingZeros("107.0"));
        System.out.println(SalaryAmountCalculator.formatAmountWithoutTrailingZeros("107"));
        //

    }


    /**
     * 根据外部企业ID列表获取员工ID列表
     */
    private List<String> getEmployeeIdsByOutTenants(String tenantId, List<String> outTenantIds) {
        Set<String> employeeIds = Sets.newHashSet();

        try {
            // 批量查询PublicEmployeeObj，避免for循环单条查询
            if (!outTenantIds.isEmpty()) {
                List<IObjectData> publicEmployees = baseDao.getAllIObjectDataListByQueryWithFields(
                        User.systemUser(tenantId),
                        SearchQuery.builder()
                                .in(PublicEmployeeFields.OUTER_TENANT_ID, outTenantIds)
                                .build(),
                        PublicEmployeeFields.API_NAME,
                        Lists.newArrayList(PublicEmployeeFields.OUTER_UID));

                for (IObjectData employee : publicEmployees) {
                    Object outerUid = employee.get(PublicEmployeeFields.OUTER_UID);
                    if (outerUid != null) {
                        employeeIds.add(outerUid.toString());
                    }
                }
            }
        } catch (Exception e) {
            log.error("根据外部企业ID获取员工ID时发生异常", e);
        }

        return Lists.newArrayList(employeeIds);
    }

    @Override
    public void updateEmployeeFixedSalaryRules(String tenantId, List<String> employeeIds,
            IObjectData salaryRuleObj, SalaryRuleOperation operation) {

        if (salaryRuleObj == null) {
            log.warn("薪资规则对象为空，无法进行处理");
            return;
        }

        String salaryRuleId = salaryRuleObj.getId();
        String salaryMethod = salaryRuleObj.get(SalaryRuleFields.SALARY_METHOD, String.class);

        log.info("更新员工固定薪资规则关联，tenantId: {}, employeeIds: {}, salaryRuleId: {}, salaryMethod: {}, operation: {}",
                tenantId, employeeIds.size(), salaryRuleId, salaryMethod, operation);

        if (CollectionUtils.isEmpty(employeeIds)) {
            log.info("员工ID列表为空，无需更新");
            return;
        }

        if (StringUtils.isBlank(salaryMethod)) {
            log.warn("定薪方式为空，无法进行处理");
            return;
        }

        try {
            User systemUser = User.systemUser(tenantId);

            // 批量查询员工固定薪资数据（按定薪方式过滤）
            List<IObjectData> employeeFixedSalaries = employeeFixedSalaryDao.getByEmployeeIdsAndSalaryMethod(tenantId,
                    employeeIds, salaryMethod);
            log.info("批量查询员工固定薪资数据（按定薪方式过滤），查询员工数: {}, 定薪方式: {}, 返回结果数: {}",
                    employeeIds.size(), salaryMethod, employeeFixedSalaries.size());

            // 创建员工ID到固定薪资对象的映射
            Map<String, IObjectData> employeeIdToSalaryMap = Maps.newHashMap();

            for (IObjectData employeeFixedSalary : employeeFixedSalaries) {
                // 获取员工ID（内部或外部）
                String employeeId = employeeFixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE, String.class);
                if (StringUtils.isBlank(employeeId)) {
                    employeeId = employeeFixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, String.class);
                }

                if (StringUtils.isNotBlank(employeeId)) {
                    employeeIdToSalaryMap.put(employeeId, employeeFixedSalary);
                }
            }

            // 分组处理需要更新的数据
            List<IObjectData> toUpdate = Lists.newArrayList();

            for (String employeeId : employeeIds) {
                IObjectData employeeFixedSalary = employeeIdToSalaryMap.get(employeeId);

                if (SalaryRuleOperation.REMOVE.equals(operation)) {
                    // 删除操作：如果员工的薪资规则是当前规则，则清空
                    if (employeeFixedSalary != null) {
                        String currentRuleId = employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_RULE,
                                String.class);
                        if (salaryRuleId.equals(currentRuleId)) {
                            employeeFixedSalary.set(EmployeeFixedSalaryFields.SALARY_RULE, null);
                            toUpdate.add(employeeFixedSalary);
                            log.info("准备移除员工 {} 的薪资规则关联", employeeId);
                        }
                    }
                } else if (SalaryRuleOperation.ADD.equals(operation)) {
                    // 添加操作：处理强制适用人员和常规适用逻辑
                    if (employeeFixedSalary != null) {
                        String currentRuleId = employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_RULE,
                                String.class);

                        // 检查是否为强制适用人员（从当前规则对象中获取）
                        boolean isMandatoryApplicable = isMandatoryApplicableEmployee(salaryRuleObj, employeeId);

                        if (isMandatoryApplicable) {
                            // 强制适用人员：直接设置规则，覆盖现有规则
                            employeeFixedSalary.set(EmployeeFixedSalaryFields.SALARY_RULE, salaryRuleId);
                            toUpdate.add(employeeFixedSalary);
                            log.info("准备为员工 {} 设置强制适用薪资规则关联: {} (覆盖现有规则: {})", employeeId, salaryRuleId, currentRuleId);
                        } else if (StringUtils.isBlank(currentRuleId)) {
                            // 常规适用逻辑：只有当员工原来没有规则时才设置
                            employeeFixedSalary.set(EmployeeFixedSalaryFields.SALARY_RULE, salaryRuleId);
                            toUpdate.add(employeeFixedSalary);
                            log.info("准备为员工 {} 设置薪资规则关联: {}", employeeId, salaryRuleId);
                        } else {
                            log.debug("员工 {} 已有薪资规则 {}，跳过常规适用逻辑", employeeId, currentRuleId);
                        }
                    }
                }
            }

            // 批量更新数据
            if (!toUpdate.isEmpty()) {
                // 使用批量更新替代for循环单条更新，提高性能
                employeeFixedSalaryDao.batchUpdate(toUpdate, systemUser);
                log.info("批量更新员工固定薪资规则关联完成，更新数量: {}", toUpdate.size());
            } else {
                log.info("没有需要更新的员工固定薪资规则关联");
            }

            log.info("员工固定薪资规则关联更新完成");
        } catch (Exception e) {
            log.error("更新员工固定薪资规则关联时发生异常", e);
            throw new RuntimeException("更新员工固定薪资规则关联失败: " + e.getMessage(), e); //ignoreI18n
        }
    }
    @Override
    public void generateSalaryDataForNewPaymentSlip(String tenantId, String salaryPaymentSlipId) {
        // 1. 获取工资发放单
        IObjectData salaryPaymentSlip = salaryPaymentSlipDao.getById(tenantId, salaryPaymentSlipId);
        if (salaryPaymentSlip == null) {
            log.error("工资发放单不存在，ID: {}", salaryPaymentSlipId);
            return;
        }
        generateSalaryDataForNewPaymentSlip(tenantId, salaryPaymentSlip);
    }

    @Override
    public void generateSalaryDataForNewPaymentSlip(String tenantId, IObjectData salaryPaymentSlip) {
        String salaryPaymentSlipId = salaryPaymentSlip.getId();
        try {
            log.info("开始为新建工资发放单生成工资条数据，tenantId: {}, salaryPaymentSlipId: {}", tenantId, salaryPaymentSlipId);
            ObjectDataExt salaryPaymentSlipExt = ObjectDataExt.of(salaryPaymentSlip);
            String salaryRuleId = salaryPaymentSlipExt.getStringValue(SalaryPaymentSlipFields.SALARY_RULE);

            // 从工资发放单中获取已经调整过的时间（已经考虑了生效日期和周期开始时间的最大值）
            Long startTime = salaryPaymentSlip.get(SalaryPaymentSlipFields.START_DATE, Long.class);
            Long endTime = salaryPaymentSlip.get(SalaryPaymentSlipFields.END_DATE, Long.class);

            log.info("使用工资发放单中已调整的时间范围，开始时间: {}, 结束时间: {}", startTime, endTime);

            // 2. 获取工资规则
            IObjectData salaryRule = salaryRuleDao.getById(tenantId, salaryRuleId);
            if (salaryRule == null) {
                log.error("工资规则不存在，ID: {}", salaryRuleId);
                updateSalaryPaymentSlipStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR, "工资规则不存在");//ignoreI18n
                return;
            }

            // 3. 获取适用的员工固定薪资对象列表
            List<IObjectData> employeeFixedSalaryObjs = employeeFixedSalaryDao.getBySalaryRuleId(tenantId, salaryRuleId);
            if (CollectionUtils.isEmpty(employeeFixedSalaryObjs)) {
                log.warn("未找到适用的员工固定薪资对象，工资规则ID: {}", salaryRuleId);
                updateSalaryPaymentSlipStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_1, "未找到适用的员工固定薪资对象");//ignoreI18n
                return;
            }

            // 4. 批量生成工资条
            SalaryGenerationResult result = batchGenerateSalaryData(employeeFixedSalaryObjs, salaryRule,
                startTime, endTime, salaryPaymentSlip);

            // 5. 根据生成结果更新工资发放单状态
            updatePaymentSlipStatusByGenerationResult(tenantId, salaryPaymentSlipId, result);

            log.info("为新建工资发放单生成工资条数据完成，工资发放单ID: {}, 成功生成: {}/{}, 是否有异常: {}",
                    salaryPaymentSlipId, result.successCount, employeeFixedSalaryObjs.size(), result.hasErrorDetails);

        } catch (Exception e) {
            log.error("为新建工资发放单生成工资条数据失败，工资发放单ID: {}", salaryPaymentSlipId, e);
            // 尝试更新状态为生成失败
            try {
                updateSalaryPaymentSlipStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR, null);
            } catch (Exception updateException) {
                log.error("更新工资发放单状态为失败时发生异常", updateException);
            }
        }
    }

    @Override
    public int recalculateSalaryDataForPaymentSlip(String tenantId, String salaryPaymentSlipId) {
        log.info("开始重新计算工资发放单的工资数据，tenantId: {}, salaryPaymentSlipId: {}", tenantId, salaryPaymentSlipId);

        try {
            // 1. 获取工资发放单信息
            IObjectData salaryPaymentSlip = salaryPaymentSlipDao.getById(tenantId, salaryPaymentSlipId);
            if (salaryPaymentSlip == null) {
                log.warn("工资发放单不存在，salaryPaymentSlipId: {}", salaryPaymentSlipId);
                return 0;
            }
            // 1.5 状态判断
            String slipPayStatus = salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class);
//            if (!SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR.equals(slipPayStatus)
//                    && !SalaryPaymentSlipFields.PAY_STATUS_Options_1.equals(slipPayStatus)) {
//                log.info("工资发放单状态不正确，无法重新计算工资，当前状态: {}, ID: {}", slipPayStatus, salaryPaymentSlipId);
//                return 0;
//            }

            // 2. 获取工资发放单下的所有工资条
            List<IObjectData> salaryDataList = salaryDataDao.getBySalaryPaymentSlipId(tenantId, salaryPaymentSlipId);

            if (CollectionUtils.isEmpty(salaryDataList)) {
                log.warn("工资发放单下没有工资条数据，salaryPaymentSlipId: {}", salaryPaymentSlipId);
                return 0;
            }

            int updatedSalaryDataCount = 0;
            int updatedDetailCount = 0;
            boolean hasErrorDetails = false;

            // 批量查询所有工资项，建立缓存Map
            Map<String, IObjectData> salaryItemCache = Maps.newHashMap();
            // 创建固定工资明细缓存，避免重复查询
            Map<String, List<IObjectData>> fixedSalaryDetailCache = new HashMap<>();

            // 4. 重新计算每个工资条的明细数据，同时统计异常状态
            for (IObjectData salaryData : salaryDataList) {
                try {
                    String salaryDataId = salaryData.getId();
                    String employeeFixedSalaryId = salaryData.get(SalaryDataFields.EMPLOYEE_FIXED_SALARY, String.class);

                    // 获取时间范围
                    Long startDate = salaryData.get(SalaryDataFields.START_DATE, Long.class);
                    Long endDate = salaryData.get(SalaryDataFields.END_DATE, Long.class);

                    if (startDate == null || endDate == null) {
                        log.warn("工资条时间范围不完整，跳过重新计算，工资条ID: {}", salaryDataId);
                        continue;
                    }

                    log.info("开始重新计算工资条明细，工资条ID: {}, 时间范围: {} - {}",
                            salaryDataId, formatBeijingTime(startDate), formatBeijingTime(endDate));

                    // 5. 获取该工资条下的所有明细
                    List<IObjectData> detailDataList = salaryDetailDataDao.getBySalaryDataId(tenantId, salaryDataId);

                    // 5. 使用公共方法重新计算明细金额
                    Pair<BigDecimal, Boolean> recalculationResult = recalculateSalaryDetails(tenantId, detailDataList,
                            employeeFixedSalaryDao.getById(tenantId,employeeFixedSalaryId), startDate, endDate, salaryItemCache, fixedSalaryDetailCache);

                    BigDecimal totalAmount = recalculationResult.getKey();
                    boolean hasErrorDetailsInSalaryData = recalculationResult.getValue();

                    updatedDetailCount += detailDataList.size();



                    // 6. 更新全局异常状态标记（已经通过公共方法获得了hasErrorDetailsInSalaryData）
                    if (hasErrorDetailsInSalaryData) {
                        hasErrorDetails = true;
                    }

                    // 7. 检查工资条是否已修正，如果已修正则不更新金额和状态
                    String currentDistributionStatus = salaryData.get(SalaryDataFields.DISTRIBUTION_STATUS, String.class);
                    boolean isSalaryDataCorrected = SalaryDataFields.DISTRIBUTION_STATUS_Options_4.equals(currentDistributionStatus);

                    // 设置工资条发放状态 - 如果工资条已修正则不更新状态
                    if (!isSalaryDataCorrected) {
                        if (hasErrorDetailsInSalaryData) {
                            salaryData.set(SalaryDataFields.DISTRIBUTION_STATUS, SalaryDataFields.DISTRIBUTION_STATUS_Options_ERROR);
                            log.warn("工资条重新计算后包含异常明细，状态设置为生成异常，工资条ID: {}", salaryDataId);
                        } else {
                            salaryData.set(SalaryDataFields.DISTRIBUTION_STATUS, SalaryDataFields.DISTRIBUTION_STATUS_Options_0);
                        }
                    } else {
                        log.info("工资条已修正，保持原有状态不变，工资条ID: {}", salaryDataId);
                    }

                    if (isSalaryDataCorrected) {
                        // 如果工资条已修正，保持原有金额和状态不变
                        String currentPayableSalary = salaryData.get(SalaryDataFields.PAYABLE_SALARY, String.class);
                        log.info("工资条已修正，保持原有金额和状态不变，工资条ID: {}, 当前金额: {}, 重新计算金额: {}",
                                salaryDataId, currentPayableSalary, totalAmount);
                    } else {
                        // 工资条未修正，更新为重新计算的金额
                        salaryData.set(SalaryDataFields.PAYABLE_SALARY, totalAmount.toString());
                        log.info("工资条未修正，更新为重新计算的金额，工资条ID: {}, 金额: {}",
                                salaryDataId, totalAmount);
                    }
                    salaryDataDao.updateSalaryData(User.systemUser(tenantId), salaryData);

                    updatedSalaryDataCount++;

                    log.info("工资条重新计算完成，工资条ID: {}, 明细数量: {}, 总金额: {}, 是否有异常: {}",
                            salaryDataId, detailDataList.size(), totalAmount, hasErrorDetailsInSalaryData);

                } catch (Exception e) {
                    log.error("重新计算工资条失败，工资条ID: {}", salaryData.getId(), e);
                }
            }

            // 5. 重新检查整个工资发放单的异常状态
            // 重置hasErrorDetails，然后重新检查所有工资条的状态
            hasErrorDetails = false;
            for (IObjectData salaryData : salaryDataList) {
                String distributionStatus = salaryData.get(SalaryDataFields.DISTRIBUTION_STATUS, String.class);
                if (SalaryDataFields.DISTRIBUTION_STATUS_Options_ERROR.equals(distributionStatus)) {
                    hasErrorDetails = true;
                    break;
                }
            }
            log.info("重新计算完成后，工资发放单是否有异常工资条: {}", hasErrorDetails);

            // 6. 根据重新计算过程中统计的异常状态，更新工资发放单状态
            if (hasErrorDetails) {
                // 如果有工资条生成异常，将工资发放单状态设置为生成异常
                updateSalaryPaymentSlipStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR, null);
                log.warn("重新计算后发现工资条异常，工资发放单状态设置为生成异常，工资发放单ID: {}", salaryPaymentSlipId);
            } else {
                // 没有异常，检查当前状态是否为生成异常，如果是则恢复为未发放状态
                String currentStatus = salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class);
                if (SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR.equals(currentStatus)) {
                    updateSalaryPaymentSlipStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_1, null);
                    log.info("重新计算后无工资条异常，工资发放单状态从生成异常恢复为未发放，工资发放单ID: {}", salaryPaymentSlipId);
                }
            }

            log.info("工资发放单工资数据重新计算完成，salaryPaymentSlipId: {}, 更新工资条数量: {}, 更新明细数量: {}, 是否有异常: {}",
                    salaryPaymentSlipId, updatedSalaryDataCount, updatedDetailCount, hasErrorDetails);

            return updatedSalaryDataCount;

        } catch (Exception e) {
            log.error("重新计算工资发放单工资数据失败，salaryPaymentSlipId: {}", salaryPaymentSlipId, e);
            throw new RuntimeException("重新计算工资数据失败: " + e.getMessage(), e); //ignoreI18n
        }
    }

    /**
     * 重新计算工资明细的核心方法
     *
     * @param tenantId 租户ID
     * @param detailDataList 要重新计算的明细列表
     * @param employeeFixedSalaryObj 员工固定薪资ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param salaryItemCache 工资项缓存
     * @param fixedSalaryDetailCache 固定工资明细缓存
     * @return Pair<BigDecimal, Boolean> 第一个元素是重新计算后的总金额，第二个元素是是否有异常明细
     */
    private Pair<BigDecimal, Boolean> recalculateSalaryDetails(String tenantId,
                                                               List<IObjectData> detailDataList,
                                                               IObjectData employeeFixedSalaryObj,
                                                               long startTime,
                                                               long endTime,
                                                               Map<String, IObjectData> salaryItemCache,
                                                               Map<String, List<IObjectData>> fixedSalaryDetailCache) {

        BigDecimal totalAmount = BigDecimal.ZERO;
        boolean hasErrorDetails = false;

        if (detailDataList == null || detailDataList.isEmpty() || employeeFixedSalaryObj == null) {
            return Pair.of(totalAmount, hasErrorDetails);
        }
        String internalEmployeeId = employeeFixedSalaryObj.get(EmployeeFixedSalaryFields.EMPLOYEE,String.class);
        String externalEmployeeId = employeeFixedSalaryObj.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL,String.class);
        boolean isExtEmployee = SalaryRecordTypeUtil.isExternalEmployee(internalEmployeeId, externalEmployeeId);
        String owner = isExtEmployee ? externalEmployeeId : internalEmployeeId;

        for (IObjectData detailData : detailDataList) {
            try {
                // 获取工资项
                IObjectData salaryItem = getSalaryItemFromCache(detailData, salaryItemCache, tenantId);
                if (salaryItem == null) {
                    continue;
                }
                // 构建薪资计算上下文
                SalaryContext salaryContext = SalaryContext.builder()
                        .tenantId(tenantId)
                        .owner(owner)
                        .startTime(detailData.get(SalaryDetailDataFields.START_DATE,Long.class))
                        .endTime(detailData.get(SalaryDetailDataFields.END_DATE,Long.class))
                        .extDataMap(Maps.newHashMap())
                        .extDataNameMap(Maps.newHashMap())
                        .stopWatch(StopWatch.create(this.getClass().getSimpleName()))
                        .build();
                // 使用公共方法处理明细
                ProcessResult processResult = processSingleSalaryDetail(detailData, salaryItem, salaryContext,
                        employeeFixedSalaryObj.getId(), fixedSalaryDetailCache);

                // 累加金额
                totalAmount = totalAmount.add(processResult.getAmount());

                // 更新异常状态
                if (processResult.isHasError()) {
                    hasErrorDetails = true;
                }

                // 保存更新后的明细（跳过的明细不需要保存）
                if (!processResult.isShouldSkip()) {
                    salaryDetailDataDao.update(User.systemUser(tenantId), detailData);
                }

            } catch (Exception e) {
                hasErrorDetails = true;
                log.error("处理工资明细失败，明细ID: {}", detailData.getId(), e);
            }
        }

        return Pair.of(totalAmount, hasErrorDetails);
    }

    /**
     * 计算工资项金额的核心方法
     * 支持固定值和公式计算两种类型，包含小数位数和舍位方式处理
     *
     * @param tenantId              租户ID
     * @param salaryItem            工资项对象
     * @param salaryContext         薪资计算上下文（owner字段为员工ID，用于KPI计算）
     * @param employeeFixedSalaryId 员工固定薪资ID（用于固定值查询）
     * @param fixedSalaryDetailCache 固定工资明细缓存，避免重复查询
     * @return Pair<String, BigDecimal> 第一个元素是计算公式（如果是公式类型），第二个元素是计算结果
     */
    private Pair<String, BigDecimal> calculateSalaryItemAmount(String tenantId, IObjectData salaryItem,
            SalaryContext salaryContext, String employeeFixedSalaryId,
            Map<String, List<IObjectData>> fixedSalaryDetailCache) {
        try {
            ObjectDataExt salaryItemExt = ObjectDataExt.of(salaryItem);
            String valueType = salaryItemExt.getStringValue(SalaryItemFields.VALUE_TYPE);

            // 获取小数位数和舍位方式
            String decimalPlacesStr = salaryItemExt.getStringValue(SalaryItemFields.DECIMAL_PLACES);
            String roundingMethodStr = salaryItemExt.getStringValue(SalaryItemFields.ROUNDING_METHOD);

            int decimalPlaces = StringUtils.isNotBlank(decimalPlacesStr) ? Integer.parseInt(decimalPlacesStr) : 2;
            int roundingMode = getRoundingMode(roundingMethodStr);

            BigDecimal calculatedAmount = null;
            String calculationFormula = null;

            if (SalaryItemFields.VALUE_TYPE_Options_1.equals(valueType)) {
                // 固定值类型 - 从员工固定薪资明细中获取（使用缓存）
                if (StringUtils.isNotBlank(employeeFixedSalaryId)) {
                    // 从缓存中获取固定工资明细，如果缓存中没有则查询数据库并放入缓存
                    List<IObjectData> employeeFixedSalaryDetailObjs = fixedSalaryDetailCache.get(employeeFixedSalaryId);
                    if (employeeFixedSalaryDetailObjs == null) {
                        employeeFixedSalaryDetailObjs = employeeFixedSalaryDetailDao
                                .getByEmployeeFixedSalaryMainId(tenantId, employeeFixedSalaryId);
                        if (employeeFixedSalaryDetailObjs != null) {
                            fixedSalaryDetailCache.put(employeeFixedSalaryId, employeeFixedSalaryDetailObjs);
                            log.debug("固定工资明细已加载到缓存，员工固定薪资ID: {}", employeeFixedSalaryId);
                        } else {
                            employeeFixedSalaryDetailObjs = Lists.newArrayList();
                            fixedSalaryDetailCache.put(employeeFixedSalaryId, employeeFixedSalaryDetailObjs);
                        }
                    } else {
                        log.debug("从缓存中获取固定工资明细，员工固定薪资ID: {}", employeeFixedSalaryId);
                    }

                    String fixedAmount = Optional.ofNullable(employeeFixedSalaryDetailObjs)
                            .map(list -> list.stream()
                                    .filter(detail -> detail.get(EmployeeFixedSalaryDetailFields.SALARY_ITEM)
                                            .equals(salaryItem.getId()))
                                    .findFirst().orElse(null))
                            .map(detail -> detail.get(EmployeeFixedSalaryDetailFields.AMOUNT, String.class))
                            .orElse("0");

                    if (StringUtils.isNotBlank(fixedAmount) && !"0".equals(fixedAmount)) {
                        try {
                            calculatedAmount = new BigDecimal(fixedAmount);
                            log.debug("获取固定值成功，工资项ID: {}, 固定金额: {}", salaryItem.getId(), fixedAmount);
                        } catch (NumberFormatException e) {
                            log.warn("固定值格式错误，工资项ID: {}, 固定金额: {}", salaryItem.getId(), fixedAmount, e);
                            calculatedAmount = BigDecimal.ZERO;
                        }
                    } else {
                        calculatedAmount = BigDecimal.ZERO;
                        log.debug("未找到固定值或固定值为0，工资项ID: {}", salaryItem.getId());
                    }
                } else {
                    log.warn("员工固定薪资ID为空，无法获取固定值，工资项ID: {}", salaryItem.getId());
                    calculatedAmount = BigDecimal.ZERO;
                }

            } else if (SalaryItemFields.VALUE_TYPE_Options_2.equals(valueType)) {
                // 公式计算类型
                Pair<String, BigDecimal> formulaResult = calculateFormulaBasedAmount(tenantId, salaryItem, salaryContext);
                if (formulaResult != null && formulaResult.getValue() != null) {
                    calculatedAmount = formulaResult.getValue();
                    calculationFormula = formulaResult.getKey();
                } else {
                    calculatedAmount = null;
                }
                // 应用小数位数和舍位方式
                if (calculatedAmount != null) {
                    calculatedAmount = calculatedAmount.setScale(decimalPlaces, roundingMode);
                    log.debug("工资项计算完成，工资项ID: {}, 金额: {}, 小数位数: {}, 舍位方式: {}",
                            salaryItem.getId(), calculatedAmount, decimalPlaces, roundingMode);
                }
            }


            return Pair.of(calculationFormula, calculatedAmount);

        } catch (Exception e) {
            // 将异常向上抛出，让上层统一处理异常状态设置和日志记录
            throw new RuntimeException("计算工资项金额异常，工资项ID: " + salaryItem.getId(), e); //ignoreI18n
        }
    }

    /**
     * 计算基于公式的工资项金额
     *
     * @param tenantId      租户ID
     * @param salaryItem    工资项对象
     * @param salaryContext 薪资计算上下文
     * @return Pair<String, BigDecimal> 第一个元素是计算公式，第二个元素是计算结果
     */
    private Pair<String, BigDecimal> calculateFormulaBasedAmount(String tenantId, IObjectData salaryItem,
            SalaryContext salaryContext) {
        ObjectDataExt salaryItemExt = ObjectDataExt.of(salaryItem);

        // 获取计算公式（放在try外面，这样catch块也能访问）
        String calculationFormula = salaryItemExt.getStringValue(SalaryItemFields.CALCULATION_FORMULA);
        if (StringUtils.isBlank(calculationFormula)) {
            log.warn("工资项缺少计算公式，工资项ID: {}", salaryItem.getId());
            return null;
        }

        try {

            // 构建表达式DTO
            ExpressionDTO expressionDTO = new ExpressionDTO();
            expressionDTO.setExpression(calculationFormula);
            expressionDTO.setExtFields(Lists.newArrayList());
//            String decimalPlacesStr = salaryItemExt.getStringValue(SalaryItemFields.DECIMAL_PLACES);
            //小数位+1 方便后续再处理位数  改成100 了 后续再处理
//            if(StringUtils.isNotBlank(decimalPlacesStr)){
//                expressionDTO.setDecimalPlaces(Integer.parseInt(decimalPlacesStr)+1);
//            }else{
//                expressionDTO.setDecimalPlaces(3);
//            }
            expressionDTO.setDecimalPlaces(10);

            // 获取并计算KPI指标
            List<String> kpiIds = salaryItemExt.getDimensionValues(SalaryItemFields.FORMULA_INCLUDES_KPI);
//            if (kpiIds == null || kpiIds.isEmpty()) {
//                log.warn("工资项中未配置指标，工资项ID: {}", salaryItem.getId());
//                return null;
//            }

            // 初始化上下文的扩展数据映射
            if (salaryContext.getExtDataMap() == null) {
                salaryContext.setExtDataMap(Maps.newHashMap());
            }
            if (salaryContext.getExtDataNameMap() == null) {
                salaryContext.setExtDataNameMap(Maps.newHashMap());
            }

            // 计算KPI指标值
            Map<String, String> kpiVariableValueMap = Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(kpiIds)){
                kpiIds = kpiIds.stream().distinct().collect(Collectors.toList());
                List<IObjectData> kpiObjs = salaryKPIDao.getbyKpiIds(tenantId, kpiIds);
                if(CollectionUtils.isNotEmpty(kpiObjs)){
                    kpiObjs = kpiObjs.stream().distinct().collect(Collectors.toList());
                }
                for (IObjectData kpiObj : kpiObjs) {
                    MetricCalculateResult calculate = salaryKPIFactory.calculate(salaryContext, kpiObj);
                    String key = SalaryExpressionCalcServiceImpl.KPI_EXT_START + kpiObj.getId();

                    // 存储KPI计算结果
                    kpiVariableValueMap.put("kpi_" + kpiObj.getId(), calculate.getValue());
                    salaryContext.getExtDataMap().put(key, calculate.getValue());
                    salaryContext.getExtDataNameMap().put(key, kpiObj.getName());
                    expressionDTO.getExtFields().add(
                            ExpressionDTO.FormVariableDTO.builder().fieldName(key).value(calculate.getValue()).build());
                }
            }

            // 执行公式计算
            Pair<String, Object> calculateResult = salaryExpressionCalcService
                    .calculateWithExpression(expressionDTO, salaryContext);

            if (calculateResult != null && calculateResult.getValue() != null) {
                Object resultValue = calculateResult.getValue();
                BigDecimal calculatedAmount;

                // 处理不同类型的计算结果
                if (resultValue instanceof BigDecimal) {
                    calculatedAmount = (BigDecimal) resultValue;
                } else if (resultValue instanceof Number) {
                    calculatedAmount = new BigDecimal(resultValue.toString());
                } else {
                    calculatedAmount = new BigDecimal(resultValue.toString());
                }

                log.debug("公式计算成功，工资项ID: {}, 公式: {}, 计算结果: {}",
                        salaryItem.getId(), calculationFormula, calculatedAmount);

                // 返回公式和计算结果的Pair
                return Pair.of(calculateResult.getKey(), calculatedAmount);
            } else {
                log.warn("公式计算失败，工资项ID: {}, 公式: {}, 计算结果为空",
                        salaryItem.getId(), calculationFormula);
                return null;
            }

        } catch (Exception e) {
            // 将异常向上抛出，让上层统一处理异常状态设置和日志记录
            throw new RuntimeException("公式计算异常，工资项ID: " + salaryItem.getId() + //ignoreI18n
                    ", 公式: " + calculationFormula, e);//ignoreI18n
        }
    }

    /**
     * 批量查询工资项并构建缓存Map
     *
     * @param tenantId       租户ID
     * @param salaryItemIds  工资项ID列表
     * @return 工资项ID到工资项对象的映射
     */
    private Map<String, IObjectData> buildSalaryItemCache(String tenantId, Collection<String> salaryItemIds) {
        Map<String, IObjectData> salaryItemCache = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(salaryItemIds)) {
            List<IObjectData> salaryItems = salaryItemDao.getbyIds(tenantId, Lists.newArrayList(salaryItemIds));
            if (!CollectionUtils.isEmpty(salaryItems)) {
                for (IObjectData item : salaryItems) {
                    salaryItemCache.put(item.getId(), item);
                }
            }
            log.debug("批量查询工资项完成，请求数量: {}, 实际查询到数量: {}", salaryItemIds.size(), salaryItemCache.size());
        }
        return salaryItemCache;
    }

    /**
     * 通用的薪资项目处理逻辑 - 使用公共SalaryItem类
     *
     * @param tenantId 租户ID
     * @param headers 表头列表
     * @param aggregatedValues 聚合数据
     * @param headerDescriptions 表头描述映射
     * @return 薪资项目列表
     */
    private List<SalaryItem> processSalaryItems(String tenantId, List<String> headers,
            Map<String, String> aggregatedValues, Map<String, IFieldDescribe> headerDescriptions) {
        List<SalaryItem> salaryItems = new ArrayList<>();

        // 根据表头创建薪资项目
        for (String header : headers) {
            IFieldDescribe fieldDescribe = headerDescriptions.get(header);
            if (fieldDescribe != null) {
                String amount;
                // 包含 kpi_保留kpi_xxx 来取数据
                if (header.contains("kpi_")) {
                    amount = aggregatedValues.get(header.substring(header.indexOf("kpi_")));
                } else {
                    amount = aggregatedValues.get(header);
                }

                SalaryItem salaryItem = new SalaryItem(
                    fieldDescribe.getLabel(),
                    header,
                    fieldDescribe.get("unit", String.class),
                    amount
                );
                salaryItems.add(salaryItem);
            }
        }

        // 补充 aggregatedValues 有 headers 里没有的数据
        List<SalaryItem> otherSalaryItems = new ArrayList<>();
        for (Map.Entry<String, String> entry : aggregatedValues.entrySet()) {
            if (!headers.contains(entry.getKey()) && entry.getValue() != null
                    && entry.getKey().startsWith("si_")) { // 工资项金额聚合结果，以"si_"开头
                SalaryItem salaryItem = new SalaryItem(
                    entry.getKey(),
                    entry.getKey(),
                    "元", // ignoreI18n
                    entry.getValue()
                );
                otherSalaryItems.add(salaryItem);
            }
        }

        // 重新赋值 Name
        if (CollectionUtils.isNotEmpty(otherSalaryItems)) {
            // 查找所有的工资项目 通过id
            List<String> salaryItemIds = otherSalaryItems.stream()
                    .map(SalaryItem::extractSalaryItemId)
                    .collect(Collectors.toList());
            Map<String, IObjectData> salaryItemMap = buildSalaryItemCache(tenantId, salaryItemIds);

            // 重新赋值 Name
            for (SalaryItem salaryItem : otherSalaryItems) {
                String salaryItemId = salaryItem.extractSalaryItemId();
                IObjectData salaryItemObj = salaryItemMap.get(salaryItemId);
                if (salaryItemObj != null) {
                    salaryItem.setName(salaryItemObj.getName());
                }
            }
        }

        // 将其他工资项金额聚合结果添加到salaryItems中
        salaryItems.addAll(otherSalaryItems);
        return salaryItems;
    }

    /**
     * 通用的表头获取逻辑
     *
     * @param salaryPaymentSlipObj 工资发放单对象
     * @param salaryRuleObj 薪资规则对象
     * @return 表头列表
     */
    private List<String> getCommonSalaryHeaders(IObjectData salaryPaymentSlipObj, IObjectData salaryRuleObj) {
        List<String> headers = new ArrayList<>();

        // 优先从工资发放单获取表头
        if (salaryPaymentSlipObj != null) {
            headers = getSalaryTableHeaders(salaryPaymentSlipObj);
        }

        // 如果没有从发放单获取到表头，尝试从薪资规则获取
        if (headers.isEmpty() && salaryRuleObj != null) {
            String headerStr = salaryRuleObj.get(SalaryRuleFields.SALARY_STATEMENT_HEADER, String.class);
            if (StringUtils.isNotBlank(headerStr)) {
                try {
                    headers = JSON.parseArray(headerStr, String.class);
                } catch (Exception e) {
                    log.warn("解析薪资规则表头失败, salaryRuleId: {}, headerStr: {}", salaryRuleObj.getId(), headerStr, e);
                }
            }
        }

        return headers;
    }

    /**
     * 表头解析结果
     */
    private static class HeaderParseResult {
        final List<String> salaryItemIds = new ArrayList<>();
        final List<String> kpiIds = new ArrayList<>();
    }

    /**
     * 解析表头，提取工资项ID和KPI ID
     */
    private HeaderParseResult parseHeaders(List<String> headers) {
        HeaderParseResult result = new HeaderParseResult();

        for (String header : headers) {
            if (header.startsWith("si_")) {
                // 工资项格式: si_xxxxxx
                String salaryItemId = header.substring(3);
                if (StringUtils.isNotBlank(salaryItemId)) {
                    result.salaryItemIds.add(salaryItemId);
                }
            } else if (header.contains("kpi_")) {
                // KPI指标格式: kpi_xxxxxx 或 xxxkpi_xxxxxx
                String kpiId = header.substring(header.indexOf("kpi_") + 4);
                if (StringUtils.isNotBlank(kpiId)) {
                    result.kpiIds.add(kpiId);
                }
            }
        }

        return result;
    }

    /**
     * 构建KPI缓存
     */
    private Map<String, IObjectData> buildKpiCache(String tenantId, List<String> kpiIds) {
        Map<String, IObjectData> kpiMap = new HashMap<>();
        if (!kpiIds.isEmpty()) {
            List<IObjectData> kpiItems = salaryKPIDao.getbyKpiIds(tenantId, kpiIds);
            if (!CollectionUtils.isEmpty(kpiItems)) {
                for (IObjectData item : kpiItems) {
                    kpiMap.put(item.getId(), item);
                }
            }
            log.debug("批量查询KPI完成，请求数量: {}, 实际查询到数量: {}", kpiIds.size(), kpiMap.size());
        }
        return kpiMap;
    }

    /**
     * 创建字段描述的基础模板
     */
    private static final String FIELD_DESCRIBE_TEMPLATE =
        "{\"describe_api_name\":\"SalaryDetailDataObj\",\"is_index\":false,\"is_active\":true," +
        "\"is_encrypted\":false,\"auto_adapt_places\":false,\"quote_field_type\":\"text\"," +
        "\"remove_mask_roles\":{},\"is_unique\":false,\"label\":\"取值方式\",\"type\":\"quote\"," + //ignoreI18n
        "\"is_required\":false,\"api_name\":\"value_type\",\"define_type\":\"package\"," +
        "\"is_single\":false,\"is_show_mask\":false,\"help_text\":\"\",\"status\":\"new\"}";

    /**
     * 为单个表头创建字段描述
     */
    private IFieldDescribe createFieldDescribe(String header, Map<String, IObjectData> salaryItemMap,
            Map<String, IObjectData> kpiMap) {
        Map<String, Object> fieldDescribeMap = JSON.parseObject(FIELD_DESCRIBE_TEMPLATE);
        QuoteFieldDescribe fieldDescribe = new QuoteFieldDescribe(fieldDescribeMap);
        fieldDescribe.setApiName(header);

        if (header.startsWith("si_")) {
            return createSalaryItemFieldDescribe(fieldDescribe, header, salaryItemMap);
        } else if (header.startsWith("kpi_") || header.contains("kpi_")) {
            return createKpiFieldDescribe(fieldDescribe, header, kpiMap);
        }

        // 其他类型的表头暂不处理
        return null;
    }

    /**
     * 创建工资项字段描述
     */
    private IFieldDescribe createSalaryItemFieldDescribe(QuoteFieldDescribe fieldDescribe, String header,
            Map<String, IObjectData> salaryItemMap) {
        String salaryItemId = header.substring(3);
        IObjectData salaryItem = salaryItemMap.get(salaryItemId);

        if (salaryItem != null) {
            fieldDescribe.setLabel(salaryItem.getName());
            fieldDescribe.set("unit", "元"); // ignoreI18n
        }

        return fieldDescribe;
    }

    /**
     * 创建KPI字段描述
     */
    private IFieldDescribe createKpiFieldDescribe(QuoteFieldDescribe fieldDescribe, String header,
            Map<String, IObjectData> kpiMap) {
        // 统一处理：从 "kpi_" 之后提取 KPI ID
        String kpiId = header.substring(header.indexOf("kpi_") + 4);

        IObjectData kpiItem = kpiMap.get(kpiId);
        if (kpiItem != null) {
            fieldDescribe.setLabel(kpiItem.getName());
            fieldDescribe.set(SalaryKPIFields.UNIT, kpiItem.get(SalaryKPIFields.UNIT, String.class));
        }

        return fieldDescribe;
    }

    /**
     * 工资条生成结果
     */
    private static class SalaryGenerationResult {
        final int successCount;
        final boolean hasErrorDetails;

        public SalaryGenerationResult(int successCount, boolean hasErrorDetails) {
            this.successCount = successCount;
            this.hasErrorDetails = hasErrorDetails;
        }
    }

    /**
     * 批量生成工资条的通用方法
     *
     * @param employeeFixedSalaryObjs 员工固定薪资对象列表
     * @param salaryRule 薪资规则
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param salaryPaymentSlip 工资发放单（可选）
     * @return 生成结果
     */
    private SalaryGenerationResult batchGenerateSalaryData(List<IObjectData> employeeFixedSalaryObjs,
            IObjectData salaryRule, long startTime, long endTime, IObjectData salaryPaymentSlip) {
        int successCount = 0;
        boolean hasErrorDetails = false;

        for (IObjectData employeeFixedSalaryObj : employeeFixedSalaryObjs) {
            try {
                // 生成工资条
                IObjectData salaryData = generateSalaryData(employeeFixedSalaryObj, salaryRule, startTime, endTime, salaryPaymentSlip);
                successCount++;

                // 检查生成的工资条状态，如果有异常则标记
                if (salaryData != null) {
                    String distributionStatus = salaryData.get(SalaryDataFields.DISTRIBUTION_STATUS, String.class);
                    if (SalaryDataFields.DISTRIBUTION_STATUS_Options_ERROR.equals(distributionStatus)) {
                        hasErrorDetails = true;
                    }
                }
            } catch (ValidateException e) {
                // 业务异常，记录日志但继续处理其他员工
                log.info("生成工资条业务异常，员工固定薪资ID: {}, 员工ID: {}, 外部员工ID: {}, 错误: {}",
                        employeeFixedSalaryObj.getId(),
                        employeeFixedSalaryObj.get(EmployeeFixedSalaryFields.EMPLOYEE),
                        employeeFixedSalaryObj.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL),
                        e.getMessage());
                hasErrorDetails = true;
            } catch (Exception e) {
                // 系统异常，记录错误日志但继续处理其他员工
                log.error("生成工资条系统异常，员工固定薪资ID: {}, 员工ID: {}, 外部员工ID: {}",
                        employeeFixedSalaryObj.getId(),
                        employeeFixedSalaryObj.get(EmployeeFixedSalaryFields.EMPLOYEE),
                        employeeFixedSalaryObj.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL), e);
                hasErrorDetails = true;
            }
        }

        return new SalaryGenerationResult(successCount, hasErrorDetails);
    }

    /**
     * 根据生成结果更新工资发放单状态
     *
     * @param tenantId 租户ID
     * @param salaryPaymentSlipId 工资发放单ID
     * @param result 生成结果
     */
    private void updatePaymentSlipStatusByGenerationResult(String tenantId, String salaryPaymentSlipId,
            SalaryGenerationResult result) {
        if (result.hasErrorDetails) {
            // 如果有工资条生成异常，将工资发放单状态设置为生成异常
            updateSalaryPaymentSlipStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR, null);
            log.warn("工资发放单包含生成异常的工资条，状态设置为生成异常，工资发放单ID: {}", salaryPaymentSlipId);
        } else {
            // 没有异常，设置为已生成
            updateSalaryPaymentSlipStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_1, null);
        }
    }

    /**
     * 获取舍位方式对应的BigDecimal舍入模式
     *
     * @param roundingMethodStr 舍位方式字符串
     * @return BigDecimal舍入模式
     */
    private int getRoundingMode(String roundingMethodStr) {
        if (StringUtils.isBlank(roundingMethodStr)) {
            return BigDecimal.ROUND_HALF_UP; // 默认四舍五入
        }

        switch (roundingMethodStr) {
            case SalaryItemFields.ROUNDING_METHOD_Options_1: // 四舍五入
                return BigDecimal.ROUND_HALF_UP;
            case SalaryItemFields.ROUNDING_METHOD_Options_2: // 进位
                return BigDecimal.ROUND_UP;
            case SalaryItemFields.ROUNDING_METHOD_Options_3: // 舍位
                return BigDecimal.ROUND_DOWN;
            default:
                return BigDecimal.ROUND_HALF_UP;
        }
    }

    /**
     * 计算工资项金额的核心方法（不使用缓存的重载方法）
     *
     * @param tenantId              租户ID
     * @param salaryItem            工资项对象
     * @param salaryContext         薪资计算上下文（owner字段为员工ID，用于KPI计算）
     * @param employeeFixedSalaryId 员工固定薪资ID（用于固定值查询）
     * @return Pair<String, BigDecimal> 第一个元素是计算公式（如果是公式类型），第二个元素是计算结果
     */
    private Pair<String, BigDecimal> calculateSalaryItemAmount(String tenantId, IObjectData salaryItem,
            SalaryContext salaryContext, String employeeFixedSalaryId) {
        // 创建临时缓存并调用带缓存的方法
        Map<String, List<IObjectData>> tempCache = new HashMap<>();
        return calculateSalaryItemAmount(tenantId, salaryItem, salaryContext, employeeFixedSalaryId, tempCache);
    }

    /**
     * 检查工资发放单下是否有工资条处于生成异常状态
     * 优化后直接查询工资条状态，避免遍历所有工资明细
     *
     * @param tenantId 租户ID
     * @param salaryPaymentSlipId 工资发放单ID
     * @return true表示有异常工资条，false表示没有异常工资条
     */
    private boolean checkForErrorSalaryDetails(String tenantId, String salaryPaymentSlipId) {
        try {
            // 直接获取工资发放单下的所有工资条，检查状态字段
            List<IObjectData> salaryDataList = salaryDataDao.getBySalaryPaymentSlipId(tenantId, salaryPaymentSlipId);

            if (CollectionUtils.isEmpty(salaryDataList)) {
                return false;
            }

            // 检查工资条的发放状态
            for (IObjectData salaryData : salaryDataList) {
                String distributionStatus = salaryData.get(SalaryDataFields.DISTRIBUTION_STATUS, String.class);

                // 如果发现有生成异常状态的工资条，返回true
                if (SalaryDataFields.DISTRIBUTION_STATUS_Options_ERROR.equals(distributionStatus)) {
                    log.debug("发现生成异常的工资条，工资条ID: {}", salaryData.getId());
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("检查工资条异常状态时发生错误，工资发放单ID: {}", salaryPaymentSlipId, e);
            // 发生异常时，为了安全起见，返回true
            return true;
        }
    }

    /**
     * 重新计算单个工资明细
     * 从calculateSalaryDetailDatas方法中抽取的单个明细计算逻辑
     *
     * @param detailData 工资明细数据
     * @param employeeFixedSalaryObj 员工固定薪资对象
     * @param salaryRuleObj 工资规则对象
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param salaryItemCache 工资项缓存，避免重复查询
     * @param fixedSalaryDetailCache 固定工资明细缓存，避免重复查询
     * @return 重新计算后的工资明细
     */
    private IObjectData recalculateSingleSalaryDetail(IObjectData detailData, IObjectData employeeFixedSalaryObj,
                                                     IObjectData salaryRuleObj, long startTime, long endTime,
                                                     Map<String, IObjectData> salaryItemCache,
                                                     Map<String, List<IObjectData>> fixedSalaryDetailCache) {
        try {
            ObjectDataExt employeeFixedSalaryObjExt = ObjectDataExt.of(employeeFixedSalaryObj);
            String tenantId = employeeFixedSalaryObjExt.getTenantId();
            String employeeId = employeeFixedSalaryObjExt.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE);
            String employeeExternalId = employeeFixedSalaryObjExt.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);

            // 获取工资项ID
            String salaryItemId = detailData.get(SalaryDetailDataFields.SALARY_ITEM, String.class);
            if (StringUtils.isBlank(salaryItemId)) {
                log.warn("工资明细缺少工资项ID，跳过计算，明细ID: {}", detailData.getId());
                return detailData;
            }

            // 从缓存中获取工资项，如果缓存中没有则查询数据库并放入缓存
            IObjectData salaryItem = salaryItemCache.get(salaryItemId);
            if (salaryItem == null) {
                salaryItem = salaryItemDao.getById(tenantId, salaryItemId);
                if (salaryItem != null) {
                    salaryItemCache.put(salaryItemId, salaryItem);
                    log.debug("工资项已加载到缓存，工资项ID: {}", salaryItemId);
                } else {
                    log.warn("工资项不存在，跳过计算，工资项ID: {}", salaryItemId);
                    return detailData;
                }
            } else {
                log.debug("从缓存中获取工资项，工资项ID: {}", salaryItemId);
            }

            // 构建薪资上下文
            SalaryContext salaryContext = SalaryContext.builder()
                    .endTime(endTime)
                    .startTime(startTime)
                    .owner(employeeId != null ? employeeId : employeeExternalId) // 这里是员工ID，用于KPI计算
                    .tenantId(tenantId)
                    .extDataMap(Maps.newHashMap())
                    .extDataNameMap(Maps.newHashMap())
                    .stopWatch(StopWatch.create(this.getClass().getSimpleName()))
                    .build();
            // 使用统一的核心计算方法
            try {
                Pair<String, BigDecimal> calculationResult = calculateSalaryItemAmount(tenantId, salaryItem, salaryContext,
                        employeeFixedSalaryObjExt.getId(), fixedSalaryDetailCache);

                if (calculationResult != null && calculationResult.getValue() != null) {
                    BigDecimal calculatedAmount = calculationResult.getValue();
                    String calculationFormula = calculationResult.getKey();

                    detailData.set(SalaryDetailDataFields.AMOUNT, calculatedAmount.toString());
                    // 设置发放状态为未发放（正常状态）
                    detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_0);

                    // 对于公式类型，设置公式相关字段
                    ObjectDataExt salaryItemExt = ObjectDataExt.of(salaryItem);
                    if (SalaryItemFields.VALUE_TYPE_Options_2.equals(salaryItemExt.getStringValue(SalaryItemFields.VALUE_TYPE))) {
                        // 设置格式化后的计算公式
                        String formattedFormula = formatFormula(salaryItemExt.getStringValue(SalaryItemFields.CALCULATION_FORMULA),
                                salaryContext.getExtDataNameMap());
                        detailData.set(SalaryDetailDataFields.CALCULATION_FORMULA, formattedFormula);

                        // 设置公式赋值（原始计算公式）
                        if (StringUtils.isNotBlank(calculationFormula)) {
                            detailData.set(SalaryDetailDataFields.FORMULA_ASSIGNMENT, calculationFormula);
                        }

                        // 设置公式变量
                        if (!MapUtils.isNullOrEmpty(salaryContext.getExtDataMap())) {
                            detailData.set(SalaryDetailDataFields.FORMULA_VARIABLE, JSON.toJSONString(salaryContext.getExtDataMap()));
                        }
                    }

                    log.info("单个工资明细重新计算成功，明细ID: {}, 工资项ID: {}, 计算结果: {}",
                            detailData.getId(), salaryItemId, calculatedAmount);
                } else {
                    // 计算结果为空，设置为生成异常状态
                    detailData.set(SalaryDetailDataFields.AMOUNT, "0");
                    detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.PAY_STATUS_Options_ERROR);
                    log.warn("单个工资明细重新计算结果为空，设置为生成异常状态，明细ID: {}, 工资项ID: {}",
                            detailData.getId(), salaryItemId);
                }
            } catch (Exception e) {
                // 公式计算异常，设置工资明细状态为生成异常
                detailData.set(SalaryDetailDataFields.AMOUNT, "0");
                detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.PAY_STATUS_Options_ERROR);

                // 记录详细的异常信息，包括工资项名称和员工信息
                String salaryItemName = salaryItem.getName();
                String errorDetail = e.getCause() != null ? e.getCause().getMessage() : e.getMessage();
                log.error("单个工资明细重新计算异常，设置为生成异常状态 - 工资项: {}({}), 员工ID: {}, 明细ID: {}, 详细错误: {}",
                        salaryItemName, salaryItemId, employeeId != null ? employeeId : employeeExternalId,
                        detailData.getId(), errorDetail, e);
            }

            return detailData;

        } catch (Exception e) {
            log.error("重新计算单个工资明细时发生系统异常，明细ID: {}", detailData.getId(), e);
            // 发生系统异常时，设置为生成异常状态
            detailData.set(SalaryDetailDataFields.AMOUNT, "0");
            detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.PAY_STATUS_Options_ERROR);
            return detailData;
        }
    }

//    /**
//     * 重新计算单个工资明细（不使用缓存的重载方法）
//     *
//     * @param detailData 工资明细数据
//     * @param employeeFixedSalaryObj 员工固定薪资对象
//     * @param salaryRuleObj 工资规则对象
//     * @param startTime 开始时间
//     * @param endTime 结束时间
//     * @return 重新计算后的工资明细
//     */
//    private IObjectData recalculateSingleSalaryDetail(IObjectData detailData, IObjectData employeeFixedSalaryObj,
//                                                     IObjectData salaryRuleObj, long startTime, long endTime) {
//        // 创建临时缓存并调用带缓存的方法
//        Map<String, IObjectData> tempSalaryItemCache = new HashMap<>();
//        Map<String, List<IObjectData>> tempFixedSalaryDetailCache = new HashMap<>();
//        return recalculateSingleSalaryDetail(detailData, employeeFixedSalaryObj, salaryRuleObj, startTime, endTime,
//                tempSalaryItemCache, tempFixedSalaryDetailCache);
//    }

    /**
     * 处理薪资任务的核心业务逻辑
     * 从Consumer中移动到Service，使业务逻辑更清晰
     * 添加分布式锁机制，确保同一薪资规则任务的幂等性
     */
    @Override
    public void handleSalaryTask(SalaryTaskMessage salaryTaskMessage) throws Exception {
        String salaryRuleId = salaryTaskMessage.getSalaryRuleId();
        String tenantId = salaryTaskMessage.getTenantId();
        Integer flag = salaryTaskMessage.getFlag();
        String dateStr = salaryTaskMessage.getDateStr();

        // 构建锁的键，确保同一薪资规则的同一类型任务在同一时间只能被一个实例处理
        String taskType = flag == 0 ? "detail" : "payment";
        String lockKey = SALARY_TASK_LOCK_PREFIX + taskType + "-" + tenantId + "-" + salaryRuleId;
        if (StringUtils.isNotBlank(dateStr)) {
            lockKey += "-" + dateStr;
        }

        // 尝试获取分布式锁
        RLock lock = redissonService.tryLock(0, LOCK_EXPIRE_TIME, TimeUnit.SECONDS, lockKey);
        if (Objects.isNull(lock)) {
            log.info("薪资任务已被其他实例处理，跳过执行，tenantId: {}, salaryRuleId: {}, flag: {}, lockKey: {}",
                    tenantId, salaryRuleId, flag, lockKey);
            return;
        }

        try {
            log.info("开始处理薪资任务，tenantId: {}, salaryRuleId: {}, flag: {}, lockKey: {}",
                    tenantId, salaryRuleId, flag, lockKey);

            // 执行原有的业务逻辑
            IObjectData salaryRule = salaryRuleDao.getById(tenantId, salaryRuleId);
            List<IObjectData> employeeFixedSalaryObjs = employeeFixedSalaryDao.getBySalaryRuleId(tenantId, salaryRuleId);
            long startTime = DateUtils.getDateFromString(salaryTaskMessage.getStartDateStr(), DateUtils.DateFormat).getTime();
            long endTime = DateUtils.getDateFromString(salaryTaskMessage.getEndDateStr(), DateUtils.DateFormat).getTime();

            if (salaryTaskMessage.getFlag() == 0) {
                // 根据工资规则生效日期调整开始时间，确保工资条明细使用正确的时间
                long adjustedStartTime = startTime;
                Long ruleEffectiveDate = salaryRule.get(SalaryRuleFields.EFFECTIVE_DATE, Long.class);
                if (ruleEffectiveDate != null && ruleEffectiveDate > startTime) {
                    adjustedStartTime = ruleEffectiveDate;
                    log.info("根据工资规则生效日期调整薪资明细计算时间，原始开始时间: {}, 规则生效时间: {}, 调整后开始时间: {}",
                            new Date(startTime), new Date(ruleEffectiveDate), new Date(adjustedStartTime));
                }

                // 计算薪资明细
                for (IObjectData employeeFixedSalaryObj : employeeFixedSalaryObjs) {
                    try {
                        // 使用调整后的时间计算薪资明细
                        List<IObjectData> salaryDetailDatas = calculateSalaryDetailDatas(employeeFixedSalaryObj, salaryRule,
                                adjustedStartTime, endTime, true);
                    } catch (ValidateException e) {
                        // 业务异常 记录一下
                        log.info("calculateSalaryDetailDatas biz error employeeFixedSalaryId:{}, userId:{} ,extUserId:{} ",
                                employeeFixedSalaryObj.getId(),
                                employeeFixedSalaryObj.get(EmployeeFixedSalaryFields.EMPLOYEE),
                                employeeFixedSalaryObj.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL), e);
                    } catch (Exception e) {
                        log.error("calculateSalaryDetailDatas error employeeFixedSalaryId:{}, userId:{} ,extUserId:{} ",
                                employeeFixedSalaryObj.getId(),
                                employeeFixedSalaryObj.get(EmployeeFixedSalaryFields.EMPLOYEE),
                                employeeFixedSalaryObj.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL), e);
                    }
                }
                if (StringUtils.isNotBlank(salaryTaskMessage.getDateStr())) {
                    // 判断是否需要同时执行工资条生成逻辑
                    LocalDate currentDate = LocalDate.parse(salaryTaskMessage.getDateStr());
                    if (shouldGenerateSalaryData(salaryRule, currentDate)) {
                        log.info("当前日期满足条件，执行工资条生成逻辑 {} ,{} ,{}", tenantId, salaryRuleId, currentDate);
                        generateSalaryDataAndPaymentSlip(tenantId, salaryRule, employeeFixedSalaryObjs, startTime, endTime);
                    }
                }
            } else if (salaryTaskMessage.getFlag() == 1) {
                // 判断是否已经在flag=0阶段处理过
                if (StringUtils.isNotBlank(salaryTaskMessage.getDateStr())) {
                    LocalDate currentDate = LocalDate.parse(salaryTaskMessage.getDateStr());
                    if (shouldGenerateSalaryData(salaryRule, currentDate)) {
                        log.info("当前日期满足条件，但已在flag=0阶段处理过，跳过flag=1处理 {} ,{} ,{}", tenantId, salaryRuleId, currentDate);
                        return;
                    }
                }

                // 生成工资条和发放单
                generateSalaryDataAndPaymentSlip(tenantId, salaryRule, employeeFixedSalaryObjs, startTime, endTime);
            }

            log.info("薪资任务处理完成，tenantId: {}, salaryRuleId: {}, flag: {}", tenantId, salaryRuleId, flag);

        } catch (Exception e) {
            log.error("处理薪资任务失败，tenantId: {}, salaryRuleId: {}, flag: {}", tenantId, salaryRuleId, flag, e);
            throw e;
        } finally {
            // 释放分布式锁
            try {
                if (lock != null && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                    log.info("释放薪资任务锁成功，lockKey: {}", lockKey);
                }
            } catch (Exception e) {
                log.error("释放薪资任务锁失败，lockKey: {}", lockKey, e);
            }
        }
    }

    /**
     * 判断指定日期是否需要生成工资条
     * 根据薪资规则的自动发放设置和发放周期来判断
     * 并且是月初 周初 的时候才需要在 flag = 0 处理 因为工资条明细可能还没有生成完
     */
    @Override
    public boolean shouldGenerateSalaryData(IObjectData salaryRule, LocalDate checkDate) {
        if (salaryRule == null) {
            log.warn("薪资规则为空，不生成工资条");
            return false;
        }

        if (checkDate == null) {
            log.warn("检查日期为空，不生成工资条");
            return false;
        }

        // 检查是否开启自动创建发放单
        String autoCreated = salaryRule.get(SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE, String.class);
        if (!SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE_Options_true.equals(autoCreated)) {
            log.info("薪资规则未开启自动创建发放单，不生成工资条");
            return false;
        }

        // 获取发放时机
        String distributionTime = salaryRule.get(SalaryRuleFields.DISTRIBUTION_TIME, String.class);
        boolean shouldGenerate = false;

        if (SalaryRuleFields.DISTRIBUTION_TIME_Options_1.equals(distributionTime)) {
            // 次日发放 - 每天都满足条件
            shouldGenerate = true;
            log.info("薪资规则设置为次日发放，满足生成条件，日期: {}", checkDate);
        } else if (SalaryRuleFields.DISTRIBUTION_TIME_Options_2.equals(distributionTime)) {
            // 次周发放 - 检查是否为设定的星期几且为周一
            String nextWeekDistributionOp = salaryRule.get(SalaryRuleFields.NEXT_WEEK_DISTRIBUTION_OP, String.class);
            if (StringUtils.isNotBlank(nextWeekDistributionOp) && checkDate.getDayOfWeek() == DayOfWeek.MONDAY) {
                shouldGenerate = true;
                log.info("薪资规则设置为次周发放，当前为周一，满足生成条件，日期: {}, 设定星期: {}", checkDate, nextWeekDistributionOp);
            }
        } else if (SalaryRuleFields.DISTRIBUTION_TIME_Options_3.equals(distributionTime)) {
            // 次月发放 - 检查是否为设定的日期且为月初
            String nextMonthDistributionOp = salaryRule.get(SalaryRuleFields.NEXT_MONTH_DISTRIBUTION_OP, String.class);
            if (StringUtils.isNotBlank(nextMonthDistributionOp) && checkDate.getDayOfMonth() == 1) {
                shouldGenerate = true;
                log.info("薪资规则设置为次月发放，当前为月初，满足生成条件，日期: {}, 设定日期: {}", checkDate, nextMonthDistributionOp);
            }
        }

        return shouldGenerate;
    }

    /**
     * 生成工资条和发放单的通用方法
     * 从Consumer中移动到Service，使业务逻辑更清晰
     */
    @Override
    public void generateSalaryDataAndPaymentSlip(String tenantId, IObjectData salaryRule,
            List<IObjectData> employeeFixedSalaryObjs, long startTime, long endTime) {
        try {
            // 生成工资发放单（会根据生效日期调整时间）
            IObjectData salaryPaymentSlip = createSalaryPaymentSlip(salaryRule, startTime, endTime, null);

            // 检查工资发放单是否创建成功
            if (salaryPaymentSlip == null) {
                log.warn("工资发放单创建失败，跳过生成工资条，工资规则ID: {}", salaryRule.getId());
                return;
            }

            log.info("工资发放单创建成功，ID: {}", salaryPaymentSlip.getId());

            // 从工资发放单中获取调整后的时间，确保时间一致性
            Long adjustedStartTime = salaryPaymentSlip.get(SalaryPaymentSlipFields.START_DATE, Long.class);
            Long adjustedEndTime = salaryPaymentSlip.get(SalaryPaymentSlipFields.END_DATE, Long.class);
            log.info("使用工资发放单中调整后的时间生成工资条，开始时间: {}, 结束时间: {}", adjustedStartTime, adjustedEndTime);

            // 批量生成工资条
            SalaryGenerationResult result = batchGenerateSalaryData(employeeFixedSalaryObjs, salaryRule,
                adjustedStartTime, adjustedEndTime, salaryPaymentSlip);

            log.info("生成工资条完成，工资规则ID: {}, 成功生成: {}/{}, 是否有异常: {}",
                    salaryRule.getId(), result.successCount, employeeFixedSalaryObjs.size(), result.hasErrorDetails);

            // 修改工资发放单状态为已生成
            updateSalaryPaymentSlipStatus(tenantId, salaryPaymentSlip.getId(),
                    SalaryPaymentSlipFields.PAY_STATUS_Options_1, null);
            log.info("工资发放单状态更新为已生成，ID: {}", salaryPaymentSlip.getId());

        } catch (ValidateException e) {
            log.info("生成工资条和发放单失败", e);
        } catch (Exception e) {
            log.error("生成工资条和发放单失败", e);
            throw e;
        }
    }

    /**
     * 处理单个薪资明细的核心方法
     * 抽取的公共逻辑，消除calculateSalaryDetailDatas和recalculateSalaryDetails的重复代码
     *
     * @param detailData 明细数据
     * @param salaryItem 工资项
     * @param salaryContext 薪资计算上下文
     * @param employeeFixedSalaryId 员工固定薪资ID
     * @param fixedSalaryDetailCache 固定工资明细缓存
     * @return ProcessResult 处理结果
     */
    private ProcessResult processSingleSalaryDetail(IObjectData detailData,
                                                   IObjectData salaryItem,
                                                   SalaryContext salaryContext,
                                                   String employeeFixedSalaryId,
                                                   Map<String, List<IObjectData>> fixedSalaryDetailCache) {
        String tenantId = salaryContext.getTenantId();
        String currentStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);

        // 1. 处理已修正明细 - 抽取的公共逻辑
        if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_4.equals(currentStatus)) {
            return handleCorrectedDetail(detailData);
        }

        // 2. 处理异常明细强制重新计算
        boolean forceRecalculate = SalaryDetailDataFields.PAY_STATUS_Options_ERROR.equals(currentStatus);
        if (forceRecalculate) {
            log.info("发现生成异常的工资明细，强制重新计算，明细ID: {}", detailData.getId());
        }

        // 3. 执行核心计算 - 抽取的公共逻辑
        try {
            Pair<String, BigDecimal> calculationResult = calculateSalaryItemAmount(tenantId, salaryItem,
                    salaryContext, employeeFixedSalaryId, fixedSalaryDetailCache);

            if (calculationResult != null && calculationResult.getValue() != null) {
                BigDecimal calculatedAmount = calculationResult.getValue();
                String calculationFormula = calculationResult.getKey();

                // 更新明细数据 - 抽取的公共逻辑
                updateDetailData(detailData, calculatedAmount, calculationFormula, salaryItem, salaryContext);
                return ProcessResult.success(calculatedAmount, calculationFormula);
            } else {
                // 计算失败处理 - 抽取的公共逻辑
                return handleCalculationFailure(detailData, tenantId);
            }
        } catch (Exception e) {
            // 异常处理 - 抽取的公共逻辑
            return handleCalculationException(detailData, tenantId, e);
        }
    }

    /**
     * 处理已修正明细
     * 抽取的公共逻辑
     */
    private ProcessResult handleCorrectedDetail(IObjectData detailData) {
        String amountStr = detailData.get(SalaryDetailDataFields.AMOUNT, String.class);
        String incrementDecrementAttrib = detailData.get(SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB, String.class);

        BigDecimal amount = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(amountStr)) {
            amount = new BigDecimal(amountStr);
            // 根据增减属性调整符号
            if (SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
                amount = amount.negate();
            }
        }

        return ProcessResult.skip(amount);
    }

    /**
     * 更新明细数据
     * 抽取的公共逻辑
     */
    private void updateDetailData(IObjectData detailData, BigDecimal amount, String calculationFormula,
                                 IObjectData salaryItem, SalaryContext salaryContext) {
        // 更新金额和状态
        detailData.set(SalaryDetailDataFields.AMOUNT, amount.toString());
        detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_0);

        // 更新公式相关字段
        ObjectDataExt salaryItemExt = ObjectDataExt.of(salaryItem);
        if (SalaryItemFields.VALUE_TYPE_Options_2.equals(salaryItemExt.getStringValue(SalaryItemFields.VALUE_TYPE))) {
            if (StringUtils.isNotBlank(calculationFormula)) {
                detailData.set(SalaryDetailDataFields.FORMULA_ASSIGNMENT, calculationFormula);
            }

            String formattedFormula = formatFormula(salaryItemExt.getStringValue(SalaryItemFields.CALCULATION_FORMULA),
                    salaryContext.getExtDataNameMap());
            detailData.set(SalaryDetailDataFields.CALCULATION_FORMULA, formattedFormula);

            if (!MapUtils.isNullOrEmpty(salaryContext.getExtDataMap())) {
                detailData.set(SalaryDetailDataFields.FORMULA_VARIABLE, JSON.toJSONString(salaryContext.getExtDataMap()));
            }
        }
    }

    /**
     * 处理计算失败
     * 抽取的公共逻辑
     */
    private ProcessResult handleCalculationFailure(IObjectData detailData, String tenantId) {
        log.warn("工资明细计算失败，结果为空，明细ID: {}", detailData.getId());
        detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.PAY_STATUS_Options_ERROR);
        return ProcessResult.error();
    }

    /**
     * 处理计算异常
     * 抽取的公共逻辑
     */
    private ProcessResult handleCalculationException(IObjectData detailData, String tenantId, Exception e) {
        log.error("工资明细计算异常，设置为异常状态，明细ID: {}", detailData.getId(), e);
        detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.PAY_STATUS_Options_ERROR);
        return ProcessResult.error();
    }

    /**
     * 设置明细基本信息
     * 抽取的公共逻辑
     */
    private void setupDetailBasicInfo(IObjectData salaryDetailData, IObjectData salaryItem, String employeeId) {
        // 设置owner
        if (Integer.valueOf(employeeId).intValue() < 100000000) {
            salaryDetailData.setOwner(Lists.newArrayList(employeeId));
        } else {
            salaryDetailData.setOwner(Lists.newArrayList("-10000"));
            salaryDetailData.setOutOwner(Lists.newArrayList(employeeId));
        }

        // 设置工资项ID和增减属性
        salaryDetailData.set(SalaryDetailDataFields.SALARY_ITEM, salaryItem.getId());
        salaryDetailData.set(SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB,
                salaryItem.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class));

        // 设置取值来源
        setValueSource(salaryDetailData, salaryItem);
    }

    /**
     * 设置取值来源
     * 根据工资项的取值方式来确定取值来源
     */
    private void setValueSource(IObjectData salaryDetailData, IObjectData salaryItem) {
        String valueType = salaryItem.get(SalaryItemFields.VALUE_TYPE, String.class);
        String valueSource;

        if (SalaryItemFields.VALUE_TYPE_Options_1.equals(valueType)) {
            // 固定值类型 -> 取值来源为员工固定工资表
            valueSource = SalaryDetailDataFields.VALUE_SOURCE_Options_1;
        } else if (SalaryItemFields.VALUE_TYPE_Options_2.equals(valueType)) {
            // 计算公式类型 -> 取值来源为工资项
            valueSource = SalaryDetailDataFields.VALUE_SOURCE_Options_2;
        } else {
            // 默认为工资项
            valueSource = SalaryDetailDataFields.VALUE_SOURCE_Options_2;
        }

        salaryDetailData.set(SalaryDetailDataFields.VALUE_SOURCE, valueSource);
        log.debug("设置工资条明细取值来源: {}, 工资项ID: {}, 工资项取值方式: {}",
                valueSource, salaryItem.getId(), valueType);
    }

    /**
     * 从缓存中获取工资项
     * 抽取的公共逻辑
     */
    private IObjectData getSalaryItemFromCache(IObjectData detailData, Map<String, IObjectData> salaryItemCache, String tenantId) {
        // 获取工资项ID
        String salaryItemId = detailData.get(SalaryDetailDataFields.SALARY_ITEM, String.class);
        if (StringUtils.isBlank(salaryItemId)) {
            log.warn("工资条明细缺少工资项ID，明细ID: {}", detailData.getId());
            return null;
        }

        // 从缓存中获取工资项信息
        IObjectData salaryItem = salaryItemCache.get(salaryItemId);
        if (salaryItem == null) {
            salaryItem = salaryItemDao.getById(tenantId, salaryItemId);
            if (salaryItem != null) {
                salaryItemCache.put(salaryItemId, salaryItem);
            } else {
                log.warn("工资项不存在，工资项ID: {}", salaryItemId);
                return null;
            }
        }

        return salaryItem;
    }

    /**
     * 根据发放周期和定薪方式计算周期开始时间
     *
     * @param currentDate 当前日期
     * @param distributionCycle 发放周期：1-按日发放，2-按周发放，3-按月发放
     * @param salaryMethod 定薪方式：1-日薪，2-周薪，3-月薪
     * @return 周期开始时间
     */
    private LocalDate calculateCycleStartDate(LocalDate currentDate, String distributionCycle, String salaryMethod) {
        //null
        if (currentDate == null) {
            currentDate = LocalDate.now();
        }
        if (SalaryRuleFields.DISTRIBUTION_CYCLE_Options_1.equals(distributionCycle)) {
            // 按日发放：开始时间就是当前日期
            return currentDate;
        } else if (SalaryRuleFields.DISTRIBUTION_CYCLE_Options_2.equals(distributionCycle)) {
            // 按周发放：开始时间是本周的周一
            return currentDate.with(DayOfWeek.MONDAY);
        } else if (SalaryRuleFields.DISTRIBUTION_CYCLE_Options_3.equals(distributionCycle)) {
            // 按月发放：开始时间是本月的第一天
            return currentDate.withDayOfMonth(1);
        } else {
            // 默认情况：使用当前日期
            log.warn("未知的发放周期: {}, 使用当前日期作为周期开始时间", distributionCycle);
            return currentDate;
        }
    }

    /**
     * 生成周期ID，考虑工资规则生效时间的影响
     * 如果生效时间在周期开始和结束时间之间，则使用生效时间作为周期开始时间
     *
     * @param cycleStartDate 周期开始时间
     * @param cycleEndDate 周期结束时间
     * @param ruleEffectiveDate 工资规则生效时间（可为null）
     * @return 周期ID，格式：yyyyMMdd_yyyyMMdd
     */
    private String generatePeriodId(LocalDate cycleStartDate, LocalDate cycleEndDate, LocalDate ruleEffectiveDate) {
        LocalDate adjustedStartDate = cycleStartDate;

        // 如果规则生效时间不为空，检查是否需要调整开始时间
        if (ruleEffectiveDate != null) {
            // 如果生效时间在周期开始和结束时间之间（包含边界），则使用生效时间作为开始时间
            if (!ruleEffectiveDate.isBefore(cycleStartDate) && !ruleEffectiveDate.isAfter(cycleEndDate)) {
                adjustedStartDate = ruleEffectiveDate;
                log.debug("根据工资规则生效时间调整周期ID开始时间，原始开始时间: {}, 生效时间: {}, 调整后开始时间: {}",
                        cycleStartDate, ruleEffectiveDate, adjustedStartDate);
            }
        }

        // 生成周期ID
        String startDateStr = adjustedStartDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String endDateStr = cycleEndDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return startDateStr + "_" + endDateStr;
    }
    /**
     * 处理强制适用人员冲突（静默处理，不抛异常）
     * 从其他规则中移除当前规则的强制适用人员
     *
     * @param tenantId 租户ID
     * @param currentRuleId 当前规则ID
     * @param mandatoryApplicablePersonnel 当前规则的强制适用人员
     */
    @Override
    public  void processMandatoryPersonnelConflicts(String tenantId, String currentRuleId,
                                                          List<String> mandatoryList) {
        try {

            if (mandatoryList.isEmpty()) {
                log.debug("规则 {} 没有强制适用人员，跳过冲突处理", currentRuleId);
                return;
            }

            log.info("开始处理规则 {} 的强制适用人员冲突，人员数量: {}", currentRuleId, mandatoryList.size());

            // 使用强制适用人员作为条件查询相关规则
            List<IObjectData> conflictRules = salaryRuleDao.getAllIObjectDataListByQuery(
                    User.systemUser(tenantId),
                    SearchQuery.builder()
                            .hasAnyOf(SalaryRuleFields.MANDATORY_APPLICABLE_PERSONNEL, mandatoryList)
                            .build(),
                    SalaryRuleFields.API_NAME);

            // 从其他规则中移除冲突的强制适用人员
            for (IObjectData rule : conflictRules) {
                // 跳过当前规则
                if (currentRuleId.equals(rule.getId())) {
                    continue;
                }

                Object otherMandatoryPersonnel = rule.get(SalaryRuleFields.MANDATORY_APPLICABLE_PERSONNEL);
                List<String> otherMandatoryList = convertToStringList(otherMandatoryPersonnel);

                if (otherMandatoryList.isEmpty()) {
                    continue;
                }

                // 检查是否有需要移除的人员
                List<String> updatedList = otherMandatoryList.stream()
                        .filter(person -> !mandatoryList.contains(person))
                        .collect(Collectors.toList());

                if (updatedList.size() != otherMandatoryList.size()) {
                    // 有人员被移除，需要更新规则
                    IObjectData updateData = rule;
                    updateData.set(SalaryRuleFields.MANDATORY_APPLICABLE_PERSONNEL, updatedList);

                    salaryRuleDao.update(User.systemUser(tenantId), updateData);

                    int removedCount = otherMandatoryList.size() - updatedList.size();
                    log.info("从规则 [{}] 的强制适用人员中移除了 {} 个人员", rule.getName(), removedCount);
                }
            }

            log.info("完成处理规则 {} 的强制适用人员冲突", currentRuleId);

        } catch (Exception e) {
            log.error("处理强制适用人员冲突失败，规则ID: {}", currentRuleId, e);
            // 静默处理，不抛出异常
        }
    }

    @Override
    public boolean checkSalaryMethodConsistency(String tenantId, String employeeId, boolean isExternal) {
        try {
            // 1. 获取员工固定工资表
            IObjectData employeeFixedSalary = employeeFixedSalaryDao.getByEmployeeId(tenantId, employeeId);
            if (employeeFixedSalary == null) {
                log.info("员工没有固定工资表，无法检查定薪方式一致性，employeeId: {}", employeeId);
                return true; // 没有固定工资表时认为是一致的
            }

            String employeeSalaryMethod = employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_METHOD, String.class);
            if (StringUtils.isBlank(employeeSalaryMethod)) {
                log.warn("员工固定工资表的定薪方式为空，employeeId: {}", employeeId);
                return false;
            }

            // 2. 获取员工适用的工资规则
            List<IObjectData> applicableRules = getApplicableSalaryRulesForEmployee(tenantId, employeeId, isExternal);
            if (CollectionUtils.isEmpty(applicableRules)) {
                log.info("员工不适用任何工资规则，无法检查定薪方式一致性，employeeId: {}", employeeId);
                return true;
            }

            // 3. 检查是否有任何一个规则的定薪方式与员工一致
            for (IObjectData rule : applicableRules) {
                String ruleSalaryMethod = ObjectDataExt.of(rule).getStringValue(SalaryRuleFields.SALARY_METHOD);
                if (employeeSalaryMethod.equals(ruleSalaryMethod)) {
                    return true; // 找到一致的规则
                }
            }

            log.info("员工定薪方式与所有适用规则都不一致，employeeId: {}, employeeSalaryMethod: {}",
                    employeeId, employeeSalaryMethod);
            return false;

        } catch (Exception e) {
            log.error("检查定薪方式一致性失败，employeeId: {}", employeeId, e);
            return false;
        }
    }
    /**
     * 格式化时间戳为北京时间字符串
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的北京时间字符串，格式：yyyy-MM-dd HH:mm
     */
    private String formatBeijingTime(long timestamp) {
        return Instant.ofEpochMilli(timestamp)
                .atZone(ZoneId.of("Asia/Shanghai"))
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    }

}
