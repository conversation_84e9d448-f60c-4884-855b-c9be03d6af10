package com.facishare.crm.fmcg.wq.notify.impl;

import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.*;
import com.facishare.crm.fmcg.wq.notify.SalaryRuleNotificationService;
import com.facishare.crm.fmcg.wq.notify.util.SalaryNotificationMessageUtil;
import com.facishare.crm.fmcg.wq.service.RoleQueryService;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.session.RestSendMessageService;
import com.github.autoconf.ConfigFactory;
import com.facishare.paas.appframework.common.service.DepartmentService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.Utils.ReceiverChannelUtils;
import com.fxiaoke.constant.ReceiverChannelType;
import com.fxiaoke.model.*;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 工资规则通知服务实现类
 */
@Slf4j
@Service
public class SalaryRuleNotificationServiceImpl implements SalaryRuleNotificationService {

    @Autowired
    private SalaryService salaryService;

    @Autowired
    private SalaryRuleDao salaryRuleDao;

    @Autowired
    private EmployeeFixedSalaryDao employeeFixedSalaryDao;

    @Autowired
    private EmployeeDao employeeDao;

    @Autowired
    private RestSendMessageService restSendMessageService;

    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private RoleQueryService roleQueryService;

    // 消息跳转URL模板（可配置）
    // Web端URL模板
    private static String SALARY_NOTIFICATION_WEB_URL = "https://www.fxiaoke.com/XV/UI/Home#crm/list/=/{apiName}";
    // H5端URL模板
    private static String SALARY_NOTIFICATION_H5_URL = "https://www.fxiaoke.com/hcrm/avah5?_menuType=5&apiName={apiName}&recordType=&beforeHash=object_list/pages/list/list&hash=uipaas_custom/pages/list/list";
    // 默认跳转的对象API名称
    private static String SALARY_NOTIFICATION_TARGET_API_NAME = "EmployeeFixedSalaryObj";

    static {
        ConfigFactory.getConfig("CheckInService", config -> {
            SALARY_NOTIFICATION_WEB_URL = config.get("salaryNotificationWebUrl", SALARY_NOTIFICATION_WEB_URL);
            SALARY_NOTIFICATION_H5_URL = config.get("salaryNotificationH5Url", SALARY_NOTIFICATION_H5_URL);
            SALARY_NOTIFICATION_TARGET_API_NAME = config.get("salaryNotificationTargetApiName", SALARY_NOTIFICATION_TARGET_API_NAME);
        });
    }

    @Override
    public boolean notifyMissingEmployeeFixedSalaryForNewRule(String tenantId, String salaryRuleId) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(salaryRuleId)) {
            log.warn("参数无效，tenantId: {}, salaryRuleId: {}", tenantId, salaryRuleId);
            return false;
        }

        try {
            log.info("开始处理新建工资规则的员工固定工资表缺失通知，tenantId: {}, salaryRuleId: {}", tenantId, salaryRuleId);

            // 1. 获取工资规则信息
            IObjectData salaryRule = salaryRuleDao.getById(tenantId, salaryRuleId);
            if (salaryRule == null) {
                log.warn("工资规则不存在，salaryRuleId: {}", salaryRuleId);
                return false;
            }

            // 2. 获取工资规则适用范围内的所有员工
            List<String> applicableEmployeeIds = salaryService.getEmployeeIdsBySalaryRule(tenantId, salaryRule);
            if (CollectionUtils.isEmpty(applicableEmployeeIds)) {
                log.info("工资规则适用范围内没有员工，salaryRuleId: {}", salaryRuleId);
                return false;
            }

            log.info("工资规则适用范围内共有 {} 个员工", applicableEmployeeIds.size());

            // 3. 批量检查哪些员工没有创建员工固定工资表，避免for循环单条查询
            List<IObjectData> existingRecords = employeeFixedSalaryDao.getByEmployeeIds(tenantId, applicableEmployeeIds);

            // 创建已有固定工资表的员工ID集合
            Set<String> existingEmployeeIds = Sets.newHashSet();
            for (IObjectData record : existingRecords) {
                String employeeId = record.get(EmployeeFixedSalaryFields.EMPLOYEE, String.class);
                if (StringUtils.isBlank(employeeId)) {
                    employeeId = record.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, String.class);
                }
                if (StringUtils.isNotBlank(employeeId)) {
                    existingEmployeeIds.add(employeeId);
                }
            }

            // 找出缺失固定工资表的员工
            List<String> missingEmployeeIds = applicableEmployeeIds.stream()
                    .filter(employeeId -> !existingEmployeeIds.contains(employeeId))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(missingEmployeeIds)) {
                log.info("所有适用员工都已创建员工固定工资表，无需发送通知");
                return false;
            }

            log.info("发现 {} 个员工未创建员工固定工资表，准备发送通知", missingEmployeeIds.size());


            // 4. 发送消息
            return sendMissingFixedSalaryNotifications(tenantId, salaryRule, missingEmployeeIds);

        } catch (Exception e) {
            log.error("处理新建工资规则的员工固定工资表缺失通知失败", e);
            return false;
        }
    }

    @Override
    public boolean notifyNewEmployeeNeedsFixedSalary(String tenantId, List<String> employeeIds, boolean isExternal, String scope) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(employeeIds)) {
            log.warn("参数无效，tenantId: {}, employeeIds为空", tenantId);
            return false;
        }

        try {
            log.info("检查新增员工是否需要创建固定工资表，tenantId: {}, employeeIds数量: {}, isExternal: {}, scope: {}",
                    tenantId, employeeIds.size(), isExternal, scope);

            // 1. 筛选需要通知的员工（没有固定工资表且有适用规则的员工）
            List<String> needNotifyEmployeeIds = Lists.newArrayList();

            for (String employeeId : employeeIds) {
                // 检查员工是否已有固定工资表
                IObjectData existingRecord = employeeFixedSalaryDao.getByEmployeeId(tenantId, employeeId);
                if (existingRecord != null) {
                    log.debug("员工已有固定工资表，跳过，employeeId: {}", employeeId);
                    continue;
                }

                // 查询该员工适用的工资规则
                List<IObjectData> applicableRuleObjs = salaryService.getApplicableSalaryRulesForEmployee(tenantId, employeeId, isExternal);
                if (CollectionUtils.isEmpty(applicableRuleObjs)) {
                    log.debug("员工不适用任何工资规则，跳过，employeeId: {}", employeeId);
                    continue;
                }

                needNotifyEmployeeIds.add(employeeId);
            }

            if (CollectionUtils.isEmpty(needNotifyEmployeeIds)) {
                log.info("没有需要通知的员工");
                return false;
            }

            log.info("需要发送通知的员工数量: {}", needNotifyEmployeeIds.size());

            // 2. 发送批量通知消息
            return sendBatchNewEmployeeNotification(tenantId, needNotifyEmployeeIds, isExternal, scope);

        } catch (Exception e) {
            log.error("处理批量新增员工固定工资表通知失败，employeeIds数量: {}", employeeIds.size(), e);
            return false;
        }
    }



    @Override
    public boolean notifySalaryMethodMismatch(String tenantId, List<String> employeeIds, String salaryRuleId, String ruleSalaryMethod) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(employeeIds)) {
            log.warn("参数无效，tenantId: {}, employeeIds为空", tenantId);
            return false;
        }

        try {
            log.info("批量处理定薪方式不一致通知，tenantId: {}, employeeIds数量: {}, salaryRuleId: {}, ruleSalaryMethod: {}",
                    tenantId, employeeIds.size(), salaryRuleId, ruleSalaryMethod);

            // 批量获取员工固定工资表，避免for循环单条查询
            List<IObjectData> employeeFixedSalaries = employeeFixedSalaryDao.getByEmployeeIds(tenantId, employeeIds);

            // 创建员工ID到固定工资表的映射
            Map<String, IObjectData> employeeIdToSalaryMap = Maps.newHashMap();
            for (IObjectData employeeFixedSalary : employeeFixedSalaries) {
                String employeeId = employeeFixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE, String.class);
                if (StringUtils.isBlank(employeeId)) {
                    employeeId = employeeFixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, String.class);
                }
                if (StringUtils.isNotBlank(employeeId)) {
                    employeeIdToSalaryMap.put(employeeId, employeeFixedSalary);
                }
            }

            // 筛选出需要推送的员工：定薪方式不一致且当前无规则的用户
            List<String> targetEmployeeIds = Lists.newArrayList();

            for (String employeeId : employeeIds) {
                try {
                    // 1. 获取员工固定工资表
                    IObjectData employeeFixedSalary = employeeIdToSalaryMap.get(employeeId);
                    if (employeeFixedSalary == null) {
                        log.debug("员工没有固定工资表，跳过，employeeId: {}", employeeId);
                        continue;
                    }

                    // 2. 检查定薪方式是否不一致
                    String employeeSalaryMethod = employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_METHOD, String.class);
                    if (Objects.equals(employeeSalaryMethod, ruleSalaryMethod)) {
                        log.debug("员工定薪方式与规则一致，跳过，employeeId: {}, salaryMethod: {}", employeeId, employeeSalaryMethod);
                        continue;
                    }

                    // 3. 检查当前是否无规则（员工固定工资表的规则字段为空或不等于当前规则）
                    String currentRuleId = employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_RULE, String.class);
                    if (StringUtils.isNotBlank(currentRuleId) && Objects.equals(currentRuleId, salaryRuleId)) {
                        log.debug("员工已有当前规则，跳过，employeeId: {}, currentRuleId: {}", employeeId, currentRuleId);
                        continue;
                    }

                    // 符合条件：定薪方式不一致且当前无规则
                    targetEmployeeIds.add(employeeId);
                    log.info("找到符合条件的员工，employeeId: {}, 员工定薪方式: {}, 规则定薪方式: {}, 当前规则: {}",
                            employeeId, employeeSalaryMethod, ruleSalaryMethod, currentRuleId);

                } catch (Exception e) {
                    log.error("筛选员工时发生异常，employeeId: {}", employeeId, e);
                }
            }

            if (CollectionUtils.isEmpty(targetEmployeeIds)) {
                log.info("没有找到符合条件的员工（定薪方式不一致且当前无规则），无需发送通知");
                return false;
            }

            // 发送批量通知
            try {
                // 构建员工I18n参数
                String employeeI18nParams = SalaryNotificationMessageUtil.buildEmployeeI18nParams(employeeDao,tenantId, targetEmployeeIds);

                // 构建批量消息内容
                SalaryNotificationMessageUtil.MessageContent messageContent =
                        SalaryNotificationMessageUtil.buildBatchSalaryMethodMismatchMessage(employeeI18nParams);

                // 发送给管理员（使用第一个员工作为代表）
                String representativeEmployeeId = targetEmployeeIds.get(0);
                boolean isExternal = isExternalEmployee(representativeEmployeeId);

                boolean success = sendNotificationMessage(tenantId, representativeEmployeeId, isExternal, messageContent);

                if (success) {
                    log.info("批量定薪方式不一致通知发送成功，员工数量: {}, salaryRuleId: {}, ruleSalaryMethod: {}",
                            targetEmployeeIds.size(), salaryRuleId, ruleSalaryMethod);
                    return true;
                } else {
                    log.warn("批量定薪方式不一致通知发送失败，员工数量: {}, salaryRuleId: {}, ruleSalaryMethod: {}",
                            targetEmployeeIds.size(), salaryRuleId, ruleSalaryMethod);
                    return false;
                }

            } catch (Exception e) {
                log.error("发送批量定薪方式不一致通知失败，员工数量: {}", targetEmployeeIds.size(), e);
                return false;
            }

        } catch (Exception e) {
            log.error("批量处理定薪方式不一致通知失败，salaryRuleId: {}, ruleSalaryMethod: {}", salaryRuleId, ruleSalaryMethod, e);
            return false;
        }
    }




    @Override
    public boolean notifyMultipleRulesMatch(String tenantId, List<String> employeeIds, boolean isExternal) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(employeeIds)) {
            log.warn("参数无效，tenantId: {}, employeeIds为空", tenantId);
            return false;
        }

        try {
            log.info("发送批量多规则适配通知，tenantId: {}, employeeIds数量: {}, isExternal: {}",
                    tenantId, employeeIds.size(), isExternal);

            // 构建批量员工I18n参数
            String employeeI18nParams = SalaryNotificationMessageUtil.buildEmployeeI18nParams(employeeDao,tenantId, employeeIds);

            SalaryNotificationMessageUtil.MessageContent messageContent =
                    SalaryNotificationMessageUtil.buildBatchMultipleRulesMatchMessage("", employeeI18nParams); //ignoreI18n

            // 使用第一个员工作为代表发送通知
            String representativeEmployeeId = employeeIds.get(0);
            return sendNotificationMessage(tenantId, representativeEmployeeId, isExternal, messageContent);

        } catch (Exception e) {
            log.error("发送批量多规则适配通知失败，employeeIds数量: {}", employeeIds.size(), e);
            return false;
        }
    }

    @Override
    public boolean notifyRuleRemoved(String tenantId, List<IObjectData> employeeFixedSalaries, String removedRuleName) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(employeeFixedSalaries)) {
            log.warn("参数无效，tenantId: {}, employeeFixedSalaryIds为空", tenantId);
            return false;
        }

        try {
            log.info("发送批量规则被移出通知，tenantId: {}, employeeFixedSalaryIds数量: {}, removedRuleName: {}",
                    tenantId, employeeFixedSalaries.size(), removedRuleName);

            // 获取所有员工固定工资表对应的员工ID
            List<String> employeeIds = Lists.newArrayList();
            String representativeEmployeeId = null;
            boolean isExternal = false;

            for (IObjectData employeeFixedSalary : employeeFixedSalaries) {
                String employeeId = employeeFixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE, String.class);
                //外部
                String employeeExternalId = employeeFixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, String.class);
                if (StringUtils.isNotBlank(employeeId)) {
                    employeeIds.add(employeeId);
                }
                if (StringUtils.isNotBlank(employeeExternalId)) {
                    employeeIds.add(employeeExternalId);
                }
            }

            if (CollectionUtils.isEmpty(employeeIds)) {
                log.warn("没有找到有效的员工ID");
                return false;
            }

            // 构建批量员工I18n参数
            String employeeI18nParams = SalaryNotificationMessageUtil.buildEmployeeI18nParams(employeeDao,tenantId, employeeIds);
            SalaryNotificationMessageUtil.MessageContent messageContent =
                    SalaryNotificationMessageUtil.buildRuleRemovedMessage(employeeI18nParams, removedRuleName);

            return sendNotificationMessage(tenantId, representativeEmployeeId, isExternal, messageContent);

        } catch (Exception e) {
            log.error("发送批量规则被移出通知失败，employeeFixedSalaryIds数量: {}", employeeFixedSalaries.size(), e);
            return false;
        }
    }



    /**
     * 判断是否为外部员工
     *
     * @param employeeId 员工ID
     * @return 是否为外部员工
     */
    private boolean isExternalEmployee(String employeeId) {
        if (StringUtils.isBlank(employeeId)) {
            log.warn("员工ID为空，默认为内部员工");
            return false;
        }

        try {
            long id = Long.parseLong(employeeId);
            boolean isExternal = id > 100000000L;
            log.debug("员工ID: {}, 是否外部员工: {}", employeeId, isExternal);
            return isExternal;
        } catch (NumberFormatException e) {
            log.warn("员工ID格式异常，employeeId: {}, 默认为内部员工", employeeId);
            return false;
        }
    }

    /**
     * 发送员工固定工资表缺失通知（批量优化版本）
     */
    private boolean sendMissingFixedSalaryNotifications(String tenantId, IObjectData salaryRule, List<String> missingEmployeeIds) {
        try {
            if (CollectionUtils.isEmpty(missingEmployeeIds)) {
                log.info("没有需要发送通知的员工");
                return false;
            }

            // 工资规则名称通过关联字段获取，如果没有则使用ID
            String ruleName = salaryRule.get("name", String.class);
            if (StringUtils.isBlank(ruleName)) {
                ruleName = "工资规则" + salaryRule.getId(); //ignoreI18n
            }

            // 构建员工I18n参数
            String employeeI18nParams = SalaryNotificationMessageUtil.buildEmployeeI18nParams(employeeDao,tenantId, missingEmployeeIds);

            // 构建批量消息内容
            SalaryNotificationMessageUtil.MessageContent messageContent =
                    SalaryNotificationMessageUtil.buildBatchMissingFixedSalaryMessage(ruleName, "", employeeI18nParams);

            // 发送给管理员（使用第一个员工的信息作为代表）
            String representativeEmployeeId = missingEmployeeIds.get(0);
            boolean isExternal = isExternalEmployee(representativeEmployeeId);

            boolean success = sendNotificationMessage(tenantId, representativeEmployeeId, isExternal, messageContent);

            if (success) {
                log.info("批量员工固定工资表缺失通知发送成功，员工数量: {}, 规则: {}", missingEmployeeIds.size(), ruleName);
                return true;
            } else {
                log.warn("批量员工固定工资表缺失通知发送失败，员工数量: {}, 规则: {}", missingEmployeeIds.size(), ruleName);
                return false;
            }

        } catch (Exception e) {
            log.error("批量发送员工固定工资表缺失通知失败，员工数量: {}", missingEmployeeIds.size(), e);
            return false;
        }
    }

    /**
     * 发送批量新增员工通知
     */
    private boolean sendBatchNewEmployeeNotification(String tenantId, List<String> employeeIds, boolean isExternal, String scope) {
        try {
            if (CollectionUtils.isEmpty(employeeIds)) {
                return false;
            }

            // 构建批量员工I18n参数
            String employeeI18nParams = SalaryNotificationMessageUtil.buildEmployeeI18nParams(employeeDao,tenantId, employeeIds);
            String triggerScope = StringUtils.isNotBlank(scope) ? scope : ""; //ignoreI18n

            SalaryNotificationMessageUtil.MessageContent messageContent =
                    SalaryNotificationMessageUtil.buildBatchNewEmployeeMessage(triggerScope, employeeI18nParams);

            // 使用第一个员工作为代表发送通知
            String representativeEmployeeId = employeeIds.get(0);
            return sendNotificationMessage(tenantId, representativeEmployeeId, isExternal, messageContent);

        } catch (Exception e) {
            log.error("发送批量新增员工通知失败，employeeIds数量: {}", employeeIds.size(), e);
            return false;
        }
    }







    /**
     * 构建新增员工消息
     */
    private SalaryNotificationMessageUtil.MessageContent buildNewEmployeeMessage(String tenantId, String employeeId, boolean isExternal, String scope) {
        try {
            // 直接使用员工I18n参数格式，不需要查询员工姓名和部门角色信息
            String employeeI18nParam = SalaryNotificationMessageUtil.buildEmployeeI18nParams(employeeDao,tenantId, Lists.newArrayList(employeeId));
            // 使用scope作为新增范围参数
            String triggerScope = StringUtils.isNotBlank(scope) ? scope : ""; //ignoreI18n

            return SalaryNotificationMessageUtil.buildBatchNewEmployeeMessage(triggerScope, employeeI18nParam);

        } catch (Exception e) {
            log.error("构建新增员工消息失败，employeeId: {}", employeeId, e);
            // 返回默认消息，使用员工I18n参数格式
            String defaultEmployeeI18nParam = SalaryNotificationMessageUtil.buildEmployeeI18nParams(employeeDao,tenantId, Lists.newArrayList(employeeId));
            return SalaryNotificationMessageUtil.buildBatchNewEmployeeMessage("", defaultEmployeeI18nParam); //ignoreI18n
        }
    }



    /**
     * 发送通知消息的统一方法
     * 消息接收人为内部CRM管理员和PMM管理员
     */
    private boolean sendNotificationMessage(String tenantId, String employeeId, boolean isExternal, SalaryNotificationMessageUtil.MessageContent messageContent) {
        try {
            // 获取CRM管理员和PMM管理员ID列表
            List<Integer> adminIds = getAdminUserIds(tenantId);
            if (CollectionUtils.isEmpty(adminIds)) {
                log.warn("未找到CRM管理员和PMM管理员，无法发送通知，tenantId: {}", tenantId);
                return false;
            }

            // 使用文本卡片消息发送给管理员
            String jumpUrl = buildJumpUrl();
            SendTextCardMessageArg messageArg = createTextCardMessageArg(
                messageContent.getTitleItem(),
                messageContent.getContentItem(),
                jumpUrl,
                messageContent.getButtonItem()
            );
            messageArg.setReceiverIds(adminIds);
            messageArg.setEi(Integer.valueOf(tenantId));

            // 使用第一个管理员ID作为发送标识
            String senderId = adminIds.get(0).toString();
            return restSendMessageService.sendTextCardMessageWithRetry(messageArg, senderId);

        } catch (Exception e) {
            log.error("发送通知消息失败，employeeId: {}, isExternal: {}", employeeId, isExternal, e);
            return false;
        }
    }

    /**
     * 构建跳转URL
     * 默认跳转到员工固定工资表列表页面
     */
    private String buildJumpUrl() {
        try {
            // 使用Web端URL模板，将{apiName}占位符替换为实际的API名称
            return SALARY_NOTIFICATION_WEB_URL.replace("{apiName}", SALARY_NOTIFICATION_TARGET_API_NAME);
        } catch (Exception e) {
            log.error("构建跳转URL失败", e);
            // 返回默认URL
            return "https://www.fxiaoke.com/XV/UI/Home#crm/list/=/EmployeeFixedSalaryObj";
        }
    }

    /**
     * 构建H5跳转URL
     * 用于移动端访问
     */
    private String buildH5JumpUrl() {
        try {
            // 使用H5端URL模板，将{apiName}占位符替换为实际的API名称
            return SALARY_NOTIFICATION_H5_URL.replace("{apiName}", SALARY_NOTIFICATION_TARGET_API_NAME);
        } catch (Exception e) {
            log.error("构建H5跳转URL失败", e);
            // 返回默认H5 URL
            return "https://www.fxiaoke.com/hcrm/avah5?_menuType=5&apiName=EmployeeFixedSalaryObj&recordType=&beforeHash=object_list/pages/list/list&hash=uipaas_custom/pages/list/list";
        }
    }

    /**
     * 获取CRM管理员和PMM管理员ID列表
     */
    private List<Integer> getAdminUserIds(String tenantId) {
        List<Integer> adminIds = Lists.newArrayList();

        try {
            List<Integer> employeeIdsByRoles = Optional.ofNullable(roleQueryService.getEmployeeIdsByRoles(tenantId, RoleQueryService.ADMIN_ROLE_CODES, true, false)).map(o->o.stream().map(e->Integer.valueOf(e)).collect(Collectors.toList())).orElse(Lists.newArrayList());
            log.info("获取到管理员ID列表，tenantId: {}, adminIds: {}", tenantId, employeeIdsByRoles);
            return employeeIdsByRoles;


        } catch (Exception e) {
            log.error("获取管理员ID列表失败，tenantId: {}", tenantId, e);
        }

        return adminIds;
    }

    /**
     * 创建文本卡片消息参数（使用国际化）
     */
    private SendTextCardMessageArg createTextCardMessageArg(InternationalItem titleItem, InternationalItem contentItem, String url, InternationalItem buttonItem) {
        // 创建默认参数
        SendTextCardMessageArg arg = buildDefaultArg(RestSendMessageService.PMM_APPID);

        // 创建文本卡片消息
        TextCardMessage textCardMessage = new TextCardMessage();

        // 设置消息头部（使用国际化）
        TextCardMessageHead head = new TextCardMessageHead();
        TextCardElement titleElement = new TextCardElement();
        titleElement.setTextInfo(titleItem);
        head.setTitleElement(titleElement);
        textCardMessage.setHead(head);

        // 设置消息体（使用国际化）
        TextCardMessageBody body = new TextCardMessageBody();
        TextCardElement contentElement = new TextCardElement();
        contentElement.setTextInfo(contentItem);
        body.setContentElement(contentElement);
        textCardMessage.setBody(body);

        // 设置消息底部（使用国际化）
        TextCardMessageFoot foot = new TextCardMessageFoot();
        TextCardElement buttonElement = new TextCardElement();
        buttonElement.setTextInfo(buttonItem);
        foot.setFootElement(buttonElement);
        textCardMessage.setFoot(foot);

        // 设置URL（Web端和移动端使用不同的URL）
        String h5Url = buildH5JumpUrl();
        textCardMessage.setInnerPlatformMobileUrl(h5Url);  // 移动端使用H5 URL
        textCardMessage.setInnerPlatformWebUrl(url);       // Web端使用传入的URL
        textCardMessage.setOutPlatformUrl(url);            // 外部平台使用Web URL

        // 设置文本卡片消息
        arg.setTextCardMessage(textCardMessage);

        return arg;
    }

    /**
     * 构建默认的文本卡片消息参数
     */
    private SendTextCardMessageArg buildDefaultArg(String appId) {
        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setUuid(java.util.UUID.randomUUID().toString());
        // 应用通知
        arg.setReceiverChannelType(ReceiverChannelType.NOTICE);
        arg.setGenerateUrlType(0);
        // APPID
        arg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData(appId));
        return arg;
    }





    /**
     * 构建多规则适配消息
     */
    private SalaryNotificationMessageUtil.MessageContent buildMultipleRulesMatchMessage(String tenantId, String employeeId, boolean isExternal) {
        try {
            // 直接使用员工I18n参数格式，不需要查询员工姓名和部门角色信息
            String employeeI18nParam = SalaryNotificationMessageUtil.buildEmployeeI18nParams(employeeDao,tenantId, Lists.newArrayList(employeeId));

            return SalaryNotificationMessageUtil.buildBatchNewEmployeeMessage("", employeeI18nParam); //ignoreI18n

        } catch (Exception e) {
            log.error("构建多规则适配消息失败，employeeId: {}", employeeId, e);
            // 返回默认消息，使用员工I18n参数格式
            String defaultEmployeeI18nParam = SalaryNotificationMessageUtil.buildEmployeeI18nParams(employeeDao,tenantId, Lists.newArrayList(employeeId));
            return SalaryNotificationMessageUtil.buildBatchNewEmployeeMessage("", defaultEmployeeI18nParam); //ignoreI18n
        }
    }

    /**
     * 构建规则被移出消息
     */
    private SalaryNotificationMessageUtil.MessageContent buildRuleRemovedMessage(String tenantId, String employeeFixedSalaryId, String removedRuleName) {
        try {
            // 获取员工固定工资表名称
            IObjectData employeeFixedSalary = employeeFixedSalaryDao.getById(tenantId, employeeFixedSalaryId);
            String employeeFixedSalaryName = "员工固定工资表"; //ignoreI18n
            if (employeeFixedSalary != null) {
                String name = employeeFixedSalary.get("name", String.class);
                if (StringUtils.isNotBlank(name)) {
                    employeeFixedSalaryName = name;
                }
            }

            return SalaryNotificationMessageUtil.buildRuleRemovedMessage(employeeFixedSalaryName, removedRuleName);

        } catch (Exception e) {
            log.error("构建规则被移出消息失败，employeeFixedSalaryId: {}", employeeFixedSalaryId, e);
            // 返回默认消息
            return SalaryNotificationMessageUtil.buildRuleRemovedMessage("员工固定工资表", removedRuleName != null ? removedRuleName : "未知规则"); //ignoreI18n
        }
    }


}