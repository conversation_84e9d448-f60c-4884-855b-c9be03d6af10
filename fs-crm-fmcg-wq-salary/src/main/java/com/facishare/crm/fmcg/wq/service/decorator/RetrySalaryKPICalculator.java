package com.facishare.crm.fmcg.wq.service.decorator;

import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.kpi.SalaryKPI;
import com.facishare.crm.fmcg.wq.service.SalaryKPICalculator;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * KPI计算重试装饰器
 * 为KPI计算提供三次重试机制，增强系统稳定性
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Getter
@Slf4j
public class RetrySalaryKPICalculator<T extends SalaryKPI> extends SalaryKPICalculator<T> {

    /**
     * -- GETTER --
     *  获取被装饰的计算器
     *
     * @return 被装饰的计算器
     */
    private final SalaryKPICalculator<T> decorated;
    /**
     * -- GETTER --
     *  获取最大重试次数
     *
     * @return 最大重试次数
     */
    private final int maxRetries;
    /**
     * -- GETTER --
     *  获取重试间隔时间
     *
     * @return 重试间隔时间（毫秒）
     */
    private final long retryDelayMs;

  /**
   * 构造函数
   * 
   * @param decorated 被装饰的KPI计算器
   */
  public RetrySalaryKPICalculator(SalaryKPICalculator<T> decorated) {
    this(decorated, 3, 100L);
  }

  /**
   * 构造函数
   * 
   * @param decorated 被装饰的KPI计算器
   * @param maxRetries 最大重试次数
   * @param retryDelayMs 重试间隔时间（毫秒）
   */
  public RetrySalaryKPICalculator(SalaryKPICalculator<T> decorated, int maxRetries, long retryDelayMs) {
    this.decorated = decorated;
    this.maxRetries = maxRetries;
    this.retryDelayMs = retryDelayMs;
  }

  @Override
  public void validate(T metric) {
    decorated.validate(metric);
  }

  @Override
  public MetricCalculateResult doCalculate(SalaryContext context, T metric) {
    Exception lastException = null;
    
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        log.debug("KPI计算尝试第{}次，KPI: {}, 员工: {}", attempt, metric.getName(), context.getOwner());
        
        MetricCalculateResult result = decorated.doCalculate(context, metric);
        
        if (attempt > 1) {
          log.info("KPI计算重试成功，KPI: {}, 员工: {}, 尝试次数: {}", 
                  metric.getName(), context.getOwner(), attempt);
        }
        
        return result;
        
      } catch (Exception e) {
        lastException = e;
        
        if (attempt < maxRetries) {
          log.warn("KPI计算失败，准备重试，KPI: {}, 员工: {}, 尝试次数: {}/{}, 错误: {}", 
                  metric.getName(), context.getOwner(), attempt, maxRetries, e.getMessage());
          
          // 等待一段时间后重试，使用指数退避策略
          try {
            long delay = retryDelayMs * (1L << (attempt - 1)); // 指数退避：100ms, 200ms, 400ms
            Thread.sleep(delay);
          } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            log.warn("KPI计算重试等待被中断，KPI: {}, 员工: {}", metric.getName(), context.getOwner());
            break;
          }
        } else {
          log.error("KPI计算重试失败，已达到最大重试次数，KPI: {}, 员工: {}, 最大重试次数: {}", 
                  metric.getName(), context.getOwner(), maxRetries, e);
        }
      }
    }
    
    // 所有重试都失败，抛出最后一次的异常
    throw new RuntimeException("KPI计算失败，已重试" + maxRetries + "次，KPI: " + metric.getName() + //ignoreI18n
                             ", 员工: " + context.getOwner(), lastException);//ignoreI18n
  }

  /**
   * 判断异常是否应该重试
   * 可以根据具体的异常类型来决定是否重试
   * 
   * @param exception 异常
   * @return 是否应该重试
   */
  private boolean shouldRetry(Exception exception) {
    // 对于以下类型的异常，不进行重试
    if (exception instanceof IllegalArgumentException ||
        exception instanceof NullPointerException ||
        exception instanceof ClassCastException) {
      return false;
    }
//
//    // 对于网络异常、超时异常等，进行重试
//    String message = exception.getMessage();
//    if (message != null) {
//      String lowerMessage = message.toLowerCase();
//      return lowerMessage.contains("timeout") ||
//             lowerMessage.contains("connection") ||
//             lowerMessage.contains("network") ||
//             lowerMessage.contains("socket") ||
//             lowerMessage.contains("服务异常") ||//ignoreI18n
//             lowerMessage.contains("系统繁忙");//ignoreI18n
//    }
    
    // 默认进行重试
    return true;
  }

}
