package com.facishare.crm.fmcg.wq.model.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 获取员工薪资类型和发放周期接口（返回员工的薪资规则和可查询的薪资周期列表）
 */
public interface EmployeeSalaryTypeQuery {
  @Data
  @ToString
  class Arg implements Serializable {
    /**
     * 员工ID
     */
    @JSONField(name = "employee_id")
    @JsonProperty("employee_id")
    @SerializedName("employee_id")
    private String employeeId;
    
    /**
     * 月份 yyyy-MM
     */
    @JSONField(name = "month")
    @JsonProperty("month")
    @SerializedName("month")
    private String month;
    /**
     * 工资条ID，如果没有对应的工资条则为null
     */
    @JSONField(name = "salary_data_id")
    @JsonProperty("salary_data_id")
    @SerializedName("salary_data_id")
    private String salaryDataId;

  }
  
  @Data
  @ToString
  class Result implements Serializable {
    /**
     * 定薪方式：1-日薪，2-周薪，3-月薪
     */
    @JSONField(name = "salary_method")
    @JsonProperty("salary_method")
    @SerializedName("salary_method")
    private String salaryMethod;
    
    /**
     * 定薪方式描述
     */
    @JSONField(name = "salary_method_desc")
    @JsonProperty("salary_method_desc")
    @SerializedName("salary_method_desc")
    private String salaryMethodDesc;
    
    @JSONField(name = "distribution_cycle")
    @JsonProperty("distribution_cycle")
    @SerializedName("distribution_cycle")
    /**
     * 发放周期：1-按日发放，2-按周发放，3-按月发放
     */
    private String distributionCycle;
    
    /**
     * 发放周期描述
     */
    @JSONField(name = "distribution_cycle_desc")
    @JsonProperty("distribution_cycle_desc")
    @SerializedName("distribution_cycle_desc")
    private String distributionCycleDesc;
    
    /**
     * 薪资规则ID
     */
    @JSONField(name = "salary_rule_id")
    @JsonProperty("salary_rule_id")
    @SerializedName("salary_rule_id")
    private String salaryRuleId;
    
    /**
     * 薪资规则名称
     */
    @JSONField(name = "salary_rule_name")
    @JsonProperty("salary_rule_name")
    @SerializedName("salary_rule_name")
    private String salaryRuleName;
    
    /**
     * 可查询的薪资周期列表
     */
    @JSONField(name = "available_periods")
    @JsonProperty("available_periods")
    @SerializedName("available_periods")
    private List<SalaryPeriod> availablePeriods;

    /**
     * 月份 yyyy-MM
     */
    @JSONField(name = "month")
    @JsonProperty("month")
    @SerializedName("month")
    private String month;
  }
  
  @Data
  class SalaryPeriod {
    /**
     * 周期ID，用于查询详细数据
     */
    @JSONField(name = "period_id")
    @JsonProperty("period_id")
    @SerializedName("period_id")
    private String periodId;
    
    /**
     * 周期标题，如"2023年12月"、"2023年12月第1周"、"2023年12月1日"
     */
    @JSONField(name = "period_title")
    @JsonProperty("period_title")
    @SerializedName("period_title")
    private String periodTitle;
    
    /**
     * 开始日期，格式：yyyy-MM-dd
     */
    @JSONField(name = "start_date")
    @JsonProperty("start_date")
    @SerializedName("start_date")
    private String startDate;
    
    /**
     * 结束日期，格式：yyyy-MM-dd
     */
    @JSONField(name = "end_date")
    @JsonProperty("end_date")
    @SerializedName("end_date")
    private String endDate;
    
    /**
     * 是否是当前周期
     */
    @JSONField(name = "is_current")
    @JsonProperty("is_current")
    @SerializedName("is_current")
    private Boolean isCurrent;

    /**
     * 发放状态 -2 没数据 -1 未发放 0 已发放 1 已结算
     */
    @JSONField(name = "distribution_status")
    @JsonProperty("distribution_status")
    @SerializedName("distribution_status")
    private String distributionStatus;
    
    /**
     * 工资条ID，如果没有对应的工资条则为null
     */
    @JSONField(name = "salary_data_id")
    @JsonProperty("salary_data_id")
    @SerializedName("salary_data_id")
    private String salaryDataId;
  }
}

