package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDetailDao;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.util.SalaryItemUsageValidator;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 工资项作废Action
 *
 * 作废规则：
 * 1. 当工资项禁用或启用后未被工资规则或员工固定工资表使用时，允许作废
 * 2. 当工资项被工资规则或员工固定工资表使用后，不可作废
 */
@Slf4j
public class SalaryItemInvalidAction extends StandardInvalidAction {

    private SalaryRuleDao salaryRuleDao = SpringUtil.getContext().getBean(SalaryRuleDao.class);
    private SalaryItemDao salaryItemDao = SpringUtil.getContext().getBean(SalaryItemDao.class);
    private EmployeeFixedSalaryDetailDao employeeFixedSalaryDetailDao = SpringUtil.getContext()
            .getBean(EmployeeFixedSalaryDetailDao.class);

    @Override
    protected void before(Arg arg) {
        String tenantId = actionContext.getTenantId();
        String salaryItemId = arg.getObjectDataId();

        log.info("开始处理工资项作废，工资项ID: {}", salaryItemId);

        // 获取工资项数据
        IObjectData salaryItemData = salaryItemDao.getById(tenantId, salaryItemId);
        if (salaryItemData == null) {
            throw new ValidateException("工资项不存在"); //ignoreI18n
        }

        // 使用工具类检查工资项使用状态
        SalaryItemUsageValidator.SalaryItemUsageInfo usageInfo = SalaryItemUsageValidator.checkSalaryItemUsage(
                tenantId, salaryItemId, salaryRuleDao, employeeFixedSalaryDetailDao);

        if (usageInfo.isUsed()) {
            log.warn("工资项 {} 已被 {} 个工资规则和 {} 个员工固定工资表使用，不允许作废",
                    salaryItemId, usageInfo.getUsingSalaryRules().size(), usageInfo.getUsingEmployeeFixedSalaryDetails().size());

            // 获取使用信息文案
            String usageMessage = usageInfo.getUsageMessage();
            log.warn("工资项 {} 被以下对象使用: {}", salaryItemId, usageMessage);

            throw new ValidateException(String.format("工资项已被使用，不可作废。%s", usageMessage)); //ignoreI18n
        }

        // 检查工资项的启用状态
        String enabledStatus = salaryItemData.get(SalaryItemFields.ENABLED_STATUS, String.class);
        if (SalaryItemFields.ENABLED_STATUS_Options_true.equals(enabledStatus)) {
            // 启用状态的工资项，需要进一步检查是否被使用
            log.info("工资项 {} 为启用状态，已验证未被工资规则或员工固定工资表使用，允许作废", salaryItemId);
        } else {
            // 禁用状态的工资项，可以直接作废
            log.info("工资项 {} 为禁用状态，允许作废", salaryItemId);
        }

        log.info("工资项 {} 未被工资规则或员工固定工资表使用，允许作废", salaryItemId);

        // 跳过基础校验
        this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        log.info("工资项作废成功，ID: {}", arg.getObjectDataId());
        return after;
    }


}
