package com.facishare.crm.fmcg.wq.service.decorator;

import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.kpi.SalaryKPI;
import com.facishare.crm.fmcg.wq.service.SalaryKPICalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 带日志记录的薪资KPI计算器装饰器
 * @author: dev
 * @create: 2024-07-10
 */
@Slf4j
public class LoggingSalaryKPICalculator<T extends SalaryKPI> extends SalaryKPICalculatorDecorator<T> {
    
    public LoggingSalaryKPICalculator(SalaryKPICalculator<T> decorated) {
        super(decorated);
    }
    
    @Override
    public MetricCalculateResult doCalculate(SalaryContext context, T metric) {
        log.info("开始计算KPI: {}, 员工: {}, 开始时间: {}, 结束时间: {}",
                metric.getName(), context.getOwner(), context.getStartTime(), context.getEndTime());
        
        long startTime = System.currentTimeMillis();
        MetricCalculateResult result = decorated.doCalculate(context, metric);
        long endTime = System.currentTimeMillis();
        
        log.info("KPI计算完成: {}, 结果: {}, 耗时: {}ms",
                metric.getName(), result.getValue(), (endTime - startTime));
        
        return result;
    }
}
