package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDao;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 薪资规则员工适用范围查询Controller
 * 根据规则查看员工适用范围的接口
 */
@Slf4j
public class SalaryRuleEmployeeScopeQueryController extends AbstractStandardController<SalaryRuleEmployeeScopeQueryController.Arg, SalaryRuleEmployeeScopeQueryController.Result> {

  private EmployeeFixedSalaryDao employeeFixedSalaryDao = SpringUtil.getContext().getBean(EmployeeFixedSalaryDao.class);
  private SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);
  private EmployeeDao employeeDao = SpringUtil.getContext().getBean(EmployeeDao.class);

  @Override
  protected void before(Arg arg) {
    super.before(arg);
    log.info("开始处理薪资规则员工适用范围查询，参数: {}", arg);

    // 参数校验
    if (arg.getSalaryRule() == null) {
      throw new ValidateException("薪资规则对象不能为空"); //ignoreI18n
    }
  }

  @Override
  protected Result doService(Arg arg) {
    String tenantId = controllerContext.getTenantId();
    IObjectData salaryRuleObj = arg.getSalaryRule();
    Boolean includeEmployeesWithoutFixedSalary = arg.getIncludeEmployeesWithoutFixedSalary();
    Boolean autoApplyRuleForNoRuleEmployees = arg.getAutoApplyRuleForNoRuleEmployees();

    log.info("查询薪资规则关联员工，tenantId: {}, includeEmployeesWithoutFixedSalary: {}, autoApplyRuleForNoRuleEmployees: {}",
            tenantId, includeEmployeesWithoutFixedSalary, autoApplyRuleForNoRuleEmployees);

    try {
      // 根据薪资规则获取员工ID列表
      List<String> employeeIds = salaryService.getEmployeeIdsBySalaryRule(tenantId, salaryRuleObj);

      if (employeeIds.isEmpty()) {
        log.info("根据薪资规则未找到员工");
        return new Result();
      }

      // 1. 直接根据员工ID查询员工固定工资表
      List<IObjectData> employeeFixedSalaryList = employeeFixedSalaryDao.getByEmployeeIdsWithRefObjects(tenantId, employeeIds);

      // 2. 处理员工信息
      Result result = processEmployeesData(tenantId, salaryRuleObj, employeeIds, employeeFixedSalaryList,
              includeEmployeesWithoutFixedSalary, autoApplyRuleForNoRuleEmployees);

      // 3. 统计数量信息
      result.setStatistics(calculateStatistics(tenantId, result, salaryRuleObj.getId()));

      log.info("查询薪资规则关联员工完成，常规员工: {}, 强制适用: {}, 强制不适用: {}, 无固定工资表: {}",
              result.getEmployees().size(), result.getMandatoryApplicableEmployees().size(),
              result.getNonApplicableEmployees().size(), result.getEmployeesWithoutFixedSalary().size());

      return result;

    } catch (Exception e) {
      log.error("查询薪资规则关联员工失败", e);
      throw new ValidateException("查询失败: " + e.getMessage()); //ignoreI18n
    }
  }

  /**
   * 处理员工数据
   *
   * @param tenantId 租户ID
   * @param salaryRuleObj 薪资规则对象
   * @param employeeIds 员工ID列表
   * @param employeeFixedSalaryList 员工固定薪资列表
   * @param includeEmployeesWithoutFixedSalary 是否包含无固定工资表的员工
   * @param autoApplyRuleForNoRuleEmployees 无规则员工是否自动适用当前规则
   * @return 处理后的结果
   */
  private Result processEmployeesData(String tenantId, IObjectData salaryRuleObj, List<String> employeeIds,
                                     List<IObjectData> employeeFixedSalaryList,
                                     Boolean includeEmployeesWithoutFixedSalary,
                                     Boolean autoApplyRuleForNoRuleEmployees) {
    Result result = new Result();

    // 1. 获取强制适用和强制不适用人员ID列表
    result.setMandatoryApplicableEmployees(getMandatoryApplicablePersonnelIds(salaryRuleObj));
    result.setNonApplicableEmployees(getNonApplicablePersonnelIds(salaryRuleObj));

    // 2. 处理有固定工资表的所有员工（返回完整数据）
    List<EmployeeInfo> allEmployeesWithFixedSalary = convertFixedSalaryEmployeesToInfoList(
            employeeFixedSalaryList, salaryRuleObj, autoApplyRuleForNoRuleEmployees);
    result.setEmployees(allEmployeesWithFixedSalary);

    // 3. 处理无固定工资表的员工（如果需要）
    if (includeEmployeesWithoutFixedSalary != null && includeEmployeesWithoutFixedSalary) {
      // 找出没有固定工资表的员工ID
      Set<String> employeesWithFixedSalary = employeeFixedSalaryList.stream()
              .map(this::getEmployeeIdFromFixedSalary)
              .filter(Objects::nonNull)
              .collect(Collectors.toSet());

      List<String> employeesWithoutFixedSalary = employeeIds.stream()
              .filter(id -> !employeesWithFixedSalary.contains(id))
              .collect(Collectors.toList());

      // 批量为无固定工资表的员工创建EmployeeInfo
      List<EmployeeInfo> employeesWithoutFixedSalaryInfo = createEmployeeInfoListWithoutFixedSalary(
              employeesWithoutFixedSalary, salaryRuleObj, autoApplyRuleForNoRuleEmployees);
      result.setEmployeesWithoutFixedSalary(employeesWithoutFixedSalaryInfo);
    }

    return result;
  }
  /**
   * 获取强制适用人员ID列表
   *
   * @param salaryRuleObj 薪资规则对象
   * @return 强制适用人员ID列表
   */
  private List<String> getMandatoryApplicablePersonnelIds(IObjectData salaryRuleObj) {
    if (salaryRuleObj == null) {
      return Lists.newArrayList();
    }

    try {
      Object mandatoryPersonnel = salaryRuleObj.get("mandatory_applicable_personnel");
      return convertToStringList(mandatoryPersonnel);
    } catch (Exception e) {
      log.error("获取强制适用人员ID列表失败", e);
      return Lists.newArrayList();
    }
  }

  /**
   * 获取强制不适用人员ID列表
   *
   * @param salaryRuleObj 薪资规则对象
   * @return 强制不适用人员ID列表
   */
  private List<String> getNonApplicablePersonnelIds(IObjectData salaryRuleObj) {
    if (salaryRuleObj == null) {
      return Lists.newArrayList();
    }

    try {
      Object nonApplicablePersonnel = salaryRuleObj.get("non_applicable_personnel");
      return convertToStringList(nonApplicablePersonnel);
    } catch (Exception e) {
      log.error("获取强制不适用人员ID列表失败", e);
      return Lists.newArrayList();
    }
  }

  /**
   * 从固定薪资对象获取员工ID
   *
   * @param fixedSalary 固定薪资对象
   * @return 员工ID
   */
  private String getEmployeeIdFromFixedSalary(IObjectData fixedSalary) {
    if (fixedSalary == null) {
      return null;
    }

    // 先尝试内部员工字段
    String employeeId = fixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE, String.class);
    if (StringUtils.isNotBlank(employeeId)) {
      return employeeId;
    }

    // 再尝试外部员工字段
    employeeId = fixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, String.class);
    return employeeId;
  }

  /**
   * 转换对象为字符串列表
   *
   * @param obj 对象
   * @return 字符串列表
   */
  private List<String> convertToStringList(Object obj) {
    if (obj == null) {
      return Lists.newArrayList();
    }

    if (obj instanceof List) {
      List<?> list = (List<?>) obj;
      return list.stream()
              .filter(Objects::nonNull)
              .map(Object::toString)
              .collect(Collectors.toList());
    } else if (obj instanceof String) {
      String str = (String) obj;
      if (StringUtils.isBlank(str)) {
        return Lists.newArrayList();
      }
      // 尝试按逗号分割
      return Lists.newArrayList(str.split(","))
              .stream()
              .map(String::trim)
              .filter(StringUtils::isNotBlank)
              .collect(Collectors.toList());
    } else {
      return Lists.newArrayList(obj.toString());
    }
  }
  /**
   * 批量为无固定工资表的员工创建EmployeeInfo列表
   *
   * @param employeeIds 员工ID列表
   * @param salaryRuleObj 薪资规则对象
   * @param autoApplyRuleForNoRuleEmployees 是否自动适用当前规则
   * @return 员工信息列表
   */
  private List<EmployeeInfo> createEmployeeInfoListWithoutFixedSalary(List<String> employeeIds,
                                                                      IObjectData salaryRuleObj,
                                                                      Boolean autoApplyRuleForNoRuleEmployees) {
    if (employeeIds == null || employeeIds.isEmpty()) {
      return Lists.newArrayList();
    }

    List<EmployeeInfo> employeeInfoList = Lists.newArrayList();

    try {
      Map<String, String> employeeNameMap = employeeDao.getUserNameByStringIds(controllerContext.getTenantId(),employeeIds);

      // 4. 创建EmployeeInfo对象
      for (String employeeId : employeeIds) {
        EmployeeInfo employeeInfo = new EmployeeInfo();

        // 基本信息
        employeeInfo.setEmployeeId(employeeId);

        // 员工类型和姓名
        String employeeType = employeeDao.isExternalEmployee(employeeId) ? EmployeeFixedSalaryFields.RECORDTYPE_EXTERNAL: EmployeeFixedSalaryFields.RECORDTYPE_INTERNAL;
        employeeInfo.setEmployeeType(employeeType);
        employeeInfo.setEmployeeName(employeeNameMap.get(employeeId));

        // 无固定工资表相关字段设为null
        employeeInfo.setFixedSalaryId(null);
        employeeInfo.setSalaryMethod(null);

        // 根据autoApplyRuleForNoRuleEmployees参数决定是否自动适用当前规则
        if (Boolean.TRUE.equals(autoApplyRuleForNoRuleEmployees)) {
          employeeInfo.setSalaryRuleId(salaryRuleObj.getId());
          employeeInfo.setSalaryRuleName(salaryRuleObj.getName());
          employeeInfo.setAutoApplied(true); // 标记为自动适用
        } else {
          employeeInfo.setSalaryRuleId(null);
          employeeInfo.setSalaryRuleName(null);
          employeeInfo.setAutoApplied(false);
        }

        employeeInfoList.add(employeeInfo);
      }

      log.info("批量创建了{}个无固定工资表的员工信息", employeeInfoList.size());

    } catch (Exception e) {
      log.error("批量创建无固定工资表员工信息失败", e);
    }

    return employeeInfoList;
  }





  /**
   * 转换有固定工资表的员工信息列表
   *
   * @param employeeFixedSalaryList 员工固定工资表列表
   * @param currentSalaryRule 当前薪资规则对象
   * @param autoApplyRuleForNoRuleEmployees 无规则员工是否自动适用当前规则
   * @return 员工信息列表
   */
  private List<EmployeeInfo> convertFixedSalaryEmployeesToInfoList(List<IObjectData> employeeFixedSalaryList,
                                                                  IObjectData currentSalaryRule,
                                                                  Boolean autoApplyRuleForNoRuleEmployees) {
    List<EmployeeInfo> employees = Lists.newArrayList();
    String currentRuleId = currentSalaryRule.getId();
    String currentRuleName = currentSalaryRule.getName();

    for (IObjectData fixedSalary : employeeFixedSalaryList) {
      EmployeeInfo employeeInfo = new EmployeeInfo();

      // 基本信息
      employeeInfo.setFixedSalaryId(fixedSalary.getId());
      employeeInfo.setSalaryMethod(fixedSalary.get(EmployeeFixedSalaryFields.SALARY_METHOD, String.class));

      // 使用 recordtype 获取员工类型
      String recordType = fixedSalary.get(BaseField.recordType.getApiName(), String.class);
      employeeInfo.setEmployeeType(recordType);

      // 员工信息 - 根据 recordtype 确定使用哪个字段
      String employeeId = null;
      String employeeName = null;

      if (EmployeeFixedSalaryFields.RECORDTYPE_INTERNAL.equals(recordType)) {
        // 内部员工
        employeeId = fixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE, String.class);
        // 从关联对象字段获取员工姓名（__r 字段返回字符串）
        employeeName = fixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE_NAME, String.class);
      } else if (EmployeeFixedSalaryFields.RECORDTYPE_EXTERNAL.equals(recordType)) {
        // 外部员工
        employeeId = fixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, String.class);
        // 从关联对象字段获取员工姓名（__r 字段返回字符串）
        employeeName = fixedSalary.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL_NAME, String.class);
      }

      employeeInfo.setEmployeeId(employeeId);
      employeeInfo.setEmployeeName(employeeName);

      // 薪资规则信息
      String salaryRuleId = fixedSalary.get(EmployeeFixedSalaryFields.SALARY_RULE, String.class);
      if (salaryRuleId != null) {
        if (currentRuleId.equals(salaryRuleId)) {
          employeeInfo.setSalaryRuleId(currentRuleId);
          employeeInfo.setSalaryRuleName(currentRuleName);
        } else {
          employeeInfo.setSalaryRuleId(salaryRuleId);
          // 从关联对象字段获取规则名称（__r 字段返回字符串）
          String salaryRuleName = fixedSalary.get(EmployeeFixedSalaryFields.SALARY_RULE_NAME, String.class);
          if (salaryRuleName != null) {
            employeeInfo.setSalaryRuleName(salaryRuleName);
          } else {
            employeeInfo.setSalaryRuleName(getSalaryRuleName(salaryRuleId));
          }
        }
      } else {
        // 无薪资规则的员工，根据autoApplyRuleForNoRuleEmployees参数决定是否自动适用当前规则
        if (Boolean.TRUE.equals(autoApplyRuleForNoRuleEmployees)) {
          employeeInfo.setSalaryRuleId(currentRuleId);
          employeeInfo.setSalaryRuleName(currentRuleName);
          employeeInfo.setAutoApplied(true); // 标记为自动适用
        }
      }

      employees.add(employeeInfo);
    }

    return employees;
  }



  /**
   * 判断是否为外部员工
   *
   * @param employeeId 员工ID
   * @return 是否为外部员工
   */
  private boolean isExternalEmployee(String employeeId) {
    if (StringUtils.isBlank(employeeId)) {
      return false;
    }

    try {
      long id = Long.parseLong(employeeId);
      return id > 100000000L;
    } catch (NumberFormatException e) {
      log.warn("员工ID格式异常，employeeId: {}, 默认为内部员工", employeeId);
      return false;
    }
  }


  /**
   * 获取薪资规则名称
   *
   * @param salaryRuleId 薪资规则ID
   * @return 薪资规则名称
   */
  private String getSalaryRuleName(String salaryRuleId) {
    try {
      // 这里可以调用薪资规则服务获取规则名称
      // 暂时返回规则ID作为名称
      return "规则" + salaryRuleId; //ignoreI18n
    } catch (Exception e) {
      log.warn("获取薪资规则名称失败，salaryRuleId: {}", salaryRuleId, e);
      return "未知规则"; //ignoreI18n
    }
  }

  /**
   * 计算统计信息
   *
   * @param tenantId  租户ID
   * @param result 查询结果
   * @param currentRuleId 当前规则ID
   * @return 统计信息
   */
  private StatisticsInfo calculateStatistics(String tenantId, Result result, String currentRuleId) {
    StatisticsInfo statistics = new StatisticsInfo();

    // 统计有固定工资表的员工
    for (EmployeeInfo employee : result.getEmployees()) {
      if (employee.getSalaryRuleId() != null) {
        // 有固定工资表且有规则的员工
        if (employee.getSalaryRuleId().equals(currentRuleId)) {
          statistics.setCurrentRuleCount(statistics.getCurrentRuleCount() + 1);
        } else {
          statistics.setOtherRuleCount(statistics.getOtherRuleCount() + 1);
        }
      } else {
        // 有固定工资表但无规则的员工
        statistics.setNoRuleCount(statistics.getNoRuleCount() + 1);
      }
    }

    // 统计无固定工资表的员工
    statistics.setNoFixedSalaryCount(result.getEmployeesWithoutFixedSalary().size());

    // 统计强制适用和强制不适用人员数量
    statistics.setMandatoryApplicableCount(result.getMandatoryApplicableEmployees().size());
    statistics.setNonApplicableCount(result.getNonApplicableEmployees().size());

    // 计算总数
    int totalCount = result.getEmployees().size() + result.getEmployeesWithoutFixedSalary().size();
    statistics.setTotalCount(totalCount);

    log.info("统计信息：当前规则{}人，其他规则{}人，无规则{}人，无固定工资表{}人，强制适用{}人，强制不适用{}人，总计{}人",
            statistics.getCurrentRuleCount(), statistics.getOtherRuleCount(),
            statistics.getNoRuleCount(), statistics.getNoFixedSalaryCount(),
            statistics.getMandatoryApplicableCount(), statistics.getNonApplicableCount(), statistics.getTotalCount());

    return statistics;
  }

  /**
   * Controller 请求参数
   */
  @Data
  public static class Arg  implements Serializable {
    /**
     * 薪资规则对象 - 必填
     * 包含修改后的适用范围等信息
     */
    private IObjectData salaryRule;

    /**
     * 是否包含无固定工资表的员工 - 可选，默认false
     * true: 返回无固定工资表的员工数据，并检查是否适配当前规则
     * false: 只返回有固定工资表的员工数据
     */
    private Boolean includeEmployeesWithoutFixedSalary = false;

    /**
     * 无规则的员工是否自动适用当前规则 - 可选，默认false
     * true: 无规则的员工会被标记为可以适用当前规则
     * false: 无规则的员工保持原状
     */
    private Boolean autoApplyRuleForNoRuleEmployees = true;
  }

  @Override
  protected List<String> getFuncPrivilegeCodes() {
    return Lists.newArrayList();
  }

  /**
   * 员工信息
   */
  @Data
  public static class EmployeeInfo {
    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 员工姓名
     */
    private String employeeName;

    /**
     * //员工类型 内部人员 default__c 外部人员 external__c
     *     public static final String RECORDTYPE_INTERNAL = "default__c";
     *
     *     public static final String RECORDTYPE_EXTERNAL = "record_external__c";
     */
    private String employeeType;

    /**
     * 所属薪资规则ID
     */
    private String salaryRuleId;

    /**
     * 所属薪资规则名称
     */
    private String salaryRuleName;

    /**
     * 固定薪资ID
     */
    private String fixedSalaryId;

    /**
     * 定薪方式
     */
    private String salaryMethod;

    /**
     * 是否为自动适用（当无规则员工自动适用当前规则时为true）
     */
    private Boolean autoApplied = false;
  }



  /**
   * 统计信息
   */
  @Data
  public static class StatisticsInfo implements Serializable {
    /**
     * 属于当前规则的员工数量
     */
    private Integer currentRuleCount;

    /**
     * 属于其他规则的员工数量
     */
    private Integer otherRuleCount;

    /**
     * 无规则的员工数量
     */
    private Integer noRuleCount;

    /**
     * 总员工数量
     */
    private Integer totalCount;

    /**
     * 无固定工资表的员工数量
     */
    private Integer noFixedSalaryCount;

    /**
     * 强制适用当前规则的员工数量
     */
    private Integer mandatoryApplicableCount;

    /**
     * 强制不适用当前规则的员工数量
     */
    private Integer nonApplicableCount;

    public StatisticsInfo() {
      this.currentRuleCount = 0;
      this.otherRuleCount = 0;
      this.noRuleCount = 0;
      this.totalCount = 0;
      this.noFixedSalaryCount = 0;
      this.mandatoryApplicableCount = 0;
      this.nonApplicableCount = 0;
    }
  }

  /**
   * 返回结果
   */
  @Data
  public static class Result  implements Serializable{
    /**
     * 有固定工资表的所有员工信息列表（完整数据）
     */
    private List<EmployeeInfo> employees;

    /**
     * 强制适用当前规则的员工ID列表
     */
    private List<String> mandatoryApplicableEmployees;

    /**
     * 强制不适用当前规则的员工ID列表
     */
    private List<String> nonApplicableEmployees;

    /**
     * 无固定工资表的所有员工信息列表（完整数据）
     */
    private List<EmployeeInfo> employeesWithoutFixedSalary;

    /**
     * 统计信息
     */
    private StatisticsInfo statistics;

    public Result() {
      this.employees = Lists.newArrayList();
      this.mandatoryApplicableEmployees = Lists.newArrayList();
      this.nonApplicableEmployees = Lists.newArrayList();
      this.employeesWithoutFixedSalary = Lists.newArrayList();
      this.statistics = new StatisticsInfo();
    }
  }
}
