package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.extern.slf4j.Slf4j;

/**
 * 工资条新增Action
 * 
 * 功能：阻止前端直接新增工资条
 */
@Slf4j
public class SalaryDataAddAction extends FmcgUnsupportedExceptionAction {

//    @Override
//    protected void before(Arg arg) {
//        log.error("工资条新增操作被阻止，不允许前端直接新增工资条");
//        throw new ValidateException("不允许直接新增工资条，请通过工资发放单生成"); //ignoreI18n
//    }
}
