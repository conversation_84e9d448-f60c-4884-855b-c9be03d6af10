package com.facishare.crm.fmcg.wq.schedule;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.wq.mq.PMMSalaryProvider;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 工资规则定时任务
 * 每天凌晨2点扫描所有工资规则，发送生成工资明细的消息
 * 每天凌晨3点扫描所有工资规则，发送生成发放单的消息
 */
@Slf4j
@Component
public class SalaryScheduleTask {

  @Autowired
  private PMMSalaryProvider pmmSalaryProvider;

  @Autowired
  private CheckinsProxy checkinsProxy;


  @Autowired
  private RedissonService redissonService;

  @Autowired
  private EIEAConverter eieaConverter;

  // Redis锁的前缀
  private static final String SALARY_SCHEDULE_LOCK_PREFIX = "fs-crm-fmcg-wq-salary-schedule-lock-";

  // 锁的过期时间（秒）- 30分钟，单个租户处理时间
  private static final long LOCK_EXPIRE_TIME = 1800L;

  /**
   * 每天凌晨2点执行 - 生成工资明细
   * cron表达式：秒 分 时 日 月 周
   */
  @Scheduled(cron = "0 0 2 * * ?")
  //  临时测试 每分钟一次
//  @Scheduled(cron = "0 0/1 * * * ?")
  public void generateSalaryDetailTask() {LocalDate currentDate = LocalDate.now();
    log.info("开始执行生成工资明细定时任务，日期: {}", currentDate);

    try {
      processSalaryRules(0, currentDate); // flag=0 表示生成工资明细
      log.info("生成工资明细定时任务执行完成");
    } catch (Exception e) {
      log.error("生成工资明细定时任务执行异常", e);
    }
  }

  /**
   * 每天凌晨3点执行 - 生成发放单
   * cron表达式：秒 分 时 日 月 周
   */
  @Scheduled(cron = "0 0 3 * * ?")
//  临时测试 每分钟一次
//  @Scheduled(cron = "0 0/1 * * * ?")
  public void generatePaymentSlipTask() {
    LocalDate currentDate = LocalDate.now();
    log.info("开始执行生成发放单定时任务，日期: {}", currentDate);

    try {
      processSalaryRules(1, currentDate); // flag=1 表示生成发放单
      log.info("生成发放单定时任务执行完成");
    } catch (Exception e) {
      log.error("生成发放单定时任务执行异常", e);
    }
  }

  /**
   * 处理工资规则，发送消息
   * 按租户分别加锁，支持并发处理
   *
   * @param flag 0-生成工资明细，1-生成发放单
   * @param currentDate 当前日期
   */
  public void processSalaryRules(int flag, LocalDate currentDate) {
    try {
      // 获取活跃的租户列表
      List<String> eas = getActiveEas();

      if (eas.isEmpty()) {
        log.info("未获取到活跃租户列表，跳过处理");
        return;
      }

      log.info("开始处理 {} 个租户的工资规则，flag={}", eas.size(), flag);

      int totalSuccessCount = 0;
      int totalFailCount = 0;
      int processedCount = 0;
      int skippedCount = 0;

      // 为每个租户处理工资规则
      for (String ea : eas) {
        String taskType = flag == 0 ? "detail" : "payment";
        String lockKey = SALARY_SCHEDULE_LOCK_PREFIX + taskType + "-" + ea + "-" + currentDate.toString();
        RLock lock = redissonService.tryLock(0, LOCK_EXPIRE_TIME, TimeUnit.SECONDS, lockKey);
        // 为每个租户单独加锁
        if (Objects.isNull(lock)) {
          log.info("租户 {} 的{}任务已被其他实例执行，跳过处理", ea, taskType);
          skippedCount++;
          continue;
        }

        try {
          // 为每个租户生成新的 traceId
          String tenantTraceId = generateTenantTraceId(ea, flag, currentDate);
          TraceContext.get().setTraceId(tenantTraceId);

          log.info("开始处理租户 {} 的工资规则，traceId: {}", ea, tenantTraceId);
          int eId =  eieaConverter.enterpriseAccountToId(ea);
          String tenantId = String.valueOf(eId);
          // 调用 PMMSalaryProvider 处理该租户的工资规则
          int[] result = pmmSalaryProvider.processSalaryRulesForTenant(tenantId, flag, currentDate);
          totalSuccessCount += result[0];
          totalFailCount += result[1];
          processedCount++;

        } catch (Exception e) {
          log.error("处理租户 {} 的工资规则失败: flag={}", ea, flag, e);
          totalFailCount++;
        } finally {
          // 清理 traceId 并释放锁
          TraceContext.remove();
//          redissonService.unlock(lock);
        }
      }

      log.info("所有租户薪资任务处理完成: 处理={}, 跳过={}, 成功={}, 失败={}, flag={}",
              processedCount, skippedCount, totalSuccessCount, totalFailCount, flag);

    } catch (Exception e) {
      log.error("处理工资规则异常: flag={}", flag, e);
      throw e;
    }
  }





  /**
   * 获取活跃的租户ID列表
   * 通过 CheckinsProxy 获取所有 PMM EA
   * 支持重试机制
   *
   * @return 租户ID列表
   */
  private List<String> getActiveEas() {
    int maxRetries = 3;
    long retryDelayMs = 1000L; // 1秒
    Exception lastException = null;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        log.debug("获取活跃租户列表尝试第{}次", attempt);

        // 调用 CheckinsProxy 获取所有 PMM EA
        List<String> eas = checkinsProxy.getAllPMMEA("-100");

        if (eas == null || eas.isEmpty()) {
          log.warn("未获取到活跃的 PMM EA 列表");
          return new ArrayList<>();
        }

        if (attempt > 1) {
          log.info("获取活跃租户列表重试成功，尝试次数: {}", attempt);
        }

        log.info("获取到 {} 个活跃的 PMM EA: {}", eas.size(), eas);
        return eas;

      } catch (Exception e) {
        lastException = e;

        if (attempt < maxRetries) {
          log.warn("获取活跃租户列表失败，准备重试，尝试次数: {}/{}, 错误: {}",
                  attempt, maxRetries, e.getMessage());

          // 等待一段时间后重试，使用指数退避策略
          try {
            long delay = retryDelayMs * (1L << (attempt - 1)); // 指数退避：1s, 2s, 4s
            Thread.sleep(delay);
          } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            log.warn("获取活跃租户列表重试等待被中断");
            break;
          }
        } else {
          log.error("获取活跃租户列表重试失败，已达到最大重试次数: {}", maxRetries, e);
        }
      }
    }

    // 所有重试都失败，记录错误并返回空列表
    log.error("获取活跃租户列表失败，已重试{}次，返回空列表", maxRetries, lastException);
    return new ArrayList<>();
  }

  /**
   * 为租户生成专用的 traceId
   * 格式：fs-crm-fmcg-wq_salary-{flag}-{tenantId}-{date}-{uuid}
   *
   * @param tenantId 租户ID
   * @param flag 任务标识（0-生成工资明细，1-生成发放单）
   * @param currentDate 当前日期
   * @return 生成的 traceId
   */
  private String generateTenantTraceId(String tenantId, int flag, LocalDate currentDate) {
    String taskType = flag == 0 ? "detail" : "payment";
    String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    return String.format("fs-crm-fmcg-wq_salary-%s-%s-%s-%s", taskType, tenantId, currentDate.toString(), uuid);
  }

}
