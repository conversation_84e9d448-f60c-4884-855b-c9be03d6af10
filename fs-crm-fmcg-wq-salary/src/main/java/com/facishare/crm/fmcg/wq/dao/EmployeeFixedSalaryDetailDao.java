
package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryDetailFields;
import com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class EmployeeFixedSalaryDetailDao extends AbstractDao{
    final static List<String> employeeFixedSalaryDetailFields = ConfigUtils.getFields(EmployeeFixedSalaryDetailFields.class);



    public List<IObjectData> getByIds(String tenantId, List<String> kpiIds) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), kpiIds)
                .build(), EmployeeFixedSalaryDetailFields.API_NAME, employeeFixedSalaryDetailFields);
    }
    /**
     * 通过员工固定工资id 查询员工固定工资明细
     */
    public List<IObjectData> getByEmployeeFixedSalaryMainId(String tenantId, String employeeFixedSalaryId) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, employeeFixedSalaryId)
                .build(), EmployeeFixedSalaryDetailFields.API_NAME, employeeFixedSalaryDetailFields);
    }

    /**
     * 根据工资项ID查询使用该工资项的员工固定工资明细
     *
     * @param tenantId 租户ID
     * @param salaryItemId 工资项ID
     * @return 使用该工资项的员工固定工资明细列表
     */
    public List<IObjectData> getBySalaryItemId(String tenantId, String salaryItemId) {
        List<IObjectData> detailList = getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(EmployeeFixedSalaryDetailFields.SALARY_ITEM, salaryItemId)
                .build(), EmployeeFixedSalaryDetailFields.API_NAME, employeeFixedSalaryDetailFields);

        // 填充关联对象信息，以便获取员工固定工资表的名称
        return fillRefObject(User.systemUser(tenantId), EmployeeFixedSalaryDetailFields.API_NAME, detailList);
    }

    /**
     * 批量查询员工固定工资明细
     *
     * @param tenantId 租户ID
     * @param employeeFixedSalaryIds 员工固定工资表ID列表
     * @return 员工固定工资明细列表
     */
    public List<IObjectData> getByEmployeeFixedSalaryIds(String tenantId, List<String> employeeFixedSalaryIds) {
        if (employeeFixedSalaryIds == null || employeeFixedSalaryIds.isEmpty()) {
            return Lists.newArrayList();
        }

        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, employeeFixedSalaryIds)
                .build(), EmployeeFixedSalaryDetailFields.API_NAME, employeeFixedSalaryDetailFields);
    }
}
