package com.facishare.crm.fmcg.wq.notify;

import com.facishare.crm.fmcg.wq.session.SessionContent;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.model.message.SendTextCardMessageArg;

import java.util.List;

/**
 * 工资条消息服务接口
 * <p>
 * 该接口定义了发送工资条消息的相关方法
 * </p>
 */
public interface SalaryMessageService {

    /**
     * 发送内部员工工资条消息
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param salaryData 工资条数据
     * @return 是否发送成功
     */
    boolean sendInternalEmployeeMessage(String tenantId, String employeeId, IObjectData salaryData);

    /**
     * 发送外部员工工资条消息
     *
     * @param tenantId 租户ID
     * @param employeeExternalId 外部员工ID
     * @param salaryData 工资条数据
     * @return 是否发送成功
     */
    boolean sendExternalEmployeeMessage(String tenantId, String employeeExternalId, IObjectData salaryData);

    /**
     * 批量发送内部员工工资条消息
     *
     * @param tenantId 租户ID
     * @param salaryDataList 内部员工工资条列表
     * @return 成功发送的消息数量
     */
    int batchSendInternalEmployeeMessages(String tenantId, List<IObjectData> salaryDataList);

    /**
     * 批量发送外部员工工资条消息
     *
     * @param tenantId 租户ID
     * @param salaryDataList 外部员工工资条列表
     * @return 成功发送的消息数量
     */
    int batchSendExternalEmployeeMessages(String tenantId, List<IObjectData> salaryDataList);

    /**
     * 创建工资条文本卡片消息参数
     *
     * @param salaryData 工资条数据
     * @return 文本卡片消息参数
     */
    SendTextCardMessageArg createSalaryTextCardMessageArg(IObjectData salaryData);
}