package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryKPIDao;
import com.facishare.crm.fmcg.wq.util.DescribeUtils;
import com.facishare.crm.fmcg.wq.util.LayoutUtils;
import com.facishare.crm.fmcg.wq.util.SalaryItemFormulaUtil;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import static com.facishare.crm.fmcg.wq.service.impl.SalaryExpressionCalcServiceImpl.KPI_EXT_START;

/**
 * 工资项详情页控制器
 * 重写公式描述字段，将计算公式格式化为易读的描述
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class SalaryItemWebDetailController extends StandardWebDetailController {
  private SalaryKPIDao salaryKPIDao = SpringUtil.getContext().getBean(SalaryKPIDao.class);
  private SalaryItemDao salaryItemDao = SpringUtil.getContext().getBean(SalaryItemDao.class);

  @Override
  protected Result after(Arg arg, Result result) {
    log.info("SalaryItemWebDetailController after method called");
    result = super.after(arg, result);

    try {
      // 添加计算公式描述虚拟字段到布局（在计算公式字段之后）
      LayoutUtils.addVirtualFieldToDetailLayout(
          result.getLayout(),
          SalaryItemFields.CALCULATION_FORMULA,
          SalaryItemFields.CALCULATION_FORMULA_DESC_DISPLAY
      );

      // 添加虚拟字段到对象描述中
      result.setDescribe(DescribeUtils.addSalaryItemFormulaVirtualFieldToObjectDescribe(
          result.getDescribe(),
          SalaryItemFields.CALCULATION_FORMULA_DESC_DISPLAY
      ));

      // 获取工资项数据
      ObjectDataDocument dataDocument = result.getData();
      if (dataDocument == null) {
        log.warn("工资项数据为空，跳过公式描述处理");
        return result;
      }

      ObjectDataExt salaryItemExt = ObjectDataExt.of(dataDocument);
      String tenantId = controllerContext.getTenantId();

      // 设置计算公式描述虚拟字段的值

      String calculationFormulaDesc = SalaryItemFormulaUtil.buildFormulaDescription(
              tenantId, salaryItemExt, salaryKPIDao);
      dataDocument.put(SalaryItemFields.CALCULATION_FORMULA_DESC_DISPLAY, calculationFormulaDesc);

//      // 保持向后兼容：继续处理原有的公式描述字段
//      String valueType = salaryItemExt.get(SalaryItemFields.VALUE_TYPE, String.class);
//      if (SalaryItemFields.VALUE_TYPE_Options_2.equals(valueType)) {
//        validateAndUpdateCalculationFormulaDescription(salaryItemExt, dataDocument, result);
//      }

      log.debug("工资项详情页计算公式描述字段处理完成");

    } catch (Exception e) {
      log.error("处理工资项详情页时发生异常", e);
      // 不影响正常显示，只记录错误日志
    }

    return result;
  }

  /**
   * 校验并更新计算公式描述字段
   * 比较数据库中存储的 calculation_formula_desc 值与重新计算得出的值，如果不一致则更新数据库中的值
   *
   * @param salaryItemExt 工资项扩展对象
   * @param dataDocument 数据文档
   * @param result 控制器结果
   */
  private void validateAndUpdateCalculationFormulaDescription(ObjectDataExt salaryItemExt,
                                                              ObjectDataDocument dataDocument, Result result) {
    try {
      String tenantId = salaryItemExt.getTenantId();

      // 使用工具类重新计算公式描述
      String newCalculationFormulaDesc = SalaryItemFormulaUtil.buildFormulaDescription(
          tenantId, salaryItemExt, salaryKPIDao);

      // 获取数据库中存储的公式描述
      String existingFormulaDesc = salaryItemExt.getStringValue(SalaryItemFields.CALCULATION_FORMULA_DESC);

      // 比较公式描述是否一致
      boolean formulaDescChanged = !isFormulaEqual(existingFormulaDesc, newCalculationFormulaDesc);

      if (formulaDescChanged) {
        log.info("检测到计算公式描述不一致，需要更新。原描述: [{}], 新描述: [{}]", existingFormulaDesc, newCalculationFormulaDesc);

        // 更新数据库中的公式描述
        updateCalculationFormulaDescriptionInDatabase(tenantId, salaryItemExt.getId(), newCalculationFormulaDesc);
      }

      // 无论是否更新数据库，都要确保前端显示的是最新计算的公式描述
      if (StringUtils.isNotBlank(newCalculationFormulaDesc)) {
        dataDocument.put(SalaryItemFields.CALCULATION_FORMULA_DESC, newCalculationFormulaDesc);
        result.setData(dataDocument);
        log.info("工资项公式描述已重写: {}", newCalculationFormulaDesc);
      }

    } catch (Exception e) {
      log.error("校验和更新计算公式描述时发生异常", e);
      // 不影响正常显示，只记录错误日志
    }
  }

  /**
   * 比较两个公式是否相等
   * 处理null值和空字符串的情况
   *
   * @param formula1 公式1
   * @param formula2 公式2
   * @return 是否相等
   */
  private boolean isFormulaEqual(String formula1, String formula2) {
    // 将null和空字符串都视为相等
    String f1 = StringUtils.isBlank(formula1) ? "" : formula1.trim();
    String f2 = StringUtils.isBlank(formula2) ? "" : formula2.trim();
    return f1.equals(f2);
  }

  /**
   * 更新数据库中的计算公式描述
   *
   * @param tenantId 租户ID
   * @param salaryItemId 工资项ID
   * @param newFormulaDesc 新的计算公式描述
   */
  private void updateCalculationFormulaDescriptionInDatabase(String tenantId, String salaryItemId, String newFormulaDesc) {
    try {
      // 获取工资项对象
      IObjectData salaryItem = salaryItemDao.getById(tenantId, salaryItemId);
      if (salaryItem == null) {
        log.warn("未找到工资项，无法更新计算公式描述，ID: {}", salaryItemId);
        return;
      }

      // 更新计算公式描述字段
      salaryItem.set(SalaryItemFields.CALCULATION_FORMULA_DESC, newFormulaDesc);

      // 保存到数据库
      salaryItemDao.update(com.facishare.paas.appframework.core.model.User.systemUser(tenantId), salaryItem);

      log.info("已更新数据库中的计算公式描述，工资项ID: {}, 新描述: {}", salaryItemId, newFormulaDesc);

    } catch (Exception e) {
      log.error("更新数据库中的计算公式描述失败，工资项ID: {}", salaryItemId, e);
    }
  }

  /**
   * 构建公式描述
   * 参考SalaryServiceImpl的逻辑，构造extDataNameMap并使用formatFormula方法
   *
   * @param salaryItemExt 工资项扩展对象
   * @return 格式化后的公式字符串
   */
  private String buildFormulaDescription(ObjectDataExt salaryItemExt) {
    try {
      // 获取计算公式
      String calculationFormula = salaryItemExt.get(SalaryItemFields.CALCULATION_FORMULA, String.class);

      if (StringUtils.isBlank(calculationFormula)) {
        log.debug("工资项计算公式为空，无法生成公式描述");
        return null;
      }

      // 构造extDataNameMap，用于变量名到实际名称的映射
      Map<String, Object> extDataNameMap = buildExtDataNameMap(salaryItemExt);

      // 使用SalaryServiceImpl的formatFormula逻辑进行变量替换
      String formattedFormula = formatFormula(calculationFormula, extDataNameMap);

//      // 再进行中文操作符转换
//      String finalFormula = formatCalculationFormula(formattedFormula);
      String finalFormula = formattedFormula;
      log.debug("构建的工资项公式描述: {}", finalFormula);
      return finalFormula;

    } catch (Exception e) {
      log.error("构建工资项公式描述时发生异常", e);
      return null;
    }
  }



  /**
   * 格式化计算公式
   * 参考SalaryServiceImpl.formatFormula的逻辑，将公式转换为中文描述
   *
   * @param calculationFormula 原始计算公式
   * @return 格式化后的公式描述
   */
  private String formatCalculationFormula(String calculationFormula) {
    if (StringUtils.isBlank(calculationFormula)) {
      return null;
    }

    try {
      String formattedFormula = calculationFormula.trim();

      // 参考SalaryExpressionCalcServiceImpl.formatExpression的逻辑
      // 替换特殊字符处理
      formattedFormula = formattedFormula.replace("&&", "$$") // 防止&& 一同被转换的
              .replace("&", "+").replace("$$", "&&");

      // 替换常见的操作符为中文描述
      formattedFormula = formattedFormula.replaceAll("\\+", " 加 "); //ignoreI18n
      formattedFormula = formattedFormula.replaceAll("-", " 减 "); //ignoreI18n
      formattedFormula = formattedFormula.replaceAll("\\*", " 乘以 "); //ignoreI18n
      formattedFormula = formattedFormula.replaceAll("/", " 除以 "); //ignoreI18n
      formattedFormula = formattedFormula.replaceAll("\\(", "（");
      formattedFormula = formattedFormula.replaceAll("\\)", "）");

      // 清理多余的空格
      formattedFormula = formattedFormula.replaceAll("\\s+", " ").trim();

      // 如果公式太长，进行截断
      if (formattedFormula.length() > 200) {
        formattedFormula = formattedFormula.substring(0, 197) + "...";
      }

      return formattedFormula;

    } catch (Exception e) {
      log.warn("格式化计算公式失败: {}", calculationFormula, e);
      return calculationFormula; // 返回原始公式
    }
  }

  /**
   * 构建扩展数据名称映射
   * 参考SalaryServiceImpl中的逻辑，主要处理KPI相关的变量名映射
   *
   * @param salaryItemExt 工资项扩展对象
   * @return 变量名到实际名称的映射
   */
  private Map<String, Object> buildExtDataNameMap(ObjectDataExt salaryItemExt) {
    Map<String, Object> extDataNameMap = Maps.newHashMap();

    try {
      String tenantId = salaryItemExt.getTenantId();

      // 获取工资项中包含的KPI指标
      List<String> kpiIds = salaryItemExt.getDimensionValues(SalaryItemFields.FORMULA_INCLUDES_KPI);

      if (CollectionUtils.isNotEmpty(kpiIds)) {

        // 查询KPI对象
        List<IObjectData> kpiObjs = salaryKPIDao.getbyKpiIds(tenantId, kpiIds);

        if (CollectionUtils.isNotEmpty(kpiObjs)) {
          for (IObjectData kpiObj : kpiObjs) {
            // 参考SalaryServiceImpl中的逻辑，构造KPI变量名映射
            String key = KPI_EXT_START + kpiObj.getId(); // 对应SalaryExpressionCalcServiceImpl.KPI_EXT_START
            extDataNameMap.put(key, kpiObj.getName());
            log.debug("添加KPI变量映射: {} -> {}", key, kpiObj.getName());
          }
        }
      }

      log.debug("构建extDataNameMap完成，映射数量: {}", extDataNameMap.size());

    } catch (Exception e) {
      log.warn("构建extDataNameMap失败", e);
    }

    return extDataNameMap;
  }

  /**
   * 格式化公式，将变量名替换为实际名称
   * 参考SalaryServiceImpl.formatFormula的实现
   *
   * @param formulaStr 原始公式字符串
   * @param extDataNameMap 变量名到实际名称的映射
   * @return 格式化后的公式字符串
   */
  private String formatFormula(String formulaStr, Map<String, Object> extDataNameMap) {
    if (formulaStr == null || extDataNameMap == null) {
      return formulaStr;
    }

    for (Map.Entry<String, Object> entry : extDataNameMap.entrySet()) {
      // 使用正则表达式中的特殊字符需要转义
      String key = Pattern.quote("$" + entry.getKey() + "$");
      String value = entry.getValue() != null ? entry.getValue().toString() : "";
      if (StringUtils.isNotBlank(value)){
        formulaStr = formulaStr.replaceAll(key, value);
      }
    }
    return formulaStr;
  }
}
