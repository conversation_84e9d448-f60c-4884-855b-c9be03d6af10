package com.facishare.crm.fmcg.wq.service.impl;

import com.facishare.crm.fmcg.wq.dao.SalaryKPIDao;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.service.SalaryExpressionCalcService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.ExpressionCheck;
import com.facishare.paas.appframework.metadata.GlobalVarService;
import com.facishare.paas.appframework.metadata.expression.*;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.expression.exception.ExpressionCompileException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe;
import com.fxiaoke.common.Pair;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-04-28 18:09
 **/
@Slf4j
@Service("salaryFormulaCalcService")
public class SalaryExpressionCalcServiceImpl implements SalaryExpressionCalcService {
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Autowired
    private SalaryKPIDao salaryKPIDao;

    @Autowired
    private GlobalVarService globalVarService;
    @Autowired
    private ExpressionService expressionService;

    public static final String KPI_EXT_START = "EXT#KPI#";
    private static final String KPI_EXT_TYPE = "KPI";
    @Override
    public List<String> extractKpiIdsFromExpression(String expression) {
        if (StringUtils.isBlank(expression)) {
            return Collections.emptyList();
        }

        List<String> kpiIds = new ArrayList<>();

        // 匹配$EXT#KPI#xxxxx$格式的变量
        Pattern pattern = Pattern.compile("\\$" + KPI_EXT_START + "([^$]+)\\$");
        Matcher matcher = pattern.matcher(expression);

        while (matcher.find()) {
            String kpiId = matcher.group(1);
            kpiIds.add(kpiId);
        }
        //去重
        kpiIds = kpiIds.stream().distinct().collect(Collectors.toList());
        return kpiIds;
    }
    @Override
    public ExpressionCheck.Result check(ExpressionDTO expressionDTO, ServiceContext context) {

        //根据extlist 和 EXT_START过滤出ids
        if (CollectionUtils.isNotEmpty(expressionDTO.getExtFields())) {
            List<String> kpiIds = extractKpiIds(expressionDTO);
            validateKpiExistence(context.getTenantId(), kpiIds);
        }
        String errMsg;
        try {
            expressionCalculateLogicService.compileCheck(context.getTenantId(), expressionDTO);
            return new ExpressionCheck.Result();
        } catch (StackOverflowError e) {
            log.warn("expression stack overflow,context:{},arg:{} ", context, expressionDTO, e);
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.EXPRESSION_STACK_OVERFLOW, "计算公式中嵌套条件太多，请减少条件判断数重试"));// ignoreI18n
        } catch (ObjectDefNotFoundError e) {
            throw new ValidateException(I18N.text(I18NKey.VERIFY_OBJECT_EXIST_AND_SAVE_SECCESS_END_CASE_ADD_CALC_FORMULA));
        } catch (ExpressionCompileException e) {
            log.warn("expression compile error,context:{},arg:{} ", context, expressionDTO, e);
            String syntaxError = I18N.text(I18NKey.FORMULA_SYNTAX_ERROR);
//            if (BooleanUtils.isTrue(arg.getErrorReminder())
//                    && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EXPRESSION_CHECK_DETAIL_REMIND_GRAY, context.getTenantId())) {
                errMsg = e.getMessage();
                if (StringUtils.isNotEmpty(errMsg)) {
                    errMsg = errMsg.replace("\n", "<br>").replace(" ", "&nbsp;");
                }
                return ExpressionCheck.Result.builder()
                        .code(400)
                        .value(errMsg)
                        .build();
//            } else {
//            }
//            throw new MetaDataBusinessException(syntaxError);
        }
    }

    @Override
//    @Cacheable(value = "expressionCache", key = "#expressionDTO.expression + '_' + #context.owner + '_' + #context.startTime + '_' + #context.endTime + '_' + #context.tenantId")
    public Pair<String,Object> calculateWithExpression(ExpressionDTO expressionDTO, SalaryContext context) {

        // 验证KPI值
        validateKpiValues(expressionDTO);

        // 创建表达式
        Expression expression = createExpression(expressionDTO);

        // 绑定变量
        bindExpressionVariables(expression, expressionDTO, context);

        // 格式化表达式
        String formatted = formatExpression(expression, expressionDTO.getExpression(),context);

        // 计算结果
        Object result = expression.doCalculate(expressionService);
        return Pair.build(formatted, result);
    }

    private void validateKpiValues(ExpressionDTO expressionDTO) {
        if (CollectionUtils.isNotEmpty(expressionDTO.getExtFields())) {
            boolean hasNullValue = expressionDTO.getExtFields().stream()
                    .filter(o -> o.getFieldName().startsWith(KPI_EXT_START))
                    .anyMatch(o -> o.getValue() == null);

            if (hasNullValue) {
                throw new ValidateException("公式中存在没有值的指标");//ignoreI18n
            }
        }
    }

    private Expression createExpression(ExpressionDTO expressionDTO) {
        Expression expression = Expression.builder()
                .expression(expressionDTO.getExpression())
                .returnType(expressionDTO.getReturnType())
                .fieldName(expressionDTO.getCalculateFieldApiName())
                .expressionLabel(expressionDTO.getExpressionLabel())
                .decimalPlaces(expressionDTO.getDecimalPlaces())
                .build();

        expression.init();
        expression.getExtVariablesByType("KPI").forEach(o -> o.setDataType(ExpressionDataType.Decimal));
        return expression;
    }

    private void bindExpressionVariables(Expression expression, ExpressionDTO expressionDTO, SalaryContext context) {
        // 转成map name 和 value
        Map<String, Object> extVariablesMap = expressionDTO.getExtFields().stream()
                .collect(Collectors.toMap(k -> k.getFieldName(), v -> v.getValue()));

        // 获取全局变量
        Map<String, Object> globalVariableData = getGlobalVariableData(expression, context.getTenantId());

        expression.bindingVariableData(null, globalVariableData, null, extVariablesMap);
    }

    private String formatExpression(Expression expression, String formatted, SalaryContext context) {

        // 替换扩展变量
        for (ExpressionVariableFactory.ExtVariable variable : expression.getExtVariables()) {
            formatted = formatted.replace(
                    "$" + variable.getName() + "$",
                    variable.getBindingValue(true, false, ExpressionDataType.Decimal, false, context.getTenantId()).toString()
            );
        }

        // 替换全局变量
        for (ExpressionVariableFactory.GlobalVariable variable : expression.getGlobalVariables()) {
            formatted = formatted.replace("$" + variable.getName() + "$", variable.getValue().toString());
        }

        // 特殊字符处理
        formatted = formatted.replace("&&", "$$") // 防止&& 一同被转换的
                .replace("&", "+").replace("$$", "&&");

        return formatted;
    }

    private Map<String, Object> batchGetGlobalVariable(Map<String, IGlobalVariableDescribe> globalVariables) {
        if (MapUtils.isEmpty(globalVariables)) {
            return Maps.newHashMap();
        }
        Map<String, Object> globalVariableValues = Maps.newHashMap();
        globalVariables.forEach((k, v) -> globalVariableValues.put(k, globalVarService.parseValue(v)));
        return globalVariableValues;
    }
    private Map<String, Object> getGlobalVariableData(Expression expression, String tenantId) {
        return Optional.ofNullable(expression.getGlobalVariables())
                .filter(vars -> !vars.isEmpty())
                .map(vars -> {
                    List<String> apiNames = vars.stream()
                            .map(ExpressionVariableFactory.GlobalVariable::getObjectAPIName)
                            .collect(Collectors.toList());
                    Map<String, IGlobalVariableDescribe> globalVariables =
                            globalVarService.findGlobalVariables(tenantId, apiNames);
                    return batchGetGlobalVariable(globalVariables);
                })
                .orElse(Maps.newHashMap());
    }
    private List<String> extractKpiIds(ExpressionDTO expressionDTO) {
        if (CollectionUtils.isEmpty(expressionDTO.getExtFields())) {
            return Collections.emptyList();
        }
        return expressionDTO.getExtFields().stream()
                .filter(o -> o.getFieldName().startsWith(KPI_EXT_START))
                .map(o -> o.getFieldName().replace(KPI_EXT_START, ""))
                .collect(Collectors.toList());
    }

    private void validateKpiExistence(String tenantId, List<String> kpiIds) {
        if (CollectionUtils.isEmpty(kpiIds)) {
            return;
        }
        List<IObjectData> iObjectDatas = salaryKPIDao.getbyKpiIds(tenantId, kpiIds);
        if (iObjectDatas.size() != kpiIds.size()) {
            throw new ValidateException("公式中存在不存在的指标");//ignoreI18n
        }
    }


}
