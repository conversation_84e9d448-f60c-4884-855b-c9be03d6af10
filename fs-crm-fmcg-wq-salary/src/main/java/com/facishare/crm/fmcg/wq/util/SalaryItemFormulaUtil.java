package com.facishare.crm.fmcg.wq.util;

import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryKPIDao;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static com.facishare.crm.fmcg.wq.service.impl.SalaryExpressionCalcServiceImpl.KPI_EXT_START;

/**
 * 薪资项公式工具类
 * 提供公式描述构建的公共方法
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class SalaryItemFormulaUtil {

  /**
   * 构建工资项公式描述
   * 只有在取值方式为"计算公式"时才构建公式描述
   * 
   * @param tenantId 租户ID
   * @param salaryItemExt 工资项扩展对象
   * @param salaryKPIDao KPI DAO
   * @return 构建的公式描述字符串，如果无法构建则返回null
   */
  public static String buildFormulaDescription(String tenantId, ObjectDataExt salaryItemExt, SalaryKPIDao salaryKPIDao) {
    try {
      // 只有在取值方式为"计算公式"时才构建公式描述
      String valueType = salaryItemExt.get(SalaryItemFields.VALUE_TYPE, String.class);
      if (!SalaryItemFields.VALUE_TYPE_Options_2.equals(valueType)) {
        log.debug("工资项取值方式不是计算公式，跳过公式描述构建");
        return null;
      }

      // 获取计算公式
      String calculationFormula = salaryItemExt.get(SalaryItemFields.CALCULATION_FORMULA, String.class);

      if (StringUtils.isBlank(calculationFormula)) {
        log.debug("工资项计算公式为空，无法生成公式描述");
        return null;
      }

      // 构造extDataNameMap，用于变量名到实际名称的映射
      Map<String, Object> extDataNameMap = buildExtDataNameMap(tenantId, salaryItemExt, salaryKPIDao);

      // 使用SalaryServiceImpl的formatFormula逻辑进行变量替换
      String formattedFormula = formatFormula(calculationFormula, extDataNameMap);

      log.debug("构建的工资项公式描述: {}", formattedFormula);
      return formattedFormula;

    } catch (Exception e) {
      log.error("构建工资项公式描述时发生异常", e);
      return null;
    }
  }

  /**
   * 构建扩展数据名称映射
   * 参考SalaryServiceImpl中的逻辑，主要处理KPI相关的变量名映射
   *
   * @param tenantId 租户ID
   * @param salaryItemExt 工资项扩展对象
   * @param salaryKPIDao KPI DAO
   * @return 变量名到实际名称的映射
   */
  private static Map<String, Object> buildExtDataNameMap(String tenantId, ObjectDataExt salaryItemExt, SalaryKPIDao salaryKPIDao) {
    Map<String, Object> extDataNameMap = Maps.newHashMap();

    try {
      // 获取工资项中包含的KPI指标
      List<String> kpiIds = salaryItemExt.getDimensionValues(SalaryItemFields.FORMULA_INCLUDES_KPI);

      if (CollectionUtils.isNotEmpty(kpiIds)) {

        // 查询KPI对象
        List<IObjectData> kpiObjs = salaryKPIDao.getbyKpiIds(tenantId, kpiIds);

        if (CollectionUtils.isNotEmpty(kpiObjs)) {
          for (IObjectData kpiObj : kpiObjs) {
            // 参考SalaryServiceImpl中的逻辑，构造KPI变量名映射
            String key = KPI_EXT_START + kpiObj.getId(); // 对应SalaryExpressionCalcServiceImpl.KPI_EXT_START
            extDataNameMap.put(key, kpiObj.getName());
            log.debug("添加KPI变量映射: {} -> {}", key, kpiObj.getName());
          }
        }
      }

      log.debug("构建extDataNameMap完成，映射数量: {}", extDataNameMap.size());

    } catch (Exception e) {
      log.warn("构建extDataNameMap失败", e);
    }

    return extDataNameMap;
  }

  /**
   * 格式化公式，将变量名替换为实际名称
   * 参考SalaryServiceImpl.formatFormula的实现
   *
   * @param formulaStr 原始公式字符串
   * @param extDataNameMap 变量名到实际名称的映射
   * @return 格式化后的公式字符串
   */
  private static String formatFormula(String formulaStr, Map<String, Object> extDataNameMap) {
    if (formulaStr == null || extDataNameMap == null) {
      return formulaStr;
    }

    for (Map.Entry<String, Object> entry : extDataNameMap.entrySet()) {
      // 使用正则表达式中的特殊字符需要转义
      String key = Pattern.quote("$" + entry.getKey() + "$");
      String value = entry.getValue() != null ? entry.getValue().toString() : "";
      if (StringUtils.isNotBlank(value)){
        formulaStr = formulaStr.replaceAll(key, value);
      }
    }
    return formulaStr;
  }

  /**
   * 格式化计算公式
   * 参考SalaryServiceImpl.formatFormula的逻辑，将公式转换为中文描述
   *
   * @param calculationFormula 原始计算公式
   * @return 格式化后的公式描述
   */
  public static String formatCalculationFormula(String calculationFormula) {
    if (StringUtils.isBlank(calculationFormula)) {
      return null;
    }

    try {
      String formattedFormula = calculationFormula.trim();

      // 参考SalaryExpressionCalcServiceImpl.formatExpression的逻辑
      // 替换特殊字符处理
      formattedFormula = formattedFormula.replace("&&", "$$") // 防止&& 一同被转换的
              .replace("&", "+").replace("$$", "&&");

      // 替换常见的操作符为中文描述
      formattedFormula = formattedFormula.replaceAll("\\+", " 加 "); //ignoreI18n
      formattedFormula = formattedFormula.replaceAll("-", " 减 "); //ignoreI18n
      formattedFormula = formattedFormula.replaceAll("\\*", " 乘以 "); //ignoreI18n
      formattedFormula = formattedFormula.replaceAll("/", " 除以 "); //ignoreI18n
      formattedFormula = formattedFormula.replaceAll("\\(", "（");
      formattedFormula = formattedFormula.replaceAll("\\)", "）");

      // 清理多余的空格
      formattedFormula = formattedFormula.replaceAll("\\s+", " ").trim();

      // 如果公式太长，进行截断
      if (formattedFormula.length() > 200) {
        formattedFormula = formattedFormula.substring(0, 197) + "...";
      }

      return formattedFormula;

    } catch (Exception e) {
      log.warn("格式化计算公式失败: {}", calculationFormula, e);
      return calculationFormula; // 返回原始公式
    }
  }
}
