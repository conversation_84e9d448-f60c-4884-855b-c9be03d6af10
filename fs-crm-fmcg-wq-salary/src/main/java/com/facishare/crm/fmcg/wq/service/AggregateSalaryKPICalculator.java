package com.facishare.crm.fmcg.wq.service;

import com.alibaba.fastjson.JSON;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SystemConstants;
import com.facishare.crm.fmcg.wq.model.AggregateFunction;
import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.exception.AbandonActionException;
import com.facishare.crm.fmcg.wq.model.kpi.AggregateSalaryKPI;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.validator.ValidatorException;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 聚合指标计算器
 */
@SuppressWarnings("Duplicates")
@Component("aggregateSalaryKPICalculator")
@Slf4j
public class AggregateSalaryKPICalculator extends SalaryKPICalculator<AggregateSalaryKPI> {

    @Resource
    private ServiceFacade serviceFacade;

    /**
     * 校验指标信息
     *
     * @param metric 指标信息
     */
    @Override
    public void validate(AggregateSalaryKPI metric) {
        super.validate(metric);

        List<String> fieldApiNames = Lists.newArrayList(metric.getAggregateUserFieldApiName(), metric.getAggregateTimeFieldApiName());
        if (!AggregateFunction.COUNT.value().equals(metric.getAggregateFunction().value())) {
            fieldApiNames.add(metric.getAggregateFieldApiName());
        }
        try {
            List<Wheres> wheres = JSON.parseArray(metric.getWheres(), Wheres.class);
            if (CollectionUtils.isNotEmpty(wheres)) {
                for (Wheres where : wheres) {
                    for (IFilter filter : where.getFilters()) {
                        fieldApiNames.add(filter.getFieldName());
                    }
                }
            }
        } catch (Exception ex) {
            throw new AbandonActionException("aggregate metric where filter cast error");
        }

        // PAAS 的查询方法在字段不存在或者未激活时会忽略该字段条件，在激励这个场景下，不应忽略，抛弃不计算为最佳
        IObjectDescribe describe;
        try {
            describe = serviceFacade.findObject(metric.getTenantId(), metric.getObjectApiName());
        } catch (ObjectDefNotFoundError ex) {
            throw new AbandonActionException("aggregate metric object not found");
        }
        Map<String, IFieldDescribe> fields = describe.getFieldDescribeMap();

        for (String fieldApiName : fieldApiNames) {
            if (!fields.containsKey(fieldApiName)) {
                throw new AbandonActionException("aggregate metric field api name not found : " + fieldApiName);
            }

            IFieldDescribe fieldDescribe = fields.get(fieldApiName);
            if (Boolean.FALSE.equals(fieldDescribe.isActive())) {
                throw new AbandonActionException("aggregate metric field api name not active : " + fieldApiName);
            }
        }
    }

    /**
     * 进行聚合查询，计算聚合值
     *
     * @param context 激励上下文
     * @param metric  指标
     * @return 聚合查询结果
     */
    @Override
    public MetricCalculateResult doCalculate(SalaryContext context, AggregateSalaryKPI metric) {
        SearchTemplateQuery query = initAggregateQuery(context, metric);
        context.watch().lap("AggregateMetricCalculator#InitAggregateQuery." + metric.getId());

        if (metric.getAggregateFunction().equals(AggregateFunction.COUNT)) {
            metric.setAggregateFieldApiName("_id");
        }

        List<IObjectData> data = serviceFacade.aggregateFindBySearchQueryWithGroupFields(
                User.systemUser(context.getTenantId()),
                query,
                metric.getObjectApiName(),
                Lists.newArrayList(metric.getAggregateUserFieldApiName()),
                metric.getAggregateFunction().value(),
                metric.getAggregateFieldApiName()
        );
        context.watch().lap("AggregateMetricCalculator#AggregateFindBySearchQueryWithGroupFields." + metric.getId());
		log.info("doCalculate tenantId {},apiName {} query {} metric {},,result {}",context.getTenantId(),metric.getObjectApiName(),query.toJsonString(),JSON.toJSON(metric),JSON.toJSON(data));
        if (CollectionUtils.isEmpty(data)) {
            return new MetricCalculateResult("0");
        }
        IObjectData datum = data.get(0);

        // 例如：sum_order_amount
        String key = String.format("%s_%s", metric.getAggregateFunction().value(), metric.getAggregateFieldApiName());
        Object value = datum.get(key);

        if (Objects.isNull(value)) {
            return new MetricCalculateResult(null);
        } else {
            return new MetricCalculateResult(value.toString());
        }
    }

    /**
     * 初始化聚合查询 query
     *
     * @param context 激励上下文
     * @param metric  指标
     * @return 聚合查询 query
     */
    private static @NotNull SearchTemplateQuery initAggregateQuery(SalaryContext context, AggregateSalaryKPI metric) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setSearchSource("db");

        // 只查询当前员工的
        IFilter userFilter = new Filter();
        userFilter.setFieldName(metric.getAggregateUserFieldApiName());
        userFilter.setOperator(Operator.EQ);
        userFilter.setFieldValues(Lists.newArrayList(context.getOwner()));

        // 时间范围，大于等于开始时间
        IFilter beginTimeFilter = new Filter();
        beginTimeFilter.setFieldName(metric.getAggregateTimeFieldApiName());
        beginTimeFilter.setOperator(Operator.GTE);
        beginTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(context.getStartTime())));

        // 时间范围，小于等于结束时间
        IFilter endTimeFilter = new Filter();
        endTimeFilter.setFieldName(metric.getAggregateTimeFieldApiName());
        endTimeFilter.setOperator(Operator.LTE);
        endTimeFilter.setFieldValues(Lists.newArrayList(String.valueOf(context.getEndTime() + DateUtils.ONE_DAY - 1)));

        IFilter lifeStatusFilter = new Filter();
        lifeStatusFilter.setFieldName(SystemConstants.Field.LifeStatus.getApiName());
        lifeStatusFilter.setOperator(Operator.EQ);
        lifeStatusFilter.setFieldValues(Lists.newArrayList(SystemConstants.LifeStatus.Normal.getValue()));

        IFilter isDeletedFilter = new Filter();
        isDeletedFilter.setFieldName(SystemConstants.Field.isDeleted.getApiName());
        isDeletedFilter.setOperator(Operator.EQ);
        isDeletedFilter.setFieldValues(Lists.newArrayList("false"));

        query.setFilters(Lists.newArrayList(userFilter, beginTimeFilter, endTimeFilter, lifeStatusFilter, isDeletedFilter));

        // 拼装额外范围条件
        if (!Strings.isNullOrEmpty(metric.getWheres())) {
            List<Wheres> wheres = JSON.parseArray(metric.getWheres(), Wheres.class);
            query.setWheres(wheres);
        }
        return query;
    }
}
