package com.facishare.crm.fmcg.wq.model;

import com.beust.jcommander.internal.Lists;

import java.io.Serializable;
import java.util.List;

public enum ValueType implements Serializable {

    STRING("string", Lists.newArrayList(
            ConditionOperator.LIKE,
            ConditionOperator.NLIKE,
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.IS,
            ConditionOperator.ISN,
            ConditionOperator.STARTWITH,
            ConditionOperator.ENDWITH
    )),

    NUMBER("number", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.GT,
            ConditionOperator.GTE,
            ConditionOperator.LT,
            ConditionOperator.LTE,
            ConditionOperator.BETWEEN,
            ConditionOperator.IS,
            ConditionOperator.ISN
    )),

    DATE("date", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.LT,
            ConditionOperator.LTE,
            ConditionOperator.GT,
            ConditionOperator.GTE,
            ConditionOperator.BETWEEN,
            ConditionOperator.IS,
            ConditionOperator.ISN
    )),

    TRUE_OR_FALSE("true_or_false", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.IS,
            ConditionOperator.ISN
    )),

    TEXT("text", Lists.newArrayList(
            ConditionOperator.LIKE,
            ConditionOperator.NLIKE,
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.IS,
            ConditionOperator.ISN,
            ConditionOperator.STARTWITH,
            ConditionOperator.ENDWITH
    )),

    LONG_TEXT("long_text", Lists.newArrayList(
            ConditionOperator.LIKE,
            ConditionOperator.NLIKE,
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.IS,
            ConditionOperator.ISN,
            ConditionOperator.STARTWITH,
            ConditionOperator.ENDWITH
    )),

    SELECT_ONE("select_one", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.ISN,
            ConditionOperator.IS
    )),

    SELECT_MANY("select_many", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.ISN,
            ConditionOperator.IS
    )),

    PERCENTILE("percentile", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.GT,
            ConditionOperator.GTE,
            ConditionOperator.LT,
            ConditionOperator.LTE,
            ConditionOperator.BETWEEN,
            ConditionOperator.IS,
            ConditionOperator.ISN
    )),

    CURRENCY("currency", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.GT,
            ConditionOperator.GTE,
            ConditionOperator.LT,
            ConditionOperator.LTE,
            ConditionOperator.BETWEEN,
            ConditionOperator.IS,
            ConditionOperator.ISN
    )),

    QUOTE("quote", Lists.newArrayList()),

    RECORD_TYPE("record_type", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N
    )),

    PHONE_NUMBER("phone_number", Lists.newArrayList(
            ConditionOperator.LIKE,
            ConditionOperator.NLIKE,
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.IS,
            ConditionOperator.ISN,
            ConditionOperator.STARTWITH,
            ConditionOperator.ENDWITH
    )),

    EMAIL("email", Lists.newArrayList(
            ConditionOperator.LIKE,
            ConditionOperator.NLIKE,
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.IS,
            ConditionOperator.ISN,
            ConditionOperator.STARTWITH,
            ConditionOperator.ENDWITH
    )),

    EMPLOYEE("employee", Lists.newArrayList(
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.ISN,
            ConditionOperator.IS
    )),

    EMPLOYEE_MANY("employee_many", Lists.newArrayList(
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.ISN,
            ConditionOperator.IS
    )),

    OUT_EMPLOYEE("out_employee", Lists.newArrayList(
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.ISN,
            ConditionOperator.IS
    )),

    DEPARTMENT("department", Lists.newArrayList(
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.ISN,
            ConditionOperator.IS
    )),

    DEPARTMENT_MANY("department_many", Lists.newArrayList(
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.ISN,
            ConditionOperator.IS
    )),

    DIMENSION("dimension", Lists.newArrayList()),

    FORMULA("formula", Lists.newArrayList()),

    TIME("time", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.LT,
            ConditionOperator.LTE,
            ConditionOperator.GT,
            ConditionOperator.GTE,
            ConditionOperator.BETWEEN,
            ConditionOperator.IS,
            ConditionOperator.ISN
    )),

    DATE_TIME("date_time", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.LT,
            ConditionOperator.LTE,
            ConditionOperator.GT,
            ConditionOperator.GTE,
            ConditionOperator.BETWEEN,
            ConditionOperator.IS,
            ConditionOperator.ISN
    )),

    COUNT("count", Lists.newArrayList()),

    OBJECT_REFERENCE("object_reference", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N)),

    OBJECT_REFERENCE_MANY("object_reference_many", Lists.newArrayList(
            ConditionOperator.EQ,
            ConditionOperator.N,
            ConditionOperator.IN,
            ConditionOperator.NIN,
            ConditionOperator.ISN,
            ConditionOperator.IS
    )),

    COUNTRY("country", Lists.newArrayList()),

    PROVINCE("province", Lists.newArrayList()),

    CITY("city", Lists.newArrayList()),

    DISTRICT("district", Lists.newArrayList()),

    AGGREGATE("aggregate", Lists.newArrayList()),

    LOCATION("location", Lists.newArrayList()),

    URL("url", Lists.newArrayList()),

    FUNC("func", Lists.newArrayList()),

    RICH_TEXT("rich_text", Lists.newArrayList()),

    HTML_RICH_TEXT("html_rich_text", Lists.newArrayList());

    private final String value;

    private final List<ConditionOperator> operators;

    ValueType(String value, List<ConditionOperator> operators) {
        this.value = value;
        this.operators = operators;
    }


    public String value() {
        return this.value;
    }

    public List<ConditionOperator> operators() {
        return this.operators;
    }
}