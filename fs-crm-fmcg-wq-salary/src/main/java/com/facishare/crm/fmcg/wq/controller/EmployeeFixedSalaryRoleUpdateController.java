package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.constants.PublicEmployeeFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDao;
import com.facishare.crm.fmcg.wq.util.RoleNameConverter;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 员工固定工资角色更新控制器
 * 提供通过 userId 更新员工固定工资表中角色字段的功能
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class EmployeeFixedSalaryRoleUpdateController extends PreDefineController<EmployeeFixedSalaryRoleUpdateController.Arg, EmployeeFixedSalaryRoleUpdateController.Result> {

  private final EmployeeDao employeeDao = SpringUtil.getContext().getBean(EmployeeDao.class);
  private final EmployeeFixedSalaryDao employeeFixedSalaryDao = SpringUtil.getContext().getBean(EmployeeFixedSalaryDao.class);
  private final RoleClient roleClient = SpringUtil.getContext().getBean(RoleClient.class);

  @Override
  protected List<String> getFuncPrivilegeCodes() {
    return Lists.newArrayList("EmployeeFixedSalaryRoleUpdate");
  }

  @Override
  protected Result doService(Arg arg) {
    String tenantId = controllerContext.getTenantId();
    List<String> userIds = arg.getUserIds();

    try {
      log.info("开始批量更新员工角色，租户ID: {}, 用户ID数量: {}", tenantId, userIds != null ? userIds.size() : 0);

      // 参数校验
      if (CollectionUtils.isEmpty(userIds)) {
        return Result.error("用户ID列表不能为空"); //ignoreI18n
      }

      // 过滤空白的用户ID
      List<String> validUserIds = userIds.stream()
          .filter(StringUtils::isNotBlank)
          .collect(java.util.stream.Collectors.toList());

      if (validUserIds.isEmpty()) {
        return Result.error("没有有效的用户ID"); //ignoreI18n
      }

      log.info("有效用户ID数量: {}", validUserIds.size());

      // 查询员工固定工资表记录
      List<IObjectData> fixedSalaryRecords = queryEmployeeFixedSalaryRecords(tenantId, validUserIds);

      if (CollectionUtils.isEmpty(fixedSalaryRecords)) {
        log.info("未找到用户 {} 的固定工资记录", validUserIds);
        return Result.success("未找到固定工资记录", 0, 0); //ignoreI18n
      }

      log.info("找到 {} 条固定工资记录", fixedSalaryRecords.size());

      // 更新角色字段
      UpdateResult updateResult = updateRoleFields(tenantId, fixedSalaryRecords);

      log.info("批量角色更新完成，成功: {}, 失败: {}", updateResult.successCount, updateResult.failureCount);

      return Result.success("角色更新完成", updateResult.successCount, updateResult.failureCount); //ignoreI18n

    } catch (Exception e) {
      log.error("批量更新员工角色时发生异常，租户ID: {}, 用户ID: {}", tenantId, userIds, e);
      return Result.error("更新失败: " + e.getMessage()); //ignoreI18n
    }
  }

  /**
   * 查询员工固定工资表记录
   * 使用现有的 getByEmployeeIds 方法，支持内部员工和外部员工
   *
   * @param tenantId 租户ID
   * @param userIds 用户ID列表
   * @return 固定工资记录列表
   */
  private List<IObjectData> queryEmployeeFixedSalaryRecords(String tenantId, List<String> userIds) {
    try {
      // 直接使用现有的 getByEmployeeIds 方法，它会自动处理内部员工和外部员工
      List<IObjectData> records = employeeFixedSalaryDao.getByEmployeeIds(tenantId, userIds);

      log.debug("查询到用户 {} 的固定工资记录 {} 条", userIds, records.size());
      return records;

    } catch (Exception e) {
      log.error("查询员工固定工资记录时发生异常，租户ID: {}, 用户ID: {}", tenantId, userIds, e);
      throw new RuntimeException("查询固定工资记录失败", e); //ignoreI18n
    }
  }

  /**
   * 更新角色字段
   *
   * @param tenantId 租户ID
   * @param fixedSalaryRecords 固定工资记录列表
   * @return 更新结果
   */
  private UpdateResult updateRoleFields(String tenantId, List<IObjectData> fixedSalaryRecords) {
    UpdateResult result = new UpdateResult();

    for (IObjectData record : fixedSalaryRecords) {
      try {
        // 获取最新角色信息
        List<String> latestRoleNames = getLatestEmployeeRoles(tenantId, record);

        if (latestRoleNames != null) {
          // 更新角色字段
          record.set(EmployeeFixedSalaryFields.CONNECTED_ROLE, latestRoleNames);
          
          // 保存到数据库
          employeeFixedSalaryDao.update(User.systemUser(tenantId), record);
          
          result.successCount++;
          log.debug("成功更新固定工资记录 {} 的角色字段: {}", record.getId(), latestRoleNames);
        } else {
          result.failureCount++;
          log.warn("无法获取固定工资记录 {} 的角色信息", record.getId());
        }

      } catch (Exception e) {
        result.failureCount++;
        log.error("更新固定工资记录 {} 的角色字段时发生异常", record.getId(), e);
      }
    }

    return result;
  }

  /**
   * 获取员工的最新角色信息
   * 
   * @param tenantId 租户ID
   * @param fixedSalaryRecord 固定工资记录
   * @return 角色名称列表
   */
  private List<String> getLatestEmployeeRoles(String tenantId, IObjectData fixedSalaryRecord) {
    try {
      // 判断是内部员工还是外部员工
      String employeeId = fixedSalaryRecord.get(EmployeeFixedSalaryFields.EMPLOYEE, String.class);
      String employeeExternalId = fixedSalaryRecord.get(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, String.class);

      List<String> roleIds = null;

      if (StringUtils.isNotBlank(employeeExternalId)) {
        // 外部员工
        IObjectData employeeObj = employeeDao.getExternalEmployeeById(tenantId, employeeExternalId);
        if (employeeObj != null && employeeObj.get(PublicEmployeeFields.OUTER_TENANT_ID) != null) {
          roleIds = employeeDao.getEmployeeRoles(tenantId, employeeExternalId,
              employeeObj.get(PublicEmployeeFields.OUTER_TENANT_ID).toString(), true);
        }
      } else if (StringUtils.isNotBlank(employeeId)) {
        // 内部员工 - 这里可能需要调用不同的方法获取内部员工角色
        // 根据实际业务逻辑调整
        log.debug("内部员工暂不支持角色更新，员工ID: {}", employeeId);
        return Lists.newArrayList();
      }

      if (CollectionUtils.isNotEmpty(roleIds)) {
        // 将角色ID转换为角色名称
        return RoleNameConverter.convertRoleIdsToNames(roleClient, tenantId, roleIds);
      }

    } catch (Exception e) {
      log.error("获取员工角色信息时发生异常，固定工资记录ID: {}", fixedSalaryRecord.getId(), e);
    }

    return Lists.newArrayList();
  }

  /**
   * 更新结果内部类
   */
  private static class UpdateResult {
    int successCount = 0;
    int failureCount = 0;
  }

  /**
   * 请求参数
   */
  @Data
  public static class Arg implements Serializable {
    /**
     * 用户ID列表（可以是内部员工ID或外部员工ID）
     */
    private List<String> userIds;
  }

  /**
   * 响应结果
   */
  @Data
  public static class Result implements Serializable {
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 成功更新的记录数
     */
    private int successCount;
    
    /**
     * 失败的记录数
     */
    private int failureCount;

    /**
     * 创建成功结果
     */
    public static Result success(String message, int successCount, int failureCount) {
      Result result = new Result();
      result.success = true;
      result.message = message;
      result.successCount = successCount;
      result.failureCount = failureCount;
      return result;
    }

    /**
     * 创建错误结果
     */
    public static Result error(String message) {
      Result result = new Result();
      result.success = false;
      result.message = message;
      result.successCount = 0;
      result.failureCount = 0;
      return result;
    }
  }
}
