package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.SalaryDataFields;
import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.dao.SalaryDataDao;
import com.facishare.crm.fmcg.wq.dao.SalaryPaymentSlipDao;
import com.facishare.crm.fmcg.wq.service.ConnectedRoleFieldService;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.util.DescribeUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工资数据列表头部控制器
 *
 * 功能：
 * 1. 隐藏互联角色字段，避免在列表页显示原始角色ID
 * 2. 处理工资条列表页面的按钮和虚拟字段
 *
 * <AUTHOR>
 * @create 2023-09-23 17:06
 */
public class SalaryDataListHeaderController extends StandardListHeaderController {


    SalaryDataDao salaryDataDao = SpringUtil.getContext().getBean(SalaryDataDao.class);
    SalaryPaymentSlipDao salaryPaymentSlipDao = SpringUtil.getContext().getBean(SalaryPaymentSlipDao.class);
    SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        // 过滤掉工资条列表页面的批量作废按钮
        if (result.getButtons() != null) {
            result.setButtons(result.getButtons().stream()
                .filter(button -> !"Abolish_button_default".equals(button.get("api_name")))
                .filter(button -> !"AsyncBulkInvalid".equals(button.get("action")))
                .collect(Collectors.toList()));
        }

        if (arg.getTargetObjectApiName() != null && SalaryPaymentSlipFields.API_NAME.equals(arg.getTargetObjectApiName())) {
           //来源工资发放单的工资条列表隐藏按钮
            result.setButtons(Lists.newArrayList());
            String salaryPaymentSlipId = arg.getTargetObjectDataId();
            IObjectData salaryPaymentSlipDaoById = salaryPaymentSlipDao.getById(controllerContext.getTenantId(), salaryPaymentSlipId);
            List<String> salaryTableHeaders = salaryService.getSalaryTableHeaders(salaryPaymentSlipDaoById);
            //描述 增加虚拟字段
            ObjectDescribeDocument objectDescribe = result.getObjectDescribe();
            Map<String, IFieldDescribe> stringObjectFieldDescribeDocumentMap =
                    salaryService.processSalaryTableHeaderDescriptions(controllerContext.getTenantId(), salaryTableHeaders);
            ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
            for (Map.Entry<String, IFieldDescribe> stringObjectFieldDescribeDocumentEntry : stringObjectFieldDescribeDocumentMap.entrySet()) {
                //ext 包含的话跳过
                if (objectDescribeExt.getFieldDescribe(stringObjectFieldDescribeDocumentEntry.getKey()) != null) {
                    continue;
                }
                objectDescribeExt.addFieldDescribe(stringObjectFieldDescribeDocumentEntry.getValue());
            }
            result.setObjectDescribe(ObjectDescribeDocument.of(objectDescribeExt));
            //templates 场景处理逻辑 样式变动
            result.getTemplates().forEach(o->{
                List<Map<String, Object>> fieldList= (List<Map<String, Object>>)o.get("field_list");
                o.put("field_list",salaryService.convertSalaryTableHeaderOrder(controllerContext.getTenantId(), salaryTableHeaders, fieldList));
            });
        }
        result.setObjectDescribe(DescribeUtils.addConnectedRoleVirtualFieldToObjectDescribe(result.getObjectDescribe(), SalaryDataFields.CONNECTED_ROLE_DISPLAY));
        return result;
    }


}
