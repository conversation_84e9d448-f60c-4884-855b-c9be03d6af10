package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.dao.SalaryKPIDao;
import com.facishare.crm.fmcg.wq.service.SalaryExpressionCalcService;
import com.facishare.crm.fmcg.wq.util.FieldValidationUtil;
import com.facishare.crm.fmcg.wq.util.SalaryItemFormulaUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Slf4j
public class SalaryItemAddAction extends FmcgSkipPermissionAddAction {

    SalaryExpressionCalcService salaryExpressionCalcService = SpringUtil.getContext()
            .getBean(SalaryExpressionCalcService.class);
    SalaryKPIDao salaryKPIDao = SpringUtil.getContext().getBean(SalaryKPIDao.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        log.info("开始处理工资项新建");

        // 验证字段显示时机和必填逻辑
        validateFieldDisplayAndRequired(arg.getObjectData());

        // 设置 指标
        Object calculationFormula = arg.getObjectData().get(SalaryItemFields.CALCULATION_FORMULA);
        if (calculationFormula == null) {
            arg.getObjectData().put(SalaryItemFields.FORMULA_INCLUDES_KPI, Lists.newArrayList());
        } else {
            List<String> strings = salaryExpressionCalcService
                    .extractKpiIdsFromExpression(calculationFormula.toString());
            arg.getObjectData().put(SalaryItemFields.FORMULA_INCLUDES_KPI, strings);
        }

        // 设置计算公式描述字段
//        setCalculationFormulaDescription(arg.getObjectData());

        log.info("工资项新建前处理完成");
    }

    /**
     * 设置计算公式描述字段
     * 根据配置的计算公式构建公式描述
     *
     * @param objectData 工资项数据
     */
    private void setCalculationFormulaDescription(ObjectDataDocument objectData) {
        try {
            String tenantId = actionContext.getTenantId();

            // 使用工具类构建计算公式描述
            String calculationFormulaDesc = SalaryItemFormulaUtil.buildFormulaDescription(
                tenantId, ObjectDataExt.of(objectData), salaryKPIDao);

            if (StringUtils.isNotBlank(calculationFormulaDesc)) {
                objectData.put(SalaryItemFields.CALCULATION_FORMULA_DESC, calculationFormulaDesc);
                log.info("工资项新增时设置计算公式描述: {}", calculationFormulaDesc);
            } else {
                log.debug("工资项未配置计算公式或无法构建公式描述，跳过设置");
            }

        } catch (Exception e) {
            log.error("设置工资项计算公式描述时发生异常", e);
            // 不影响主流程，只记录错误日志
        }
    }

    /**
     * 验证字段显示时机和必填逻辑
     *
     * @param salaryItemData 工资项数据
     */
    private void validateFieldDisplayAndRequired(ObjectDataDocument salaryItemData) {
        String valueType = (String) salaryItemData.get(SalaryItemFields.VALUE_TYPE);

        // 当"取值方式=计算公式"时的字段验证
        if (SalaryItemFields.VALUE_TYPE_Options_2.equals(valueType)) {
            // 舍位方式必填
            String roundingMethod = (String) salaryItemData.get(SalaryItemFields.ROUNDING_METHOD);
            if (StringUtils.isBlank(roundingMethod)) {
                String message = FieldValidationUtil.createFieldRequiredMessage(objectDescribe,
                    SalaryItemFields.ROUNDING_METHOD, "取值方式为计算公式"); // ignoreI18n
                throw new ValidateException(message);
            }

//            if (SalaryItemFields.ROUNDING_METHOD_Options_1.equals(roundingMethod)) {
                String decimalPlaces = (String) salaryItemData.get(SalaryItemFields.DECIMAL_PLACES);
                if (StringUtils.isBlank(decimalPlaces)) {
                    String message = FieldValidationUtil.createFieldRequiredMessage(objectDescribe,
                        SalaryItemFields.DECIMAL_PLACES, "舍位方式为四舍五入"); // ignoreI18n
                    throw new ValidateException(message);
                }
//            }

            // 计算公式必填（虽然字段说明中提到是只读，但新建时需要设置）
            String calculationFormula = (String) salaryItemData.get(SalaryItemFields.CALCULATION_FORMULA);
            if (StringUtils.isBlank(calculationFormula)) {
                String valueTypeDisplayName = FieldValidationUtil.getFieldDisplayName(objectDescribe, SalaryItemFields.VALUE_TYPE);
                String calculationFormulaDisplayName = FieldValidationUtil.getFieldDisplayName(objectDescribe, SalaryItemFields.CALCULATION_FORMULA);
                throw new ValidateException(String.format("%s为计算公式时，必须设置%s", valueTypeDisplayName, calculationFormulaDisplayName)); // ignoreI18n
            }
        } else if (SalaryItemFields.VALUE_TYPE_Options_1.equals(valueType)) {
            // 取值方式为固定值时，清空计算公式相关字段
            salaryItemData.put(SalaryItemFields.CALCULATION_FORMULA, null);
            salaryItemData.put(SalaryItemFields.ROUNDING_METHOD, null);
            salaryItemData.put(SalaryItemFields.DECIMAL_PLACES, null);
        }
    }
}
