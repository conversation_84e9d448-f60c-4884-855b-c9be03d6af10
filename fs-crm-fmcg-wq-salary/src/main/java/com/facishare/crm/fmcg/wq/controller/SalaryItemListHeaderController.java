package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.util.DescribeUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;

/**
 *
 *
 * <AUTHOR>
 * @create 2023-09-23 17:06
 */
public class SalaryItemListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        result.setObjectDescribe(DescribeUtils.addSalaryItemFormulaVirtualFieldToObjectDescribe(
                result.getObjectDescribe(),
                SalaryItemFields.CALCULATION_FORMULA_DESC_DISPLAY
        ));
        return result;
    }

}
