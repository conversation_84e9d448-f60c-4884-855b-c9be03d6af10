package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.model.api.SalaryPeriodTotalQuery;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 获取日/周/月薪资数据接口（返回总薪资金额和薪资明细项目列表）
 * @author: zhangsm
 * @create: 2025-05-15 10:30
 **/
@Slf4j
public class SalaryDataTotalQueryController extends PreDefineController<SalaryPeriodTotalQuery.Arg, SalaryPeriodTotalQuery.Result> {
  
  private final SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);

  @Override
  protected List<String> getFuncPrivilegeCodes() {
    return null;
  }

  @Override
  protected SalaryPeriodTotalQuery.Result doService(SalaryPeriodTotalQuery.Arg arg) {
    if (StringUtils.isBlank(arg.getEmployeeId())) {
      arg.setEmployeeId(controllerContext.getUser().getUserId());
    }
    
    log.info("查询薪资周期详情, employeeId: {}, periodId: {}, salaryDataId: {}", 
        arg.getEmployeeId(), arg.getPeriodId(), arg.getSalaryDataId());
    
    // 创建服务上下文
    ServiceContext serviceContext = new ServiceContext(
        controllerContext.getRequestContext(),
        "fmcg-wq",
        "salaryPeriodTotalQuery"
    );
    
    // 调用服务获取薪资周期详情
    SalaryPeriodTotalQuery.Result result = salaryService.getSalaryPeriodTotal(
        arg.getEmployeeId(),
        arg.getPeriodId(),
        arg.getStartDate(),
        arg.getEndDate(),
        arg.getSalaryDataId(),
        serviceContext
    );
    
    if (result == null) {
      result = new SalaryPeriodTotalQuery.Result();
      result.setSalaryItems(new ArrayList<>());
    }
    
    log.info("查询薪资周期详情完成, employeeId: {}, periodId: {}, totalAmount: {}, 薪资项目数量: {}", 
        arg.getEmployeeId(), 
        arg.getPeriodId(),
        result.getTotalAmount(),
        result.getSalaryItems() != null ? result.getSalaryItems().size() : 0);
    
    return result;
  }
}