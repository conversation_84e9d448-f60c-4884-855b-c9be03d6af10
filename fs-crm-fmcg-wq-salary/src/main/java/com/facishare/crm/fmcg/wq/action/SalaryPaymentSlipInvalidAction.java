package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.dao.SalaryPaymentSlipDao;
import com.facishare.crm.fmcg.wq.exception.CheckinsErrorCode;
import com.facishare.crm.fmcg.wq.exception.CheckinsException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 工资发放单作废Action
 * 
 * 功能：阻止前端直接作废工资发放单
 */
@Slf4j
public class SalaryPaymentSlipInvalidAction extends StandardInvalidAction {
    SalaryPaymentSlipDao salaryPaymentSlipDao = SpringUtil.getContext().getBean(SalaryPaymentSlipDao.class);
    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //查数据 长期的 2h以上的正在生成中的数据允许删除
        IObjectData salaryPaymentSlipDaoById = salaryPaymentSlipDao.getById(actionContext.getTenantId(), arg.getObjectDataId());
        String payStatus = null;

        if (salaryPaymentSlipDaoById != null) {
            payStatus = salaryPaymentSlipDaoById.get(SalaryPaymentSlipFields.PAY_STATUS, String.class);
            if (SalaryPaymentSlipFields.PAY_STATUS_Options_0.equals(payStatus) ) {
                long createTime = salaryPaymentSlipDaoById.getCreateTime();
                long currentTime = System.currentTimeMillis();
                if (currentTime -createTime > 7200000) {
                    return;
                }
            }else if (SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR.equals(payStatus)){
                return;
            }
        }

        log.info("工资发放单作废失败，ID: {}, 状态: {}", arg.getObjectDataId(), payStatus);

        SelectOneFieldDescribe fieldDescribe = (SelectOneFieldDescribe) objectDescribe.getFieldDescribe(SalaryPaymentSlipFields.PAY_STATUS);
        throw new CheckinsException(CheckinsErrorCode.UNSUPPORTED_INVALID_CONDITION,new String[]{objectDescribe.getDisplayName(), fieldDescribe.getLabel(),fieldDescribe.getOption(payStatus).map(o->o.getLabel()).orElse("errorOption")});
    }
}
