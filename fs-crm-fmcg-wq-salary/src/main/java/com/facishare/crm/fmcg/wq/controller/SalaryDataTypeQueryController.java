package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.model.api.EmployeeSalaryTypeQuery;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 获取员工薪资类型和发放周期接口
 * @author: zhangsm
 * @create: 2025-05-10 14:30
 **/
@Slf4j
public class SalaryDataTypeQueryController
        extends PreDefineController<EmployeeSalaryTypeQuery.Arg, EmployeeSalaryTypeQuery.Result> {

    private final SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected EmployeeSalaryTypeQuery.Result doService(EmployeeSalaryTypeQuery.Arg arg) {
        if (StringUtils.isBlank(arg.getEmployeeId())) {
            arg.setEmployeeId(controllerContext.getUser().getUserId());
        }

        log.info("查询员工薪资类型和发放周期, employeeId: {}, month: {}, salaryDataId: {}",
                arg.getEmployeeId(), arg.getMonth(), arg.getSalaryDataId());

        // 创建服务上下文
        ServiceContext serviceContext = new ServiceContext(
                controllerContext.getRequestContext(),
                "fmcg-wq",
                "employeeSalaryTypeQuery");

        // 调用服务获取员工薪资类型和发放周期信息
        EmployeeSalaryTypeQuery.Result result = salaryService.getEmployeeSalaryType(
                arg.getEmployeeId(),
                arg.getMonth(),
                arg.getSalaryDataId(),
                serviceContext);

        if (result == null) {
            result = new EmployeeSalaryTypeQuery.Result();
            result.setAvailablePeriods(new ArrayList<>());
        }

        log.info("查询员工薪资类型和发放周期完成, employeeId: {}, salaryMethod: {}, distributionCycle: {}, 可用周期数量: {}",
                arg.getEmployeeId(),
                result.getSalaryMethod(),
                result.getDistributionCycle(),
                result.getAvailablePeriods() != null ? result.getAvailablePeriods().size() : 0);

        return result;
    }
}
