package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.service.SalaryExpressionCalcService;
import com.facishare.crm.fmcg.wq.service.impl.SalaryExpressionCalcServiceImpl;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.ExpressionCheck;
import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 验证计算公式正确性
 * @author: zhangsm
 * @create: 2025-04-28 18:02
 **/
public class SalaryItemExpressionCheckController extends PreDefineController<SalaryItemExpressionCheckController.Arg, SalaryItemExpressionCheckController.Result> {
    SalaryExpressionCalcService salaryExpressionCalcService = SpringUtil.getContext().getBean(SalaryExpressionCalcService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected Result doService(Arg arg) {
        //检查ext数据
        ExpressionDTO expressionDTO = JSON.parseObject(arg.getJson_data(), ExpressionDTO.class);
        Object calculationFormula = expressionDTO.getExpression();
        if (calculationFormula != null) {
            List<String> kpiIds = salaryExpressionCalcService.extractKpiIdsFromExpression(calculationFormula.toString());
            //根据指标生成extFields
            expressionDTO.setExtFields(Lists.newArrayList());
            for (String kpiId : kpiIds) {
                String key = SalaryExpressionCalcServiceImpl.KPI_EXT_START + kpiId;
                expressionDTO.getExtFields().add(ExpressionDTO.FormVariableDTO.builder().fieldName(key).type("number").build());
            }
        }
        expressionDTO.setReturnType("number");
        expressionDTO.setOnlySupportGrounded(Boolean.TRUE.equals(arg.getOnlySupportGrounded()));
        ExpressionCheck.Result check = salaryExpressionCalcService.check(expressionDTO, new ServiceContext(controllerContext.getRequestContext(),"fmcg-wq","expressionCheck"));
        Result result = new Result();
        result.setCode(check.getCode());
        result.setValue(check.getValue());
        return result;
    }

    @Data
    public static class Arg {
        private String json_data;
        private List<ExpressionDTO.FormVariableDTO> extFields;
        private Boolean onlySupportGrounded;
        private Boolean errorReminder;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Result {
        private String value;
        private int code;
    }
}
