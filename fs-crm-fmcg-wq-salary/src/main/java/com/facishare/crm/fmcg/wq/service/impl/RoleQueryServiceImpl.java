package com.facishare.crm.fmcg.wq.service.impl;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.service.RoleQueryService;
import com.facishare.organization.paas.model.PaaSResult;
import com.facishare.organization.paas.model.permission.QueryRoleUsersByRoles;
import com.facishare.organization.paas.model.permission.RoleListDto;
import com.facishare.organization.paas.service.PaaSPermissionService;
import com.facishare.organization.paas.util.PaasArgumentUtil;
import com.facishare.paas.metadata.api.describe.Array;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.SelectMany;
import com.facishare.paas.metadata.impl.describe.ArrayFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.fxiaoke.enterpriserelation2.arg.ListAllOutRoleArg;
import com.fxiaoke.enterpriserelation2.arg.ListOuterUidsByOuterRoleIdsArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;

import com.fxiaoke.enterpriserelation2.service.AppOuterRoleService;
import com.fxiaoke.enterpriserelation2.service.LinkAppService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.github.autoconf.ConfigFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色查询服务实现类
 * 提供内部业务角色和外部角色的查询功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class RoleQueryServiceImpl implements RoleQueryService {

    @Autowired
    private PaaSPermissionService paaSPermissionService;

    @Autowired
    private LinkAppService linkAppService;

    private static final String ROLE_APP_ID = "CRM";

    @Autowired
    private EIEAConverter eieaConverter;

    // 外部角色查询配置
    private static String checkConnectedAppId;

    @Autowired
    private AppOuterRoleService appOuterRoleService;


    static {
        ConfigFactory.getConfig("checkin-custom-config", config -> {
            checkConnectedAppId = config.get("checkConnectedAppId", "FSAID_11491079");
        });
    }

    @Override
    public List<Map<String, Object>> getInternalRoleOptions(String tenantId) {
        List<Map<String, Object>> options = Lists.newArrayList();

        try {
            log.info("开始查询内部业务角色，tenantId: {}", tenantId);

            if (StringUtils.isBlank(tenantId)) {
                log.warn("租户ID为空，返回空角色列表");
                return options;
            }

            // 调用真实的角色查询API
            PaaSResult<RoleListDto.Result> roleListResult = getAllRoleInfoList(Integer.valueOf(tenantId));

            if (roleListResult != null && roleListResult.getErrCode() == 0 && roleListResult.getResult() != null) {
                List<?> roles = roleListResult.getResult().getRoles();
                if (CollectionUtils.isNotEmpty(roles)) {
                    for (Object roleObj : roles) {
                        try {
                            // 使用反射获取角色信息，避免直接依赖RoleListDto.Role类型
                            String roleCode = getFieldValue(roleObj, "roleCode");
                            String roleName = getFieldValue(roleObj, "roleName");
                            Boolean delFlag = getBooleanFieldValue(roleObj, "delFlag");

                            if (StringUtils.isNotBlank(roleCode) && StringUtils.isNotBlank(roleName) &&
                                (delFlag == null || !delFlag)) {
                                Map<String, Object> option = Maps.newHashMap();
                                option.put("label", roleName);
                                option.put("value", roleCode);
                                options.add(option);
                            }
                        } catch (Exception e) {
                            log.warn("处理角色对象时发生异常: {}", e.getMessage());
                        }
                    }
                }
                log.info("查询到内部业务角色数量: {}", options.size());
            } else {
                log.warn("查询内部业务角色失败或结果为空，tenantId: {}, errCode: {}",
                        tenantId, roleListResult != null ? roleListResult.getErrCode() : "null");
            }

        } catch (Exception e) {
            log.error("查询内部业务角色时发生异常，tenantId: {}", tenantId, e);
        }

        return options;
    }

    /**
     * 查询所有角色信息列表
     *
     * @param sourceEid 企业ID
     * @return 角色列表结果
     */
    private PaaSResult<RoleListDto.Result> getAllRoleInfoList(int sourceEid) {
        RoleListDto.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(
                RoleListDto.Argument.class, sourceEid, -10000, ROLE_APP_ID);
        return paaSPermissionService.roleList(argument);
    }

    /**
     * 查询所有外部角色
     *
     * @param ea 企业账号
     * @param eid 企业ID
     * @return 外部角色列表
     */
    private List<?> listAllOutRole(String ea, int eid) {
        try {
            if (StringUtils.isBlank(checkConnectedAppId)) {
                log.warn("checkConnectedAppId配置为空，无法查询外部角色");
                return Lists.newArrayList();
            }

            ListAllOutRoleArg arg = new ListAllOutRoleArg();
            arg.setLinkAppId(checkConnectedAppId);
            arg.setUpstreamEa(ea);

            HeaderObj headerObj = HeaderObj.newInstance(eid);
            headerObj.setAppId(checkConnectedAppId);

            RestResult<?> result = linkAppService.listAllOutRole(headerObj, arg);
            if (result.isSuccess() && result.getData() instanceof List) {
                return (List<?>) result.getData();
            } else {
                log.error("listAllOutRole error: {}", result);
                return Lists.newArrayList();
            }
        } catch (Exception e) {
            log.error("查询外部角色时发生异常，eid: {}", eid, e);
            return Lists.newArrayList();
        }
    }



    @Override
    public List<Map<String, Object>> getExternalRoleOptions(String tenantId) {
        List<Map<String, Object>> options = Lists.newArrayList();

        try {
            log.info("开始查询外部角色，tenantId: {}", tenantId);

            if (StringUtils.isBlank(tenantId)) {
                log.warn("租户ID为空，返回空外部角色列表");
                return options;
            }
            // 调用真实的外部角色查询API
            List<?> outRoles = listAllOutRole(eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId)), Integer.valueOf(tenantId));

            if (CollectionUtils.isNotEmpty(outRoles)) {
                for (Object outRoleObj : outRoles) {
                    try {
                        // 使用反射获取外部角色信息，避免直接依赖OutRoleData类型
                        String roleId = getFieldValue(outRoleObj, "id");
                        String roleName = getFieldValue(outRoleObj, "name");

                        if (StringUtils.isNotBlank(roleId) && StringUtils.isNotBlank(roleName)) {
                            Map<String, Object> option = Maps.newHashMap();
                            option.put("label", roleName);
                            option.put("value", roleId);
                            options.add(option);
                        }
                    } catch (Exception e) {
                        log.warn("处理外部角色对象时发生异常: {}", e.getMessage());
                    }
                }
                log.info("查询到外部角色数量: {}", options.size());
            } else {
                log.warn("查询外部角色失败或结果为空，tenantId: {}", tenantId);
                // 如果API调用失败，使用预设选项作为备选
//                addCommonExternalRoleOptions(options);
            }

        } catch (Exception e) {
            log.error("查询外部角色时发生异常，tenantId: {}", tenantId, e);
            // 异常时使用预设选项作为备选
//            addCommonExternalRoleOptions(options);
        }

        return options;
    }


    @Override
    public void addRoleOptionsToFieldDescribe(IFieldDescribe fieldDescribe, String fieldApiName, String tenantId) {
        if (fieldDescribe == null || StringUtils.isBlank(fieldApiName) || StringUtils.isBlank(tenantId)) {
            log.warn("参数无效，跳过添加角色选项");
            return;
        }
        
        try {
            // 只处理SelectMany类型的字段（多选字段）
            if (!(fieldDescribe instanceof SelectMany)) {
                log.debug("字段 {} 不是SelectMany类型，跳过处理", fieldApiName);
                return;
            }

            // 只处理SelectMany类型的字段（多选字段）
            List<Map<String, Object>> options = Lists.newArrayList();

            // 根据字段名称确定查询哪种角色
            if (SalaryRuleFields.APPLICABLE_ROLE_INTE.equals(fieldApiName)) {
                // 内部角色字段
                options = getInternalRoleOptions(tenantId);
                log.info("为字段 {} 添加内部角色选项，数量: {}", fieldApiName, options.size());
            } else if (SalaryRuleFields.APPLICABLE_OUT_ROLE.equals(fieldApiName)) {
                // 外部角色字段
                options = getExternalRoleOptions(tenantId);
                log.info("为字段 {} 添加外部角色选项，数量: {}", fieldApiName, options.size());
            } else {
                log.debug("字段 {} 不是角色字段，跳过处理", fieldApiName);
                return;
            }

            // 设置选项到字段描述
            if (CollectionUtils.isNotEmpty(options)) {
                // 注意：这里需要根据实际的SelectMany接口来设置选项
                // 由于SelectMany可能没有直接的setOptions方法，我们记录日志表示处理完成
                log.info("成功为字段 {} 准备角色选项，数量: {}", fieldApiName, options.size());
                log.debug("角色选项详情: {}", options);

                fieldDescribe.set("options",new SelectOption(Maps.newHashMap()));
            } else {
                log.warn("字段 {} 的角色选项为空", fieldApiName);
            }

        } catch (Exception e) {
            log.error("为字段 {} 添加角色选项时发生异常", fieldApiName, e);
        }
    }

    /**
     * 使用反射获取对象的字符串字段值
     *
     * @param obj 对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private String getFieldValue(Object obj, String fieldName) {
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            log.debug("获取字段 {} 值失败: {}", fieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 使用反射获取对象的布尔字段值
     *
     * @param obj 对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private Boolean getBooleanFieldValue(Object obj, String fieldName) {
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            if (value instanceof Boolean) {
                return (Boolean) value;
            } else if (value != null) {
                return Boolean.valueOf(value.toString());
            }
            return null;
        } catch (Exception e) {
            log.debug("获取布尔字段 {} 值失败: {}", fieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 根据角色ID列表获取员工ID列表
     */
    public List<String> getEmployeeIdsByRoles(String tenantId, List<String> roleIds, boolean isExternal,
                                               boolean isOnlyMainRole) {
        Set<String> employeeIds = Sets.newHashSet();

        try {
            if (CollectionUtils.isEmpty(roleIds)) {
                return new ArrayList<>();
            }
            if (isExternal) {
                Set<Long> res = Sets.newHashSet();
                int size = 1000;
                int offset = 0; // 至多调用5次吧
                while (size == 1000 && offset < 50) {
                    // 接口只支持1000条 改成循环调用
                    ListOuterUidsByOuterRoleIdsArg arg = new ListOuterUidsByOuterRoleIdsArg();
                    arg.setOffset(offset * 1000);
                    arg.setLimit(1000);
                    arg.setTenantId(Integer.valueOf(tenantId));
                    arg.setOuterRoleIds(roleIds);
                    HeaderObj headerObj = HeaderObj.newInstance(Integer.valueOf(tenantId));
                    RestResult<Set<Long>> result = appOuterRoleService.listOuterUidsByOuterRoleIds(headerObj, arg);
                    if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
                        res.addAll(result.getData());
                        size = result.getData().size();
                    } else {
                        size = 0;
                    }
                    offset++;
                }
                return res.stream().map(o -> o.toString()).collect(Collectors.toList());
            } else {
                QueryRoleUsersByRoles.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(
                        QueryRoleUsersByRoles.Argument.class, Integer.valueOf(tenantId), -10000, "CRM");
                argument.setRoles(roleIds);
                PaaSResult<Map<String, Map<String, Boolean>>> result = paaSPermissionService
                        .queryRoleUsersByRoles(argument);
                log.info("role result : {}", JSON.toJSONString(result));

                if (Objects.isNull(result) || Objects.isNull(result.getResult())) {
                    return null;
                }
                Map<String, Map<String, Boolean>> roleMap = result.getResult();
                if (Objects.isNull(roleMap)) {
                    return new ArrayList<>();
                }
                roleMap.values().forEach(roleItem -> {
                    if (roleItem != null) {
                        roleItem.forEach((key, value) -> {
                            if (isOnlyMainRole) {
                                if (value != null && value) {
                                    employeeIds.add(key);
                                }
                            } else {
                                if (value != null) {
                                    employeeIds.add(key);
                                }
                            }
                        });
                    }
                });

                return Lists.newArrayList(employeeIds);
            }

        } catch (Exception e) {
            log.error("根据角色ID获取员工ID时发生异常", e);
        }

        return Lists.newArrayList(employeeIds);
    }
}
