package com.facishare.crm.fmcg.wq.model;

@SuppressWarnings("all")
public enum RuleConditionOperator {

    EQUALS("equals", ConditionOperator.EQ),

    NOTEQUALS("notequals", ConditionOperator.N),
    /**
     * 包含
     */
    CONTAINS("contains", ConditionOperator.CONTAINS),
    /**
     * 不包含
     */
    NOTCONTAINS("notcontains", ConditionOperator.NCONTAINS),
    /**
     * 以..开始
     */
    STARTWITH("startwith", ConditionOperator.STARTWITH),
    /**
     * 以..结尾
     */
    ENDWITH("endwith", ConditionOperator.ENDWITH),
    /***
     * in
     */
    IN("IN", ConditionOperator.IN),
    /***
     * not in
     */
    NIN("NIN", ConditionOperator.NIN),
    /***
     * between
     */
    BETWEEN("between", ConditionOperator.BETWEEN),
    /***
     * not between
     */
    NOTBETWEEN("notbetween", ConditionOperator.NBETWEEN),

    /**
     * is null  为空
     */
    IS("IS", ConditionOperator.IS),
    /**
     * is not null 不为空
     */
    ISN("ISN", ConditionOperator.ISN),
    /***
     * not like
     */
    NLIKE("NLIKE", ConditionOperator.NLIKE),
    /***
     * like
     */
    LIKE("LIKE", ConditionOperator.LIKE),
    /***
     * greater than
     */
    GT("GT", ConditionOperator.GT),
    /***
     * less than
     */
    LT("LT", ConditionOperator.LT),
    /***
     * greater than equals
     */
    GTE("GTE", ConditionOperator.GTE),
    /***
     * less than equals
     */
    LTE("LTE", ConditionOperator.LTE),
    /***
     * equals
     */
    EQ("EQ", ConditionOperator.EQ),
    /***
     * not equals
     */
    N("N", ConditionOperator.NEQ),

    BETWEEN_UP("BETWEEN", ConditionOperator.BETWEEN);

    private final String value;

    private final ConditionOperator metadataOperator;

    RuleConditionOperator(String value, ConditionOperator metadataOperator) {
        this.value = value;
        this.metadataOperator = metadataOperator;
    }

    public String value() {
        return this.value;
    }

    public ConditionOperator metadataOperator() {
        return this.metadataOperator;
    }
}