package com.facishare.crm.fmcg.wq.action;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryDetailFields;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.constants.PublicEmployeeFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.util.RoleNameConverter;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.facishare.crm.fmcg.wq.util.FieldValidationUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 员工固定薪资新增Action
 */
@Slf4j
public class EmployeeFixedSalaryAddAction extends FmcgSkipPermissionAddAction {

  private SalaryRuleDao salaryRuleDao = SpringUtil.getContext().getBean(SalaryRuleDao.class);
  private SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);
  private EmployeeDao employeeDao = SpringUtil.getContext().getBean(EmployeeDao.class);
  private EmployeeFixedSalaryDao employeeFixedSalaryDao = SpringUtil.getContext().getBean(EmployeeFixedSalaryDao.class);
  private RoleClient roleClient = SpringUtil.getContext().getBean(RoleClient.class);

  @Override
  protected void before(Arg arg) {
    this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
    super.before(arg);
    log.info("开始处理员工固定薪资新增，数据: {}", arg.getObjectData());

    ObjectDataExt objectData = ObjectDataExt.of(arg.getObjectData().toObjectData());

    // 员工唯一性校验
    validateEmployeeUniqueness(objectData);
    
    // 获取主对象的定薪方式
    String salaryMethod = objectData.getStringValue(EmployeeFixedSalaryFields.SALARY_METHOD);
    if (StringUtils.isBlank(salaryMethod)) {
      throw new ValidateException("请选择定薪方式"); //ignoreI18n
    }
    
    // 验证薪资规则
    Object salaryRuleId = objectData.get(EmployeeFixedSalaryFields.SALARY_RULE);
    if (salaryRuleId != null) {
      // 获取薪资规则对象
      IObjectData salaryRuleObj = salaryRuleDao.getById(actionContext.getTenantId(), salaryRuleId.toString());
      if (salaryRuleObj == null) {
        throw new ValidateException("薪资规则不存在"); //ignoreI18n
      }
      
      // 验证定薪方式与规则的定薪方式一致
      String ruleSalaryMethod = ObjectDataExt.of(salaryRuleObj).getStringValue(SalaryRuleFields.SALARY_METHOD);
      if (!salaryMethod.equals(ruleSalaryMethod)) {
        String salaryMethodDisplayName = getFieldDisplayName(EmployeeFixedSalaryFields.SALARY_METHOD);
        throw new ValidateException(String.format("%s与规则的%s不一致", salaryMethodDisplayName, salaryMethodDisplayName)); //ignoreI18n
      }
      
      // 验证选择的规则是否在适用范围内
      List<IObjectData> applicableRuleObjs = salaryService.getApplicableSalaryRuleIdsByEmployeeFixedSalary(
          actionContext.getTenantId(), objectData);
      List<String> applicableRuleIds = applicableRuleObjs.stream()
          .map(IObjectData::getId)
          .collect(Collectors.toList());
      
      if (CollectionUtils.isNotEmpty(applicableRuleIds) && !applicableRuleIds.contains(salaryRuleId.toString())) {
        throw new ValidateException("选择的薪资规则不在适用范围内"); //ignoreI18n
      }
    }
    
    // 验证从对象中的工资项定薪方式与主对象一致，并校验工资项不重复
    List<ObjectDataDocument> salaryDetails = arg.getDetails().getOrDefault(EmployeeFixedSalaryDetailFields.API_NAME, Lists.newArrayList());
    if (!CollectionUtils.isEmpty(salaryDetails)) {
      validateSalaryItemsInDetails(salaryDetails, salaryMethod);
    }
    
    // 互联角色重写赋值
    // 互联用户
//    String employeeExternalId = objectData.getStringValue(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);
//    if (StringUtils.isNotBlank(employeeExternalId)) {
//      //互联用户
//      IObjectData employeeObj = employeeDao.getExternalEmployeeById(actionContext.getTenantId(), employeeExternalId);
//      if (employeeObj != null && employeeObj.get(PublicEmployeeFields.OUTER_TENANT_ID) != null) {
//        List<String> roleIds = employeeDao.getEmployeeRoles(actionContext.getTenantId(), employeeExternalId,
//                employeeObj.get(PublicEmployeeFields.OUTER_TENANT_ID).toString(), true);
//
//        // 将角色ID转换为角色名称
//        List<String> roleNames = convertRoleIdsToNames(roleIds);
//
//        arg.getObjectData().put(EmployeeFixedSalaryFields.CONNECTED_ROLE, roleNames);
//
//        log.debug("员工 {} 的角色ID: {}, 转换后的角色名称: {}", employeeExternalId, roleIds, roleNames);
//      }
//    }
    
    log.info("员工固定薪资新增前处理完成");
  }

  /**
   * 将角色ID列表转换为角色名称列表
   *
   * @param roleIds 角色ID列表
   * @return 角色名称列表
   */
  private List<String> convertRoleIdsToNames(List<String> roleIds) {
    try {
      String tenantId = actionContext.getTenantId();
      return RoleNameConverter.convertRoleIdsToNames(roleClient, tenantId, roleIds);
    } catch (Exception e) {
      log.error("转换角色ID到名称时发生异常，角色ID: {}", roleIds, e);
      // 异常时返回原始ID列表作为备选
      return roleIds != null ? roleIds : Lists.newArrayList();
    }
  }
  
  @Override
  protected Result after(Arg arg, Result result) {
    Result after = super.after(arg, result);
    log.info("员工固定薪资新增成功，ID: {}", result.getObjectData().getId());
    return after;
  }

  /**
   * 员工唯一性校验
   * 除了空值外，每个员工只能有一条固定薪资记录
   *
   * @param objectData 员工固定薪资数据
   */
  private void validateEmployeeUniqueness(ObjectDataExt objectData) {
    String internalEmployeeId = objectData.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE);
    String externalEmployeeId = objectData.getEmployeeFieldValue(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);

    // 校验不能同时设置内部员工和外部员工
    if (StringUtils.isNotBlank(internalEmployeeId) && StringUtils.isNotBlank(externalEmployeeId)) {
      throw new ValidateException("不能同时设置内部员工和外部员工"); //ignoreI18n
    }

    // 至少要设置一个员工
    if (StringUtils.isBlank(internalEmployeeId) && StringUtils.isBlank(externalEmployeeId)) {
      throw new ValidateException("请选择员工"); //ignoreI18n
    }

    // 检查内部员工唯一性
    if (StringUtils.isNotBlank(internalEmployeeId)) {
      IObjectData existingRecord = employeeFixedSalaryDao.getByEmployeeId(actionContext.getTenantId(), internalEmployeeId);
      if (existingRecord != null) {
        throw new ValidateException("该内部员工已存在固定薪资记录，不能重复创建"); //ignoreI18n
      }
    }

    // 检查外部员工唯一性
    if (StringUtils.isNotBlank(externalEmployeeId)) {
      IObjectData existingRecord = employeeFixedSalaryDao.getByEmployeeId(actionContext.getTenantId(), externalEmployeeId);
      if (existingRecord != null) {
        throw new ValidateException("该外部员工已存在固定薪资记录，不能重复创建"); //ignoreI18n
      }
    }

    log.info("员工唯一性校验通过，内部员工ID: {}, 外部员工ID: {}", internalEmployeeId, externalEmployeeId);
  }

  /**
   * 验证从对象中的工资项
   * 1. 校验工资项的定薪方式与主对象一致
   * 2. 校验工资项不重复
   * 3. 校验金额不能为负数
   *
   * @param salaryDetails 员工固定工资明细列表
   * @param salaryMethod 主对象的定薪方式
   */
  private void validateSalaryItemsInDetails(List<ObjectDataDocument> salaryDetails, String salaryMethod) {
    List<String> salaryItemIds = Lists.newArrayList();

    for (ObjectDataDocument detail : salaryDetails) {
      IObjectData detailData = detail.toObjectData();
      Object salaryItemId = detailData.get(EmployeeFixedSalaryDetailFields.SALARY_ITEM);

      // 验证金额不能为负数
      validateAmountNotNegative(detailData, salaryItemId);

      if (salaryItemId != null) {
        String salaryItemIdStr = salaryItemId.toString();

        // 校验工资项不重复
        if (salaryItemIds.contains(salaryItemIdStr)) {
          // 获取工资项名称用于错误提示
          IObjectData salaryItemObj = serviceFacade.findObjectData(actionContext.getUser(), salaryItemIdStr, "SalaryItemObj");
          String salaryItemName = salaryItemObj != null ? salaryItemObj.getName() : salaryItemIdStr;
          throw new ValidateException("工资项[" + salaryItemName + "]不能重复"); //ignoreI18n
        }

        salaryItemIds.add(salaryItemIdStr);

        // 获取工资项对象
        IObjectData salaryItemObj = serviceFacade.findObjectData(actionContext.getUser(), salaryItemIdStr, "SalaryItemObj");
        if (salaryItemObj == null) {
          throw new ValidateException("工资项不存在"); //ignoreI18n
        }

        // 验证工资项的定薪方式与主对象一致
        String itemSalaryMethod = ObjectDataExt.of(salaryItemObj).getStringValue(SalaryItemFields.SALARY_METHOD);
        if (!salaryMethod.equals(itemSalaryMethod)) {
          throw new ValidateException("工资项[" + salaryItemObj.getName() + "]的定薪方式与主对象不一致"); //ignoreI18n
        }
      }
    }

    log.info("工资项校验通过，共{}个工资项，无重复", salaryItemIds.size());
  }

  /**
   * 验证金额不能为负数
   *
   * @param detailData 明细数据
   * @param salaryItemId 工资项ID
   */
  private void validateAmountNotNegative(IObjectData detailData, Object salaryItemId) {
    Object amountObj = detailData.get(EmployeeFixedSalaryDetailFields.AMOUNT);

    if (amountObj == null) {
      // 金额为空，不进行验证（由必填验证处理）
      return;
    }

    try {
      BigDecimal amount;

      // 处理不同类型的金额数据
      if (amountObj instanceof BigDecimal) {
        amount = (BigDecimal) amountObj;
      } else if (amountObj instanceof Number) {
        amount = new BigDecimal(amountObj.toString());
      } else if (amountObj instanceof String) {
        String amountStr = (String) amountObj;
        if (StringUtils.isBlank(amountStr)) {
          return; // 空字符串不验证
        }
        amount = new BigDecimal(amountStr);
      } else {
        log.warn("金额字段类型不支持: {}", amountObj.getClass().getName());
        return;
      }

      // 验证金额不能为负数
      if (amount.compareTo(BigDecimal.ZERO) < 0) {
        // 获取工资项名称用于错误提示
        String salaryItemName = getSalaryItemName(salaryItemId);
        throw new ValidateException("工资项 " + salaryItemName + "，金额不能为负数"); //ignoreI18n
      }

      log.debug("金额验证通过: {}", amount);

    } catch (NumberFormatException e) {
      log.error("金额格式错误: {}", amountObj, e);
      // 获取工资项名称用于错误提示
      String salaryItemName = getSalaryItemName(salaryItemId);
      throw new ValidateException("工资项 " + salaryItemName + "，金额格式错误"); //ignoreI18n
    }
  }

  /**
   * 获取工资项名称
   *
   * @param salaryItemId 工资项ID
   * @return 工资项名称
   */
  private String getSalaryItemName(Object salaryItemId) {
    if (salaryItemId == null) {
      return "未知工资项"; //ignoreI18n
    }

    try {
      IObjectData salaryItemObj = serviceFacade.findObjectData(actionContext.getUser(), salaryItemId.toString(), "SalaryItemObj");
      return salaryItemObj != null ? salaryItemObj.getName() : salaryItemId.toString();
    } catch (Exception e) {
      log.warn("获取工资项名称失败，工资项ID: {}", salaryItemId, e);
      return salaryItemId.toString();
    }
  }

  /**
   * 获取字段的显示名称
   * 从字段描述中动态获取标签信息
   *
   * @param fieldName 字段名
   * @return 显示名称
   */
  private String getFieldDisplayName(String fieldName) {
    return FieldValidationUtil.getFieldDisplayName(objectDescribe, fieldName);
  }
}


