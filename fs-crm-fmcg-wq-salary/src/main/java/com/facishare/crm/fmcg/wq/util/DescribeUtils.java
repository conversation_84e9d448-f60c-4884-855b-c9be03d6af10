package com.facishare.crm.fmcg.wq.util;

import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import lombok.extern.slf4j.Slf4j;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-07-09 20:22
 **/
@Slf4j
public class DescribeUtils {

    /**
     * 虚拟字段配置类
     */
    public static class VirtualFieldConfig {
        private String fieldName;
        private String label;
        private String description;
        private String helpText;
        private int maxLength;

        public VirtualFieldConfig(String fieldName, String label, String description, String helpText) {
            this(fieldName, label, description, helpText, 2000);
        }

        public VirtualFieldConfig(String fieldName, String label, String description, String helpText, int maxLength) {
            this.fieldName = fieldName;
            this.label = label;
            this.description = description;
            this.helpText = helpText;
            this.maxLength = maxLength;
        }

        // Getters
        public String getFieldName() { return fieldName; }
        public String getLabel() { return label; }
        public String getDescription() { return description; }
        public String getHelpText() { return helpText; }
        public int getMaxLength() { return maxLength; }
    }

    /**
     * 统一的添加虚拟字段到对象描述的方法
     *
     * @param describeDocument 对象描述文档
     * @param config 虚拟字段配置
     * @return 更新后的对象描述文档
     */
    public static ObjectDescribeDocument addVirtualFieldToObjectDescribe(
            ObjectDescribeDocument describeDocument, VirtualFieldConfig config) {
        try {
            if (describeDocument == null) {
                log.warn("对象描述为空，跳过虚拟字段添加");
                return describeDocument;
            }

            if (config == null) {
                log.warn("虚拟字段配置为空，跳过添加");
                return describeDocument;
            }

            ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describeDocument);

            // 检查虚拟字段是否已存在
            if (objectDescribeExt.getFieldDescribe(config.getFieldName()) != null) {
                log.debug("虚拟字段已存在于对象描述中，跳过添加: {}", config.getFieldName());
                return describeDocument;
            }

            // 创建虚拟字段描述
            TextFieldDescribe virtualFieldDescribe = createVirtualFieldDescribe(objectDescribeExt, config);

            IObjectDescribe iObjectDescribe = objectDescribeExt.copy();
            // 添加虚拟字段到对象描述
            iObjectDescribe.addFieldDescribe(virtualFieldDescribe);

            // 更新result中的对象描述
            return ObjectDescribeDocument.of(iObjectDescribe);
        } catch (Exception e) {
            log.warn("添加虚拟字段到对象描述失败: {}", config != null ? config.getFieldName() : "unknown", e);
        }
        return describeDocument;
    }

    /**
     * 创建虚拟字段描述
     *
     * @param objectDescribeExt 对象描述扩展
     * @param config 虚拟字段配置
     * @return 虚拟字段描述
     */
    private static TextFieldDescribe createVirtualFieldDescribe(ObjectDescribeExt objectDescribeExt, VirtualFieldConfig config) {
        TextFieldDescribe virtualFieldDescribe = new TextFieldDescribe();

        // 基本属性
        virtualFieldDescribe.set(IFieldDescribe.API_NAME, config.getFieldName());
        virtualFieldDescribe.set(IFieldDescribe.LABEL, config.getLabel());
        virtualFieldDescribe.set("label_r", config.getLabel());
        virtualFieldDescribe.set(IFieldDescribe.TYPE, "text");

        // 字段配置属性
        virtualFieldDescribe.set("describe_api_name", objectDescribeExt.getApiName());
        virtualFieldDescribe.set("default_is_expression", false);
        virtualFieldDescribe.set("auto_adapt_places", false);
        virtualFieldDescribe.set("empty_prompt", "");
        virtualFieldDescribe.set("pattern", "");
        virtualFieldDescribe.set("description", config.getDescription());
        virtualFieldDescribe.set("is_unique", false);
        virtualFieldDescribe.set("default_to_zero", false);
        virtualFieldDescribe.set("ai_api_name", "");
        virtualFieldDescribe.set("is_required", false);
        virtualFieldDescribe.set("enable_clone", false);
        virtualFieldDescribe.set("define_type", "package");
        virtualFieldDescribe.set("input_mode", "");
        virtualFieldDescribe.set("is_single", false);
        virtualFieldDescribe.set("max_length", config.getMaxLength());
        virtualFieldDescribe.set("is_index", false);
        virtualFieldDescribe.set("is_active", true);
        virtualFieldDescribe.set("is_encrypted", false);
        virtualFieldDescribe.set("default_value", "");
        virtualFieldDescribe.set("security_level", "");
        virtualFieldDescribe.set("inherit_type", 0);
        virtualFieldDescribe.set("help_text_type", "hover");
        virtualFieldDescribe.set("is_index_field", false);
        virtualFieldDescribe.set("is_show_mask", false);
        virtualFieldDescribe.set("help_text", config.getHelpText());
        virtualFieldDescribe.set("status", "new");

        // 时间戳
        long currentTime = System.currentTimeMillis();
        virtualFieldDescribe.set("last_modified_time", currentTime);
        virtualFieldDescribe.set("create_time", currentTime);

        return virtualFieldDescribe;
    }

    /**
     * 添加KPI虚拟字段到对象描述（保持向后兼容）
     */
    public static ObjectDescribeDocument addKPIVirtualFieldToObjectDescribe(ObjectDescribeDocument describeDocument) {
        VirtualFieldConfig config = new VirtualFieldConfig(
            SalaryKPIFields.DIMENSION_DESCRIPTION,
                I18N.text("SalaryKPIObj.field.dimension_description.label"),//"计算方式描述", //ignoreI18n
                I18N.text("SalaryKPIObj.field.dimension_description.description"),//"指标维度统计方式的详细描述", //ignoreI18n
                I18N.text("SalaryKPIObj.field.dimension_description.text.field_help_text")//"根据指标计算方式自动生成的详细描述信息" //ignoreI18n
        );
        return addVirtualFieldToObjectDescribe(describeDocument, config);
    }

    /**
     * 添加互联角色显示虚拟字段到对象描述
     *
     * @param describeDocument 对象描述文档
     * @param virtualFieldName 虚拟字段名称
     * @return 更新后的对象描述文档
     */
    public static ObjectDescribeDocument addConnectedRoleVirtualFieldToObjectDescribe(
            ObjectDescribeDocument describeDocument, String virtualFieldName) {
        VirtualFieldConfig config = new VirtualFieldConfig(
            virtualFieldName,
                I18N.text("EmployeeFixedSalaryObj.field.connected_role.label"),//"互联角色", //ignoreI18n
                I18N.text("EmployeeFixedSalaryObj.field.connected_role.description"),//"外部员工的互联角色信息", //ignoreI18n
                I18N.text("EmployeeFixedSalaryObj.field.connected_role.text.field_help_text")// "显示外部员工在互联企业中的角色信息" //ignoreI18n
        );
        return addVirtualFieldToObjectDescribe(describeDocument, config);
    }

    /**
     * 添加工资项计算公式描述虚拟字段到对象描述
     *
     * @param describeDocument 对象描述文档
     * @param virtualFieldName 虚拟字段名称
     * @return 更新后的对象描述文档
     */
    public static ObjectDescribeDocument addSalaryItemFormulaVirtualFieldToObjectDescribe(
            ObjectDescribeDocument describeDocument, String virtualFieldName) {
        VirtualFieldConfig config = new VirtualFieldConfig(
            virtualFieldName,
                I18N.text("SalaryItemObj.field.calculation_formula_desc.label"),//"计算公式描述", //ignoreI18n
                I18N.text("SalaryItemObj.field.calculation_formula_desc.description"),//"工资项计算公式的中文描述信息", //ignoreI18n
                I18N.text("SalaryItemObj.field.calculation_formula_desc.text.field_help_text")//"显示工资项计算公式的中文描述，便于理解公式含义" //ignoreI18n
        );
        return addVirtualFieldToObjectDescribe(describeDocument, config);
    }

    /**
     * 添加工资规则内部角色虚拟字段到对象描述
     *
     * @param describeDocument 对象描述文档
     * @param virtualFieldName 虚拟字段名称
     * @return 更新后的对象描述文档
     */
    public static ObjectDescribeDocument addSalaryRuleInternalRoleVirtualFieldToObjectDescribe(
            ObjectDescribeDocument describeDocument, String virtualFieldName,String label) {
        VirtualFieldConfig config = new VirtualFieldConfig(
            virtualFieldName,
            label, //ignoreI18n
            null, //ignoreI18n
            null //ignoreI18n
        );
        return addVirtualFieldToObjectDescribe(describeDocument, config);
    }

    /**
     * 添加工资规则外部角色虚拟字段到对象描述
     *
     * @param describeDocument 对象描述文档
     * @param virtualFieldName 虚拟字段名称
     * @return 更新后的对象描述文档
     */
    public static ObjectDescribeDocument addSalaryRuleExternalRoleVirtualFieldToObjectDescribe(
            ObjectDescribeDocument describeDocument, String virtualFieldName,String label) {
        VirtualFieldConfig config = new VirtualFieldConfig(
            virtualFieldName,
            label, //ignoreI18n
            null, //ignoreI18n
            null //ignoreI18n
        );
        return addVirtualFieldToObjectDescribe(describeDocument, config);
    }

    /**
     * 创建虚拟字段配置的便利方法
     *
     * @param fieldName 字段名称
     * @param label 字段标签
     * @param description 字段描述
     * @param helpText 帮助文本
     * @return 虚拟字段配置
     */
    public static VirtualFieldConfig createVirtualFieldConfig(String fieldName, String label, String description, String helpText) {
        return new VirtualFieldConfig(fieldName, label, description, helpText);
    }

    /**
     * 创建虚拟字段配置的便利方法（带最大长度）
     *
     * @param fieldName 字段名称
     * @param label 字段标签
     * @param description 字段描述
     * @param helpText 帮助文本
     * @param maxLength 最大长度
     * @return 虚拟字段配置
     */
    public static VirtualFieldConfig createVirtualFieldConfig(String fieldName, String label, String description, String helpText, int maxLength) {
        return new VirtualFieldConfig(fieldName, label, description, helpText, maxLength);
    }
}
