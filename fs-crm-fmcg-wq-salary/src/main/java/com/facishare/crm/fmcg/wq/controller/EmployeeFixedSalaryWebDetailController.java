package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.service.ConnectedRoleFieldService;
import com.facishare.crm.fmcg.wq.util.DescribeUtils;
import com.facishare.crm.fmcg.wq.util.LayoutUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 员工固定工资表详情页控制器
 *
 * 功能：
 * 1. 添加互联角色显示虚拟字段到布局
 * 2. 设置互联角色显示虚拟字段的值
 * 3. 保持向后兼容，继续支持原有的角色字段覆盖
 *
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class EmployeeFixedSalaryWebDetailController extends StandardWebDetailController {

  private ConnectedRoleFieldService connectedRoleFieldService = SpringUtil.getContext().getBean(ConnectedRoleFieldService.class);

  @Override
  protected Result after(Arg arg, Result result) {
    log.info("EmployeeFixedSalaryWebDetailController after method called");
    result = super.after(arg, result);

    try {
      // 添加互联角色显示虚拟字段到布局
      result.setLayout(LayoutUtils.addVirtualFieldToDetailLayout(
              result.getLayout(),
              EmployeeFixedSalaryFields.CONNECTED_COMPANY,
              EmployeeFixedSalaryFields.CONNECTED_ROLE_DISPLAY
      ));

      // 添加虚拟字段到对象描述中
      result.setDescribe(DescribeUtils.addConnectedRoleVirtualFieldToObjectDescribe(
          result.getDescribe(),
          EmployeeFixedSalaryFields.CONNECTED_ROLE_DISPLAY
      ));

      // 获取员工固定工资数据
      ObjectDataDocument dataDocument = result.getData();
      if (dataDocument == null) {
        log.warn("员工固定工资数据为空，跳过角色信息处理");
        return result;
      }

      ObjectDataExt salaryExt = ObjectDataExt.of(dataDocument);
      String tenantId = controllerContext.getTenantId();

      // 设置互联角色显示虚拟字段的值
      connectedRoleFieldService.setConnectedRoleDisplayField(
          tenantId,
          salaryExt,
          dataDocument,
          EmployeeFixedSalaryFields.EMPLOYEE,
          EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL,
          EmployeeFixedSalaryFields.CONNECTED_ROLE_DISPLAY
      );

      log.debug("员工固定工资详情页互联角色字段处理完成");

    } catch (Exception e) {
      log.error("处理员工固定工资详情页时发生异常", e);
      // 不影响正常显示，只记录错误日志
    }

    return result;
  }
}
