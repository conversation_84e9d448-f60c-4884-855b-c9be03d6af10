package com.facishare.crm.fmcg.wq.util;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-08-11 18:53
 **/
public class DataUtils {
    public static List<String> convertToStringList(Object obj) {
        if (obj == null) {
            return Lists.newArrayList();
        }

        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            return list.stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.toList());
        } else if (obj instanceof String) {
            String str = (String) obj;
            if (StringUtils.isNotBlank(str)) {
                return Lists.newArrayList(str.split(","));
            }
        }

        return Lists.newArrayList();
    }
}
