package com.facishare.crm.fmcg.wq.notify.impl;

import com.facishare.crm.fmcg.wq.constants.SalaryDataFields;
import com.facishare.crm.fmcg.wq.notify.SalaryMessageService;
import com.facishare.crm.fmcg.wq.session.FmcgPushSession;
import com.facishare.crm.fmcg.wq.session.RestSendMessageService;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.Utils.ReceiverChannelUtils;
import com.fxiaoke.constant.ReceiverChannelType;
import com.fxiaoke.enterpriserelation2.arg.UnionMessageSendArg;
import com.fxiaoke.enterpriserelation2.arg.message.UnionCardMessage;
import com.fxiaoke.model.*;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工资条消息服务实现类
 */
@Slf4j
@Service
public class SalaryMessageServiceImpl implements SalaryMessageService {

    @Autowired
    private RestSendMessageService restSendMessageService;

    @Autowired
    private FmcgPushSession fmcgPushSession;

    public static String SALARY_JUMP_URL = "https://www.fxiaoke.com/fsh5/attendance/gray/index.html#/salary/%s";

    static {
        ConfigFactory.getInstance().getConfig("CheckInService", config -> {
            SALARY_JUMP_URL = config.get("salaryJumpUrl", SALARY_JUMP_URL);
        });
    }



    @Override
    public boolean sendInternalEmployeeMessage(String tenantId, String employeeId, IObjectData salaryData) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeId) || salaryData == null) {
            log.warn("发送内部员工工资条消息参数无效，tenantId: {}, employeeId: {}", tenantId, employeeId);
            return false;
        }

        try {
            // 创建文本卡片消息参数
            SendTextCardMessageArg messageArg = createSalaryTextCardMessageArg(salaryData);

            // 设置接收者和租户ID
            messageArg.setReceiverIds(Lists.newArrayList(Integer.valueOf(employeeId)));
            messageArg.setEi(Integer.valueOf(tenantId));

            // 使用RestSendMessageService的重试方法发送消息
            return restSendMessageService.sendTextCardMessageWithRetry(messageArg, employeeId);
        } catch (NumberFormatException e) {
            log.error("员工ID格式错误，无法发送消息，employeeId: {}", employeeId, e);
            return false;
        }
    }

    @Override
    public boolean sendExternalEmployeeMessage(String tenantId, String employeeExternalId, IObjectData salaryData) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeExternalId) || salaryData == null) {
            log.warn("发送外部员工工资条消息参数无效，tenantId: {}, employeeExternalId: {}", tenantId, employeeExternalId);
            return false;
        }

        try {
            // 获取工资条信息
            Long startTime = salaryData.get(SalaryDataFields.START_DATE, Long.class);
            Long endTime = salaryData.get(SalaryDataFields.END_DATE, Long.class);
            String payableSalary = salaryData.get(SalaryDataFields.PAYABLE_SALARY, String.class);

            // 格式化日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String startDateStr = dateFormat.format(new Date(startTime));
            String endDateStr = dateFormat.format(new Date(endTime));
            String periodStr = startDateStr + " 至 " + endDateStr; //ignoreI18n

            // 构建通知标题和内容
            String title = periodStr + "工资条下发提醒"; //ignoreI18n
            
            // 构建URL
            String url = String.format(SALARY_JUMP_URL, salaryData.getId());
            
            // 构建通知内容，将URL放入内容中
            String content = url; //ignoreI18n
            
            // 构建外部用户ID列表
            List<String> outerUids = Collections.singletonList("E." + tenantId + "." + employeeExternalId);
            
            // 使用系统用户ID发送通知
            String systemUserId = "-10000"; // 系统用户ID
            
            // 调用发送互联通知公告方法
            return restSendMessageService.sendInterconnectNoticeWithRetry(tenantId, systemUserId, title, content, outerUids);
        } catch (Exception e) {
            log.error("发送外部员工工资条消息异常，employeeExternalId: {}", employeeExternalId, e);
            return false;
        }
    }

    @Override
    public int batchSendInternalEmployeeMessages(String tenantId, List<IObjectData> salaryDataList) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(salaryDataList)) {
            log.warn("批量发送内部员工工资条消息参数无效，tenantId: {}, salaryDataList为空", tenantId);
            return 0;
        }

        log.info("批量发送内部员工工资条消息，数量: {}", salaryDataList.size());
        int successCount = 0;
        
        // 按员工ID分组，一个员工可能有多个工资条
        Map<String, List<IObjectData>> employeeSalaryMap = salaryDataList.stream()
            .filter(data -> StringUtils.isNotBlank(data.get(SalaryDataFields.EMPLOYEE, String.class)))
            .collect(Collectors.groupingBy(data -> data.get(SalaryDataFields.EMPLOYEE, String.class)));
        
        // 处理每个员工的工资条
        for (Map.Entry<String, List<IObjectData>> entry : employeeSalaryMap.entrySet()) {
            String employeeId = entry.getKey();
            List<IObjectData> employeeSalaryList = entry.getValue();
            
            // 为每个工资条创建并发送消息
            for (IObjectData salaryData : employeeSalaryList) {
                if (sendInternalEmployeeMessage(tenantId, employeeId, salaryData)) {
                    successCount++;
                }
            }
        }
        
        return successCount;
    }

    @Override
    public int batchSendExternalEmployeeMessages(String tenantId, List<IObjectData> salaryDataList) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(salaryDataList)) {
            log.warn("批量发送外部员工工资条消息参数无效，tenantId: {}, salaryDataList为空", tenantId);
            return 0;
        }

        log.info("批量发送外部员工工资条消息，数量: {}", salaryDataList.size());
        int successCount = 0;
        
        // 系统用户ID
        String systemUserId = "1000";
        
        try {
            // 按照时间段分组，相同时间段的工资条使用相同的标题和内容格式
            Map<String, List<IObjectData>> periodSalaryMap = salaryDataList.stream()
                .filter(data -> StringUtils.isNotBlank(data.get(SalaryDataFields.EMPLOYEE_EXTERNAL, String.class)))
                .collect(Collectors.groupingBy(data -> {
                    Long startTime = data.get(SalaryDataFields.START_DATE, Long.class);
                    Long endTime = data.get(SalaryDataFields.END_DATE, Long.class);
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    String startDateStr = dateFormat.format(new Date(startTime));
                    String endDateStr = dateFormat.format(new Date(endTime));
                    return startDateStr + " 至 " + endDateStr; //ignoreI18n
                }));
            
            // 处理每个时间段的工资条
            for (Map.Entry<String, List<IObjectData>> periodEntry : periodSalaryMap.entrySet()) {
                String periodStr = periodEntry.getKey();
                List<IObjectData> periodSalaryList = periodEntry.getValue();

                // 构建通知标题和内容模板
                String title = periodStr + "工资条下发提醒"; //ignoreI18n
                String url = String.format(SALARY_JUMP_URL, periodSalaryList.get(0).getId());
                //value 下所有 员工
                String content = url; //ignoreI18n

                // 构建外部用户ID列表
                List<String> outerUids = periodSalaryList.stream()
                    .map(data -> "E." + tenantId + "." + data.get(SalaryDataFields.EMPLOYEE_EXTERNAL, String.class))
                    .distinct()
                    .collect(Collectors.toList());

                // 发送通知
                if (restSendMessageService.sendInterconnectNoticeWithRetry(tenantId, systemUserId, title, content, outerUids)) {
                    successCount += periodSalaryList.size();
                }
            }
            
            log.info("批量发送外部员工工资条消息完成，总数: {}, 成功数: {}", salaryDataList.size(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量发送外部员工工资条消息异常", e);
            return successCount;
        }
    }
    @Override
    public SendTextCardMessageArg createSalaryTextCardMessageArg(IObjectData salaryData) {
        // 获取工资条信息
        long startTime = salaryData.get(SalaryDataFields.START_DATE, Long.class);
        long endTime = salaryData.get(SalaryDataFields.END_DATE, Long.class);
        String payableSalary = salaryData.get(SalaryDataFields.PAYABLE_SALARY, String.class);

        // 格式化日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String startDateStr = dateFormat.format(new Date(startTime));
        String endDateStr = dateFormat.format(new Date(endTime));
        String periodStr = startDateStr + " 至 " + endDateStr; //ignoreI18n

        // 创建默认参数
        SendTextCardMessageArg arg = buildDefaultArg(RestSendMessageService.PMM_APPID);

        // 创建文本卡片消息
        TextCardMessage textCardMessage = new TextCardMessage();

        // 设置消息头部
        TextCardMessageHead head = new TextCardMessageHead();
        head.setTitleElement(new TextCardElement("工资条下发提醒", "", "")); //ignoreI18n
        textCardMessage.setHead(head);
        //请查收2025年3月的工资条

        // 设置消息体
        TextCardMessageBody body = new TextCardMessageBody();
        body.setContentElement(new TextCardElement("请查收" + periodStr + "的工资条", "", "")); //ignoreI18n
        textCardMessage.setBody(body);

        // 设置消息底部
        TextCardMessageFoot foot = new TextCardMessageFoot();
        foot.setFootElement(new TextCardElement("查看详情", "", "")); //ignoreI18n
        textCardMessage.setFoot(foot);

        // 设置URL，使用配置的 jumpUrlFormart
        String url = String.format(SALARY_JUMP_URL, salaryData.getId());
        textCardMessage.setInnerPlatformMobileUrl(url);
        textCardMessage.setInnerPlatformWebUrl(url);
        textCardMessage.setOutPlatformUrl(url);

        // 设置文本卡片消息
        arg.setTextCardMessage(textCardMessage);

        return arg;
    }

    /**
     * 构建默认的文本卡片消息参数
     *
     * @param appId 应用ID
     * @return 文本卡片消息参数
     */
    private SendTextCardMessageArg buildDefaultArg(String appId) {
        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setUuid(UUID.randomUUID().toString());
        // 应用通知
        arg.setReceiverChannelType(ReceiverChannelType.NOTICE);
        arg.setGenerateUrlType(0);
        // APPID
        arg.setReceiverChannelData(ReceiverChannelUtils.buildNoticeChannelData(appId));
        return arg;
    }

    /**
     * 创建外部员工工资条UnionMessageSendArg参数
     *
     * @param salaryData 工资条数据
     * @param tenantId 租户ID
     * @param employeeExternalId 外部员工ID
     * @return UnionMessageSendArg参数
     */
    private UnionMessageSendArg createExternalSalaryUnionMessageSendArg(IObjectData salaryData, String tenantId, String employeeExternalId) {
        // 获取工资条信息
        Long startTime = salaryData.get(SalaryDataFields.START_DATE, Long.class);
        Long endTime = salaryData.get(SalaryDataFields.END_DATE, Long.class);
        String payableSalary = salaryData.get(SalaryDataFields.PAYABLE_SALARY, String.class);

        // 格式化日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String startDateStr = dateFormat.format(new Date(startTime));
        String endDateStr = dateFormat.format(new Date(endTime));
        String periodStr = startDateStr + " 至 " + endDateStr; //ignoreI18n

        // 创建UnionMessageSendArg
        UnionMessageSendArg unionMessageSendArg = new UnionMessageSendArg();

        // 创建UnionCardMessage
        UnionCardMessage unionCardMessage = new UnionCardMessage();

        // 设置URL
        String url = String.format("ava://salary-detail/pages/detail?salary_id=%s", salaryData.getId());
        unionCardMessage.setForwardUrl(url);

        // 设置消息内容
        unionCardMessage.setSummary("请查收" + periodStr + "的工资条"); //ignoreI18n
        unionCardMessage.setTile("工资条下发提醒"); //ignoreI18n

//        // 设置模板ID映射
//        Map<String, LinkedHashMap<String, String>> templateIdKeyMap = new LinkedHashMap<>();
//        templateIdKeyMap.put("", new LinkedHashMap<>());
//        unionCardMessage.setTemplateIdKeyMap(templateIdKeyMap);

//        // 设置额外数据
//        Map<String, Object> extraDataMap = new HashMap<>();
//        extraDataMap.put("salaryId", salaryData.getId());
//        extraDataMap.put("period", periodStr);
//        if (StringUtils.isNotBlank(payableSalary)) {
//            extraDataMap.put("payableSalary", payableSalary);
//        }
//        unionCardMessage.setExtraDataMap(extraDataMap);

        // 设置UnionMessageSendArg属性
        unionMessageSendArg.setFsAppId(RestSendMessageService.PMM_APPID);
        unionMessageSendArg.setMessage(unionCardMessage);
        unionMessageSendArg.setLinkType(1); // 默认链接类型
        unionMessageSendArg.setMessageType(UnionMessageSendArg.UnionMessageType.CARD.getType());


        // 设置渠道信息
//        Set<Integer> channels = new HashSet<>();
//        channels.add(1); // 默认渠道
//        unionMessageSendArg.setChannels(channels);

        return unionMessageSendArg;
    }

}