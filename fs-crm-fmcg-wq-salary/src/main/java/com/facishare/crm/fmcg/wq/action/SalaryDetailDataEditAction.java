package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryDataFields;
import com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields;
import com.facishare.crm.fmcg.wq.dao.SalaryDataDao;
import com.facishare.crm.fmcg.wq.dao.SalaryDetailDataDao;
import com.facishare.crm.fmcg.wq.util.FieldValidationUtil;
import com.facishare.crm.fmcg.wq.util.SalaryAmountCalculator;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 工资条明细编辑Action
 *
 * 功能：
 * 1. 允许修改工资条明细的金额
 * 2. 自动将发放状态改为"已修正"
 * 3. 同步更新工资条总金额
 * 4. 限制其他关键字段的修改
 */
@Slf4j
public class SalaryDetailDataEditAction extends StandardEditAction {

    private SalaryDetailDataDao salaryDetailDataDao = SpringUtil.getContext().getBean(SalaryDetailDataDao.class);
    private SalaryDataDao salaryDataDao = SpringUtil.getContext().getBean(SalaryDataDao.class);

    /**
     * 允许修改的字段列表（只有金额字段）
     */
    private static final java.util.Set<String> ALLOWED_EDIT_FIELDS = new java.util.HashSet<String>() {{
        add(SalaryDetailDataFields.AMOUNT);
    }};

    /**
     * 系统字段，不需要验证（这些字段由系统自动管理）
     */
    private static final java.util.Set<String> SYSTEM_FIELDS = new java.util.HashSet<String>() {{
        add("id");
        add("created_by");
        add("created_time");
        add("updated_by");
        add("updated_time");
        add("owner");
        add("out_owner");
        add("tenant_id");
        add("version");
    }};

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        String tenantId = actionContext.getTenantId();
        String salaryDetailDataId = arg.getObjectData().getId();

        log.info("开始处理工资条明细编辑，明细ID: {}", salaryDetailDataId);


        // 验证编辑权限和数据
        validateEditData(arg, dbMasterData);

        // 检查是否修改了金额
        Object newAmountObj = arg.getObjectData().get(SalaryDetailDataFields.AMOUNT);
        Object originalAmountObj = dbMasterData.get(SalaryDetailDataFields.AMOUNT);

        BigDecimal newAmount = parseAmount(newAmountObj);
        BigDecimal originalAmount = parseAmount(originalAmountObj);

        if (newAmount != null && !isAmountEqual(newAmount, originalAmount)) {
            // 金额发生变化，自动设置状态为已修正
            arg.getObjectData().put(SalaryDetailDataFields.DISTRIBUTION_STATUS,
                SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_4);
            log.info("工资条明细金额发生变化，自动设置状态为已修正，明细ID: {}, 原金额: {}, 新金额: {}",
                salaryDetailDataId, originalAmount, newAmount);
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result afterResult = super.after(arg, result);

        // 如果编辑成功，同步更新工资条总金额
        if (result.getObjectData() != null) {
            String tenantId = actionContext.getTenantId();
            IObjectData updatedData = result.getObjectData().toObjectData();
            syncRelatedSalaryData(tenantId, updatedData);
        }

        return afterResult;
    }

    /**
     * 验证编辑数据
     * @param arg 编辑参数
     * @param originalData 原始数据
     */
    private void validateEditData(Arg arg, IObjectData originalData) {
        // 验证发放状态 - 已发放的数据不能修改
        String distributionStatus = originalData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);
        if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3.equals(distributionStatus)) {
            throw new ValidateException("已发放的工资条明细不能修改"); //ignoreI18n
        }

        // 验证只有金额字段可以修改，其他字段都不能修改
        validateOnlyAmountFieldCanBeModified(arg, originalData);

        // 验证金额
        Object newAmountObj = arg.getObjectData().get(SalaryDetailDataFields.AMOUNT);
        if (newAmountObj != null) {
            BigDecimal newAmount = parseAmount(newAmountObj);
            validateAmount(newAmount);
        }

        log.info("编辑数据验证通过，当前发放状态: {}", distributionStatus);
    }

    /**
     * 验证只有金额字段可以修改，其他字段都不能修改
     */
    private void validateOnlyAmountFieldCanBeModified(Arg arg, IObjectData originalData) {
        // 获取工资条明细对象的所有字段
        java.util.Set<String> allFieldNames = getAllFieldNames();

        // 检查每个字段是否被修改
        for (String fieldName : allFieldNames) {
            // 跳过系统字段
            if (SYSTEM_FIELDS.contains(fieldName)) {
                continue;
            }

            // 跳过允许修改的字段
            if (ALLOWED_EDIT_FIELDS.contains(fieldName)) {
                continue;
            }

            // 对于不允许修改的字段，使用工具类验证
            Object newValue = arg.getObjectData().get(fieldName);
            Object originalValue = originalData.get(fieldName);

            String reason = "工资条明细编辑时只允许修改金额字段"; // ignoreI18n
            FieldValidationUtil.validateFieldNotChanged(objectDescribe, fieldName, newValue, originalValue, reason);
        }

        log.info("字段修改验证通过，只有允许的字段被修改");
    }

    /**
     * 获取工资条明细对象的所有字段名
     */
    private java.util.Set<String> getAllFieldNames() {
        java.util.Set<String> fieldNames = new java.util.HashSet<>();

        try {
            // 通过反射获取所有字段常量
            java.lang.reflect.Field[] fields = SalaryDetailDataFields.class.getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) &&
                    java.lang.reflect.Modifier.isFinal(field.getModifiers()) &&
                    field.getType() == String.class) {
                    try {
                        String fieldName = (String) field.get(null);
                        if (fieldName != null && !fieldName.equals(SalaryDetailDataFields.API_NAME)) {
                            fieldNames.add(fieldName);
                        }
                    } catch (IllegalAccessException e) {
                        log.warn("无法访问字段: {}", field.getName());
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取字段名列表失败", e);
            // 如果反射失败，使用硬编码的字段列表作为备选方案
            fieldNames.addAll(java.util.Arrays.asList(
                SalaryDetailDataFields.AMOUNT,
                SalaryDetailDataFields.SALARY_DATA,
                SalaryDetailDataFields.SALARY_ITEM,
                SalaryDetailDataFields.SALARY_RULE,
                SalaryDetailDataFields.EMPLOYEE,
                SalaryDetailDataFields.EMPLOYEE_EXTERNAL,
                SalaryDetailDataFields.VALUE_TYPE,
                SalaryDetailDataFields.CALCULATION_FORMULA,
                SalaryDetailDataFields.FORMULA_ASSIGNMENT,
                SalaryDetailDataFields.DISTRIBUTION_STATUS,
                SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB
            ));
        }

        log.debug("获取到字段名列表，共{}个字段", fieldNames.size());
        return fieldNames;
    }

    /**
     * 验证金额
     */
    private void validateAmount(BigDecimal amount) {
        if (amount == null) {
            return; // 允许为空
        }

        // 使用工具类验证金额
        if (!SalaryAmountCalculator.isValidAmount(amount)) {
            throw new ValidateException("金额不能为负数"); //ignoreI18n
        }

        // 金额精度检查（最多3位小数）
        if (amount.scale() > SalaryAmountCalculator.getUnifiedScale()) {
            throw new ValidateException("金额最多保留3位小数"); //ignoreI18n
        }
    }

    /**
     * 解析金额
     */
    private BigDecimal parseAmount(Object amountObj) {
        if (amountObj == null) {
            return null;
        }

        if (amountObj instanceof BigDecimal) {
            return (BigDecimal) amountObj;
        }

        if (amountObj instanceof String) {
            String amountStr = (String) amountObj;
            if (StringUtils.isBlank(amountStr)) {
                return null;
            }
            BigDecimal amount = SalaryAmountCalculator.validateAndFormatAmount(amountStr);
            if (amount == null) {
                throw new ValidateException("金额格式不正确: " + amountStr); //ignoreI18n
            }
            return amount;
        }

        throw new ValidateException("不支持的金额类型: " + amountObj.getClass().getSimpleName()); //ignoreI18n
    }

    /**
     * 比较两个金额是否相等
     */
    private boolean isAmountEqual(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null && amount2 == null) {
            return true;
        }
        if (amount1 == null || amount2 == null) {
            return false;
        }
        return amount1.compareTo(amount2) == 0;
    }

    /**
     * 同步相关的工资条对象
     * 当工资条明细修改后，需要重新计算工资条的总金额
     *
     * @param tenantId 租户ID
     * @param salaryDetailData 修改后的工资条明细
     */
    private void syncRelatedSalaryData(String tenantId, IObjectData salaryDetailData) {
        try {
            // 获取工资条ID
            String salaryDataId = salaryDetailData.get(SalaryDetailDataFields.SALARY_DATA, String.class);

            if (StringUtils.isBlank(salaryDataId)) {
                log.info("工资条明细未关联工资条，跳过同步，明细ID: {}", salaryDetailData.getId());
                return;
            }

            // 获取工资条对象
            IObjectData salaryData = salaryDataDao.getById(tenantId, salaryDataId);
            if (salaryData == null) {
                log.warn("未找到关联的工资条，工资条ID: {}", salaryDataId);
                return;
            }

            log.info("开始同步工资条，工资条ID: {}", salaryDataId);

            // 重新计算工资条的总金额
            BigDecimal totalAmount = calculateSalaryDataTotalAmount(tenantId, salaryDataId);

            // 更新工资条的总金额（使用统一的3位小数格式）
            salaryData.set(SalaryDataFields.PAYABLE_SALARY, SalaryAmountCalculator.formatAmount(totalAmount));

            // 保存工资条
            salaryDataDao.updateSalaryData(User.systemUser(tenantId), salaryData);

            log.info("工资条同步完成，工资条ID: {}, 新总金额: {}", salaryDataId, totalAmount);

        } catch (Exception e) {
            log.error("同步工资条失败，明细ID: {}", salaryDetailData.getId(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 计算工资条的总金额
     * 通过汇总所有相关工资条明细的金额
     *
     * @param tenantId 租户ID
     * @param salaryDataId 工资条ID
     * @return 总金额
     */
    private BigDecimal calculateSalaryDataTotalAmount(String tenantId, String salaryDataId) {
        try {
            // 查询该工资条下的所有明细
            List<IObjectData> detailDataList = salaryDetailDataDao.getAllIObjectDataListByQueryWithFields(
                User.systemUser(tenantId),
                com.facishare.crm.fmcg.wq.util.SearchQuery.builder()
                    .eq(SalaryDetailDataFields.SALARY_DATA, salaryDataId)
                    .build(),
                SalaryDetailDataFields.API_NAME,
                null
            );

            // 使用工具类计算总金额
            BigDecimal totalAmount = SalaryAmountCalculator.calculatePayableSalary( detailDataList);

            log.info("计算工资条总金额完成，工资条ID: {}, 明细数量: {}, 总金额: {}",
                    salaryDataId, detailDataList.size(), totalAmount);

            return totalAmount;

        } catch (Exception e) {
            log.error("计算工资条总金额失败，工资条ID: {}", salaryDataId, e);
            return BigDecimal.ZERO;
        }
    }
}
