package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDetailDao;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.util.SalaryItemUsageValidator;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工资项批量作废Action
 * 
 * 作废规则：
 * 1. 当工资项禁用或启用后未被工资规则或员工固定工资表使用时，允许作废
 * 2. 当工资项被工资规则或员工固定工资表使用后，不可作废
 * 3. 批量作废时，如果有任何一个工资项被使用，则整个批量操作失败
 */
@Slf4j
public class SalaryItemBulkInvalidAction extends StandardBulkInvalidAction {

    private SalaryRuleDao salaryRuleDao = SpringUtil.getContext().getBean(SalaryRuleDao.class);
    private SalaryItemDao salaryItemDao = SpringUtil.getContext().getBean(SalaryItemDao.class);
    private EmployeeFixedSalaryDetailDao employeeFixedSalaryDetailDao = SpringUtil.getContext()
            .getBean(EmployeeFixedSalaryDetailDao.class);

    @Override
    protected void before(Arg arg) {
        String tenantId = actionContext.getTenantId();
        List<String> salaryItemIds = arg.getDataIds();
        
        log.info("开始处理工资项批量作废，工资项数量: {}", salaryItemIds.size());
        
        // 检查所有工资项的使用状态
        validateBulkInvalidPermissions(tenantId, salaryItemIds);
        
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        
        log.info("工资项批量作废成功，作废数量: {}", arg.getDataList().size());
        
        return after;
    }

    /**
     * 验证批量作废权限
     * @param tenantId 租户ID
     * @param salaryItemIds 工资项ID列表
     */
    private void validateBulkInvalidPermissions(String tenantId, List<String> salaryItemIds) {
        if (CollectionUtils.isEmpty(salaryItemIds)) {
            throw new ValidateException("请选择要作废的工资项"); //ignoreI18n
        }

        // 获取所有工资项数据
        List<IObjectData> salaryItems = salaryItemDao.getByIds(tenantId, salaryItemIds);

        if (CollectionUtils.isEmpty(salaryItems)) {
            throw new ValidateException("未找到要作废的工资项"); //ignoreI18n
        }

        if (salaryItems.size() != salaryItemIds.size()) {
            throw new ValidateException("部分工资项不存在"); //ignoreI18n
        }

        // 批量检查工资项使用情况（性能优化：避免N+1查询）
        Map<String, List<IObjectData>> salaryRuleUsageMap = batchCheckSalaryRuleUsage(tenantId, salaryItemIds);
        Map<String, List<IObjectData>> employeeFixedSalaryUsageMap = batchCheckEmployeeFixedSalaryUsage(tenantId, salaryItemIds);

        // 检查每个工资项的使用情况
        StringBuilder usedItems = new StringBuilder();

        for (IObjectData salaryItem : salaryItems) {
            String salaryItemId = salaryItem.getId();
            String salaryItemName = salaryItem.getName();

            List<IObjectData> usingSalaryRules = salaryRuleUsageMap.getOrDefault(salaryItemId, Lists.newArrayList());
            List<IObjectData> usingEmployeeFixedSalaryDetails = employeeFixedSalaryUsageMap.getOrDefault(salaryItemId, Lists.newArrayList());

            boolean isUsedBySalaryRules = CollectionUtils.isNotEmpty(usingSalaryRules);
            boolean isUsedByEmployeeFixedSalary = CollectionUtils.isNotEmpty(usingEmployeeFixedSalaryDetails);

            if (isUsedBySalaryRules || isUsedByEmployeeFixedSalary) {
                if (usedItems.length() > 0) {
                    usedItems.append("、");
                }
                usedItems.append(salaryItemName);

                // 构建详细的使用信息
                String usageMessage = SalaryItemUsageValidator.buildUsageMessage(usingSalaryRules, usingEmployeeFixedSalaryDetails);
                log.warn("工资项 {} ({}) 被以下对象使用: {}", salaryItemName, salaryItemId, usageMessage);
            }
        }

        // 如果有被使用的工资项，抛出异常
        if (usedItems.length() > 0) {
            String errorMessage = String.format("以下工资项已被使用，不可作废：%s", usedItems.toString()); //ignoreI18n
            throw new ValidateException(errorMessage); //ignoreI18n
        }

        log.info("所有工资项均未被使用，允许批量作废");
    }

    /**
     * 批量检查工资规则使用情况
     * @param tenantId 租户ID
     * @param salaryItemIds 工资项ID列表
     * @return 工资项ID -> 使用该工资项的工资规则列表的映射
     */
    private Map<String, List<IObjectData>> batchCheckSalaryRuleUsage(String tenantId, List<String> salaryItemIds) {
        Map<String, List<IObjectData>> usageMap = new HashMap<>();

        // 使用现有的单个查询方法，但可以考虑后续优化为批量查询
        for (String salaryItemId : salaryItemIds) {
            List<IObjectData> usingSalaryRules = salaryRuleDao.getSalaryRulesBySalaryItemId(tenantId, salaryItemId);
            if (CollectionUtils.isNotEmpty(usingSalaryRules)) {
                usageMap.put(salaryItemId, usingSalaryRules);
            }
        }

        return usageMap;
    }

    /**
     * 批量检查员工固定工资表使用情况
     * @param tenantId 租户ID
     * @param salaryItemIds 工资项ID列表
     * @return 工资项ID -> 使用该工资项的员工固定工资明细列表的映射
     */
    private Map<String, List<IObjectData>> batchCheckEmployeeFixedSalaryUsage(String tenantId, List<String> salaryItemIds) {
        Map<String, List<IObjectData>> usageMap = new HashMap<>();

        // 使用现有的单个查询方法，但可以考虑后续优化为批量查询
        for (String salaryItemId : salaryItemIds) {
            List<IObjectData> usingDetails = employeeFixedSalaryDetailDao.getBySalaryItemId(tenantId, salaryItemId);
            if (CollectionUtils.isNotEmpty(usingDetails)) {
                usageMap.put(salaryItemId, usingDetails);
            }
        }

        return usageMap;
    }


}
