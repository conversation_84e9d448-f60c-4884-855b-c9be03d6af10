package com.facishare.crm.fmcg.wq.util;

import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 薪资规则计算公式工具类
 * 提供计算公式构建的公共方法
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class SalaryRuleFormulaUtil {

  /**
   * 根据工资项构建计算公式
   * 按照增减属性将工资项名称连接起来，格式如：基本工资 + 绩效奖金 - 社保扣款 - 个税
   * 
   * @param tenantId 租户ID
   * @param salaryRuleExt 工资规则扩展对象
   * @param salaryItemDao 工资项DAO
   * @return 构建的计算公式字符串，如果无法构建则返回null
   */
  public static String buildCalculationFormulaFromSalaryItems(String tenantId, ObjectDataExt salaryRuleExt, SalaryItemDao salaryItemDao) {
    try {
      // 获取工资项ID列表
      List<String> salaryItemIds = getSalaryItemIds(salaryRuleExt);
      if (CollectionUtils.isEmpty(salaryItemIds)) {
        log.debug("工资规则未配置工资项，无法构建计算公式");
        return null;
      }

      // 查询工资项详情
      List<IObjectData> salaryItems = salaryItemDao.getbyIds(tenantId, salaryItemIds);
      if (CollectionUtils.isEmpty(salaryItems)) {
        log.warn("未找到工资项详情，工资项ID列表: {}", salaryItemIds);
        return null;
      }

      // 按照工资项ID的顺序构建公式
      return buildFormulaString(salaryItems, salaryItemIds);

    } catch (Exception e) {
      log.error("构建计算公式时发生异常", e);
      return null;
    }
  }

  /**
   * 从工资规则中获取工资项ID列表
   * 
   * @param salaryRuleExt 工资规则扩展对象
   * @return 工资项ID列表
   */
  private static List<String> getSalaryItemIds(ObjectDataExt salaryRuleExt) {
    Object salaryItemsObj = salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM);
    if (salaryItemsObj == null) {
      return new ArrayList<>();
    }

    List<String> salaryItemIds = new ArrayList<>();

    // 处理不同的数据类型
    if (salaryItemsObj instanceof List) {
      // 如果是List类型，直接转换
      List<?> itemList = (List<?>) salaryItemsObj;
      for (Object item : itemList) {
        if (item != null) {
          salaryItemIds.add(item.toString());
        }
      }
    } else if (salaryItemsObj instanceof String[]) {
      // 如果是数组类型，转换为List
      String[] itemArray = (String[]) salaryItemsObj;
      for (String item : itemArray) {
        if (StringUtils.isNotBlank(item)) {
          salaryItemIds.add(item);
        }
      }
    } else {
      // 如果是字符串类型，按逗号分割
      String itemsStr = salaryItemsObj.toString();
      if (StringUtils.isNotBlank(itemsStr)) {
        String[] itemArray = itemsStr.split(",");
        for (String item : itemArray) {
          if (StringUtils.isNotBlank(item.trim())) {
            salaryItemIds.add(item.trim());
          }
        }
      }
    }

    return salaryItemIds;
  }

  /**
   * 构建公式字符串
   * 按照工资项ID的顺序，根据增减属性连接工资项名称
   * 
   * @param salaryItems 工资项列表
   * @param salaryItemIds 工资项ID顺序列表
   * @return 构建的公式字符串
   */
  private static String buildFormulaString(List<IObjectData> salaryItems, List<String> salaryItemIds) {
    // 创建工资项ID到工资项对象的映射
    Map<String, IObjectData> salaryItemMap = new HashMap<>();
    for (IObjectData salaryItem : salaryItems) {
      salaryItemMap.put(salaryItem.getId(), salaryItem);
    }

    StringBuilder formulaBuilder = new StringBuilder();
    boolean isFirst = true;

    // 按照工资项ID的顺序构建公式
    for (String salaryItemId : salaryItemIds) {
      IObjectData salaryItem = salaryItemMap.get(salaryItemId);
      if (salaryItem == null) {
        log.warn("未找到工资项，ID: {}", salaryItemId);
        continue;
      }

      String salaryItemName = salaryItem.getName();
      if (StringUtils.isBlank(salaryItemName)) {
        log.warn("工资项名称为空，ID: {}", salaryItemId);
        continue;
      }

      // 获取增减属性
      String incrementDecrementAttrib = salaryItem.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class);
      
      // 确定符号
      String operator = getOperatorByAttribute(incrementDecrementAttrib, isFirst);
      
      // 构建公式片段
      if (isFirst) {
        // 第一个工资项：如果是扣减项需要加减号，应发项不需要符号
        if (SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
          formulaBuilder.append("-").append(salaryItemName);
        } else {
          formulaBuilder.append(salaryItemName);
        }
        isFirst = false;
      } else {
        formulaBuilder.append(" ").append(operator).append(" ").append(salaryItemName);
      }
    }

    String formula = formulaBuilder.toString();
    log.debug("构建的计算公式: {}", formula);
    return formula;
  }

  /**
   * 根据增减属性获取操作符
   * 注意：此方法仅用于非第一个工资项，第一个工资项的符号处理在buildFormulaString中单独处理
   *
   * @param incrementDecrementAttrib 增减属性
   * @param isFirst 是否是第一个工资项（此方法中应该总是false）
   * @return 操作符（+或-）
   */
  private static String getOperatorByAttribute(String incrementDecrementAttrib, boolean isFirst) {
    // 此方法仅用于非第一个工资项
    if (isFirst) {
      return "";
    }

    // 根据增减属性确定操作符
    if (SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1.equals(incrementDecrementAttrib)) {
      // 应发项，使用加号
      return "+";
    } else if (SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
      // 扣减项，使用减号
      return "-";
    } else {
      // 未知属性，默认使用加号
      log.warn("未知的增减属性: {}", incrementDecrementAttrib);
      return "+";
    }
  }
}
