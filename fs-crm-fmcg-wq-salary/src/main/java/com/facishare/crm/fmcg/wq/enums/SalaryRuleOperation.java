package com.facishare.crm.fmcg.wq.enums;

import lombok.Getter;

/**
 * 薪资规则操作类型枚举
 * 
 * <AUTHOR>
 * @create 2024-06-23
 */
@Getter
public enum SalaryRuleOperation {
    
    /**
     * 添加操作：为员工添加新的薪资规则（如果原来没有规则）
     */
    ADD("ADD", "添加"), // ignoreI18n
    
    /**
     * 移除操作：移除员工的薪资规则关联
     */
    REMOVE("REMOVE", "移除"); // ignoreI18n
    
    /**
     * 操作代码
     */
    private final String code;
    
    /**
     * 操作描述
     */
    private final String description;
    
    SalaryRuleOperation(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举值
     * 
     * @param code 操作代码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果代码不存在
     */
    public static SalaryRuleOperation fromCode(String code) {
        for (SalaryRuleOperation operation : values()) {
            if (operation.code.equals(code)) {
                return operation;
            }
        }
        throw new IllegalArgumentException("Unknown operation code: " + code);
    }
    
    /**
     * 检查代码是否有效
     * 
     * @param code 操作代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        try {
            fromCode(code);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
