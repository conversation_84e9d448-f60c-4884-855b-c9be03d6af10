package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Date;
import java.util.Calendar;
import java.util.ArrayList;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class SalaryRuleDao extends AbstractDao {
    final static List<String> salaryRuleFields = ConfigUtils.getFields(SalaryRuleFields.class);

    public List<IObjectData> getbyIds(String tenantId, List<String> salaryRuleIds) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), salaryRuleIds)
                .build(), SalaryRuleFields.API_NAME, salaryRuleFields);
    }

    public IObjectData getById(String tenantId, String salaryRuleId) {
        return getById(tenantId, SalaryRuleFields.API_NAME, salaryRuleId);
    }

    /**
     * 根据发放时间类型和日期查询工资规则
     * 
     * @param tenantId             租户ID
     * @param distributionTimeType 发放时间类型（1-次日，2-次周，3-次月）
     * @param date                 参考日期
     * @return 符合条件的工资规则列表
     */
    public List<IObjectData> queryRulesByDistributionTimeAndDate(String tenantId, String distributionTimeType,
            Date date) {
        if (date == null) {
            date = new Date();
        }

        // 创建日历实例
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 获取当日的开始时间（00:00:00）用于生效时间过滤
        LocalDate currentDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        long currentDayStart = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        SearchQuery.SearchQueryBuilder queryBuilder = SearchQuery.builder()
                .eq(SalaryRuleFields.DISTRIBUTION_TIME, distributionTimeType)
                .eq(SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE,
                        SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE_Options_true)
                .lt(SalaryRuleFields.EFFECTIVE_DATE, currentDayStart);

        // 根据不同的发放时间类型添加特定条件
        if (SalaryRuleFields.DISTRIBUTION_TIME_Options_2.equals(distributionTimeType)) {
            // 次周规则 - 需要匹配周几
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            // 转成中国常用周表示法（0-6，周日到周六）
            if (dayOfWeek == Calendar.SUNDAY) {
                dayOfWeek = 0;
            } else {
                dayOfWeek -= 1;
            }
            queryBuilder.eq(SalaryRuleFields.NEXT_WEEK_DISTRIBUTION_OP, String.valueOf(dayOfWeek));
        } else if (SalaryRuleFields.DISTRIBUTION_TIME_Options_3.equals(distributionTimeType)) {
            // 次月规则 - 需要匹配日期
            int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
            queryBuilder.eq(SalaryRuleFields.NEXT_MONTH_DISTRIBUTION_OP, String.valueOf(dayOfMonth));
        }

        return getAllIObjectDataListByQueryWithFields(
                User.systemUser(tenantId),
                queryBuilder.build(),
                SalaryRuleFields.API_NAME,
                salaryRuleFields);
    }

    /**
     * 查询月末范围内的次月发放规则
     *
     * @param tenantId 租户ID
     * @param startDay 开始日期（当前日期）
     * @param endDay   结束日期（月末）
     * @param date     参考日期，用于生效时间过滤
     * @return 符合条件的工资规则列表
     */
    public List<IObjectData> queryMonthEndRules(String tenantId, int startDay, int endDay, Date date) {
        // 创建日期范围列表
        List<String> dayValues = new ArrayList<>();
        for (int day = startDay; day <= endDay; day++) {
            dayValues.add(String.valueOf(day));
        }

        // 获取当日的开始时间（00:00:00）用于生效时间过滤
        LocalDate currentDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        long currentDayStart = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        // 构建查询条件，使用in操作符一次性查询所有符合条件的规则
        SearchQuery query = SearchQuery.builder()
                .eq(SalaryRuleFields.DISTRIBUTION_TIME, SalaryRuleFields.DISTRIBUTION_TIME_Options_3)
                .eq(SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE,
                        SalaryRuleFields.AUTO_CREATED_PAYROLLVOUCHE_Options_true)
                .in(SalaryRuleFields.NEXT_MONTH_DISTRIBUTION_OP, dayValues)
                .lt(SalaryRuleFields.EFFECTIVE_DATE, currentDayStart)
                .build();

        // 执行查询
        return getAllIObjectDataListByQueryWithFields(
                User.systemUser(tenantId),
                query,
                SalaryRuleFields.API_NAME,
                salaryRuleFields);
    }

    /**
     * 根据日期查询所有应该生成工资发放单的规则
     *
     * @param tenantId 租户ID
     * @param date     参考日期
     * @return 符合条件的工资规则列表
     */
    public List<IObjectData> queryRulesByDate(String tenantId, Date date) {
        if (date == null) {
            date = new Date();
        }

        List<IObjectData> result = new ArrayList<>();

        // 1. 查询次日规则
        List<IObjectData> nextDayRules = queryRulesByDistributionTimeAndDate(
                tenantId,
                SalaryRuleFields.DISTRIBUTION_TIME_Options_1,
                date);
        if (nextDayRules != null && !nextDayRules.isEmpty()) {
            result.addAll(nextDayRules);
        }

        // 2. 查询次周规则
        List<IObjectData> nextWeekRules = queryRulesByDistributionTimeAndDate(
                tenantId,
                SalaryRuleFields.DISTRIBUTION_TIME_Options_2,
                date);
        if (nextWeekRules != null && !nextWeekRules.isEmpty()) {
            result.addAll(nextWeekRules);
        }

        // 3. 查询次月规则
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

        if (dayOfMonth == lastDay) {
            // 如果是月末，查询所有从当前日期到月底的次月规则
            List<IObjectData> monthEndRules = queryMonthEndRules(tenantId, dayOfMonth, 31, date);
            if (monthEndRules != null && !monthEndRules.isEmpty()) {
                result.addAll(monthEndRules);
            }
        } else {
            // 如果不是月末，只查询当前日期的次月规则
            List<IObjectData> nextMonthRules = queryRulesByDistributionTimeAndDate(
                    tenantId,
                    SalaryRuleFields.DISTRIBUTION_TIME_Options_3,
                    date);
            if (nextMonthRules != null && !nextMonthRules.isEmpty()) {
                result.addAll(nextMonthRules);
            }
        }

        return result;
    }

    /**
     * 根据日期查询特定条件的工资规则（次日、周一的次周、1日的次月）
     * 不区分是否自动创建发放单
     *
     * @param tenantId 租户ID
     * @param date     参考日期
     * @return 符合条件的工资规则列表
     */
    public List<IObjectData> queryRulesByDateForDetail(String tenantId, Date date) {
        if (date == null) {
            date = new Date();
        }

        List<IObjectData> result = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 获取当日的开始时间（00:00:00）用于生效时间过滤
        LocalDate currentDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        long currentDayStart = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

        // 1. 查询所有次日规则
        SearchQuery nextDayQuery = SearchQuery.builder()
                .eq(SalaryRuleFields.SALARY_METHOD, SalaryRuleFields.SALARY_METHOD_Options_1)
                .lt(SalaryRuleFields.EFFECTIVE_DATE, currentDayStart)
                .build();

        List<IObjectData> nextDayRules = getAllIObjectDataListByQueryWithFields(
                User.systemUser(tenantId),
                nextDayQuery,
                SalaryRuleFields.API_NAME,
                salaryRuleFields);

        if (nextDayRules != null && !nextDayRules.isEmpty()) {
            result.addAll(nextDayRules);
        }

        // 2. 如果当前是周一，查询所有次周规则
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == Calendar.MONDAY) {
            SearchQuery nextWeekQuery = SearchQuery.builder()
                    .eq(SalaryRuleFields.SALARY_METHOD, SalaryRuleFields.SALARY_METHOD_Options_2)
                    .lt(SalaryRuleFields.EFFECTIVE_DATE, currentDayStart)
                    .build();

            List<IObjectData> nextWeekRules = getAllIObjectDataListByQueryWithFields(
                    User.systemUser(tenantId),
                    nextWeekQuery,
                    SalaryRuleFields.API_NAME,
                    salaryRuleFields);

            if (nextWeekRules != null && !nextWeekRules.isEmpty()) {
                result.addAll(nextWeekRules);
            }
        }

        // 3. 如果当前是1日，查询所有次月规则
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        if (dayOfMonth == 1) {
            SearchQuery nextMonthQuery = SearchQuery.builder()
                    .eq(SalaryRuleFields.SALARY_METHOD, SalaryRuleFields.SALARY_METHOD_Options_3)
                    .lt(SalaryRuleFields.EFFECTIVE_DATE, currentDayStart)
                    .build();

            List<IObjectData> nextMonthRules = getAllIObjectDataListByQueryWithFields(
                    User.systemUser(tenantId),
                    nextMonthQuery,
                    SalaryRuleFields.API_NAME,
                    salaryRuleFields);

            if (nextMonthRules != null && !nextMonthRules.isEmpty()) {
                result.addAll(nextMonthRules);
            }
        }

        return result;
    }

    /**
     * 根据工资项ID查询使用该工资项的工资规则
     * 
     * @param tenantId     租户ID
     * @param salaryItemId 工资项ID
     * @return 使用该工资项的工资规则列表
     */
    public List<IObjectData> getSalaryRulesBySalaryItemId(String tenantId, String salaryItemId) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .hasAnyOf(SalaryRuleFields.SALARY_ITEM, Lists.newArrayList(salaryItemId))
                .build(), SalaryRuleFields.API_NAME, salaryRuleFields);
    }

    public List<IObjectData> getByIds(String tenantId, List<String> salaryRuleIds) {
        return getByIds(tenantId,SalaryRuleFields.API_NAME, salaryRuleIds);
    }

    /**
     * 查询所有有效的工资规则（跨租户）
     * 用于定时任务扫描所有租户的工资规则
     *
     * @return 所有有效的工资规则列表
     */
    public List<IObjectData> getAllActiveSalaryRules() {
        try {
            // 构建查询条件：查询所有工资规则（不过滤状态，因为SalaryRuleFields中没有STATUS字段）
            // 假设所有存在的工资规则都是有效的，或者根据生效日期来判断
            SearchQuery query = SearchQuery.builder()
                    .exist(SalaryRuleFields.EFFECTIVE_DATE) // 存在生效日期的规则
                    .build();

            // 使用系统用户查询所有租户的数据
            // 注意：这里需要传入一个默认的租户ID，因为 User.systemUser() 需要租户ID参数
            // 在实际使用中，可能需要遍历所有租户或使用特殊的跨租户查询方法
            return getAllIObjectDataListByQueryWithFields(
                    User.systemUser("system"), // 使用系统租户
                    query,
                    SalaryRuleFields.API_NAME,
                    salaryRuleFields);
        } catch (Exception e) {
            log.error("查询所有有效工资规则失败", e);
            throw e;
        }
    }

}
