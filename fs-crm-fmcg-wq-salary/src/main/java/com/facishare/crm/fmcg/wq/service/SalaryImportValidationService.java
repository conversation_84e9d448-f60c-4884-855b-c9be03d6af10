package com.facishare.crm.fmcg.wq.service;

import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryDetailFields;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.util.SalaryRecordTypeUtil;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction.ImportData;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.facishare.paas.appframework.core.predef.action.BaseImportAction.ImportError;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 薪资导入验证服务
 * 
 * 提供公共的验证逻辑，减少代码重复
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
@Service
public class SalaryImportValidationService {

  @Autowired
  private EmployeeFixedSalaryDao employeeFixedSalaryDao;
  
  @Autowired
  private SalaryRuleDao salaryRuleDao;
  
  @Autowired
  private SalaryService salaryService;

  /**
   * 员工唯一性校验
   */
  public List<ImportError> validateEmployeeUniqueness(List<ImportData> dataList,
                                                     Map<String, IObjectData> existingEmployeeRecords) {
    List<ImportError> errorList = Lists.newArrayList();
    Set<String> importEmployeeIds = Sets.newHashSet();

    for (ImportData data : dataList) {
      IObjectData objectData = data.getData();

      String internalEmployeeId = getStringValue(objectData, EmployeeFixedSalaryFields.EMPLOYEE);
      String externalEmployeeId = getStringValue(objectData, EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);

      // 1. 校验不能同时设置内部员工和外部员工
      if (StringUtils.isNotBlank(internalEmployeeId) && StringUtils.isNotBlank(externalEmployeeId)) {
        errorList.add(new ImportError(data.getRowNo(), "不能同时设置内部员工和外部员工")); //ignoreI18n
        continue;
      }

      // 2. 根据业务类型判断员工字段必填验证
      if (StringUtils.isBlank(internalEmployeeId) && StringUtils.isBlank(externalEmployeeId)) {
        errorList.add(new ImportError(data.getRowNo(), "请选择员工（内部员工或外部员工）")); //ignoreI18n
        continue;
      }

      String employeeId = StringUtils.isNotBlank(internalEmployeeId) ? internalEmployeeId : externalEmployeeId;

      // 3. 检查数据库中是否已存在该员工的记录
      if (existingEmployeeRecords.containsKey(employeeId)) {
        String employeeType = StringUtils.isNotBlank(internalEmployeeId) ? "内部员工" : "外部员工"; //ignoreI18n
        errorList.add(new ImportError(data.getRowNo(), 
            MessageFormat.format("该{0}已存在固定薪资记录，不能重复创建", employeeType))); //ignoreI18n
        continue;
      }

      // 4. 检查导入数据中是否有重复的员工
      if (importEmployeeIds.contains(employeeId)) {
        String employeeType = StringUtils.isNotBlank(internalEmployeeId) ? "内部员工" : "外部员工"; //ignoreI18n
        errorList.add(new ImportError(data.getRowNo(), 
            MessageFormat.format("导入数据中该{0}重复，请检查", employeeType))); //ignoreI18n
        continue;
      }

      importEmployeeIds.add(employeeId);
    }

    log.info("员工唯一性校验完成，错误数: {}", errorList.size());
    return errorList;
  }

  /**
   * 薪资规则验证
   */
  public List<ImportError> validateSalaryRules(List<ImportData> dataList, String tenantId) {
    List<ImportError> errorList = Lists.newArrayList();

    for (ImportData data : dataList) {
      IObjectData objectData = data.getData();

      // 获取定薪方式
      String salaryMethod = getStringValue(objectData, EmployeeFixedSalaryFields.SALARY_METHOD);
      if (StringUtils.isBlank(salaryMethod)) {
        errorList.add(new ImportError(data.getRowNo(), "请选择定薪方式")); //ignoreI18n
        continue;
      }

      // 验证薪资规则
      String salaryRuleId = objectData.get(EmployeeFixedSalaryFields.SALARY_RULE,String.class);
      if (StringUtils.isNotBlank(salaryRuleId)) {
        try {
          // 获取薪资规则对象
          IObjectData salaryRuleObj = salaryRuleDao.getById(tenantId, salaryRuleId.toString());
          if (salaryRuleObj == null) {
            errorList.add(new ImportError(data.getRowNo(), "薪资规则不存在")); //ignoreI18n
            continue;
          }

          // 1. 验证定薪方式与规则的定薪方式一致（首要条件）
          String ruleSalaryMethod = getStringValue(salaryRuleObj, SalaryRuleFields.SALARY_METHOD);
          if (!salaryMethod.equals(ruleSalaryMethod)) {
            errorList.add(new ImportError(data.getRowNo(),
                MessageFormat.format("定薪方式({0})与薪资规则的定薪方式({1})不一致", //ignoreI18n
                    salaryMethod, ruleSalaryMethod)));
            continue;
          }

          // 2. 验证选择的规则是否在适用范围内
          List<IObjectData> applicableRuleObjs = salaryService.getApplicableSalaryRuleIdsByEmployeeFixedSalary(
              tenantId, objectData);
          List<String> applicableRuleIds = applicableRuleObjs.stream()
              .map(IObjectData::getId)
              .collect(Collectors.toList());

          if (CollectionUtils.isNotEmpty(applicableRuleIds)) {
            if (!applicableRuleIds.contains(salaryRuleId.toString())) {
              errorList.add(new ImportError(data.getRowNo(), "选择的薪资规则不在适用范围内")); //ignoreI18n
              continue;
            }
          } else {
            errorList.add(new ImportError(data.getRowNo(), "当前员工不符合任何薪资规则的适用范围")); //ignoreI18n
            continue;
          }

          log.debug("薪资规则验证通过，员工: {}, 规则ID: {}, 定薪方式: {}", 
              getEmployeeDisplayName(objectData), salaryRuleId, salaryMethod);

        } catch (Exception e) {
          log.error("验证薪资规则时发生异常，规则ID: {}", salaryRuleId, e);
          errorList.add(new ImportError(data.getRowNo(), "薪资规则验证失败: " + e.getMessage())); //ignoreI18n
        }
      }
    }

    log.info("薪资规则验证完成，错误数: {}", errorList.size());
    return errorList;
  }

  /**
   * 验证工资项
   */
  public List<ImportError> validateSalaryItems(List<ImportData> dataList, 
                                              Map<String, IObjectData> salaryItemMap,
                                              Map<String, IObjectData> employeeFixedSalaryMap) {
    List<ImportError> errorList = Lists.newArrayList();

    for (ImportData data : dataList) {
      IObjectData objectData = data.getData();
      String salaryItemId = getStringValue(objectData, EmployeeFixedSalaryDetailFields.SALARY_ITEM);
      String employeeFixedSalaryId = getStringValue(objectData, EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY);

      if (StringUtils.isBlank(salaryItemId)) {
        errorList.add(new ImportError(data.getRowNo(), "请选择工资项")); //ignoreI18n
        continue;
      }

      // 检查工资项是否存在
      if (!salaryItemMap.containsKey(salaryItemId)) {
        errorList.add(new ImportError(data.getRowNo(), "工资项不存在或已被删除")); //ignoreI18n
        continue;
      }

      // 检查工资项是否启用
      IObjectData salaryItem = salaryItemMap.get(salaryItemId);
      String enabledStatus = getStringValue(salaryItem, SalaryItemFields.ENABLED_STATUS);
      if (!SalaryItemFields.ENABLED_STATUS_Options_true.equals(enabledStatus)) {
        errorList.add(new ImportError(data.getRowNo(), "选择的工资项已禁用，请重新选择")); //ignoreI18n
        continue;
      }

      // 验证工资项与员工定薪方式的一致性
      if (StringUtils.isNotBlank(employeeFixedSalaryId) && employeeFixedSalaryMap.containsKey(employeeFixedSalaryId)) {
        IObjectData employeeFixedSalary = employeeFixedSalaryMap.get(employeeFixedSalaryId);
        String employeeSalaryMethod = getStringValue(employeeFixedSalary, EmployeeFixedSalaryFields.SALARY_METHOD);
        String salaryItemMethod = getStringValue(salaryItem, SalaryItemFields.SALARY_METHOD);
        
        if (StringUtils.isNotBlank(employeeSalaryMethod) && StringUtils.isNotBlank(salaryItemMethod)) {
          if (!employeeSalaryMethod.equals(salaryItemMethod)) {
            errorList.add(new ImportError(data.getRowNo(),
                MessageFormat.format("工资项的定薪方式({0})与员工的定薪方式({1})不一致", //ignoreI18n
                    salaryItemMethod, employeeSalaryMethod)));
            continue;
          }
        }
      }
    }

    log.info("工资项验证完成，错误数: {}", errorList.size());
    return errorList;
  }

  /**
   * 验证金额
   */
  public List<ImportError> validateAmounts(List<ImportData> dataList) {
    List<ImportError> errorList = Lists.newArrayList();

    for (ImportData data : dataList) {
      IObjectData objectData = data.getData();
      Object amountObj = objectData.get(EmployeeFixedSalaryDetailFields.AMOUNT);

      if (amountObj == null) {
        errorList.add(new ImportError(data.getRowNo(), "请输入金额")); //ignoreI18n
        continue;
      }

      try {
        BigDecimal amount = new BigDecimal(amountObj.toString());
        
        // 检查金额不能为负数
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
          errorList.add(new ImportError(data.getRowNo(), "金额不能为负数")); //ignoreI18n
          continue;
        }

        // 检查金额精度（保留2位小数）
        if (amount.scale() > 2) {
          errorList.add(new ImportError(data.getRowNo(), "金额最多保留2位小数")); //ignoreI18n
        }
      } catch (NumberFormatException e) {
        errorList.add(new ImportError(data.getRowNo(), "金额格式不正确")); //ignoreI18n
      }
    }

    log.info("金额验证完成，错误数: {}", errorList.size());
    return errorList;
  }

  /**
   * 设置业务类型
   */
  public void setRecordTypes(List<IObjectData> validList) {
    for (IObjectData objectData : validList) {
      String internalEmployeeId = getStringValue(objectData, EmployeeFixedSalaryFields.EMPLOYEE);
      String externalEmployeeId = getStringValue(objectData, EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);

      // 根据员工类型设置业务类型
      String recordType = SalaryRecordTypeUtil.getEmployeeFixedRecordType(internalEmployeeId, externalEmployeeId);
      objectData.setRecordType(recordType);

      log.debug("设置员工固定工资表业务类型: {}, 内部员工ID: {}, 外部员工ID: {}", 
          recordType, internalEmployeeId, externalEmployeeId);
    }

    log.info("业务类型设置完成，处理记录数: {}", validList.size());
  }

  /**
   * 获取员工显示名称
   */
  public String getEmployeeDisplayName(IObjectData objectData) {
    String internalEmployeeId = getStringValue(objectData, EmployeeFixedSalaryFields.EMPLOYEE);
    String externalEmployeeId = getStringValue(objectData, EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);
    
    if (StringUtils.isNotBlank(internalEmployeeId)) {
      return "内部员工ID:" + internalEmployeeId; //ignoreI18n
    } else if (StringUtils.isNotBlank(externalEmployeeId)) {
      return "外部员工ID:" + externalEmployeeId; //ignoreI18n
    } else {
      return "未知员工"; //ignoreI18n
    }
  }

  /**
   * 获取字符串字段值的辅助方法
   */
  public String getStringValue(IObjectData objectData, String fieldName) {
    Object value = objectData.get(fieldName);
    return value != null ? value.toString() : null;
  }

  /**
   * 加载已存在的员工固定薪资记录
   */
  public Map<String, IObjectData> loadExistingEmployeeRecords(List<ImportData> dataList, String tenantId) {
    Set<String> allEmployeeIds = dataList.stream()
        .map(data -> {
          IObjectData objectData = data.getData();
          String internalEmployeeId = getStringValue(objectData, EmployeeFixedSalaryFields.EMPLOYEE);
          String externalEmployeeId = getStringValue(objectData, EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);
          return StringUtils.isNotBlank(internalEmployeeId) ? internalEmployeeId : externalEmployeeId;
        })
        .filter(StringUtils::isNotBlank)
        .filter(NumberUtils::isNumber)
        .collect(Collectors.toSet());

    Map<String, IObjectData> existingEmployeeRecords;
    if (CollectionUtils.isNotEmpty(allEmployeeIds)) {
      List<IObjectData> existingRecords = employeeFixedSalaryDao.getByEmployeeIds(tenantId, Lists.newArrayList(allEmployeeIds));
      existingEmployeeRecords = existingRecords.stream()
          .collect(Collectors.toMap(record -> {
            String internalEmployeeId = getStringValue(record, EmployeeFixedSalaryFields.EMPLOYEE);
            String externalEmployeeId = getStringValue(record, EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);
            return StringUtils.isNotBlank(internalEmployeeId) ? internalEmployeeId : externalEmployeeId;
          }, record -> record));
    } else {
      existingEmployeeRecords = Maps.newHashMap();
    }

    log.info("加载已存在的员工固定薪资记录完成，记录数: {}", existingEmployeeRecords.size());
    return existingEmployeeRecords;
  }
}
