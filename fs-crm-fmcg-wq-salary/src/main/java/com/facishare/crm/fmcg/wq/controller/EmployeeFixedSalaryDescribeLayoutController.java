package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryDetailFields;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import org.apache.commons.collections4.CollectionUtils;

import java.util.stream.Collectors;

/**
 * Created by zhangsm on 2018/4/8/0008.
 */
public class EmployeeFixedSalaryDescribeLayoutController extends StandardDescribeLayoutController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        //detailObjectList 不为空 过滤 apiName EmployeeFixedSalaryDetailObj
        if (CollectionUtils.isNotEmpty(result.getDetailObjectList())) {
            result.getDetailObjectList().stream().filter(o -> EmployeeFixedSalaryDetailFields.API_NAME.equals(o.getObjectApiName())).forEach(
                    o -> {
                        if (CollectionUtils.isNotEmpty(o.getLayoutList())){
                            for (RecordTypeLayoutStructure recordTypeLayoutStructure : o.getLayoutList()) {
                                if (CollectionUtils.isNotEmpty(recordTypeLayoutStructure.getBatchButtons())){
                                    //action = Clone
                                    recordTypeLayoutStructure.getBatchButtons().removeIf(b-> ObjectAction.CLONE.getActionCode().equals(b.getOrDefault("action","default").toString()));
                                }
                                if (CollectionUtils.isNotEmpty(recordTypeLayoutStructure.getSingleButtons())){
                                    //singleButtons = Clone
                                    recordTypeLayoutStructure.getSingleButtons().removeIf(b-> ObjectAction.CLONE.getActionCode().equals(b.getOrDefault("action","default").toString()));
                                }
                            }
                        }

                    }
            );
        }
        return after;
    }
}
