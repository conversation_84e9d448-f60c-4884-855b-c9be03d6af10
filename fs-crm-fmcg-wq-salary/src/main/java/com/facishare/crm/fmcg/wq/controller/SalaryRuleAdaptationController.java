package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.model.RestResult;
import com.facishare.crm.fmcg.wq.service.SalaryRuleAdaptationService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 薪资规则员工适用范围查询Controller
 * 根据规则查看员工适用范围的接口
 */
@Slf4j
public class SalaryRuleAdaptationController extends AbstractStandardController<SalaryRuleAdaptationController.Arg, SalaryRuleAdaptationController.Result> {

  private SalaryRuleAdaptationService salaryRuleAdaptationService = SpringUtil.getContext().getBean(SalaryRuleAdaptationService.class);

  @Override
  protected List<String> getFuncPrivilegeCodes() {
    return Lists.newArrayList();
  }

  /**
   * 判断是否为外部员工
   *
   * @param employeeId 员工ID
   * @return 是否为外部员工
   */
  private boolean isExternalEmployee(String employeeId) {
    if (StringUtils.isBlank(employeeId)) {
      log.warn("员工ID为空，默认为内部员工");
      return false;
    }

    try {
      long id = Long.parseLong(employeeId);
      boolean isExternal = id > 100000000L;
      log.debug("员工ID: {}, 是否外部员工: {}", employeeId, isExternal);
      return isExternal;
    } catch (NumberFormatException e) {
      log.warn("员工ID格式异常，employeeId: {}, 默认为内部员工", employeeId);
      return false;
    }
  }



  @Override
  protected Result doService(Arg arg) {
    String tenantId = controllerContext.getTenantId();

    try {
      log.info("开始处理工资规则适配重新计算，tenantId: {}, employeeIds: {}, addSpace: {}",
              tenantId, arg.getEmployeeIds(), arg.getAddSpace());

      // 参数校验
      if (arg.getEmployeeIds() == null || arg.getEmployeeIds().isEmpty()) {
        throw new ValidateException("员工ID列表不能为空"); //ignoreI18n
      }

      Result result = new Result();
      result.setSuccess(true);
      result.setMessage("工资规则适配重新计算完成"); //ignoreI18n

      // 批量处理员工规则适配
      for (String employeeId : arg.getEmployeeIds()) {
        try {
          // 判断是否为外部员工
          boolean isExternal = isExternalEmployee(employeeId);

          // 调用规则适配逻辑重新计算规则并触发发送消息
          SalaryRuleAdaptationService.SalaryRuleAdaptationResult adaptationResult =
                  salaryRuleAdaptationService.checkEmployeeSalaryRuleAdaptation(tenantId, employeeId, isExternal);

          if (adaptationResult != null) {
            log.info("员工规则适配重新计算完成，tenantId: {}, employeeId: {}, 适配规则数: {}, 通知数: {}",
                    tenantId, employeeId,
                    adaptationResult.getApplicableRules() != null ? adaptationResult.getApplicableRules().size() : 0,
                    adaptationResult.getNotificationsSent() != null ? adaptationResult.getNotificationsSent().size() : 0);
          } else {
            log.warn("员工规则适配重新计算失败，tenantId: {}, employeeId: {}", tenantId, employeeId);
          }

        } catch (Exception e) {
          log.error("处理员工规则适配失败，tenantId: {}, employeeId: {}", tenantId, employeeId, e);
          // 继续处理其他员工，不中断整个流程
        }
      }

      log.info("工资规则适配重新计算完成，tenantId: {}, 处理员工数: {}", tenantId, arg.getEmployeeIds().size());
      return result;

    } catch (Exception e) {
      log.error("工资规则适配重新计算失败，tenantId: {}", tenantId, e);
      Result result = new Result();
      result.setSuccess(false);
      result.setMessage("处理失败: " + e.getMessage()); //ignoreI18n
      return result;
    }
  }

  /**
   * Controller 请求参数
   */
  @Data
  public static class Arg  implements Serializable {
    /**
     * 员工ids
     */
    private List<String> employeeIds;
    /**
     * 新增的范围  角色1,角色2
     * 部门1,部门2 等字符串 用于拼接文案
     */
    private String addSpace;
  }



  /**
   * 返回结果
   */
  @Data
  public static class Result  extends RestResult<Object> {
    private boolean success;
    private String message;

    public Result() {
      super();
    }

    public Result(boolean success, String message) {
      this.success = success;
      this.message = message;
    }
  }
}
