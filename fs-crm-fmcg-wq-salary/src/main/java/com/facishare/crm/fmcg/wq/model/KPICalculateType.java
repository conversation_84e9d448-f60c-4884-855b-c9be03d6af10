package com.facishare.crm.fmcg.wq.model;

import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum KPICalculateType {

    OBJECT("object","aggregateSalaryKPICalculator"),
    KAOQIN_STAT("kaoqin_stat","kaoQinStatSalaryKPICalculator"),
    APL("apl","aplSalaryKPICalculator");

    private final String value;
    @Getter
    private final String beanName;


    public String value() {
        return this.value;
    }
    public static KPICalculateType of(String value) {
        switch (value) {
            case SalaryKPIFields.INDICATOR_CALC_METHOD_Options_1: {
                return KPICalculateType.OBJECT;

            }
            case SalaryKPIFields.INDICATOR_CALC_METHOD_Options_2: {
                return KPICalculateType.KAOQIN_STAT;
            }
            case SalaryKPIFields.INDICATOR_CALC_METHOD_Options_3:{
                return KPICalculateType.APL;
            }
            default:
                throw new ValidateException("not support calc type");
        }
    }
}