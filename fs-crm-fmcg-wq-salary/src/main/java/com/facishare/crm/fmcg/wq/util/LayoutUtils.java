package com.facishare.crm.fmcg.wq.util;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 布局工具类
 * 
 * 功能：
 * 1. 添加虚拟字段到布局中
 * 2. 参考 SalaryKPIWebDetailController 的实现方式
 * 3. 统一处理各种对象的虚拟字段布局需求
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class LayoutUtils {

    /**
     * listHeader list 布局隐藏字段
     *
     * @param result
     * @param hideFieldNames
     */
    public static void hideFieldsInList(StandardListHeaderController.Result result, List<String> hideFieldNames) {
        if (CollectionUtils.isEmpty(hideFieldNames)) {
            return;
        }

        try {
            // 从模板中移除字段
            if (result.getTemplates() != null) {
                result.getTemplates().forEach(template -> {
                    List<Map<String, Object>> fieldList = (List<Map<String, Object>>) template.get("field_list");
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(fieldList)) {
                        fieldList = fieldList.stream()
                                .filter(field -> !hideFieldNames.contains(field.get("field_name")))
                                .collect(Collectors.toList());
                        template.put("field_list", fieldList);
                    }
                });
            }
            //从布局里移除


            // 从可见字段列表中移除字段
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(result.getVisibleFields())) {
                result.getVisibleFields().removeAll(hideFieldNames);
            }

            // 从可见字段宽度配置中移除字段
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(result.getVisibleFieldsWidth())) {
                result.getVisibleFieldsWidth().removeIf(field ->
                        hideFieldNames.contains(field.get("field_name")));
            }

            log.debug("成功隐藏字段: {}", hideFieldNames);

        } catch (Exception e) {
            log.warn("隐藏字段失败: {}", hideFieldNames, e);
        }
    }


    /**
     * 添加虚拟字段到详情页布局中
     * 参考 SalaryKPIWebDetailController 的实现方式
     *
     * @param layoutDocument 布局文档
     * @param referenceField 参考字段名称（在此字段之后插入虚拟字段）
     * @param virtualFieldName 虚拟字段名称
     * @return 修改后的布局文档
     */
    public static LayoutDocument addVirtualFieldToDetailLayout(LayoutDocument layoutDocument, String referenceField, String virtualFieldName) {
        try {
            if (layoutDocument == null) {
                log.warn("布局信息为空，无法添加虚拟字段");
                return layoutDocument;
            }

            LayoutExt layoutExt = LayoutExt.of(layoutDocument);
            // 获取所有组件
            List<IComponent> components = layoutExt.getComponents();
            if (components == null || components.isEmpty()) {
                log.warn("layout中没有找到components，跳过虚拟字段添加");
                return layoutDocument;
            }

            // 查找form_component并在其中添加虚拟字段
            boolean fieldAdded = false;
            for (IComponent component : components) {
                if (component != null && "form_component".equals(component.get("api_name"))) {
                    fieldAdded = addVirtualFieldToFormComponent(component, referenceField, virtualFieldName);
                    if (fieldAdded) {
                        break;
                    }
                }
            }

            if (!fieldAdded) {
                log.warn("未找到form_component或INDICATOR_CALC_METHOD字段，跳过虚拟字段添加");
                return layoutDocument;
            }
            // 更新layout
            log.info("成功添加虚拟字段到layout");
            return LayoutDocument.of(layoutExt);
        } catch (Exception e) {
            log.error("添加虚拟字段到布局失败", e);
            return layoutDocument;
        }
    }
    /**
     * 替换虚拟字段到布局中（删除原来的，添加新的） 详情页布局
     * 基于 addVirtualFieldToLayout 方法，大部分逻辑相同，只是先删除再添加
     *
     * @param layoutDocument 布局文档
     * @param referenceField 参考字段名称（在此字段之后插入虚拟字段）
     * @param virtualFieldName 虚拟字段名称
     * @return 修改后的布局文档
     */
    public static LayoutDocument replaceVirtualFieldInDetailLayout(LayoutDocument layoutDocument, String referenceField, String virtualFieldName) {
        try {
            if (layoutDocument == null) {
                log.warn("布局信息为空，无法替换虚拟字段");
                return layoutDocument;
            }

            LayoutExt layoutExt = LayoutExt.of(layoutDocument);
            // 获取所有组件
            List<IComponent> components = layoutExt.getComponents();
            if (components == null || components.isEmpty()) {
                log.warn("layout中没有找到components，跳过虚拟字段替换");
                return layoutDocument;
            }

            // 查找form_component并在其中替换虚拟字段
            boolean fieldReplaced = false;
            for (IComponent component : components) {
                if (component != null && "form_component".equals(component.get("api_name"))) {
                    fieldReplaced = replaceVirtualFieldInFormComponent(component, referenceField, virtualFieldName);
                    if (fieldReplaced) {
                        break;
                    }
                }
            }

            if (!fieldReplaced) {
                log.warn("未找到form_component或参考字段，跳过虚拟字段替换");
                return layoutDocument;
            }
            // 更新layout
            log.info("成功替换虚拟字段到layout");
            return LayoutDocument.of(layoutExt);
        } catch (Exception e) {
            log.error("替换虚拟字段到布局失败", e);
            return layoutDocument;
        }
    }

    /**
     * 在form_component中添加虚拟字段
     * 参考 SalaryKPIWebDetailController 的实现
     */
    @SuppressWarnings("unchecked")
    private static boolean addVirtualFieldToFormComponent(IComponent formComponent, String referenceField, String virtualFieldName) {
        try {
            // 获取field_section数组
            List<Map<String, Object>> fieldSections = (List<Map<String, Object>>) formComponent.get("field_section");
            if (fieldSections == null || fieldSections.isEmpty()) {
                return false;
            }

            // 遍历field_section查找包含参考字段的section
            for (Map<String, Object> section : fieldSections) {
                List<Map<String, Object>> formFields = (List<Map<String, Object>>) section.get("form_fields");
                if (formFields == null) {
                    continue;
                }

                // 查找参考字段的位置
                int insertIndex = -1;
                for (int i = 0; i < formFields.size(); i++) {
                    Map<String, Object> field = formFields.get(i);
                    if (referenceField.equals(field.get("field_name"))) {
                        insertIndex = i + 1; // 在参考字段之后插入
                        break;
                    }
                }

                if (insertIndex != -1) {
                    // 检查虚拟字段是否已存在
                    boolean fieldExists = formFields.stream()
                            .anyMatch(field -> virtualFieldName.equals(field.get("field_name")));

                    if (fieldExists) {
                        log.debug("虚拟字段已存在: {}", virtualFieldName);
                        return true;
                    }

                    // 创建虚拟字段配置
                    Map<String, Object> virtualField = createVirtualFieldConfig(virtualFieldName);

                    // 插入虚拟字段
                    formFields.add(insertIndex, virtualField);

                    log.info("成功在form_component中添加虚拟字段，插入位置: {}, 字段名: {}", insertIndex, virtualFieldName);
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.warn("在form_component中添加虚拟字段失败", e);
            return false;
        }
    }

    /**
     * 创建虚拟字段配置
     * 参考 SalaryKPIWebDetailController 的字段配置
     * 
     * @param virtualFieldName 虚拟字段名称
     * @return 虚拟字段配置
     */
    private static Map<String, Object> createVirtualFieldConfig(String virtualFieldName) {
        Map<String, Object> virtualField = Maps.newHashMap();
        virtualField.put("is_readonly", true);
        virtualField.put("full_line", true);
        virtualField.put("is_required", false);
        virtualField.put("render_type", "text");
        virtualField.put("field_name", virtualFieldName);
        return virtualField;
    }

    /**
     * 在form_component中替换虚拟字段（删除原来的，添加新的）
     * 基于 addVirtualFieldToFormComponent 方法的实现
     */
    @SuppressWarnings("unchecked")
    private static boolean replaceVirtualFieldInFormComponent(IComponent formComponent, String referenceField, String virtualFieldName) {
        try {
            // 获取field_section数组
            List<Map<String, Object>> fieldSections = (List<Map<String, Object>>) formComponent.get("field_section");
            if (fieldSections == null || fieldSections.isEmpty()) {
                return false;
            }

            // 遍历field_section查找包含参考字段的section
            for (Map<String, Object> section : fieldSections) {
                List<Map<String, Object>> formFields = (List<Map<String, Object>>) section.get("form_fields");
                if (formFields == null) {
                    continue;
                }

                // 查找参考字段的位置
                int insertIndex = -1;
                for (int i = 0; i < formFields.size(); i++) {
                    Map<String, Object> field = formFields.get(i);
                    if (referenceField.equals(field.get("field_name"))) {
                        insertIndex = i + 1; // 在参考字段之后插入
                        break;
                    }
                }

                if (insertIndex != -1) {
                    // 先删除已存在的虚拟字段（如果存在）
                    int removedIndex = insertIndex - 1;
                    formFields.remove(removedIndex);
                    insertIndex--;
                    // 创建虚拟字段配置
                    Map<String, Object> virtualField = createVirtualFieldConfig(virtualFieldName);

                    // 插入虚拟字段
                    formFields.add(insertIndex, virtualField);

                    log.info("成功在form_component中替换虚拟字段，插入位置: {}, 字段名: {}", insertIndex, virtualFieldName);
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.warn("在form_component中替换虚拟字段失败", e);
            return false;
        }
    }
}
