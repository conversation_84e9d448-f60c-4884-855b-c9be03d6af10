package com.facishare.crm.fmcg.wq.model;

public enum AggregateFunction {

    COUNT("count", "fmcg.reward.core.enums.AggregateFunction.count"),
    SUM("sum", "fmcg.reward.core.enums.AggregateFunction.sum"),
    MAX("max", "fmcg.reward.core.enums.AggregateFunction.max"),
    MIN("min", "fmcg.reward.core.enums.AggregateFunction.min"),
    AVG("avg", "fmcg.reward.core.enums.AggregateFunction.avg");

    private final String value;

    private final String i18key;

    AggregateFunction(String value, String i18key) {
        this.value = value;
        this.i18key = i18key;
    }

    public String value() {
        return this.value;
    }

    public String i18key() {
        return this.i18key;
    }
    public static AggregateFunction fromValue(String value) {
        for (AggregateFunction type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return null;
    }
}
