package com.facishare.crm.fmcg.wq.util;

import com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 工资金额计算工具类
 * 
 * 功能：
 * 1. 提供统一的金额精度处理
 * 2. 提供应付工资计算逻辑
 * 3. 提供金额格式化和设置的通用方法
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class SalaryAmountCalculator {

    /**
     * 统一精度（3位小数）
     */
    private static final int UNIFIED_SCALE = 3;
    
    /**
     * 默认舍入模式（四舍五入）
     */
    private static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 计算应付工资总额
     *
     * @param salaryDetailDataList 工资条明细列表
     * @return 应付工资总额（保留3位小数）
     */
    public static BigDecimal calculatePayableSalary(List<IObjectData> salaryDetailDataList) {
        if (salaryDetailDataList == null || salaryDetailDataList.isEmpty()) {
            log.debug("工资条明细列表为空，返回0");
            return BigDecimal.ZERO.setScale(UNIFIED_SCALE, DEFAULT_ROUNDING_MODE);
        }

        BigDecimal totalAmount = BigDecimal.ZERO;

        for (IObjectData detailData : salaryDetailDataList) {
            try {
                // 获取金额
                String amountStr = detailData.get(SalaryDetailDataFields.AMOUNT, String.class);
                if (StringUtils.isBlank(amountStr)) {
                    log.debug("工资条明细金额为空，跳过，明细ID: {}", detailData.getId());
                    continue;
                }

                BigDecimal amount = new BigDecimal(amountStr);

                // 获取增减属性
                String incrementDecrementAttrib = detailData.get(
                    SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB, String.class);

                // 根据增减属性判断是加还是减
                if (SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
                    // 减少
                    totalAmount = totalAmount.subtract(amount);
                    log.debug("工资条明细减少金额，明细ID: {}, 金额: -{}", detailData.getId(), amount);
                } else {
                    // 增加（默认为增加，包括null和"1"的情况）
                    totalAmount = totalAmount.add(amount);
                    log.debug("工资条明细增加金额，明细ID: {}, 金额: +{}", detailData.getId(), amount);
                }

            } catch (NumberFormatException e) {
                log.warn("无法解析工资条明细金额，跳过该明细，明细ID: {}, 金额: {}",
                    detailData.getId(), detailData.get(SalaryDetailDataFields.AMOUNT));
            } catch (Exception e) {
                log.warn("处理工资条明细时发生异常，跳过该明细，明细ID: {}", detailData.getId(), e);
            }
        }

        // 设置统一精度
        totalAmount = totalAmount.setScale(UNIFIED_SCALE, DEFAULT_ROUNDING_MODE);

        log.info("应付工资计算完成，明细数量: {}, 总金额: {}", salaryDetailDataList.size(), totalAmount);
        return totalAmount;
    }

    /**
     * 格式化金额（统一使用3位小数）
     *
     * @param amount 金额
     * @return 格式化后的金额字符串（保留3位小数）
     */
    public static String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0.000";
        }

        return amount.setScale(UNIFIED_SCALE, DEFAULT_ROUNDING_MODE).toString();
    }

    /**
     * 格式化金额，去掉末尾无意义的0
     *
     * @param amount 金额
     * @return 格式化后的金额字符串（去掉末尾的0）
     */
    public static String formatAmountWithoutTrailingZeros(String formatted) {
        // 确保至少保留2位小数，只去掉第3位及以后的末尾0
        if (formatted.contains(".")) {
            String[] parts = formatted.split("\\.");
            if (parts.length == 2) {
                String decimalPart = parts[1];
                if (decimalPart.length() <= 2) {
                    // 如果小数位数不足2位，补齐到2位
                    return parts[0] + "." + String.format("%-2s", decimalPart).replace(' ', '0');
                } else {
                    // 保留前2位小数，去掉第3位及以后的末尾0
                    String first2Decimals = decimalPart.substring(0, 2);
                    String remaining = decimalPart.substring(2);
                    remaining = remaining.replaceAll("0+$", "");
                    return parts[0] + "." + first2Decimals + remaining;
                }
            }
        }else {
            return formatted + ".00";
        }
        return formatted;
    }

    /**
     * 解析金额字符串为BigDecimal
     * 
     * @param amountStr 金额字符串
     * @return BigDecimal金额，解析失败返回0
     */
    public static BigDecimal parseAmount(String amountStr) {
        if (StringUtils.isBlank(amountStr)) {
            return BigDecimal.ZERO;
        }
        
        try {
            return new BigDecimal(amountStr);
        } catch (NumberFormatException e) {
            log.warn("金额格式错误，返回0: {}", amountStr);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 验证金额是否有效
     * 
     * @param amount 金额
     * @return 是否有效
     */
    public static boolean isValidAmount(BigDecimal amount) {
        return amount != null && amount.compareTo(BigDecimal.ZERO) >= 0;
    }

    /**
     * 应用精度和舍入模式
     * 
     * @param amount 原始金额
     * @param scale 精度（小数位数）
     * @param roundingMode 舍入模式
     * @return 处理后的金额
     */
    public static BigDecimal applyPrecision(BigDecimal amount, int scale, RoundingMode roundingMode) {
        if (amount == null) {
            return BigDecimal.ZERO.setScale(scale, roundingMode);
        }
        
        return amount.setScale(scale, roundingMode);
    }

    /**
     * 获取统一精度
     *
     * @return 统一精度（3位小数）
     */
    public static int getUnifiedScale() {
        return UNIFIED_SCALE;
    }

    /**
     * 获取默认舍入模式
     *
     * @return 默认舍入模式
     */
    public static RoundingMode getDefaultRoundingMode() {
        return DEFAULT_ROUNDING_MODE;
    }


    /**
     * 验证并格式化金额字符串
     *
     * @param amountStr 金额字符串
     * @return 格式化后的金额，如果无效则返回null
     */
    public static BigDecimal validateAndFormatAmount(String amountStr) {
        if (StringUtils.isBlank(amountStr)) {
            return null;
        }

        try {
            BigDecimal amount = new BigDecimal(amountStr);
            return amount.setScale(UNIFIED_SCALE, DEFAULT_ROUNDING_MODE);
        } catch (NumberFormatException e) {
            log.warn("金额格式无效: {}", amountStr);
            return null;
        }
    }
}
