package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.crm.fmcg.wq.dao.SalaryKPIDao;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 工资绩效指标新建Action
 * 
 * 新建规则：
 * 1. 当指标计算方式为"月度考勤表"时，考勤类型选项不允许重复
 * 2. 验证必填字段和字段依赖关系
 */
@Slf4j
public class SalaryKPIAddAction extends FmcgSkipPermissionAddAction {

    private SalaryKPIDao salaryKPIDao = SpringUtil.getContext().getBean(SalaryKPIDao.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        
        String tenantId = actionContext.getTenantId();
        
        log.info("开始处理工资绩效指标新建");
        
        // 验证字段逻辑和考勤类型重复性
        validateKpiData(tenantId, arg.getObjectData());
        
        log.info("工资绩效指标新建前处理完成");
    }

    /**
     * 验证KPI指标数据
     * @param tenantId 租户ID
     * @param kpiData KPI指标数据
     */
    private void validateKpiData(String tenantId, ObjectDataDocument kpiData) {
        // 获取指标计算方式字段值，并确保其为 String 类型
        Object calcMethodObj = kpiData.get(SalaryKPIFields.INDICATOR_CALC_METHOD);

        if (calcMethodObj == null) {
            throw new ValidateException("指标计算方式不能为空");//ignoreI18n
        }

        String calcMethod;
        if (calcMethodObj instanceof String) {
            calcMethod = (String) calcMethodObj;
        } else if (calcMethodObj instanceof Integer) {
            calcMethod = calcMethodObj.toString(); // 或者根据业务定义映射逻辑
        } else {
            throw new ValidateException("指标计算方式必须是字符串或整数类型");// ignoreI18n
        }
        kpiData.put(SalaryKPIFields.INDICATOR_CALC_METHOD,  calcMethod);
        if (StringUtils.isBlank(calcMethod)) {
            throw new ValidateException("指标计算方式为必填字段"); //ignoreI18n
        }
        
        // 当指标计算方式为"月度考勤表"时的特殊验证
        if (SalaryKPIFields.INDICATOR_CALC_METHOD_Options_2.equals(calcMethod)) {
            validateAttendanceKpi(tenantId, kpiData);
        } else if (SalaryKPIFields.INDICATOR_CALC_METHOD_Options_1.equals(calcMethod)) {
            // 对象计算方式的验证
            validateObjectKpi(kpiData);
        } else if (SalaryKPIFields.INDICATOR_CALC_METHOD_Options_3.equals(calcMethod)) {
            // APL函数计算方式的验证
            validateAplKpi(kpiData);
        }
    }

    /**
     * 验证考勤类型KPI指标
     * @param tenantId 租户ID
     * @param kpiData KPI指标数据
     */
    private void validateAttendanceKpi(String tenantId, ObjectDataDocument kpiData) {
        String attendanceField = (String) kpiData.get(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD);
        if (StringUtils.isBlank(attendanceField)) {
            throw new ValidateException("指标计算方式为月度考勤表时，月度考勤表字段为必填字段"); //ignoreI18n
        }
        
        // 检查考勤类型选项是否已被其他指标使用
        List<IObjectData> existingKpis = salaryKPIDao.getKpisByAttendanceField(tenantId, attendanceField);
        if (CollectionUtils.isNotEmpty(existingKpis)) {
            // 构建已使用该考勤字段的指标信息
            StringBuilder existingKpiInfo = new StringBuilder();
            for (IObjectData existingKpi : existingKpis) {
                if (existingKpiInfo.length() > 0) {
                    existingKpiInfo.append("、");
                }
                existingKpiInfo.append(existingKpi.getName());
            }
            
            // 获取考勤字段的显示名称
            String attendanceFieldDisplayName = getAttendanceFieldDisplayName(attendanceField);
            
            log.warn("考勤字段 {} 已被以下指标使用: {}", attendanceFieldDisplayName, existingKpiInfo.toString());
            throw new ValidateException(String.format("考勤类型选项[%s]已被其他指标使用，不允许重复。使用该考勤字段的指标: %s", //ignoreI18n
                    attendanceFieldDisplayName, existingKpiInfo.toString()));
        }
        
        log.info("考勤字段 {} 验证通过，可以使用", attendanceField);
    }

    /**
     * 验证对象计算方式的KPI指标
     * @param kpiData KPI指标数据
     */
    private void validateObjectKpi(ObjectDataDocument kpiData) {
        // 聚合对象必填
        String aggregatedObject = (String) kpiData.get(SalaryKPIFields.AGGREGATED_OBJECT);
        if (StringUtils.isBlank(aggregatedObject)) {
            throw new ValidateException("指标计算方式为对象时，聚合对象为必填字段"); //ignoreI18n
        }
        
        // 聚合字段必填
        String aggregatedField = (String) kpiData.get(SalaryKPIFields.AGGREGATED_FIELD);
        if (StringUtils.isBlank(aggregatedField)) {
            throw new ValidateException("指标计算方式为对象时，聚合字段为必填字段"); //ignoreI18n
        }
        
        // 聚合函数必填
        String aggregationFunction = (String) kpiData.get(SalaryKPIFields.AGGREGATION_FUNCTION);
        if (StringUtils.isBlank(aggregationFunction)) {
            throw new ValidateException("指标计算方式为对象时，聚合函数为必填字段"); //ignoreI18n
        }
        
        // 清空月度考勤表相关字段
        kpiData.put(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD, null);
        kpiData.put(SalaryKPIFields.APL_INFO, null);
    }

    /**
     * 验证APL函数计算方式的KPI指标
     * @param kpiData KPI指标数据
     */
    private void validateAplKpi(ObjectDataDocument kpiData) {
        // APL信息必填
        String aplInfo = (String) kpiData.get(SalaryKPIFields.APL_INFO);
        if (StringUtils.isBlank(aplInfo)) {
            throw new ValidateException("指标计算方式为APL函数时，APL信息为必填字段"); //ignoreI18n
        }
        
        // 清空其他计算方式的字段
        kpiData.put(SalaryKPIFields.AGGREGATED_OBJECT, null);
        kpiData.put(SalaryKPIFields.AGGREGATED_FIELD, null);
        kpiData.put(SalaryKPIFields.AGGREGATED_PERSON_FIELD, null);
        kpiData.put(SalaryKPIFields.AGGREGATED_DATE_FIELD, null);
        kpiData.put(SalaryKPIFields.AGGREGATION_FUNCTION, null);
        kpiData.put(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD, null);
    }

    /**
     * 获取考勤字段的显示名称
     * 从字段描述中动态获取选项的标签信息
     *
     * @param attendanceField 考勤字段值
     * @return 显示名称
     */
    private String getAttendanceFieldDisplayName(String attendanceField) {
        try {
            // 获取月度考勤表字段的描述信息
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD);
            if (fieldDescribe != null) {
                // 获取字段选项数组
                List<Map<String, String>> optionArray = (List<Map<String, String>>) fieldDescribe.get("options");
                if (CollectionUtils.isNotEmpty(optionArray)) {
                    // 遍历选项，查找匹配的值
                    for (Map<String, String> option : optionArray) {
                        if (attendanceField.equals(option.get("value"))) {
                            // 只使用标签信息
                            String label = option.get("label");
                            if (StringUtils.isNotBlank(label)) {
                                return label;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取考勤字段显示名称失败，使用默认逻辑: {}", e.getMessage());
        }

        // 如果从描述中获取失败，使用原有的硬编码逻辑作为兜底
        switch (attendanceField) {
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_ruleDaysNum:
                return "应出勤天数（天）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_checkDayNum:
                return "正常出勤（天）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_absentDays:
                return "旷工（天）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_waiQinDaysNum:
                return "外勤（天）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_checkWorkTime:
                return "实际工作时长（小时）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_overTime:
                return "加班时长（小时）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_laterNum:
                return "迟到（次）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_laterTime:
                return "迟到（分钟）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_earlyNum:
                return "早退（次）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_earlyTime:
                return "早退（分钟）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_missNum:
                return "未打卡（次）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_locationExNum:
                return "不在考勤范围（次）"; // ignoreI18n
            default:
                return attendanceField;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        log.info("工资绩效指标新建成功，ID: {}", result.getObjectData().getId());
        return after;
    }
}
