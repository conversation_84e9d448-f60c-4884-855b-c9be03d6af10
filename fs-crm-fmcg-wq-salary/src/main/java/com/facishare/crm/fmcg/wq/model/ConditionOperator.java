package com.facishare.crm.fmcg.wq.model;

@SuppressWarnings("all")
public enum ConditionOperator {
    EQ("EQ", "fmcg.reward.core.enums.Operator.EQ", RuleConditionOperator.EQ),
    N("N", "fmcg.reward.core.enums.Operator.N", RuleConditionOperator.N),
    NEQ("NEQ", "fmcg.reward.core.enums.Operator.NEQ", RuleConditionOperator.N),
    EQL("EQL", "fmcg.reward.core.enums.Operator.EQL", null),
    GT("GT", "fmcg.reward.core.enums.Operator.GT", RuleConditionOperator.GT),
    LT("LT", "fmcg.reward.core.enums.Operator.LT", RuleConditionOperator.LT),
    GTE("GTE", "fmcg.reward.core.enums.Operator.GTE", RuleConditionOperator.GTE),
    L<PERSON>("LTE", "fmcg.reward.core.enums.Operator.LTE", RuleConditionOperator.LTE),
    GTEO("GTEO", "fmcg.reward.core.enums.Operator.GTEO", null),
    LTEO("LTEO", "fmcg.reward.core.enums.Operator.LTEO", null),
    // NEQ
    LIKE("LIKE", "fmcg.reward.core.enums.Operator.LIKE", RuleConditionOperator.LIKE),
    NLIKE("NLIKE", "fmcg.reward.core.enums.Operator.NLIKE", RuleConditionOperator.NLIKE),
    // is null
    IS("IS", "fmcg.reward.core.enums.Operator.IS", RuleConditionOperator.IS),
    // is not null
    ISN("ISN", "fmcg.reward.core.enums.Operator.ISN", RuleConditionOperator.ISN),
    IN("IN", "fmcg.reward.core.enums.Operator.IN", RuleConditionOperator.IN),
    NIN("NIN", "fmcg.reward.core.enums.Operator.NIN", RuleConditionOperator.NIN),
    BETWEEN("BETWEEN", "fmcg.reward.core.enums.Operator.BETWEEN", RuleConditionOperator.BETWEEN),
    NBETWEEN("NBETWEEN", "fmcg.reward.core.enums.Operator.NBETWEEN", RuleConditionOperator.NOTBETWEEN),
    STARTWITH("STARTWITH", "fmcg.reward.core.enums.Operator.STARTWITH", RuleConditionOperator.STARTWITH),
    ENDWITH("ENDWITH", "fmcg.reward.core.enums.Operator.ENDWITH", RuleConditionOperator.ENDWITH),
    CONTAINS("CONTAINS", "fmcg.reward.core.enums.Operator.CONTAINS", RuleConditionOperator.CONTAINS),
    NCONTAINS("NCONTAINS", "fmcg.reward.core.enums.Operator.NCONTAINS", RuleConditionOperator.NOTCONTAINS),
    EXISTS("EXISTS", "fmcg.reward.core.enums.Operator.EXISTS", RuleConditionOperator.ISN),
    NEXISTS("NEXISTS", "fmcg.reward.core.enums.Operator.NEXISTS", RuleConditionOperator.IS),
    HASANYOF("HASANYOF", "fmcg.reward.core.enums.Operator.HASANYOF", null),
    NHASANYOF("NHASANYOF", "fmcg.reward.core.enums.Operator.NHASANYOF", null),
    CONTAINED("CONTAINED", "fmcg.reward.core.enums.Operator.CONTAINED", null),
    MATCH("MATCH", "fmcg.reward.core.enums.Operator.MATCH", null),
    WILDCARD("WILDCARD", "fmcg.reward.core.enums.Operator.WILDCARD", null),
    PERCENTILE("percentile", "fmcg.reward.core.enums.Operator.PERCENTILE", null),
    CSEQ("CSEQ", "fmcg.reward.core.enums.Operator.CSEQ", null);

    private final String value;

    private final String i18key;

    private final RuleConditionOperator ruleConditionOperator;

    ConditionOperator(String value, String i18key, RuleConditionOperator ruleConditionOperator) {
        this.value = value;
        this.i18key = i18key;
        this.ruleConditionOperator = ruleConditionOperator;
    }

    public String value() {
        return this.value;
    }

    public String i18key() {
        return this.i18key;
    }

    public RuleConditionOperator ruleConditionOperator() {
        return this.ruleConditionOperator;
    }
}