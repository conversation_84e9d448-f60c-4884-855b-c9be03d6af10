package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryKPIDao;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 工资绩效指标作废Action
 * 
 * 作废规则：
 * 1. 当未被工资项使用时，支持作废
 * 2. 当指标被工资项使用后，不可作废
 * 3. 特殊地，对于预置的月度考勤表相关的指标，不支持作废
 */
@Slf4j
public class SalaryKPIInvalidAction extends StandardInvalidAction {

    private SalaryItemDao salaryItemDao = SpringUtil.getContext().getBean(SalaryItemDao.class);
    private SalaryKPIDao salaryKPIDao = SpringUtil.getContext().getBean(SalaryKPIDao.class);

    @Override
    protected void before(Arg arg) {
        String tenantId = actionContext.getTenantId();
        String kpiId = arg.getObjectDataId();
        
        log.info("开始处理工资绩效指标作废，指标ID: {}", kpiId);
        
        // 获取指标数据
        IObjectData kpiData = salaryKPIDao.getById(tenantId, kpiId);
        if (kpiData == null) {
            throw new ValidateException("工资绩效指标不存在"); //ignoreI18n
        }
        
        // 检查是否为预置的月度考勤表相关指标
        if (salaryKPIDao.isPresetMonthlyAttendanceKpi(kpiData)) {
            throw new ValidateException("预置的月度考勤表相关指标不支持作废"); //ignoreI18n
        }
        
        // 检查指标是否被工资项使用
        List<IObjectData> usingSalaryItems = salaryItemDao.getSalaryItemsByKpiId(tenantId, kpiId);
        if (CollectionUtils.isNotEmpty(usingSalaryItems)) {
            // 构建使用该指标的工资项信息
            StringBuilder usingItemsInfo = new StringBuilder();
            for (IObjectData item : usingSalaryItems) {
                if (usingItemsInfo.length() > 0) {
                    usingItemsInfo.append("、");
                }
                usingItemsInfo.append(item.getName());
            }
            
            log.warn("指标 {} 被以下工资项使用，不允许作废: {}", kpiId, usingItemsInfo.toString());
            throw new ValidateException(String.format("指标已被工资项使用，不可作废。使用该指标的工资项: %s", usingItemsInfo.toString())); //ignoreI18n
        }
        
        log.info("指标 {} 未被工资项使用且非预置指标，允许作废", kpiId);
        
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        log.info("工资绩效指标作废成功，ID: {}", arg.getObjectDataId());
        return after;
    }
}
