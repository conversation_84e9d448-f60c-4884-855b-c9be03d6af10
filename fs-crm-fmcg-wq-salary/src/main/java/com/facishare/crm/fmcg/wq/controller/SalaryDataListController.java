package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.SalaryDataFields;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 工资条列表控制器
 * 
 * 功能：权限控制 - 如果没有 reviewAll 权限，只能查看已发放状态的工资条
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class SalaryDataListController extends StandardListController {

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        // 检查用户是否有 reviewAll 权限
        Map<String, Boolean> privilegeMap = serviceFacade.funPrivilegeCheck(
                controllerContext.getUser(), 
                SalaryDataFields.API_NAME, 
                Lists.newArrayList(ObjectAction.VIEW_ALL.getActionCode()));
        
        boolean hasReviewAllPermission = privilegeMap.getOrDefault(ObjectAction.VIEW_ALL.getActionCode(), false);
        
        log.debug("用户权限检查结果 - reviewAll: {}, 用户ID: {}", hasReviewAllPermission, controllerContext.getUser().getUserId());
        
        // 如果没有 reviewAll 权限，添加发放状态过滤条件
        if (!hasReviewAllPermission) {
            if (query.getFilters() == null) {
                query.setFilters(Lists.newArrayList());
            }
            
            // 添加过滤条件：只显示已发放状态的工资条
            Filter distributionStatusFilter = new Filter();
            distributionStatusFilter.setFieldName(SalaryDataFields.DISTRIBUTION_STATUS);
            distributionStatusFilter.setOperator(Operator.EQ);
            distributionStatusFilter.setFieldValues(Lists.newArrayList(SalaryDataFields.DISTRIBUTION_STATUS_Options_3)); // 已发放
            query.getFilters().add(distributionStatusFilter);
            
            log.info("用户无 reviewAll 权限，已添加发放状态过滤条件，只显示已发放的工资条");
        }
        
        return super.getQueryResult(query);
    }
}
