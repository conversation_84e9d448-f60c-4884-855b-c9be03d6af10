package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryKPIDao;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 工资绩效指标批量作废Action
 *
 * 作废规则：
 * 1. 当未被工资项使用时，支持作废
 * 2. 当指标被工资项使用后，不可作废
 * 3. 特殊地，对于预置的月度考勤表相关的指标，不支持作废
 */
@Slf4j
public class SalaryKPIBulkInvalidAction extends StandardBulkInvalidAction {

    private SalaryItemDao salaryItemDao = SpringUtil.getContext().getBean(SalaryItemDao.class);
    private SalaryKPIDao salaryKPIDao = SpringUtil.getContext().getBean(SalaryKPIDao.class);

    @Override
    protected void before(Arg arg) {
        String tenantId = actionContext.getTenantId();
        List<String> kpiIds = arg.getDataIds();

        log.info("开始处理工资绩效指标批量作废，租户ID: {}, 指标数量: {}", tenantId, kpiIds.size());

        // 批量获取指标数据
        List<IObjectData> kpiDataList = salaryKPIDao.getbyKpiIds(tenantId, kpiIds);
        if (kpiDataList.size() != kpiIds.size()) {
            throw new ValidateException("部分工资绩效指标不存在"); //ignoreI18n
        }

        // 验证每个指标是否可以作废
        for (IObjectData kpiData : kpiDataList) {
            validateKpiCanBeInvalid(tenantId, kpiData);
        }

        log.info("工资绩效指标批量作废前验证通过");
        super.before(arg);
    }

    /**
     * 验证单个指标是否可以作废
     * @param tenantId 租户ID
     * @param kpiData 指标数据
     */
    private void validateKpiCanBeInvalid(String tenantId, IObjectData kpiData) {
        String kpiId = kpiData.getId();
        String kpiName = kpiData.getName();

        // 检查是否为预置的月度考勤表相关指标
        if (salaryKPIDao.isPresetMonthlyAttendanceKpi(kpiData)) {
            throw new ValidateException(String.format("指标[%s]为预置的月度考勤表相关指标，不支持作废", kpiName)); //ignoreI18n
        }

        // 检查指标是否被工资项使用
        List<IObjectData> usingSalaryItems = salaryItemDao.getSalaryItemsByKpiId(tenantId, kpiId);
        if (CollectionUtils.isNotEmpty(usingSalaryItems)) {
            // 构建使用该指标的工资项信息
            StringBuilder usingItemsInfo = new StringBuilder();
            for (IObjectData item : usingSalaryItems) {
                if (usingItemsInfo.length() > 0) {
                    usingItemsInfo.append("、");
                }
                usingItemsInfo.append(item.getName());
            }

            log.warn("指标[{}]被以下工资项使用，不允许作废: {}", kpiName, usingItemsInfo.toString());
            throw new ValidateException(String.format("指标[%s]已被工资项使用，不可作废。使用该指标的工资项: %s", //ignoreI18n
                    kpiName, usingItemsInfo.toString()));
        }

        log.info("指标[{}]验证通过，可以作废", kpiName);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        log.info("工资绩效指标批量作废成功，作废数量: {}", arg.getDataIds().size());
        return after;
    }
}
