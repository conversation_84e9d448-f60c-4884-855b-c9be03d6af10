package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import lombok.extern.slf4j.Slf4j;

/**
 * 工资条作废Action
 * 
 * 功能：阻止前端直接作废工资条
 */
@Slf4j
public class SalaryDataInvalidAction extends FmcgUnsupportedExceptionAction {

//    @Override
//    protected void before(Arg arg) {
//        log.error("工资条作废操作被阻止，不允许前端直接作废工资条");
//        throw new ValidateException("不允许直接作废工资条，请通过工资发放单管理"); //ignoreI18n
//    }
}
