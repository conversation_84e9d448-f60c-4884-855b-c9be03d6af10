package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * 工资规则批量作废Action
 * 
 * 作废规则：
 * 1. 工资规则生效日期前，支持作废
 * 2. 当工资生效日期后，不允许作废
 * 3. 批量作废时，如果有任何一个规则已生效，则整个批量操作失败
 */
@Slf4j
public class SalaryRuleBulkInvalidAction extends StandardBulkInvalidAction {

    private SalaryRuleDao salaryRuleDao = SpringUtil.getContext().getBean(SalaryRuleDao.class);

    @Override
    protected void before(Arg arg) {
        String tenantId = actionContext.getTenantId();
        List<String> salaryRuleIds = arg.getDataIds();
        
        log.info("开始处理工资规则批量作废，规则数量: {}", salaryRuleIds.size());
        
        // 检查所有工资规则的生效状态
        validateBulkInvalidPermissions(tenantId, salaryRuleIds);
        
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        
        log.info("工资规则批量作废成功，作废数量: {}", arg.getDataList().size());
        
        return after;
    }

    /**
     * 验证批量作废权限
     * @param tenantId 租户ID
     * @param salaryRuleIds 工资规则ID列表
     */
    private void validateBulkInvalidPermissions(String tenantId, List<String> salaryRuleIds) {
        if (CollectionUtils.isEmpty(salaryRuleIds)) {
            throw new ValidateException("请选择要作废的工资规则"); //ignoreI18n
        }
        
        // 获取所有工资规则数据
        List<IObjectData> salaryRules = salaryRuleDao.getByIds(tenantId, salaryRuleIds);
        
        if (CollectionUtils.isEmpty(salaryRules)) {
            throw new ValidateException("未找到要作废的工资规则"); //ignoreI18n
        }
        
        long currentTime = System.currentTimeMillis();
        StringBuilder effectiveRules = new StringBuilder();
        
        // 检查每个工资规则的生效状态
        for (IObjectData salaryRule : salaryRules) {
            String ruleName = salaryRule.getName();
            Long effectiveDate = salaryRule.get(SalaryRuleFields.EFFECTIVE_DATE, Long.class);
            boolean isEffective = effectiveDate != null && effectiveDate <= currentTime;
            
            if (isEffective) {
                if (effectiveRules.length() > 0) {
                    effectiveRules.append("、");
                }
                effectiveRules.append(ruleName);
                
                log.warn("工资规则 {} 已生效，不允许作废。生效时间: {}, 当前时间: {}", 
                        ruleName, effectiveDate, currentTime);
            }
        }
        
        // 如果有已生效的规则，抛出异常
        if (effectiveRules.length() > 0) {
            String errorMessage = String.format("以下工资规则已生效，不可作废：%s", effectiveRules.toString()); //ignoreI18n
            throw new ValidateException(errorMessage); //ignoreI18n
        }
        
        log.info("所有工资规则均未生效，允许批量作废");
    }
}
