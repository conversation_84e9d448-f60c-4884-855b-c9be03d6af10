package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.service.ConnectedRoleFieldService;
import com.facishare.crm.fmcg.wq.util.DescribeUtils;
import com.facishare.crm.fmcg.wq.util.LayoutUtils;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 工资规则列表头控制器
 * 隐藏适用角色字段，避免在列表页显示原始角色ID
 *
 * <AUTHOR>
 */
@Slf4j
public class SalaryRuleListHeaderController extends StandardListHeaderController {
    private static final List<String> hideKeyList = Lists.newArrayList(SalaryRuleFields.APPLICABLE_ROLE_INTE, SalaryRuleFields.APPLICABLE_OUT_ROLE);

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("SalaryRuleListHeaderController after method called");
        result = super.after(arg, result);

        try {
            if(result.getObjectDescribe() != null){
                // 添加内部角色虚拟字段到对象描述
                result.setObjectDescribe(DescribeUtils.addSalaryRuleInternalRoleVirtualFieldToObjectDescribe(
                    result.getObjectDescribe(),
                    SalaryRuleFields.APPLICABLE_ROLE_INTE_DISPLAY, objectDescribeExt.getFieldDescribe(SalaryRuleFields.APPLICABLE_ROLE_INTE).getLabel() //ignoreI18n
                ));
    
                // 添加外部角色虚拟字段到对象描述
                result.setObjectDescribe(DescribeUtils.addSalaryRuleExternalRoleVirtualFieldToObjectDescribe(
                    result.getObjectDescribe(),
                    SalaryRuleFields.APPLICABLE_OUT_ROLE_DISPLAY, objectDescribeExt.getFieldDescribe(SalaryRuleFields.APPLICABLE_OUT_ROLE).getLabel() //ignoreI18n
                ));
    
                log.debug("工资规则列表页面角色虚拟字段已添加");
            }
            LayoutUtils.hideFieldsInList(result, hideKeyList);

        } catch (Exception e) {
            log.error("处理工资规则列表页面时发生异常", e);
        }

        return result;
    }



}
