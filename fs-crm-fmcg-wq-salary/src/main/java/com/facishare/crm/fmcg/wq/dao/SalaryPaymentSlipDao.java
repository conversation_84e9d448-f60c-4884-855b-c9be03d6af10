package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 工资发放单数据访问类
 * @author: dev
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class SalaryPaymentSlipDao extends AbstractDao {
    final static List<String> salaryPaymentSlipFields = ConfigUtils.getFields(SalaryPaymentSlipFields.class);

    /**
     * 根据ID获取工资发放单
     *
     * @param tenantId 租户ID
     * @param salaryPaymentSlipId 工资发放单ID
     * @return 工资发放单对象
     */
    public IObjectData getById(String tenantId, String salaryPaymentSlipId) {
        return getById(tenantId, SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipId);
    }

    /**
     * 根据ID列表批量获取工资发放单
     *
     * @param tenantId 租户ID
     * @param ids 工资发放单ID列表
     * @return 工资发放单对象列表
     */
    public List<IObjectData> getByIds(String tenantId, List<String> ids) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), ids)
                .build(), SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipFields);
    }
    /**
     * 包含所有字段的
     */
    public List<IObjectData> getSalaryPaymentSlipListWithAllFields(String tenantId, List<String> salaryPaymentSlipIds) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), salaryPaymentSlipIds)
                .build(), SalaryPaymentSlipFields.API_NAME, null);
    }


    /**
     * 根据发放周期获取工资发放单
     *
     * @param tenantId 租户ID
     * @param payCycle 发放周期
     * @return 工资发放单对象列表
     */
//    public List<IObjectData> getByPayCycle(String tenantId, String payCycle) {
//        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
//                .eq(SalaryPaymentSlipFields.PAY_CYCLE, payCycle)
//                .build(), SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipFields);
//    }

    public List<IObjectData> getByRangeAndRule(String tenantId, long startTime, long endTime, String salaryRuleId) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .lte(SalaryPaymentSlipFields.START_DATE, endTime)
                .gte(SalaryPaymentSlipFields.END_DATE, startTime)
                .eq(SalaryPaymentSlipFields.SALARY_RULE, salaryRuleId)
                .build(), SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipFields);
    }

    /**
     * 根据发放状态获取工资发放单
     *
     * @param tenantId 租户ID
     * @param payStatus 发放状态
     * @return 工资发放单对象列表
     */
    public List<IObjectData> getByPayStatus(String tenantId, String payStatus) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryPaymentSlipFields.PAY_STATUS, payStatus)
                .build(), SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipFields);
    }

    /**
     * 根据时间范围获取工资发放单
     *
     * @param tenantId 租户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工资发放单对象列表
     */
    public List<IObjectData> getByTimeRange(String tenantId, long startTime, long endTime) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryPaymentSlipFields.START_DATE, startTime)
                .eq(SalaryPaymentSlipFields.END_DATE, endTime)
                .build(), SalaryPaymentSlipFields.API_NAME, salaryPaymentSlipFields);
    }

    /**
     * 创建工资发放单对象
     *
     * @param tenantId 租户ID
     * @return 新创建的工资发放单对象
     */
    public IObjectData createSalaryPaymentSlip(String tenantId) {
        IObjectData salaryPaymentSlip = createBaseObjectData(tenantId, null, SalaryPaymentSlipFields.API_NAME);
        salaryPaymentSlip.setId(null);
        return salaryPaymentSlip;
    }

    /**
     * 创建工资发放单对象（支持指定业务类型）
     *
     * @param tenantId 租户ID
     * @param recordType 业务类型
     * @return 新创建的工资发放单对象
     */
    public IObjectData createSalaryPaymentSlip(String tenantId, String recordType) {
        IObjectData salaryPaymentSlip = createBaseObjectData(tenantId, null, SalaryPaymentSlipFields.API_NAME);
        salaryPaymentSlip.setId(null);

        // 设置业务类型
        if (StringUtils.isNotBlank(recordType)) {
            salaryPaymentSlip.setRecordType(recordType);
            log.debug("设置工资发放单业务类型为: {}", recordType);
        }

        return salaryPaymentSlip;
    }

    /**
     * 保存工资发放单对象
     *
     * @param user 用户
     * @param salaryPaymentSlip 工资发放单对象
     * @return 保存后的工资发放单对象
     */
    public IObjectData saveSalaryPaymentSlip(User user, IObjectData salaryPaymentSlip) {
        return save(user, salaryPaymentSlip);
    }

    /**
     * 更新工资发放单对象
     *
     * @param user 用户
     * @param salaryPaymentSlip 工资发放单对象
     * @return 更新后的工资发放单对象
     */
    public IObjectData updateSalaryPaymentSlip(User user, IObjectData salaryPaymentSlip) {
        return update(user, salaryPaymentSlip);
    }

    /**
     * 更新工资发放单的发放状态
     *
     * @param tenantId 租户ID
     * @param salaryPaymentSlipId 工资发放单ID
     * @param payStatus 新的发放状态
     * @return 更新后的工资发放单对象
     */
    public IObjectData updatePayStatus(String tenantId, String salaryPaymentSlipId, String payStatus) {
        try {
            // 获取工资发放单
            IObjectData salaryPaymentSlip = getById(tenantId, salaryPaymentSlipId);
            if (salaryPaymentSlip == null) {
                log.error("工资发放单不存在，无法更新状态，ID: {}", salaryPaymentSlipId);
                return null;
            }

            // 记录状态变更日志
            String oldStatus = salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class);
            log.info("更新工资发放单状态，ID: {}, 原状态: {}, 新状态: {}", salaryPaymentSlipId, oldStatus, payStatus);

            // 更新状态
            salaryPaymentSlip.set(SalaryPaymentSlipFields.PAY_STATUS, payStatus);

            // 保存更新
            IObjectData updatedSlip = update(User.systemUser(tenantId), salaryPaymentSlip);

            log.info("工资发放单状态更新成功，ID: {}, 状态: {}", salaryPaymentSlipId, payStatus);
            return updatedSlip;

        } catch (Exception e) {
            log.error("更新工资发放单状态失败，ID: {}, 状态: {}", salaryPaymentSlipId, payStatus, e);
            throw new RuntimeException("更新工资发放单状态失败: " + e.getMessage(), e); //ignoreI18n
        }
    }

    /**
     * 批量更新工资发放单的发放状态
     *
     * @param tenantId 租户ID
     * @param salaryPaymentSlipIds 工资发放单ID列表
     * @param payStatus 新的发放状态
     * @return 成功更新的数量
     */
    public int batchUpdatePayStatus(String tenantId, List<String> salaryPaymentSlipIds, String payStatus) {
        if (salaryPaymentSlipIds == null || salaryPaymentSlipIds.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (String salaryPaymentSlipId : salaryPaymentSlipIds) {
            try {
                IObjectData updatedSlip = updatePayStatus(tenantId, salaryPaymentSlipId, payStatus);
                if (updatedSlip != null) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量更新工资发放单状态失败，ID: {}", salaryPaymentSlipId, e);
            }
        }

        log.info("批量更新工资发放单状态完成，总数: {}, 成功: {}, 状态: {}",
                salaryPaymentSlipIds.size(), successCount, payStatus);
        return successCount;
    }
}
