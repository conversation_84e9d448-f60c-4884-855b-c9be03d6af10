package com.facishare.crm.fmcg.wq.model;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 考勤字段类型
 * @author: zhangsm
 * @create: 2025-05-08 14:33
 **/
public enum KaoQinFieldType {
    /**
     *
     //应出勤天数（天）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_ruleDaysNum = "ruleDaysNum";

     //正常出勤（天）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_checkDayNum = "checkDayNum";

     //旷工（天）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_absentDays = "absentDays";

     //外勤（天）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_waiQinDaysNum = "waiQinDaysNum";

     //实际工作时长（小时）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_checkWorkTime = "checkWorkTime";

     //加班时长（小时）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_overTime = "overTime";

     //迟到（次）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_laterNum = "laterNum";

     //迟到（分钟）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_laterTime = "laterTime";

     //早退（次）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_earlyNum = "earlyNum";

     //早退（分钟）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_earlyTime = "earlyTime";

     //未打卡（次）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_missNum = "missNum";

     //不在考勤范围（次）
     public static final String MONTHLY_ATTENDANCE_FIELD_Options_locationExNum = "locationExNum";
     */
    ruleDaysNum("ruleDaysNum","EmployeeKPIObj.field.monthly_attendance_field.option.ruleDaysNum"),
    checkDayNum("checkDayNum","EmployeeKPIObj.field.monthly_attendance_field.option.checkDayNum"),
    absentDays("absentDays","EmployeeKPIObj.field.monthly_attendance_field.option.absentDays"),
    waiQinDaysNum("waiQinDaysNum","EmployeeKPIObj.field.monthly_attendance_field.option.waiQinDaysNum"),
    checkWorkTime("checkWorkTime","EmployeeKPIObj.field.monthly_attendance_field.option.checkWorkTime"),
    overTime("overTime","EmployeeKPIObj.field.monthly_attendance_field.option.overTime"),
    laterNum("laterNum","EmployeeKPIObj.field.monthly_attendance_field.option.laterNum"),
    laterTime("laterTime","EmployeeKPIObj.field.monthly_attendance_field.option.laterTime"),
    earlyNum("earlyNum","EmployeeKPIObj.field.monthly_attendance_field.option.earlyNum"),
    earlyTime("earlyTime","EmployeeKPIObj.field.monthly_attendance_field.option.earlyTime"),
    missNum("missNum","EmployeeKPIObj.field.monthly_attendance_field.option.missNum"),
    locationExNum("locationExNum","EmployeeKPIObj.field.monthly_attendance_field.option.locationExNum");

    private final String value;
    private final String i18nKey;

    KaoQinFieldType(String value,String i18nKey) {
        this.value = value;
        this.i18nKey = i18nKey;
    }

}
