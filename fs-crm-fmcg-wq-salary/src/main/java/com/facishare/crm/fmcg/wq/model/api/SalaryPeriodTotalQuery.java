package com.facishare.crm.fmcg.wq.model.api;

import com.facishare.crm.fmcg.wq.model.SalaryItem;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 获取日/周/月薪资数据接口（返回总薪资金额和薪资明细项目列表）
 */
public interface SalaryPeriodTotalQuery {
  @Data
  @ToString
  class Arg implements Serializable {
    /**
     * 员工ID
     */
    @JSONField(name = "employee_id")
    @JsonProperty("employee_id")
    @SerializedName("employee_id")
    private String employeeId;

    
    /**
     * 周期ID，从EmployeeSalaryTypeQuery接口获取
     */
    @JSONField(name = "period_id")
    @JsonProperty("period_id")
    @SerializedName("period_id")
    private String periodId;


    @JSONField(name = "start_date")
    @JsonProperty("start_date")
    @SerializedName("start_date")
    private String startDate;
    
    @JSONField(name = "end_date")
    @JsonProperty("end_date")
    @SerializedName("end_date")
    private String endDate;
    
   /**
    * 工资条id
    */
    @JSONField(name = "salary_data_id")
    @JsonProperty("salary_data_id")
    @SerializedName("salary_data_id")
    private String salaryDataId;

  }
  
  @Data
  @ToString
  class Result implements Serializable {
    /**
     * 总薪资金额
     */
    @JSONField(name = "total_amount")
    @JsonProperty("total_amount")
    @SerializedName("total_amount")
    private String totalAmount;

    /**
     * 周期ID，从EmployeeSalaryTypeQuery接口获取
     */
    @JSONField(name = "period_id")
    @JsonProperty("period_id")
    @SerializedName("period_id")
    private String periodId;
    

    
    /**
     * 周期开始日期，格式：yyyy-MM-dd
     */
    @JSONField(name = "start_date")
    @JsonProperty("start_date")
    @SerializedName("start_date")
    private String startDate;
    
    /**
     * 周期结束日期，格式：yyyy-MM-dd
     */
    @JSONField(name = "end_date")
    @JsonProperty("end_date")
    @SerializedName("end_date")
    private String endDate;
    
    /**
     * 薪资明细项目列表
     */
    @JSONField(name = "salary_items")
    @JsonProperty("salary_items")
    @SerializedName("salary_items")
    private List<SalaryItem> salaryItems;
    
    
    
    // /**
    //  * 薪资规则ID
    //  */
    // @JSONField(name = "salary_rule_id")
    // @JsonProperty("salary_rule_id")
    // @SerializedName("salary_rule_id")
    // private String salaryRuleId;
    
    // /**
    //  * 薪资规则名称
    //  */
    // @JSONField(name = "salary_rule_name")
    // @JsonProperty("salary_rule_name")
    // @SerializedName("salary_rule_name")
    // private String salaryRuleName;
    
    // /**
    //  * 定薪方式：1-日薪，2-周薪，3-月薪
    //  */
    // @JSONField(name = "salary_method")
    // @JsonProperty("salary_method")
    // @SerializedName("salary_method")
    // private String salaryMethod;
    
    // /**
    //  * 发放周期：1-按日发放，2-按周发放，3-按月发放
    //  */
    // @JSONField(name = "distribution_cycle")
    // @JsonProperty("distribution_cycle")
    // @SerializedName("distribution_cycle")
    // private String distributionCycle;
  }
}
