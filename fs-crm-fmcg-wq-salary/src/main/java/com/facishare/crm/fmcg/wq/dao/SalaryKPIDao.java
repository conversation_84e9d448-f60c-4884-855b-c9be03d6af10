package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-05-07 16:51
 **/
@Slf4j
@Component
public class SalaryKPIDao extends AbstractDao {
    final static List<String> salaryKPIFields = ConfigUtils.getFields(SalaryKPIFields.class);

    public List<IObjectData> getbyKpiIds(String tenantId, List<String> kpiIds) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(BaseField.id.getApiName(), kpiIds)
                .build(), SalaryKPIFields.API_NAME, salaryKPIFields);
    }

    /**
     * 根据ID获取单个KPI指标
     * 
     * @param tenantId 租户ID
     * @param kpiId    KPI指标ID
     * @return KPI指标对象
     */
    public IObjectData getById(String tenantId, String kpiId) {
        return getById(tenantId, SalaryKPIFields.API_NAME, kpiId);
    }

    /**
     * 检查是否为预置的月度考勤表相关指标
     *
     * @param kpiData KPI指标数据
     * @return true表示是预置的月度考勤表指标，不可作废
     */
    public boolean isPresetMonthlyAttendanceKpi(IObjectData kpiData) {
        if (kpiData == null) {
            return false;
        }

        // 检查指标计算方式是否为月度考勤表
        String calcMethod = kpiData.get(SalaryKPIFields.INDICATOR_CALC_METHOD, String.class);
        return SalaryKPIFields.INDICATOR_CALC_METHOD_Options_2.equals(calcMethod);
    }

    /**
     * 根据月度考勤表字段查询使用该字段的KPI指标
     * 
     * @param tenantId        租户ID
     * @param attendanceField 月度考勤表字段值
     * @return 使用该考勤字段的KPI指标列表
     */
    public List<IObjectData> getKpisByAttendanceField(String tenantId, String attendanceField) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryKPIFields.INDICATOR_CALC_METHOD, SalaryKPIFields.INDICATOR_CALC_METHOD_Options_2) // 月度考勤表
                .eq(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD, attendanceField)
                .build(), SalaryKPIFields.API_NAME, salaryKPIFields);
    }

    /**
     * 根据月度考勤表字段查询使用该字段的KPI指标（排除指定ID）
     *
     * @param tenantId        租户ID
     * @param attendanceField 月度考勤表字段值
     * @param excludeKpiId    要排除的KPI指标ID
     * @return 使用该考勤字段的KPI指标列表
     */
    public List<IObjectData> getKpisByAttendanceFieldExcludeId(String tenantId, String attendanceField,
            String excludeKpiId) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .eq(SalaryKPIFields.INDICATOR_CALC_METHOD, SalaryKPIFields.INDICATOR_CALC_METHOD_Options_2) // 月度考勤表
                .eq(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD, attendanceField)
                .neq(BaseField.id.getApiName(), excludeKpiId)
                .build(), SalaryKPIFields.API_NAME, salaryKPIFields);
    }
}
