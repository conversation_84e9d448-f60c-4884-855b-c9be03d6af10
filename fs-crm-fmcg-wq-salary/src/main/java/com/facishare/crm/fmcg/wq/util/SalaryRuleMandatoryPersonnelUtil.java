package com.facishare.crm.fmcg.wq.util;

import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 薪资规则强制适用人员处理工具类
 * 
 * 提供强制适用人员的验证和冲突处理功能
 */
@Slf4j
public class SalaryRuleMandatoryPersonnelUtil {

    /**
     * 验证强制适用和强制不适用人员（仅验证重复，不验证冲突）
     * 
     * @param mandatoryApplicablePersonnel 强制适用人员
     * @param nonApplicablePersonnel 强制不适用人员
     * @throws ValidateException 如果有重复人员
     */
    public static void validateMandatoryPersonnelDuplicates(Object mandatoryApplicablePersonnel, 
                                                           Object nonApplicablePersonnel) {
        try {
            List<String> mandatoryList = DataUtils.convertToStringList(mandatoryApplicablePersonnel);
            List<String> nonApplicableList = DataUtils.convertToStringList(nonApplicablePersonnel);
            
            // 验证强制适用和强制不适用人员是否有重复
            if (!mandatoryList.isEmpty() && !nonApplicableList.isEmpty()) {
                List<String> duplicates = mandatoryList.stream()
                        .filter(nonApplicableList::contains)
                        .collect(Collectors.toList());
                
                if (!duplicates.isEmpty()) {
                    throw new ValidateException("以下人员同时在强制适用和强制不适用列表中，请检查：" + String.join(", ", duplicates)); //ignoreI18n
                }
            }
            
        } catch (ValidateException e) {
            throw e;
        } catch (Exception e) {
            log.error("验证强制适用和强制不适用人员重复时发生异常", e);
            throw new ValidateException("验证强制适用人员失败"); //ignoreI18n
        }
    }
}
