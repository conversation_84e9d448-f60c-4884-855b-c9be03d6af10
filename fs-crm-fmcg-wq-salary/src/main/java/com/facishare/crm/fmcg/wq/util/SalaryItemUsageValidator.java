package com.facishare.crm.fmcg.wq.util;

import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryDetailFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDetailDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 工资项使用状态验证工具类
 * 提供统一的工资项使用状态检查和使用信息构建功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class SalaryItemUsageValidator {

    /**
     * 检查工资项是否被使用
     * 
     * @param tenantId 租户ID
     * @param salaryItemId 工资项ID
     * @param salaryRuleDao 工资规则DAO
     * @param employeeFixedSalaryDetailDao 员工固定工资明细DAO
     * @return 使用状态信息
     */
    public static SalaryItemUsageInfo checkSalaryItemUsage(String tenantId, String salaryItemId,
            SalaryRuleDao salaryRuleDao, EmployeeFixedSalaryDetailDao employeeFixedSalaryDetailDao) {
        
        // 检查工资项是否被工资规则使用
        List<IObjectData> usingSalaryRules = salaryRuleDao.getSalaryRulesBySalaryItemId(tenantId, salaryItemId);
        
        // 检查工资项是否被员工固定工资表使用
        List<IObjectData> usingEmployeeFixedSalaryDetails = employeeFixedSalaryDetailDao.getBySalaryItemId(tenantId, salaryItemId);
        employeeFixedSalaryDetailDao.fillRefObject(User.systemUser(tenantId), EmployeeFixedSalaryDetailFields.API_NAME, usingEmployeeFixedSalaryDetails);
        
        return new SalaryItemUsageInfo(usingSalaryRules, usingEmployeeFixedSalaryDetails);
    }

    /**
     * 构建使用信息文案
     *
     * @param usingSalaryRules 使用该工资项的工资规则列表
     * @param usingEmployeeFixedSalaryDetails 使用该工资项的员工固定工资明细列表
     * @return 使用信息文案
     */
    public static String buildUsageMessage(List<IObjectData> usingSalaryRules, List<IObjectData> usingEmployeeFixedSalaryDetails) {
        StringBuilder usingInfo = new StringBuilder();
        
        // 添加工资规则使用信息
        if (CollectionUtils.isNotEmpty(usingSalaryRules)) {
            StringBuilder usingRulesInfo = new StringBuilder();
            for (IObjectData rule : usingSalaryRules) {
                if (usingRulesInfo.length() > 0) {
                    usingRulesInfo.append("、");
                }
                usingRulesInfo.append(rule.getName());
            }
            usingInfo.append("正在使用的工资规则: ").append(usingRulesInfo.toString()); // ignoreI18n
        }
        
        // 添加员工固定工资表使用信息
        if (CollectionUtils.isNotEmpty(usingEmployeeFixedSalaryDetails)) {
            if (usingInfo.length() > 0) {
                usingInfo.append("；"); // ignoreI18n
            }
            
            StringBuilder usingEmployeeFixedSalaryInfo = new StringBuilder();
            for (IObjectData detail : usingEmployeeFixedSalaryDetails) {
                if (usingEmployeeFixedSalaryInfo.length() > 0) {
                    usingEmployeeFixedSalaryInfo.append("、");
                }
                
                String employeeFixedSalaryName = getEmployeeFixedSalaryName(detail);
                usingEmployeeFixedSalaryInfo.append(employeeFixedSalaryName);
            }
            usingInfo.append("正在使用的员工固定工资表: ").append(usingEmployeeFixedSalaryInfo.toString()); // ignoreI18n
        }
        
        return usingInfo.toString();
    }

    /**
     * 获取员工固定工资表的显示名称
     *
     * @param employeeFixedSalaryDetail 员工固定工资明细
     * @return 员工固定工资表的显示名称
     */
    private static String getEmployeeFixedSalaryName(IObjectData employeeFixedSalaryDetail) {
        try {
            // 尝试从关联对象字段获取员工固定工资表的名称
            String employeeFixedSalaryId = employeeFixedSalaryDetail.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, String.class);
            if (StringUtils.isBlank(employeeFixedSalaryId)) {
                return "未知员工固定工资表"; //ignoreI18n
            }

            // 通过关联对象字段获取名称（使用__r后缀，这是原来的实现方式）
            Object refObject = employeeFixedSalaryDetail.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY + "__r");
            if (refObject instanceof String) {
                return (String) refObject;
            }

            // 如果无法获取名称，返回ID
            return "员工固定工资表(" + employeeFixedSalaryId + ")"; //ignoreI18n

        } catch (Exception e) {
            log.warn("获取员工固定工资表名称失败", e);
            return "未知员工固定工资表"; //ignoreI18n
        }
    }

    /**
     * 工资项使用状态信息
     */
    public static class SalaryItemUsageInfo {
        private final List<IObjectData> usingSalaryRules;
        private final List<IObjectData> usingEmployeeFixedSalaryDetails;
        
        public SalaryItemUsageInfo(List<IObjectData> usingSalaryRules, List<IObjectData> usingEmployeeFixedSalaryDetails) {
            this.usingSalaryRules = usingSalaryRules;
            this.usingEmployeeFixedSalaryDetails = usingEmployeeFixedSalaryDetails;
        }
        
        public boolean isUsed() {
            return CollectionUtils.isNotEmpty(usingSalaryRules) || CollectionUtils.isNotEmpty(usingEmployeeFixedSalaryDetails);
        }
        
        public boolean isUsedBySalaryRules() {
            return CollectionUtils.isNotEmpty(usingSalaryRules);
        }
        
        public boolean isUsedByEmployeeFixedSalary() {
            return CollectionUtils.isNotEmpty(usingEmployeeFixedSalaryDetails);
        }
        
        public List<IObjectData> getUsingSalaryRules() {
            return usingSalaryRules;
        }
        
        public List<IObjectData> getUsingEmployeeFixedSalaryDetails() {
            return usingEmployeeFixedSalaryDetails;
        }
        
        public String getUsageMessage() {
            return buildUsageMessage(usingSalaryRules, usingEmployeeFixedSalaryDetails);
        }
    }
}
