package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.service.RoleQueryService;
import com.facishare.crm.fmcg.wq.util.DescribeUtils;
import com.facishare.crm.fmcg.wq.util.LayoutUtils;
import com.facishare.crm.fmcg.wq.util.RoleNameConverter;
import com.facishare.crm.fmcg.wq.util.SalaryRuleFormulaUtil;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工资规则详情页控制器
 * 重写计算公式字段，将工资项按增减属性连接显示
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class SalaryRuleWebDetailController extends StandardWebDetailController {

  private SalaryItemDao salaryItemDao = SpringUtil.getContext().getBean(SalaryItemDao.class);
  private SalaryRuleDao salaryRuleDao = SpringUtil.getContext().getBean(SalaryRuleDao.class);
  private RoleQueryService roleQueryService = SpringUtil.getContext().getBean(RoleQueryService.class);
  private RoleClient roleClient = SpringUtil.getContext().getBean(RoleClient.class);

  @Override
  protected Result after(Arg arg, Result result) {
    log.info("SalaryRuleWebDetailController after method called");
    result = super.after(arg, result);

    try {
      String tenantId = controllerContext.getTenantId();

      // 添加角色虚拟字段到布局和对象描述
      addRoleVirtualFieldsToLayout(result);

      // 获取工资规则数据
      ObjectDataDocument dataDocument = result.getData();
      if (dataDocument == null) {
        log.warn("工资规则数据为空，跳过处理");
        return result;
      }

      ObjectDataExt salaryRuleExt = ObjectDataExt.of(dataDocument);

      // 设置角色虚拟字段的值
      dataDocument.put(SalaryRuleFields.APPLICABLE_ROLE_INTE_DISPLAY,
              RoleNameConverter.convertRoleIdsToNames(roleClient, tenantId,  salaryRuleExt.getDimensionValues(SalaryRuleFields.APPLICABLE_ROLE_INTE)).stream().collect(Collectors.joining(",")));
      dataDocument.put(SalaryRuleFields.APPLICABLE_OUT_ROLE_DISPLAY,
              RoleNameConverter.convertRoleIdsToNames(roleClient, tenantId,  salaryRuleExt.getDimensionValues(SalaryRuleFields.APPLICABLE_OUT_ROLE)).stream().collect(Collectors.joining(",")));

      // 重写计算公式字段并校验是否需要更新数据库
      validateAndUpdateCalculationFormula(tenantId, salaryRuleExt, dataDocument, result);

    } catch (Exception e) {
      log.error("处理工资规则详情页时发生异常", e);
      // 不影响正常显示，只记录错误日志
    }

    return result;
  }

  /**
   * 添加角色虚拟字段到布局和对象描述
   *
   * @param result 控制器结果
   */
  private void addRoleVirtualFieldsToLayout(Result result) {
    try {
      // 添加内部角色虚拟字段到布局（在适用企业(外部)字段之后）
      result.setLayout(
          LayoutUtils.replaceVirtualFieldInDetailLayout(
              result.getLayout(),
              SalaryRuleFields.APPLICABLE_ROLE_INTE,
              SalaryRuleFields.APPLICABLE_ROLE_INTE_DISPLAY
          )
      );

      // 添加外部角色虚拟字段到布局（在内部角色虚拟字段之后）
      result.setLayout(
          LayoutUtils.replaceVirtualFieldInDetailLayout(
              result.getLayout(),
              SalaryRuleFields.APPLICABLE_OUT_ROLE,
              SalaryRuleFields.APPLICABLE_OUT_ROLE_DISPLAY
          )
      );
      if (result.getDescribe() != null){
        // 添加内部角色虚拟字段到对象描述
        result.setDescribe(DescribeUtils.addSalaryRuleInternalRoleVirtualFieldToObjectDescribe(
            result.getDescribe(),
            SalaryRuleFields.APPLICABLE_ROLE_INTE_DISPLAY,
            describe.getFieldDescribe(SalaryRuleFields.APPLICABLE_ROLE_INTE).getLabel() //ignoreI18n
        ));

        // 添加外部角色虚拟字段到对象描述
        result.setDescribe(DescribeUtils.addSalaryRuleExternalRoleVirtualFieldToObjectDescribe(
            result.getDescribe(),
            SalaryRuleFields.APPLICABLE_OUT_ROLE_DISPLAY,
            describe.getFieldDescribe(SalaryRuleFields.APPLICABLE_OUT_ROLE).getLabel() //ignoreI18n
        ));

        log.info("成功添加工资规则角色虚拟字段到布局和对象描述");
      }

    } catch (Exception e) {
      log.error("添加角色虚拟字段到布局失败", e);
    }
  }

  /**
   * 校验并更新计算公式字段
   * 比较数据库中存储的 calculation_formula 值与重新计算得出的值，如果不一致则更新数据库中的值
   *
   * @param tenantId 租户ID
   * @param salaryRuleExt 工资规则扩展对象
   * @param dataDocument 数据文档
   * @param result 控制器结果
   */
  private void validateAndUpdateCalculationFormula(String tenantId, ObjectDataExt salaryRuleExt,
                                                   ObjectDataDocument dataDocument, Result result) {
    try {
      // 使用工具类重新计算公式
      String newCalculationFormula = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
          tenantId, salaryRuleExt, salaryItemDao);

      // 获取数据库中存储的公式
      String existingFormula = salaryRuleExt.getStringValue(SalaryRuleFields.CALCULATION_FORMULA);

      // 比较公式是否一致
      boolean formulaChanged = !isFormulaEqual(existingFormula, newCalculationFormula);

      if (formulaChanged) {
        log.info("检测到计算公式不一致，需要更新。原公式: [{}], 新公式: [{}]", existingFormula, newCalculationFormula);

        // 更新数据库中的公式
        updateCalculationFormulaInDatabase(tenantId, salaryRuleExt.getId(), newCalculationFormula);

        // 更新返回给前端的数据
        if (StringUtils.isNotBlank(newCalculationFormula)) {
          dataDocument.put(SalaryRuleFields.CALCULATION_FORMULA, newCalculationFormula);
          result.setData(dataDocument);
        }

        log.info("工资规则计算公式已更新: {}", newCalculationFormula);
      } else {
        // 即使公式一致，也要确保前端显示的是最新计算的公式
        if (StringUtils.isNotBlank(newCalculationFormula)) {
          dataDocument.put(SalaryRuleFields.CALCULATION_FORMULA, newCalculationFormula);
          result.setData(dataDocument);
        }
        log.debug("工资规则计算公式无需更新: {}", newCalculationFormula);
      }

    } catch (Exception e) {
      log.error("校验和更新计算公式时发生异常", e);
      // 不影响正常显示，只记录错误日志
    }
  }

  /**
   * 比较两个公式是否相等
   * 处理null值和空字符串的情况
   *
   * @param formula1 公式1
   * @param formula2 公式2
   * @return 是否相等
   */
  private boolean isFormulaEqual(String formula1, String formula2) {
    // 将null和空字符串都视为相等
    String f1 = StringUtils.isBlank(formula1) ? "" : formula1.trim();
    String f2 = StringUtils.isBlank(formula2) ? "" : formula2.trim();
    return f1.equals(f2);
  }

  /**
   * 重新查询并覆盖适用外部角色字段（互联角色）
   */
  private void refreshApplicableOutRoleField(String tenantId, ObjectDataExt ruleExt, ObjectDataDocument dataDocument) {
    try {
      // 只处理适用外部角色字段（互联角色）
      refreshRoleField(tenantId, ruleExt, dataDocument, SalaryRuleFields.APPLICABLE_OUT_ROLE, true);

    } catch (Exception e) {
      log.warn("重新查询适用外部角色字段失败", e);
    }
  }

  /**
   * 重新查询单个角色字段
   */
  private void refreshRoleField(String tenantId, ObjectDataExt ruleExt, ObjectDataDocument dataDocument,
                               String fieldName, boolean isExternal) {
    try {
      // 获取角色ID字符串
      String roleIdsStr = ruleExt.get(fieldName, String.class);

      if (StringUtils.isBlank(roleIdsStr)) {
        // 如果角色ID为空，设置为空字符串
        dataDocument.put(fieldName, "");
        log.debug("角色字段 {} 为空，设置为空字符串", fieldName);
        return;
      }

      // 解析角色ID列表
      List<String> roleIds = parseRoleIds(roleIdsStr);

      if (CollectionUtils.isEmpty(roleIds)) {
        dataDocument.put(fieldName, "");
        log.debug("角色字段 {} 解析后为空，设置为空字符串", fieldName);
        return;
      }

      // 查询角色名称
      List<String> roleNames = queryRoleNames(tenantId, roleIds, isExternal);

      if (CollectionUtils.isNotEmpty(roleNames)) {
        // 将角色名称列表转换为显示字符串
        String roleDisplayValue = String.join(", ", roleNames);

        // 覆盖角色字段值
        dataDocument.put(fieldName, roleDisplayValue);

        log.info("成功更新工资规则角色字段: fieldName={}, roleNames={}", fieldName, roleDisplayValue);
      } else {
        // 如果没有查询到角色，设置为空字符串
        dataDocument.put(fieldName, "");
        log.debug("未查询到角色信息: fieldName={}, roleIds={}", fieldName, roleIds);
      }

    } catch (Exception e) {
      log.warn("重新查询角色字段失败: fieldName={}", fieldName, e);
      // 查询失败时设置为空字符串，避免显示原始角色ID
      dataDocument.put(fieldName, "");
    }
  }

  /**
   * 解析角色ID字符串
   */
  private List<String> parseRoleIds(String roleIdsStr) {
    if (StringUtils.isBlank(roleIdsStr)) {
      return new ArrayList<>();
    }

    try {
      // 尝试按逗号分割
      if (roleIdsStr.contains(",")) {
        return Arrays.asList(roleIdsStr.split(","));
      }

      // 尝试按分号分割
      if (roleIdsStr.contains(";")) {
        return Arrays.asList(roleIdsStr.split(";"));
      }

      // 单个角色ID
      return Arrays.asList(roleIdsStr.trim());

    } catch (Exception e) {
      log.warn("解析角色ID字符串失败: {}", roleIdsStr, e);
      return new ArrayList<>();
    }
  }

  /**
   * 查询角色名称
   */
  private List<String> queryRoleNames(String tenantId, List<String> roleIds, boolean isExternal) {
    try {
      // 清理角色ID列表，移除空值
      List<String> cleanRoleIds = roleIds.stream()
          .filter(StringUtils::isNotBlank)
          .map(String::trim)
          .collect(Collectors.toList());

      if (CollectionUtils.isEmpty(cleanRoleIds)) {
        return new ArrayList<>();
      }

      // 将角色ID转换为角色名称
      return RoleNameConverter.convertRoleIdsToNames(roleClient, tenantId, cleanRoleIds);

    } catch (Exception e) {
      log.warn("查询角色名称失败: tenantId={}, roleIds={}, isExternal={}", tenantId, roleIds, isExternal, e);
      return new ArrayList<>();
    }
  }

  /**
   * 更新数据库中的计算公式
   *
   * @param tenantId 租户ID
   * @param salaryRuleId 工资规则ID
   * @param newFormula 新的计算公式
   */
  private void updateCalculationFormulaInDatabase(String tenantId, String salaryRuleId, String newFormula) {
    try {
      // 获取工资规则对象
      IObjectData salaryRule = salaryRuleDao.getById(tenantId, salaryRuleId);
      if (salaryRule == null) {
        log.warn("未找到工资规则，无法更新计算公式，ID: {}", salaryRuleId);
        return;
      }

      // 更新计算公式字段
      salaryRule.set(SalaryRuleFields.CALCULATION_FORMULA, newFormula);

      // 保存到数据库
      salaryRuleDao.update(User.systemUser(tenantId), salaryRule);

      log.info("已更新数据库中的计算公式，工资规则ID: {}, 新公式: {}", salaryRuleId, newFormula);

    } catch (Exception e) {
      log.error("更新数据库中的计算公式失败，工资规则ID: {}", salaryRuleId, e);
    }
  }

  /**
   * 处理角色字段选项
   *
   * @param result 控制器结果
   * @param tenantId 租户ID
   */
  private void processRoleFieldOptions(Result result, String tenantId) {
    try {
      // 获取对象描述
      ObjectDescribeDocument objectDescribe = result.getDescribe();
      if (objectDescribe == null) {
        log.warn("对象描述为空，跳过角色选项处理");
        return;
      }

      ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
      Map<String, IFieldDescribe> fieldDescribeMap = objectDescribeExt.getFieldDescribeMap();

      // 处理适用内部角色字段
      processRoleField(fieldDescribeMap, SalaryRuleFields.APPLICABLE_ROLE_INTE, tenantId);

      // 处理适用外部角色字段
      processRoleField(fieldDescribeMap, SalaryRuleFields.APPLICABLE_OUT_ROLE, tenantId);

      // 更新对象描述
      result.setDescribe(ObjectDescribeDocument.of(objectDescribeExt));

      log.info("工资规则详情页角色选项处理完成");

    } catch (Exception e) {
      log.error("处理工资规则详情页角色选项时发生异常", e);
    }
  }

  /**
   * 处理角色字段，添加动态选项
   *
   * @param fieldDescribeMap 字段描述映射
   * @param fieldApiName 字段API名称
   * @param tenantId 租户ID
   */
  private void processRoleField(Map<String, IFieldDescribe> fieldDescribeMap, String fieldApiName, String tenantId) {
    try {
      IFieldDescribe fieldDescribe = fieldDescribeMap.get(fieldApiName);
      if (fieldDescribe != null) {
        log.info("开始处理角色字段: {}", fieldApiName);
        roleQueryService.addRoleOptionsToFieldDescribe(fieldDescribe, fieldApiName, tenantId);
        log.info("角色字段 {} 处理完成", fieldApiName);
      } else {
        log.debug("未找到字段描述: {}", fieldApiName);
      }
    } catch (Exception e) {
      log.error("处理角色字段 {} 时发生异常", fieldApiName, e);
    }
  }

  /**
   * 根据工资项构建计算公式
   * 按照增减属性将工资项名称连接起来，格式如：基本工资 + 绩效奖金 - 社保扣款 - 个税
   * 
   * @param tenantId 租户ID
   * @param salaryRuleExt 工资规则扩展对象
   * @return 构建的计算公式字符串
   */
  private String buildCalculationFormulaFromSalaryItems(String tenantId, ObjectDataExt salaryRuleExt) {
    try {
      // 获取工资项ID列表
      List<String> salaryItemIds = getSalaryItemIds(salaryRuleExt);
      if (CollectionUtils.isEmpty(salaryItemIds)) {
        log.debug("工资规则未配置工资项，无法构建计算公式");
        return null;
      }

      // 查询工资项详情
      List<IObjectData> salaryItems = salaryItemDao.getbyIds(tenantId, salaryItemIds);
      if (CollectionUtils.isEmpty(salaryItems)) {
        log.warn("未找到工资项详情，工资项ID列表: {}", salaryItemIds);
        return null;
      }

      // 按照工资项ID的顺序构建公式
      return buildFormulaString(salaryItems, salaryItemIds);

    } catch (Exception e) {
      log.error("构建计算公式时发生异常", e);
      return null;
    }
  }

  /**
   * 从工资规则中获取工资项ID列表
   * 
   * @param salaryRuleExt 工资规则扩展对象
   * @return 工资项ID列表
   */
  private List<String> getSalaryItemIds(ObjectDataExt salaryRuleExt) {
    Object salaryItemsObj = salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM);
    if (salaryItemsObj == null) {
      return new ArrayList<>();
    }

    List<String> salaryItemIds = new ArrayList<>();

    // 处理不同的数据类型
    if (salaryItemsObj instanceof List) {
      // 如果是List类型，直接转换
      List<?> itemList = (List<?>) salaryItemsObj;
      for (Object item : itemList) {
        if (item != null) {
          salaryItemIds.add(item.toString());
        }
      }
    } else if (salaryItemsObj instanceof String[]) {
      // 如果是数组类型，转换为List
      String[] itemArray = (String[]) salaryItemsObj;
      for (String item : itemArray) {
        if (StringUtils.isNotBlank(item)) {
          salaryItemIds.add(item);
        }
      }
    } else {
      // 如果是字符串类型，按逗号分割
      String itemsStr = salaryItemsObj.toString();
      if (StringUtils.isNotBlank(itemsStr)) {
        String[] itemArray = itemsStr.split(",");
        for (String item : itemArray) {
          if (StringUtils.isNotBlank(item.trim())) {
            salaryItemIds.add(item.trim());
          }
        }
      }
    }

    return salaryItemIds;
  }

  /**
   * 构建公式字符串
   * 按照工资项ID的顺序，根据增减属性连接工资项名称
   * 
   * @param salaryItems 工资项列表
   * @param salaryItemIds 工资项ID顺序列表
   * @return 构建的公式字符串
   */
  private String buildFormulaString(List<IObjectData> salaryItems, List<String> salaryItemIds) {
    // 创建工资项ID到工资项对象的映射
    java.util.Map<String, IObjectData> salaryItemMap = new java.util.HashMap<>();
    for (IObjectData salaryItem : salaryItems) {
      salaryItemMap.put(salaryItem.getId(), salaryItem);
    }

    StringBuilder formulaBuilder = new StringBuilder();
    boolean isFirst = true;

    // 按照工资项ID的顺序构建公式
    for (String salaryItemId : salaryItemIds) {
      IObjectData salaryItem = salaryItemMap.get(salaryItemId);
      if (salaryItem == null) {
        log.warn("未找到工资项，ID: {}", salaryItemId);
        continue;
      }

      String salaryItemName = salaryItem.getName();
      if (StringUtils.isBlank(salaryItemName)) {
        log.warn("工资项名称为空，ID: {}", salaryItemId);
        continue;
      }

      // 获取增减属性
      String incrementDecrementAttrib = salaryItem.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class);
      
      // 确定符号
      String operator = getOperatorByAttribute(incrementDecrementAttrib, isFirst);
      
      // 构建公式片段
      if (isFirst) {
        // 第一个工资项：如果是扣减项需要加减号，应发项不需要符号
        if (SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
          formulaBuilder.append("-").append(salaryItemName);
        } else {
          formulaBuilder.append(salaryItemName);
        }
        isFirst = false;
      } else {
        formulaBuilder.append(" ").append(operator).append(" ").append(salaryItemName);
      }
    }

    String formula = formulaBuilder.toString();
    log.debug("构建的计算公式: {}", formula);
    return formula;
  }

  /**
   * 根据增减属性获取操作符
   * 注意：此方法仅用于非第一个工资项，第一个工资项的符号处理在buildFormulaString中单独处理
   *
   * @param incrementDecrementAttrib 增减属性
   * @param isFirst 是否是第一个工资项（此方法中应该总是false）
   * @return 操作符（+或-）
   */
  private String getOperatorByAttribute(String incrementDecrementAttrib, boolean isFirst) {
    // 此方法仅用于非第一个工资项
    if (isFirst) {
      return "";
    }

    // 根据增减属性确定操作符
    if (SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1.equals(incrementDecrementAttrib)) {
      // 应发项，使用加号
      return "+";
    } else if (SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
      // 扣减项，使用减号
      return "-";
    } else {
      // 未知属性，默认使用加号
      log.warn("未知的增减属性: {}", incrementDecrementAttrib);
      return "+";
    }
  }
}
