package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.metadata.importobject.ImportType;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 员工固定工资表
 * @author: zhangsm
 * @create: 2025-07-08 16:42
 **/
public class EmployeeFixedSalaryDetailImportObjectController extends StandardImportObjectController {


    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        after.getImportObject().setSupportType(ImportType.UNSUPPORT_UPDATE_IMPORT);
        return after;
    }

}
