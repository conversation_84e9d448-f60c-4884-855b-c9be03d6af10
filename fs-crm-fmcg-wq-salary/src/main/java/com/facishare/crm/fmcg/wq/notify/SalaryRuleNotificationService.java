package com.facishare.crm.fmcg.wq.notify;

import java.util.List;

import com.facishare.paas.metadata.api.IObjectData;

/**
 * 工资规则通知服务接口
 * <p>
 * 该接口定义了工资规则相关的消息通知方法，按业务场景分为五种情况：
 * 1. 新建工资规则后通知适用范围内未创建员工固定工资表的员工
 * 2. 组织架构新增员工后通知需要创建固定工资表的情况
 * 3. 检查并通知定薪方式不一致的员工
 * 4. 员工固定工资表有多规则适配时的通知
 * 5. 规则被移出时的通知
 * </p>
 */
public interface SalaryRuleNotificationService {

    // ==================== 核心通知方法 ====================

    /**
     * 情况一：新建工资规则后，通知适用范围内没有创建员工固定工资表的员工
     * 推送文案：基本工资规则【工资规则】适用范围下员工张三未创建【员工固定工资表】，请尽快创建，创建后方可参与工资计算
     *
     * @param tenantId 租户ID
     * @param salaryRuleId 工资规则ID
     * @return 是否发送了通知
     */
    boolean notifyMissingEmployeeFixedSalaryForNewRule(String tenantId, String salaryRuleId);

    /**
     * 情况二：组织架构新增员工后，通知需要创建固定工资表的情况
     * 推送文案："销售部"新增了员工张三，因该员工有生效的【工资规则】，请尽快为新员工创建【员工固定工资表】，方可参与工资计算
     *
     * @param tenantId 租户ID
     * @param employeeIds 新增员工ID列表
     * @param isExternal 是否外部员工
     * @param scope 员工新增的范围（如："销售部"、"经理角色"等）
     * @return 是否发送了通知
     */
    boolean notifyNewEmployeeNeedsFixedSalary(String tenantId, List<String> employeeIds, boolean isExternal, String scope);

    /**
     * 情况三：定薪方式不一致时的通知
     * 推送文案：员工张三【员工固定工资表】与适用的【工资规则】定薪方式不一致，将无法参与工资计算，请尽快调整
     * 筛选条件：定薪方式不一致且当前无规则的员工
     *
     * @param tenantId 租户ID
     * @param employeeIds 员工ID列表
     * @param salaryRuleId 适用的工资规则ID
     * @param ruleSalaryMethod 工资规则的定薪方式
     * @return 是否发送了通知
     */
    boolean notifySalaryMethodMismatch(String tenantId, List<String> employeeIds, String salaryRuleId, String ruleSalaryMethod);

    /**
     * 情况四：员工固定工资表有多规则适配时的通知
     * 推送文案：员工张三，因该员工有多个生效的【工资规则】，请尽快为该【员工固定工资表】调整规则，方可参与工资计算
     *
     * @param tenantId 租户ID
     * @param employeeIds 员工ID列表
     * @param isExternal 是否外部员工
     * @return 是否发送了通知
     */
    boolean notifyMultipleRulesMatch(String tenantId, List<String> employeeIds, boolean isExternal);

    /**
     * 情况五：规则被移出时的通知
     * 推送文案：【员工固定工资表】张三，基本工资规则被移出,请尽快为该【员工固定工资表】调整规则，方可参与工资计算
     *
     * @param tenantId 租户ID
     * @param employeeFixedSalaries 员工固定工资表列表
     * @param removedRuleName 被移出的规则名称
     * @return 是否发送了通知
     */
    boolean notifyRuleRemoved(String tenantId, List<IObjectData> employeeFixedSalaries, String removedRuleName);


}
