package com.facishare.crm.fmcg.wq.model.api;

import com.facishare.crm.fmcg.wq.model.SalaryItem;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 获取日工资数据接口（返回日历数据和当天的详细薪资数据）
 */
public interface DailySalaryQuery {
  @Data
  @ToString
  class Arg implements Serializable {
    /**
     * 员工ID
     */
    @JSONField(name = "employee_id")
    @JsonProperty("employee_id")
    @SerializedName("employee_id")
    private String employeeId;
    /**
     * 查询日期，格式：yyyy-MM-dd（可选，如果提供则返回指定日期的详细数据）
     */
    @JSONField(name = "query_date")
    @JsonProperty("query_date")
    @SerializedName("query_date")
    private String queryDate;
    /**
     * 月 yyyy-MM 返回整月总计数据 (可选，如果提供则返回指定月份的整月总计数据)
     */
    @JSONField(name = "month")
    @JsonProperty("month")
    @SerializedName("month")
    private String month;
  }
  
  @Data
  @ToString
  class Result implements Serializable {
   
    
    
    /**
     * 日历数据，包含每天的工资信息
     */
    @JSONField(name = "calendar_days")
    @JsonProperty("calendar_days")
    @SerializedName("calendar_days")
    private List<CalendarDay> calendarDays;
    
    /**
     * 当前选中日期的详细薪资数据
     */
    @JSONField(name = "current_day_detail")
    @JsonProperty("current_day_detail")
    @SerializedName("current_day_detail")
    private DailySalaryDetail currentDayDetail;
  }
  
  @Data
  class CalendarDay {
    /**
     * 日期，格式：yyyy-MM-dd
     */
    @JSONField(name = "date")
    @JsonProperty("date")
    @SerializedName("date")
    private String date;
    /**
     * 日期显示，如"1"表示1号
     */
    @JSONField(name = "day_display")
    @JsonProperty("day_display")
    @SerializedName("day_display")
    private String dayDisplay;
    /**
     * 星期几，0-6表示周日到周六
     */
    @JSONField(name = "week_day")
    @JsonProperty("week_day")
    @SerializedName("week_day")
    private Integer weekDay;
    
    
    /**
     * 是否有薪资数据
     */
    @JSONField(name = "has_salary_data")
    @JsonProperty("has_salary_data")
    @SerializedName("has_salary_data")
    private Boolean hasSalaryData;
    
    /**
     * 当天薪资金额
     */
    @JSONField(name = "amount")
    @JsonProperty("amount")
    @SerializedName("amount")
    private String amount;

    /**
     * 是否是当前选中日期
     */
    @JSONField(name = "is_selected")
    @JsonProperty("is_selected")
    @SerializedName("is_selected")
    private Boolean isSelected;

    /**
     * 是否是当前日期
     */
    @JSONField(name = "is_today")
    @JsonProperty("is_today")
    @SerializedName("is_today")
    private Boolean isToday;
  }
  
  @Data
  class DailySalaryDetail {
    /**
     * 日期，格式：yyyy-MM-dd
     */
    @JSONField(name = "date")
    @JsonProperty("date")
    @SerializedName("date")
    private String date;

    
    /**
     * 薪资项目列表
     */
    @JSONField(name = "salary_items")
    @JsonProperty("salary_items")
    @SerializedName("salary_items")
    private List<SalaryItem> salaryItems;
    
    /**
     * 总金额
     */
    @JSONField(name = "total_amount")
    @JsonProperty("total_amount")
    @SerializedName("total_amount")
    private String totalAmount;
  }
}
