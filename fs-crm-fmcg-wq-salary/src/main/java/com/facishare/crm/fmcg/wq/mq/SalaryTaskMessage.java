package com.facishare.crm.fmcg.wq.mq;

import io.protostuff.LinkedBuffer;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.Tag;
import io.protostuff.runtime.RuntimeSchema;
import lombok.*;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-06-30 11:39
 **/
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalaryTaskMessage {

    @Tag(1)
    private String tenantId = "";
    @Tag(2)
    private String ea = "";
    @Tag(3)
    private String dateStr = "";//日期
    @Tag(4)
    private String salaryRuleId = "";//薪资规则id
    /**
     * 开始时间
     */
    @Tag(5)
    private String startDateStr = "";
    @Tag(6)
    private String endDateStr = "";
    /**
     * 0 生成工资条明细
     * 1 生成工资条
     */
    @Tag(7)
    private int flag = 0;

    public byte[] toProto() {
        Schema schema = RuntimeSchema.getSchema(getClass());
        return ProtobufIOUtil.toByteArray(this, schema, LinkedBuffer.allocate(256));
    }

    public void fromProto(byte[] bytes) {
        Schema schema = RuntimeSchema.getSchema(getClass());
        ProtobufIOUtil.mergeFrom(bytes, this, schema);
    }

}