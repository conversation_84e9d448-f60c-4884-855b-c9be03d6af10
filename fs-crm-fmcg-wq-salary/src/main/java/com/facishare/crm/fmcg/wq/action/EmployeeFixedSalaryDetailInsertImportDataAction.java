package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryDetailFields;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDetailDao;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.service.SalaryImportValidationService;
import com.facishare.crm.fmcg.wq.util.SalaryRecordTypeUtil;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 员工固定工资明细导入Action
 * 
 * 功能：
 * 1. 工资项验证（工资项必须存在且有效）
 * 2. 金额验证（金额不能为负数）
 * 3. 增减属性验证
 * 4. 员工固定工资表关联验证
 * 5. 业务类型设置（内部员工vs外部员工）
 * 
 * 导入处理流程：
 * 1. customInit - 字段转换和初始化
 * 2. customValidate - 数据校验
 * 3. customDefaultValue - 业务默认值赋值
 * 4. importData - 写入数据库
 * 
 * <AUTHOR>
 * @create 2025-07-08
 */
@Slf4j
public class EmployeeFixedSalaryDetailInsertImportDataAction extends StandardInsertImportDataAction {

  private SalaryImportValidationService validationService = SpringUtil.getContext().getBean(SalaryImportValidationService.class);

  private EmployeeFixedSalaryDao employeeFixedSalaryDao = SpringUtil.getContext().getBean(EmployeeFixedSalaryDao.class);
  private EmployeeFixedSalaryDetailDao employeeFixedSalaryDetailDao = SpringUtil.getContext().getBean(EmployeeFixedSalaryDetailDao.class);
  private SalaryItemDao salaryItemDao = SpringUtil.getContext().getBean(SalaryItemDao.class);

  // 缓存工资项信息
  private Map<String, IObjectData> salaryItemMap;
  // 缓存员工固定工资表信息
  private Map<String, IObjectData> employeeFixedSalaryMap;
  // 缓存已存在的员工固定工资明细（按员工+工资项组合）
  private Map<String, IObjectData> existingEmployeeSalaryItemMap;

  @Override
  protected void customInit(List<ImportData> dataList) {
    // 设置金额字段为必填
    objectDescribeExt.getFieldDescribeSilently(EmployeeFixedSalaryDetailFields.AMOUNT).ifPresent(field -> {
      field.setRequired(true);
    });

    // 设置工资项字段为必填
    objectDescribeExt.getFieldDescribeSilently(EmployeeFixedSalaryDetailFields.SALARY_ITEM).ifPresent(field -> {
      field.setRequired(true);
    });

    // 设置员工固定工资表字段为必填
    objectDescribeExt.getFieldDescribeSilently(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY).ifPresent(field -> {
      field.setRequired(true);
    });

    super.customInit(dataList);
    log.info("员工固定工资明细导入初始化完成，数据行数: {}", dataList.size());
  }

  @Override
  protected void customValidate(List<ImportData> dataList) {
    super.customValidate(dataList);

    // 加载工资项信息
    loadSalaryItems(dataList);

    // 加载员工固定工资表信息
    loadEmployeeFixedSalaries(dataList);

    // 加载已存在的员工固定工资明细
    loadExistingEmployeeSalaryDetails(dataList);

    // 验证工资项
    List<ImportError> salaryItemErrors = validationService.validateSalaryItems(dataList, salaryItemMap, employeeFixedSalaryMap);
    mergeErrorList(salaryItemErrors);

    // 验证金额
    List<ImportError> amountErrors = validationService.validateAmounts(dataList);
    mergeErrorList(amountErrors);

    // 验证员工固定工资表关联
    validateEmployeeFixedSalaryRelations(dataList);

    // 验证员工工资项唯一性
    validateEmployeeSalaryItemUniqueness(dataList);

    log.info("员工固定工资明细导入验证完成");
  }

  @Override
  protected void customDefaultValue(List<IObjectData> validList) {
    super.customDefaultValue(validList);

    // 设置业务类型
//    setRecordTypes(validList);

    log.info("员工固定工资明细导入默认值设置完成，有效数据行数: {}", validList.size());
  }

  /**
   * 加载工资项信息
   */
  private void loadSalaryItems(List<ImportData> dataList) {
    Set<String> salaryItemIds = dataList.stream()
        .map(data -> getStringValue(data.getData(), EmployeeFixedSalaryDetailFields.SALARY_ITEM))
        .filter(StringUtils::isNotBlank)
        .collect(Collectors.toSet());

    if (CollectionUtils.isNotEmpty(salaryItemIds)) {
      List<IObjectData> salaryItems = salaryItemDao.getByIds(actionContext.getTenantId(), Lists.newArrayList(salaryItemIds));
      salaryItemMap = salaryItems.stream()
          .collect(Collectors.toMap(item -> item.getId(), item -> item));
    } else {
      salaryItemMap = Maps.newHashMap();
    }

    log.info("加载工资项信息完成，工资项数: {}", salaryItemMap.size());
  }

  /**
   * 加载员工固定工资表信息
   */
  private void loadEmployeeFixedSalaries(List<ImportData> dataList) {
    Set<String> employeeFixedSalaryIds = dataList.stream()
        .map(data -> getStringValue(data.getData(), EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY))
        .filter(StringUtils::isNotBlank)
        .collect(Collectors.toSet());

    if (CollectionUtils.isNotEmpty(employeeFixedSalaryIds)) {
      List<IObjectData> employeeFixedSalaries = employeeFixedSalaryDao.getByIds(actionContext.getTenantId(), Lists.newArrayList(employeeFixedSalaryIds));
      employeeFixedSalaryMap = employeeFixedSalaries.stream()
          .collect(Collectors.toMap(item -> item.getId(), item -> item));
    } else {
      employeeFixedSalaryMap = Maps.newHashMap();
    }

    log.info("加载员工固定工资表信息完成，记录数: {}", employeeFixedSalaryMap.size());
  }

  /**
   * 加载已存在的员工固定工资明细（与现有数据库数据进行对比验证）
   * 使用批量查询提高性能
   */
  private void loadExistingEmployeeSalaryDetails(List<ImportData> dataList) {
    // 收集所有员工固定工资表ID
    Set<String> employeeFixedSalaryIds = dataList.stream()
        .map(data -> getStringValue(data.getData(), EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY))
        .filter(StringUtils::isNotBlank)
        .collect(Collectors.toSet());

    existingEmployeeSalaryItemMap = Maps.newHashMap();

    if (CollectionUtils.isNotEmpty(employeeFixedSalaryIds)) {
      log.info("开始批量加载员工固定工资明细，涉及员工固定工资表数: {}", employeeFixedSalaryIds.size());

      try {
        // 使用批量查询方法，一次性查询所有相关的员工固定工资明细
        List<IObjectData> allExistingDetails = employeeFixedSalaryDetailDao.getByEmployeeFixedSalaryIds(
            actionContext.getTenantId(), Lists.newArrayList(employeeFixedSalaryIds));

        log.info("批量查询到现有明细总数: {}", allExistingDetails.size());

        // 按员工固定工资表ID+工资项ID组合作为key缓存现有明细
        for (IObjectData detail : allExistingDetails) {
          String employeeFixedSalaryId = getStringValue(detail, EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY);
          String salaryItemId = getStringValue(detail, EmployeeFixedSalaryDetailFields.SALARY_ITEM);

          if (StringUtils.isNotBlank(employeeFixedSalaryId) && StringUtils.isNotBlank(salaryItemId)) {
            String key = employeeFixedSalaryId + "#" + salaryItemId;
            existingEmployeeSalaryItemMap.put(key, detail);

            log.debug("缓存现有明细: 员工固定工资表ID={}, 工资项ID={}, key={}",
                employeeFixedSalaryId, salaryItemId, key);
          }
        }

        log.info("批量加载员工固定工资明细完成，缓存记录数: {}, 涉及员工数: {}",
            existingEmployeeSalaryItemMap.size(), employeeFixedSalaryIds.size());

      } catch (Exception e) {
        log.error("批量查询员工固定工资明细失败，涉及员工固定工资表ID: {}", employeeFixedSalaryIds, e);
        // 如果批量查询失败，可以考虑降级为逐个查询，或者抛出异常
        throw new RuntimeException("加载现有员工固定工资明细失败", e); //ignoreI18n
      }
    } else {
      log.info("没有需要验证的员工固定工资表，跳过明细加载");
    }
  }

  /**
   * 验证工资项
   */
  private void validateSalaryItems(List<ImportData> dataList) {
    List<ImportError> errorList = Lists.newArrayList();

    for (ImportData data : dataList) {
      IObjectData objectData = data.getData();
      String salaryItemId = getStringValue(objectData, EmployeeFixedSalaryDetailFields.SALARY_ITEM);
      String employeeFixedSalaryId = getStringValue(objectData, EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY);

      if (StringUtils.isBlank(salaryItemId)) {
        errorList.add(new ImportError(data.getRowNo(), "请选择工资项")); //ignoreI18n
        continue;
      }

      // 检查工资项是否存在
      if (!salaryItemMap.containsKey(salaryItemId)) {
        errorList.add(new ImportError(data.getRowNo(), "工资项不存在或已被删除")); //ignoreI18n
        continue;
      }

      // 检查工资项是否启用
      IObjectData salaryItem = salaryItemMap.get(salaryItemId);
      String enabledStatus = getStringValue(salaryItem, SalaryItemFields.ENABLED_STATUS);
      if (!SalaryItemFields.ENABLED_STATUS_Options_true.equals(enabledStatus)) {
        errorList.add(new ImportError(data.getRowNo(), "选择的工资项已禁用，请重新选择")); //ignoreI18n
        continue;
      }

      // 验证工资项与员工定薪方式的一致性
      if (StringUtils.isNotBlank(employeeFixedSalaryId) && employeeFixedSalaryMap.containsKey(employeeFixedSalaryId)) {
        IObjectData employeeFixedSalary = employeeFixedSalaryMap.get(employeeFixedSalaryId);
        String employeeSalaryMethod = getStringValue(employeeFixedSalary, EmployeeFixedSalaryFields.SALARY_METHOD);
        String salaryItemMethod = getStringValue(salaryItem, SalaryItemFields.SALARY_METHOD);

        if (StringUtils.isNotBlank(employeeSalaryMethod) && StringUtils.isNotBlank(salaryItemMethod)) {
          if (!employeeSalaryMethod.equals(salaryItemMethod)) {
            errorList.add(new ImportError(data.getRowNo(),
                MessageFormat.format("工资项的定薪方式({0})与员工的定薪方式({1})不一致", //ignoreI18n
                    salaryItemMethod, employeeSalaryMethod)));
            continue;
          }
        }
      }
    }

    mergeErrorList(errorList);
    log.info("工资项验证完成，错误数: {}", errorList.size());
  }

  /**
   * 验证金额
   */
  private void validateAmounts(List<ImportData> dataList) {
    List<ImportError> errorList = Lists.newArrayList();

    for (ImportData data : dataList) {
      IObjectData objectData = data.getData();
      Object amountObj = objectData.get(EmployeeFixedSalaryDetailFields.AMOUNT);

      if (amountObj == null) {
        errorList.add(new ImportError(data.getRowNo(), "请输入金额")); //ignoreI18n
        continue;
      }

      try {
        BigDecimal amount = new BigDecimal(amountObj.toString());
        
        // 检查金额不能为负数
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
          errorList.add(new ImportError(data.getRowNo(), "金额不能为负数")); //ignoreI18n
          continue;
        }

        // 检查金额精度（保留2位小数）
        if (amount.scale() > 2) {
          errorList.add(new ImportError(data.getRowNo(), "金额最多保留2位小数")); //ignoreI18n
        }
      } catch (NumberFormatException e) {
        errorList.add(new ImportError(data.getRowNo(), "金额格式不正确")); //ignoreI18n
      }
    }

    mergeErrorList(errorList);
    log.info("金额验证完成，错误数: {}", errorList.size());
  }

  /**
   * 验证员工固定工资表关联
   */
  private void validateEmployeeFixedSalaryRelations(List<ImportData> dataList) {
    List<ImportError> errorList = Lists.newArrayList();

    for (ImportData data : dataList) {
      IObjectData objectData = data.getData();
      String employeeFixedSalaryId = getStringValue(objectData, EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY);

      if (StringUtils.isBlank(employeeFixedSalaryId)) {
        errorList.add(new ImportError(data.getRowNo(), "请选择员工固定工资表")); //ignoreI18n
        continue;
      }

      // 检查员工固定工资表是否存在
      if (!employeeFixedSalaryMap.containsKey(employeeFixedSalaryId)) {
        errorList.add(new ImportError(data.getRowNo(), "员工固定工资表不存在或已被删除")); //ignoreI18n
      }
    }

    mergeErrorList(errorList);
    log.info("员工固定工资表关联验证完成，错误数: {}", errorList.size());
  }

  /**
   * 设置业务类型
   */
  private void setRecordTypes(List<IObjectData> validList) {
    for (IObjectData objectData : validList) {
      String employeeFixedSalaryId = getStringValue(objectData, EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY);

      if (StringUtils.isNotBlank(employeeFixedSalaryId) && employeeFixedSalaryMap.containsKey(employeeFixedSalaryId)) {
        IObjectData employeeFixedSalary = employeeFixedSalaryMap.get(employeeFixedSalaryId);

        String internalEmployeeId = getStringValue(employeeFixedSalary, "employee"); // 员工(内部)
        String externalEmployeeId = getStringValue(employeeFixedSalary, "employee_external"); // 员工(外部)

        // 根据员工类型设置业务类型
        String recordType = SalaryRecordTypeUtil.getSalaryDetailDataRecordType(internalEmployeeId, externalEmployeeId);
        objectData.setRecordType(recordType);

        log.debug("设置员工固定工资明细业务类型: {}, 内部员工ID: {}, 外部员工ID: {}",
            recordType, internalEmployeeId, externalEmployeeId);
      }
    }

    log.info("业务类型设置完成，处理记录数: {}", validList.size());
  }

  /**
   * 验证员工工资项唯一性（与现有数据库数据进行验证）
   */
  private void validateEmployeeSalaryItemUniqueness(List<ImportData> dataList) {
    List<ImportError> errorList = Lists.newArrayList();
    Set<String> importEmployeeSalaryItemKeys = Sets.newHashSet();

    for (ImportData data : dataList) {
      IObjectData objectData = data.getData();

      String employeeFixedSalaryId = getStringValue(objectData, EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY);
      String salaryItemId = getStringValue(objectData, EmployeeFixedSalaryDetailFields.SALARY_ITEM);

      if (StringUtils.isBlank(employeeFixedSalaryId) || StringUtils.isBlank(salaryItemId)) {
        // 这些验证在其他方法中已经处理
        continue;
      }

      String key = employeeFixedSalaryId + "#" + salaryItemId;

      // 1. 检查数据库中是否已存在该员工的该工资项明细（与现有数据验证）
      if (existingEmployeeSalaryItemMap.containsKey(key)) {
        // 获取员工信息用于错误提示
        String employeeName = getEmployeeNameFromFixedSalary(employeeFixedSalaryId);
        String salaryItemName = getSalaryItemName(salaryItemId);

        errorList.add(new ImportError(data.getRowNo(),
            MessageFormat.format("数据库中员工{0}已存在工资项{1}的明细记录，不能重复创建", //ignoreI18n
                employeeName, salaryItemName)));
        continue;
      }

      // 2. 检查导入数据中是否有重复的员工工资项组合（导入数据内部验证）
      if (importEmployeeSalaryItemKeys.contains(key)) {
        String employeeName = getEmployeeNameFromFixedSalary(employeeFixedSalaryId);
        String salaryItemName = getSalaryItemName(salaryItemId);

        errorList.add(new ImportError(data.getRowNo(),
            MessageFormat.format("导入数据中员工{0}的工资项{1}重复，请检查", //ignoreI18n
                employeeName, salaryItemName)));
        continue;
      }

      // 3. 验证员工是否存在（确保员工固定工资表存在）
      if (!employeeFixedSalaryMap.containsKey(employeeFixedSalaryId)) {
        errorList.add(new ImportError(data.getRowNo(), "员工固定工资表不存在，请先创建员工固定工资表")); //ignoreI18n
        continue;
      }

      importEmployeeSalaryItemKeys.add(key);
    }

    mergeErrorList(errorList);
    log.info("员工工资项唯一性验证完成（包含现有数据验证），错误数: {}", errorList.size());
  }

  /**
   * 从员工固定工资表获取员工姓名
   */
  private String getEmployeeNameFromFixedSalary(String employeeFixedSalaryId) {
    if (employeeFixedSalaryMap.containsKey(employeeFixedSalaryId)) {
      IObjectData employeeFixedSalary = employeeFixedSalaryMap.get(employeeFixedSalaryId);

      // 尝试获取内部员工姓名
      String internalEmployeeId = getStringValue(employeeFixedSalary, EmployeeFixedSalaryFields.EMPLOYEE);
      if (StringUtils.isNotBlank(internalEmployeeId)) {
        // 这里可以通过员工ID获取员工姓名，暂时返回ID
        return "员工ID:" + internalEmployeeId; //ignoreI18n
      }

      // 尝试获取外部员工姓名
      String externalEmployeeId = getStringValue(employeeFixedSalary, EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL);
      if (StringUtils.isNotBlank(externalEmployeeId)) {
        return "外部员工ID:" + externalEmployeeId; //ignoreI18n
      }
    }

    return "未知员工"; //ignoreI18n
  }

  /**
   * 获取工资项名称
   */
  private String getSalaryItemName(String salaryItemId) {
    if (salaryItemMap.containsKey(salaryItemId)) {
      IObjectData salaryItem = salaryItemMap.get(salaryItemId);
      String name = getStringValue(salaryItem, "name"); // 假设工资项有name字段
      return StringUtils.isNotBlank(name) ? name : "工资项ID:" + salaryItemId; //ignoreI18n
    }

    return "未知工资项"; //ignoreI18n
  }

  /**
   * 获取字符串字段值的辅助方法
   */
  protected String getStringValue(IObjectData objectData, String fieldName) {
    Object value = objectData.get(fieldName);
    return value != null ? value.toString() : null;
  }
}
