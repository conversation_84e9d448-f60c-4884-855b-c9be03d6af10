package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.model.api.DailySalaryQuery;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 获取日工资数据接口（返回日历数据和当天的详细薪资数据）
 * @author: zhangsm
 * @create: 2025-05-20 14:30
 **/
@Slf4j
public class SalaryDataDailyQueryController extends PreDefineController<DailySalaryQuery.Arg, DailySalaryQuery.Result> {
  
  private final SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);

  @Override
  protected List<String> getFuncPrivilegeCodes() {
    return null;
  }

  @Override
  protected DailySalaryQuery.Result doService(DailySalaryQuery.Arg arg) {
    // 如果未提供员工ID，则使用当前登录用户的ID
    if (StringUtils.isBlank(arg.getEmployeeId())) {
      arg.setEmployeeId(controllerContext.getUser().getUserId());
    }
    
    // 处理查询日期和月份
    String queryDate = arg.getQueryDate();
    String month = arg.getMonth();
    
    // 如果未提供查询日期，则设置为null，表示不返回当日详情
    // 如果未提供月份，则设置为null，表示不返回日历数据
    
    log.info("查询日工资数据, employeeId: {}, queryDate: {}, month: {}", 
        arg.getEmployeeId(), queryDate, month);
    
    // 创建服务上下文
    ServiceContext serviceContext = new ServiceContext(
        controllerContext.getRequestContext(),
        "fmcg-wq",
        "dailySalaryQuery"
    );
    
    // 调用服务获取日工资数据
    DailySalaryQuery.Result result = salaryService.getDailySalary(
        arg.getEmployeeId(), 
        queryDate,
        month,
        serviceContext
    );
    
    // 如果结果为空，则创建空结果对象
    if (result == null) {
      result = new DailySalaryQuery.Result();
      // 只有当month不为空时才初始化calendarDays
      if (StringUtils.isNotBlank(month)) {
        result.setCalendarDays(new ArrayList<>());
      }
    }
    
    // 记录日志
    log.info("查询日工资数据完成, employeeId: {}, queryDate: {}, 日历数据数量: {}, 是否有当日详情: {}", 
        arg.getEmployeeId(), 
        queryDate,
        result.getCalendarDays() != null ? result.getCalendarDays().size() : 0,
        result.getCurrentDayDetail() != null);
    
    return result;
  }
}
