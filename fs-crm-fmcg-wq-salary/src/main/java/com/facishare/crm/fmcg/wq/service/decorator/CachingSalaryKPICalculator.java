package com.facishare.crm.fmcg.wq.service.decorator;

import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.kpi.SalaryKPI;
import com.facishare.crm.fmcg.wq.service.SalaryKPICalculator;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 带缓存的薪资KPI计算器装饰器
 * @author: dev
 * @create: 2024-07-10
 */
@Slf4j
public class CachingSalaryKPICalculator<T extends SalaryKPI> extends SalaryKPICalculatorDecorator<T> {
    
    // 默认缓存大小
    private static final int DEFAULT_CACHE_SIZE = 1000;
    
    // 默认缓存过期时间（分钟）
    private static final int DEFAULT_EXPIRE_MINUTES = 30;
    
    // 使用Guava Cache替代ConcurrentHashMap，支持自动过期和容量限制
    private final Cache<String, MetricCalculateResult> cache;
    
    public CachingSalaryKPICalculator(SalaryKPICalculator<T> decorated) {
        this(decorated, DEFAULT_CACHE_SIZE, DEFAULT_EXPIRE_MINUTES);
    }
    
    /**
     * 构造函数，允许指定缓存大小和过期时间
     * 
     * @param decorated 被装饰的计算器
     * @param maxSize 缓存最大容量
     * @param expireMinutes 缓存过期时间（分钟）
     */
    public CachingSalaryKPICalculator(SalaryKPICalculator<T> decorated, int maxSize, int expireMinutes) {
        super(decorated);
        
        // 创建Guava Cache，设置最大容量和过期时间
        this.cache = CacheBuilder.newBuilder()
                .maximumSize(maxSize) // 设置缓存最大容量
                .expireAfterWrite(expireMinutes, TimeUnit.MINUTES) // 设置写入后过期时间
                .recordStats() // 记录缓存统计信息
                .removalListener(notification -> 
                    log.debug("KPI计算缓存条目被移除: key={}, 原因={}", 
                            notification.getKey(), notification.getCause()))
                .build();
        
        log.info("初始化KPI计算缓存，最大容量: {}, 过期时间: {}分钟", maxSize, expireMinutes);
    }
    
    @Override
    public MetricCalculateResult doCalculate(SalaryContext context, T metric) {
        String cacheKey = generateCacheKey(context, metric);
        
        try {
            // 尝试从缓存获取，如果不存在则计算并放入缓存
            return cache.get(cacheKey, () -> {
                log.debug("KPI计算缓存未命中，执行计算: {}", cacheKey);
                MetricCalculateResult result = decorated.doCalculate(context, metric);
                log.debug("KPI计算结果已缓存: {}", cacheKey);
                return result;
            });
        } catch (ExecutionException e) {
            // 如果计算过程中发生异常，记录日志并直接调用被装饰对象的计算方法
            log.warn("从缓存获取KPI计算结果时发生异常: {}", e.getMessage());
            return decorated.doCalculate(context, metric);
        }
    }
    
    /**
     * 生成缓存键
     *
     * @param context 薪资上下文
     * @param metric KPI指标
     * @return 缓存键
     */
    private String generateCacheKey(SalaryContext context, T metric) {
        return String.format("%s_%s_%d_%d",
                metric.getId(),
                context.getOwner(),
                context.getStartTime(),
                context.getEndTime());
    }
    
    /**
     * 清除缓存
     */
    public void clearCache() {
        cache.invalidateAll();
        log.info("KPI计算缓存已清除");
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息字符串
     */
    public String getCacheStats() {
        return cache.stats().toString();
    }
    
    /**
     * 获取当前缓存大小
     * 
     * @return 当前缓存中的条目数
     */
    public long getCacheSize() {
        return cache.size();
    }
}
