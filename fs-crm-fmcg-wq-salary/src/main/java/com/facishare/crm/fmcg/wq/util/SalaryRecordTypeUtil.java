package com.facishare.crm.fmcg.wq.util;

import com.facishare.crm.fmcg.wq.constants.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 工资相关业务类型工具类
 * 用于判断员工类型并返回对应的业务类型
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class SalaryRecordTypeUtil {

  /**
   * 根据员工信息获取工资条的业务类型
   * 
   * @param employeeId 内部员工ID
   * @param employeeExternalId 外部员工ID
   * @return 业务类型：内部员工返回 default__c，外部员工返回 record_outer__c
   */
  public static String getSalaryDataRecordType(String employeeId, String employeeExternalId) {
    try {
      // 判断是否为外部员工
      if (isExternalEmployee(employeeId, employeeExternalId)) {
        log.debug("外部员工，设置业务类型为: {}", SalaryDataFields.RECORD_TYPE_EXTERNAL);
        return SalaryDataFields.RECORD_TYPE_EXTERNAL;
      } else {
        log.debug("内部员工，设置业务类型为: {}", SalaryDataFields.RECORD_TYPE_INTERNAL);
        return SalaryDataFields.RECORD_TYPE_INTERNAL;
      }
    } catch (Exception e) {
      log.error("获取工资条业务类型时发生异常，employeeId: {}, employeeExternalId: {}", 
          employeeId, employeeExternalId, e);
      // 异常时默认返回内部员工类型
      return SalaryDataFields.RECORD_TYPE_INTERNAL;
    }
  }
  public static String getEmployeeFixedRecordType(String employeeId, String employeeExternalId) {
    try {
      // 判断是否为外部员工
      if (isExternalEmployee(employeeId, employeeExternalId)) {
        log.debug("外部员工，设置业务类型为: {}", EmployeeFixedSalaryFields.RECORDTYPE_EXTERNAL);
        return EmployeeFixedSalaryFields.RECORDTYPE_EXTERNAL;
      } else {
        log.debug("内部员工，设置业务类型为: {}", EmployeeFixedSalaryFields.RECORDTYPE_INTERNAL);
        return EmployeeFixedSalaryFields.RECORDTYPE_INTERNAL;
      }
    } catch (Exception e) {
      log.error("获取工资条业务类型时发生异常，employeeId: {}, employeeExternalId: {}",
              employeeId, employeeExternalId, e);
      // 异常时默认返回内部员工类型
      return EmployeeFixedSalaryFields.RECORDTYPE_INTERNAL;
    }
  }

  /**
   * 根据员工信息获取工资条明细的业务类型
   * 
   * @param employeeId 内部员工ID
   * @param employeeExternalId 外部员工ID
   * @return 业务类型：内部员工返回 default__c，外部员工返回 record_outer__c
   */
  public static String getSalaryDetailDataRecordType(String employeeId, String employeeExternalId) {
    try {
      // 判断是否为外部员工
      if (isExternalEmployee(employeeId, employeeExternalId)) {
        log.debug("外部员工，设置业务类型为: {}", SalaryDetailDataFields.RECORD_TYPE_EXTERNAL);
        return SalaryDetailDataFields.RECORD_TYPE_EXTERNAL;
      } else {
        log.debug("内部员工，设置业务类型为: {}", SalaryDetailDataFields.RECORD_TYPE_INTERNAL);
        return SalaryDetailDataFields.RECORD_TYPE_INTERNAL;
      }
    } catch (Exception e) {
      log.error("获取工资条明细业务类型时发生异常，employeeId: {}, employeeExternalId: {}", 
          employeeId, employeeExternalId, e);
      // 异常时默认返回内部员工类型
      return SalaryDetailDataFields.RECORD_TYPE_INTERNAL;
    }
  }

  /**
   * 根据员工ID判断员工类型并获取对应的业务类型
   * 适用于只有一个员工ID的情况（可能是内部员工ID或外部员工ID）
   * 
   * @param employeeId 员工ID（可能是内部员工ID或外部员工ID）
   * @param isForSalaryData 是否为工资条（true）还是工资条明细（false）
   * @return 业务类型
   */
  public static String getRecordTypeByEmployeeId(String employeeId, boolean isForSalaryData) {
    try {
      if (StringUtils.isBlank(employeeId)) {
        log.warn("员工ID为空，默认返回内部员工业务类型");
        return isForSalaryData ? SalaryDataFields.RECORD_TYPE_INTERNAL : SalaryDetailDataFields.RECORD_TYPE_INTERNAL;
      }

      // 根据员工ID数值大小判断员工类型
      // 内部员工ID <= 100000000，外部员工ID > 100000000
      try {
        long empId = Long.parseLong(employeeId);
        if (empId > 100000000L) {
          // 外部员工
          log.debug("根据员工ID {} 判断为外部员工", employeeId);
          return isForSalaryData ? SalaryDataFields.RECORD_TYPE_EXTERNAL : SalaryDetailDataFields.RECORD_TYPE_EXTERNAL;
        } else {
          // 内部员工
          log.debug("根据员工ID {} 判断为内部员工", employeeId);
          return isForSalaryData ? SalaryDataFields.RECORD_TYPE_INTERNAL : SalaryDetailDataFields.RECORD_TYPE_INTERNAL;
        }
      } catch (NumberFormatException e) {
        log.warn("员工ID {} 不是有效的数字格式，默认返回内部员工业务类型", employeeId);
        return isForSalaryData ? SalaryDataFields.RECORD_TYPE_INTERNAL : SalaryDetailDataFields.RECORD_TYPE_INTERNAL;
      }

    } catch (Exception e) {
      log.error("根据员工ID获取业务类型时发生异常，employeeId: {}", employeeId, e);
      // 异常时默认返回内部员工类型
      return isForSalaryData ? SalaryDataFields.RECORD_TYPE_INTERNAL : SalaryDetailDataFields.RECORD_TYPE_INTERNAL;
    }
  }

  /**
   * 判断是否为外部员工
   * 
   * @param employeeId 内部员工ID
   * @param employeeExternalId 外部员工ID
   * @return true-外部员工，false-内部员工
   */
  public static boolean isExternalEmployee(String employeeId, String employeeExternalId) {
    // 如果外部员工ID不为空，则为外部员工
    if (StringUtils.isNotBlank(employeeExternalId)) {
      return true;
    }
    
    // 如果内部员工ID不为空，则为内部员工
    if (StringUtils.isNotBlank(employeeId)) {
      return false;
    }
    
    // 如果都为空，默认为内部员工
    log.warn("员工ID和外部员工ID都为空，默认判断为内部员工");
    return false;
  }

  /**
   * 根据员工固定工资对象判断员工类型并获取业务类型
   * 
   * @param employeeFixedSalaryRecordType 员工固定工资表的业务类型
   * @param isForSalaryData 是否为工资条（true）还是工资条明细（false）
   * @return 业务类型
   */
  public static String getRecordTypeByFixedSalaryRecordType(String employeeFixedSalaryRecordType, boolean isForSalaryData) {
    try {
      if (StringUtils.isBlank(employeeFixedSalaryRecordType)) {
        log.warn("员工固定工资表业务类型为空，默认返回内部员工业务类型");
        return isForSalaryData ? SalaryDataFields.RECORD_TYPE_INTERNAL : SalaryDetailDataFields.RECORD_TYPE_INTERNAL;
      }

      // 根据员工固定工资表的业务类型判断
      if ("external__c".equals(employeeFixedSalaryRecordType)) {
        // 外部员工
        log.debug("根据员工固定工资表业务类型 {} 判断为外部员工", employeeFixedSalaryRecordType);
        return isForSalaryData ? SalaryDataFields.RECORD_TYPE_EXTERNAL : SalaryDetailDataFields.RECORD_TYPE_EXTERNAL;
      } else {
        // 内部员工（default__c 或其他）
        log.debug("根据员工固定工资表业务类型 {} 判断为内部员工", employeeFixedSalaryRecordType);
        return isForSalaryData ? SalaryDataFields.RECORD_TYPE_INTERNAL : SalaryDetailDataFields.RECORD_TYPE_INTERNAL;
      }

    } catch (Exception e) {
      log.error("根据员工固定工资表业务类型获取业务类型时发生异常，recordType: {}", employeeFixedSalaryRecordType, e);
      // 异常时默认返回内部员工类型
      return isForSalaryData ? SalaryDataFields.RECORD_TYPE_INTERNAL : SalaryDetailDataFields.RECORD_TYPE_INTERNAL;
    }
  }

  /**
   * 根据工资规则类型获取工资发放单的业务类型
   *
   * @param salaryRuleObj 工资规则对象
   * @return 业务类型：外部规则返回 record_outer__c，内部规则返回 default__c
   */
  public static String getSalaryPaymentSlipRecordType(Object salaryRuleObj) {
    try {
      if (salaryRuleObj == null) {
        log.warn("工资规则对象为空，默认返回内部规则业务类型");
        return SalaryPaymentSlipFields.RECORD_TYPE_INTERNAL;
      }

      // 如果是 IObjectData 类型，直接获取其 record_type 字段
      if (salaryRuleObj instanceof com.facishare.paas.metadata.api.IObjectData) {
        com.facishare.paas.metadata.api.IObjectData ruleData = (com.facishare.paas.metadata.api.IObjectData) salaryRuleObj;
        String recordType = ruleData.getRecordType();

        if (StringUtils.isNotBlank(recordType)) {
          // 根据工资规则的业务类型判断
          if (SalaryRuleFields.RECORD_TYPE_EXTERNAL.equals(recordType)) {
            log.debug("外部员工工资规则，设置工资发放单业务类型为: {}", SalaryPaymentSlipFields.RECORD_TYPE_EXTERNAL);
            return SalaryPaymentSlipFields.RECORD_TYPE_EXTERNAL;
          } else if (SalaryRuleFields.RECORD_TYPE_INTERNAL.equals(recordType)) {
            log.debug("内部员工工资规则，设置工资发放单业务类型为: {}", SalaryPaymentSlipFields.RECORD_TYPE_INTERNAL);
            return SalaryPaymentSlipFields.RECORD_TYPE_INTERNAL;
          } else {
            log.warn("未知的工资规则业务类型: {}，默认返回内部规则业务类型", recordType);
            return SalaryPaymentSlipFields.RECORD_TYPE_INTERNAL;
          }
        } else {
          log.warn("工资规则业务类型为空，默认返回内部规则业务类型");
          return SalaryPaymentSlipFields.RECORD_TYPE_INTERNAL;
        }
      } else {
        log.warn("工资规则对象类型不是 IObjectData，默认返回内部规则业务类型，类型: {}", salaryRuleObj.getClass().getName());
        return SalaryPaymentSlipFields.RECORD_TYPE_INTERNAL;
      }

    } catch (Exception e) {
      log.error("获取工资发放单业务类型时发生异常，salaryRuleObj: {}", salaryRuleObj, e);
      // 异常时默认返回内部规则类型
      return SalaryPaymentSlipFields.RECORD_TYPE_INTERNAL;
    }
  }


}
