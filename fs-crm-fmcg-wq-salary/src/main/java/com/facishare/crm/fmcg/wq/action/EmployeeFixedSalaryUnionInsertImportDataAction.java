package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.service.SalaryImportValidationService;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.util.SalaryRecordTypeUtil;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 员工固定工资表联合导入Action
 * 
 * 功能：
 * 1. 支持主对象（员工固定工资表）和从对象（员工固定工资明细）的联合导入
 * 2. 员工唯一性校验（每个员工只能有一条固定薪资记录）
 * 3. 定薪方式验证
 * 4. 薪资规则验证（包含适用范围验证）
 * 5. 业务类型设置（内部员工vs外部员工）
 * 
 * 联合导入处理流程：
 * 1. customInit - 字段转换和初始化
 * 2. customValidate - 数据校验（主对象和从对象）
 * 3. customDefaultValue - 业务默认值赋值
 * 4. importData - 写入数据库
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class EmployeeFixedSalaryUnionInsertImportDataAction extends StandardUnionInsertImportDataAction {

  private SalaryImportValidationService validationService = SpringUtil.getContext().getBean(SalaryImportValidationService.class);

  // 缓存已存在的员工固定薪资记录
  private Map<String, IObjectData> existingEmployeeRecords;

  @Override
  protected void customInit(List<ImportData> dataList) {
    // 设置定薪方式为必填
    objectDescribeExt.getFieldDescribeSilently(EmployeeFixedSalaryFields.SALARY_METHOD).ifPresent(field -> {
      field.setRequired(true);
    });
    //业务类型
    objectDescribeExt.getFieldDescribeSilently(BaseField.recordType.getApiName()).ifPresent(field -> {
      field.setRequired(true);
    });

    // 员工字段的必填验证将在后续根据业务类型来判断
    // 这里不设置员工字段为必填，避免在初始化阶段就限制用户输入

    super.customInit(dataList);
    log.info("员工固定工资表导入初始化完成，数据行数: {}", dataList.size());
  }

  @Override
  protected void customValidate(List<ImportData> dataList) {
    super.customValidate(dataList);

    // 查询已存在的员工固定薪资记录
    existingEmployeeRecords = validationService.loadExistingEmployeeRecords(dataList, actionContext.getTenantId());

    // 员工唯一性校验
    List<ImportError> employeeErrors = validationService.validateEmployeeUniqueness(dataList, existingEmployeeRecords);
    mergeErrorList(employeeErrors);

    // 薪资规则验证
    List<ImportError> salaryRuleErrors = validationService.validateSalaryRules(dataList, actionContext.getTenantId());
    mergeErrorList(salaryRuleErrors);

    log.info("员工固定工资表导入验证完成");
  }

  @Override
  protected void customDefaultValue(List<IObjectData> validList) {
    super.customDefaultValue(validList);

    // 设置业务类型
    validationService.setRecordTypes(validList);

    log.info("员工固定工资表导入默认值设置完成，有效数据行数: {}", validList.size());
  }


}
