package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import lombok.extern.slf4j.Slf4j;

import java.util.stream.Collectors;

/**
 * 工资发放单列表头部控制器
 * 
 * 功能：过滤掉批量作废按钮，防止用户误操作批量作废工资发放单
 */
@Slf4j
public class SalaryPaymentSlipListHeaderController extends StandardListHeaderController {

  @Override
  protected Result after(Arg arg, Result result) {
    result = super.after(arg, result);
    
    // 过滤掉批量作废按钮
    if (result.getButtons() != null) {
      result.setButtons(result.getButtons().stream()
          .filter(button -> !"Abolish_button_default".equals(button.get("api_name")))
          .filter(button -> !"AsyncBulkInvalid".equals(button.get("action")))
          .collect(Collectors.toList()));
      
      log.debug("工资发放单列表页面已过滤批量作废按钮，剩余按钮数量: {}", result.getButtons().size());
    }
    
    return result;
  }
}
