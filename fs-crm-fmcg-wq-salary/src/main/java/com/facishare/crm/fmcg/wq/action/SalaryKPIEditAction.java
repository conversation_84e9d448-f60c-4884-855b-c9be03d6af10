package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryKPIDao;
import com.facishare.crm.fmcg.wq.util.FieldValidationUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 工资绩效指标编辑Action
 * 
 * 编辑规则：
 * 1. 任何时机都可以编辑
 * 2. 当未被工资项使用时，支持编辑全部字段
 * 3. 当指标被工资项使用后，仅可编辑「指标名称」「指标说明」
 */
@Slf4j
public class SalaryKPIEditAction extends FmcgSkipPermissionEditAction {

    private SalaryItemDao salaryItemDao = SpringUtil.getContext().getBean(SalaryItemDao.class);
    private SalaryKPIDao salaryKPIDao = SpringUtil.getContext().getBean(SalaryKPIDao.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        String tenantId = actionContext.getTenantId();
        String kpiId = arg.getObjectData().getId();

        log.info("开始处理工资绩效指标编辑，指标ID: {}", kpiId);

        // 检查指标是否被工资项使用
        List<IObjectData> usingSalaryItems = salaryItemDao.getSalaryItemsByKpiId(tenantId, kpiId);
        boolean isUsedBySalaryItems = CollectionUtils.isNotEmpty(usingSalaryItems);

        if (isUsedBySalaryItems) {
            log.info("指标 {} 已被 {} 个工资项使用，限制编辑字段", kpiId, usingSalaryItems.size());
            // 检查是否有不允许编辑的字段被修改
            validateEditableFields(arg.getObjectData().toObjectData(), dbMasterData);

            // 记录使用该指标的工资项信息
            StringBuilder usingItemsInfo = new StringBuilder();
            for (IObjectData item : usingSalaryItems) {
                if (usingItemsInfo.length() > 0) {
                    usingItemsInfo.append("、");
                }
                usingItemsInfo.append(item.getName());
            }
            log.info("指标 {} 被以下工资项使用: {}", kpiId, usingItemsInfo.toString());
        } else {
            log.info("指标 {} 未被工资项使用，允许编辑全部字段", kpiId);

            // 验证考勤类型重复性（仅在未被工资项使用时检查）
            validateAttendanceFieldUniqueness(tenantId, kpiId, arg.getObjectData());
        }

        log.info("工资绩效指标编辑前处理完成");
    }

    /**
     * 验证编辑的字段是否被允许
     *
     * @param newData      新数据
     * @param originalData 原始数据
     */
    private void validateEditableFields(IObjectData newData, IObjectData originalData) {
        // 检查关键字段是否被修改
        validateFieldNotChanged(newData, originalData, SalaryKPIFields.INDICATOR_CALC_METHOD);
        validateFieldNotChanged(newData, originalData, SalaryKPIFields.AGGREGATED_OBJECT);
        validateFieldNotChanged(newData, originalData, SalaryKPIFields.AGGREGATED_FIELD);
        validateFieldNotChanged(newData, originalData, SalaryKPIFields.AGGREGATED_PERSON_FIELD);
        validateFieldNotChanged(newData, originalData, SalaryKPIFields.AGGREGATED_DATE_FIELD);
        validateFieldNotChanged(newData, originalData, SalaryKPIFields.AGGREGATION_FUNCTION);
        validateFieldNotChanged(newData, originalData, SalaryKPIFields.APL_INFO);
        validateFieldNotChanged(newData, originalData, SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD);
        validateFieldNotChanged(newData, originalData, SalaryKPIFields.WHERES);
        // validateFieldNotChanged(newData, originalData, SalaryKPIFields.UNIT);
    }

    /**
     * 验证字段是否被修改
     *
     * @param newData      新数据
     * @param originalData 原始数据
     * @param fieldName    字段名
     */
    private void validateFieldNotChanged(IObjectData newData, IObjectData originalData, String fieldName) {
        Object newValue = newData.get(fieldName);
        Object originalValue = originalData.get(fieldName);

        String reason = "指标已被工资项使用"; // ignoreI18n
        FieldValidationUtil.validateFieldNotChanged(objectDescribe, fieldName, newValue, originalValue, reason);
    }

    /**
     * 验证考勤字段的唯一性
     * 
     * @param tenantId 租户ID
     * @param kpiId    当前KPI指标ID
     * @param kpiData  KPI指标数据
     */
    private void validateAttendanceFieldUniqueness(String tenantId, String kpiId, ObjectDataDocument kpiData) {
        //参数可能不是 String 是int 怎么转换
        // 获取指标计算方式字段值，并确保其为 String 类型
        Object calcMethodObj = kpiData.get(SalaryKPIFields.INDICATOR_CALC_METHOD);

        if (calcMethodObj == null) {
            throw new ValidateException("指标计算方式不能为空");//ignoreI18n
        }

        String calcMethod;
        if (calcMethodObj instanceof String) {
            calcMethod = (String) calcMethodObj;
        } else if (calcMethodObj instanceof Integer) {
            calcMethod = calcMethodObj.toString(); // 或者根据业务定义映射逻辑
        } else {
            throw new ValidateException("指标计算方式必须是字符串或整数类型");// ignoreI18n
        }
        kpiData.put(SalaryKPIFields.INDICATOR_CALC_METHOD,  calcMethod);
        // 只有当指标计算方式为"月度考勤表"时才需要检查
        if (!SalaryKPIFields.INDICATOR_CALC_METHOD_Options_2.equals(calcMethod)) {
            return;
        }

        String attendanceField = (String) kpiData.get(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD);
        if (StringUtils.isBlank(attendanceField)) {
            throw new ValidateException("指标计算方式为月度考勤表时，月度考勤表字段为必填字段"); // ignoreI18n
        }

        // 检查考勤类型选项是否已被其他指标使用（排除当前指标）
        List<IObjectData> existingKpis = salaryKPIDao.getKpisByAttendanceFieldExcludeId(tenantId, attendanceField,
                kpiId);
        if (CollectionUtils.isNotEmpty(existingKpis)) {
            // 构建已使用该考勤字段的指标信息
            StringBuilder existingKpiInfo = new StringBuilder();
            for (IObjectData existingKpi : existingKpis) {
                if (existingKpiInfo.length() > 0) {
                    existingKpiInfo.append("、");
                }
                existingKpiInfo.append(existingKpi.getName());
            }

            // 获取考勤字段的显示名称
            String attendanceFieldDisplayName = getAttendanceFieldDisplayName(attendanceField);

            log.warn("考勤字段 {} 已被以下指标使用: {}", attendanceFieldDisplayName, existingKpiInfo.toString());
            throw new ValidateException(String.format("考勤类型选项[%s]已被其他指标使用，不允许重复。使用该考勤字段的指标: %s", //ignoreI18n
                    attendanceFieldDisplayName, existingKpiInfo.toString()));
        }

        log.info("考勤字段 {} 验证通过，可以使用", attendanceField);
    }

    /**
     * 获取字段的显示名称
     * 从字段描述中动态获取标签信息
     *
     * @param fieldName 字段名
     * @return 显示名称
     */
    private String getFieldDisplayName(String fieldName) {
        try {
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
            if (fieldDescribe != null) {
                String label = fieldDescribe.getLabel();
                if (StringUtils.isNotBlank(label)) {
                    return label;
                }
            }
        } catch (Exception e) {
            log.warn("获取字段显示名称失败，使用字段名: {}", e.getMessage());
        }

        // 如果获取失败，返回字段名
        return fieldName;
    }

    /**
     * 获取考勤字段的显示名称
     * 从字段描述中动态获取选项的标签信息
     *
     * @param attendanceField 考勤字段值
     * @return 显示名称
     */
    private String getAttendanceFieldDisplayName(String attendanceField) {
        try {
            // 获取月度考勤表字段的描述信息
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD);
            if (fieldDescribe != null) {
                // 获取字段选项数组
                List<Map<String, String>> optionArray = (List<Map<String, String>>) fieldDescribe.get("options");
                if (CollectionUtils.isNotEmpty(optionArray)) {
                    // 遍历选项，查找匹配的值
                    for (Map<String, String> option : optionArray) {
                        if (attendanceField.equals(option.get("value"))) {
                            // 只使用标签信息
                            String label = option.get("label");
                            if (StringUtils.isNotBlank(label)) {
                                return label;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取考勤字段显示名称失败，使用默认逻辑: {}", e.getMessage());
        }

        // 如果从描述中获取失败，使用原有的硬编码逻辑作为兜底
        switch (attendanceField) {
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_ruleDaysNum:
                return "应出勤天数（天）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_checkDayNum:
                return "正常出勤（天）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_absentDays:
                return "旷工（天）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_waiQinDaysNum:
                return "外勤（天）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_checkWorkTime:
                return "实际工作时长（小时）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_overTime:
                return "加班时长（小时）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_laterNum:
                return "迟到（次）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_laterTime:
                return "迟到（分钟）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_earlyNum:
                return "早退（次）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_earlyTime:
                return "早退（分钟）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_missNum:
                return "未打卡（次）"; // ignoreI18n
            case SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD_Options_locationExNum:
                return "不在考勤范围（次）"; // ignoreI18n
            default:
                return attendanceField;
        }
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        log.info("工资绩效指标编辑成功，ID: {}", result.getObjectData().getId());
        return after;
    }
}
