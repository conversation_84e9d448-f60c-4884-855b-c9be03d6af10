package com.facishare.crm.fmcg.wq.service;

import com.beust.jcommander.internal.Lists;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.model.APLReturnType;
import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.exception.AbandonActionException;
import com.facishare.crm.fmcg.wq.model.exception.RetryException;
import com.facishare.crm.fmcg.wq.model.kpi.AplSalaryKPI;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component("aplSalaryKPICalculator")
@Slf4j
public class AplSalaryKPICalculator extends SalaryKPICalculator<AplSalaryKPI> {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    public void validate(AplSalaryKPI metric) {
        super.validate(metric);

        AplSalaryKPI.FunctionInfo functionInfo = metric.getFunctionInfo();

        if (StringUtils.isEmpty(functionInfo.getApiName())) {
            throw new ValidateException("function api name is null");
        }

        if (StringUtils.isEmpty(functionInfo.getReturnType())) {
            throw new ValidateException("function return type is null");
        }
    }

    @Override
    public MetricCalculateResult doCalculate(SalaryContext context, AplSalaryKPI metric) {
        User user = User.systemUser(context.getTenantId());
        Map<String, Object> parameters = Maps.newHashMap();
//        parameters.put("cycle_value", context.getCycleValue());
        parameters.put("kpi_start_time", DateUtils.getStringFromTime(context.getStartTime(), DateUtils.DateFormat));
        parameters.put("kpi_end_time", DateUtils.getStringFromTime(context.getEndTime(), DateUtils.DateFormat));
        parameters.put("kpi_owner", context.getOwner());
        //id
        parameters.put("kpi_id", metric.getId());
        //参数
        Map<String, Object> functionParameters = new HashMap<>();
        functionParameters.put("kpi_parameters", parameters);
//        parameters.put("task_member_id", context.getMemberId());
        RunResult runResult = serviceFacade.getFunctionLogicService().executeUDefFunction(user, getFunction(user, metric.getFunctionInfo().getApiName(), metric.getFunctionInfo().getBindObjectApiName()), functionParameters, new ObjectData(), null);
        Object value = getResultValue(metric.getFunctionInfo(), runResult);
        if (Objects.isNull(value)) {
            return new MetricCalculateResult(null);
        } else {
            return new MetricCalculateResult(value.toString());
        }
    }

    private Object getResultValue(AplSalaryKPI.FunctionInfo functionInfo, RunResult runResult) {
        Object functionResult = getObjResult(functionInfo.getApiName(), runResult);
        if (functionResult == null) {
            return null;
        }
        Class<?> clazz;
        try {
            clazz = APLReturnType.valueOf(functionInfo.getReturnType().toUpperCase()).getClassType();
        } catch (IllegalArgumentException ex) {
            throw new AbandonActionException(ex.getMessage());
        }
        if (clazz.isInstance(functionResult)) {
            return functionResult;
        } else {
            throw new AbandonActionException("not support this return type");
        }
    }

    private Object getObjResult(String functionApiName, RunResult runResult) {
        if (!runResult.isSuccess()) {
            log.error("functionApiName {} run has error msg is {}", functionApiName, runResult.getErrorInfo());
            throw new RetryException(runResult.getErrorInfo());
        }
        return runResult.getFunctionResult();
    }

    private IUdefFunction getFunction(User user, String functionApiName, String bindingObjectAPIName) {
        IUdefFunction function = serviceFacade.getFunctionLogicService().findUDefFunction(user, functionApiName, bindingObjectAPIName);
        if (function == null) {
            throw new RetryException("function is null");
        }
        if (Boolean.TRUE.equals(function.isDeleted())) {
            throw new RetryException("function is deleted");
        }
        if (!function.isActive()) {
            throw new RetryException("function is disable");
        }
        return function;
    }
}
