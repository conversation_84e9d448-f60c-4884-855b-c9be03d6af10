package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 工资规则作废Action
 * 
 * 作废规则：
 * 1. 工资规则生效日期前，支持作废
 * 2. 当工资生效日期后，不允许作废
 */
@Slf4j
public class SalaryRuleInvalidAction extends StandardInvalidAction {

    private SalaryRuleDao salaryRuleDao = SpringUtil.getContext().getBean(SalaryRuleDao.class);

    @Override
    protected void before(Arg arg) {
        String tenantId = actionContext.getTenantId();
        String salaryRuleId = arg.getObjectDataId();
        
        log.info("开始处理工资规则作废，工资规则ID: {}", salaryRuleId);
        
        // 获取工资规则数据
        IObjectData salaryRuleData = salaryRuleDao.getById(tenantId, salaryRuleId);
        if (salaryRuleData == null) {
            throw new ValidateException("工资规则不存在"); //ignoreI18n
        }
        
        // 检查工资规则是否已生效
        Long effectiveDate = salaryRuleData.get(SalaryRuleFields.EFFECTIVE_DATE, Long.class);
        long currentTime = System.currentTimeMillis();
        boolean isEffective = effectiveDate != null && effectiveDate <= currentTime;
        
        if (isEffective) {
            log.warn("工资规则 {} 已生效，不允许作废。生效时间: {}, 当前时间: {}", 
                    salaryRuleId, effectiveDate, currentTime);
            throw new ValidateException("工资规则已生效，不可作废"); //ignoreI18n
        }
        
        log.info("工资规则 {} 未生效，允许作废。生效时间: {}, 当前时间: {}", 
                salaryRuleId, effectiveDate, currentTime);
        
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        log.info("工资规则作废成功，ID: {}", arg.getObjectDataId());
        return after;
    }
}
