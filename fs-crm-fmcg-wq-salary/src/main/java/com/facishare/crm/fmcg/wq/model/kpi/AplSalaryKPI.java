package com.facishare.crm.fmcg.wq.model.kpi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.crm.fmcg.wq.model.KPICalculateType;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class AplSalaryKPI extends SalaryKPI {

    private FunctionInfo functionInfo;

    @SuppressWarnings("Duplicates")
    public static AplSalaryKPI of(IObjectData data) {
        AplSalaryKPI metric = new AplSalaryKPI();

        metric.setTenantId(data.getTenantId());
        metric.setId(data.getId());
        metric.setName(data.getName());
        metric.setDescription(data.get(SalaryKPIFields.INDICATOR_DESC, String.class));
        metric.setKpiCalculateType(KPICalculateType.of(data.get(SalaryKPIFields.INDICATOR_CALC_METHOD, String.class)));

        String functionInfoStr = data.get(SalaryKPIFields.APL_INFO, String.class);
        //if __c 结尾的 直接构造参数
        if (StringUtils.isNotBlank(functionInfoStr) && functionInfoStr.endsWith("__c")) {
            FunctionInfo functionInfo = new FunctionInfo();
            functionInfo.setApiName(functionInfoStr);
            functionInfo.setReturnType("Number");
            functionInfo.setAplName(functionInfoStr);
            functionInfo.setBindObjectApiName("NONE");
            functionInfo.setNameSpace("fmcg_salary_kpi_calculate");
            functionInfo.setType("function");
            metric.setFunctionInfo(functionInfo);
        }else{
            metric.setFunctionInfo(FunctionInfo.of(functionInfoStr));
        }

        return metric;
    }

    @Data
    public static class FunctionInfo implements Serializable {
        @JSONField(name = "apl_api_name")
        @SerializedName("apl_api_name")
        @JsonProperty("apl_api_name")
        private String apiName;

        @JSONField(name = "apl_name")
        @SerializedName("apl_name")
        @JsonProperty("apl_name")
        private String aplName;

        @JSONField(name = "bind_object_api_name")
        @SerializedName("bind_object_api_name")
        @JsonProperty("bind_object_api_name")
        private String bindObjectApiName;

        @JSONField(name = "name_space")
        @SerializedName("name_space")
        @JsonProperty("name_space")
        private String nameSpace;

        @JSONField(name = "return_type")
        @SerializedName("return_type")
        @JsonProperty("return_type")
        private String returnType;

        @JSONField(name = "type")
        @SerializedName("type")
        @JsonProperty("type")
        private String type;

        public static FunctionInfo of(String functionInfo) {
            if (StringUtils.isEmpty(functionInfo)) {
                return new FunctionInfo();
            }
            return JSON.parseObject(functionInfo, FunctionInfo.class);
        }
    }
}