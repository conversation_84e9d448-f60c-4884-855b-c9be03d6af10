package com.facishare.crm.fmcg.wq.service.impl;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.constants.PublicEmployeeFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.crm.fmcg.wq.notify.SalaryRuleNotificationService;
import com.facishare.crm.fmcg.wq.service.SalaryRuleAdaptationService;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工资规则适配服务实现类
 * <p>
 * 该服务负责检查员工与工资规则的适配情况，包括：
 * 1. 员工部门角色信息获取
 * 2. 适用工资规则查询
 * 3. 员工固定工资表状态检查
 * 4. 定薪方式一致性验证
 * 5. 相应通知消息的触发
 * </p>
 *
 * <p>
 * 主要功能场景：
 * - 新员工入职时检查是否需要创建固定工资表
 * - 工资规则变更时检查影响的员工
 * - 定期检查定薪方式不一致的员工
 * - 批量员工适配情况检查
 * </p>
 *
 * <p>
 * 通知触发逻辑：
 * - 员工无固定工资表但有适用规则 → 发送创建固定工资表通知
 * - 员工定薪方式与规则不一致 → 发送定薪方式不一致通知
 * - 员工有多个适用规则 → 发送多规则适配通知
 * </p>
 *
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
@Slf4j
@Service
public class SalaryRuleAdaptationServiceImpl implements SalaryRuleAdaptationService {

    // 通知类型常量
    private static final String NOTIFICATION_TYPE_MISSING_FIXED_SALARY = "MISSING_FIXED_SALARY";
    private static final String NOTIFICATION_TYPE_SALARY_METHOD_MISMATCH = "SALARY_METHOD_MISMATCH";
    private static final String NOTIFICATION_TYPE_MULTIPLE_RULES_MATCH = "MULTIPLE_RULES_MATCH";

    // 默认消息常量
    private static final String DEFAULT_EMPLOYEE_NAME_PREFIX = "员工"; //ignoreI18n
    private static final String DEFAULT_EXTERNAL_EMPLOYEE_NAME_PREFIX = "外部员工"; //ignoreI18n
    private static final String DEFAULT_DEPARTMENT_NAME_PREFIX = "部门"; //ignoreI18n
    private static final String DEFAULT_ROLE_NAME_PREFIX = "角色"; //ignoreI18n
    private static final String DEFAULT_UNKNOWN_DEPARTMENT = "未知部门"; //ignoreI18n
    private static final String DEFAULT_UNKNOWN_ROLE = "未知角色"; //ignoreI18n

    // 通知范围常量
    private static final String NOTIFICATION_SCOPE_NEW_EMPLOYEE = "新增员工"; //ignoreI18n



    @Autowired
    private SalaryService salaryService;

    @Autowired
    private SalaryRuleDao salaryRuleDao;

    @Autowired
    private EmployeeFixedSalaryDao employeeFixedSalaryDao;

    @Autowired
    private EmployeeDao employeeDao;

    @Autowired
    private SalaryRuleNotificationService salaryRuleNotificationService;

    @Autowired
    private BaseDao baseDao;

    @Override
    public SalaryRuleAdaptationResult checkEmployeeSalaryRuleAdaptation(String tenantId, String employeeId, boolean isExternal) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(employeeId)) {
            log.warn("参数无效，tenantId: {}, employeeId: {}", tenantId, employeeId);
            return null;
        }

        try {
            log.info("开始检查员工工资规则适配情况，tenantId: {}, employeeId: {}, isExternal: {}",
                    tenantId, employeeId, isExternal);

            SalaryRuleAdaptationResult result = new SalaryRuleAdaptationResult();
            result.setEmployeeId(employeeId);
            result.setNotificationsSent(Lists.newArrayList());
            List<IObjectData> applicableRules = salaryService.getApplicableSalaryRulesForEmployee(tenantId, employeeId, isExternal);

            result.setApplicableRules(applicableRules);

            // 5. 检查员工固定工资表
            IObjectData employeeFixedSalary = employeeFixedSalaryDao.getByEmployeeId(tenantId, employeeId);
            result.setHasFixedSalary(employeeFixedSalary != null);

            if (employeeFixedSalary != null) {
                String fixedSalaryMethod = employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_METHOD, String.class);
                result.setFixedSalaryMethod(fixedSalaryMethod);
            }


            // 7. 处理规则适配并自动修复
            processEmployeeSalaryRuleAdaptationAndAutoFix(tenantId, result, isExternal, employeeFixedSalary);

            log.info("员工工资规则适配检查完成，tenantId: {}, employeeId: {}, 适用规则数: {}, 有固定工资表: {}",
                    tenantId, employeeId, applicableRules.size(), result.isHasFixedSalary());

            return result;

        } catch (Exception e) {
            log.error("检查员工工资规则适配情况失败，tenantId: {}, employeeId: {}", tenantId, employeeId, e);
            return null;
        }
    }

    @Override
    public BatchSalaryRuleAdaptationResult batchCheckEmployeeSalaryRuleAdaptation(String tenantId, List<String> employeeIds, boolean isExternal) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.isEmpty(employeeIds)) {
            log.warn("批量检查参数无效，tenantId: {}, employeeIds为空", tenantId);
            return null;
        }

        log.info("开始批量检查员工工资规则适配情况，tenantId: {}, 员工数量: {}, isExternal: {}",
                tenantId, employeeIds.size(), isExternal);

        BatchSalaryRuleAdaptationResult batchResult = new BatchSalaryRuleAdaptationResult();
        batchResult.setTotalCount(employeeIds.size());
        batchResult.setResults(Lists.newArrayList());
        batchResult.setErrors(Lists.newArrayList());

        int successCount = 0;
        int failureCount = 0;

        // 简单的串行处理
        for (String employeeId : employeeIds) {
            try {
                SalaryRuleAdaptationResult result = checkEmployeeSalaryRuleAdaptation(tenantId, employeeId, isExternal);
                if (result != null) {
                    batchResult.getResults().add(result);
                    successCount++;
                } else {
                    failureCount++;
                    batchResult.getErrors().add("员工 " + employeeId + " 检查失败"); //ignoreI18n
                }
            } catch (Exception e) {
                failureCount++;
                handleEmployeeCheckException(employeeId, e, batchResult);
            }
        }

        batchResult.setSuccessCount(successCount);
        batchResult.setFailureCount(failureCount);

        log.info("批量检查员工工资规则适配完成，tenantId: {}, 总数: {}, 成功: {}, 失败: {}",
                tenantId, employeeIds.size(), successCount, failureCount);

        return batchResult;
    }

    @Override
    public boolean checkNewEmployeeNeedsFixedSalary(String tenantId, String employeeId, boolean isExternal) {
        try {
            // 检查员工是否已有固定工资表
            IObjectData existingRecord = employeeFixedSalaryDao.getByEmployeeId(tenantId, employeeId);
            if (existingRecord != null) {
                log.info("员工已有固定工资表，无需创建，tenantId: {}, employeeId: {}", tenantId, employeeId);
                return false;
            }

            // 获取员工的部门和角色信息
            List<String> departmentIds = employeeDao.getEmployeeDepartments(tenantId, employeeId, isExternal);

            // 获取员工的互联企业ID（如果是外部员工）
            String outTenantId = null;
            if (isExternal) {
                outTenantId = getEmployeeOutTenantId(tenantId, employeeId);
            }

            List<String> roleIds = employeeDao.getEmployeeRoles(tenantId, employeeId, outTenantId, isExternal);

            // 查询该员工适用的工资规则
            List<IObjectData> applicableRules = salaryService.queryApplicableSalaryRules(
                    tenantId, employeeId, departmentIds, outTenantId, roleIds, isExternal, null);

            if (CollectionUtils.isEmpty(applicableRules)) {
                log.info("员工不适用任何工资规则，无需创建固定工资表，tenantId: {}, employeeId: {}", tenantId, employeeId);
                return false;
            }

            log.info("新员工需要创建固定工资表，tenantId: {}, employeeId: {}, 适用规则数: {}", tenantId, employeeId, applicableRules.size());

            // 发送新员工通知
            List<String> employeeIds = Arrays.asList(employeeId);
            boolean notificationSent = salaryRuleNotificationService.notifyNewEmployeeNeedsFixedSalary(tenantId, employeeIds, isExternal, NOTIFICATION_SCOPE_NEW_EMPLOYEE);
            log.info("新员工通知发送结果，tenantId: {}, employeeId: {}, 发送成功: {}", tenantId, employeeId, notificationSent);

            return true;

        } catch (Exception e) {
            log.error("检查新员工是否需要固定工资表失败，tenantId: {}, employeeId: {}", tenantId, employeeId, e);
            return false;
        }
    }

    @Override
    public List<String> checkMissingFixedSalaryEmployees(String tenantId, String salaryRuleId) {
        List<String> missingEmployeeIds = Lists.newArrayList();

        try {
            log.info("检查工资规则适用范围内缺失固定工资表的员工，tenantId: {}, salaryRuleId: {}", tenantId, salaryRuleId);

            // 1. 获取工资规则信息
            IObjectData salaryRule = salaryRuleDao.getById(tenantId, salaryRuleId);
            if (salaryRule == null) {
                log.warn("工资规则不存在，tenantId: {}, salaryRuleId: {}", tenantId, salaryRuleId);
                return missingEmployeeIds;
            }

            // 2. 获取工资规则适用范围内的所有员工
            List<String> applicableEmployeeIds = salaryService.getEmployeeIdsBySalaryRule(tenantId, salaryRule);
            if (CollectionUtils.isEmpty(applicableEmployeeIds)) {
                log.info("工资规则适用范围内没有员工，tenantId: {}, salaryRuleId: {}", tenantId, salaryRuleId);
                return missingEmployeeIds;
            }

            // 3. 检查哪些员工没有创建员工固定工资表
            for (String employeeId : applicableEmployeeIds) {
                IObjectData existingRecord = employeeFixedSalaryDao.getByEmployeeId(tenantId, employeeId);
                if (existingRecord == null) {
                    missingEmployeeIds.add(employeeId);
                }
            }

            log.info("发现 {} 个员工未创建员工固定工资表，tenantId: {}", missingEmployeeIds.size(), tenantId);

            // 4. 发送缺失固定工资表通知
            if (CollectionUtils.isNotEmpty(missingEmployeeIds)) {
                boolean notificationSent = salaryRuleNotificationService.notifyMissingEmployeeFixedSalaryForNewRule(tenantId, salaryRuleId);
                log.info("缺失固定工资表通知发送完成，tenantId: {}, 发送结果: {}", tenantId, notificationSent);
            }

        } catch (Exception e) {
            log.error("检查缺失固定工资表员工失败，tenantId: {}, salaryRuleId: {}", tenantId, salaryRuleId, e);
        }

        return missingEmployeeIds;
    }

    @Override
    public List<String> checkSalaryMethodMismatchEmployees(String tenantId, List<String> employeeIds) {
        List<String> mismatchEmployeeIds = Lists.newArrayList();

        try {
            List<String> targetEmployeeIds = employeeIds;

            // 如果没有指定员工列表，则查询所有有固定工资表的员工
            if (CollectionUtils.isEmpty(targetEmployeeIds)) {
                // TODO: 这里可以添加查询所有员工的逻辑
                log.info("未指定员工列表，跳过定薪方式一致性检查，tenantId: {}", tenantId);
                return mismatchEmployeeIds;
            }

            log.info("检查定薪方式不一致的员工，tenantId: {}, 员工数量: {}", tenantId, targetEmployeeIds.size());

            for (String employeeId : targetEmployeeIds) {
                try {
                    boolean isExternal = isExternalEmployee(employeeId);
                    boolean consistent = salaryService.checkSalaryMethodConsistency(tenantId, employeeId, isExternal);

                    if (!consistent) {
                        mismatchEmployeeIds.add(employeeId);
                    }
                } catch (Exception e) {
                    log.error("检查员工定薪方式一致性失败，tenantId: {}, employeeId: {}", tenantId, employeeId, e);
                }
            }

            log.info("发现 {} 个员工定薪方式不一致，tenantId: {}", mismatchEmployeeIds.size(), tenantId);

            // 发送定薪方式不一致通知
            if (CollectionUtils.isNotEmpty(mismatchEmployeeIds)) {
                boolean notificationSent = salaryRuleNotificationService.notifySalaryMethodMismatch(tenantId, mismatchEmployeeIds, null, null);
                log.info("定薪方式不一致通知发送完成，tenantId: {}, 发送结果: {}", tenantId, notificationSent);
            }

        } catch (Exception e) {
            log.error("检查定薪方式不一致员工失败，tenantId: {}", tenantId, e);
        }

        return mismatchEmployeeIds;
    }

    /**
     * 处理员工工资规则适配并自动修复
     * <p>
     * 核心规则适配和自动修复逻辑：
     * 1. 适配规则数量 = 0：检查是否需要清空固定工资表的规则ID并发送规则移出通知
     * 2. 适配规则数量 = 1：自动设置规则ID到固定工资表，检查其他问题
     * 3. 适配规则数量 > 1：检查固定工资表规则是否在适配范围内，决定是否发送多规则通知
     * </p>
     *
     * @param tenantId 租户ID
     * @param result 员工适配检查结果
     * @param isExternal 是否外部员工
     * @param employeeFixedSalary 员工固定工资表数据
     */
    private void processEmployeeSalaryRuleAdaptationAndAutoFix(String tenantId, SalaryRuleAdaptationResult result, boolean isExternal, IObjectData employeeFixedSalary) {
        try {
            String employeeId = result.getEmployeeId();
            List<IObjectData> applicableRules = result.getApplicableRules();
            //定薪方式也适配的规则
            List<IObjectData> applicableRulesWithSalaryMethod = (result.getApplicableRules() == null ? Lists.<IObjectData>newArrayList() : result.getApplicableRules())
                    .stream()
                    .filter(rule -> {
                        String ruleSalaryMethod = ObjectDataExt.of(rule).getStringValue(SalaryRuleFields.SALARY_METHOD);
                        return java.util.Objects.equals(ruleSalaryMethod, result.getFixedSalaryMethod());
                    })
                    .collect(Collectors.toList());
            List<IObjectData> safeApplicableRules = (applicableRules == null ? Lists.<IObjectData>newArrayList() : applicableRules);
            List<String> applicableRuleIds = safeApplicableRules.stream()
                    .map(IObjectData::getId)
                    .collect(Collectors.toList());
            int applicableRuleCount = CollectionUtils.isEmpty(applicableRules) ? 0 : applicableRules.size();

            // 获取固定工资表中的当前规则ID
            String currentRuleId = null;
            String currentRuleName = null;
            if (employeeFixedSalary != null) {
                currentRuleId = employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_RULE, String.class);
                if (StringUtils.isNotBlank(currentRuleId)) {
                    //查规则名字
                    currentRuleName = getRuleName(tenantId, currentRuleId);
                }
            }

            log.info("员工规则适配处理，employeeId: {}, 适配规则数量: {}, 有固定工资表: {}, 当前规则ID: {}, 当前规则: {}",
                    employeeId, applicableRuleCount, result.isHasFixedSalary(), currentRuleId, currentRuleName);

            // 情况1：没有适配的规则
            if (applicableRuleCount == 0) {
                // 如果有固定工资表且有规则ID，需要清空规则ID并发送规则移出通知
                if (employeeFixedSalary != null && StringUtils.isNotBlank(currentRuleId)) {
                    try {
                        // 清空固定工资表的规则ID
                        updateEmployeeFixedSalaryRule(tenantId, employeeFixedSalary.getId(), null);
                        log.info("已清空规则ID，employeeId: {}, 原规则: {}", employeeId, currentRuleName);

                        // 发送规则移出通知
                        List<IObjectData> employeeFixedSalaries = Arrays.asList(employeeFixedSalary);
                        boolean sent = salaryRuleNotificationService.notifyRuleRemoved(tenantId, employeeFixedSalaries, currentRuleName);
                        result.getNotificationsSent().add(buildNotificationSent(
                                "RULE_REMOVED", //ignoreI18n
                                "规则被移出通知", //ignoreI18n
                                sent,
                                null
                        ));
                        log.info("清空规则ID并发送规则移出通知，employeeId: {}, 移出规则: {}, 发送结果: {}", employeeId, currentRuleName, sent);
                    } catch (Exception e) {
                        log.error("清空规则ID或发送规则移出通知失败，employeeId: {}", employeeId, e);
                        result.getNotificationsSent().add(buildNotificationSent(
                                "RULE_REMOVED", //ignoreI18n
                                "规则被移出通知", //ignoreI18n
                                false,
                                e.getMessage()
                        ));
                    }
                } else {
                    log.info("员工无适配规则且固定工资表无规则ID，无需处理，employeeId: {}", employeeId);
                }
                return;
            }
            // 如果没有固定工资表，发送创建提醒
            if (!result.isHasFixedSalary()) {
                try {
                    List<String> employeeIds = Arrays.asList(employeeId);
                    boolean sent = salaryRuleNotificationService.notifyNewEmployeeNeedsFixedSalary(tenantId, employeeIds, isExternal, NOTIFICATION_SCOPE_NEW_EMPLOYEE);
                    result.getNotificationsSent().add(buildNotificationSent(
                            NOTIFICATION_TYPE_MISSING_FIXED_SALARY,
                            "员工缺失固定工资表通知", //ignoreI18n
                            sent,
                            null
                    ));
                    log.info("发送缺失固定工资表通知，employeeId: {}, 发送结果: {}", employeeId, sent);
                } catch (Exception e) {
                    log.error("发送缺失固定工资表通知失败，employeeId: {}", employeeId, e);
                    result.getNotificationsSent().add(buildNotificationSent(
                            NOTIFICATION_TYPE_MISSING_FIXED_SALARY,
                            "员工缺失固定工资表通知", //ignoreI18n
                            false,
                            e.getMessage()
                    ));
                }
                return;
            }
            // 情况2：适配1个规则
            if (applicableRuleCount == 1) {
                String targetRuleId = applicableRules.get(0).getId();
                // 如果有固定工资表，需要检查定薪方式一致性再决定是否更新规则ID
                if (employeeFixedSalary != null) {
                    // 先检查定薪方式一致性
                    if (CollectionUtils.isEmpty(applicableRulesWithSalaryMethod)) {
                        // 定薪方式不一致，发送通知但不更新规则ID
                        try {
                            List<String> employeeIds = Arrays.asList(employeeId);
                            boolean sent = salaryRuleNotificationService.notifySalaryMethodMismatch(tenantId, employeeIds, targetRuleId, applicableRules.get(0).get(SalaryRuleFields.SALARY_METHOD, String.class));
                            result.getNotificationsSent().add(buildNotificationSent(
                                    NOTIFICATION_TYPE_SALARY_METHOD_MISMATCH,
                                    "定薪方式不一致通知", //ignoreI18n
                                    sent,
                                    null
                            ));
                            log.info("定薪方式不一致，不更新规则ID，employeeId: {}, 发送通知结果: {}", employeeId, sent);
                        } catch (Exception e) {
                            log.error("发送定薪方式不一致通知失败，employeeId: {}", employeeId, e);
                            result.getNotificationsSent().add(buildNotificationSent(
                                    NOTIFICATION_TYPE_SALARY_METHOD_MISMATCH,
                                    "定薪方式不一致通知", //ignoreI18n
                                    false,
                                    e.getMessage()
                            ));
                        }
                    } else {
                        // 定薪方式一致，可以更新规则ID
                        if (StringUtils.isBlank(currentRuleId)) {
                            // 规则ID为空，自动设置规则ID
                            updateEmployeeFixedSalaryRule(tenantId, employeeFixedSalary.getId(), targetRuleId);
                            log.info("定薪方式一致，已自动设置规则ID，employeeId: {}, 设置规则ID: {}", employeeId, targetRuleId);
                        } else if (!targetRuleId.equals(currentRuleId)) {
                            // 规则ID不匹配，更新规则ID
                            updateEmployeeFixedSalaryRule(tenantId, employeeFixedSalary.getId(), targetRuleId);
                            log.info("定薪方式一致，已更新规则ID，employeeId: {}, 从 {} 更新到 {}", employeeId, currentRuleId, targetRuleId);
                        } else {
                            log.info("规则ID已匹配且定薪方式一致，无需更新，employeeId: {}, 规则ID: {}", employeeId, currentRuleId);
                        }
                    }
                }
                return;
            }

            // 情况3：适配多个规则
            if (applicableRuleCount > 1) {
                // 员工固定工资表存在
                if (employeeFixedSalary != null) {
                    // 多规则冲突：如果规则不包含在固定工资表内,清空规则ID并发送多规则适配通知
                    if (StringUtils.isNotBlank(currentRuleId) && !applicableRuleIds.contains(currentRuleId)) {
                        // 清空固定工资表的规则ID
                        updateEmployeeFixedSalaryRule(tenantId, employeeFixedSalary.getId(), null);
                        log.info("多规则冲突，已清空规则ID，employeeId: {}, 原规则: {}", employeeId, currentRuleId);
                    }
                    // 发送多规则适配通知
                    try {
                        //员工固定工资表规则为空,适配规则,定薪方式一致的只有一个,自动设置规则ID到固定工资表中,否则发送多规则适配通知
                        if (StringUtils.isBlank(employeeFixedSalary.get(EmployeeFixedSalaryFields.SALARY_RULE, String.class))) {
                            if (applicableRulesWithSalaryMethod.size() == 1){
                                //适配规则,定薪方式一致的只有一个,自动设置规则ID到固定工资表中
                                updateEmployeeFixedSalaryRule(tenantId, employeeFixedSalary.getId(), applicableRulesWithSalaryMethod.get(0).getId());
                                log.info("多规则冲突,但定薪方式一致的只有一个,已自动设置规则ID，employeeId: {}, 设置规则ID: {}", employeeId, applicableRulesWithSalaryMethod.get(0).getId());
                            }else{
                                List<String> employeeIds = Arrays.asList(employeeId);
                                boolean sent = salaryRuleNotificationService.notifyMultipleRulesMatch(tenantId, employeeIds, isExternal);
                                result.getNotificationsSent().add(buildNotificationSent(
                                        NOTIFICATION_TYPE_MULTIPLE_RULES_MATCH,
                                        "多规则适配通知", //ignoreI18n
                                        sent,
                                        null
                                ));
                                log.info("发送多规则适配通知，employeeId: {}, 规则数量: {}, 发送结果: {}",
                                        employeeId, applicableRuleCount, sent);
                            }
                        }
                    } catch (Exception e) {
                        log.error("发送多规则适配通知失败，employeeId: {}", employeeId, e);
                        result.getNotificationsSent().add(buildNotificationSent(
                                NOTIFICATION_TYPE_MULTIPLE_RULES_MATCH,
                                "多规则适配通知", //ignoreI18n
                                false,
                                e.getMessage()
                        ));
                    }
                }

                return;
            }

        } catch (Exception e) {
            log.error("处理员工规则适配失败，employeeId: {}", result.getEmployeeId(), e);
        }
    }

    /**
     * 获取员工姓名
     * <p>
     * 根据员工类型（内部/外部）从不同的数据源获取员工姓名：
     * - 外部员工：从PublicEmployeeObj获取
     * - 内部员工：从员工服务获取（当前返回默认格式）
     * </p>
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param isExternal 是否外部员工
     * @return 员工姓名，获取失败时返回默认格式
     */
    private String getEmployeeName(String tenantId, String employeeId, boolean isExternal) {
        try {
            if (isExternal) {
                // 外部员工：从PublicEmployeeObj获取
                IObjectData publicEmployee = employeeDao.getExternalEmployeeById(tenantId, employeeId);
                if (publicEmployee != null) {
                    String name = publicEmployee.get("name", String.class);
                    if (StringUtils.isNotBlank(name)) {
                        return name;
                    }
                }
                return DEFAULT_EXTERNAL_EMPLOYEE_NAME_PREFIX + employeeId;
            } else {
                // 内部员工：可以通过员工服务获取，这里暂时返回ID
                return DEFAULT_EMPLOYEE_NAME_PREFIX + employeeId;
            }
        } catch (Exception e) {
            log.error("获取员工姓名失败，employeeId: {}, isExternal: {}", employeeId, isExternal, e);
            return isExternal ? DEFAULT_EXTERNAL_EMPLOYEE_NAME_PREFIX + employeeId : DEFAULT_EMPLOYEE_NAME_PREFIX + employeeId;
        }
    }


    /**
     * 更新员工固定工资表的规则ID
     *
     * @param tenantId 租户ID
     * @param employeeFixedSalaryId 员工固定工资表ID
     * @param ruleId 规则ID，null表示清空
     */
    private void updateEmployeeFixedSalaryRule(String tenantId, String employeeFixedSalaryId, String ruleId) {
        try {
            IObjectData employeeFixedSalary = employeeFixedSalaryDao.getById(tenantId, employeeFixedSalaryId);
            if (employeeFixedSalary != null) {
                // 使用set方法更新规则ID
                if (ruleId == null) {
                    // 清空规则ID
                    employeeFixedSalary.set(EmployeeFixedSalaryFields.SALARY_RULE, null);
                } else {
                    // 设置规则ID
                    employeeFixedSalary.set(EmployeeFixedSalaryFields.SALARY_RULE, ruleId);
                }

                // 使用update方法更新数据
                employeeFixedSalaryDao.update(tenantId, "-10000", employeeFixedSalary);
                log.debug("更新员工固定工资表规则ID成功，employeeFixedSalaryId: {}, ruleId: {}", employeeFixedSalaryId, ruleId);
            } else {
                log.warn("员工固定工资表不存在，无法更新规则ID，employeeFixedSalaryId: {}", employeeFixedSalaryId);
            }
        } catch (Exception e) {
            log.error("更新员工固定工资表规则ID失败，employeeFixedSalaryId: {}, ruleId: {}", employeeFixedSalaryId, ruleId, e);
            throw new RuntimeException("更新员工固定工资表规则ID失败", e); //ignoreI18n
        }
    }

    /**
     * 获取外部员工的互联企业ID
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @return 互联企业ID，如果不是外部员工或查询失败返回null
     */
    private String getEmployeeOutTenantId(String tenantId, String employeeId) {
        try {
            // 查询PublicEmployeeObj获取外部员工的互联企业ID
            List<IObjectData> publicEmployees = baseDao.getAllIObjectDataListByQuery(
                    User.systemUser(tenantId),
                    SearchQuery.builder()
                            .eq(PublicEmployeeFields.OUTER_UID, employeeId)
                            .build(),
                    PublicEmployeeFields.API_NAME);

            if (CollectionUtils.isNotEmpty(publicEmployees)) {
                IObjectData publicEmployee = publicEmployees.get(0);
                String outTenantId = publicEmployee.get(PublicEmployeeFields.OUTER_TENANT_ID, String.class);
                log.debug("获取外部员工互联企业ID成功，employeeId: {}, outTenantId: {}", employeeId, outTenantId);
                return outTenantId;
            } else {
                log.warn("未找到外部员工记录，employeeId: {}", employeeId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取外部员工互联企业ID失败，employeeId: {}", employeeId, e);
            return null;
        }
    }

    /**
     * 获取规则名称
     *
     * @param tenantId 租户ID
     * @param ruleId 规则ID
     * @return 规则名称
     */
    private String getRuleName(String tenantId, String ruleId) {
        try {
            if (StringUtils.isBlank(ruleId)) {
                return "未知规则"; //ignoreI18n
            }

            // 通过DAO获取规则信息
            IObjectData rule = salaryRuleDao.getById(tenantId, ruleId);
            if (rule != null) {
                String ruleName = rule.get(BaseField.name.getApiName(), String.class);
                return StringUtils.isNotBlank(ruleName) ? ruleName : "规则" + ruleId; //ignoreI18n
            } else {
                return "规则" + ruleId; //ignoreI18n
            }
        } catch (Exception e) {
            log.warn("获取规则名称失败，ruleId: {}", ruleId, e);
            return "规则" + ruleId; //ignoreI18n
        }
    }

    /**
     * 判断是否为外部员工
     *
     * @param employeeId 员工ID
     * @return 是否为外部员工
     */
    private boolean isExternalEmployee(String employeeId) {
        if (StringUtils.isBlank(employeeId)) {
            log.warn("员工ID为空，默认为内部员工");
            return false;
        }

        try {
            long id = Long.parseLong(employeeId);
            boolean isExternal = id > 100000000L;
            log.debug("员工ID: {}, 是否外部员工: {}", employeeId, isExternal);
            return isExternal;
        } catch (NumberFormatException e) {
            log.warn("员工ID格式异常，employeeId: {}, 默认为内部员工", employeeId);
            return false;
        }
    }

    /**
     * 统一处理员工检查异常
     * <p>
     * 统一处理批量检查过程中单个员工的异常情况，
     * 记录错误信息并更新批量结果统计。
     * </p>
     *
     * @param employeeId 员工ID
     * @param e 异常信息
     * @param batchResult 批量结果对象
     */
    private void handleEmployeeCheckException(String employeeId, Exception e, BatchSalaryRuleAdaptationResult batchResult) {
        String errorMsg = String.format("员工 %s 检查异常: %s", employeeId, e.getMessage()); //ignoreI18n
        batchResult.getErrors().add(errorMsg);
        log.error("批量检查员工失败，employeeId: {}", employeeId, e);
    }

    /**
     * 构建默认的通知发送记录
     * <p>
     * 为通知发送结果创建标准化的记录对象，
     * 统一成功和失败情况的处理逻辑。
     * </p>
     *
     * @param notificationType 通知类型
     * @param message 通知消息描述
     * @param success 是否发送成功
     * @param errorMessage 错误消息（成功时为null）
     * @return 通知发送记录
     */
    private NotificationSent buildNotificationSent(String notificationType, String message, boolean success, String errorMessage) {
        return new NotificationSent(
                notificationType,
                message,
                success,
                success ? null : (errorMessage != null ? errorMessage : "发送失败") //ignoreI18n
        );
    }






}