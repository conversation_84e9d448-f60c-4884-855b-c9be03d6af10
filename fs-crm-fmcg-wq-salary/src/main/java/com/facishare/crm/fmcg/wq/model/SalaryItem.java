package com.facishare.crm.fmcg.wq.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * 通用薪资项目模型
 *
 * <AUTHOR>
 */
@Data
public class SalaryItem implements Serializable {
  /**
   * 薪资项目ID
   */
  @JSONField(name = "id")
  @JsonProperty("id")
  @SerializedName("id")
  private String id;

  /**
   * 薪资项目名称
   */
  @JSONField(name = "name")
  @JsonProperty("name")
  @SerializedName("name")
  private String name;

  /**
   * 薪资项目单位（可选）
   */
  @JSONField(name = "unit")
  @JsonProperty("unit")
  @SerializedName("unit")
  private String unit;

  /**
   * 薪资项目金额
   */
  @JSONField(name = "amount")
  @JsonProperty("amount")
  @SerializedName("amount")
  private String amount;

  public SalaryItem() {
  }

  public SalaryItem(String name, String id, String unit, String amount) {
    this.name = name;
    this.id = id;
    this.unit = unit;
    this.amount = amount;
  }

  /**
   * 从ID中提取工资项ID（去掉"si_"前缀）
   */
  public String extractSalaryItemId() {
    if (id != null && id.startsWith("si_") && id.length() > 3) {
      return id.substring(3);
    }
    return id;
  }

  @Override
  public String toString() {
    return "SalaryItem{" +
        "id='" + id + '\'' +
        ", name='" + name + '\'' +
        ", unit='" + unit + '\'' +
        ", amount='" + amount + '\'' +
        '}';
  }
}
