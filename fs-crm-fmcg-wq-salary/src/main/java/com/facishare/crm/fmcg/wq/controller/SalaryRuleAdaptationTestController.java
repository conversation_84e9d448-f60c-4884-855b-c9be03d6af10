package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.model.RestResult;
import com.facishare.crm.fmcg.wq.notify.SalaryRuleNotificationService;
import com.facishare.crm.fmcg.wq.service.SalaryRuleAdaptationService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 薪资规则适配测试 Controller
 * 参考 SalaryRuleAdaptationController，用于快速验证4种测试场景
 */
@Slf4j
public class SalaryRuleAdaptationTestController extends AbstractStandardController<SalaryRuleAdaptationTestController.Arg, SalaryRuleAdaptationTestController.Result> {

  private SalaryRuleAdaptationService salaryRuleAdaptationService = SpringUtil.getContext().getBean(SalaryRuleAdaptationService.class);
  private SalaryRuleNotificationService salaryRuleNotificationService = SpringUtil.getContext().getBean(SalaryRuleNotificationService.class);
  @Override
  protected List<String> getFuncPrivilegeCodes() {
    return Lists.newArrayList();
  }

  @Override
  protected Result doService(Arg arg) {
    String tenantId = controllerContext.getTenantId();

    if (arg == null || StringUtils.isBlank(arg.getTestType())) {
      throw new ValidateException("testType不能为空"); //ignoreI18n
    }

    try {
      switch (arg.getTestType()) {
        case "single_internal": {
          String employeeId = requireEmployeeId(arg.getEmployeeId());
          log.info("开始单员工内部测试, tenantId: {}, employeeId: {}", tenantId, employeeId);
          SalaryRuleAdaptationService.SalaryRuleAdaptationResult res =
              salaryRuleAdaptationService.checkEmployeeSalaryRuleAdaptation(tenantId, employeeId, false);
          return Result.ok(res);
        }
        case "single_external": {
          String employeeId = requireEmployeeId(arg.getEmployeeId());
          log.info("开始单员工外部测试, tenantId: {}, employeeId: {}", tenantId, employeeId);
          SalaryRuleAdaptationService.SalaryRuleAdaptationResult res =
              salaryRuleAdaptationService.checkEmployeeSalaryRuleAdaptation(tenantId, employeeId, true);
          return Result.ok(res);
        }
        case "batch_internal": {
          List<String> employeeIds = requireEmployeeIds(arg.getEmployeeIds());
          log.info("开始批量内部员工测试, tenantId: {}, size: {}", tenantId, employeeIds.size());
          SalaryRuleAdaptationService.BatchSalaryRuleAdaptationResult res =
              salaryRuleAdaptationService.batchCheckEmployeeSalaryRuleAdaptation(tenantId, employeeIds, false);
          return Result.ok(res);
        }
        case "check_new_employee": {
          String employeeId = requireEmployeeId(arg.getEmployeeId());
          boolean isExternal = isExternalEmployee(employeeId);
          log.info("开始新员工固定工资表检查, tenantId: {}, employeeId: {}, isExternal: {}", tenantId, employeeId, isExternal);
          boolean need = salaryRuleAdaptationService.checkNewEmployeeNeedsFixedSalary(tenantId, employeeId, isExternal);
          return Result.ok(Boolean.valueOf(need));
        }
        case "session_MethodMismatch": {
          List<String> employeeIds = requireEmployeeIds(arg.getEmployeeIds());
          log.info("开始定薪方式不一致检查, tenantId: {}, size: {}", tenantId, employeeIds.size());
         boolean mismatchEmployeeIds = salaryRuleNotificationService.notifySalaryMethodMismatch(tenantId, employeeIds, "68777e736b98970007b70d56", "2");
          return Result.ok(mismatchEmployeeIds);
        }
        case "session_NewEmployee": {
          List<String> employeeIds = requireEmployeeIds(arg.getEmployeeIds());
          log.info("开始新员工通知检查, tenantId: {}, size: {}", tenantId, employeeIds.size());
          boolean sent = salaryRuleNotificationService.notifyNewEmployeeNeedsFixedSalary(tenantId, employeeIds, false, "test");
          return Result.ok(sent);
        }
        //notifyMissingEmployeeFixedSalaryForNewRule
        case "session_NewRule": {
            boolean sent = salaryRuleNotificationService.notifyMissingEmployeeFixedSalaryForNewRule(tenantId, "68777e736b98970007b70d56");
            return Result.ok(sent);
        }
        case "session_MultipleRules": {
          List<String> employeeIds = requireEmployeeIds(arg.getEmployeeIds());
          log.info("开始多规则检查, tenantId: {}, size: {}", tenantId, employeeIds.size());
          boolean sent = salaryRuleNotificationService.notifyMultipleRulesMatch(tenantId, employeeIds, false);
          return Result.ok(sent);
        }
        case "session_RuleRemoved": {
          List<String> employeeIds = requireEmployeeIds(arg.getEmployeeIds());
          log.info("开始规则被移除检查, tenantId: {}, size: {}", tenantId, employeeIds.size());
          IObjectData employeeFixedSalary = serviceFacade.findObjectData(controllerContext.getUser(), "6877706a6b98970007b6c8ee", "EmployeeFixedSalaryObj");
          List<IObjectData> employeeFixedSalaries = Lists.newArrayList(employeeFixedSalary);
          boolean sent = salaryRuleNotificationService.notifyRuleRemoved(tenantId, employeeFixedSalaries, "test");
          return Result.ok(sent);
        }
        default:
          throw new ValidateException("不支持的testType: " + arg.getTestType()); //ignoreI18n
      }
    } catch (Exception e) {
      log.error("薪资规则适配测试失败, tenantId: {}", tenantId, e);
      return Result.fail("处理失败: " + e.getMessage()); //ignoreI18n
    }
  }

  private String requireEmployeeId(String employeeId) {
    if (StringUtils.isBlank(employeeId)) {
      throw new ValidateException("employeeId不能为空"); //ignoreI18n
    }
    return employeeId;
  }

  private List<String> requireEmployeeIds(List<String> employeeIds) {
    if (employeeIds == null || employeeIds.isEmpty()) {
      throw new ValidateException("employeeIds不能为空"); //ignoreI18n
    }
    return employeeIds;
  }

  /**
   * 简单判断外部员工（同 SalaryRuleAdaptationController）
   */
  private boolean isExternalEmployee(String employeeId) {
    if (StringUtils.isBlank(employeeId)) {
      log.warn("员工ID为空，默认为内部员工");
      return false;
    }
    try {
      long id = Long.parseLong(employeeId);
      boolean isExternal = id > 100000000L;
      log.debug("员工ID: {}, 是否外部员工: {}", employeeId, isExternal);
      return isExternal;
    } catch (NumberFormatException e) {
      log.warn("员工ID格式异常，employeeId: {}, 默认为内部员工", employeeId);
      return false;
    }
  }

  @Data
  public static class Arg implements Serializable {
    /**
     * 测试类型：single_internal | single_external | batch_internal | check_new_employee
     */
    private String testType;
    /**
     * 单个员工ID（single_*、check_new_employee场景使用）
     */
    private String employeeId;
    /**
     * 批量员工ID（batch_internal场景使用）
     */
    private List<String> employeeIds;
  }

  @Data
  public static class Result extends RestResult<Object> {
    private boolean success;
    private String message;

    public Result() {
      super();
    }

    private static Result ok(Object data) {
      Result r = new Result();
      r.setSuccess(true);
      r.setMessage("OK"); //ignoreI18n
      r.setData(data);
      return r;
    }

    private static Result fail(String msg) {
      Result r = new Result();
      r.setSuccess(false);
      r.setMessage(msg);
      return r;
    }
  }
}

