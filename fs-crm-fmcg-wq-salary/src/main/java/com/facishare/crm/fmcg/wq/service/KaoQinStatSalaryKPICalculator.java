package com.facishare.crm.fmcg.wq.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.wq.constants.SystemConstants;
import com.facishare.crm.fmcg.wq.model.AggregateFunction;
import com.facishare.crm.fmcg.wq.model.KaoQinFieldType;
import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.RestResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.exception.AbandonActionException;
import com.facishare.crm.fmcg.wq.model.kpi.AggregateSalaryKPI;
import com.facishare.crm.fmcg.wq.model.kpi.KaoQinStatSalaryKPI;
import com.facishare.crm.fmcg.wq.proxy.CheckinsOfficeProxy;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 聚合指标计算器
 */
@Slf4j
@SuppressWarnings("Duplicates")
@Component("kaoQinStatSalaryKPICalculator")
public class KaoQinStatSalaryKPICalculator extends SalaryKPICalculator<KaoQinStatSalaryKPI> {

    @Resource
    private ServiceFacade serviceFacade;

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private CheckinsOfficeProxy checkinsOfficeProxy;

    /**
     * 进行聚合查询，计算聚合值
     *
     * @param context 激励上下文
     * @param metric  指标
     * @return 聚合查询结果
     */
    @Override
    public MetricCalculateResult doCalculate(SalaryContext context, KaoQinStatSalaryKPI metric) {
        MetricCalculateResult metricCalculateResult = new MetricCalculateResult();
        if (context.getExtDataMap() != null
                && context.getExtDataMap().containsKey(metric.getKaoQinFieldType().toString())) {
            metricCalculateResult.setValue(
                    context.getExtDataMap().getOrDefault(metric.getKaoQinFieldType().toString(), "0").toString());
        } else {
            setKaoQinStatMap(context);
            metricCalculateResult.setValue(
                    context.getExtDataMap().getOrDefault(metric.getKaoQinFieldType().toString(), "0").toString());
        }
        return metricCalculateResult;
    }

    private void setKaoQinStatMap(SalaryContext context) {
        if (context.getExtDataMap() == null) {
            context.setExtDataMap(Maps.newHashMap());
        }
        String ea = eieaConverter.enterpriseIdToAccount(Integer.valueOf(context.getTenantId()));
        if (Strings.isNullOrEmpty(ea)) {
            throw new AbandonActionException("找不到企业信息"); //ignoreI18n
        }
        // 考勤接口 /** 查询开始日期 */
        // @Tag(10)
        // @JSONField(name = "M10")
        // private String startDate;
        //
        // /** 查询结束日期 */
        // @Tag(11)
        // @JSONField(name = "M11")
        // private String endDate;
        //
        // /** 部门id列表 */
        // @Tag(12)
        // @JSONField(name = "M12")
        // private List<Integer> deptIds;
        //
        // /** 员工id列表 */
        // @Tag(13)
        // @JSONField(name = "M13")
        // private List<Integer> userIds;
        //
        // /**
        // * 分页类型，0-按页号分页，1-下拉加载
        // */
        // @Tag(14)
        // @JSONField(name = "M14")
        // private int pageType;
        //
        // /**
        // * 每页查询数量 -1代表全量查询
        // */
        // @Tag(15)
        // @JSONField(name = "M15")
        // private int pageSize;
        //
        // /**
        // * pageType =0 使用， 翻页页号
        // */
        // @Tag(16)
        // @JSONField(name = "M17")
        // private int pageNum;
        //
        // /**
        // * pageType = 1，下拉加载
        // */
        // @Tag(17)
        // @JSONField(name = "M17")
        // private long lastTime;
        //
        // /**
        // * 是否显示休息日打卡 0-都显示 1-不显示休息日
        // */
        // @Tag(18)
        // @JSONField(name = "M18")
        // private int isWorkDayShow;
        //
        // /**
        // *是否显示系统异常和地点异常 0-不显示 1-显示
        // */
        // @Tag(19)
        // @JSONField(name="M19")
        // private int isSystemExcShow;
        //
        // /**
        // *是否将批注展开到单元格中 0-不显示 1-显示
        // */
        // @Tag(20)
        // @JSONField(name = "M20")
        // private int isCellExpand;
        //
        // /**
        // * 0（批注中展示），1（直接在单元格中展示）
        // */
        // @Tag(21)
        // @JSONField(name = "21")
        // private int showNotes;
        // @Tag(22)
        // @JSONField(name = "M22")
        // private List<String> roles;
        //
        // /** 外部员工id列表 */
        // @Tag(23)
        // @JSONField(name = "M23")
        // private List<Integer> outerUserIds;
        // @Tag(24)
        // @JSONField(name = "M24")
        // private List<String> outerRoleIds;
        //
        // /**
        // *是否来自外部 1 是来自外部
        // */
        // @Tag(25)
        // @JSONField(name = "M25")
        // private int isOuter;
        // /**
        // * 取交集的角色id
        // */
        // @Tag(26)
        // @JSONField(name = "M26")
        // private List<String> intersectRoles;
        //
        // /**
        // *是否新的外部排班 1 是
        // */
        // @Tag(27)
        // @JSONField(name = "M27")
        // private int isNewOuterSchedule;
        // @Tag(28)
        // @JSONField(name = "M28")
        // private List<Long> outerTenantIds;
        // /**
        // * 是否包含停用员工
        // * 0-不包含
        // * 1-包含
        // */
        // @Tag(29)
        // @JSONField(name = "M29")
        // private int includeStop;
        JSONObject args = new JSONObject();
        args.put("startDate", DateUtils.getStringFromTime(context.getStartTime(), DateUtils.DateFormat));
        args.put("endDate", DateUtils.getStringFromTime(context.getEndTime(), DateUtils.DateFormat));
        args.put("includeStop", 1);
        // 判断是否来自外部
        if (Integer.valueOf(context.getOwner()).intValue() > 100000000) {
            args.put("outerUserIds", Lists.newArrayList(Integer.valueOf(context.getOwner())));
            args.put("isOuter", 1);
        } else {
            args.put("userIds", Lists.newArrayList(Integer.valueOf(context.getOwner())));
            args.put("isOuter", 0);
        }
        //  monthArgs.setPageSize(1);
        //        monthArgs.setPageNum(1);
        //        monthArgs.setIsWorkDayShow(0);
        //        monthArgs.setIsSystemExcShow(1);
        //        monthArgs.setIsOuter(userId > 1000000 ? 1 : 0);
        args.put("pageType", 0);
        args.put("pageSize", 1);
        args.put("pageNum", 1);
        args.put("isWorkDayShow", 0);
        args.put("isSystemExcShow", 1);
        RestResult<Map<Integer, Map<String, Object>>> restResult = checkinsOfficeProxy.queryMonthStatFields(context.getTenantId(), args);
        //增加日志 开始结束 时间 人 返回值
        log.info("KaoQinStatSalaryKPICalculator#setKaoQinStatMap : {}, {}, {}, {}, {}", context.getStartTime(), context.getEndTime(), context.getOwner(), args, restResult);
        if (restResult == null || restResult.getData() == null) {
            return;
        }
        Map<Integer, Map<String, Object>> data = restResult.getData();
        if (data == null || data.isEmpty()) {
            return;
        }
        for (Map.Entry<Integer, Map<String, Object>> entry : data.entrySet()) {
            for (KaoQinFieldType value : KaoQinFieldType.values()) {
                context.getExtDataMap().put(value.toString(), entry.getValue().get(value.toString()));
            }
        }
        context.watch().lap("KaoQinStatSalaryKPICalculator#setKaoQinStatMap." + context.getOwner());
    }

}
