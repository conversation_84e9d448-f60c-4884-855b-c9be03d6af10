package com.facishare.crm.fmcg.wq.service.decorator;

import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.service.SalaryExpressionCalcService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.ExpressionCheck;
import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import com.fxiaoke.common.Pair;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 工资项表达式计算重试装饰器
 * 为工资项表达式计算提供三次重试机制，增强系统稳定性
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Getter
@Slf4j
public class RetrySalaryExpressionCalcService implements SalaryExpressionCalcService {

    /**
     * -- GETTER --
     *  获取被装饰的服务
     *
     * @return 被装饰的服务
     */
    private final SalaryExpressionCalcService decorated;
    /**
     * -- GETTER --
     *  获取最大重试次数
     *
     * @return 最大重试次数
     */
    private final int maxRetries;
    /**
     * -- GETTER --
     *  获取重试间隔时间
     *
     * @return 重试间隔时间（毫秒）
     */
    private final long retryDelayMs;

  /**
   * 构造函数
   * 
   * @param decorated 被装饰的表达式计算服务
   */
  public RetrySalaryExpressionCalcService(SalaryExpressionCalcService decorated) {
    this(decorated, 3, 100L);
  }

  /**
   * 构造函数
   * 
   * @param decorated 被装饰的表达式计算服务
   * @param maxRetries 最大重试次数
   * @param retryDelayMs 重试间隔时间（毫秒）
   */
  public RetrySalaryExpressionCalcService(SalaryExpressionCalcService decorated, int maxRetries, long retryDelayMs) {
    this.decorated = decorated;
    this.maxRetries = maxRetries;
    this.retryDelayMs = retryDelayMs;
  }

  @Override
  public List<String> extractKpiIdsFromExpression(String expression) {
    // KPI ID提取不需要重试，直接调用原方法
    return decorated.extractKpiIdsFromExpression(expression);
  }

  @Override
  public ExpressionCheck.Result check(ExpressionDTO expressionDTO, ServiceContext context) {
    // 表达式检查不需要重试，直接调用原方法
    return decorated.check(expressionDTO, context);
  }

  @Override
  public Pair<String, Object> calculateWithExpression(ExpressionDTO expressionDTO, SalaryContext context) {
    Exception lastException = null;
    String expressionInfo = getExpressionInfo(expressionDTO, context);
    
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        log.debug("工资项表达式计算尝试第{}次，{}", attempt, expressionInfo);
        
        Pair<String, Object> result = decorated.calculateWithExpression(expressionDTO, context);
        
        if (attempt > 1) {
          log.info("工资项表达式计算重试成功，{}, 尝试次数: {}", expressionInfo, attempt);
        }
        
        return result;
        
      } catch (Exception e) {
        lastException = e;
        
        // 判断是否应该重试
        if (!shouldRetry(e)) {
          log.warn("工资项表达式计算遇到不可重试的异常，{}, 异常: {}", expressionInfo, e.getMessage());
          throw e;
        }
        
        if (attempt < maxRetries) {
          log.warn("工资项表达式计算失败，准备重试，{}, 尝试次数: {}/{}, 错误: {}", 
                  expressionInfo, attempt, maxRetries, e.getMessage());
          
          // 等待一段时间后重试，使用指数退避策略
          try {
            long delay = retryDelayMs * (1L << (attempt - 1)); // 指数退避：100ms, 200ms, 400ms
            Thread.sleep(delay);
          } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            log.warn("工资项表达式计算重试等待被中断，{}", expressionInfo);
            break;
          }
        } else {
          log.error("工资项表达式计算重试失败，已达到最大重试次数，{}, 最大重试次数: {}", 
                  expressionInfo, maxRetries, e);
        }
      }
    }
    
    // 所有重试都失败，抛出最后一次的异常
    throw new RuntimeException("工资项表达式计算失败，已重试" + maxRetries + "次，" + expressionInfo, lastException);//ignoreI18n
  }

  /**
   * 获取表达式信息用于日志记录
   * 
   * @param expressionDTO 表达式DTO
   * @param context 薪资上下文
   * @return 表达式信息字符串
   */
  private String getExpressionInfo(ExpressionDTO expressionDTO, SalaryContext context) {
    String expression = expressionDTO != null ? expressionDTO.getExpression() : "null";
    String owner = context != null ? context.getOwner() : "unknown";
    
    // 截断过长的表达式用于日志显示
    if (expression != null && expression.length() > 100) {
      expression = expression.substring(0, 100) + "...";
    }
    
    return String.format("表达式: [%s], 员工: %s", expression, owner);//ignoreI18n
  }

  /**
   * 判断异常是否应该重试
   * 可以根据具体的异常类型来决定是否重试
   * 
   * @param exception 异常
   * @return 是否应该重试
   */
  private boolean shouldRetry(Exception exception) {
    // 对于以下类型的异常，不进行重试（通常是业务逻辑错误或配置错误）
    if (exception instanceof IllegalArgumentException ||
        exception instanceof NullPointerException ||
        exception instanceof ClassCastException ||
        exception instanceof NumberFormatException) {
      return false;
    }
    
    // 检查异常消息，对于明显的语法错误或配置错误不重试
//    String message = exception.getMessage();
//    if (message != null) {
//      String lowerMessage = message.toLowerCase();
//
//      // 不重试的情况：语法错误、配置错误等
//      if (lowerMessage.contains("syntax error") ||
//          lowerMessage.contains("语法错误") ||//ignoreI18n
//          lowerMessage.contains("invalid expression") ||
//          lowerMessage.contains("表达式无效") ||//ignoreI18n
//          lowerMessage.contains("compile error") ||
//          lowerMessage.contains("编译错误")) {//ignoreI18n
//        return false;
//      }
//
//      // 重试的情况：网络异常、超时异常、系统异常等
//      if (lowerMessage.contains("timeout") ||
//          lowerMessage.contains("connection") ||
//          lowerMessage.contains("network") ||
//          lowerMessage.contains("socket") ||
//          lowerMessage.contains("服务异常") ||//ignoreI18n
//          lowerMessage.contains("系统繁忙") ||//ignoreI18n
//          lowerMessage.contains("service unavailable") ||
//          lowerMessage.contains("internal server error")) {
//        return true;
//      }
//    }
    
    // 默认进行重试
    return true;
  }

}
