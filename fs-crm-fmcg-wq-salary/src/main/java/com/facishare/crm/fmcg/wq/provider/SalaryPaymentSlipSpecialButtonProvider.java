package com.facishare.crm.fmcg.wq.provider;

import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 工资发放单特殊按钮提供者
 *
 * 提供工资发放单的特殊功能按钮：
 * 1. 更新工资数据 - 重新计算工资条数据
 * 2. 下发工资条 - 发送工资条通知给员工
 */
@Component
public class SalaryPaymentSlipSpecialButtonProvider implements SpecialButtonProvider {
    
    @Override
    public String getApiName() {
        return SalaryPaymentSlipFields.API_NAME;
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = new ArrayList<>(2);

        // 更新工资数据按钮
        buttons.add(ButtonUtils.buildButton(ObjectAction.UPDATE_SALARY_DATA));

        // 下发工资条按钮
        buttons.add(ButtonUtils.buildButton(ObjectAction.DISTRIBUTE));

        return buttons;
    }
}
