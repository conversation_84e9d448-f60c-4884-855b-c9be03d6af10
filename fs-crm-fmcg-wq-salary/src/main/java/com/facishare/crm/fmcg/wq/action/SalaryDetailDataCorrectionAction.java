//package com.facishare.crm.fmcg.wq.action;
//
//import com.facishare.crm.fmcg.wq.constants.SalaryDataFields;
//import com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields;
//import com.facishare.crm.fmcg.wq.dao.SalaryDataDao;
//import com.facishare.crm.fmcg.wq.dao.SalaryDetailDataDao;
//import com.facishare.crm.fmcg.wq.model.FmcgPreActionArgs;
//import com.facishare.crm.fmcg.wq.util.FieldValidationUtil;
//import com.facishare.paas.appframework.common.util.ObjectAction;
//import com.facishare.paas.appframework.core.exception.ValidateException;
//import com.facishare.paas.appframework.core.model.User;
//import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
//import com.facishare.paas.appframework.metadata.ObjectDataExt;
//import com.facishare.paas.metadata.api.IObjectData;
//import com.facishare.paas.metadata.api.describe.IFieldDescribe;
//import com.facishare.paas.metadata.util.SpringUtil;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.util.List;
//
///**
// * 工资条明细修正数据Action
// *
// * 功能：
// * 1. 允许修改工资条明细的金额
// * 2. 自动将发放状态改为"已修正"
// * 3. 记录修正操作日志
// */
//@Slf4j
//public class SalaryDetailDataCorrectionAction extends FmcgAbstractStandardAction<SalaryDetailDataCorrectionAction.Arg, SalaryDetailDataCorrectionAction.Result> {
//
//    private SalaryDetailDataDao salaryDetailDataDao = SpringUtil.getContext().getBean(SalaryDetailDataDao.class);
//    private SalaryDataDao salaryDataDao = SpringUtil.getContext().getBean(SalaryDataDao.class);
//
//
//    @Override
//    protected IObjectData getPreObjectData() {
//        return dbData;
//    }
//
//    @Override
//    protected IObjectData getPostObjectData() {
//        return objectData;
//    }
//
//
//
//    @Override
//    protected ObjectAction getObjectAction() {
//        return ObjectAction.CORRECTION;
//    }
//
//    @Override
//    protected Result after(Arg arg, Result result) {
//        return super.after(arg, result);
//    }
//
//    @Override
//    protected List<String> getFuncPrivilegeCodes() {
//        return null;
//    }
//
//    @Override
//    protected List<String> getDataPrivilegeIds(Arg arg) {
//        return null;
//    }
//
//    @Override
//    protected void before(Arg arg) {
//        super.before(arg);
//
//        String tenantId = actionContext.getTenantId();
//        String salaryDetailDataId = arg.getObjectDataId();
//
//        log.info("开始处理工资条明细修正，明细ID: {}", salaryDetailDataId);
//        if (objectData == null) {
//            objectData = salaryDetailDataDao.getById(tenantId, salaryDetailDataId);
//        }
//        // 验证修正数据
//        validateCorrectionData(arg, objectData);
//
//        // 修改objectData中的金额和状态
//        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
//        objectDataExt.set(SalaryDetailDataFields.AMOUNT, arg.getAmount());
//        objectDataExt.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_4);
//
//        log.info("工资条明细修正前处理完成，明细ID: {}, 新金额: {}", salaryDetailDataId, arg.getAmount());
//    }
//
//    @Override
//    protected Result doAct(Arg arg) {
//
//
//        String tenantId = actionContext.getTenantId();
//        String salaryDetailDataId = arg.getObjectDataId();
//
//        // 保存修改后的数据
//        IObjectData updatedData = salaryDetailDataDao.update(User.systemUser(tenantId), objectData);
//
//        // 记录修改日志
//        recodeLog();
//
//        // 同步对应的工资条对象（如果存在）
//        syncRelatedSalaryData(tenantId, updatedData);
//
//        Result result = new Result();
//        result.setSuccess(true);
//        result.setMessage("工资条明细修正成功");
//
//        log.info("工资条明细修正完成，明细ID: {}, 新金额: {}", salaryDetailDataId, arg.getAmount());
//
//        return result;
//    }
//
//    /**
//     * 验证修正数据
//     * @param arg 修正参数
//     */
//    private void validateCorrectionData(Arg arg,IObjectData currentData) {
//        BigDecimal amount = arg.getAmount();
//
//        if (amount == null) {
//            throw new ValidateException("金额不能为空"); //ignoreI18n
//        }
//
//        // 金额不能为负数
//        if (amount.compareTo(BigDecimal.ZERO) < 0) {
//            throw new ValidateException("金额不能为负数"); //ignoreI18n
//        }
//
//        // 金额精度检查（最多2位小数）
//        if (amount.scale() > 3) {
//            throw new ValidateException("金额最多保留3位小数"); //ignoreI18n
//        }
//
//        // 验证发放状态 - 已发放的数据不能修正
//        if (currentData == null) {
//            throw new ValidateException("工资条明细不存在"); //ignoreI18n
//        }
//
//        String distributionStatus = currentData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);
//        if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3.equals(distributionStatus)) {
//            throw new ValidateException("已发放的工资条明细不能修正"); //ignoreI18n
//        }
//
//        log.info("修正数据验证通过，金额: {}, 当前发放状态: {}", amount, distributionStatus);
//    }
//
//    /**
//     * 同步相关的工资条对象
//     * 当工资条明细修正后，需要重新计算工资条的总金额
//     *
//     * @param tenantId 租户ID
//     * @param salaryDetailData 修正后的工资条明细
//     */
//    private void syncRelatedSalaryData(String tenantId, IObjectData salaryDetailData) {
//        try {
//            // 获取工资条ID
//            String salaryDataId = salaryDetailData.get(SalaryDetailDataFields.SALARY_DATA, String.class);
//
//            if (StringUtils.isBlank(salaryDataId)) {
//                log.info("工资条明细未关联工资条，跳过同步，明细ID: {}", salaryDetailData.getId());
//                return;
//            }
//
//            // 获取工资条对象
//            IObjectData salaryData = salaryDataDao.getById(tenantId, salaryDataId);
//            if (salaryData == null) {
//                log.warn("未找到关联的工资条，工资条ID: {}", salaryDataId);
//                return;
//            }
//
//            log.info("开始同步工资条，工资条ID: {}", salaryDataId);
//
//            // 重新计算工资条的总金额
//            BigDecimal totalAmount = calculateSalaryDataTotalAmount(tenantId, salaryDataId);
//
//            // 更新工资条的总金额
//            ObjectDataExt salaryDataExt = ObjectDataExt.of(salaryData);
//            salaryDataExt.set(SalaryDataFields.PAYABLE_SALARY, totalAmount.toString());
//
//            // 保存工资条
//            salaryDataDao.updateSalaryData(User.systemUser(tenantId), salaryData);
//
//            log.info("工资条同步完成，工资条ID: {}, 新总金额: {}", salaryDataId, totalAmount);
//
//        } catch (Exception e) {
//            log.error("同步工资条失败，明细ID: {}", salaryDetailData.getId(), e);
//            // 不抛出异常，避免影响主流程
//        }
//    }
//
//    /**
//     * 计算工资条的总金额
//     * 通过汇总所有相关工资条明细的金额
//     *
//     * @param tenantId 租户ID
//     * @param salaryDataId 工资条ID
//     * @return 总金额
//     */
//    private BigDecimal calculateSalaryDataTotalAmount(String tenantId, String salaryDataId) {
//        try {
//            // 查询该工资条下的所有明细
//            List<IObjectData> detailDataList = salaryDetailDataDao.getAllIObjectDataListByQueryWithFields(
//                User.systemUser(tenantId),
//                com.facishare.crm.fmcg.wq.util.SearchQuery.builder()
//                    .eq(SalaryDetailDataFields.SALARY_DATA, salaryDataId)
//                    .build(),
//                SalaryDetailDataFields.API_NAME,
//                null
//            );
//
//            BigDecimal totalAmount = BigDecimal.ZERO;
//
//            for (IObjectData detailData : detailDataList) {
//                String amountStr = detailData.get(SalaryDetailDataFields.AMOUNT, String.class);
//                String incrementDecrementAttrib = detailData.get(SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB, String.class);
//
//                if (StringUtils.isNotBlank(amountStr)) {
//                    try {
//                        BigDecimal amount = new BigDecimal(amountStr);
//
//                        // 根据增减属性判断是加还是减
//                        if (SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
//                            // 减少
//                            totalAmount = totalAmount.subtract(amount);
//                            log.debug("工资条明细减少金额，明细ID: {}, 金额: -{}", detailData.getId(), amount);
//                        } else {
//                            // 增加（默认为增加，包括null和"1"的情况）
//                            totalAmount = totalAmount.add(amount);
//                            log.debug("工资条明细增加金额，明细ID: {}, 金额: +{}", detailData.getId(), amount);
//                        }
//                    } catch (NumberFormatException e) {
//                        log.warn("无法解析工资条明细金额: {}, 明细ID: {}", amountStr, detailData.getId());
//                    }
//                }
//            }
//
//            log.info("计算工资条总金额完成，工资条ID: {}, 明细数量: {}, 总金额: {}",
//                    salaryDataId, detailDataList.size(), totalAmount);
//            totalAmount.setScale(3);
//            return totalAmount;
//
//        } catch (Exception e) {
//            log.error("计算工资条总金额失败，工资条ID: {}", salaryDataId, e);
//            return BigDecimal.ZERO;
//        }
//    }
//
//    /**
//     * 修正数据参数
//     */
//    @Data
//    @EqualsAndHashCode(callSuper = true)
//    public static class Arg extends FmcgPreActionArgs  {
//        /**
//         * 修正后的金额
//         */
//        private BigDecimal amount;
//
//        /**
//         * 修正原因（可选）
//         */
//        private String reason;
//    }
//
//    /**
//     * 修正数据结果
//     */
//    @Data
//    public static class Result {
//        /**
//         * 是否成功
//         */
//        private boolean success;
//
//        /**
//         * 消息
//         */
//        private String message;
//    }
//
//
//}
