package com.facishare.crm.fmcg.wq.model.kpi;

import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.crm.fmcg.wq.model.KPICalculateType;
import com.facishare.crm.fmcg.wq.model.KaoQinFieldType;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class KaoQinStatSalaryKPI extends SalaryKPI {

    private KaoQinFieldType kaoQinFieldType;


    @SuppressWarnings("Duplicates")
    public static KaoQinStatSalaryKPI of(IObjectData data) {
        KaoQinStatSalaryKPI metric = new KaoQinStatSalaryKPI();

        metric.setTenantId(data.getTenantId());
        metric.setId(data.getId());
        metric.setName(data.getName());
        metric.setDescription(data.get(SalaryKPIFields.INDICATOR_DESC, String.class));
        metric.setKpiCalculateType(KPICalculateType.of(data.get(SalaryKPIFields.INDICATOR_CALC_METHOD, String.class)));
        metric.setKaoQinFieldType(KaoQinFieldType.valueOf(data.get(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD, String.class)));
        return metric;
    }
}