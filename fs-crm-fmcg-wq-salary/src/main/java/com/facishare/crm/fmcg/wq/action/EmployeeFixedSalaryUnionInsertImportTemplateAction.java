package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.paas.appframework.core.predef.action.StandardUnionInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 员工固定工资表联合新建导入模板
 * 控制联合导入模板中包括的列，支持主对象和从对象的联合导入
 *
 * 扩展点：
 *
 * 1 com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction#customHeader（）：在通用的处理逻辑后，控制模板展示的列
 * @author: system
 * @create: 2025-07-11
 **/
@Slf4j
public class EmployeeFixedSalaryUnionInsertImportTemplateAction extends StandardUnionInsertImportTemplateAction {
  
  @Override
  protected void customHeader(List<IFieldDescribe> headerFieldList) {
    super.customHeader(headerFieldList);
    //apiName一致
    if (!headerFieldList.stream().anyMatch(field -> field.getDescribeApiName().equals(EmployeeFixedSalaryFields.API_NAME))) {
      return;
    }

    // 检查是否缺少owner字段，如果缺少则添加
    boolean hasOwnerField = headerFieldList.stream()
        .anyMatch(field -> BaseField.owner.getApiName().equals(field.getApiName()));
    if (!hasOwnerField) {
      log.info("员工固定工资表联合导入模板缺少owner字段，正在添加");
      addOwnerField(headerFieldList);
    }
    
    log.info("员工固定工资表联合导入模板字段处理完成，字段数量: {}", headerFieldList.size());
  }
  
  /**
   * 添加owner字段到导入模板
   * 
   * @param headerFieldList 字段列表
   */
  private void addOwnerField(List<IFieldDescribe> headerFieldList) {
    try {
      // 尝试从对象描述中获取owner字段
      IObjectDescribe objectDescribe = serviceFacade.findObject(
          actionContext.getTenantId(), 
          EmployeeFixedSalaryFields.API_NAME);
      
      if (objectDescribe != null) {
        IFieldDescribe ownerFieldDescribe = objectDescribe.getFieldDescribe(BaseField.owner.getApiName());
        
        if (ownerFieldDescribe != null) {
          // 如果对象描述中有owner字段，直接使用
          headerFieldList.add(ownerFieldDescribe);
          log.info("从对象描述中获取owner字段并添加到联合导入模板");
        }
      }
    } catch (Exception e) {
      log.warn("获取owner字段失败，创建虚拟owner字段: {}", e.getMessage());
    }
  }

}
