package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.SalaryDataFields;
import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.dao.SalaryDataDao;
import com.facishare.crm.fmcg.wq.dao.SalaryPaymentSlipDao;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.crm.fmcg.wq.util.DescribeUtils;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @create 2023-09-23 17:06
 */
public class SalaryKPIListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        result.setObjectDescribe(DescribeUtils.addKPIVirtualFieldToObjectDescribe(result.getObjectDescribe()));
        return after;
    }

}
