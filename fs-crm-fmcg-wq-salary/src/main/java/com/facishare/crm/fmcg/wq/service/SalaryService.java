package com.facishare.crm.fmcg.wq.service;

import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.crm.fmcg.wq.enums.SalaryRuleOperation;
import com.facishare.crm.fmcg.wq.model.api.DailySalaryQuery;
import com.facishare.crm.fmcg.wq.model.api.EmployeeSalaryTypeQuery;
import com.facishare.crm.fmcg.wq.model.api.SalaryPeriodTotalQuery;

import com.facishare.crm.fmcg.wq.mq.SalaryTaskMessage;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Date;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 薪资服务接口
 * @author: dev
 * @create: 2024-06-23
 */
public interface SalaryService {
    /**
     * 计算工资条明细数据，根据员工固定薪资对象和时间范围生成薪资明细列表。
     * <p>
     * 该方法会执行以下操作：
     * <ol>
     * <li>从employeeFixedSalaryObj获取员工信息和相关的薪资规则</li>
     * <li>获取薪资规则关联的工资项列表</li>
     * <li>对每个工资项，根据其取值方式进行处理：</li>
     * <ul>
     * <li>如果是固定值类型，从员工固定薪资明细中获取金额</li>
     * <li>如果是计算公式类型，解析公式并计算结果</li>
     * </ul>
     * <li>生成对应的薪资明细对象，设置各项字段</li>
     * </ol>
     * <p>
     * 对于计算公式类型的工资项，会根据公式中包含的KPI指标，使用相应的KPI计算器进行计算。
     *
     * @param employeeFixedSalaryObj 员工固定薪资对象，包含员工信息和固定薪资数据
     * @param startTime              计算周期的开始时间，毫秒时间戳
     * @param endTime                计算周期的结束时间，毫秒时间戳
     * @return 生成的薪资明细对象列表(SalaryDetailDataObj)
     * @throws RuntimeException 如果计算薪资明细过程中发生错误
     *
     * @see com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields
     * @see com.facishare.crm.fmcg.wq.constants.SalaryItemFields
     * @see SalaryKPIFields
     *
     *      <pre>
     *      // 示例用法
     *      IObjectData employeeFixedSalary = employeeFixedSalaryDao.getById(tenantId, employeeFixedSalaryId);
     *      long startTime = DateUtils.parseDate("2023-01-01").getTime();
     *      long endTime = DateUtils.parseDate("2023-01-31").getTime();
     *      List<IObjectData> salaryDetails = salaryService.calculateSalaryDetailDatas(employeeFixedSalary, startTime,
     *              endTime);
     *      </pre>
     */
    List<IObjectData> calculateSalaryDetailDatas(IObjectData employeeFixedSalaryObj, IObjectData salaryRuleObj,
            long startTime, long endTime, boolean recalc);

    /**
     * 创建工资发放单
     *
     * @param salaryRuleObj  工资规则对象
     * @param startTime      开始时间
     * @param endTime        结束时间
     * @param payDescription 发放说明
     * @return 创建的工资发放单对象
     */
    IObjectData createSalaryPaymentSlip(IObjectData salaryRuleObj, long startTime, long endTime, String payDescription);

    /**
     * 更新工资发放单状态
     *
     * @param tenantId            租户ID
     * @param salaryPaymentSlipId 工资发放单ID
     * @param payStatus           发放状态
     * @param payDescription      发放说明
     * @return 更新后的工资发放单对象
     */
    IObjectData updateSalaryPaymentSlipStatus(String tenantId, String salaryPaymentSlipId,
            String payStatus, String payDescription);

    /**
     * 生成工资条对象，根据员工固定薪资对象、时间范围和薪资明细数据生成工资条。
     * <p>
     * 该方法会执行以下操作：
     * <ol>
     * <li>从employeeFixedSalaryObj获取员工信息</li>
     * <li>查询并删除已存在的工资条数据，避免重复数据</li>
     * <li>创建新的工资条对象，设置员工信息、时间范围等字段</li>
     * <li>计算薪资总额（应发工资）并设置到工资条对象中</li>
     * <li>保存工资条对象</li>
     * <li>更新薪资明细对象，关联到新生成的工资条</li>
     * </ol>
     * <p>
     * 如果salaryDetailDatas为空，仍然会创建一个空的工资条对象，但不会关联任何薪资明细。
     *
     * @param employeeFixedSalaryObj 员工固定薪资对象，包含员工信息和固定薪资数据
     * @param startTime              工资条的开始时间，毫秒时间戳
     * @param endTime                工资条的结束时间，毫秒时间戳
     * @param salaryDetailDatas      薪资明细数据列表，可以为空
     * @return 生成的工资条对象(SalaryDataObj)
     * @throws RuntimeException 如果生成工资条过程中发生错误
     *
     * @see com.facishare.crm.fmcg.wq.constants.SalaryDataFields
     * @see com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields
     *
     *      <pre>
     *      // 示例用法
     *      IObjectData employeeFixedSalary = employeeFixedSalaryDao.getById(tenantId, employeeFixedSalaryId);
     *      long startTime = DateUtils.parseDate("2023-01-01").getTime();
     *      long endTime = DateUtils.parseDate("2023-01-31").getTime();
     *      List<IObjectData> salaryDetails = salaryService.calculateSalaryDetailDatas(employeeFixedSalary, startTime,
     *              endTime);
     *      IObjectData salaryData = salaryService.generateSalaryData(employeeFixedSalary, startTime, endTime,
     *              salaryDetails);
     *      </pre>
     */
    IObjectData generateSalaryData(IObjectData employeeFixedSalaryObj, IObjectData salaryRuleObj, long startTime,
            long endTime, IObjectData salaryPaymentSlipObj);

    /**
     * 更具工资发放单下发薪资
     * 1.给员工发送消息，导航到工资条信息
     * 2.更新工资条状态为已发放
     * 3.更新工资发放单状态为已发放
     *
     * @param salaryPaymentSlipObj 工资发放单据
     * @return 处理结果
     */
    IObjectData paySalaryBySalaryPaymentSlip(IObjectData salaryPaymentSlipObj);



    /**
     * 根据员工ID查询工资条
     * <p>
     * 该方法查询指定员工的所有工资条数据。
     *
     * @param tenantId   租户ID
     * @param employeeId 员工ID
     * @return 工资条对象列表
     */
    List<IObjectData> getSalaryDataByEmployeeId(String tenantId, String employeeId);

    /**
     * 根据外部员工ID查询工资条
     * <p>
     * 该方法查询指定外部员工的所有工资条数据。
     *
     * @param tenantId           租户ID
     * @param employeeExternalId 外部员工ID
     * @return 工资条对象列表
     */
    List<IObjectData> getSalaryDataByEmployeeExternalId(String tenantId, String employeeExternalId);

    /**
     * 批量发送工资条消息给员工
     * <p>
     * 该方法将指定的工资条信息批量发送给员工。
     * 根据用户类型区分不同的通知方式：
     * <ul>
     * <li>对于内部用户（employeeId有效），使用CRM通知方式发送消息</li>
     * <li>对于外部用户（employeeExternalId有效），使用通知公告方式发送消息</li>
     * </ul>
     *
     * @param tenantId       租户ID
     * @param salaryDataList 工资条对象列表
     * @return 成功发送的工资条数量
     */
    int sendSalaryDataMessages(String tenantId, List<IObjectData> salaryDataList);

    /**
     * 发送工资条消息给员工（单个工资条）
     * <p>
     * 该方法将指定的工资条信息发送给员工。
     *
     * @param tenantId     租户ID
     * @param employeeId   员工ID
     * @param salaryDataId 工资条ID
     * @return 是否发送成功
     * @deprecated 请使用 {@link #sendSalaryDataMessages(String, List)} 方法代替
     */
    @Deprecated
    boolean sendSalaryDataMessage(String tenantId, String employeeId, String salaryDataId);

    /**
     * 获取工资表表头
     * <p>
     * 该方法从工资规则对象中获取指定字段，并将其转换为表头数组。
     * 对于特定字段，会将其转换为对应的描述文本。
     * <p>
     * 表头数据可用于导出工资表或展示工资明细列表。
     *
     * @throws RuntimeException 如果获取表头过程中发生错误
     */
    List<String> getSalaryTableHeaders(IObjectData salaryPaymentSlipObj);

    /**
     * 获取虚拟的工资表表头描述
     *
     */
    Map<String, IFieldDescribe> processSalaryTableHeaderDescriptions(String tenantId, List<String> headers);

    /**
     * 处理场景字段顺序
     */
    List<Map<String, Object>> convertSalaryTableHeaderOrder(String tenantId, List<String> headers,
            List<Map<String, Object>> sourceFieldList);

    /**
     * 根据日期获取应该生成工资发放单的工资规则
     * 
     * @param tenantId 租户ID
     * @param date     参考日期，如果为null则使用当前日期
     * @return 符合条件的工资规则列表
     */
    List<IObjectData> getSalaryRulesByDate(String tenantId, Date date);

    /**
     * 根据日期获取应该生成工资条明细的规则
     * 
     * @param tenantId
     * @param date
     * @return
     */
    List<IObjectData> getSalaryRulesByDateForDetail(String tenantId, Date date);

    /**
     * 获取员工薪资类型和发放周期信息
     *
     * @param employeeId   员工ID
     * @param month        月份，格式：yyyy-MM
     * @param salaryDataId 工资条ID，如果传入则从工资条获取规则信息
     * @param context      服务上下文
     * @return 员工薪资类型和发放周期信息
     */
    EmployeeSalaryTypeQuery.Result getEmployeeSalaryType(String employeeId, String month, String salaryDataId,
            ServiceContext context);

    /**
     * 获取薪资周期详情
     *
     * @param employeeId   员工ID
     * @param periodId     周期ID
     * @param startDate    开始日期，格式：yyyy-MM-dd
     * @param endDate      结束日期，格式：yyyy-MM-dd
     * @param salaryDataId 工资条ID（可选）
     * @param context      服务上下文
     * @return 薪资周期详情
     */
    SalaryPeriodTotalQuery.Result getSalaryPeriodTotal(String employeeId, String periodId, String startDate,
            String endDate, String salaryDataId, ServiceContext context);

    /**
     * 构建扩展表头映射
     * <p>
     * 该方法从工资表表头中提取包含"kpi_"但不以"kpi_"开头的表头，
     * 并按照KPI ID进行分组。
     * <p>
     * 例如：表头"base_kpi_001"会被提取出KPI ID "001"，
     * 并将"base_kpi_001"添加到对应的列表中。
     *
     * @param salaryTableHeaders 工资表表头列表
     * @return 扩展表头映射，key为KPI ID，value为包含该KPI ID的表头列表
     */
    Map<String, List<String>> buildExtHeaderMap(List<String> salaryTableHeaders);

    /**
     * 获取工资条的额外聚合数据
     * <p>
     * 该方法计算工资条明细中的公式变量和工资项金额的聚合值。
     * 聚合结果包含两部分：
     * <ol>
     * <li>公式变量聚合：将所有工资条明细中的公式变量值进行累加</li>
     * <li>工资项金额聚合：将相同工资项的金额进行累加</li>
     * </ol>
     *
     * @param tenantId     租户ID
     * @param salaryDataId 工资条ID
     * @param extHeaderMap xxxkpi_xxxxxx 格式表头 key 为 kpiId，value 为表头
     * @return 聚合结果Map，包含公式变量和工资项金额的聚合值
     */
    Map<String, String> getSalaryDataAggregatedValues(String tenantId, String salaryDataId);

    /**
     * 获取日工资数据
     *
     * @param employeeId 员工ID
     * @param queryDate  查询日期，格式：yyyy-MM-dd
     * @param month      月份，格式：yyyy-MM
     * @param context    服务上下文
     * @return 日工资数据，包含日历数据和当天的详细薪资数据
     */
    DailySalaryQuery.Result getDailySalary(String employeeId, String queryDate, String month, ServiceContext context);

    /**
     * 查询适用的薪资规则
     * <p>
     * 该方法根据员工信息查询可能适配的薪资规则。
     * 查询条件包括：
     * <ul>
     * <li>员工ID：直接匹配特定员工</li>
     * <li>部门IDs：匹配员工所在部门及其所有上级部门</li>
     * <li>互联企业ID：匹配特定互联企业</li>
     * <li>角色IDs：匹配员工拥有的角色</li>
     * <li>员工类型：区分内部员工和外部员工</li>
     * </ul>
     *
     * @param tenantId      租户ID
     * @param employeeId    员工ID
     * @param departmentIds 部门ID列表
     * @param outTenantId   互联企业ID
     * @param roleIds       角色ID列表
     * @param isExternal    是否为外部员工
     * @return 适用的薪资规则列表
     */
    List<IObjectData> queryApplicableSalaryRules(String tenantId, String employeeId,
            List<String> departmentIds, String outTenantId,
            List<String> roleIds, boolean isExternal, String salaryMethod);

    /**
     * 根据员工固定工资表获取适用的薪资规则
     * <p>
     * 该方法从员工固定工资表中提取员工信息，然后查询适用的薪资规则。
     * 处理逻辑包括：
     * <ul>
     * <li>从员工固定工资表中获取员工ID或外部员工ID</li>
     * <li>获取员工的部门信息</li>
     * <li>获取员工的角色信息</li>
     * <li>根据这些信息查询适用的薪资规则</li>
     * </ul>
     *
     * @param tenantId               租户ID
     * @param employeeFixedSalaryObj 员工固定工资表对象
     * @return 适用的薪资规则ID列表
     */
    List<IObjectData> getApplicableSalaryRuleIdsByEmployeeFixedSalary(String tenantId, IObjectData employeeFixedSalaryObj);
    /**
     * 获取员工适用的工资规则列表（用于检查是否需要创建固定工资表）
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param isExternal 是否外部员工
     * @return 适用的工资规则列表
     */
    List<IObjectData> getApplicableSalaryRulesForEmployee(String tenantId, String employeeId, boolean isExternal);

    /**
     * 处理薪资规则适用范围变更
     * <p>
     * 该方法处理薪资规则适用范围的变更，包括：
     * <ul>
     * <li>比较旧的适用范围和新的适用范围</li>
     * <li>获取受影响的员工ID列表</li>
     * <li>更新员工固定薪资规则关联</li>
     * </ul>
     *
     * @param tenantId      租户ID
     * @param salaryRuleId  薪资规则ID
     * @param oldSalaryRule 旧的适用范围数据
     * @param newSalaryRule 新的适用范围数据
     */
    void processApplicableScopeChanges(String tenantId, String salaryRuleId,
            IObjectData oldSalaryRule, IObjectData newSalaryRule);

    /**
     * 根据薪资规则获取员工ID列表
     * <p>
     * 该方法从薪资规则对象中提取适用范围配置，然后获取所有匹配的员工ID。
     * 支持的适用范围包括：
     * <ul>
     * <li>内部员工：适用人员、适用部门、适用角色</li>
     * <li>外部员工：适用人员、适用企业、适用角色</li>
     * </ul>
     *
     * @param tenantId   租户ID
     * @param salaryRule 薪资规则对象
     * @return 员工ID列表
     */
    List<String> getEmployeeIdsBySalaryRule(String tenantId, IObjectData salaryRule);

    /**
     * 更新员工固定薪资规则关联
     * <p>
     * 该方法根据员工ID列表和操作类型更新员工固定薪资规则关联：
     * <ul>
     * <li>ADD：为员工添加新的薪资规则（支持强制适用人员覆盖现有规则）</li>
     * <li>REMOVE：移除员工的薪资规则关联</li>
     * </ul>
     * <p>
     * 注意：定薪方式和强制适用人员信息都从薪资规则对象中获取。
     *
     * @param tenantId     租户ID
     * @param employeeIds  员工ID列表
     * @param salaryRuleObj 薪资规则对象，包含规则ID、定薪方式、强制适用人员等信息
     * @param operation    操作类型枚举
     */
    void updateEmployeeFixedSalaryRules(String tenantId, List<String> employeeIds,
            IObjectData salaryRuleObj, SalaryRuleOperation operation);

    /**
     * 为新建的工资发放单生成工资条数据
     * <p>
     * 该方法专门用于手动新建工资发放单后，异步生成对应的工资条。
     * 与定时任务生成的工资条不同，此方法不需要进行状态判断。
     * <p>
     * 处理流程：
     * <ol>
     * <li>根据工资发放单获取工资规则</li>
     * <li>获取适用的员工固定薪资对象列表</li>
     * <li>为每个员工生成工资条</li>
     * <li>更新工资发放单状态</li>
     * </ol>
     *
     * @param tenantId            租户ID
     * @param salaryPaymentSlipId 工资发放单ID
     * @throws RuntimeException 如果生成工资条过程中发生错误
     */
    void generateSalaryDataForNewPaymentSlip(String tenantId, String salaryPaymentSlipId);
    void generateSalaryDataForNewPaymentSlip(String tenantId, IObjectData salaryPaymentSlipObj);

    /**
     * 重新计算工资发放单的工资数据
     * <p>
     * 该方法会重新计算指定工资发放单下的所有工资条和工资条明细数据。
     * 主要用于工资条明细修正后，需要重新同步工资条总额的场景。
     * <p>
     * 处理流程：
     * <ol>
     * <li>获取工资发放单下的所有工资条</li>
     * <li>重新计算每个工资条的总金额</li>
     * <li>更新工资条数据</li>
     * <li>记录操作日志</li>
     * </ol>
     *
     * @param tenantId            租户ID
     * @param salaryPaymentSlipId 工资发放单ID
     * @return 更新的工资条数量
     * @throws RuntimeException 如果重新计算过程中发生错误
     */
    int recalculateSalaryDataForPaymentSlip(String tenantId, String salaryPaymentSlipId);

    /**
     * 处理薪资任务的核心业务逻辑
     * 从Consumer中移动到Service，使业务逻辑更清晰
     *
     * @param salaryTaskMessage 薪资任务消息对象
     * @throws Exception 处理过程中的异常
     */
    void handleSalaryTask(SalaryTaskMessage salaryTaskMessage) throws Exception;

    /**
     * 判断指定日期是否需要生成工资条
     * 根据薪资规则的自动发放设置和发放周期来判断
     *
     * @param salaryRule 薪资规则对象
     * @param checkDate 要检查的日期
     * @return true-需要生成工资条，false-不需要生成
     */
    boolean shouldGenerateSalaryData(IObjectData salaryRule, LocalDate checkDate);

    /**
     * 生成工资条和发放单的通用方法
     * 从Consumer中移动到Service，使业务逻辑更清晰
     *
     * @param tenantId 租户ID
     * @param salaryRule 薪资规则
     * @param employeeFixedSalaryObjs 员工固定薪资对象列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void generateSalaryDataAndPaymentSlip(String tenantId, IObjectData salaryRule,
            List<IObjectData> employeeFixedSalaryObjs, long startTime, long endTime);

    /**
     * 处理强制适用人员冲突
     * @param tenantId
     * @param currentRuleId
     * @param mandatoryList
     */
    void processMandatoryPersonnelConflicts(String tenantId, String currentRuleId,
                                                    List<String> mandatoryList);

    /**
     * 检查指定员工的定薪方式是否与适用的工资规则一致
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param isExternal 是否外部员工
     * @return true-一致，false-不一致
     */
    boolean checkSalaryMethodConsistency(String tenantId, String employeeId, boolean isExternal);

    /**
     * 明细处理结果
     */
    @Data
    public static class ProcessResult {
        private final BigDecimal amount;
        private final boolean hasError;
        private final boolean shouldSkip;
        private final String calculationFormula;
        
        public static ProcessResult skip(BigDecimal amount) { return new ProcessResult(amount, false, true, null); }
        public static ProcessResult success(BigDecimal amount, String formula) { return new ProcessResult(amount, false, false, formula); }
        public static ProcessResult error() { return new ProcessResult(BigDecimal.ZERO, true, false, null); }
    }


}
