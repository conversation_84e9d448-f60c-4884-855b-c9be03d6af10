package com.facishare.crm.fmcg.wq.service;

import com.facishare.crm.fmcg.wq.factory.SalaryKPIFactory;
import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.kpi.SalaryKPI;
import com.facishare.paas.metadata.api.IObjectData;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 薪资KPI计算器抽象基类
 * @author: dev
 * @create: 2024-06-23
 */
public abstract class SalaryKPICalculator<T extends SalaryKPI> {

    /**
     * 验证KPI指标
     *
     * @param metric KPI指标
     */
    public void validate(T metric) {
        // 基础验证逻辑，子类可以覆盖并扩展
    }

    /**
     * 计算KPI值
     * 模板方法，定义了计算流程
     *
     * @param context 薪资上下文
     * @param metric KPI对象
     * @return 计算结果
     */
    public final MetricCalculateResult calculate(SalaryContext context, T metric) {
        return calculateWithKPI(context, metric);
    }

    /**
     * 直接使用KPI对象计算
     *
     * @param context 薪资上下文
     * @param metric KPI对象
     * @return 计算结果
     */
    @SuppressWarnings("unchecked")
    public final MetricCalculateResult calculateWithKPI(SalaryContext context, SalaryKPI metric) {
        // 1. 验证KPI对象
        validate((T) metric);
        context.watch().lap("SalaryKPICalculator#validate");

        // 2. 执行具体计算
        MetricCalculateResult result = doCalculate(context, (T) metric);
        context.watch().lap("SalaryKPICalculator#doCalculate");

        return result;
    }


    /**
     * 执行具体的KPI计算
     *
     * @param context 薪资上下文
     * @param metric KPI指标
     * @return 计算结果
     */
    public abstract MetricCalculateResult doCalculate(SalaryContext context, T metric);
}