package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryDataFields;
import com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields;
import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.dao.SalaryDataDao;
import com.facishare.crm.fmcg.wq.dao.SalaryDetailDataDao;
import com.facishare.crm.fmcg.wq.dao.SalaryPaymentSlipDao;
import com.facishare.crm.fmcg.wq.model.FmcgPreActionArgs;
import com.facishare.crm.fmcg.wq.service.SalaryService;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工资发放单下发工资条Action
 * 
 * 功能：
 * 1. 更新发放状态为"发放中"
 * 2. 给工资发放单中的员工推送消息提醒
 * 3. 内部用CRM通知，外部用互联通知公告
 */
@Slf4j
public class SalaryPaymentSlipDistributeAction extends FmcgAbstractStandardAction<SalaryPaymentSlipDistributeAction.Arg, SalaryPaymentSlipDistributeAction.Result> {

    private SalaryPaymentSlipDao salaryPaymentSlipDao = SpringUtil.getContext().getBean(SalaryPaymentSlipDao.class);
    private SalaryDataDao salaryDataDao = SpringUtil.getContext().getBean(SalaryDataDao.class);
    private SalaryDetailDataDao salaryDetailDataDao = SpringUtil.getContext().getBean(SalaryDetailDataDao.class);
    private SalaryService salaryService = SpringUtil.getContext().getBean(SalaryService.class);

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.DISTRIBUTE; // 自定义action
    }

    @Override
    protected IObjectData getPreObjectData() {
        return dbData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.DISTRIBUTE.getButtonApiName();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return super.after(arg, result);
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);

        String tenantId = actionContext.getTenantId();
        String salaryPaymentSlipId = arg.getObjectDataId();

        log.info("开始处理工资发放单下发工资条，发放单ID: {}", salaryPaymentSlipId);

        // 获取工资发放单数据
        if (objectData == null) {
            objectData = salaryPaymentSlipDao.getById(tenantId, salaryPaymentSlipId);
        }

        if (objectData == null) {
            throw new ValidateException("工资发放单不存在"); //ignoreI18n
        }

        // 验证发放状态
        validateDistributeStatus(objectData);

        // 验证是否有工资条数据
        validateSalaryDataExists(tenantId, salaryPaymentSlipId);

        log.info("工资发放单下发工资条前处理完成，发放单ID: {}", salaryPaymentSlipId);
    }

    @Override
    protected Result doAct(Arg arg) {
        String tenantId = actionContext.getTenantId();
        String salaryPaymentSlipId = arg.getObjectDataId();

        try {
                    // 判断是否需要触发审批流程
            if (!arg.isSkipTriggerApprovalFlow()) {
                Map<String, Object> updateMap = Maps.newHashMap();
                updateMap.put(SalaryPaymentSlipFields.PAY_STATUS, SalaryPaymentSlipFields.PAY_STATUS_Options_2);
                Map<String, Map<String, Object>> dataMap = Maps.newHashMap();
                dataMap.put(arg.getDataId(), updateMap);

                Map<String, Map<String, Object>> callbackMap = Maps.newHashMap();
                callbackMap.put(arg.getDataId(), updateMap);
                // 尝试触发审批流程
                startApprovalFlow(Lists.newArrayList(objectData), ApprovalFlowTriggerType.DISTRIBUTE.getId(), dataMap, callbackMap);
                if (isApprovalFlowStartSuccess(objectData.getId())) {
                    Result result = new Result();
                    result.setCode(1);
                    result.setMessage("触发审批流");//ignoreI18n
                    return result;
                }
            }
            log.info("开始下发工资条，发放单ID: {}", salaryPaymentSlipId);

            // 1. 更新发放状态为"发放中"
            salaryPaymentSlipDao.updatePayStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_2);
            ParallelUtils.createBackgroundTask().submit(() -> {
                // 2. 获取工资条数据并发送通知
                List<IObjectData> salaryDataList = salaryDataDao.getBySalaryPaymentSlipId(tenantId, salaryPaymentSlipId);

                // 3. 使用 SalaryService 发送工资条消息
                int notificationCount = salaryService.sendSalaryDataMessages(tenantId, salaryDataList);

                // 4. 如果发送成功，更新工资条和工资条明细的发放状态
                if (!salaryDataList.isEmpty() && notificationCount > 0) {
                    // 更新工资条和工资条明细状态为已发放
                    updateSalaryDataAndDetailStatus(tenantId, salaryDataList);

                    // 更新工资发放单状态为已发放
                    salaryPaymentSlipDao.updatePayStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_4);
                } else {
                    // 发送失败，更新工资发放单状态为发放失败
                    salaryPaymentSlipDao.updatePayStatus(tenantId, salaryPaymentSlipId, SalaryPaymentSlipFields.PAY_STATUS_Options_3);
                }

                Result result = new Result();
                result.setSuccess(true);
                result.setMessage("工资条下发成功");//ignoreI18n
                result.setNotifiedEmployeeCount(notificationCount);

                log.info("工资发放单下发工资条完成，发放单ID: {}, 通知员工数: {}", salaryPaymentSlipId, notificationCount);
            }).run();


            Result result = new Result();
            result.setSuccess(true);
            result.setMessage("工资条下发中，成功后会更新状态为已发放");//ignoreI18n
            return result;

        } catch (Exception e) {
            log.error("工资发放单下发工资条失败，发放单ID: {}", salaryPaymentSlipId, e);
            throw new ValidateException("下发工资条失败: " + e.getMessage()); //ignoreI18n
        }
    }

    /**
     * 更新工资条和工资条明细的发放状态为已发放
     *
     * @param tenantId 租户ID
     * @param salaryDataList 工资条列表
     */
    private void updateSalaryDataAndDetailStatus(String tenantId, List<IObjectData> salaryDataList) {
        if (CollectionUtils.isEmpty(salaryDataList)) {
            return;
        }

        User systemUser = User.systemUser(tenantId);
        int updatedSalaryDataCount = 0;
        int updatedDetailCount = 0;

        log.info("开始更新工资条和工资条明细发放状态，工资条数量: {}", salaryDataList.size());

        for (IObjectData salaryData : salaryDataList) {
            try {
                String salaryDataId = salaryData.getId();
                String currentDistributionStatus = salaryData.get(SalaryDataFields.DISTRIBUTION_STATUS, String.class);

                // 如果工资条已经是已发放状态，则跳过
                if (SalaryDataFields.DISTRIBUTION_STATUS_Options_3.equals(currentDistributionStatus)) {
                    log.info("工资条已经是已发放状态，跳过更新，工资条ID: {}", salaryDataId);
                    updatedSalaryDataCount++;
                    continue;
                }

                // 更新工资条状态为已发放
                salaryData.set(SalaryDataFields.DISTRIBUTION_STATUS, SalaryDataFields.DISTRIBUTION_STATUS_Options_3);
                salaryDataDao.updateSalaryData(systemUser, salaryData);
                updatedSalaryDataCount++;

                log.info("更新工资条为已发放状态，工资条ID: {}", salaryDataId);

                // 获取工资条下的所有明细
                List<IObjectData> detailDataList = salaryDetailDataDao.getBySalaryDataId(tenantId, salaryDataId);

                // 更新每个薪资明细的发放状态
                for (IObjectData detailData : detailDataList) {
                    String distributionStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);

                    // 如果已经是已发放状态，则跳过
                    if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3.equals(distributionStatus)) {
                        log.info("薪资明细已经是已发放状态，跳过更新，明细ID: {}", detailData.getId());
                        updatedDetailCount++;
                        continue;
                    }

                    // 更新为已发放状态
                    detailData.set(SalaryDetailDataFields.DISTRIBUTION_STATUS, SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_3);
                    salaryDetailDataDao.updateSalaryDetailData(systemUser, detailData);
                    updatedDetailCount++;

                    log.info("更新薪资明细为已发放状态，明细ID: {}", detailData.getId());
                }

            } catch (Exception e) {
                log.error("更新工资条和明细发放状态失败，工资条ID: {}", salaryData.getId(), e);
            }
        }

        log.info("更新工资条和工资条明细发放状态完成，工资条总数: {}, 已更新工资条数: {}, 已更新明细数: {}",
                salaryDataList.size(), updatedSalaryDataCount, updatedDetailCount);
    }

    /**
     * 验证下发状态
     * 只有未发放 或者发放异常的工资发放单才能下发
     * 
     * @param salaryPaymentSlip 工资发放单
     */
    private void validateDistributeStatus(IObjectData salaryPaymentSlip) {
        String payStatus = salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class);

        if (StringUtils.isBlank(payStatus)) {
            throw new ValidateException("工资发放单状态异常"); //ignoreI18n
        }

        // 只有未发放 或者发放异常的工资发放单才能下发
        if (!SalaryPaymentSlipFields.PAY_STATUS_Options_3.equals(payStatus)
                && !SalaryPaymentSlipFields.PAY_STATUS_Options_1.equals(payStatus)) {
            throw new ValidateException("只有未发放和发放中的工资发放单才能下发工资条"); //ignoreI18n
        }

        log.debug("下发状态验证通过，当前状态: {}", payStatus);
    }

    /**
     * 验证是否存在工资条数据
     * 
     * @param tenantId 租户ID
     * @param salaryPaymentSlipId 工资发放单ID
     */
    private void validateSalaryDataExists(String tenantId, String salaryPaymentSlipId) {
        List<IObjectData> salaryDataList = salaryDataDao.getBySalaryPaymentSlipId(tenantId, salaryPaymentSlipId);

        if (CollectionUtils.isEmpty(salaryDataList)) {
            throw new ValidateException("该工资发放单下没有工资条数据，无法下发"); //ignoreI18n
        }

        log.debug("工资条数据验证通过，工资条数量: {}", salaryDataList.size());
    }






    /**
     * 下发工资条参数
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Arg extends FmcgPreActionArgs {
        /**
         * 是否发送短信通知（可选）
         */
        private Boolean sendSmsNotification = false;

        /**
         * 自定义通知消息（可选）
         */
        private String customMessage;
    }

    /**
     * 下发工资条结果
     */
    @Data
    public static class Result {
        private int code;
        /**
         * 是否成功
         */
        private boolean success;

        /**
         * 消息
         */
        private String message;

        /**
         * 通知的员工数量
         */
        private int notifiedEmployeeCount;

        /**
         * 下发时间
         */
        private Date distributeTime = new Date();
    }
}
