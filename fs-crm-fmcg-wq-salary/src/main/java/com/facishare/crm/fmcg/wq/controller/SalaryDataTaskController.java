package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.mq.PMMSalaryConsumer;
import com.facishare.crm.fmcg.wq.mq.SalaryTaskMessage;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.controller.AbstractStandardController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * 薪资任务处理Controller
 * 用于手动触发薪资计算任务，调用handleTaskMessage中的逻辑
 */
@Slf4j
public class SalaryDataTaskController extends AbstractStandardController<SalaryDataTaskController.Arg, SalaryDataTaskController.Result> {

  private PMMSalaryConsumer pmmSalaryConsumer = SpringUtil.getContext().getBean(PMMSalaryConsumer.class);

  @Override
  protected void before(Arg arg) {
    super.before(arg);
    log.info("开始处理薪资任务，参数: {}", JSONObject.toJSONString(arg));

    // 参数校验
    SalaryTaskMessage salaryTaskMessage = arg.getSalaryTaskMessage();
    if (salaryTaskMessage == null) {
      throw new ValidateException("薪资任务消息不能为空"); //ignoreI18n
    }

    if (StringUtils.isBlank(salaryTaskMessage.getTenantId())) {
      throw new ValidateException("租户ID不能为空"); //ignoreI18n
    }

    if (StringUtils.isBlank(salaryTaskMessage.getSalaryRuleId())) {
      throw new ValidateException("薪资规则ID不能为空"); //ignoreI18n
    }

    if (StringUtils.isBlank(salaryTaskMessage.getStartDateStr())) {
      throw new ValidateException("开始日期不能为空"); //ignoreI18n
    }

    if (StringUtils.isBlank(salaryTaskMessage.getEndDateStr())) {
      throw new ValidateException("结束日期不能为空"); //ignoreI18n
    }

    Integer flag = salaryTaskMessage.getFlag();
    if (flag == null) {
      throw new ValidateException("处理标志不能为空"); //ignoreI18n
    }
  }

  @Override
  protected Result doService(Arg arg) {
    SalaryTaskMessage salaryTaskMessage = arg.getSalaryTaskMessage();
    
    try {
      // 调用PMMSalaryConsumer的公共处理方法
      pmmSalaryConsumer.handleSalaryTask(salaryTaskMessage);

      return new Result(true, "薪资任务处理成功"); //ignoreI18n
    } catch (Exception e) {
      log.error("薪资任务处理失败", e);
      return new Result(false, "薪资任务处理失败: " + e.getMessage()); //ignoreI18n
    }
  }



  @Override
  protected List<String> getFuncPrivilegeCodes() {
    return Lists.newArrayList();
  }

  /**
   * Controller 请求参数
   */
  @Data
  public static class Arg implements Serializable {
    /**
     * 薪资任务消息对象 - 必填
     */
    private SalaryTaskMessage salaryTaskMessage;
    /**
     * 0 生成工资条明细
     * 1 生成工资条
     * null 不会跑定时任务
     */
    private Integer taskFlag;
  }

  /**
   * 返回结果
   */
  @Data
  public static class Result implements Serializable {
    /**
     * 处理是否成功
     */
    private Boolean success;

    /**
     * 处理结果消息
     */
    private String message;

    public Result() {
    }

    public Result(Boolean success, String message) {
      this.success = success;
      this.message = message;
    }
  }
}
