package com.facishare.crm.fmcg.wq.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MetricCalculateResult implements Serializable {

//    @JSONField(name = "value_type")
//    private ValueType valueType;
//
//    @JSONField(name = "values")
//    private List<String> values;

    private String value;
}