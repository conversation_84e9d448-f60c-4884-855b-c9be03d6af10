package com.facishare.crm.fmcg.wq.service;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 工资规则适配服务接口
 * <p>
 * 负责检查员工的部门角色、适用规则、固定工资表等，并触发相应的推送消息
 * </p>
 */
public interface SalaryRuleAdaptationService {

    /**
     * 检查员工的工资规则适配情况并发送相应通知
     *
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param isExternal 是否外部员工
     * @return 适配检查结果
     */
    SalaryRuleAdaptationResult checkEmployeeSalaryRuleAdaptation(String tenantId, String employeeId, boolean isExternal);



    /**
     * 批量检查员工的工资规则适配情况
     * 
     * @param tenantId 租户ID
     * @param employeeIds 员工ID列表
     * @param isExternal 是否外部员工
     * @return 批量检查结果
     */
    BatchSalaryRuleAdaptationResult batchCheckEmployeeSalaryRuleAdaptation(String tenantId, List<String> employeeIds, boolean isExternal);

    /**
     * 检查新增员工是否需要创建固定工资表
     * 
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param isExternal 是否外部员工
     * @return 是否需要创建固定工资表
     */
    boolean checkNewEmployeeNeedsFixedSalary(String tenantId, String employeeId, boolean isExternal);

    /**
     * 检查工资规则适用范围内缺失固定工资表的员工
     * 
     * @param tenantId 租户ID
     * @param salaryRuleId 工资规则ID
     * @return 缺失固定工资表的员工列表
     */
    List<String> checkMissingFixedSalaryEmployees(String tenantId, String salaryRuleId);

    /**
     * 检查定薪方式不一致的员工
     * 
     * @param tenantId 租户ID
     * @param employeeIds 员工ID列表（可选，为空时检查所有员工）
     * @return 定薪方式不一致的员工列表
     */
    List<String> checkSalaryMethodMismatchEmployees(String tenantId, List<String> employeeIds);

    /**
     * 工资规则适配结果
     */
    @Setter
    @Getter
    class SalaryRuleAdaptationResult {
        // Getters and Setters
        private String employeeId;
        private List<String> departmentIds;
        private List<String> roleIds;
        private List<IObjectData> applicableRules;
        private boolean hasFixedSalary;
        private String fixedSalaryMethod;
        private List<NotificationSent> notificationsSent;

    }

    /**
     * 批量适配结果
     */
    @Setter
    @Getter
    class BatchSalaryRuleAdaptationResult {
        // Getters and Setters
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<SalaryRuleAdaptationResult> results;
        private List<String> errors;

    }

    /**
     * 通知发送记录
     */
    @Setter
    @Getter
    class NotificationSent {
        // Getters and Setters
        private String notificationType;
        private String message;
        private boolean success;
        private String errorMessage;

        public NotificationSent(String notificationType, String message, boolean success, String errorMessage) {
            this.notificationType = notificationType;
            this.message = message;
            this.success = success;
            this.errorMessage = errorMessage;
        }

    }
}
