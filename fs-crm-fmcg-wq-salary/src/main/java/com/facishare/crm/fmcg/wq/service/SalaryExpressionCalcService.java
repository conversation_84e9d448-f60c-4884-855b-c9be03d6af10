package com.facishare.crm.fmcg.wq.service;

import com.facishare.crm.fmcg.wq.controller.SalaryItemExpressionCheckController;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.ExpressionCheck;
import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import com.fxiaoke.common.Pair;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-04-28 18:07
 **/
public interface SalaryExpressionCalcService {

    List<String> extractKpiIdsFromExpression(String expression);
    /**
     * 验证接口
     */
    ExpressionCheck.Result check(ExpressionDTO expressionDTO, ServiceContext context);
    /**
     * 计算接口
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param owner 负责人
     * @param arg 计算公式
     * @param context 上下文
     */
    Pair<String/*表达式*/,Object/*计算后的值*/> calculateWithExpression(ExpressionDTO expressionDTO, SalaryContext context);
//
//    /**
//     * 计算结果
//     */
//    @Data
//     class CalculateResult {
//        /**
//         * 指标计算结果
//         */
//        List<CalcItemResult> kpiCalcResults;
//
//        /**
//         * 总计算结果 工资项的计算结果
//         */
//        CalcItemResult totalResult;
//    }
//    @Data
//     class CalcItemResult {
//        /**
//         * 指标id
//         */
//        String id;
//        String name;
//        String value;
//        /**
//         * 表达式
//         */
//        String expression;
//        /**
//         * 中间表达式，已经赋值了一部分的
//         */
//        String middleExpression;
//
//
//
//    }
}
