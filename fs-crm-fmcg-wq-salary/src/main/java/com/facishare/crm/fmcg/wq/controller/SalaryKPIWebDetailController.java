package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.crm.fmcg.wq.model.AggregateFunction;
import com.facishare.crm.fmcg.wq.model.KaoQinFieldType;
import com.facishare.crm.fmcg.wq.util.DescribeUtils;
import com.facishare.crm.fmcg.wq.util.LayoutUtils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * 工资绩效指标详情页控制器
 * 
 * 功能：在详情页增加虚拟字段显示指标维度统计方式的详细描述
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@Slf4j
public class SalaryKPIWebDetailController extends StandardWebDetailController {


  @Override
  protected Result after(Arg arg, Result result) {
    log.info("SalaryKPIWebDetailController after method called");
    result = super.after(arg, result);

    try {
      // 获取工资绩效指标数据
      ObjectDataDocument dataDocument = result.getData();
      if (dataDocument == null) {
        log.warn("工资绩效指标数据为空，跳过维度描述生成");
        return result;
      }

      ObjectDataExt kpiExt = ObjectDataExt.of(dataDocument);

      // 生成指标维度统计方式描述
      String dimensionDescription = buildDimensionDescription(kpiExt);
      if (StringUtils.isNotBlank(dimensionDescription)) {
        // 添加虚拟字段到数据中
        dataDocument.put(SalaryKPIFields.DIMENSION_DESCRIPTION, dimensionDescription);
        log.info("工资绩效指标维度描述已生成: {}", dimensionDescription);
      }

      // 添加虚拟字段到layout中
      result.setLayout(
              LayoutUtils.addVirtualFieldToDetailLayout(
                      result.getLayout(),
                      SalaryKPIFields.INDICATOR_CALC_METHOD,
                      SalaryKPIFields.DIMENSION_DESCRIPTION
              )
      );

      // 添加虚拟字段到对象描述中
      result.setDescribe(DescribeUtils.addKPIVirtualFieldToObjectDescribe(result.getDescribe()));

    } catch (Exception e) {
      log.error("生成工资绩效指标维度描述时发生异常", e);
      // 不影响正常显示，只记录错误日志
    }

    return result;
  }

  /**
   * 构建指标维度统计方式描述
   * 
   * @param kpiExt 工资绩效指标扩展对象
   * @return 维度描述文本
   */
  private String buildDimensionDescription(ObjectDataExt kpiExt) {
    try {
      String calcMethod = kpiExt.get(SalaryKPIFields.INDICATOR_CALC_METHOD, String.class);
      if (StringUtils.isBlank(calcMethod)) {
        return I18N.text("salary.kpi.calc.method.unconfigured");
      }

      StringBuilder description = new StringBuilder();

      // 根据不同的计算方式生成描述
      switch (calcMethod) {
        case SalaryKPIFields.INDICATOR_CALC_METHOD_Options_1: // 对象聚合
          description.append(buildObjectAggregateDescription(kpiExt));
          break;
        case SalaryKPIFields.INDICATOR_CALC_METHOD_Options_2: // 月度考勤表
          description.append(buildKaoQinStatDescription(kpiExt));
          break;
        case SalaryKPIFields.INDICATOR_CALC_METHOD_Options_3: // APL函数
          description.append(buildAplDescription(kpiExt));
          break;
        default:
          description.append(I18N.text("salary.kpi.calc.method.unknown", calcMethod));
      }

      // 添加单位信息
      String unit = kpiExt.get(SalaryKPIFields.UNIT, String.class);
      if (StringUtils.isNotBlank(unit)) {
        description.append(I18N.text("salary.kpi.unit", unit));
      }

      return description.toString();

    } catch (Exception e) {
      log.warn("构建指标维度描述失败", e);
      return I18N.text("salary.kpi.description.failed");
    }
  }

  /**
   * 构建对象聚合方式的描述
   * 格式：将 [对象名] 对象下的 [字段名] 字段，以 [人员字段] 维度，按照 [日期字段] 通过 [统计方式] 统计方式集合
   */
  private String buildObjectAggregateDescription(ObjectDataExt kpiExt) {
    // 聚合对象
    String aggregatedObject = kpiExt.get(SalaryKPIFields.AGGREGATED_OBJECT, String.class);
    IObjectDescribe aggregatedObjectDescribe = null;
    if (StringUtils.isNotBlank(aggregatedObject)) {
      try {
        aggregatedObjectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), aggregatedObject);
      } catch (Exception e) {
        log.warn("获取聚合对象描述失败: {}", aggregatedObject, e);
        return I18N.text("salary.kpi.aggregate.object.failed");
      }
    }

    String objectDisplayName = getObjectDisplayName(aggregatedObjectDescribe, aggregatedObject);
    if (StringUtils.isBlank(objectDisplayName)) {
      objectDisplayName = I18N.text("salary.kpi.aggregate.unknown.object");
    }

    // 聚合字段
    String aggregatedField = kpiExt.get(SalaryKPIFields.AGGREGATED_FIELD, String.class);
    String fieldDisplayName = getFieldDisplayName(aggregatedObjectDescribe, aggregatedField);
    if (StringUtils.isBlank(fieldDisplayName)) {
      fieldDisplayName = I18N.text("salary.kpi.aggregate.unknown.field");
    }

    // 聚合人员字段（维度）
    String aggregatedPersonField = kpiExt.get(SalaryKPIFields.AGGREGATED_PERSON_FIELD, String.class);
    String personFieldDisplayName = getFieldDisplayName(aggregatedObjectDescribe, aggregatedPersonField);
    if (StringUtils.isBlank(personFieldDisplayName)) {
      personFieldDisplayName = I18N.text("salary.kpi.aggregate.unknown.dimension");
    }

    // 聚合日期字段
    String aggregatedDateField = kpiExt.get(SalaryKPIFields.AGGREGATED_DATE_FIELD, String.class);
    String dateFieldDisplayName = getFieldDisplayName(aggregatedObjectDescribe, aggregatedDateField);
    if (StringUtils.isBlank(dateFieldDisplayName)) {
      dateFieldDisplayName = I18N.text("salary.kpi.aggregate.unknown.date.field");
    }

    // 聚合函数（统计方式）
    String aggregationFunction = kpiExt.get(SalaryKPIFields.AGGREGATION_FUNCTION, String.class);
    String functionDesc = StringUtils.isNotBlank(aggregationFunction) 
        ? getAggregationFunctionDescription(aggregationFunction)
        : I18N.text("salary.kpi.aggregate.unknown.function");

    // 使用MessageFormat格式化整句话，带占位符
    return I18N.text("salary.kpi.aggregate.description", 
        objectDisplayName, 
        fieldDisplayName, 
        personFieldDisplayName, 
        dateFieldDisplayName, 
        functionDesc);
  }

  /**
   * 构建月度考勤表方式的描述
   * 格式：月度考勤表方式计算：将月度考勤表[字段名]字段，按照考勤日期累计统计
   */
  private String buildKaoQinStatDescription(ObjectDataExt kpiExt) {
    String monthlyAttendanceField = kpiExt.get(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD, String.class);
    String fieldDesc = StringUtils.isNotBlank(monthlyAttendanceField) 
        ? getKaoQinFieldDescription(monthlyAttendanceField)
        : I18N.text("salary.kpi.kaoqin.unconfigured.field");

    // 使用MessageFormat格式化整句话，带占位符
    return I18N.text("salary.kpi.kaoqin.description", fieldDesc);
  }

  /**
   * 构建APL函数方式的描述
   */
  private String buildAplDescription(ObjectDataExt kpiExt) {
    String aplInfo = kpiExt.get(SalaryKPIFields.APL_INFO, String.class);
    String displayValue = StringUtils.isNotBlank(aplInfo) 
        ? extractAplDisplayValue(aplInfo)
        : I18N.text("salary.kpi.apl.unconfigured");

    // 使用MessageFormat格式化整句话，带占位符
    return I18N.text("salary.kpi.apl.description", displayValue);
  }

  /**
   * 从APL_INFO字段中提取显示值
   * 如果是JSON格式，提取apiName字段；否则直接返回原始值
   */
  private String extractAplDisplayValue(String aplInfo) {
    try {
      // 尝试解析为JSON
      JSONObject jsonObject = JSONObject.parseObject(aplInfo);
      if (jsonObject != null && jsonObject.containsKey("apiName")) {
        String apiName = jsonObject.getString("apiName");
        if (StringUtils.isNotBlank(apiName)) {
          return apiName;
        }
      }
    } catch (Exception e) {
      log.debug("APL_INFO不是有效的JSON格式，使用原始值: {}", aplInfo);
    }

    // 如果不是JSON格式或解析失败，返回原始值
    return aplInfo;
  }

  /**
   * 获取聚合函数的中文描述
   */
  private String getAggregationFunctionDescription(String functionValue) {
    try {
      AggregateFunction function = AggregateFunction.fromValue(functionValue);
      if (function != null) {
//        switch (function) {
//          case COUNT:
//            return I18N.text("salary.kpi.aggregate.function.count");
//          case SUM:
//            return I18N.text("salary.kpi.aggregate.function.sum");
//          case MAX:
//            return I18N.text("salary.kpi.aggregate.function.max");
//          case MIN:
//            return I18N.text("salary.kpi.aggregate.function.min");
//          case AVG:
//            return I18N.text("salary.kpi.aggregate.function.avg");
//          default:
//            return functionValue;
//        }
        return I18N.text("fmcg.reward.core.enums.AggregateFunction." + functionValue);
      }
    } catch (Exception e) {
      log.warn("解析聚合函数失败: {}", functionValue, e);
    }
    return functionValue;
  }

  /**
   * 获取考勤字段的中文描述
   * 根据MONTHLY_ATTENDANCE_FIELD选项值来渲染描述，关联对象的需要查关联对象的描述来渲染各个字段名
   */
  private String getKaoQinFieldDescription(String fieldValue) {
    try {
      // 首先尝试从字段描述中获取显示名称
      String fieldDisplayName = getFieldDisplayNameFromDescribe(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD, fieldValue);
      if (StringUtils.isNotBlank(fieldDisplayName)) {
        return fieldDisplayName;
      }

      // 如果无法从字段描述获取，则使用枚举值的硬编码描述作为备选
      return getKaoQinFieldDescriptionByEnum(fieldValue);
    } catch (Exception e) {
      log.warn("解析考勤字段失败: {}", fieldValue, e);
      return fieldValue;
    }
  }

  /**
   * 从字段描述中获取选项的显示名称
   */
  private String getFieldDisplayNameFromDescribe(String fieldApiName, String optionValue) {
    try {
      // 获取字段描述
      IFieldDescribe fieldDescribe = getFieldDescribe(fieldApiName);
      if (fieldDescribe == null) {
        return null;
      }

      // 如果是选择字段，获取选项的显示名称
      if (fieldDescribe instanceof SelectOne) {
        SelectOne selectField = (SelectOne) fieldDescribe;
        List<ISelectOption> options = selectField.getSelectOptions();
        if (options != null) {
          for (ISelectOption option : options) {
            if (optionValue.equals(option.getValue())) {
              return option.getLabel();
            }
          }
        }
      }

      return null;
    } catch (Exception e) {
      log.warn("从字段描述获取选项显示名称失败: fieldApiName={}, optionValue={}", fieldApiName, optionValue, e);
      return null;
    }
  }

  /**
   * 获取字段描述
   */
  private IFieldDescribe getFieldDescribe(String fieldApiName) {
    try {
      // 获取工资绩效指标对象的描述

      if (describe != null) {
        return describe.getFieldDescribe(fieldApiName);
      }

      return null;
    } catch (Exception e) {
      log.warn("获取字段描述失败: {}", fieldApiName, e);
      return null;
    }
  }

  /**
   * 通过枚举值获取考勤字段描述（备选方案）
   */
  private String getKaoQinFieldDescriptionByEnum(String fieldValue) {
    try {
      KaoQinFieldType fieldType = KaoQinFieldType.valueOf(fieldValue);
      switch (fieldType) {
        case ruleDaysNum:
          return "应出勤天数（天）"; //ignoreI18n
        case checkDayNum:
          return "正常出勤（天）"; //ignoreI18n
        case absentDays:
          return "旷工（天）"; //ignoreI18n
        case waiQinDaysNum:
          return "外勤（天）"; //ignoreI18n
        case checkWorkTime:
          return "实际工作时长（小时）"; //ignoreI18n
        case overTime:
          return "加班时长（小时）"; //ignoreI18n
        case laterNum:
          return "迟到（次）"; //ignoreI18n
        case laterTime:
          return "迟到（分钟）"; //ignoreI18n
        case earlyNum:
          return "早退（次）"; //ignoreI18n
        case earlyTime:
          return "早退（分钟）"; //ignoreI18n
        case missNum:
          return "未打卡（次）"; //ignoreI18n
        case locationExNum:
          return "不在考勤范围（次）"; //ignoreI18n
        default:
          return fieldValue;
      }
    } catch (Exception e) {
      log.warn("通过枚举解析考勤字段失败: {}", fieldValue, e);
      return fieldValue;
    }
  }

  /**
   * 获取对象的显示名称
   *
   * @param objectDescribe 对象描述
   * @param objectApiName 对象API名称（用作降级方案）
   * @return 对象显示名称，如果获取失败则返回API名称
   */
  private String getObjectDisplayName(IObjectDescribe objectDescribe, String objectApiName) {
    if (objectDescribe != null) {
      try {
        // 从对象描述中获取显示名称
        String label = (String) objectDescribe.get("label");
        if (StringUtils.isNotBlank(label)) {
          return label;
        }

        // 如果没有label，尝试获取display_name
        String displayName = (String) objectDescribe.get("display_name");
        if (StringUtils.isNotBlank(displayName)) {
          return displayName;
        }
      } catch (Exception e) {
        log.warn("从对象描述获取显示名称失败: {}", objectApiName, e);
      }
    }

    // 如果获取失败，返回API名称
    return StringUtils.isNotBlank(objectApiName) ? objectApiName : "";
  }

  /**
   * 获取指定对象中字段的显示名称
   *
   * @param objectDescribe 对象描述
   * @param fieldApiName 字段API名称
   * @return 字段显示名称，如果获取失败则返回字段API名称
   */
  private String getFieldDisplayName(IObjectDescribe objectDescribe, String fieldApiName) {
    if (StringUtils.isBlank(fieldApiName)) {
      return fieldApiName;
    }

    if (objectDescribe != null) {
      try {
        IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldApiName);
        if (fieldDescribe != null) {
          String label = fieldDescribe.getLabel();
          if (StringUtils.isNotBlank(label)) {
            return label;
          }
        }
      } catch (Exception e) {
        log.warn("从对象描述获取字段显示名称失败: fieldApiName={}", fieldApiName, e);
      }
    }

    // 如果获取失败，返回字段API名称
    return StringUtils.isNotBlank(fieldApiName) ? fieldApiName : "";
  }

  /**
   * 添加虚拟字段到layout中
   * 在INDICATOR_CALC_METHOD字段之后添加dimension_description字段
   */
//  private LayoutDocument addVirtualFieldToLayout(LayoutDocument layoutDocument) {
//    try {
//      LayoutExt layoutExt = LayoutExt.of(layoutDocument);
//
//      // 获取所有组件
//      List<IComponent> components = layoutExt.getComponents();
//      if (components == null || components.isEmpty()) {
//        log.warn("layout中没有找到components，跳过虚拟字段添加");
//        return layoutDocument;
//      }
//
//      // 查找form_component并在其中添加虚拟字段
//      boolean fieldAdded = false;
//      for (IComponent component : components) {
//        if (component != null && "form_component".equals(component.get("api_name"))) {
//          fieldAdded = addVirtualFieldToFormComponent(component);
//          if (fieldAdded) {
//            break;
//          }
//        }
//      }
//
//      if (!fieldAdded) {
//        log.warn("未找到form_component或INDICATOR_CALC_METHOD字段，跳过虚拟字段添加");
//        return layoutDocument;
//      }
//
//      // 更新layout
//
//      log.info("成功添加虚拟字段到layout");
//      return LayoutDocument.of(layoutExt);
//    } catch (Exception e) {
//      log.warn("添加虚拟字段到layout失败", e);
//    }
//    return layoutDocument;
//  }
//
//  /**
//   * 在form_component中添加虚拟字段
//   */
//  @SuppressWarnings("unchecked")
//  private boolean addVirtualFieldToFormComponent(IComponent formComponent) {
//    try {
//      // 获取field_section数组
//      List<Map<String, Object>> fieldSections = (List<Map<String, Object>>) formComponent.get("field_section");
//      if (fieldSections == null || fieldSections.isEmpty()) {
//        return false;
//      }
//
//      // 遍历field_section查找包含INDICATOR_CALC_METHOD的section
//      for (Map<String, Object> section : fieldSections) {
//        List<Map<String, Object>> formFields = (List<Map<String, Object>>) section.get("form_fields");
//        if (formFields == null) {
//          continue;
//        }
//
//        // 查找INDICATOR_CALC_METHOD字段的位置
//        int insertIndex = -1;
//        for (int i = 0; i < formFields.size(); i++) {
//          Map<String, Object> field = formFields.get(i);
//          if (SalaryKPIFields.INDICATOR_CALC_METHOD.equals(field.get("field_name"))) {
//            insertIndex = i + 1; // 在INDICATOR_CALC_METHOD之后插入
//            break;
//          }
//        }
//
//        if (insertIndex != -1) {
//          // 创建虚拟字段配置
//          Map<String, Object> virtualField = Maps.newHashMap();
//          virtualField.put("is_readonly", true);
//          virtualField.put("full_line", true);
//          virtualField.put("is_required", false);
//          virtualField.put("render_type", "text");
//          virtualField.put("field_name", SalaryKPIFields.DIMENSION_DESCRIPTION);
//
//          // 插入虚拟字段
//          formFields.add(insertIndex, virtualField);
//
//          log.info("成功在form_component中添加虚拟字段，插入位置: {}", insertIndex);
//          return true;
//        }
//      }
//
//      return false;
//    } catch (Exception e) {
//      log.warn("在form_component中添加虚拟字段失败", e);
//      return false;
//    }
//  }

}
