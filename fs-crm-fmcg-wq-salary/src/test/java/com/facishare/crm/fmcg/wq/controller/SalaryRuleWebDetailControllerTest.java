package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.metadata.api.IObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 工资规则详情页控制器测试类
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
class SalaryRuleWebDetailControllerTest {

  @Mock
  private SalaryItemDao salaryItemDao;

  @Mock
  private StandardWebDetailController.Arg arg;

  @Mock
  private StandardWebDetailController.Result result;

  @Mock
  private ObjectDataDocument dataDocument;

  @Mock
  private IObjectData salaryItem1;

  @Mock
  private IObjectData salaryItem2;

  @Mock
  private IObjectData salaryItem3;

  @InjectMocks
  private SalaryRuleWebDetailController controller;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    
    // 设置基本的mock行为
    when(result.getData()).thenReturn(dataDocument);
//    when(dataDocument.getTenantId()).thenReturn("test_tenant");
  }

  @Test
  void testAfterWithValidSalaryItems() {
    // 准备测试数据
    List<String> salaryItemIds = Arrays.asList("item1", "item2", "item3");
    when(dataDocument.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);

    // 设置工资项mock数据
    when(salaryItem1.getId()).thenReturn("item1");
    when(salaryItem1.getName()).thenReturn("基本工资");
    when(salaryItem1.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1); // 应发项

    when(salaryItem2.getId()).thenReturn("item2");
    when(salaryItem2.getName()).thenReturn("绩效奖金");
    when(salaryItem2.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1); // 应发项

    when(salaryItem3.getId()).thenReturn("item3");
    when(salaryItem3.getName()).thenReturn("社保扣款");
    when(salaryItem3.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2); // 扣减项

    List<IObjectData> salaryItems = Arrays.asList(salaryItem1, salaryItem2, salaryItem3);
    when(salaryItemDao.getbyIds(eq("test_tenant"), eq(salaryItemIds))).thenReturn(salaryItems);

    // 执行测试
    StandardWebDetailController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertSame(result, actualResult);
//    verify(dataDocument).set(eq(SalaryRuleFields.CALCULATION_FORMULA), eq("基本工资 + 绩效奖金 - 社保扣款"));
    verify(result).setData(dataDocument);
  }

  @Test
  void testAfterWithMixedIncrementDecrementItems() {
    // 准备测试数据 - 混合增减项
    List<String> salaryItemIds = Arrays.asList("item1", "item2", "item3", "item4");
    when(dataDocument.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);

    // 设置工资项mock数据
    when(salaryItem1.getId()).thenReturn("item1");
    when(salaryItem1.getName()).thenReturn("基本工资");
    when(salaryItem1.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1); // 应发项

    when(salaryItem2.getId()).thenReturn("item2");
    when(salaryItem2.getName()).thenReturn("社保扣款");
    when(salaryItem2.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2); // 扣减项

    when(salaryItem3.getId()).thenReturn("item3");
    when(salaryItem3.getName()).thenReturn("绩效奖金");
    when(salaryItem3.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1); // 应发项

    IObjectData salaryItem4 = mock(IObjectData.class);
    when(salaryItem4.getId()).thenReturn("item4");
    when(salaryItem4.getName()).thenReturn("个税");
    when(salaryItem4.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2); // 扣减项

    List<IObjectData> salaryItems = Arrays.asList(salaryItem1, salaryItem2, salaryItem3, salaryItem4);
    when(salaryItemDao.getbyIds(eq("test_tenant"), eq(salaryItemIds))).thenReturn(salaryItems);

    // 执行测试
    controller.after(arg, result);

    // 验证结果 - 按照ID顺序：基本工资 - 社保扣款 + 绩效奖金 - 个税
//    verify(dataDocument).set(eq(SalaryRuleFields.CALCULATION_FORMULA), eq("基本工资 - 社保扣款 + 绩效奖金 - 个税"));
  }

  @Test
  void testAfterWithEmptySalaryItems() {
    // 准备测试数据 - 空的工资项列表
    when(dataDocument.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(Arrays.asList());

    // 执行测试
    controller.after(arg, result);

    // 验证结果 - 不应该设置计算公式
//    verify(dataDocument, never()).set(eq(SalaryRuleFields.CALCULATION_FORMULA), any());
  }

  @Test
  void testAfterWithNullSalaryItems() {
    // 准备测试数据 - null的工资项
    when(dataDocument.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(null);

    // 执行测试
    controller.after(arg, result);

    // 验证结果 - 不应该设置计算公式
//    verify(dataDocument, never()).set(eq(SalaryRuleFields.CALCULATION_FORMULA), any());
  }

  @Test
  void testAfterWithStringArraySalaryItems() {
    // 准备测试数据 - 字符串数组格式的工资项
    String[] salaryItemIds = {"item1", "item2"};
    when(dataDocument.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);

    // 设置工资项mock数据
    when(salaryItem1.getId()).thenReturn("item1");
    when(salaryItem1.getName()).thenReturn("基本工资");
    when(salaryItem1.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1);

    when(salaryItem2.getId()).thenReturn("item2");
    when(salaryItem2.getName()).thenReturn("社保扣款");
    when(salaryItem2.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2);

    List<IObjectData> salaryItems = Arrays.asList(salaryItem1, salaryItem2);
    when(salaryItemDao.getbyIds(eq("test_tenant"), eq(Arrays.asList("item1", "item2")))).thenReturn(salaryItems);

    // 执行测试
    controller.after(arg, result);

    // 验证结果
//    verify(dataDocument).set(eq(SalaryRuleFields.CALCULATION_FORMULA), eq("基本工资 - 社保扣款"));
  }

  @Test
  void testAfterWithCommaSeparatedStringSalaryItems() {
    // 准备测试数据 - 逗号分隔的字符串格式
    when(dataDocument.get(SalaryRuleFields.SALARY_ITEM)).thenReturn("item1,item2");

    // 设置工资项mock数据
    when(salaryItem1.getId()).thenReturn("item1");
    when(salaryItem1.getName()).thenReturn("基本工资");
    when(salaryItem1.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1);

    when(salaryItem2.getId()).thenReturn("item2");
    when(salaryItem2.getName()).thenReturn("绩效奖金");
    when(salaryItem2.get(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB, String.class))
            .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1);

    List<IObjectData> salaryItems = Arrays.asList(salaryItem1, salaryItem2);
    when(salaryItemDao.getbyIds(eq("test_tenant"), eq(Arrays.asList("item1", "item2")))).thenReturn(salaryItems);

    // 执行测试
    controller.after(arg, result);

    // 验证结果
//    verify(dataDocument).set(eq(SalaryRuleFields.CALCULATION_FORMULA), eq("基本工资 + 绩效奖金"));
  }

  @Test
  void testAfterWithException() {
    // 准备测试数据 - 模拟异常
    when(dataDocument.get(SalaryRuleFields.SALARY_ITEM)).thenThrow(new RuntimeException("Test exception"));

    // 执行测试 - 不应该抛出异常
    assertDoesNotThrow(() -> controller.after(arg, result));

    // 验证结果 - 不应该设置计算公式
//    verify(dataDocument, never()).set(eq(SalaryRuleFields.CALCULATION_FORMULA), any());
  }

  @Test
  void testAfterWithNullDataDocument() {
    // 准备测试数据 - null的数据文档
    when(result.getData()).thenReturn(null);

    // 执行测试
    controller.after(arg, result);

    // 验证结果 - 不应该有任何设置操作
//    verify(dataDocument, never()).set(any(), any());
  }
}
