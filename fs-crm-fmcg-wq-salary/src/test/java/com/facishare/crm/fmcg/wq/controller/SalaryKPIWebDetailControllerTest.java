package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.SalaryKPIFields;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工资绩效指标详情页控制器测试
 */
@ExtendWith(MockitoExtension.class)
class SalaryKPIWebDetailControllerTest {

  private SalaryKPIWebDetailController controller;
  private StandardWebDetailController.Arg arg;
  private StandardWebDetailController.Result result;

  @BeforeEach
  void setUp() {
    controller = new SalaryKPIWebDetailController();
    arg = new StandardWebDetailController.Arg();
    result = new StandardWebDetailController.Result();
  }

  @Test
  void testAfter_ObjectAggregateKPI_ShouldGenerateDimensionDescription() {
    // 准备测试数据 - 对象聚合类型的KPI
    ObjectDataDocument dataDocument = createObjectDataDocument();
    dataDocument.put(SalaryKPIFields.INDICATOR_CALC_METHOD, SalaryKPIFields.INDICATOR_CALC_METHOD_Options_1);
    dataDocument.put(SalaryKPIFields.AGGREGATED_OBJECT, "SalesOrderObj");
    dataDocument.put(SalaryKPIFields.AGGREGATED_FIELD, "total_amount");
    dataDocument.put(SalaryKPIFields.AGGREGATION_FUNCTION, "sum");
    dataDocument.put(SalaryKPIFields.AGGREGATED_PERSON_FIELD, "sales_person");
    dataDocument.put(SalaryKPIFields.AGGREGATED_DATE_FIELD, "order_date");
    dataDocument.put(SalaryKPIFields.UNIT, "元");

    result.setData(dataDocument);

    // 执行测试
    StandardWebDetailController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getData());
    assertTrue(actualResult.getData().containsKey("dimension_description"));

    String dimensionDescription = (String) actualResult.getData().get("dimension_description");
    assertNotNull(dimensionDescription);
    assertTrue(dimensionDescription.contains("对象聚合计算"));
    // 注意：现在会尝试获取对象和字段的显示名称，如果获取失败会使用API名称
    assertTrue(dimensionDescription.contains("求和"));
    assertTrue(dimensionDescription.contains("元"));
  }

  @Test
  void testAfter_AplKPIWithJsonInfo_ShouldExtractApiName() {
    // 准备测试数据 - APL函数类型的KPI，包含JSON格式的APL_INFO
    ObjectDataDocument dataDocument = createObjectDataDocument();
    dataDocument.put(SalaryKPIFields.INDICATOR_CALC_METHOD, SalaryKPIFields.INDICATOR_CALC_METHOD_Options_3);
    dataDocument.put(SalaryKPIFields.APL_INFO, "{\"apiName\":\"fmcg_skpi_salaryIndicatorAPL3__c\",\"aplName\":\"工资指标APL-3\",\"nameSpace\":\"fmcg_salary_kpi_calculate\",\"returnType\":\"Number\",\"type\":\"function\"}");
    dataDocument.put(SalaryKPIFields.UNIT, "%");

    result.setData(dataDocument);

    // 执行测试
    StandardWebDetailController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getData());
    assertTrue(actualResult.getData().containsKey("dimension_description"));

    String dimensionDescription = (String) actualResult.getData().get("dimension_description");
    assertNotNull(dimensionDescription);
    assertTrue(dimensionDescription.contains("APL函数计算"));
    assertTrue(dimensionDescription.contains("fmcg_skpi_salaryIndicatorAPL3__c")); // 应该提取apiName
    assertTrue(dimensionDescription.contains("%"));
  }

  @Test
  void testAfter_AplKPIWithPlainText_ShouldUseOriginalValue() {
    // 准备测试数据 - APL函数类型的KPI，包含普通文本的APL_INFO
    ObjectDataDocument dataDocument = createObjectDataDocument();
    dataDocument.put(SalaryKPIFields.INDICATOR_CALC_METHOD, SalaryKPIFields.INDICATOR_CALC_METHOD_Options_3);
    dataDocument.put(SalaryKPIFields.APL_INFO, "calculateSalesCommission");
    dataDocument.put(SalaryKPIFields.UNIT, "%");

    result.setData(dataDocument);

    // 执行测试
    StandardWebDetailController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getData());
    assertTrue(actualResult.getData().containsKey("dimension_description"));

    String dimensionDescription = (String) actualResult.getData().get("dimension_description");
    assertNotNull(dimensionDescription);
    assertTrue(dimensionDescription.contains("APL函数计算"));
    assertTrue(dimensionDescription.contains("calculateSalesCommission")); // 应该使用原始值
    assertTrue(dimensionDescription.contains("%"));
  }

  @Test
  void testAfter_KaoQinStatKPI_ShouldGenerateDimensionDescription() {
    // 准备测试数据 - 月度考勤表类型的KPI
    ObjectDataDocument dataDocument = createObjectDataDocument();
    dataDocument.put(SalaryKPIFields.INDICATOR_CALC_METHOD, SalaryKPIFields.INDICATOR_CALC_METHOD_Options_2);
    dataDocument.put(SalaryKPIFields.MONTHLY_ATTENDANCE_FIELD, "checkDayNum");
    dataDocument.put(SalaryKPIFields.UNIT, "天");

    result.setData(dataDocument);

    // 执行测试
    StandardWebDetailController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getData());
    assertTrue(actualResult.getData().containsKey("dimension_description"));

    String dimensionDescription = (String) actualResult.getData().get("dimension_description");
    assertNotNull(dimensionDescription);
    assertTrue(dimensionDescription.contains("月度考勤表统计"));
    // 注意：由于我们现在优先从字段描述获取显示名称，如果获取不到才使用硬编码描述
    // 所以这里的测试需要更灵活一些
    assertTrue(dimensionDescription.contains("天"));
  }

  @Test
  void testAfter_AplKPI_ShouldGenerateDimensionDescription() {
    // 准备测试数据 - APL函数类型的KPI
    ObjectDataDocument dataDocument = createObjectDataDocument();
    dataDocument.put(SalaryKPIFields.INDICATOR_CALC_METHOD, SalaryKPIFields.INDICATOR_CALC_METHOD_Options_3);
    dataDocument.put(SalaryKPIFields.APL_INFO, "calculateSalesCommission");
    dataDocument.put(SalaryKPIFields.UNIT, "%");
    
    result.setData(dataDocument);

    // 执行测试
    StandardWebDetailController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getData());
    assertTrue(actualResult.getData().containsKey("dimension_description"));

    String dimensionDescription = (String) actualResult.getData().get("dimension_description");
    assertNotNull(dimensionDescription);
    assertTrue(dimensionDescription.contains("APL函数计算"));
    assertTrue(dimensionDescription.contains("calculateSalesCommission"));
    assertTrue(dimensionDescription.contains("%"));
  }

  @Test
  void testAfter_WithNullData_ShouldNotThrowException() {
    // 准备测试数据 - 数据为null
    result.setData(null);

    // 执行测试 - 应该不抛出异常
    assertDoesNotThrow(() -> {
      controller.after(arg, result);
    });
  }

  @Test
  void testAfter_WithEmptyCalcMethod_ShouldGenerateDefaultDescription() {
    // 准备测试数据 - 计算方式为空
    ObjectDataDocument dataDocument = createObjectDataDocument();
    // 不设置INDICATOR_CALC_METHOD字段
    
    result.setData(dataDocument);

    // 执行测试
    StandardWebDetailController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getData());
    assertTrue(actualResult.getData().containsKey("dimension_description"));

    String dimensionDescription = (String) actualResult.getData().get("dimension_description");
    assertNotNull(dimensionDescription);
    assertTrue(dimensionDescription.contains("指标计算方式未配置"));
  }

  @Test
  void testAfter_WithUnknownCalcMethod_ShouldGenerateUnknownDescription() {
    // 准备测试数据 - 未知的计算方式
    ObjectDataDocument dataDocument = createObjectDataDocument();
    dataDocument.put(SalaryKPIFields.INDICATOR_CALC_METHOD, "unknown_method");

    result.setData(dataDocument);

    // 执行测试
    StandardWebDetailController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getData());
    assertTrue(actualResult.getData().containsKey("dimension_description"));

    String dimensionDescription = (String) actualResult.getData().get("dimension_description");
    assertNotNull(dimensionDescription);
    assertTrue(dimensionDescription.contains("未知的计算方式"));
    assertTrue(dimensionDescription.contains("unknown_method"));
  }

  @Test
  void testAfter_PartialObjectAggregateData_ShouldGeneratePartialDescription() {
    // 准备测试数据 - 部分对象聚合数据
    ObjectDataDocument dataDocument = createObjectDataDocument();
    dataDocument.put(SalaryKPIFields.INDICATOR_CALC_METHOD, SalaryKPIFields.INDICATOR_CALC_METHOD_Options_1);
    dataDocument.put(SalaryKPIFields.AGGREGATED_OBJECT, "SalesOrderObj");
    // 只设置部分字段，测试容错性

    result.setData(dataDocument);

    // 执行测试
    StandardWebDetailController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getData());
    assertTrue(actualResult.getData().containsKey("dimension_description"));

    String dimensionDescription = (String) actualResult.getData().get("dimension_description");
    assertNotNull(dimensionDescription);
    assertTrue(dimensionDescription.contains("对象聚合计算"));
    assertTrue(dimensionDescription.contains("SalesOrderObj"));
  }

  /**
   * 创建基础的ObjectDataDocument
   */
  private ObjectDataDocument createObjectDataDocument() {
    Map<String, Object> data = Maps.newHashMap();
    data.put("id", "test-kpi-id");
    data.put("name", "测试KPI指标");
    return ObjectDataDocument.of(data);
  }
}
