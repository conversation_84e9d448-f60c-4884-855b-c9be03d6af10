package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工资发放单列表头部控制器测试
 */
@ExtendWith(MockitoExtension.class)
class SalaryPaymentSlipListHeaderControllerTest {

  private SalaryPaymentSlipListHeaderController controller;
  private StandardListHeaderController.Arg arg;
  private StandardListHeaderController.Result result;

  @BeforeEach
  void setUp() {
    controller = new SalaryPaymentSlipListHeaderController();
    arg = new StandardListHeaderController.Arg();
    result = new StandardListHeaderController.Result();
  }

  @Test
  void testAfter_ShouldFilterOutAbolishButton() {
    // 准备测试数据 - 包含批量作废按钮
    List<Map<String, Object>> buttons = Lists.newArrayList();
    
    Map<String, Object> changeOwnerButton = Maps.newHashMap();
    changeOwnerButton.put("action_type", "system");
    changeOwnerButton.put("api_name", "ChangeOwner_button_default");
    changeOwnerButton.put("label", "更换负责人");
    changeOwnerButton.put("action", "ChangeOwner");
    buttons.add(changeOwnerButton);
    
    Map<String, Object> abolishButton = Maps.newHashMap();
    abolishButton.put("action_type", "system");
    abolishButton.put("api_name", "Abolish_button_default");
    abolishButton.put("label", "作废");
    abolishButton.put("action", "AsyncBulkInvalid");
    buttons.add(abolishButton);
    
//    result.setButtons(buttons);

    // 执行测试
    StandardListHeaderController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getButtons());
    assertEquals(1, actualResult.getButtons().size());
    
    // 验证剩余的按钮是更换负责人按钮
    Map<String, Object> remainingButton = actualResult.getButtons().get(0);
    assertEquals("ChangeOwner_button_default", remainingButton.get("api_name"));
    assertEquals("更换负责人", remainingButton.get("label"));
    
    // 验证批量作废按钮已被过滤掉
    boolean hasAbolishButton = actualResult.getButtons().stream()
        .anyMatch(button -> "Abolish_button_default".equals(button.get("api_name")));
    assertFalse(hasAbolishButton, "批量作废按钮应该被过滤掉");
  }

  @Test
  void testAfter_ShouldFilterOutAsyncBulkInvalidAction() {
    // 准备测试数据 - 包含AsyncBulkInvalid动作的按钮
    List<Map<String, Object>> buttons = Lists.newArrayList();
    
    Map<String, Object> normalButton = Maps.newHashMap();
    normalButton.put("action_type", "system");
    normalButton.put("api_name", "Export_button_default");
    normalButton.put("label", "导出");
    normalButton.put("action", "Export");
    buttons.add(normalButton);
    
    Map<String, Object> bulkInvalidButton = Maps.newHashMap();
    bulkInvalidButton.put("action_type", "system");
    bulkInvalidButton.put("api_name", "CustomBulkInvalid_button");
    bulkInvalidButton.put("label", "批量作废");
    bulkInvalidButton.put("action", "AsyncBulkInvalid");
    buttons.add(bulkInvalidButton);
    
//    result.setButtons(buttons);

    // 执行测试
    StandardListHeaderController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getButtons());
    assertEquals(1, actualResult.getButtons().size());
    
    // 验证剩余的按钮是导出按钮
    Map<String, Object> remainingButton = actualResult.getButtons().get(0);
    assertEquals("Export_button_default", remainingButton.get("api_name"));
    assertEquals("导出", remainingButton.get("label"));
    
    // 验证AsyncBulkInvalid动作的按钮已被过滤掉
    boolean hasAsyncBulkInvalidButton = actualResult.getButtons().stream()
        .anyMatch(button -> "AsyncBulkInvalid".equals(button.get("action")));
    assertFalse(hasAsyncBulkInvalidButton, "AsyncBulkInvalid动作的按钮应该被过滤掉");
  }

  @Test
  void testAfter_WithNullButtons_ShouldNotThrowException() {
    // 准备测试数据 - buttons为null
    result.setButtons(null);

    // 执行测试 - 应该不抛出异常
    assertDoesNotThrow(() -> {
      controller.after(arg, result);
    });
  }

  @Test
  void testAfter_WithEmptyButtons_ShouldReturnEmptyList() {
    // 准备测试数据 - 空按钮列表
    result.setButtons(Lists.newArrayList());

    // 执行测试
    StandardListHeaderController.Result actualResult = controller.after(arg, result);

    // 验证结果
    assertNotNull(actualResult.getButtons());
    assertTrue(actualResult.getButtons().isEmpty());
  }
}
