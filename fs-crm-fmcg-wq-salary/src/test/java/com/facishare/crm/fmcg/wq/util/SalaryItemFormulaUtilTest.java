package com.facishare.crm.fmcg.wq.util;

import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.dao.SalaryKPIDao;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * SalaryItemFormulaUtil 单元测试
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class SalaryItemFormulaUtilTest {

  @Mock
  private SalaryKPIDao salaryKPIDao;

  @Mock
  private ObjectDataExt salaryItemExt;

  @Mock
  private IObjectData kpiObj1;

  @Mock
  private IObjectData kpiObj2;

  private String tenantId = "test-tenant";

  @BeforeEach
  void setUp() {
    // 设置KPI对象1
    when(kpiObj1.getId()).thenReturn("kpi1");
    when(kpiObj1.getName()).thenReturn("销售业绩指标");

    // 设置KPI对象2
    when(kpiObj2.getId()).thenReturn("kpi2");
    when(kpiObj2.getName()).thenReturn("客户满意度指标");
  }

  @Test
  void testBuildFormulaDescription_CalculationFormulaType() {
    // 准备数据：取值方式为计算公式
    when(salaryItemExt.get(eq(SalaryItemFields.VALUE_TYPE), eq(String.class)))
        .thenReturn(SalaryItemFields.VALUE_TYPE_Options_2);
    when(salaryItemExt.get(eq(SalaryItemFields.CALCULATION_FORMULA), eq(String.class)))
        .thenReturn("base_salary * 0.2 + $kpi_ext_kpi1$ * 100");
    when(salaryItemExt.getDimensionValues(SalaryItemFields.FORMULA_INCLUDES_KPI))
        .thenReturn(Arrays.asList("kpi1"));
    when(salaryKPIDao.getbyKpiIds(tenantId, Arrays.asList("kpi1")))
        .thenReturn(Arrays.asList(kpiObj1));

    // 执行测试
    String result = SalaryItemFormulaUtil.buildFormulaDescription(tenantId, salaryItemExt, salaryKPIDao);

    // 验证结果
    assertEquals("base_salary * 0.2 + 销售业绩指标 * 100", result);
  }

  @Test
  void testBuildFormulaDescription_FixedValueType() {
    // 准备数据：取值方式为固定值
    when(salaryItemExt.get(eq(SalaryItemFields.VALUE_TYPE), eq(String.class)))
        .thenReturn(SalaryItemFields.VALUE_TYPE_Options_1);

    // 执行测试
    String result = SalaryItemFormulaUtil.buildFormulaDescription(tenantId, salaryItemExt, salaryKPIDao);

    // 验证结果：固定值类型应该返回null
    assertNull(result);
  }

  @Test
  void testBuildFormulaDescription_EmptyCalculationFormula() {
    // 准备数据：取值方式为计算公式，但计算公式为空
    when(salaryItemExt.get(eq(SalaryItemFields.VALUE_TYPE), eq(String.class)))
        .thenReturn(SalaryItemFields.VALUE_TYPE_Options_2);
    when(salaryItemExt.get(eq(SalaryItemFields.CALCULATION_FORMULA), eq(String.class)))
        .thenReturn("");

    // 执行测试
    String result = SalaryItemFormulaUtil.buildFormulaDescription(tenantId, salaryItemExt, salaryKPIDao);

    // 验证结果
    assertNull(result);
  }

  @Test
  void testBuildFormulaDescription_WithMultipleKPIs() {
    // 准备数据：包含多个KPI的公式
    when(salaryItemExt.get(eq(SalaryItemFields.VALUE_TYPE), eq(String.class)))
        .thenReturn(SalaryItemFields.VALUE_TYPE_Options_2);
    when(salaryItemExt.get(eq(SalaryItemFields.CALCULATION_FORMULA), eq(String.class)))
        .thenReturn("$kpi_ext_kpi1$ * 0.5 + $kpi_ext_kpi2$ * 0.3");
    when(salaryItemExt.getDimensionValues(SalaryItemFields.FORMULA_INCLUDES_KPI))
        .thenReturn(Arrays.asList("kpi1", "kpi2"));
    when(salaryKPIDao.getbyKpiIds(tenantId, Arrays.asList("kpi1", "kpi2")))
        .thenReturn(Arrays.asList(kpiObj1, kpiObj2));

    // 执行测试
    String result = SalaryItemFormulaUtil.buildFormulaDescription(tenantId, salaryItemExt, salaryKPIDao);

    // 验证结果
    assertEquals("销售业绩指标 * 0.5 + 客户满意度指标 * 0.3", result);
  }

  @Test
  void testBuildFormulaDescription_NoKPIs() {
    // 准备数据：不包含KPI的公式
    when(salaryItemExt.get(eq(SalaryItemFields.VALUE_TYPE), eq(String.class)))
        .thenReturn(SalaryItemFields.VALUE_TYPE_Options_2);
    when(salaryItemExt.get(eq(SalaryItemFields.CALCULATION_FORMULA), eq(String.class)))
        .thenReturn("base_salary * 1.2 + allowance");
    when(salaryItemExt.getDimensionValues(SalaryItemFields.FORMULA_INCLUDES_KPI))
        .thenReturn(Collections.emptyList());

    // 执行测试
    String result = SalaryItemFormulaUtil.buildFormulaDescription(tenantId, salaryItemExt, salaryKPIDao);

    // 验证结果
    assertEquals("base_salary * 1.2 + allowance", result);
  }

  @Test
  void testBuildFormulaDescription_KPINotFound() {
    // 准备数据：KPI在数据库中不存在
    when(salaryItemExt.get(eq(SalaryItemFields.VALUE_TYPE), eq(String.class)))
        .thenReturn(SalaryItemFields.VALUE_TYPE_Options_2);
    when(salaryItemExt.get(eq(SalaryItemFields.CALCULATION_FORMULA), eq(String.class)))
        .thenReturn("$kpi_ext_kpi1$ * 100");
    when(salaryItemExt.getDimensionValues(SalaryItemFields.FORMULA_INCLUDES_KPI))
        .thenReturn(Arrays.asList("kpi1"));
    when(salaryKPIDao.getbyKpiIds(tenantId, Arrays.asList("kpi1")))
        .thenReturn(Collections.emptyList());

    // 执行测试
    String result = SalaryItemFormulaUtil.buildFormulaDescription(tenantId, salaryItemExt, salaryKPIDao);

    // 验证结果：KPI未找到时，应该返回原始公式
    assertEquals("$kpi_ext_kpi1$ * 100", result);
  }

  @Test
  void testBuildFormulaDescription_ExceptionHandling() {
    // 准备数据：模拟异常情况
    when(salaryItemExt.get(eq(SalaryItemFields.VALUE_TYPE), eq(String.class)))
        .thenThrow(new RuntimeException("Test exception"));

    // 执行测试
    String result = SalaryItemFormulaUtil.buildFormulaDescription(tenantId, salaryItemExt, salaryKPIDao);

    // 验证结果：异常时应返回null
    assertNull(result);
  }

  @Test
  void testFormatCalculationFormula_BasicOperators() {
    // 测试基本操作符转换
    String input = "a + b - c * d / e";
    String result = SalaryItemFormulaUtil.formatCalculationFormula(input);
    assertEquals("a 加 b 减 c 乘以 d 除以 e", result);
  }

  @Test
  void testFormatCalculationFormula_WithParentheses() {
    // 测试括号转换
    String input = "(a + b) * (c - d)";
    String result = SalaryItemFormulaUtil.formatCalculationFormula(input);
    assertEquals("（a 加 b） 乘以 （c 减 d）", result);
  }

  @Test
  void testFormatCalculationFormula_SpecialCharacters() {
    // 测试特殊字符处理
    String input = "a & b && c";
    String result = SalaryItemFormulaUtil.formatCalculationFormula(input);
    assertEquals("a 加 b && c", result);
  }

  @Test
  void testFormatCalculationFormula_LongFormula() {
    // 测试长公式截断
    StringBuilder longFormula = new StringBuilder();
    for (int i = 0; i < 50; i++) {
      longFormula.append("variable").append(i).append(" + ");
    }
    longFormula.append("final");
    
    String result = SalaryItemFormulaUtil.formatCalculationFormula(longFormula.toString());
    
    // 验证结果长度不超过200字符
    assertTrue(result.length() <= 200);
    assertTrue(result.endsWith("..."));
  }

  @Test
  void testFormatCalculationFormula_EmptyInput() {
    // 测试空输入
    assertNull(SalaryItemFormulaUtil.formatCalculationFormula(""));
    assertNull(SalaryItemFormulaUtil.formatCalculationFormula(null));
    assertNull(SalaryItemFormulaUtil.formatCalculationFormula("   "));
  }

  @Test
  void testFormatCalculationFormula_ExceptionHandling() {
    // 测试异常处理（虽然当前实现不太可能抛出异常，但为了完整性）
    String input = "normal formula";
    String result = SalaryItemFormulaUtil.formatCalculationFormula(input);
    assertEquals("normal formula", result);
  }
}
