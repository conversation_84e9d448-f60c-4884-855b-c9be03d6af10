package com.facishare.crm.fmcg.wq.service.decorator;

import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.service.SalaryExpressionCalcService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.dto.calculate.ExpressionCheck;
import com.facishare.paas.appframework.metadata.expression.ExpressionDTO;
import com.fxiaoke.common.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 工资项表达式计算重试装饰器测试类
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
class RetrySalaryExpressionCalcServiceTest {

  @Mock
  private SalaryExpressionCalcService mockService;

  @Mock
  private SalaryContext mockContext;

  @Mock
  private ExpressionDTO mockExpressionDTO;

  @Mock
  private ServiceContext mockServiceContext;

  @Mock
  private ExpressionCheck.Result mockCheckResult;

  private RetrySalaryExpressionCalcService retryService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    retryService = new RetrySalaryExpressionCalcService(mockService, 3, 10L);
    
    // 设置mock对象的基本行为
    when(mockExpressionDTO.getExpression()).thenReturn("test_expression");
    when(mockContext.getOwner()).thenReturn("TestEmployee");
  }

  @Test
  void testSuccessfulCalculation() {
    // 准备测试数据
    Pair<String, Object> expectedResult = Pair.of("formula", 100.0);
    when(mockService.calculateWithExpression(mockExpressionDTO, mockContext)).thenReturn(expectedResult);

    // 执行测试
    Pair<String, Object> result = retryService.calculateWithExpression(mockExpressionDTO, mockContext);

    // 验证结果
    assertSame(expectedResult, result);
    verify(mockService, times(1)).calculateWithExpression(mockExpressionDTO, mockContext);
  }

  @Test
  void testRetryOnNetworkException() {
    // 准备测试数据 - 前两次网络异常，第三次成功
    Pair<String, Object> expectedResult = Pair.of("formula", 100.0);
    when(mockService.calculateWithExpression(mockExpressionDTO, mockContext))
            .thenThrow(new RuntimeException("Connection timeout"))
            .thenThrow(new RuntimeException("Network error"))
            .thenReturn(expectedResult);

    // 执行测试
    Pair<String, Object> result = retryService.calculateWithExpression(mockExpressionDTO, mockContext);

    // 验证结果
    assertSame(expectedResult, result);
    verify(mockService, times(3)).calculateWithExpression(mockExpressionDTO, mockContext);
  }

  @Test
  void testNoRetryOnSyntaxError() {
    // 准备测试数据 - 语法错误不应该重试
    RuntimeException syntaxError = new RuntimeException("Syntax error in expression");
    when(mockService.calculateWithExpression(mockExpressionDTO, mockContext)).thenThrow(syntaxError);

    // 执行测试并验证异常
    RuntimeException thrown = assertThrows(RuntimeException.class, () -> {
      retryService.calculateWithExpression(mockExpressionDTO, mockContext);
    });

    // 验证异常
    assertSame(syntaxError, thrown);

    // 验证只调用了一次（没有重试）
    verify(mockService, times(1)).calculateWithExpression(mockExpressionDTO, mockContext);
  }

  @Test
  void testNoRetryOnIllegalArgumentException() {
    // 准备测试数据 - IllegalArgumentException不应该重试
    IllegalArgumentException illegalArgException = new IllegalArgumentException("Invalid argument");
    when(mockService.calculateWithExpression(mockExpressionDTO, mockContext)).thenThrow(illegalArgException);

    // 执行测试并验证异常
    IllegalArgumentException thrown = assertThrows(IllegalArgumentException.class, () -> {
      retryService.calculateWithExpression(mockExpressionDTO, mockContext);
    });

    // 验证异常
    assertSame(illegalArgException, thrown);

    // 验证只调用了一次（没有重试）
    verify(mockService, times(1)).calculateWithExpression(mockExpressionDTO, mockContext);
  }

  @Test
  void testMaxRetriesExceeded() {
    // 准备测试数据 - 所有尝试都失败
    RuntimeException persistentError = new RuntimeException("Service unavailable");
    when(mockService.calculateWithExpression(mockExpressionDTO, mockContext)).thenThrow(persistentError);

    // 执行测试并验证异常
    RuntimeException thrown = assertThrows(RuntimeException.class, () -> {
      retryService.calculateWithExpression(mockExpressionDTO, mockContext);
    });

    // 验证异常信息
    assertTrue(thrown.getMessage().contains("工资项表达式计算失败，已重试3次"));
    assertTrue(thrown.getMessage().contains("test_expression"));
    assertTrue(thrown.getMessage().contains("TestEmployee"));
    assertSame(persistentError, thrown.getCause());

    // 验证重试次数
    verify(mockService, times(3)).calculateWithExpression(mockExpressionDTO, mockContext);
  }

  @Test
  void testExtractKpiIdsFromExpressionDelegation() {
    // 准备测试数据
    List<String> expectedKpiIds = Arrays.asList("kpi1", "kpi2");
    when(mockService.extractKpiIdsFromExpression("test_expression")).thenReturn(expectedKpiIds);

    // 执行测试
    List<String> result = retryService.extractKpiIdsFromExpression("test_expression");

    // 验证结果
    assertSame(expectedKpiIds, result);
    verify(mockService, times(1)).extractKpiIdsFromExpression("test_expression");
  }

  @Test
  void testCheckMethodDelegation() {
    // 准备测试数据
    when(mockService.check(mockExpressionDTO, mockServiceContext)).thenReturn(mockCheckResult);

    // 执行测试
    ExpressionCheck.Result result = retryService.check(mockExpressionDTO, mockServiceContext);

    // 验证结果
    assertSame(mockCheckResult, result);
    verify(mockService, times(1)).check(mockExpressionDTO, mockServiceContext);
  }

  @Test
  void testGetters() {
    // 验证getter方法
    assertSame(mockService, retryService.getDecorated());
    assertEquals(3, retryService.getMaxRetries());
    assertEquals(10L, retryService.getRetryDelayMs());
  }

  @Test
  void testDefaultConstructor() {
    // 测试默认构造函数
    RetrySalaryExpressionCalcService defaultRetryService = 
            new RetrySalaryExpressionCalcService(mockService);

    // 验证默认值
    assertEquals(3, defaultRetryService.getMaxRetries());
    assertEquals(100L, defaultRetryService.getRetryDelayMs());
  }

  @Test
  void testLongExpressionTruncation() {
    // 准备测试数据 - 长表达式
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < 150; i++) {
      sb.append("a");
    }
    String longExpression = sb.toString(); // 150个字符的表达式
    when(mockExpressionDTO.getExpression()).thenReturn(longExpression);
    
    RuntimeException testException = new RuntimeException("Test error");
    when(mockService.calculateWithExpression(mockExpressionDTO, mockContext)).thenThrow(testException);

    // 执行测试并验证异常
    RuntimeException thrown = assertThrows(RuntimeException.class, () -> {
      retryService.calculateWithExpression(mockExpressionDTO, mockContext);
    });

    // 验证异常信息包含截断的表达式
    assertTrue(thrown.getMessage().contains("..."));
    assertTrue(thrown.getMessage().length() < longExpression.length() + 100); // 确保被截断了
  }

  @Test
  void testNullExpressionHandling() {
    // 准备测试数据 - null表达式
    when(mockExpressionDTO.getExpression()).thenReturn(null);
    
    RuntimeException testException = new RuntimeException("Test error");
    when(mockService.calculateWithExpression(mockExpressionDTO, mockContext)).thenThrow(testException);

    // 执行测试并验证异常
    RuntimeException thrown = assertThrows(RuntimeException.class, () -> {
      retryService.calculateWithExpression(mockExpressionDTO, mockContext);
    });

    // 验证异常信息包含null处理
    assertTrue(thrown.getMessage().contains("表达式: [null]"));
  }
}
