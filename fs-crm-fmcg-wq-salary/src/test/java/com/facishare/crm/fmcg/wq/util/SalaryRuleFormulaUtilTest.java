package com.facishare.crm.fmcg.wq.util;

import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.constants.SalaryRuleFields;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * SalaryRuleFormulaUtil 单元测试
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class SalaryRuleFormulaUtilTest {

  @Mock
  private SalaryItemDao salaryItemDao;

  @Mock
  private ObjectDataExt salaryRuleExt;

  @Mock
  private IObjectData salaryItem1;

  @Mock
  private IObjectData salaryItem2;

  @Mock
  private IObjectData salaryItem3;

  private String tenantId = "test-tenant";

  @BeforeEach
  void setUp() {
    // 设置工资项1：基本工资（应发项）
    when(salaryItem1.getId()).thenReturn("item1");
    when(salaryItem1.getName()).thenReturn("基本工资");
    when(salaryItem1.get(eq(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB), eq(String.class)))
        .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1);

    // 设置工资项2：绩效奖金（应发项）
    when(salaryItem2.getId()).thenReturn("item2");
    when(salaryItem2.getName()).thenReturn("绩效奖金");
    when(salaryItem2.get(eq(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB), eq(String.class)))
        .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_1);

    // 设置工资项3：社保扣款（扣减项）
    when(salaryItem3.getId()).thenReturn("item3");
    when(salaryItem3.getName()).thenReturn("社保扣款");
    when(salaryItem3.get(eq(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB), eq(String.class)))
        .thenReturn(SalaryItemFields.INCREMENT_DECREMENT_ATTRIB_Options_2);
  }

  @Test
  void testBuildCalculationFormula_FirstItemIsIncome() {
    // 准备数据：第一个工资项是应发项
    List<String> salaryItemIds = Arrays.asList("item1", "item2", "item3");
    List<IObjectData> salaryItems = Arrays.asList(salaryItem1, salaryItem2, salaryItem3);

    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);
    when(salaryItemDao.getbyIds(tenantId, salaryItemIds)).thenReturn(salaryItems);

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果
    assertEquals("基本工资 + 绩效奖金 - 社保扣款", result);
  }

  @Test
  void testBuildCalculationFormula_FirstItemIsDeduction() {
    // 准备数据：第一个工资项是扣减项
    List<String> salaryItemIds = Arrays.asList("item3", "item1", "item2");
    List<IObjectData> salaryItems = Arrays.asList(salaryItem1, salaryItem2, salaryItem3);

    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);
    when(salaryItemDao.getbyIds(tenantId, salaryItemIds)).thenReturn(salaryItems);

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果
    assertEquals("-社保扣款 + 基本工资 + 绩效奖金", result);
  }

  @Test
  void testBuildCalculationFormula_AllIncomeItems() {
    // 准备数据：全部是应发项
    List<String> salaryItemIds = Arrays.asList("item1", "item2");
    List<IObjectData> salaryItems = Arrays.asList(salaryItem1, salaryItem2);

    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);
    when(salaryItemDao.getbyIds(tenantId, salaryItemIds)).thenReturn(salaryItems);

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果
    assertEquals("基本工资 + 绩效奖金", result);
  }

  @Test
  void testBuildCalculationFormula_SingleItem() {
    // 准备数据：单个工资项
    List<String> salaryItemIds = Arrays.asList("item1");
    List<IObjectData> salaryItems = Arrays.asList(salaryItem1);

    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);
    when(salaryItemDao.getbyIds(tenantId, salaryItemIds)).thenReturn(salaryItems);

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果
    assertEquals("基本工资", result);
  }

  @Test
  void testBuildCalculationFormula_EmptySalaryItems() {
    // 准备数据：空的工资项列表
    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(Collections.emptyList());

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果
    assertNull(result);
  }

  @Test
  void testBuildCalculationFormula_NullSalaryItems() {
    // 准备数据：null的工资项
    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(null);

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果
    assertNull(result);
  }

  @Test
  void testBuildCalculationFormula_SalaryItemNotFound() {
    // 准备数据：工资项在数据库中不存在
    List<String> salaryItemIds = Arrays.asList("item1", "item2");
    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);
    when(salaryItemDao.getbyIds(tenantId, salaryItemIds)).thenReturn(Collections.emptyList());

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果
    assertNull(result);
  }

  @Test
  void testBuildCalculationFormula_StringArrayInput() {
    // 准备数据：工资项ID以字符串数组形式提供
    String[] salaryItemIds = {"item1", "item2", "item3"};
    List<IObjectData> salaryItems = Arrays.asList(salaryItem1, salaryItem2, salaryItem3);

    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);
    when(salaryItemDao.getbyIds(eq(tenantId), any(List.class))).thenReturn(salaryItems);

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果
    assertEquals("基本工资 + 绩效奖金 - 社保扣款", result);
  }

  @Test
  void testBuildCalculationFormula_CommaSeperatedString() {
    // 准备数据：工资项ID以逗号分隔字符串形式提供
    String salaryItemIds = "item1,item2,item3";
    List<IObjectData> salaryItems = Arrays.asList(salaryItem1, salaryItem2, salaryItem3);

    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);
    when(salaryItemDao.getbyIds(eq(tenantId), any(List.class))).thenReturn(salaryItems);

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果
    assertEquals("基本工资 + 绩效奖金 - 社保扣款", result);
  }

  @Test
  void testBuildCalculationFormula_WithSpacesInString() {
    // 准备数据：工资项ID字符串包含空格
    String salaryItemIds = " item1 , item2 , item3 ";
    List<IObjectData> salaryItems = Arrays.asList(salaryItem1, salaryItem2, salaryItem3);

    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenReturn(salaryItemIds);
    when(salaryItemDao.getbyIds(eq(tenantId), any(List.class))).thenReturn(salaryItems);

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果
    assertEquals("基本工资 + 绩效奖金 - 社保扣款", result);
  }

  @Test
  void testBuildCalculationFormula_ExceptionHandling() {
    // 准备数据：模拟异常情况
    when(salaryRuleExt.get(SalaryRuleFields.SALARY_ITEM)).thenThrow(new RuntimeException("Test exception"));

    // 执行测试
    String result = SalaryRuleFormulaUtil.buildCalculationFormulaFromSalaryItems(
        tenantId, salaryRuleExt, salaryItemDao);

    // 验证结果：异常时应返回null
    assertNull(result);
  }
}
