package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryDetailFields;
import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDetailDao;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.crm.fmcg.wq.dao.SalaryRuleDao;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SalaryItemInvalidAction 测试类
 */
public class SalaryItemInvalidActionTest {

    @Mock
    private SalaryRuleDao salaryRuleDao;
    
    @Mock
    private SalaryItemDao salaryItemDao;
    
    @Mock
    private EmployeeFixedSalaryDetailDao employeeFixedSalaryDetailDao;
    
    @Mock
    private IObjectData salaryItemData;
    
    @Mock
    private IObjectData salaryRuleData;
    
    @Mock
    private IObjectData employeeFixedSalaryDetailData;

    private SalaryItemInvalidAction action;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        action = new SalaryItemInvalidAction();
        
        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field salaryRuleDaoField = SalaryItemInvalidAction.class.getDeclaredField("salaryRuleDao");
            salaryRuleDaoField.setAccessible(true);
            salaryRuleDaoField.set(action, salaryRuleDao);
            
            java.lang.reflect.Field salaryItemDaoField = SalaryItemInvalidAction.class.getDeclaredField("salaryItemDao");
            salaryItemDaoField.setAccessible(true);
            salaryItemDaoField.set(action, salaryItemDao);
            
            java.lang.reflect.Field employeeFixedSalaryDetailDaoField = SalaryItemInvalidAction.class.getDeclaredField("employeeFixedSalaryDetailDao");
            employeeFixedSalaryDetailDaoField.setAccessible(true);
            employeeFixedSalaryDetailDaoField.set(action, employeeFixedSalaryDetailDao);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set up test", e);
        }
    }

    @Test
    void testBuildUsageMessage_WithSalaryRulesOnly() throws Exception {
        // 准备测试数据
        List<IObjectData> usingSalaryRules = new ArrayList<>();
        usingSalaryRules.add(salaryRuleData);
        when(salaryRuleData.getName()).thenReturn("测试工资规则");
        
        List<IObjectData> usingEmployeeFixedSalaryDetails = new ArrayList<>();
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = SalaryItemInvalidAction.class.getDeclaredMethod(
                "buildUsageMessage", List.class, List.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(action, usingSalaryRules, usingEmployeeFixedSalaryDetails);
        
        // 验证结果
        assertEquals("正在使用的工资规则: 测试工资规则", result);
    }

    @Test
    void testBuildUsageMessage_WithEmployeeFixedSalaryOnly() throws Exception {
        // 准备测试数据
        List<IObjectData> usingSalaryRules = new ArrayList<>();
        
        List<IObjectData> usingEmployeeFixedSalaryDetails = new ArrayList<>();
        usingEmployeeFixedSalaryDetails.add(employeeFixedSalaryDetailData);
        when(employeeFixedSalaryDetailData.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, String.class))
                .thenReturn("test-id");
        when(employeeFixedSalaryDetailData.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY + "__r"))
                .thenReturn("测试员工固定工资表");
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = SalaryItemInvalidAction.class.getDeclaredMethod(
                "buildUsageMessage", List.class, List.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(action, usingSalaryRules, usingEmployeeFixedSalaryDetails);
        
        // 验证结果
        assertEquals("正在使用的员工固定工资表: 测试员工固定工资表", result);
    }

    @Test
    void testBuildUsageMessage_WithBothTypes() throws Exception {
        // 准备测试数据
        List<IObjectData> usingSalaryRules = new ArrayList<>();
        usingSalaryRules.add(salaryRuleData);
        when(salaryRuleData.getName()).thenReturn("测试工资规则");
        
        List<IObjectData> usingEmployeeFixedSalaryDetails = new ArrayList<>();
        usingEmployeeFixedSalaryDetails.add(employeeFixedSalaryDetailData);
        when(employeeFixedSalaryDetailData.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, String.class))
                .thenReturn("test-id");
        when(employeeFixedSalaryDetailData.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY + "__r"))
                .thenReturn("测试员工固定工资表");
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = SalaryItemInvalidAction.class.getDeclaredMethod(
                "buildUsageMessage", List.class, List.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(action, usingSalaryRules, usingEmployeeFixedSalaryDetails);
        
        // 验证结果
        assertEquals("正在使用的工资规则: 测试工资规则；正在使用的员工固定工资表: 测试员工固定工资表", result);
    }

    @Test
    void testGetEmployeeFixedSalaryName_WithRefObject() throws Exception {
        // 准备测试数据
        when(employeeFixedSalaryDetailData.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, String.class))
                .thenReturn("test-id");
        when(employeeFixedSalaryDetailData.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY + "__r"))
                .thenReturn("测试员工固定工资表");
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = SalaryItemInvalidAction.class.getDeclaredMethod(
                "getEmployeeFixedSalaryName", IObjectData.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(action, employeeFixedSalaryDetailData);
        
        // 验证结果
        assertEquals("测试员工固定工资表", result);
    }

    @Test
    void testGetEmployeeFixedSalaryName_WithoutRefObject() throws Exception {
        // 准备测试数据
        when(employeeFixedSalaryDetailData.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, String.class))
                .thenReturn("test-id");
        when(employeeFixedSalaryDetailData.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY + "__r"))
                .thenReturn(null);
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = SalaryItemInvalidAction.class.getDeclaredMethod(
                "getEmployeeFixedSalaryName", IObjectData.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(action, employeeFixedSalaryDetailData);
        
        // 验证结果
        assertEquals("员工固定工资表(test-id)", result);
    }

    @Test
    void testGetEmployeeFixedSalaryName_WithBlankId() throws Exception {
        // 准备测试数据
        when(employeeFixedSalaryDetailData.get(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, String.class))
                .thenReturn("");
        
        // 使用反射调用私有方法
        java.lang.reflect.Method method = SalaryItemInvalidAction.class.getDeclaredMethod(
                "getEmployeeFixedSalaryName", IObjectData.class);
        method.setAccessible(true);
        
        String result = (String) method.invoke(action, employeeFixedSalaryDetailData);
        
        // 验证结果
        assertEquals("未知员工固定工资表", result);
    }
}
