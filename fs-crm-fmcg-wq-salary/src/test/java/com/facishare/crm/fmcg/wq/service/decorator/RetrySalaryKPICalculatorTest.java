package com.facishare.crm.fmcg.wq.service.decorator;

import com.facishare.crm.fmcg.wq.model.MetricCalculateResult;
import com.facishare.crm.fmcg.wq.model.SalaryContext;
import com.facishare.crm.fmcg.wq.model.kpi.SalaryKPI;
import com.facishare.crm.fmcg.wq.service.SalaryKPICalculator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * KPI计算重试装饰器测试类
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
class RetrySalaryKPICalculatorTest {

  @Mock
  private SalaryKPICalculator<SalaryKPI> mockCalculator;

  @Mock
  private SalaryContext mockContext;

  @Mock
  private SalaryKPI mockKPI;

  @Mock
  private MetricCalculateResult mockResult;

  private RetrySalaryKPICalculator<SalaryKPI> retryCalculator;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    retryCalculator = new RetrySalaryKPICalculator<>(mockCalculator, 3, 10L);
    
    // 设置mock对象的基本行为
    when(mockKPI.getName()).thenReturn("TestKPI");
    when(mockContext.getOwner()).thenReturn("TestEmployee");
  }

  @Test
  void testSuccessfulCalculation() {
    // 准备测试数据
    when(mockCalculator.doCalculate(mockContext, mockKPI)).thenReturn(mockResult);

    // 执行测试
    MetricCalculateResult result = retryCalculator.doCalculate(mockContext, mockKPI);

    // 验证结果
    assertSame(mockResult, result);
    verify(mockCalculator, times(1)).doCalculate(mockContext, mockKPI);
  }

  @Test
  void testRetryOnException() {
    // 准备测试数据 - 前两次失败，第三次成功
    when(mockCalculator.doCalculate(mockContext, mockKPI))
            .thenThrow(new RuntimeException("Network timeout"))
            .thenThrow(new RuntimeException("Connection failed"))
            .thenReturn(mockResult);

    // 执行测试
    MetricCalculateResult result = retryCalculator.doCalculate(mockContext, mockKPI);

    // 验证结果
    assertSame(mockResult, result);
    verify(mockCalculator, times(3)).doCalculate(mockContext, mockKPI);
  }

  @Test
  void testMaxRetriesExceeded() {
    // 准备测试数据 - 所有尝试都失败
    RuntimeException testException = new RuntimeException("Persistent failure");
    when(mockCalculator.doCalculate(mockContext, mockKPI)).thenThrow(testException);

    // 执行测试并验证异常
    RuntimeException thrown = assertThrows(RuntimeException.class, () -> {
      retryCalculator.doCalculate(mockContext, mockKPI);
    });

    // 验证异常信息
    assertTrue(thrown.getMessage().contains("KPI计算失败，已重试3次"));
    assertTrue(thrown.getMessage().contains("TestKPI"));
    assertTrue(thrown.getMessage().contains("TestEmployee"));
    assertSame(testException, thrown.getCause());

    // 验证重试次数
    verify(mockCalculator, times(3)).doCalculate(mockContext, mockKPI);
  }

  @Test
  void testValidateMethodDelegation() {
    // 执行测试
    retryCalculator.validate(mockKPI);

    // 验证委托调用
    verify(mockCalculator, times(1)).validate(mockKPI);
  }

  @Test
  void testGetters() {
    // 验证getter方法
    assertSame(mockCalculator, retryCalculator.getDecorated());
    assertEquals(3, retryCalculator.getMaxRetries());
    assertEquals(10L, retryCalculator.getRetryDelayMs());
  }

  @Test
  void testDefaultConstructor() {
    // 测试默认构造函数
    RetrySalaryKPICalculator<SalaryKPI> defaultRetryCalculator = 
            new RetrySalaryKPICalculator<>(mockCalculator);

    // 验证默认值
    assertEquals(3, defaultRetryCalculator.getMaxRetries());
    assertEquals(100L, defaultRetryCalculator.getRetryDelayMs());
  }

  @Test
  void testInterruptedException() {
    // 准备测试数据
    when(mockCalculator.doCalculate(mockContext, mockKPI))
            .thenThrow(new RuntimeException("First failure"))
            .thenReturn(mockResult);

    // 在第一次重试等待时中断线程
    Thread.currentThread().interrupt();

    // 执行测试
    RuntimeException thrown = assertThrows(RuntimeException.class, () -> {
      retryCalculator.doCalculate(mockContext, mockKPI);
    });

    // 验证异常信息
    assertTrue(thrown.getMessage().contains("KPI计算失败"));

    // 验证线程中断状态被恢复
    assertTrue(Thread.interrupted()); // 这会清除中断状态

    // 验证只调用了一次（因为中断了重试）
    verify(mockCalculator, times(1)).doCalculate(mockContext, mockKPI);
  }
}
