package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.SalaryPaymentSlipFields;
import com.facishare.crm.fmcg.wq.dao.SalaryPaymentSlipDao;
import com.facishare.crm.fmcg.wq.exception.CheckinsException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 工资发放单作废Action测试
 */
@ExtendWith(MockitoExtension.class)
class SalaryPaymentSlipInvalidActionTest {

  @Mock
  private SalaryPaymentSlipDao salaryPaymentSlipDao;

  @Mock
  private ActionContext actionContext;

  @Mock
  private IObjectData salaryPaymentSlip;

  @Mock
  private ApplicationContext applicationContext;

  @InjectMocks
  private SalaryPaymentSlipInvalidAction action;

  private StandardInvalidAction.Arg arg;

  @BeforeEach
  void setUp() {
    arg = new StandardInvalidAction.Arg();
    arg.setObjectDataId("test-id");
    
    // 设置actionContext
    when(actionContext.getTenantId()).thenReturn("test-tenant");
  }

  @Test
  void testInvalidAction_DistributingStatus_ShouldThrowExceptionWithCorrectMessage() {
    // 模拟Spring上下文
    try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
      springUtilMock.when(SpringUtil::getContext).thenReturn(applicationContext);
      when(applicationContext.getBean(SalaryPaymentSlipDao.class)).thenReturn(salaryPaymentSlipDao);
      
      // 设置工资发放单状态为"发放中"
      when(salaryPaymentSlipDao.getById("test-tenant", "test-id")).thenReturn(salaryPaymentSlip);
      when(salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class))
          .thenReturn(SalaryPaymentSlipFields.PAY_STATUS_Options_2);

      // 执行测试
      CheckinsException exception = assertThrows(CheckinsException.class, () -> {
        action.before(arg);
      });

      // 验证异常信息
      assertEquals("工资单正在发放中，不可作废", exception.getMessage()); //ignoreI18n
    }
  }

  @Test
  void testInvalidAction_DistributedStatus_ShouldThrowExceptionWithCorrectMessage() {
    // 模拟Spring上下文
    try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
      springUtilMock.when(SpringUtil::getContext).thenReturn(applicationContext);
      when(applicationContext.getBean(SalaryPaymentSlipDao.class)).thenReturn(salaryPaymentSlipDao);
      
      // 设置工资发放单状态为"已发放"
      when(salaryPaymentSlipDao.getById("test-tenant", "test-id")).thenReturn(salaryPaymentSlip);
      when(salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class))
          .thenReturn(SalaryPaymentSlipFields.PAY_STATUS_Options_4);

      // 执行测试
      CheckinsException exception = assertThrows(CheckinsException.class, () -> {
        action.before(arg);
      });

      // 验证异常信息
      assertEquals("此工资单已发放完成，不可作废", exception.getMessage()); //ignoreI18n
    }
  }

  @Test
  void testInvalidAction_GeneratingStatusWithinTwoHours_ShouldThrowException() {
    // 模拟Spring上下文
    try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
      springUtilMock.when(SpringUtil::getContext).thenReturn(applicationContext);
      when(applicationContext.getBean(SalaryPaymentSlipDao.class)).thenReturn(salaryPaymentSlipDao);
      
      // 设置工资发放单状态为"正在生成"且创建时间在2小时内
      when(salaryPaymentSlipDao.getById("test-tenant", "test-id")).thenReturn(salaryPaymentSlip);
      when(salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class))
          .thenReturn(SalaryPaymentSlipFields.PAY_STATUS_Options_0);
      when(salaryPaymentSlip.getCreateTime()).thenReturn(System.currentTimeMillis() - 3600000); // 1小时前

      // 执行测试
      assertThrows(CheckinsException.class, () -> {
        action.before(arg);
      });
    }
  }

  @Test
  void testInvalidAction_GeneratingStatusOverTwoHours_ShouldAllowInvalid() {
    // 模拟Spring上下文
    try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
      springUtilMock.when(SpringUtil::getContext).thenReturn(applicationContext);
      when(applicationContext.getBean(SalaryPaymentSlipDao.class)).thenReturn(salaryPaymentSlipDao);
      
      // 设置工资发放单状态为"正在生成"且创建时间超过2小时
      when(salaryPaymentSlipDao.getById("test-tenant", "test-id")).thenReturn(salaryPaymentSlip);
      when(salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class))
          .thenReturn(SalaryPaymentSlipFields.PAY_STATUS_Options_0);
      when(salaryPaymentSlip.getCreateTime()).thenReturn(System.currentTimeMillis() - 7300000); // 超过2小时

      // 执行测试 - 应该不抛出异常
      assertDoesNotThrow(() -> {
        action.before(arg);
      });
    }
  }

  @Test
  void testInvalidAction_ErrorStatus_ShouldAllowInvalid() {
    // 模拟Spring上下文
    try (MockedStatic<SpringUtil> springUtilMock = mockStatic(SpringUtil.class)) {
      springUtilMock.when(SpringUtil::getContext).thenReturn(applicationContext);
      when(applicationContext.getBean(SalaryPaymentSlipDao.class)).thenReturn(salaryPaymentSlipDao);
      
      // 设置工资发放单状态为"生成异常"
      when(salaryPaymentSlipDao.getById("test-tenant", "test-id")).thenReturn(salaryPaymentSlip);
      when(salaryPaymentSlip.get(SalaryPaymentSlipFields.PAY_STATUS, String.class))
          .thenReturn(SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR);

      // 执行测试 - 应该不抛出异常
      assertDoesNotThrow(() -> {
        action.before(arg);
      });
    }
  }
}
