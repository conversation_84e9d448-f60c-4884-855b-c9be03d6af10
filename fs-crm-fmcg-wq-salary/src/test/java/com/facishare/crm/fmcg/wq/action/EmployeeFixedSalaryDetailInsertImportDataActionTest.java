package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryDetailFields;
import com.facishare.crm.fmcg.wq.constants.EmployeeFixedSalaryFields;
import com.facishare.crm.fmcg.wq.constants.SalaryItemFields;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeFixedSalaryDetailDao;
import com.facishare.crm.fmcg.wq.dao.SalaryItemDao;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.predef.action.BaseImportDataAction.ImportData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 员工固定工资明细导入Action单元测试
 * 
 * 测试重点：
 * 1. 员工工资项唯一性验证（与现有数据对比）
 * 2. 工资项启用状态验证
 * 3. 金额验证
 * 4. 导入数据内部重复检查
 * 5. 业务类型设置
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class EmployeeFixedSalaryDetailInsertImportDataActionTest {

  @Mock
  private EmployeeFixedSalaryDao employeeFixedSalaryDao;

  @Mock
  private EmployeeFixedSalaryDetailDao employeeFixedSalaryDetailDao;

  @Mock
  private SalaryItemDao salaryItemDao;

  @Mock
  private ActionContext actionContext;

  @InjectMocks
  private EmployeeFixedSalaryDetailInsertImportDataAction action;

  private static final String TENANT_ID = "test-tenant";
  private static final String EMPLOYEE_FIXED_SALARY_ID_1 = "emp-salary-1";
  private static final String EMPLOYEE_FIXED_SALARY_ID_2 = "emp-salary-2";
  private static final String SALARY_ITEM_ID_1 = "salary-item-1";
  private static final String SALARY_ITEM_ID_2 = "salary-item-2";
  private static final String INTERNAL_EMPLOYEE_ID = "12345";
  private static final String EXTERNAL_EMPLOYEE_ID = "123456789";

  @BeforeEach
  void setUp() {
    when(actionContext.getTenantId()).thenReturn(TENANT_ID);
  }

  /**
   * 测试员工工资项唯一性验证 - 与现有数据冲突
   */
  @Test
  void testValidateEmployeeSalaryItemUniqueness_ExistingDataConflict() {
    // 准备现有数据
    IObjectData existingDetail = createEmployeeFixedSalaryDetail(EMPLOYEE_FIXED_SALARY_ID_1, SALARY_ITEM_ID_1, "100.00");
    when(employeeFixedSalaryDetailDao.getByEmployeeFixedSalaryMainId(TENANT_ID, EMPLOYEE_FIXED_SALARY_ID_1))
        .thenReturn(Lists.newArrayList(existingDetail));

    // 准备导入数据（与现有数据冲突）
    List<ImportData> importDataList = Lists.newArrayList();
    ImportData conflictData = createImportData(1, EMPLOYEE_FIXED_SALARY_ID_1, SALARY_ITEM_ID_1, "200.00");
    importDataList.add(conflictData);

    // 准备工资项数据
    IObjectData salaryItem = createSalaryItem(SALARY_ITEM_ID_1, "基本工资", true);
    when(salaryItemDao.getByIds(TENANT_ID, Lists.newArrayList(SALARY_ITEM_ID_1)))
        .thenReturn(Lists.newArrayList(salaryItem));

    // 准备员工固定工资表数据
    IObjectData employeeFixedSalary = createEmployeeFixedSalary(EMPLOYEE_FIXED_SALARY_ID_1, INTERNAL_EMPLOYEE_ID, null);
    when(employeeFixedSalaryDao.getByIds(TENANT_ID, Lists.newArrayList(EMPLOYEE_FIXED_SALARY_ID_1)))
        .thenReturn(Lists.newArrayList(employeeFixedSalary));

    // 执行验证
    try {
      action.customValidate(importDataList);
      fail("应该抛出验证异常");
    } catch (Exception e) {
      // 验证错误信息包含冲突提示
      assertTrue(e.getMessage().contains("已存在工资项") || e.getMessage().contains("不能重复创建"));
    }
  }

  /**
   * 测试员工工资项唯一性验证 - 导入数据内部重复
   */
  @Test
  void testValidateEmployeeSalaryItemUniqueness_InternalDuplication() {
    // 准备导入数据（内部重复）
    List<ImportData> importDataList = Lists.newArrayList();
    ImportData data1 = createImportData(1, EMPLOYEE_FIXED_SALARY_ID_1, SALARY_ITEM_ID_1, "100.00");
    ImportData data2 = createImportData(2, EMPLOYEE_FIXED_SALARY_ID_1, SALARY_ITEM_ID_1, "200.00"); // 重复
    importDataList.add(data1);
    importDataList.add(data2);

    // 准备工资项数据
    IObjectData salaryItem = createSalaryItem(SALARY_ITEM_ID_1, "基本工资", true);
    when(salaryItemDao.getByIds(TENANT_ID, Lists.newArrayList(SALARY_ITEM_ID_1)))
        .thenReturn(Lists.newArrayList(salaryItem));

    // 准备员工固定工资表数据
    IObjectData employeeFixedSalary = createEmployeeFixedSalary(EMPLOYEE_FIXED_SALARY_ID_1, INTERNAL_EMPLOYEE_ID, null);
    when(employeeFixedSalaryDao.getByIds(TENANT_ID, Lists.newArrayList(EMPLOYEE_FIXED_SALARY_ID_1)))
        .thenReturn(Lists.newArrayList(employeeFixedSalary));

    // 没有现有明细数据
    when(employeeFixedSalaryDetailDao.getByEmployeeFixedSalaryMainId(TENANT_ID, EMPLOYEE_FIXED_SALARY_ID_1))
        .thenReturn(Lists.newArrayList());

    // 执行验证
    try {
      action.customValidate(importDataList);
      fail("应该抛出验证异常");
    } catch (Exception e) {
      // 验证错误信息包含重复提示
      assertTrue(e.getMessage().contains("重复") || e.getMessage().contains("duplicate"));
    }
  }

  /**
   * 测试工资项启用状态验证 - 禁用的工资项
   */
  @Test
  void testValidateSalaryItems_DisabledItem() {
    // 准备导入数据
    List<ImportData> importDataList = Lists.newArrayList();
    ImportData data = createImportData(1, EMPLOYEE_FIXED_SALARY_ID_1, SALARY_ITEM_ID_1, "100.00");
    importDataList.add(data);

    // 准备禁用的工资项数据
    IObjectData disabledSalaryItem = createSalaryItem(SALARY_ITEM_ID_1, "基本工资", false);
    when(salaryItemDao.getByIds(TENANT_ID, Lists.newArrayList(SALARY_ITEM_ID_1)))
        .thenReturn(Lists.newArrayList(disabledSalaryItem));

    // 准备员工固定工资表数据
    IObjectData employeeFixedSalary = createEmployeeFixedSalary(EMPLOYEE_FIXED_SALARY_ID_1, INTERNAL_EMPLOYEE_ID, null);
    when(employeeFixedSalaryDao.getByIds(TENANT_ID, Lists.newArrayList(EMPLOYEE_FIXED_SALARY_ID_1)))
        .thenReturn(Lists.newArrayList(employeeFixedSalary));

    // 没有现有明细数据
    when(employeeFixedSalaryDetailDao.getByEmployeeFixedSalaryMainId(TENANT_ID, EMPLOYEE_FIXED_SALARY_ID_1))
        .thenReturn(Lists.newArrayList());

    // 执行验证
    try {
      action.customValidate(importDataList);
      fail("应该抛出验证异常");
    } catch (Exception e) {
      // 验证错误信息包含禁用提示
      assertTrue(e.getMessage().contains("已禁用") || e.getMessage().contains("disabled"));
    }
  }

  /**
   * 测试金额验证 - 负数金额
   */
  @Test
  void testValidateAmounts_NegativeAmount() {
    // 准备导入数据（负数金额）
    List<ImportData> importDataList = Lists.newArrayList();
    ImportData data = createImportData(1, EMPLOYEE_FIXED_SALARY_ID_1, SALARY_ITEM_ID_1, "-100.00");
    importDataList.add(data);

    // 准备工资项数据
    IObjectData salaryItem = createSalaryItem(SALARY_ITEM_ID_1, "基本工资", true);
    when(salaryItemDao.getByIds(TENANT_ID, Lists.newArrayList(SALARY_ITEM_ID_1)))
        .thenReturn(Lists.newArrayList(salaryItem));

    // 准备员工固定工资表数据
    IObjectData employeeFixedSalary = createEmployeeFixedSalary(EMPLOYEE_FIXED_SALARY_ID_1, INTERNAL_EMPLOYEE_ID, null);
    when(employeeFixedSalaryDao.getByIds(TENANT_ID, Lists.newArrayList(EMPLOYEE_FIXED_SALARY_ID_1)))
        .thenReturn(Lists.newArrayList(employeeFixedSalary));

    // 没有现有明细数据
    when(employeeFixedSalaryDetailDao.getByEmployeeFixedSalaryMainId(TENANT_ID, EMPLOYEE_FIXED_SALARY_ID_1))
        .thenReturn(Lists.newArrayList());

    // 执行验证
    try {
      action.customValidate(importDataList);
      fail("应该抛出验证异常");
    } catch (Exception e) {
      // 验证错误信息包含负数提示
      assertTrue(e.getMessage().contains("不能为负数") || e.getMessage().contains("negative"));
    }
  }

  /**
   * 测试成功场景 - 无冲突的新数据
   */
  @Test
  void testValidateSuccess_NoConflict() {
    // 准备导入数据（不同员工和工资项）
    List<ImportData> importDataList = Lists.newArrayList();
    ImportData data1 = createImportData(1, EMPLOYEE_FIXED_SALARY_ID_1, SALARY_ITEM_ID_1, "100.00");
    ImportData data2 = createImportData(2, EMPLOYEE_FIXED_SALARY_ID_2, SALARY_ITEM_ID_2, "200.00");
    importDataList.add(data1);
    importDataList.add(data2);

    // 准备工资项数据
    IObjectData salaryItem1 = createSalaryItem(SALARY_ITEM_ID_1, "基本工资", true);
    IObjectData salaryItem2 = createSalaryItem(SALARY_ITEM_ID_2, "绩效工资", true);
    when(salaryItemDao.getByIds(eq(TENANT_ID), anyList()))
        .thenReturn(Lists.newArrayList(salaryItem1, salaryItem2));

    // 准备员工固定工资表数据
    IObjectData employeeFixedSalary1 = createEmployeeFixedSalary(EMPLOYEE_FIXED_SALARY_ID_1, INTERNAL_EMPLOYEE_ID, null);
    IObjectData employeeFixedSalary2 = createEmployeeFixedSalary(EMPLOYEE_FIXED_SALARY_ID_2, null, EXTERNAL_EMPLOYEE_ID);
    when(employeeFixedSalaryDao.getByIds(eq(TENANT_ID), anyList()))
        .thenReturn(Lists.newArrayList(employeeFixedSalary1, employeeFixedSalary2));

    // 没有现有明细数据
    when(employeeFixedSalaryDetailDao.getByEmployeeFixedSalaryMainId(eq(TENANT_ID), anyString()))
        .thenReturn(Lists.newArrayList());

    // 执行验证 - 应该成功
    assertDoesNotThrow(() -> action.customValidate(importDataList));
  }

  /**
   * 创建导入数据
   */
  private ImportData createImportData(int rowNo, String employeeFixedSalaryId, String salaryItemId, String amount) {
    IObjectData objectData = new ObjectData();
    objectData.set(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, employeeFixedSalaryId);
    objectData.set(EmployeeFixedSalaryDetailFields.SALARY_ITEM, salaryItemId);
    objectData.set(EmployeeFixedSalaryDetailFields.AMOUNT, new BigDecimal(amount));

    ImportData importData = mock(ImportData.class);
    when(importData.getRowNo()).thenReturn(rowNo);
    when(importData.getData()).thenReturn(objectData);
    return importData;
  }

  /**
   * 创建员工固定工资明细
   */
  private IObjectData createEmployeeFixedSalaryDetail(String employeeFixedSalaryId, String salaryItemId, String amount) {
    IObjectData detail = new ObjectData();
    detail.set(EmployeeFixedSalaryDetailFields.EMPLOYEE_FIXED_SALARY, employeeFixedSalaryId);
    detail.set(EmployeeFixedSalaryDetailFields.SALARY_ITEM, salaryItemId);
    detail.set(EmployeeFixedSalaryDetailFields.AMOUNT, new BigDecimal(amount));
    return detail;
  }

  /**
   * 创建工资项
   */
  private IObjectData createSalaryItem(String id, String name, boolean enabled) {
    IObjectData salaryItem = new ObjectData();
    salaryItem.setId(id);
    salaryItem.set("name", name);
    salaryItem.set(SalaryItemFields.ENABLED_STATUS, enabled ?
        SalaryItemFields.ENABLED_STATUS_Options_true : SalaryItemFields.ENABLED_STATUS_Options_false);
    return salaryItem;
  }

  /**
   * 创建员工固定工资表
   */
  private IObjectData createEmployeeFixedSalary(String id, String internalEmployeeId, String externalEmployeeId) {
    IObjectData employeeFixedSalary = new ObjectData();
    employeeFixedSalary.setId(id);
    if (internalEmployeeId != null) {
      employeeFixedSalary.set(EmployeeFixedSalaryFields.EMPLOYEE, internalEmployeeId);
    }
    if (externalEmployeeId != null) {
      employeeFixedSalary.set(EmployeeFixedSalaryFields.EMPLOYEE_EXTERNAL, externalEmployeeId);
    }
    return employeeFixedSalary;
  }
}
