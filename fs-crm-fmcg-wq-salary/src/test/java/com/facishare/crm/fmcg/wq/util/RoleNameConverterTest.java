package com.facishare.crm.fmcg.wq.util;

import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.RolePojo;
import com.fxiaoke.paas.auth.factory.RoleClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RoleNameConverter 单元测试
 * 测试角色ID到角色名称的转换功能
 *
 * <AUTHOR>
 * @create 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
class RoleNameConverterTest {

  @Mock
  private RoleClient roleClient;

  private String tenantId;
  private List<String> roleIds;
  private List<RolePojo> rolePojoList;

  @BeforeEach
  void setUp() {
    tenantId = "test-tenant-123";
    roleIds = Arrays.asList("role001", "role002", "role003");

    // 设置角色信息列表
    rolePojoList = new ArrayList<>();

    RolePojo rolePojo1 = new RolePojo();
    rolePojo1.setRoleCode("role001");
    rolePojo1.setRoleName("销售经理"); //ignoreI18n
    rolePojoList.add(rolePojo1);

    RolePojo rolePojo2 = new RolePojo();
    rolePojo2.setRoleCode("role002");
    rolePojo2.setRoleName("区域主管"); //ignoreI18n
    rolePojoList.add(rolePojo2);

    RolePojo rolePojo3 = new RolePojo();
    rolePojo3.setRoleCode("role003");
    rolePojo3.setRoleName("业务代表"); //ignoreI18n
    rolePojoList.add(rolePojo3);
  }

  @Test
  void testConvertRoleIdsToNames_Success() {
    // 准备数据
    when(roleClient.queryAllRoleInfo(any(AuthContext.class), anySet(), any(Boolean.class)))
        .thenReturn(rolePojoList);

    // 执行测试
    List<String> result = RoleNameConverter.convertRoleIdsToNames(roleClient, tenantId, roleIds);

    // 验证结果
    assertNotNull(result);
    assertEquals(3, result.size());
    assertEquals("销售经理", result.get(0)); //ignoreI18n
    assertEquals("区域主管", result.get(1)); //ignoreI18n
    assertEquals("业务代表", result.get(2)); //ignoreI18n

    // 验证方法调用
    verify(roleClient, times(1)).queryAllRoleInfo(any(AuthContext.class), anySet(), any(Boolean.class));
  }

  @Test
  void testConvertRoleIdsToNames_EmptyRoleIds() {
    // 执行测试
    List<String> result = RoleNameConverter.convertRoleIdsToNames(roleClient, tenantId, Collections.emptyList());

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());

    // 验证没有调用 roleClient
    verify(roleClient, never()).queryAllRoleInfo(any(AuthContext.class), anySet(), any(Boolean.class));
  }

  @Test
  void testConvertRoleIdsToNames_NullRoleIds() {
    // 执行测试
    List<String> result = RoleNameConverter.convertRoleIdsToNames(roleClient, tenantId, null);

    // 验证结果
    assertNotNull(result);
    assertTrue(result.isEmpty());

    // 验证没有调用 roleClient
    verify(roleClient, never()).queryAllRoleInfo(any(AuthContext.class), anySet(), any(Boolean.class));
  }

  @Test
  void testConvertRoleIdToName_Success() {
    // 准备数据
    when(roleClient.queryAllRoleInfo(any(AuthContext.class), anySet(), any(Boolean.class)))
        .thenReturn(rolePojoList);

    // 执行测试
    String result = RoleNameConverter.convertRoleIdToName(roleClient, tenantId, "role001");

    // 验证结果
    assertNotNull(result);
    assertEquals("销售经理", result); //ignoreI18n

    // 验证方法调用
    verify(roleClient, times(1)).queryAllRoleInfo(any(AuthContext.class), anySet(), any(Boolean.class));
  }

  @Test
  void testConvertRoleIdToName_BlankRoleId() {
    // 执行测试
    String result = RoleNameConverter.convertRoleIdToName(roleClient, tenantId, "");

    // 验证结果
    assertNotNull(result);
    assertEquals("", result);

    // 验证没有调用 roleClient
    verify(roleClient, never()).queryAllRoleInfo(any(AuthContext.class), anySet(), any(Boolean.class));
  }
}
