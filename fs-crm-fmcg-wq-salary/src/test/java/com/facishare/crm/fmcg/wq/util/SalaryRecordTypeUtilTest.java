package com.facishare.crm.fmcg.wq.util;

import com.facishare.crm.fmcg.wq.constants.SalaryDataFields;
import com.facishare.crm.fmcg.wq.constants.SalaryDetailDataFields;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SalaryRecordTypeUtil 单元测试
 * 
 * <AUTHOR>
 * @create 2025-01-27
 */
class SalaryRecordTypeUtilTest {

  @Test
  void testGetSalaryDataRecordType_InternalEmployee() {
    // 测试内部员工
    String employeeId = "12345";
    String employeeExternalId = null;
    
    String result = SalaryRecordTypeUtil.getSalaryDataRecordType(employeeId, employeeExternalId);
    
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testGetSalaryDataRecordType_ExternalEmployee() {
    // 测试外部员工
    String employeeId = null;
    String employeeExternalId = "100000001";
    
    String result = SalaryRecordTypeUtil.getSalaryDataRecordType(employeeId, employeeExternalId);
    
    assertEquals(SalaryDataFields.RECORD_TYPE_EXTERNAL, result);
  }

  @Test
  void testGetSalaryDataRecordType_BothEmployeeIds() {
    // 测试同时有内部员工ID和外部员工ID（外部员工优先）
    String employeeId = "12345";
    String employeeExternalId = "100000001";
    
    String result = SalaryRecordTypeUtil.getSalaryDataRecordType(employeeId, employeeExternalId);
    
    assertEquals(SalaryDataFields.RECORD_TYPE_EXTERNAL, result);
  }

  @Test
  void testGetSalaryDataRecordType_BothEmpty() {
    // 测试都为空的情况
    String employeeId = null;
    String employeeExternalId = null;
    
    String result = SalaryRecordTypeUtil.getSalaryDataRecordType(employeeId, employeeExternalId);
    
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testGetSalaryDetailDataRecordType_InternalEmployee() {
    // 测试内部员工
    String employeeId = "12345";
    String employeeExternalId = "";
    
    String result = SalaryRecordTypeUtil.getSalaryDetailDataRecordType(employeeId, employeeExternalId);
    
    assertEquals(SalaryDetailDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testGetSalaryDetailDataRecordType_ExternalEmployee() {
    // 测试外部员工
    String employeeId = "";
    String employeeExternalId = "100000001";
    
    String result = SalaryRecordTypeUtil.getSalaryDetailDataRecordType(employeeId, employeeExternalId);
    
    assertEquals(SalaryDetailDataFields.RECORD_TYPE_EXTERNAL, result);
  }

  @Test
  void testGetRecordTypeByEmployeeId_InternalEmployee() {
    // 测试内部员工ID（小于等于100000000）
    String employeeId = "12345";
    
    // 测试工资条
    String salaryDataResult = SalaryRecordTypeUtil.getRecordTypeByEmployeeId(employeeId, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, salaryDataResult);
    
    // 测试工资条明细
    String salaryDetailResult = SalaryRecordTypeUtil.getRecordTypeByEmployeeId(employeeId, false);
    assertEquals(SalaryDetailDataFields.RECORD_TYPE_INTERNAL, salaryDetailResult);
  }

  @Test
  void testGetRecordTypeByEmployeeId_ExternalEmployee() {
    // 测试外部员工ID（大于100000000）
    String employeeId = "100000001";
    
    // 测试工资条
    String salaryDataResult = SalaryRecordTypeUtil.getRecordTypeByEmployeeId(employeeId, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_EXTERNAL, salaryDataResult);
    
    // 测试工资条明细
    String salaryDetailResult = SalaryRecordTypeUtil.getRecordTypeByEmployeeId(employeeId, false);
    assertEquals(SalaryDetailDataFields.RECORD_TYPE_EXTERNAL, salaryDetailResult);
  }

  @Test
  void testGetRecordTypeByEmployeeId_BoundaryValue() {
    // 测试边界值100000000
    String employeeId = "100000000";
    
    String result = SalaryRecordTypeUtil.getRecordTypeByEmployeeId(employeeId, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testGetRecordTypeByEmployeeId_BoundaryValuePlusOne() {
    // 测试边界值100000001
    String employeeId = "100000001";
    
    String result = SalaryRecordTypeUtil.getRecordTypeByEmployeeId(employeeId, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_EXTERNAL, result);
  }

  @Test
  void testGetRecordTypeByEmployeeId_EmptyEmployeeId() {
    // 测试空员工ID
    String employeeId = "";
    
    String result = SalaryRecordTypeUtil.getRecordTypeByEmployeeId(employeeId, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testGetRecordTypeByEmployeeId_NullEmployeeId() {
    // 测试null员工ID
    String employeeId = null;
    
    String result = SalaryRecordTypeUtil.getRecordTypeByEmployeeId(employeeId, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testGetRecordTypeByEmployeeId_InvalidFormat() {
    // 测试无效格式的员工ID
    String employeeId = "abc123";
    
    String result = SalaryRecordTypeUtil.getRecordTypeByEmployeeId(employeeId, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testGetRecordTypeByFixedSalaryRecordType_ExternalEmployee() {
    // 测试外部员工固定工资表业务类型
    String fixedSalaryRecordType = "external__c";
    
    // 测试工资条
    String salaryDataResult = SalaryRecordTypeUtil.getRecordTypeByFixedSalaryRecordType(fixedSalaryRecordType, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_EXTERNAL, salaryDataResult);
    
    // 测试工资条明细
    String salaryDetailResult = SalaryRecordTypeUtil.getRecordTypeByFixedSalaryRecordType(fixedSalaryRecordType, false);
    assertEquals(SalaryDetailDataFields.RECORD_TYPE_EXTERNAL, salaryDetailResult);
  }

  @Test
  void testGetRecordTypeByFixedSalaryRecordType_InternalEmployee() {
    // 测试内部员工固定工资表业务类型
    String fixedSalaryRecordType = "default__c";
    
    String result = SalaryRecordTypeUtil.getRecordTypeByFixedSalaryRecordType(fixedSalaryRecordType, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testGetRecordTypeByFixedSalaryRecordType_OtherType() {
    // 测试其他业务类型
    String fixedSalaryRecordType = "other__c";
    
    String result = SalaryRecordTypeUtil.getRecordTypeByFixedSalaryRecordType(fixedSalaryRecordType, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testGetRecordTypeByFixedSalaryRecordType_EmptyType() {
    // 测试空业务类型
    String fixedSalaryRecordType = "";
    
    String result = SalaryRecordTypeUtil.getRecordTypeByFixedSalaryRecordType(fixedSalaryRecordType, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testGetRecordTypeByFixedSalaryRecordType_NullType() {
    // 测试null业务类型
    String fixedSalaryRecordType = null;
    
    String result = SalaryRecordTypeUtil.getRecordTypeByFixedSalaryRecordType(fixedSalaryRecordType, true);
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testIsExternalEmployee_ExternalEmployeeIdNotBlank() {
    // 通过反射测试私有方法 isExternalEmployee
    // 这里我们通过公共方法间接测试
    String result = SalaryRecordTypeUtil.getSalaryDataRecordType("", "100000001");
    assertEquals(SalaryDataFields.RECORD_TYPE_EXTERNAL, result);
  }

  @Test
  void testIsExternalEmployee_InternalEmployeeIdNotBlank() {
    // 通过反射测试私有方法 isExternalEmployee
    // 这里我们通过公共方法间接测试
    String result = SalaryRecordTypeUtil.getSalaryDataRecordType("12345", "");
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testIsExternalEmployee_BothBlank() {
    // 通过反射测试私有方法 isExternalEmployee
    // 这里我们通过公共方法间接测试
    String result = SalaryRecordTypeUtil.getSalaryDataRecordType("", "");
    assertEquals(SalaryDataFields.RECORD_TYPE_INTERNAL, result);
  }

  @Test
  void testConstantValues() {
    // 测试常量值是否正确
    assertEquals("default__c", SalaryDataFields.RECORD_TYPE_INTERNAL);
    assertEquals("record_outer__c", SalaryDataFields.RECORD_TYPE_EXTERNAL);
    assertEquals("default__c", SalaryDetailDataFields.RECORD_TYPE_INTERNAL);
    assertEquals("record_outer__c", SalaryDetailDataFields.RECORD_TYPE_EXTERNAL);
  }

  @Test
  void testGetSalaryPaymentSlipRecordType_NullSalaryRule() {
    // 测试空工资规则
    String result = SalaryRecordTypeUtil.getSalaryPaymentSlipRecordType(null);
    assertEquals("default__c", result);
  }

  @Test
  void testGetSalaryPaymentSlipRecordType_ExternalRule() {
    // 测试外部员工工资规则（record_external__c）
    // 由于 IObjectData 接口复杂，这里主要测试逻辑分支
    // 实际使用中会传入真实的 IObjectData 对象，其 getRecordType() 返回 "record_external__c"
    String result = SalaryRecordTypeUtil.getSalaryPaymentSlipRecordType("mock_external_rule");
    assertEquals("default__c", result); // 非 IObjectData 类型默认返回内部规则类型
  }

  @Test
  void testGetSalaryPaymentSlipRecordType_InternalRule() {
    // 测试内部员工工资规则（default__c）
    // 由于 IObjectData 接口复杂，这里主要测试逻辑分支
    // 实际使用中会传入真实的 IObjectData 对象，其 getRecordType() 返回 "default__c"
    String result = SalaryRecordTypeUtil.getSalaryPaymentSlipRecordType("mock_internal_rule");
    assertEquals("default__c", result); // 非 IObjectData 类型默认返回内部规则类型
  }

  @Test
  void testGetSalaryPaymentSlipRecordType_NonIObjectDataType() {
    // 测试非 IObjectData 类型的对象
    String result = SalaryRecordTypeUtil.getSalaryPaymentSlipRecordType("not_an_iobjectdata");
    assertEquals("default__c", result);
  }

  @Test
  void testGetSalaryPaymentSlipRecordType_EmptyRule() {
    // 测试空工资规则
    String result = SalaryRecordTypeUtil.getSalaryPaymentSlipRecordType("mock_empty_rule");
    assertEquals("default__c", result);
  }




}
