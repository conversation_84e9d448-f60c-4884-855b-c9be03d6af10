<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-crm-fmcg-wq</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>fs-crm-fmcg-wq-checkins</artifactId>
    <packaging>jar</packaging>
    <properties>
<!--        <maven.compiler.source>${java.version}</maven.compiler.source>-->
<!--        <maven.compiler.target>${jdk.version}</maven.compiler.target>-->
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-wq-common</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>