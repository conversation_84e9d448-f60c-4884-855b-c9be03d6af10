{"checkins.createInterconnectRelationForSecondaryDealer": {"paramField": ["accountId"], "url": "${variables_endpoint.svc_wq_server_stage}/customProxy/business/interconnect/secondary/create"}, "nps.pushQuestionnaire": {"paramField": ["planId", "tasks"], "headerMapping": {"tenantId": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:39414/fs-support-nps/fs-support/API/Question/pushQuestionnaire"}, "approval.getInstance": {"paramField": ["instanceId"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_crm_workflow}/approval/getInstanceDetail"}, "TRS.updateFanWeiCustomer": {"paramField": [], "url": "http://************:41045/biz/contacts/updateFanWeiCustomer/${enterpriseAccount}/${contactId}/${type}"}, "log.search": {"paramField": ["apiName", "objectId", "operationalType", "pageNumber", "pageSize"], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/modifyLog/service/getNewLogInfoListForWeb"}, "fmcg.upSaveDealerSupply": {"paramField": ["object_data"], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/DealerSupplyObj/controller/UpSave"}, "custom.pay.proxy.gray": {"paramField": ["tenantId", "userId", "<PERSON><PERSON><PERSON>", "args"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:37735/fs-open-custom-pay-gray/customfunction/pay/proxy"}, "checkins.getCheckinsDataList": {"paramField": ["pageType", "pageSize", "pageNum", "startTime", "endTime", "userIds"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "http://************:60178/Export/ForOtherTeamMethods/getCheckinsDataList"}, "stage.getInstanceInfo": {"paramField": ["entityId", "objectId"], "bodyMapping": {}, "url": "http://${variables_flow.stage_rest_url}/rest/open/instance/getInstanceInfoByObjectId"}, "qixin.CSMSessionFindOrCreate": {"paramField": ["customerEa", "<PERSON><PERSON><PERSON>", "admin", "participants", "welcomeMessage"], "url": "http://************:39414/qixin-provider/cross/CSMSession/findOrCreate"}, "er.queryPublicEmployees": {"paramField": ["pageSize", "pageNumber", "srcOuterTenantId", "srcOuterUid", "destOuterTenantId", "queryType", "needFsAccountInfo", "searchText", "needDestRange", "mapperStatus", "limit", "offset"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/queryPublicEmployees"}, "sms.sendSms": {"paramField": ["phone", "businessId", "param"], "allowTenantId": [1], "url": "http://************:63634/sms/api/json/send"}, "fmcg.tpm.budget_operator.writeOff": {"paramField": ["api_name", "data_id", "write_off_data"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:30752/FMCG/InnerAPI/TPM/CommonBudget/WriteOff"}, "syncdata.getSyncDataMapping": {"paramField": ["sourceTenantId", "sourceObjectApiName", "sourceDataId", "destTenantId", "destObjectApiName"], "headerMapping": {"tenantId": "X-fs-Enterprise-Id", "userId": "X-fs-Employee-Id"}, "bodyMapping": {}, "url": "http://************:56749/outrest/syncDataMapping/getSyncDataMapping"}, "custom.OrionDev.synpersonne.sync": {"paramField": [], "url": "http://*************:8090/job/synpersonne"}, "er.getPublicRelationOwnerOuterUid": {"paramField": ["upstreamTenantId", "downstreamOuterTenantId"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/getPublicRelationOwnerOuterUid", "bodyMapping": {"tenantIdFieldName": "upstreamTenantId"}}, "custom.Orion.synproduct.sync": {"paramField": [], "url": "http://************:8080/sync/product"}, "available_range.get_real_price": {"paramField": ["accountId", "partnerId", "productIdList", "fullProductList"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/available_range/service/get_real_price"}, "industry.checkoutIndustryByName": {"paramField": ["companyName"], "headerMapping": {"tenantIdFieldName": "X-fs-Enterprise-Id", "userIdFieldName": "X-fs-Employee-Id"}, "url": "http://${variables_endpoint.dns_industry}/industry/industryFcpService/checkoutIndustryByName"}, "assgin.queryGroup": {"paramField": ["productCodes", "dealerCode", "currentDate", "tenantId", "sendStatus"], "bodyMapping": {}, "url": "http://************:32734/assign/queryGroup"}, "feed.getRangeInfoByUUID": {"paramField": ["uuid"], "headerMapping": {"tenantIdFieldName": "x-fs-enterprise-id", "userIdFieldName": "x-fs-employee-id"}, "bodyMapping": {}, "url": "http://${variables_plat.feed_apl_url}/FeedInfo/getRangeInfoByUUID"}, "kekai.printPdf.pdfWithAttachment": {"paramField": ["tenantId", "userId", "dataId", "dataApiName", "enterpriseAccount", "printTemplateId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:30611/fs-tuosida-print/print/pdfWithAttachment"}, "nps.deleteTimer": {"paramField": ["planId", "taskId", "tenantId", "pushTimestamps"], "headerMapping": {"tenantId": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:39414/fs-support-nps/fs-support/API/Question/deleteTimer"}, "sfa.available.getAvailablePriceBookList": {"paramField": ["accountId", "partnerId", "enableIsValidorder"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/available_range/service/get_available_price_book_list"}, "help.deleteDocuments": {"paramField": ["dataIds"], "bodyMapping": {}, "url": "http://************:39414/fs-support-help/fs-support/API/Help/deleteDocuments"}, "kekai.print.billSign": {"paramField": ["billPrintList"], "url": "http://************:33505/fs-open-custom-print/kekai/print/billSign"}, "eservice.proxy": {"paramField": ["tenantId", "userId", "<PERSON><PERSON><PERSON>", "args"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservicefunction/proxy"}, "userprivilege.getUsersByRoleCodes": {"paramField": ["roleCodes"], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/userprivilege/service/getUsersByRoleCodes"}, "bpm.start": {"paramField": ["sourceWorkflowId", "objectId", "entityId", "source"], "headerMapping": {"tenantIdFieldName": "x-tenant-id"}, "bodyMapping": {}, "url": "http://${variables_endpoint.svc_bpm_biz}/plugins/instance/start"}, "openapi.npath2MediaId": {"paramField": ["npathIds", "appId"], "url": "http://${variables_endpoint.svc_open_api}/inner/file/npath2MediaId"}, "userprivilege.deleteByUserIds": {"paramField": ["roleCode", "userIds"], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/userprivilege/service/deleteByUserIds"}, "i18n.syncData": {"paramField": ["loc"], "url": "http://oss.firstshare.cn/i18n-console/api/crm/sync"}, "custom.pay.proxy": {"paramField": ["tenantId", "userId", "<PERSON><PERSON><PERSON>", "args"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-pay/customfunction/pay/proxy"}, "sfa.accountAddr.setDefault": {"paramField": ["location_id", "account_id"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/AccountAddrObj/action/SetDefault"}, "privilege.userRole": {"paramField": ["enterpriseId", "employeeId", "appId"], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://organizationadpater.nsvc.foneshare.cn/PermissionService/getRolesByEmployeeAndAppId"}, "routes.routeDataExport": {"paramField": ["routeName", "userIdList", "deptIdList", "areaId"], "url": "http://************:30070/route_data_proxy/export/${sendToUserId}"}, "industry.getCompanyByName": {"paramField": ["KeyWord"], "url": "http://${variables_endpoint.dns_industry}/industry/industryFcpService/getCompanyByName"}, "checkins.changeRouteByOwner": {"url": "${variables_endpoint.svc_wq_server_stage}/customProxy/changeRouteByChangeOwner/${tenantId}/${accountId}/${owner}/${flag}"}, "kekai.printPdf.getPrintData": {"paramField": ["tenantId", "userId", "dataId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-print/kekai/printPdf/getPrintData"}, "feed.getFeedIdByUUID": {"paramField": ["uuid"], "headerMapping": {"tenantIdFieldName": "x-fs-enterprise-id", "userIdFieldName": "x-fs-employee-id"}, "bodyMapping": {}, "url": "http://${variables_plat.feed_apl_url}/FsFeed/getFeedIdByUUID"}, "stock.initInterconnectionOuter": {"paramField": ["upstreamEa", "downstreamEa"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://${variables_endpoint.dns_ncrm}/API/v1/inner/object/interconnection_plugin/service/init_interconnection_outer"}, "custom.OrionDev.synpersonneldepartment.sync": {"paramField": [], "url": "http://*************:8090/job/synpersonneldepartment"}, "fmcg.tpm.agreement.abandon": {"paramField": ["object_data_id"], "headerMapping": {"X-fs-Employee-Id": "X-fs-Employee-Id", "X-fs-Enterprise-Id": "X-fs-Enterprise-Id"}, "bodyMapping": {}, "url": "http://************:41933/API/v1/object/TPMActivityAgreementObj/controller/Abandon"}, "erp.syncData.updateSyncDataMapping": {"paramField": ["sourceObjectApiName", "sourceDataId", "destObjectApiName", "destDataId", "remark"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "http://${variables_endpoint.Erp_SyncData_Visit_Address}/inner/erp/syncdata/customfunction/SyncDataMapping/updateSyncDataMapping", "bodyMapping": {}}, "checkins.getQRCode": {"paramField": [], "url": "http://************:43152/Export/outer/dealAccountInfo/${accountId}/${eId}/${userId}"}, "template.findPrintTemplate": {"paramField": ["objectApiName", "pageNumber", "pageSize"], "headerMapping": {"tenantIdFieldName": "x-tenant-id", "userIdFieldName": "x-user-id"}, "url": "http://************:63524/crm_template/rest/template/findPrintTemplate"}, "enterprise.changeEnterpriseSource": {"paramField": ["enterpriseId", "source"], "allowTenantId": [1], "url": "http://usercenter.nsvc.foneshare.cn/uc-provider/EnterpriseEditionService/changeEnterpriseSource"}, "industry.getCompanyByCreditCode": {"paramField": ["credit_code"], "headerMapping": {"tenantIdFieldName": "X-fs-Enterprise-Id", "userIdFieldName": "X-fs-Employee-Id"}, "url": "http://${variables_endpoint.dns_industry}/industry/industryFcpService/getCompanyByCreditCode"}, "eolinker.syncTestCase": {"paramField": ["project_id", "service_id", "project_name"], "url": "http://oss.foneshare.cn/qat2/eolinker/syncTestCase"}, "account.enterAccount": {"paramField": ["objectDataId", "args"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/${describe_api_name}/action/EnterAccount"}, "sfa.pircePolicy.match": {"paramField": ["requestId", "accountId", "masterObjectApiName", "masterData", "detailDataMap", "modifiedFieldApiNames", "modifiedDataIndexList", "matchType", "batchNo", "removeGroupKeySet", "exactlyMatchModifiedData"], "replaceUserIdHeader": false, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/price_policy/service/match"}, "metadata.execJava": {"paramField": ["branch", "gitUrl", "cases", "serviceName", "job<PERSON>ame", "serviceNo", "runType"], "allowTenantId": [683661, 1, 750076], "url": "http://oss.foneshare.cn/qat2/ci/task/execJava"}, "userprivilege.getRolesByUsers": {"paramField": ["appIds", "roleSource", "userIds"], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/userprivilege/service/getRolesByUsers"}, "callcenter.crmcall": {"paramField": ["tenantId", "userId", "customerNum", "objApiName", "objDataId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/open/callcenter/common/crm/call"}, "er.batchDeleteSourcePublicEmployees": {"paramField": ["downstreamOuterUids"], "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/batchDeleteSourcePublicEmployees", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa"}}, "currency.findCurrencyByCode": {"paramField": ["currencyCode"], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/currency/service/find_currency_by_code"}, "yq.budget_operator.writeOff": {"paramField": ["write_off_object_api_name", "write_off_object_data_id", "biz_object_api_name", "biz_object_data_id", "close", "exclude_account_ids", "data"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:30752/FMCG/InnerAPI/TPM/CustomBudget/WriteOff"}, "industry.getCompanyLabelByNames": {"paramField": ["companyName"], "headerMapping": {"tenantIdFieldName": "X-fs-Enterprise-Id", "userIdFieldName": "X-fs-Employee-Id"}, "url": "http://${variables_endpoint.dns_industry}/industry/industryFunction/getCompanyLabelByNames"}, "feedback.updateRecord": {"paramField": ["masterObjDataId", "subObjDataId", "tenantId", "employeeId", "content"], "replaceHeaders": false, "bodyMapping": {}, "url": "http://************:39414/fs-support-feedback/fs-support/API/Feedback/updateRecord"}, "custom.Orion.syndepartment.sync.test": {"paramField": [], "url": "http://************:8090/job/syndepartment"}, "zihaiguo.adduceCheckinsEffective": {"paramField": [], "url": "http://************:63669/biz/v1/checkins/adduceCheckinsEffective/${checkinsId}"}, "feed.searchFeedList.topic": {"paramField": ["filterType", "searchArg"], "headerMapping": {"tenantIdFieldName": "x-fs-enterprise-id", "userIdFieldName": "x-fs-employee-id", "enterpriseAccount": "x-fs-enterprise-account"}, "bodyMapping": {}, "url": "http://${variables_plat.feed_apl_url}/Feeds/searchFeedList/topic"}, "Nestle.forecast.router": {"paramField": ["type", "data"], "url": "http://************:46018/fs-fmcg-customized-nestle-biz/Nestle/forecast/v1/${router}"}, "crm.crmOrderToDetail": {"paramField": ["orderId"], "url": "http://************:36337/versionRegisterService/crmOrderToDetail"}, "customeraccount.queryByCustomerId": {"paramField": ["customerId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/inner/object/customer_account/service/get_by_customer_id"}, "funcTemplate.save": {"paramField": ["id", "name", "type", "description", "content"], "replaceHeaders": false, "url": "http://************:34567/template/API/functionTemplate/save"}, "yuanqi.findRouteInfo": {"paramField": ["routeId", "checkinsId", "masterId", "planDate"], "url": "http://************:63669/biz/v1/route/findRouteInfo"}, "usergroupmember.insert": {"paramField": ["groupId", "userIds"], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/usergroupMember/service/insert"}, "sanbox.dc.updateCancleState": {"paramField": ["<PERSON><PERSON><PERSON><PERSON>", "appId", "SCUST_CD", "SALE_DT"], "bodyMapping": {}, "url": "http://************:8088/open_api/crm/dc/updateCancleState"}, "esign.flow.cancel": {"paramField": ["dataId"], "url": "http://************:56602/sign/cancel"}, "marketing.shareXiaotongAccessToken": {"paramField": [], "headerMapping": {"tenantIdFieldName": "x-fs-ei", "userIdFieldName": "x-fs-userinfo"}, "url": "https://${variables_endpoint.http_domain}/marketing/xiaoetong/shareXiaotongAccessToken"}, "checkoffice.saveHoliday": {"paramField": ["id", "typeId", "typeName", "ea", "userId", "startTime", "endTime", "hours", "days", "status"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "${variables_endpoint.svc_kaoqin_server}/approval/saveHoliday"}, "obj.button.call.faratronic.button_T5iJ5__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/object_MvtI8__c/action/button_T5iJ5__c"}, "kekai.payment.addOnlinePayment": {"paramField": ["tenantId", "userId", "payer", "owner", "paymentPlans"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-pay/payment/addOnlinePayment"}, "obj.button.call.706854_sandbox.button_wXZWe__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/QuoteObj/action/button_wXZWe__c"}, "fmcg.tpm.budget_operator.writeOffValidate": {"paramField": ["api_name", "data_id", "write_off_data"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:30752/FMCG/InnerAPI/TPM/CommonBudget/WriteOffValidate"}, "sfa.account.move": {"paramField": ["objectIDs", "args", "objectPoolId", "skipTriggerApprovalFlow", "skipFunctionCheck"], "headerMapping": {"tenantIdFieldName": "X-fs-Enterprise-Id", "userIdFieldName": "X-fs-Employee-Id"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/object/AccountObj/action/Move", "bodyMapping": {}}, "fmcg.findAccountRecentLocation": {"url": "http://************:45080/findbug/findAccountRecentLocation/${ea}/${userId}/${accountId}"}, "feedback.syncStory": {"paramField": ["tapdStoryId", "tapdWorkspaceId", "title", "description"], "allowTenantId": [687889, 1], "url": "http://************:32674/TAPDHelper/CRM/SyncStory"}, "er.getEaByOuterTenantId": {"paramField": ["outerTenantId"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/fxiaokeAccount/getEaByOuterTenantId"}, "department.bulkResume": {"paramField": ["dataIds"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/DepartmentObj/action/BulkResume"}, "feedback.updateStatus": {"paramField": ["masterObjDataId", "subObjDataId", "tenantId", "employeeId", "status"], "replaceHeaders": false, "bodyMapping": {}, "url": "http://************:39414/fs-support-feedback/fs-support/API/Feedback/updateStatus"}, "fmcg.tpm.budget_operator.freezeValidate": {"paramField": ["freeze_data"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:30752/FMCG/InnerAPI/TPM/CommonBudget/FreezeValidate"}, "obj.button.call": {"paramField": ["objectDataId", "args"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/${objectApiName}/action/${buttonApiName}"}, "sfa.account.remove": {"paramField": ["objectIDs", "objectPoolId", "owner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/AccountObj/action/Remove"}, "weex.jenkinsBuild": {"paramField": ["job<PERSON>ame", "objectDataId", "buildParams"], "url": "http://112.firstshare.cn/weex-console/api/jenkins/build"}, "er.getMapperId": {"paramField": ["upstreamEa", "downstreamOuterTenantId", "downstreamOuterUid"], "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/getMapperId", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa"}}, "stock.checkDeliveryRealStock": {"paramField": ["deliveryNoteDataId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://${variables_endpoint.dns_ncrm}/API/v1/inner/object/delivery_note/service/check_delivery_real_stock"}, "eolinker.searchReport": {"paramField": ["space_id", "project_id", "report_type", "start_time", "end_time"], "url": "http://oss.foneshare.cn/qat2/eolinker/searchReport"}, "eservice.proxyGetHttp": {"paramField": ["tenantId", "userId", "url"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/worldmap/proxyGetHttpByFunction"}, "object.modifyLogs": {"paramField": ["apiName", "objectId", "operationalType", "pageNumber", "pageSize"], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/modifyLog/service/getNewLogInfoListForWeb"}, "buginfo.count": {"paramField": ["query"], "allowTenantId": [1, 158833, 750076], "url": "http://************:9200/fs-bug-info/_count"}, "object.merge": {"paramField": ["objectApiName", "targetDataId", "sourceDataIds", "objectData", "needMergeRelationObjects", "source"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/${describe_api_name}/action/Merge"}, "eservice.countDistanceByWorldMap": {"paramField": ["tenantId", "userId", "accountKey", "originLon", "originLat", "destinationLon", "destinationLat", "strategy", "mapCountType"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/worldmap/countDistanceByWorldMapByFunction"}, "Fx.sign.addEmployees": {"paramField": ["addEmployeeIds", "enterpriseAccount", "enterpriseId", "employeeId", "signAppType"], "headerMapping": {"tenantId": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:39414/fs-paas-sign-provider/customerVendor/addEmployee"}, "eservice.submitReceiveMaterialBill": {"paramField": ["tenantId", "userId", "id", "recordType"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/accessory/casesAccessory/submitReceiveMaterialBill"}, "TRS.sendWXMessage": {"paramField": ["title", "contents", "outerTenantId", "outerUserId", "linkAppId", "linkAppType", "tenantId", "outerRoleIds", "wxServiceAppIds", "message", "messageType", "channels", "forwardUrl", "templateIdForWechatServiceMsg", "accountId", "responsibility"], "url": "http://************:41045/biz/contacts/sendWXMessage"}, "kpi-cep.count": {"paramField": ["query"], "allowTenantId": [1, 158833, 750076], "url": "http://************:9200/kpi-cep/_count"}, "userprivilege.batchSetUserRoles": {"paramField": ["roleCodes", "updateMajorRole", "majorRole", "userIds"], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/userprivilege/service/batchSetUserRoles"}, "object.bulkDelete": {"paramField": ["describe_api_name", "idList", "objectAPIName"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/${describe_api_name}/action/BulkDelete"}, "feedback.updateFeedback": {"paramField": ["objDataId", "tenantId", "employeeId", "releaseVersion", "status"], "replaceHeaders": false, "bodyMapping": {}, "url": "http://************:39414/fs-support-feedback/fs-support/API/Feedback/updateFeedback"}, "area.getParentZone": {"paramField": ["value"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/global_data/service/get_parent_zone"}, "checkoffice.getleaveInfo": {"paramField": ["isIncludeStop", "ea", "inquireDate", "empIdList", "departIdList"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "${variables_endpoint.svc_kaoqin_server}/holiday/getAllEmpLeaveInfo"}, "eservice.confirmReceiveMaterial": {"paramField": ["tenantId", "userId", "receiveMaterialBillId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/accessory/casesAccessory/confirmReceiveMaterial"}, "er.getOuterTenantIdByEa": {"paramField": ["ea", "ei"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "http://${variables_endpoint.svc_er_biz}/outapi/fxiaokeAccount/getOuterTenantIdByEa"}, "weex.getHistory": {"paramField": ["gray<PERSON><PERSON>", "bundleName"], "url": "http://************:48805/weex-console/api/get/history"}, "sfa.get_object_limit_info": {"paramField": ["objectApiName"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/object_limit_service/service/get_object_limit_info"}, "usergroupmember.list": {"paramField": ["groupId"], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/usergroupMember/service/list"}, "feed.searchFeedListData": {"paramField": ["filterType", "searchArg"], "headerMapping": {"tenantIdFieldName": "x-fs-enterprise-id", "userIdFieldName": "x-fs-employee-id", "enterpriseAccount": "x-fs-enterprise-account"}, "bodyMapping": {}, "url": "http://${variables_plat.feed_apl_url}/FeedParticular/searchFeedListParticular"}, "bpm.instances": {"paramField": ["entityId", "objectId", "state"], "bodyMapping": {}, "url": "http://${variables_flow.bpm_rest_url}/plugins/instance/list/data"}, "template.sendEmail": {"paramField": ["userid_list", "email_list", "template_id", "obj_data_id", "obj_api_name"], "headerMapping": {"tenantIdFieldName": "x-tenant-id", "userIdFieldName": "x-user-id"}, "url": "http://crmtemplate.nsvc.foneshare.cn/crm_template/rest/v1/email/send"}, "checkins.dealSzLogicInAccount": {"url": "${variables_endpoint.svc_wq_server}/customProxy/dealSzLogicInAccount/${tenantId}/${szId}/${apiName}/${accountId}"}, "checkins.queryTodayRouteAccount": {"url": "${variables_endpoint.svc_wq_server_vip}/customProxy/queryTodayRouteAccount/${tenantId}/${accountId}"}, "eservice.confirmReMaterial": {"paramField": ["tenantId", "userId", "refundMaterialBillId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/accessory/casesAccessory/confirmReMaterial"}, "enterprisepay.querySubAccountsFilter": {"paramField": ["arg1"], "url": "http://************:53094/com.facishare.pay.enterprise.service.EnterpriseSubAccountService/querySubAccountsFilter/1.0"}, "fmcg.delSupply": {"paramField": ["supplyId"], "url": "http://************:43259/API/v1/rest/object/DealerSupplyObj/controller/DelBySupplyId"}, "erp.syncData.createErpfieldmapping": {"paramField": ["dataCenterId", "channel", "dataType", "fsDataId", "fsDataName", "erpDataId", "erpDataName"], "url": "http://${variables_endpoint.Erp_SyncData_Visit_Address}/inner/erp/syncdata/customfunction/ErpFieldMapping/create", "bodyMapping": {}}, "fmcg.resetAccountRoutes": {"url": "http://************:45080/dealData/fixAccountRoutes/${tenantId}/${accountId}"}, "eservice.send.email": {"paramField": ["arg1", "arg2", "arg3", "arg4"], "bodyMapping": {"enterpriseAccountFieldName": "arg1", "userIdFieldName": "arg3"}, "url": "http://${variables_endpoint.svc_email_service}/com.facishare.open.emailproxy.task.api.service.BpmEmailService/sendEmailByFunction/1.0"}, "ekuaibao.updateRelevantTeam": {"paramField": ["dataId"], "url": "http://************:35065/sync/project/relevant/team"}, "yq.device_photo_object.add": {"paramField": ["object_data", "details", "detect_result"], "headerMapping": {"x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:33670/API/inner/v1/custom/yq/device_photo/add"}, "eservice.createDevicePlanCases": {"paramField": ["tenantId", "userId", "devicePlanDetailId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/inspectionData/createDevicePlanCases"}, "function.enableDeveloperVerification": {"paramField": ["tenantId"], "allowTenantId": [1, 590064, 683750], "url": "http://************:57320/API/v1/inner/object/function/service/enableDeveloperVerification"}, "openapi.openUserId2FsId": {"paramField": ["openUserIds", "appId"], "url": "http://************:41494/inner/openuser/openUserId2FsId"}, "checkins.getLocationAreaCode": {"paramField": ["location", "accountId", "ea", "flag"], "url": "${variables_endpoint.svc_wq_server}/customProxy/getLocationAreaCode"}, "shahe.kekai.print.billSign": {"paramField": ["tenantId", "userId", "billPrintList"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:37735/fs-open-custom-print-gray/kekai/print/billSign"}, "nps.syncPlan": {"paramField": ["name", "planObjId", "status", "questionnaireId"], "headerMapping": {"tenantId": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:39414/fs-support-nps/fs-support/API/Question/syncPlan"}, "fs.checkEA": {"paramField": ["enterpriseAccount"], "url": "http://************:36337/versionRegisterService/checkIsValidEnterpriseAccount"}, "hospital.batchGetHospitalIdByNames": {"paramField": ["hospitalNames"], "url": "http://************:41501/spider-union/outapi/hospital/batchGetHospitalIdByNames"}, "eservice.findNearestArea": {"paramField": ["tenantId", "userId", "address", "postCode", "longitude", "latitude", "mapType"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/worldmap/findNearestAreaByFunction"}, "obj.button.call.asiainfo123.button_c12Y0__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/LeadsObj/action/button_c12Y0__c"}, "openapi.fsId2OpenUserId": {"paramField": ["employeeIds", "appId"], "url": "http://************:41494/inner/openuser/fsId2OpenUserId"}, "stage.regenerateHandler": {"paramField": ["stageTaskId", "opinion"], "bodyMapping": {}, "url": "http://${variables_flow.stage_rest_url}/rest/open/task/regenerateHandlerByStageTaskId"}, "custom.print.proxy": {"paramField": ["tenantId", "userId", "<PERSON><PERSON><PERSON>", "args"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-print/customfunction/print/proxy"}, "custom.OrionDev.synproduct.sync": {"paramField": [], "url": "http://*************:8090/sync/product"}, "kekai.common.getExpireDate": {"paramField": ["tenantId", "userId", "createTime", "dateType", "number"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-pay/common/getExpireDate"}, "eolinker.clearCoverRate": {"paramField": ["service_name"], "url": "http://oss.foneshare.cn/qat2/eolinker/clearCoverRate"}, "er.listDownstreamOuterAccountsByPhone": {"paramField": ["phone"], "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/listDownstreamOuterAccountsByPhone", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa"}}, "kadc.getTryCount": {"paramField": ["<PERSON><PERSON><PERSON><PERSON>", "appId", "CUST_CD", "SALE_DT", "POST_STR_DT", "POST_END_DT", "dataId"], "bodyMapping": {}, "url": "http://************:8089/open_api/crm/dc2/getTryCount"}, "checkins.getLocationCodeV2": {"url": "http://************:45080/custom/getLocationCodeV2/${lat}/${lon}/"}, "checkins.editExtFields": {"paramField": ["userId", "checkinsId", "addExtFields"], "url": "${variables_endpoint.svc_wq_server}/checkinsUpdate/editExtFields/${tenantId}"}, "account.cancelEntry": {"paramField": ["objectDataId"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/${describe_api_name}/action/CancelEntry"}, "send.sendSms": {"paramField": ["channelType", "type", "templateName", "templateContent", "ea", "userId", "phoneDataList"], "url": "http://${variables_marketing.send_sendSms}/marketing/send/sendSms"}, "fmcg.delSupplyByAccount": {"paramField": ["account_id"], "url": "http://************:30752/API/v1/object/DealerSupplyObj/controller/DelAccount"}, "eolinker.getStatisticsByDate": {"paramField": ["project_id", "start_date", "end_date"], "url": "http://oss.foneshare.cn/qat2/eolinker/getIncCasesByDate"}, "yq.budget_operator.unfreeze": {"paramField": ["object_api_name", "data_id", "data"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:30752/FMCG/InnerAPI/TPM/CustomBudget/Unfreeze"}, "file.getShareToken": {"paramField": ["nPath"], "url": "http://************:50276/share/getInnerToken?path=${nPath}&ea=${enterpriseAccount}", "bodyMapping": {"enterpriseAccountFieldName": "ea"}}, "checkins.updateNumberFormAction": {"paramField": ["checkId", "sourceActionId", "objectApiName", "objDataId", "images", "formData", "productMap", "spuMap"], "url": "http://************:45505/Export/customProxy/updateNumberFormAction"}, "bom.syncToOtherNode": {"paramField": ["node", "same"], "bodyMapping": {}, "url": "http://${variables_endpoint.dns_crm_sfa}/API/v1/inner/object/bom/service/syncToOtherNode"}, "sanbox.kadc.getTryCount": {"paramField": ["<PERSON><PERSON><PERSON><PERSON>", "appId", "CUST_CD", "SALE_DT", "POST_STR_DT", "POST_END_DT", "dataId"], "bodyMapping": {}, "url": "http://************:8088/open_api/crm/dc2/getTryCount"}, "custom.OrionProd.synproduct.sync": {"paramField": [], "url": "http://*************:8080/sync/product"}, "industry.saveCustomIndustry": {"paramField": ["companyName", "address", "registNo", "operName", "mobile", "phoneNumber", "started", "url", "province", "termStart", "creditCode", "scope", "registCapi", "registCapiDesc", "belongRrg", "checked", "updated", "email", "econKind", "status", "industryName"], "headerMapping": {"tenantIdFieldName": "X-fs-Enterprise-Id", "userIdFieldName": "X-fs-Employee-Id"}, "url": "http://${variables_endpoint.dns_industry}/industry/industryFcpService/saveCustomIndustry"}, "geo.location": {"paramField": ["address"], "url": "http://${variables_endpoint.svc_geo_location}/geocode/dec?address=${address}"}, "userprivilege.getUsersByRoleCodesForFs": {"paramField": ["roleCodes"], "replaceHeaders": false, "allowTenantId": [1], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/userprivilege/service/getUsersByRoleCodes"}, "message.addRemindRecord": {"paramField": ["remindRecordItem"], "headerMapping": {}, "bodyMapping": {"tenantIdFieldName": "ei"}, "url": "http://message.nsvc.foneshare.cn/fs-message/api/v2/addRemindRecord"}, "object.findBySearchTemplateQuery": {"paramField": ["search_query_info", "need_return_count_num", "fill_field_info"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/${objectApiName}/controller/FindBySearchTemplateQuery"}, "yuanqi.getImageExt": {"paramField": ["path"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "http://************:63669/biz/v1/customProxy/getImageExt"}, "function.find": {"paramField": ["api_name", "binding_object_api_name"], "replaceHeaders": false, "allowTenantId": [1, 590056, 590057], "url": "http://************:30393/API/v1/inner/object/function/service/find"}, "er.listPublicEmployeesByDestOuterTenantId": {"paramField": ["sourceOuterTenantId", "destOuterTenantId"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/listPublicEmployeesByDestOuterTenantId"}, "schedule.cancel": {"paramField": ["platform", "data"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "http://schedule.nsvc.foneshare.cn/schedule/cancel"}, "sfa.accountFinInfo.setDefault": {"paramField": ["invoice_id", "account_id"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/AccountFinInfoObj/action/SetDefault"}, "eservice.assign": {"paramField": ["tenantId", "fsUserId", "workOrderInstanceId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "fsUserId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/workorder/reAssignWorkOrderByFunction"}, "erp.syncData.getSyncDataMappingBySourceDataId": {"paramField": ["destDataId", "sourceObjectApiName", "sourceDataId", "destObjectApiName"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "http://${variables_endpoint.Erp_SyncData_Visit_Address}/inner/erp/syncdata/customfunction/SyncDataMapping/getSyncDataMappingBySourceDataId", "bodyMapping": {}}, "custom.Orion.synpersonneldepartment.sync": {"paramField": [], "url": "http://************:8080/job/synpersonneldepartment"}, "bpm.cancel": {"paramField": ["instanceId", "reason"], "bodyMapping": {}, "url": "http://${variables_flow.bpm_rest_url}/plugins/instance/cancel"}, "mutipleUnit.calcPriceByUnit": {"paramField": ["params"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/mutipleUnit/service/calcPriceByUnit"}, "er.listByWxAppIdsAndOuterUIds": {"paramField": ["wxAppIds", "outerUIds"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/wxservice/listByWxAppIdsAndOuterUIds", "bodyMapping": {"tenantIdFieldName": "upstreamTenantId"}}, "custom.other.proxy.gray": {"paramField": ["tenantId", "userId", "<PERSON><PERSON><PERSON>", "args"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:37735/fs-open-custom-other-gray/customfunction/other/proxy"}, "stock.ops.enableSpecialStockOperation": {"paramField": ["targetTenantId", "opType"], "bodyMapping": {}, "url": "http://${variables_endpoint.dns_ncrm}/API/v1/inner/object/stock_ops/service/enableSpecialStockOperation"}, "Nestle.forecast.gray.router": {"paramField": ["type", "data"], "url": "http://************:34841/fs-fmcg-customized-nestle-biz/Nestle/forecast/v1/${router}"}, "object.changePartnerOwner": {"paramField": ["dataIds", "describe_api_name", "ownerId"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/${describe_api_name}/action/ChangePartnerOwner"}, "custom.OrionDev.syndepartment.sync": {"paramField": [], "url": "http://*************:8090/job/syndepartment"}, "business.findproject": {"paramField": ["evidenceValue", "isAttachment", "beginLocation", "endLocation", "businessName", "locationName", "openUserId"], "url": "http://************:34243/fs-oupu-connector/project/distanceCal"}, "eservice.completeFeeSettle": {"paramField": ["tenantId", "userId", "feeSettlementBillInstanceId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/feeSettlement/completeFeeSettleByFunction"}, "kadc.updateSaveDc": {"paramField": ["<PERSON><PERSON><PERSON><PERSON>", "appId", "CUST_CD", "SALE_DT", "POST_STR_DT", "POST_END_DT"], "bodyMapping": {}, "url": "http://************:8089/open_api/crm/dc2/updateSave"}, "custom.OrionProd.synpersonne.sync": {"paramField": [], "url": "http://*************:8080/job/synpersonne"}, "er.batchRemoveOutAppRoleAssigns4Function": {"paramField": ["downstreamOuterUids", "roleId", "linkAppId"], "url": "${variables_endpoint.dns_er}/outapi/linkApp/batchRemoveOutAppRoleAssigns4Function", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa"}}, "ekuaibao.update": {"paramField": ["dataId"], "url": "http://************:35065/update/project"}, "custom.OrionProd.syndepartment.sync": {"paramField": [], "url": "http://*************:8080/job/syndepartment"}, "fmcg.custom.yl.agreement_enable_filter": {"paramField": ["account_id", "user_id"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "allowTenantId": [721787], "bodyMapping": {}, "url": "http://************:34271/API/v1/inner/object/TPMActivityObj/controller/YinLuAgreementEnableFilter"}, "checkins.getDefaultAccount": {"url": "${variables_endpoint.svc_wq_server}/plan/getDefultAccountDta/${tenantId}/${userId}"}, "erp.syncData.executeCustomFunction": {"paramField": ["type", "params"], "headerMapping": {"x-fs-ei": "x-fs-ei"}, "url": "http://${variables_endpoint.Erp_SyncData_Visit_Address}/inner/erp/syncdata/customfunction/common/execute", "bodyMapping": {}}, "fmcg.sales.querySuggestProductList": {"paramField": ["customerId", "productIdList"], "bodyMapping": {}, "url": "http://************:45334/product/query_suggest_product"}, "hospital.batchUpdateAccountObjByHospitalIds": {"paramField": ["accountIdAndHospitalIds", "updateFieldApiNames"], "url": "http://************:41501/spider-union/outapi/hospital/batchUpdateAccountObjByHospitalIds"}, "eservice.refundMaterialBillTurnDown": {"paramField": ["tenantId", "userId", "id", "reason"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/accessory/casesAccessory/refundMaterialBillTurnDown"}, "checkins.syncAIFinishStatusByTask": {"url": "http://************:45080/syncData/syncAIFinishStatusByTask/${tenantId}/${checkId}"}, "business.matchCondition": {"paramField": ["evidenceValue", "isAttachment", "beginLocation", "endLocation", "businessName", "locationName", "openUserId"], "url": "http://************:31674/fs-keshun-connector/project/distanceCal"}, "eservice.turnDown": {"paramField": ["tenantId", "userId", "id", "reason"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/accessory/casesAccessory/turnDown"}, "custom.Orion.syndepartment.sync": {"paramField": [], "url": "http://************:8080/job/syndepartment"}, "function.query": {"paramField": ["binding_object_api_name", "api_name", "pageSize", "pageNumber"], "replaceHeaders": false, "allowTenantId": [1, 590056, 590057], "url": "http://************:30393/v1/function/query"}, "obj.button.call.asiainfo123.button_lXcnK__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/object_oq1Lo__c/action/button_lXcnK__c"}, "enterprisepay.queryEACollectPayOrder": {"paramField": ["arg1"], "url": "http://************:59927/com.facishare.pay.toB.pay.service.EAPayOrderQueryService/queryEACollectPayOrder/1.0"}, "checkins.getBackFillFieldValue": {"paramField": ["ea", "sourceActionId", "accountId", "checkInId", "userId"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "http://************:45505/Export/customProxy/getBackFillFieldValue"}, "feedback.insertFeedback": {"paramField": ["objectId", "casesObjNumber", "tenantId", "employeeId", "type", "status", "module", "description", "submitDeviceType", "version", "deviceModel", "releaseVersion", "happenedTime", "images", "deviceTypeList"], "replaceHeaders": false, "bodyMapping": {}, "url": "http://************:39414/fs-support-feedback/fs-support/API/Feedback/insertFeedback"}, "usergroupmember.update": {"paramField": ["groupId", "employeeIds"], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/usergroupMember/service/update"}, "industry.getCompanyStaffsByNames": {"paramField": ["companyName"], "headerMapping": {"tenantIdFieldName": "X-fs-Enterprise-Id", "userIdFieldName": "X-fs-Employee-Id"}, "url": "http://${variables_endpoint.dns_industry}/industry/industryFunction/getCompanyStaffsByNames"}, "checkins.queryExecuteRouteByOwner": {"url": "http://************:45505/Export/customProxy/queryExecuteRouteByOwner/${tenantId}/${owner}/${date}"}, "checkoffice.getleaveByFeedIds": {"paramField": ["feedIds", "ea"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "${variables_endpoint.svc_kaoqin_server}/holiday/batchGetEmpApproveIsPass"}, "kekai.pay.getEncryptExpireDate": {"paramField": ["createTime"], "url": "http://************:60125/fs-pay-kekai/pay/getEncryptExpireDate"}, "gaodun.bi.getBiAndLabel": {"paramField": ["id", "flagCode", "pageSize", "pageNumber", "refresh", "filterList"], "headerMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "uId"}, "url": "http://************:35595/common/gaodun/getBiAndLabel"}, "object.batchCalculate": {"paramField": ["calculateFieldApiNames", "calculateFields", "detailDataMap", "masterData", "masterObjectApiName"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/calculate/service/batchCalculate"}, "checkins.dealAddAccountInfo": {"url": "${variables_endpoint.svc_wq_server}/customProxy/dealAddAccountInfo/${accountId}/${tenantId}"}, "fmcg.queryDealerProductGroup": {"paramField": ["shopId", "productIdList"], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/DealerSupplyObj/controller/ShopQuery"}, "dc.updateCancleState": {"paramField": ["<PERSON><PERSON><PERSON><PERSON>", "appId", "SCUST_CD", "SALE_DT"], "bodyMapping": {}, "url": "http://************:8089/open_api/crm/dc/updateCancleState"}, "yq.budget_operator.freeze": {"paramField": ["object_api_name", "data_id", "data"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:30752/FMCG/InnerAPI/TPM/CustomBudget/Freeze"}, "fmcg.tpm.budget_operator.close": {"paramField": ["api_name", "data_id"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:30752/FMCG/InnerAPI/TPM/CommonBudget/Close"}, "feedback.addBug": {"paramField": ["appVersion", "clientName", "description", "title", "clientType", "customerContact", "current<PERSON>wner", "startTime", "userId", "objectId", "entityId", "owner", "severity", "images", "operate", "module", "team", "created_by", "userId", "customerLevel", "tenantId", "name", "files", "customerAccount", "bugSource", "customerType", "follower", "operate", "frequency", "ei"], "allowTenantId": [687889, 1], "url": "http://************:32674/tapd/bug/add"}, "er.listDownstreamEmployeeIdsByLinkAppId": {"paramField": ["upstreamEa", "downstreamOuterTenantIds", "linkAppId"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/downstream/listDownstreamEmployeeIdsByLinkAppId", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa"}}, "department.bulkDelete": {"paramField": ["dataIds"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/DepartmentObj/action/BulkDelete"}, "open.sendRegistrationNoticeByFunction": {"paramField": ["tenantId", "userId", "title", "summary", "content", "covers", "crmAccountIds", "attachments", "objectId", "objectApiName", "isCoverImageInText"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:30926/open/inner-material/wx/link/notice/sendRegistrationNoticeByFunction"}, "eservice.createCode128": {"paramField": ["tenantId", "userId", "content", "width", "height", "withQuietZone", "msgPosition", "fontSize", "angle", "codeSet", "mime", "imageType", "resolution", "antiAlias", "orientation"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/barcode/customized/createCode128ByFunction"}, "metadata.bulkUpdateT2nMap": {"paramField": ["listArg"], "allowTenantId": [1, 634858], "url": "http://************:36492/fs-metadata-for-test/paas/metadata/data/bulk/update/t2nmap"}, "kekai.common.sign": {"paramField": ["tenantId", "userId", "amount", "merchantCode", "goodsId", "merchantOrderNo", "toEA", "isWeb"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-pay/common/sign"}, "er.listMapperObjectByMapperObjectId": {"paramField": ["upstreamTenantId", "mapperObjectIds"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/enterpriseRelation/listMapperObjectByMapperObjectId", "bodyMapping": {"tenantIdFieldName": "upstreamTenantId"}}, "custom.Orion.sync.test": {"paramField": ["api_name", "id", "object_dictionary", "operation"], "url": "http://************:8090/api/add_asyn"}, "custom.OrionProd.synpersonneldepartment.sync": {"paramField": [], "url": "http://*************:8080/job/synpersonneldepartment"}, "kekai.printPdf.exportPdf": {"paramField": ["tenantId", "userId", "dataName", "dataId", "objectType", "templateList"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-print/kekai/printPdf/exportPdf"}, "department.bulkStop": {"paramField": ["dataIds"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/DepartmentObj/action/BulkStop"}, "message.sendTextCardMessage": {"paramField": ["uuid", "ei", "receiverChannelType", "receiverChannelData", "receiverIds", "textCardMessage", "generateUrlType", "objectApiName", "objectId", "extraDataMap"], "headerMapping": {}, "replaceHeaders": false, "replaceUserIdHeader": false, "allowTenantId": [1, 691717], "url": "http://${variables_endpoint.svc_apibus_global}/fs-message/api/v2/sendTextCardMessage"}, "kekai.payment.pushPayUrl": {"paramField": ["tenantId", "userId", "url", "amount", "goodsName", "wxOpenId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-pay/payment/pushPayUrl"}, "feedback.deleteFeedback": {"paramField": ["objDataId"], "replaceHeaders": false, "bodyMapping": {}, "url": "http://************:39414/fs-support-feedback/fs-support/API/Feedback/deleteFeedback"}, "kekai.common.payStatusCheck": {"paramField": ["tenantId", "userId", "dataId", "apiName", "fieldName"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-pay/common/payStatusCheck"}, "checkins.createPlan": {"paramField": ["extFields", "userId", "checkTypeId", "planTime", "executorId", "assistantIds", "mainObjList", "mainObject", "referenceObject"], "url": "${variables_endpoint.svc_wq_server}/plan/createPlan/${tenantId}/${userId}"}, "sanbox.kadc.updateSaveDc": {"paramField": ["<PERSON><PERSON><PERSON><PERSON>", "appId", "CUST_CD", "SALE_DT", "POST_STR_DT", "POST_END_DT", "dataId"], "bodyMapping": {}, "url": "http://************:8088/open_api/crm/dc2/updateSave"}, "email.page": {"paramField": ["objDescApiName", "pageNumber", "pageSize", "SortField", "SortType", "QueryInfo"], "url": "http://crmtemplate.nsvc.foneshare.cn/crm_template/rest/v1/email/page"}, "TRS.createFanWeiCompanyOrCustomer": {"paramField": [], "url": "http://************:41045/biz/contacts/createFanWeiCompanyOrCustomer/${enterpriseAccount}/${accountId}"}, "file.createFileShareTokens": {"paramField": ["employeeId", "pathList", "ea", "expireDay"], "url": " http://************:34820/fs-fsc-provider/SharedFileService/createFileShareTokens"}, "metadata.getDescribe": {"paramField": ["tenantId", "apiNames"], "url": "http://************:36492/fs-metadata-for-test/paas/metadata/describe/findBy/apiNames"}, "sfa.get_account_limit_info": {"paramField": [], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/account/service/get_account_limit_info"}, "kekai.pay.getExpireDate": {"paramField": ["createTime"], "url": "http://************:60125/fs-pay-kekai/pay/getExpireDate"}, "er.batchAddOutAppRoleAssigns": {"paramField": ["downstreamOuterUids", "roleId", "linkAppId"], "url": "${variables_endpoint.dns_er}/outapi/linkApp/batchAddOutAppRoleAssigns", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa"}}, "sfa.accountAddr.setMain": {"paramField": ["location_id", "account_id"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/AccountAddrObj/action/SetMain"}, "saml2.setMapping": {"paramField": ["employeeId", "nameId"], "url": "http://************:58642/saml2mapping/sp/saml2/setMapping"}, "esign.flow.start": {"paramField": ["dataId"], "url": "http://************:56602/sign/start"}, "industry.getCompanyById": {"paramField": ["KeyNo"], "url": "http://${variables_endpoint.dns_industry}/industry/industryFcpService/getCompanyDetailById"}, "ekuaibao.create": {"paramField": ["dataId"], "url": "http://************:35065/create/project"}, "object.changePartner": {"paramField": ["dataIds", "describe_api_name", "partnerId"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/${describe_api_name}/action/ChangePartner"}, "er.listDownstreamEmployeesByDownstreamOuterTenantIds": {"paramField": ["downstreamOuterTenantIds"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/listDownstreamEmployeesByDownstreamOuterTenantIds", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa"}}, "er.createDownstreamPublicEmployeeByMapperObjectId": {"paramField": ["mapperApiName", "mapperId", "contractId", "isInitFsAccount", "templateEa", "outerRoleIds", "linkAppIds", "identityType", "downstreamEnterpriseName", "mainRoleId"], "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/createDownstreamPublicEmployeeByMapperObjectId", "bodyMapping": {"tenantIdFieldName": "upstreamEi"}}, "checkins.routeChangeRecord": {"paramField": ["ea", "id"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "http://************:46877/Export/ruleApp/routeChangeRecord"}, "fmcg.syncRoute": {"paramField": ["ea", "routeId", "accountId", "taskId"], "url": "http://************:45080/dealData/syncRoute"}, "kekai.fala.calculate": {"paramField": ["tenantId", "userId", "ids", "masterId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-other/calculate/SimulationsOffer"}, "weex.jenkinsBuildTest": {"paramField": ["job<PERSON>ame", "objectDataId", "buildParams"], "url": "http://112.firstshare.cn/weex-console/api/jenkins/build/test"}, "object.edit": {"paramField": ["object_data", "details", "describe_api_name", "optionInfo"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/${describe_api_name}/action/Edit"}, "file.saveFileFromTempFile": {"paramField": ["employeeId", "temp_file_name", "ea", "extension_name", "business"], "url": "http://stone.nsvc.foneshare.cn/fs-stone-proxy/api/v1/file/save_file_from_temp_file", "bodyMapping": {"enterpriseAccountFieldName": "ea"}}, "sfa.pricePolicy.findPricePolicyConfig": {"paramField": ["requestId", "accountId", "masterObjectApiName"], "replaceHeaders": false, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/object/price_policy/service/findPricePolicyConfig"}, "custom.Orion.synpersonne.sync": {"paramField": [], "url": "http://************:8080/job/synpersonne"}, "er.getDownstreamOuterTenantIdByMappObjectId": {"paramField": ["mapperObjectId"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/enterpriseRelation/getDownstreamOuterTenantIdByMappObjectId", "bodyMapping": {"tenantIdFieldName": "upstreamTenantId"}}, "er.batchGetRelationDownstream": {"paramField": ["crmDataIds", "objectApiName"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/enterpriseRelation/batchGetRelationDownstream", "bodyMapping": {"tenantIdFieldName": "upstreamTenantId"}}, "dc.getTryCount": {"paramField": ["<PERSON><PERSON><PERSON><PERSON>", "appId", "SCUST_CD", "SALE_DT", "dataId"], "bodyMapping": {}, "url": "http://************:8089/open_api/crm/dc/getTryCount"}, "eolinker.getReport": {"paramField": ["space_id", "project_id", "report_type", "report_id"], "url": "http://oss.foneshare.cn/qat2/eolinker/getReport"}, "er.batchGetEmployeeCards": {"paramField": ["outerUids"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/employeeCard/batchGetEmployeeCards", "bodyMapping": {}}, "function.disableDeveloperVerification": {"paramField": ["tenantId"], "allowTenantId": [1, 590064, 590057, 683750], "url": "http://************:57320/API/v1/inner/object/function/service/disableDeveloperVerification"}, "bizLogFunction.search": {"paramField": ["query", "aggs", "size"], "allowTenantId": [], "url": "http://************:9200/biz-log-function-*/_search"}, "fs.updateCustomer": {"paramField": ["dataId"], "url": "http://************:36337/versionRegisterService/updateCustomerByCustomerId"}, "uc.changeEnterpriseEmployLimit": {"paramField": ["enterpriseId", "employLimit"], "url": "http://usercenter.nsvc.foneshare.cn/uc-provider/EnterpriseEditionService/changeEnterpriseEmployLimit"}, "sfa.get_employee_object_limit_filter": {"paramField": ["objectApiName", "employeeId"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/object_limit/service/get_employee_object_limit_filter"}, "fmcg.sales.exportSalesOrderProduct": {"paramField": ["data"], "bodyMapping": {}, "url": "http://************:45334/sales_order/export_product"}, "checkins.delCheckins": {"paramField": ["checkinsId"], "url": "${variables_endpoint.svc_wq_server}/plan/delCheckins/${tenantId}/${userId}/${checkinsId}"}, "TRS.syncContactIdsToFanwei": {"paramField": [], "url": "http://************:41045/biz/contacts/syncContactIdsToFanwei/${enterpriseAccount}/${contactId}"}, "roleprivilege.getRoleList": {"paramField": ["roleSource"], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/roleprivilege/service/getRoleList"}, "industry.getCompanyDetailsByNames": {"paramField": ["companyName", "argType"], "headerMapping": {"tenantIdFieldName": "X-fs-Enterprise-Id", "userIdFieldName": "X-fs-Employee-Id"}, "url": "http://${variables_endpoint.dns_industry}/industry/industryFunction/getCompanyDetailsByNames"}, "kekai.pay.getShort": {"paramField": ["url"], "url": "http://************:60125/fs-pay-kekai/pay/getShort"}, "sanbox.dc.updateSaveDc": {"paramField": ["<PERSON><PERSON><PERSON><PERSON>", "appId", "SCUST_CD", "SALE_DT", "dataId"], "bodyMapping": {}, "url": "http://************:8088/open_api/crm/dc/updateSaveDc"}, "bpm.task.change.handler": {"paramField": ["taskId", "candidateIds"], "bodyMapping": {}, "url": "http://${variables_flow.bpm_rest_url}/plugins/task/changeTaskHandler"}, "custom.Orion.synpersonne.sync.test": {"paramField": [], "url": "http://************:8090/job/synpersonne"}, "qixin.CSMSessionAddParticipant": {"paramField": ["sessionId", "addParticipants"], "url": "http://************:39414/qixin-provider/cross/CSMSession/addParticipant"}, "checkins.updateCustomerActionData": {"url": "${variables_endpoint.svc_wq_server_yqsl}/customProxy/updateCustomerActionData/${tenantId}/${actionId}"}, "erp.syncData.keshunPushData2Table": {"paramField": ["startTime", "endTime", "sapCustomers", "erpFieldApiName", "fakeErpApiName"], "headerMapping": {"x-fs-ei": "x-fs-ei"}, "url": "http://${variables_endpoint.Erp_SyncData_Custom_Address}/inner/erp/syncdata/customfunction/keshun/getKeShunErpData2Table", "bodyMapping": {}}, "eservice.createBarcode": {"paramField": ["tenantId", "userId", "type", "content", "width", "height", "logoFileName", "widthOfLogo", "heightOfLogo", "subscript", "fontFamily", "fontStyle", "fontSize", "charset", "angle"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/barcode/customized/createBarcodeByFunction"}, "checkins.insertAccount2Route": {"paramField": ["locationId", "customerAddress", "customerLat", "customerLon", "customerId"], "url": "http://************:45505/Export/customProxy/insertAccount2Route/${tenantId}/${owner}/${routeId}"}, "kekai.pay.payment.getEncryptExpireDate": {"paramField": ["createTime"], "url": "http://************:60125/fs-pay-kekai/pay/payment/getEncryptExpireDate"}, "fmcg.syncAvailableRange": {"paramField": ["supply_id"], "url": "http://************:30752/API/v1/rest/object/DealerSupplyObj/controller/SyncAvailableRange"}, "er.listDownstreamOuterUidByRoleIdAndLinkAppId": {"paramField": ["downstreamOuterTenantId", "roleId", "upstreamOuterTenantId", "linkAppId"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/downstream/listDownstreamOuterUidByRoleIdAndLinkAppId", "bodyMapping": {}}, "fmcg.custom.yq.detect": {"paramField": ["model_id", "path", "is_pure_detect"], "headerMapping": {"x-fs-ei": "x-fs-ei"}, "allowTenantId": [735454, 735463], "bodyMapping": {}, "url": "http://************:33670/API/inner/v1/custom/yq/ai/detect"}, "eservice.updateMaintenanceStatus": {"paramField": ["tenantId", "userId", "maintenanceId", "operatorType"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/maintenance/updateMaintenanceStatus"}, "custom.Orion.sync": {"paramField": ["api_name", "id", "object_dictionary", "operation"], "url": "http://************:8080/api/add_asyn"}, "foneshare.sendWXMessage": {"paramField": ["title", "contents", "outerTenantId", "outerUserId", "linkAppId", "linkAppType", "tenantId", "outerRoleIds", "wxServiceAppIds", "message", "messageType", "channels", "forwardUrl", "templateIdForWechatServiceMsg", "accountId", "responsibility"], "url": "http://${variables_endpoint.svc_fmcg_customized_service}/biz/contacts/sendWXMessage"}, "fmcg.tpm.budget_operator.freeze": {"paramField": ["api_name", "data_id", "freeze_data"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:30752/FMCG/InnerAPI/TPM/CommonBudget/Freeze"}, "checkins.recalculateCheckinAction": {"paramField": ["mainApiName", "forceExcute", "date", "mainDataId"], "url": "http://************:45505/Export/customProxy/recalculateCheckinAction"}, "dc.updateSaveDc": {"paramField": ["<PERSON><PERSON><PERSON><PERSON>", "appId", "SCUST_CD", "SALE_DT"], "bodyMapping": {}, "url": "http://************:8089/open_api/crm/dc/updateSaveDc"}, "smartform.generateCard": {"paramField": ["formDescribeId", "targetObjectApiName", "targetObjectDataId", "formParams"], "url": "http://${variables_endpoint.svc_apibus_global}/smartform/rest/api/card/generate"}, "object.bulkRecover": {"paramField": ["describe_api_name", "idList"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/${describe_api_name}/action/BulkRecover"}, "message.sendTextLinkMessage": {"paramField": ["uuid", "ei", "receiverChannelType", "receiverChannelData", "receiverIds", "outEmployees", "title", "messageContent", "innerPlatformWebUrl", "innerPlatformMobileUrl", "outPlatformUrl", "generateUrlType", "objectApiName", "objectId", "extraDataMap", "lastSummary"], "headerMapping": {}, "replaceHeaders": false, "replaceUserIdHeader": false, "allowTenantId": [1, 691717], "url": "http://${variables_endpoint.svc_apibus_global}/fs-message/api/v2/sendTextLinkMessage"}, "object.ChangePartner": {"paramField": ["dataIds", "describe_api_name", "partnerId"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/${describe_api_name}/action/ChangePartner"}, "fs.triggerOrder": {"paramField": ["orderId"], "allowTenantId": [1], "url": "http://************:36337/versionRegisterService/crmOrderToDetail"}, "nps.syncTask": {"paramField": ["planObjId", "taskObjId", "tenantId", "pushCount"], "headerMapping": {"tenantId": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:39414/fs-support-nps/fs-support/API/Question/syncTask"}, "checkoffice.getCheckOfficeDataList": {"paramField": ["pageType", "pageSize", "pageNum", "lastTime", "startTime", "endTime", "userIds"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "http://************:55275/ProvideOtherTeamMethods/getCheckOfficeDataList"}, "obj.button.call.asiainfo123.button_r7Aup__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/object_61ym3__c/action/button_r7Aup__c"}, "fmcg.tpm.budget_operator.unfreeze": {"paramField": ["api_name", "data_id"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:30752/FMCG/InnerAPI/TPM/CommonBudget/Unfreeze"}, "er.listDownstreamOuterUids": {"paramField": ["identityType", "offset", "limit"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/listDownstreamOuterUids", "bodyMapping": {"tenantIdFieldName": "upstreamEi"}}, "erp.syncData.createSyncDataMapping": {"paramField": ["ployDetailId", "sourceObjectApiName", "sourceDataId", "masterDataId", "sourceDataName", "destObjectApiName", "destDataId", "destDataName", "remark", "enableUpdateSourceDataId"], "headerMapping": {"x-fs-ei": "x-fs-ei"}, "url": "http://${variables_endpoint.Erp_SyncData_Visit_Address}/inner/erp/syncdata/customfunction/SyncDataMapping/createSyncDataMapping"}, "feedback.deleteAttach": {"paramField": ["objDataId"], "replaceHeaders": false, "bodyMapping": {}, "url": "http://************:39414/fs-support-feedback/fs-support/API/Feedback/deleteAttach"}, "er.batchGetOuterTenantIdByOuterUids": {"paramField": ["outerUIds"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/employeeCard/batchGetOuterTenantIdByOuterUids"}, "fs.copyConfig": {"paramField": ["enterpriseId", "toEnterpriseId"], "url": "http://************:32622/SandboxService/enterpriseCopyNoDepartment"}, "er.getMapperObjectId": {"paramField": ["downstreamOuterTenantId", "objectApiName"], "url": "${variables_endpoint.dns_er}/outapi/enterpriseRelation/getMapperObjectId", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa"}}, "marketing.deleteTagsToCrmData": {"paramField": ["tenantId", "crmObjectDescribeApiName", "crmObjectIds", "tagNames"], "url": "https://${variables_endpoint.http_domain}/marketing/userMarketingAccount/deleteTagsToCrmData"}, "industry.getCompanyDetailById": {"paramField": ["KeyNo"], "url": "http://${variables_endpoint.dns_industry}/industry/industryFcpService/getCompanyDetailById"}, "department.update": {"paramField": ["data", "separate", "observerIds"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/DepartmentObj/action/IncrementUpdate"}, "nps.regisTimer": {"paramField": ["planId", "taskId", "tenantId", "pushTimestamps"], "headerMapping": {"tenantId": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:39414/fs-support-nps/fs-support/API/Question/regisTimer"}, "eolinker.getStatisticsByProject": {"paramField": ["project_id"], "url": "http://oss.foneshare.cn/qat2/eolinker/getStatisticsByProject"}, "checkins.getVisitObjInfoById": {"paramField": ["ea", "checkId"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "http://************:60178/ForOtherTeamMethods/getVisitObjInfoById/${tenantId}/${checkId}"}, "checkoffice.queryEmpSignInfoByDate": {"paramField": ["empId", "statDate"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "http://************:55275/customProxy/queryEmpSignInfoByDate"}, "approval.getInstanceInfo": {"paramField": ["entityId", "objectId"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_crm_workflow}/approval/getInstanceProgress"}, "obj.button.call.709154_sandbox.button_y1r7Z__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/object_MvtI8__c/action/button_y1r7Z__c"}, "custom.Orion.synsanboxproduct.sync": {"paramField": [], "url": "http://************:8090/sync/product"}, "stock.queryRealTimeAvailableStockByProductAndWarehouses": {"paramField": ["warehouseIds", "productId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/inner/object/stock_for_erp/service/query_real_time_available_stock_by_product_and_warehouses"}, "fmcg.tpm.closeActivity": {"paramField": ["objectDataId", "skipDataValidate"], "headerMapping": {}, "url": "http://************:30752/API/v1/rest/object/TPMActivityObj/action/CloseActivity"}, "yuanqi.getRouteAccountNumber": {"paramField": ["ea", "checkId"], "bodyMapping": {"enterpriseAccountFieldName": "ea"}, "url": "http://************:63669/biz/v1/route/countAccountNumOfRoute/${enterpriseAccount}/${checkId}"}, "feedback.addStory": {"paramField": ["appVersion", "title", "description", "tapdWorkspaceId"], "allowTenantId": [687889, 1], "url": "http://************:32674/TAPDHelper/CRM/AddStory"}, "stock.queryRealTimeAvailableStockByWarehouseAndProduct": {"paramField": ["warehouseId", "productIds"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://${variables_endpoint.dns_ncrm}/API/v1/inner/object/stock_for_erp/service/query_real_time_available_stock_by_warehouse_and_product"}, "kekai.printQuote.exportPdf": {"paramField": ["dataName", "dataId", "templateList", "objectType"], "url": "http://************:30611/fs-tuosida-print/printQuote/exportPdf"}, "feedback.sendFeed": {"paramField": ["appVersion", "clientName", "description", "title", "clientType", "customerContact", "startTime", "userId", "tenantId", "objectId", "entityId", "owner", "images", "operate", "module", "customer", "files", "customerAccount", "operate", "created_by", "ei"], "allowTenantId": [687889, 1], "url": "http://************:32674/tapd/feed/bug/send"}, "object.deletePartner": {"paramField": ["dataIds", "describe_api_name"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/${describe_api_name}/action/DeletePartner"}, "help.saveDocuments": {"paramField": ["documentInfoList"], "bodyMapping": {}, "url": "http://************:39414/fs-support-help/fs-support/API/Help/saveDocuments"}, "cms.grayEnterpriseAccountAppend": {"paramField": ["config", "profile", "key", "eaSet", "token", "editor"], "allowTenantId": [1], "bodyMapping": {}, "url": "http://oss.foneshare.cn/cs/api/gray/ea/append"}, "custom.print.proxy.gray": {"paramField": ["tenantId", "userId", "<PERSON><PERSON><PERSON>", "args"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:37735/fs-open-custom-print-gray/customfunction/print/proxy"}, "obj.button.call.asiainfo123.button_C3uel__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/object_dm0ba__c/action/button_C3uel__c"}, "email.buildCrmBindInfo": {"paramField": ["tenantId", "fsUserId", "userIdList", "objDataId", "objDataIdList", "objApiName", "objApiNameDataIdMap"], "bodyMapping": {"tenantIdFieldName": "tenantId"}, "url": "http://172.17.32.72:8001/open/emailproxy/migrate/buildCrmBindInfo"}, "eservice.updateEmployeeStatus": {"paramField": ["arg1", "arg2", "arg3"], "bodyMapping": {"enterpriseAccountFieldName": "arg2"}, "url": "${variables_eservice.eservice_cases_rest_base_url}/com.facishare.eservice.cases.api.service.EmployeeStatusService/updateEmployeeStatusByFunction/1.0"}, "metadata.createTaskJob": {"paramField": ["job<PERSON>ame"], "allowTenantId": [683661, 1, 750076], "url": "http://oss.foneshare.cn/qat2/ci/job/createTaskJob"}, "checkins.batchUpdateFields": {"paramField": ["extFields", "checkinsIds"], "url": "http://************:45505/Export/customProxy/batchUpdateFields/checkins/mongo"}, "custom.Orion.synpersonneldepartment.sync.test": {"paramField": [], "url": "http://************:8090/job/synpersonneldepartment"}, "nps.batchDeleteTimer": {"paramField": ["planId", "tasks"], "headerMapping": {"tenantId": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:39414/fs-support-nps/fs-support/API/Question/batchDeleteTimer"}, "weex.rollback": {"paramField": ["gray<PERSON><PERSON>", "bundleName"], "url": "http://************:48805/weex-console/api/publish/rollback"}, "custom.other.proxy": {"paramField": ["tenantId", "userId", "<PERSON><PERSON><PERSON>", "args"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-other/customfunction/other/proxy"}, "er.listBindWxServiceDataByWxAppIdsAndOuterUids": {"paramField": ["wxAppIds", "outerUids"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/wxservice/listBindWxServiceDataByWxAppIdsAndOuterUids", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa"}}, "obj.button.call.fktest080.button_b9gd0__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/object_6TNd9__c/action/button_b9gd0__c"}, "obj.button.call.asiainfo123.button_lHb2J__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/QuoteObj/action/button_lHb2J__c"}, "kekai.other.labelLibrary": {"paramField": ["tenantId", "userId", "dataIds", "dataId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-print/kekai/other/labelLibrary"}, "dirtywords.megvii": {"paramField": ["text"], "url": "http://************:56296/parse/group/megvii"}, "usergroup.list": {"paramField": [], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/usergroup/service/list"}, "eservice.batchAddOrUpdateServiceGroups": {"paramField": ["tenantId", "userId", "serviceGroupList"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/serviceGroup/batchAddOrUpdateServiceGroupsByFunction"}, "fmcg.ai.dzDetect": {"paramField": ["recordType", "nPath", "agreementId"], "headerMapping": {}, "url": "http://${variables_endpoint.svc_fmcg_service}/API/inner/detect/dz_detect"}, "area.getCountryAreaOptions": {"paramField": [], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/global_data/service/country_area_field_options"}, "obj.button.call.youzan.button_w04s7__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/object_zPg47__c/action/button_w04s7__c"}, "checkins.getLocationCode": {"url": "${variables_endpoint.svc_wq_server}/customProxy/getLocationFieldCodeByLonAndLat/${lat}/${lon}/"}, "kekai.common.getShortUrl": {"paramField": ["tenantId", "userId", "url"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:33505/fs-open-custom-pay/common/getShortUrl"}, "weex.publish": {"paramField": ["bundle", "gray<PERSON><PERSON>"], "url": "http://112.firstshare.cn/weex-console/api/wx/crm/publish"}, "TRS.updateFanWeiCompanyInfo": {"paramField": ["type"], "url": "http://************:41045/biz/contacts/updateFanWeiCompanyInfo/${enterpriseAccount}/${accountId}"}, "sanbox.dc.getTryCount": {"paramField": ["<PERSON><PERSON><PERSON><PERSON>", "appId", "SCUST_CD", "SALE_DT", "dataId"], "bodyMapping": {}, "url": "http://************:8088/open_api/crm/dc/getTryCount"}, "eservice.submitRefundMaterialBill": {"paramField": ["tenantId", "userId", "id", "recordType"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/accessory/casesAccessory/submitRefundMaterialBill"}, "enterprisepay.updateEAOrder": {"paramField": ["arg1"], "url": "http://************:59927/com.facishare.pay.toB.pay.service.EAPayOrderService/updateEAOrder/1.0"}, "email.fill": {"paramField": ["templateId", "objectId"], "url": "http://${variables_endpoint.svc_crm_template}/crm_template/rest/v1/email/fillEmail"}, "sfa.get_my_leads_pool_list": {"paramField": ["ignoreCount"], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/pool_service/service/get_my_leads_pool_list"}, "er.batchDeleteRelations": {"paramField": ["destOuterTenantIds"], "headerMapping": {}, "url": "${variables_endpoint.dns_er}/outapi/enterpriseRelation/batchDeleteRelations", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa", "userIdFieldName": "userId"}}, "fmcg.ai.saveDZDetectResult": {"paramField": ["proofId"], "headerMapping": {}, "url": "http://${variables_endpoint.svc_fmcg_service}/API/inner/detect/save_dz_detect_result"}, "weex.getBundleInfo": {"paramField": ["dataId"], "url": "http://112.firstshare.cn/weex-console/api/wx/crm/bundle/get?dataId=${dataId}"}, "er.getDownstreamOuterUidByMapperObjectId": {"paramField": ["contractId"], "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/getDownstreamOuterUidByMapperObjectId", "bodyMapping": {"tenantIdFieldName": "upstreamEi"}}, "checkoffice.batchGetHoliday": {"paramField": ["startTime", "endTime"], "url": "${variables_endpoint.svc_kaoqin_server}/holiday/batchGetHoliday"}, "weex.getWeexByCrm": {"paramField": ["dataId"], "url": "http://112.firstshare.cn/weex-console/api/wx/crm/bundle/get?dataId=${dataId}"}, "customeraccount.create": {"paramField": ["customerId", "fundAccountId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/inner/object/customer_account/service/create_new_customer_account"}, "newcoverage.crawlCoverageDetails": {"paramField": ["url", "expect", "need_method", "service_coverage_id"], "url": "http://oss.foneshare.cn/qat2/newcoverage/crawlCoverageDetails"}, "sfa.policy.occupy.custom.subtract": {"paramField": ["orderIds"], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/policy_occupy/service/update_occupy_by_order"}, "industry.getCompanyHoldersByNames": {"paramField": ["companyName"], "headerMapping": {"tenantIdFieldName": "X-fs-Enterprise-Id", "userIdFieldName": "X-fs-Employee-Id"}, "url": "http://${variables_endpoint.dns_industry}/industry/industryFunction/getCompanyHoldersByNames"}, "area.getZoningCodeByLabel": {"paramField": ["type", "label"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/global_data/service/get_zoning_code"}, "integralProvider.managementAddDetail": {"paramField": ["employeeIds", "amount", "actionTime", "remark", "enableMode", "disableMode", "departmentIds", "tags", "enableTimeValue", "disableTimeV<PERSON>ue"], "url": "http://************:52805/management/addDetail"}, "eservice.createMaintenanceCases": {"paramField": ["tenantId", "userId", "maintenanceDetailId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/maintenance/createMaintenanceCases"}, "kekai.pay.payment.addOnlinePayment": {"paramField": ["payer", "owner", "paymentPlans"], "url": "http://************:60125/fs-pay-kekai/pay/payment/addOnlinePayment"}, "fmcg.service.yq.aggregate.function": {"paramField": ["objectApiName", "groupFields", "functionList", "groupBy", "filterList"], "allowTenantId": [735454, 735463, 753512, 707988, 754668, 748862], "bodyMapping": {}, "url": "http://************:33670/v1/yuan_qi/query_aggregate_order"}, "qyweixin.deleteBind": {"paramField": ["deleteBindType", "corpIds", "appId"], "allowTenantId": [1], "url": "http://open.intranet.fxiaoke.com/open/qyweixin/deleteBind"}, "eservice.countDistance": {"paramField": ["tenantId", "userId", "accountKey", "originLon", "originLat", "destinationLon", "destinationLat", "strategy"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/guidemap/countDistanceByFunction"}, "integral.coin_calculate_tax": {"paramField": ["ei", "employeeId", "data"], "url": "http://************:53917/API/inner/v1/function/calculate_tax"}, "customized.brushData": {"paramField": ["checkScene", "triggerScene", "changedObjApiName", "changedObjId", "changedField", "brushObjApiNames"], "url": "http://************:63669/biz/data_brush/scene"}, "userprivilege.batchAddUserRole": {"paramField": ["roleCodes", "updateMajorRole", "majorRole", "userIds"], "bodyMapping": {"tenantIdFieldName": "enterpriseId"}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/userprivilege/service/batchAddUserRole"}, "feedback.updateReply": {"paramField": ["masterObjDataId", "subObjDataId", "tenantId", "employeeId", "content"], "replaceHeaders": false, "bodyMapping": {}, "url": "http://************:39414/fs-support-feedback/fs-support/API/Feedback/updateReply"}, "checkins.editPlan": {"paramField": ["checkinId", "endTime", "planTime"], "url": "${variables_endpoint.svc_wq_server}/checkinsUpdate/editPlan/${tenantId}/${userId}"}, "eolinker.createTestReport": {"paramField": ["service_id", "project_id", "report_type", "create_time"], "url": "http://oss.foneshare.cn/qat2/eolinker/createTestReport"}, "log.scrollSearch": {"paramField": ["apiNames", "bizOperationNames", "tenantId", "pageSize", "sort", "operationTimeFrom", "operationTimeTo"], "url": "http://************:34674/log/web/scrollSearch", "bodyMapping": {"tenantIdFieldName": "tenantId"}}, "industry.getCompanyBranchByNames": {"paramField": ["companyName"], "headerMapping": {"tenantIdFieldName": "X-fs-Enterprise-Id", "userIdFieldName": "X-fs-Employee-Id"}, "url": "http://${variables_endpoint.dns_industry}/industry/industryFunction/getCompanyBranchByNames"}, "sfa.check_object_limit": {"paramField": ["objectApiName", "objectDataList"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/object_limit_service/service/check_object_limit"}, "stock.flowCompletedCheckStock": {"paramField": ["dataId", "apiName"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:58500/API/v1/inner/object/stock/service/flow_completed_check_stock"}, "eservice.getLocation": {"paramField": ["tenantId", "userId", "address"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "${variables_eservice.eservice_internal_name_address}/eservice/worldmap/getLocationByFunction"}, "checkins.updateCustomerAction": {"paramField": ["checkId", "sourceActionId", "objectApiName", "objDataId", "map"], "url": "http://************:45505/Export/customProxy/updateCustomerAction"}, "feed.getRangeInfoByFeedId": {"paramField": ["feedId"], "headerMapping": {"tenantIdFieldName": "x-fs-enterprise-id", "userIdFieldName": "x-fs-employee-id"}, "bodyMapping": {}, "url": "http://${variables_plat.feed_apl_url}/FeedInfo/getRangeInfoByFeedId"}, "stock.queryProductAvailableStocks": {"paramField": ["customerId", "warehouseId", "productIds", "isOutUser", "recordType"], "bodyMapping": {}, "url": "http://${variables_endpoint.dns_ncrm}/API/v1/inner/object/stock/service/query_product_available_stocks"}, "fmcg.resetDealerSupply": {"paramField": ["account_id"], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/rest/object/DealerSupplyObj/controller/Reset"}, "erp.syncData.commonPushData2Table": {"paramField": ["crmDataFilter", "erpDataFilter"], "headerMapping": {"x-fs-ei": "x-fs-ei"}, "url": "http://${variables_endpoint.Erp_SyncData_Custom_Address}/inner/erp/syncdata/customfunction/getErpData2Table", "bodyMapping": {}}, "integral.sendGoods": {"paramField": ["orderIds"], "url": "${variables_endpoint.svc_integral}/API/inner/mall/sendGoods", "bodyMapping": {}}, "fmcg.tpm.agreement.reset_end_date": {"paramField": ["object_data_id", "end_date"], "headerMapping": {"X-fs-Employee-Id": "X-fs-Employee-Id", "X-fs-Enterprise-Id": "X-fs-Enterprise-Id"}, "bodyMapping": {}, "url": "http://************:55960/API/v1/object/TPMActivityAgreementObj/controller/ResetEndDate"}, "dataFactory.createInitialzeEnterpriData": {"paramField": ["userInfo", "enterpriseResource", "systemResource", "customResource"], "url": "http://oss.foneshare.cn/qat2/dataFactory/createInitialEnterpriseData"}, "marketing.addTagToCrmData": {"paramField": ["tenantId", "crmObjectDescribeApiName", "crmObjectId", "tagGroupName", "tagName"], "url": "https://${variables_endpoint.http_domain}/marketing/userMarketingAccount/addTagToCrmData"}, "openapi.mediaId2Npath": {"paramField": ["mediaIds", "appId"], "url": "http://${variables_endpoint.svc_open_api}/inner/file/mediaId2Npath"}, "sfa.get_object_count_by_limit_rule": {"paramField": ["dataId", "objectApiName"], "bodyMapping": {}, "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/object_limit_service/service/get_object_count_by_limit_rule"}, "interconnect.createInterconnectByAccountIds": {"paramField": ["accountIds"], "url": "http://************:58504/interconnect/createInterconnectByAccountIds"}, "kekai.other.labelLibrary.shahe": {"paramField": ["tenantId", "userId", "dataIds", "dataId"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://************:37735/fs-open-custom-print-gray/kekai/other/labelLibrary"}, "metadata.getCase": {"paramField": ["gitUrl", "gitBranch", "serviceName", "casePath"], "allowTenantId": [683661, 1, 750076], "url": "http://oss.foneshare.cn/qat2/case/getCase"}, "sfa.get_my_high_seas_list": {"paramField": ["ignoreCount"], "url": "http://${variables_endpoint.svc_apibus_ncrm}/API/v1/inner/object/pool_service/service/get_my_high_seas_list"}, "obj.button.call.asiainfo123.button_GejkQ__c": {"paramField": ["objectDataId"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/rest/object/object_bc9w1__c/action/button_GejkQ__c"}, "stock.getRealStock.warePosition": {"paramField": ["customerId", "warehouseId", "productIds", "isOutUser", "deliveryMode", "warehouseIds"], "bodyMapping": {"tenantIdFieldName": "tenantId", "userIdFieldName": "userId"}, "url": "http://${variables_endpoint.dns_ncrm}/API/v1/inner/object/stock/service/query_delivery_note_product_real_stock"}, "eolinker.createCoverRate": {"paramField": ["service_name", "team", "jars"], "url": "http://oss.foneshare.cn/qat2/eolinker/createCoverRate"}, "deliveryNote.confirmReceive": {"paramField": ["deliveryNoteId", "receiveRemark", "attachments", "images", "deliveryNoteProducts"], "url": "http://${variables_endpoint.dns_ncrm}/API/v1/inner/object/delivery_note/service/confirm_receive"}, "er.getMapperIdByOuterUid": {"paramField": ["upstreamEa", "downstreamOuterTenantId", "downstreamOuterUid"], "headerMapping": {"tenantId": "x-fs-ei"}, "url": "${variables_endpoint.dns_er}/outapi/publicEmployee/getMapperId", "bodyMapping": {"enterpriseAccountFieldName": "upstreamEa"}}, "ekuaibao.disable": {"paramField": ["dataId"], "url": "http://************:35065/disable/project"}, "yq.budget_operator.appendFreeze": {"paramField": ["object_api_name", "data_id", "withholding_data_id"], "headerMapping": {"x-fs-userInfo": "x-fs-userInfo", "x-fs-ei": "x-fs-ei"}, "bodyMapping": {}, "url": "http://************:30752/FMCG/InnerAPI/TPM/CustomBudget/AppendFreeze"}, "erp.syncData.custom": {"paramField": ["param"], "headerMapping": {"x-fs-ei": "x-fs-ei"}, "url": "http://${variables_endpoint.Erp_SyncData_Custom_Address}/inner/erp/syncdata/customfunction/${tenantId}/dowork", "bodyMapping": {}}}