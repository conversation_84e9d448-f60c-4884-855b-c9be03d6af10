package com.facishare.crm.fmcg.wq.test1

/**
 * @program: fs-crm-fmcg-wq
 *
 * @description: ${description}*
 * @author: zhangsm
 *
 * @create: 2022-10-19 17:38
 * */
class ApiBusConfigDemo {


    static void main(String[] args) {
        def toBeReplacedStr = '''http://************:46877
http://************:43152
http://************:45505
http://************:63482
http://************:30070
http://************:59951
http://************:30890
http://************:38294
${variables_endpoint.svc_wq_server_yqsl}
${variables_endpoint.svc_wq_server_stage}
${variables_endpoint.svc_wq_server}
${variables_endpoint.svc_wq_server_vip}
'''
        def toBeReplacedArray = toBeReplacedStr.split("\\n")
        println toBeReplacedArray
        def testResourcesPath = new File(".").canonicalPath + "\\fs-crm-fmcg-wq-checkins\\src\\test\\resources\\"
        println testResourcesPath
        def sourceFileStr = new File(testResourcesPath + "fs-paas-function-proxy-config.source").text
        toBeReplacedArray.each {sourceOne ->
            sourceFileStr = sourceFileStr.replace(sourceOne,'''http://${variables_endpoint.svc_apibus_global}/fs-appserver-checkins-v2''')
        }

        def file2 = new File(testResourcesPath + "fs-paas-function-proxy-config.target")
        file2.delete()
        file2 << sourceFileStr
    }
}
