package com.facishare.crm.fmcg.wq.test1

import groovy.json.JsonSlurper


/**
 * @program: fs-crm-fmcg-wq
 *
 * @description: ${description}*
 * @author: zhangsm
 *
 * @create: 2022-10-13 11:26
 * */
class GroovyTest {
    /**
     * 自动生成 getSet 方法
     👆代码特点：
     ●
     🐀类默认修饰符是public
     ●
     🐂字段默认修饰符private
     ●
     🐅Getter、Setter运行时动态生成
     ●
     🐇分号可选
     这些Groovy默认约定可以减少样板代码。
     */
    static class GettersSettersDemo {
        public static void main(String[] args) {
            //Java一样的main函数
            Person person = new Person()//new关键字声明对象
            person.setFirstName("Lyta")//调用自动生成的set方法
            person.setLastName("Alexander")
            System.out.println("Person first name is" + person.getFirstName())
            System.out.println("Person last name is " + person.getLastName())//调用自动生成的get方法
        }
    }

    static class GettersSettersDemo2 {
        public static void main(String[] args) {
            Person person = new Person()
            person.firstName = "Marcus"//调用setFirstName
            person.lastName = "Cole"
            println("Person first name is " + person.firstName)//Java使用System.out.println
            println("Person last name is " + person.lastName)
        }


    }
    /**
     * jsp/jstl
     */
    static class GettersSettersDemo3 {
        static void main(String[] args) {
            Person person = new Person()
            person.firstName = "Susan "
            person.lastName = "Ivanova"
            person.rank = "Commander "
            println "Person first name is $person.firstName"//$ JSP/JSTL支持
            println "Person last name is $person.lastName"//非空参数方法括号可以省略
            println "Person rank is $person.rank"
        }
    }

    static class GettersSettersDemo4 {
        def static createName(String firstName, String lastName) {
            return "$lastName, $firstName"
        }

        def static createMilitaryName(def firstName, def lastName, def rank) {
            return "$lastName, $firstName ($rank)"
        }

        static void main(String[] args) {

            def fullName = createName "Susan", "Ivanova"
            println fullName

            def militaryName = createMilitaryName "Susan", "Ivanova", "Commander"
            println militaryName

            Person person1 = new Person(firstName: "firstName", lastName: "lastName", age: 11)
            Person person2 = new Person(firstName: "firstName2", lastName: "lastName2", age: 22)
            println person1.createMessage()


            Map<String, Integer> wordCounts = new HashMap<>();
            wordCounts.put("Hello", 1);//Java方式
            wordCounts.put("Java", 1);
            wordCounts.put("World", 2);

            Map<String, Integer> wordCounts2 = ["Hello": 1, "Groovy": 1, "World": 2]//Groovy初始化赋值
            Map<Person, Integer> staffAddresses = new HashMap<>();//Java
            staffAddresses.put(person1, 1);//Java
            staffAddresses.put(person2, 2);//Java
            /**
             * 对象作为键是需要加圆括号，不加默认为字符串。List和Map类似
             */
            Map<Person, Integer> staffAddresses2 =//Groovy
                    [(person1): 1, (person2): 2]//默认创建LInkedHashMap
            println staffAddresses2
            def staffAddresses3 = [person1: 1, person2: 2]
            println staffAddresses3
            List<String> races = Arrays.asList("Drazi", "Minbari", "Humans")//Java

            List<String> races2 = ["Drazi", "Minbari", "Humans"]//Groovy

            assert races == races2
            /**
             * groovy 初始化数组
             */
            String[] racesArray = ["Drazi", "Minbari", "Humans"]//Groovy合法
//            String[] racesArrayJava = {"Drazi", "Minbari", "Humans"}//Java


            List<String> humanShips = ["Condor", "Explorer"]
            assert humanShips.get(0) == "Condor"//Java方式
            assert humanShips[0] == "Condor"//Groovy方式

            humanShips.add("Hyperion")//Java方式添加元素
            humanShips << "sss"
            Map<String, Integer> map = ["s": 1, "ss": 2]
            List<String> list = ["1", "2"]
            String[] array = ["1", "2"]
            map["s"] = 3
            list << 3
            println "----------------array  $array"
            println "----------------map  $map"
            println "----------------list  $list"
            /**
             * Groovy方式添加元素
             */
            humanShips << "Nova" << "Olympus"//Groovy方式添加元素

            assert humanShips[3] == "Nova"
            assert humanShips[4] == "Olympus"

            humanShips[3] = "Omega"
            assert humanShips[3] == "Omega"

            Map<String, String> personRoles = [:]//空Map
            personRoles.put("Suzan Ivanova", "Lt. Commander")//Java
            personRoles["Stephen Franklin"] = "Doctor"//Groovy

            assert personRoles.get("Suzan Ivanova") == "Lt. Commander"//Java
            assert personRoles["Stephen Franklin"] == "Doctor"//Groovy

            personRoles["Suzan Ivanova"] = "Commander"//Groovy方式覆盖
            assert personRoles["Suzan Ivanova"] == "Commander"

        }
    }

    class DefDemoSpec extends spock.lang.Specification {

        public void trivialSum1() {//Java方式声明方法

            when: "number is one"
            int number = 1;//分号
            then: "number plus number is two"
            number + number == 2;
        }

        def trivialSum2() {//Groovy方式声明方法

            when: "number is one"
            int number = 1//分号可选
            then: "number plus number is two"
            number + number == 2

        }

        def "Testing a trivial sum"() {//字符串式方法名

            when: "number is one"
            def number = 1//对象类型可选
            then: "number plus number is two"
            number + number == 2

        }

    }

    static class GroovyStrings {
        static void main(String[] args) {
            SimpleDepartment sales =
                    new SimpleDepartment(name: "Sales", location: ["block B", "block C"])//基于映射构造函数
            SimpleDepartment sales2 =
                    new SimpleDepartment(name: "Sales", location: ["block B2", "block C2"])//基于映射构造函数

            SimpleEmployee employee =
                    new SimpleEmployee(fullName: "Andrew Collins", age: 37, department: [sales, sales2])

            System.out.println("Age is " + employee.getAge())//Java方式访问字段
            println "Age is $employee.age"

            System.out.println("Department location is at " + employee.getDepartment()[0].getLocation())
            println "Department location is at $employee.department.location"//Groovy方式访问字段

            println "Person is adult ${employee.age > 18}"//{}计算表达式
            println "Amount in dollars is \$300"
            println "Amount in dollars is \$300"
            println 'Person is adult ${employee.age > 18}'//单引号单纯代表字符串


            String input = '''I want you to know you were right. I didn't want \
    to admit that. Just pride I guess. You get my age, you \
    get kinda set in your ways. It had to be \
    done. Don't blame yourself for what happened later.'''
//            def jsonRoot =
//                    new JsonSlurper().parse(new File('src/main/resources/employee-data.json'))
//
//            assert jsonRoot.staff.department.name =="sales"
//            assert jsonRoot.staff.department.employee.size() ==2
//            assert jsonRoot.staff.department.employee[0].firstName =="Orlando"
//            assert jsonRoot.staff.department.employee[0].lastName =="Boren"
//            assert jsonRoot.staff.department.employee[0].age =="24"
//            assert jsonRoot.staff.department.employee[1].firstName =="Diana"
//            assert jsonRoot.staff.department.employee[1].lastName =="Colgan"
//            assert jsonRoot.staff.department.employee[1].age =="28"
            /**
             * groovy闭包
             过滤指定类型图片。
             其他可用方法：
             ●
             🐀any(closure)：至少一个满足
             ●
             🐂find(closure)：第一个满足条件的元素
             ●
             🐅findAll(closure)：所有满足条件的元素
             every(closure)：验证每一个元素

             */
            Closure simple = { int x -> return x * 2 }//完整闭包语法
            assert simple(3) == 6//闭包方法

            def simpler = { x -> x * 2 }//省略return
            assert simpler(3) == 6

            def twoArguments = { x, y -> x + y }//两个参数
            assert twoArguments(3, 5) == 8

            List<Integer> testData = [1, 2, 3, 4, 5, 6]
            println testData.every { it - it < 7 }
            println testData.find { it - it % 3 == 0 }
            println testData.findAll { it - it % 3 == 0 }
            println testData.any { it - it % 3 == 0 }

            ObjectGraphBuilder builder = new ObjectGraphBuilder()
            builder.classNameResolver = "com.facishare.crm.fmcg.wq.test1"//设置领域对象包

            AssetInventory shipRegistry = builder.assetInventory() {
                ship(name: "Sea Spirit", destination: "Chiba") {

                    crewMember(firstName: "Michael", lastName: "Curiel", age: 43)//基于映射的构造函数
                    crewMember(firstName: "Sean", lastName: "Parker", age: 28)
                    crewMember(firstName: "Lillian ", lastName: "Zimmerman", age: 32)

                    cargo(type: "Cotton", tons: 5.4) {
                        cargoOrder(buyer: "ReiHosokawa", city: "Yokohama", price: 34000)//创建子节点关联父节点
                    }

                    cargo(type: "Olive Oil", tons: 3.0) {
                        cargoOrder(buyer: "HirokumiKasaya", city: "Kobe", price: 27000)
                    }

                }
                ship(name: "Calypso I", destination: "Bristol") {

                    crewMember(firstName: "Eric", lastName: "Folkes", age: 35)
                    crewMember(firstName: "Louis", lastName: "Lessard", age: 22)

                    cargo(type: "Oranges", tons: 2.4) {
                        cargoOrder(buyer: "GregorySchmidt", city: "Manchester", price: 62000)
                    }
                }

                ship(name: "Desert Glory", destination: "Los Angeles") {

                    crewMember(firstName: "Michelle", lastName: "Kindred", age: 38)
                    crewMember(firstName: "Kathy", lastName: "Parker", age: 21)

                    cargo(type: "Timber", tons: 4.8) {
                        cargoOrder(buyer: "CarolynCox", city: "Sacramento", price: 18000)
                    }
                }
            }
            assert shipRegistry.ships.size() == 3
            assert shipRegistry.ships[0].name == "Sea Spirit"
            assert shipRegistry.ships[1].crewMembers.size() == 2
            assert shipRegistry.ships[1].crewMembers[0].firstName == "Eric"
            assert shipRegistry.ships[2].cargos[0].type == "Timber"
            assert shipRegistry.ships[2].cargos[0].cargoOrder.city == "Sacramento"
        }
    }

    static class CoolantSensorSpec extends spock.lang.Specification {
        def "If current temperature difference is within limits everything is ok"() {
            given: "that temperature readings are within limits"
            TemperatureReadings prev = new TemperatureReadings(sensor1Data: 20, sensor2Data: 40, sensor3Data: 80)//预设数据
            TemperatureReadings current = new TemperatureReadings(sensor1Data: 30, sensor2Data: 45, sensor3Data: 73);

            TemperatureReader reader = Stub(TemperatureReader)//虚拟接口实现

            reader.getCurrentReadings() >>> [prev, current]//指定返回值

            TemperatureMonitor monitor = new TemperatureMonitor(reader)//注入虚拟对象

            when: "we ask the status of temperature control"
            monitor.readSensor()//调用方法
            monitor.readSensor()

            then: "everything should be ok"
            monitor.isTemperatureNormal()//断言
        }

        def "If current temperature difference is more than 20 degrees the alarm should sound"() {
            given: "that temperature readings are not within limits"
            TemperatureReadings prev = new TemperatureReadings(sensor1Data: 20, sensor2Data: 40, sensor3Data: 80)
            TemperatureReadings current = new TemperatureReadings(sensor1Data: 30, sensor2Data: 10, sensor3Data: 73);
            TemperatureReader reader = Stub(TemperatureReader)

            reader.getCurrentReadings() >>> [prev, current]
            TemperatureMonitor monitor = new TemperatureMonitor(reader)

            when: "we ask the status of temperature control"
            monitor.readSensor()
            monitor.readSensor()

            then: "the alarm should sound"
            !monitor.isTemperatureNormal()
        }

        def "If current temperature difference is more than 20 degrees the alarm sounds"() {

            given: "that temperature readings are not within limits"
            TemperatureReadings prev = new TemperatureReadings(sensor1Data: 20, sensor2Data: 40, sensor3Data: 80)
            TemperatureReadings current = new TemperatureReadings(sensor1Data: 30, sensor2Data: 10, sensor3Data: 73);

            TemperatureReader reader = Stub(TemperatureReader)// stub
            reader.getCurrentReadings() >>> [prev, current]

            ReactorControl control = Mock(ReactorControl)// mock

            ImprovedTemperatureMonitor monitor = new ImprovedTemperatureMonitor(reader, control)//注入stub和mock

            when: "we ask the status of temperature control"
            monitor.readSensor()//mock方法被调用
            monitor.readSensor()//

            then: "the alarm should sound"
            0 * control.shutdownReactor()//验证模拟对象被调用
            1 * control.activateAlarm()

        }

        def "If current temperature difference is more than 50 degrees the reactor shuts down"() {

            given: "that temperature readings are not within limits"
            TemperatureReadings prev = new TemperatureReadings(sensor1Data: 20, sensor2Data: 40, sensor3Data: 80)
            TemperatureReadings current = new TemperatureReadings(sensor1Data: 30, sensor2Data: 10, sensor3Data: 160);

            TemperatureReader reader = Stub(TemperatureReader)
            reader.getCurrentReadings() >>> [prev, current]

            ReactorControl control = Mock(ReactorControl)

            ImprovedTemperatureMonitor monitor = new ImprovedTemperatureMonitor(reader, control)

            when: "we ask the status of temperature control"
            monitor.readSensor()
            monitor.readSensor()

            then: "the alarm should sound and the reactor should shut down"
            1 * control.shutdownReactor()
            1 * control.activateAlarm()
        }

        def "Testing of all 3 sensors with temperatures that rise and fall"() {

            given: "various temperature readings"
            TemperatureReadings prev = new TemperatureReadings(sensor1Data: previousTemp[0], sensor2Data: previousTemp[1], sensor3Data: previousTemp[2])//取温度参数
            TemperatureReadings current = new TemperatureReadings(sensor1Data: currentTemp[0], sensor2Data: currentTemp[1], sensor3Data: currentTemp[2]);//取温度参数

            TemperatureReader reader = Stub(TemperatureReader)//stub
            reader.getCurrentReadings() >>> [prev, current]//指定返回值

            ReactorControl control = Mock(ReactorControl)//mock

            ImprovedTemperatureMonitor monitor = new ImprovedTemperatureMonitor(reader, control)//注入reader、control

            when: "we ask the status of temperature control"
            monitor.readSensor()//调用
            monitor.readSensor()

            then: "the alarm should sound and the reactor should shut down if needed "
            shutDown * control.shutdownReactor()//参数化验证
            alarm * control.activateAlarm()//参数化验证

            where: "possible temperatures are:"
            previousTemp | currentTemp        || alarm | shutDown//参数变量
            [20, 30, 40] | [25, 15, 43.2]     || 0     | 0
            [20, 30, 40] | [13.3, 37.8, 39.2] || 0     | 0
            [20, 30, 40] | [50, 15, 43.2]     || 1     | 0
            [20, 30, 40] | [-20, 15, 43.2]    || 1     | 0
            [20, 30, 40] | [100, 15, 43.2]    || 1     | 1
            [20, 30, 40] | [-80, 15, 43.2]    || 1     | 1
            [20, 30, 40] | [20, 55, 43.2]     || 1     | 0
            [20, 30, 40] | [20, 8, 43.2]      || 1     | 0
            [20, 30, 40] | [21, 100, 43.2]    || 1     | 1
            [20, 30, 40] | [22, -40, 43.2]    || 1     | 1
            [20, 30, 40] | [20, 35, 76]       || 1     | 0
            [20, 30, 40] | [20, 31, 13.2]     || 1     | 0
            [20, 30, 40] | [21, 33, 97]       || 1     | 1
            [20, 30, 40] | [22, 39, -22]      || 1     | 1
        }
    }

}
 class Product {//产品
    private String name;
    private int price;
    private int weight;
}

 class Basket {//购物车

    public void addProduct(Product product) {//添加产品
        addProduct(product,1);
    }
    public void addProduct(Product product, int times) {
    }

    public int getCurrentWeight() {//计重
    }

    public int getProductTypesCount() {//计算产品种类
    }
}

class TemperatureReadings {

    double sensor1Data
    double sensor2Data
    double sensor3Data

}

public interface TemperatureReader {//接口
    TemperatureReadings getCurrentReadings();//被调用方法
}

public class TemperatureMonitor {//被测试类

    private final TemperatureReader reader;//注入
    private TemperatureReadings lastReadings;//上一次读取数据
    private TemperatureReadings currentReadings;//当前数据

    public TemperatureMonitor(final TemperatureReader reader) {
        this.reader = reader;//构造器注入
    }

    public boolean isTemperatureNormal()//被测试方法
    {
        Math.max(Math.abs(lastReadings.sensor1Data - currentReadings.sensor1Data), Math.max(Math.abs(lastReadings.sensor2Data - currentReadings.sensor2Data), Math.abs(lastReadings.sensor3Data - currentReadings.sensor3Data))) <= 20
    }

    public void readSensor() {//定时调用
        lastReadings = currentReadings;
        currentReadings = reader.getCurrentReadings();
    }
}

public class ReactorControl {//反应堆控制程序
    public void activateAlarm() {
    }

    public void shutdownReactor() {
    }
}

public class ImprovedTemperatureMonitor {//被测试类

    private final TemperatureReader reader;//注入字段
    private TemperatureReadings lastReadings;
    private TemperatureReadings currentReadings;
    private final ReactorControl reactorControl;//注入字段

    public ImprovedTemperatureMonitor(final TemperatureReader reader, final ReactorControl reactorControl) {
        this.reactorControl = reactorControl;
        this.reader = reader;
    }

    private boolean isTemperatureDiffMoreThan(long degrees) {

        lastReadings == null ? Boolean.FALSE : Math.max(Math.abs(lastReadings.sensor1Data - currentReadings.sensor1Data), Math.max(Math.abs(lastReadings.sensor2Data - currentReadings.sensor2Data), Math.abs(lastReadings.sensor3Data - currentReadings.sensor3Data))) > degrees
    }

    public void readSensor() {

        lastReadings = currentReadings;
        currentReadings = reader.getCurrentReadings();

        if (isTemperatureDiffMoreThan(20)) {
            reactorControl.activateAlarm();//执行外部方法
        }

        if (isTemperatureDiffMoreThan(50)) {
            reactorControl.shutdownReactor();//执行外部方法
        }
    }
}

public class AssetInventory {
    private List<Ship> ships = new ArrayList<>();
}

public class Ship {
    private String name;
    private List<CrewMember> crewMembers = new ArrayList<>();
    private String destination;
    private List<Cargo> cargos = new ArrayList<>();
}

public class CrewMember {
    private String firstName;
    private String lastName;
    private int age;
}

public class Cargo {
    private String type;
    private CargoOrder cargoOrder;
    private float tons;
}

public class CargoOrder {
    private String buyer;
    private String city;
    private BigDecimal price;
}

class SimpleEmployee {
    String fullName
    int age
    SimpleDepartment[] department
}

class SimpleDepartment {
    String name
    List<String> location
}

class Person {//Person.groovy
    String firstName //字段可不加分号
    String lastName
    int age
    /**
     * 隐藏return
     * @return
     */
    public String createMessage() {
//        return "$firstName +++++++++++ $lastName"
        "$firstName +++++++++++ $lastName"
    }
}
