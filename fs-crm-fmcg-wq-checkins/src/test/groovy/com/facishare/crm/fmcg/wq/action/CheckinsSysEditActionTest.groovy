package com.facishare.crm.fmcg.wq.action

import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.metadata.ActionContextExt
import com.facishare.paas.metadata.util.SpringUtil
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PowerMockIgnore
import org.springframework.context.ApplicationContext
import spock.lang.*
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.spockframework.runtime.Sputnik;

/**
 * @program: fs-crm-fmcg-wq
 *
 * @description:
 *
 * @author: zhangsm
 *
 * @create: 2022-10-20 17:42
 * */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
// 需替换成所引入的静态类
@PrepareForTest([RequestContextManager.class,SpringUtil.class])
@SuppressStaticInitializationFor(["com.facishare.paas.appframework.core.model.RequestContextManager","com.facishare.paas.metadata.util.SpringUtil"])
//忽略
@PowerMockIgnore(["com.sun.org.apache.xerces.internal.jaxp.*", "ch.qos.logback.*", "org.slf4j.*"])
class CheckinsSysEditActionTest extends Specification {
    CheckinsSysEditAction testObj
    def requestContext = Mock(RequestContext)
    def applicationContext = Mock(ApplicationContext)
    def checkinsProxy = Mock(CheckinsProxy)

    def setup() {
        // 需Mock成所引入的静态类
        PowerMockito.mockStatic(RequestContextManager.class)
        PowerMockito.mockStatic(SpringUtil.class)
        PowerMockito.when(RequestContextManager.getContext()).thenReturn(requestContext)
        PowerMockito.when(SpringUtil.getContext()).thenReturn(applicationContext)
        applicationContext.getBean(CheckinsProxy.class) >> checkinsProxy
        testObj  = new CheckinsSysEditAction()

    }

    @Unroll
    def "before"() {
        when:

        testObj.before(arg)

        then: "验证返回结果里属性值是否符合预期"
        setAttribute
        context.getAttribute(ActionContextExt.NOT_VALIDATE)
        where: "表格方式验证多种分支调用场景"
        arg  || noVolidate
        new com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction.Arg() || true
    }

}

