//package com.facishare.crm.fmcg.wq.test1;
//
//import org.junit.Assert;
//import org.junit.Test;
//
///**
// * @program: fs-crm-fmcg-wq
// * @description:
// * @author: zhangsm
// * @create: 2022-09-29 18:41
// **/
//public class JunitTest {
//
//    @Test
//    public void simpleTest() {//场景1
//        SpockTest.Adder adder = new SpockTest.Adder();//初始化
//        Assert.assertEquals("1 + 1 is 2", 2, adder.add(1, 1));//JUnit断言：1+1=2
//        String[] racesArrayJava = {"Drazi", "Minbari", "Humans"};
//    }
//
//    @Test
//    public void orderTest() {//场景2，测试顺序的影响
//        SpockTest.Adder adder = new SpockTest.Adder();//初始化
//        Assert.assertEquals("Order does not matter ", 5, adder.add(2, 3));//断言：2+3=5
//        Assert.assertEquals("Order does not matter ", 5, adder.add(3, 2));//断言：3+2=5
//    }
//    @Test
//    public void orderTestError() {//场景2，测试顺序的影响
//        SpockTest.Adder adder = new SpockTest.Adder();//初始化
//        Assert.assertEquals("Order does not matter ", 6, adder.add(2, 3));//断言：2+3=5
//        Assert.assertEquals("Order does not matter ", 7, adder.add(3, 2));//断言：3+2=5
//    }
//}
