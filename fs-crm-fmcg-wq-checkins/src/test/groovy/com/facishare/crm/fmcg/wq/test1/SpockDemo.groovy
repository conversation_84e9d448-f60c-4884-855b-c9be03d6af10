package com.facishare.crm.fmcg.wq.test1


/**
 * @program: fs-crm-fmcg-wq
 *
 * @description: ${description}*
 * @author: zhangsm
 *
 * @create: 2022-09-29 18:20
 * */
class SpockDemo extends spock.lang.Specification {

    public static class Adder {

        public int add(int a, int b) {
            return a + b;
        }

    }



    def "Adding two numbers to return the sum"() {//自然语言命名的方法

        when: "a new Adder class is created"//when块描述场景
        def adder = new Adder();//初始化加法器

        then: "1 plus 1 is 2"//then块验证
        adder.add(1, 1) == 2

    }

    def "Order of numbers does not matter"() {//另一场景

        when: "a new Adder class is created"
        def adder = new Adder();

        then: "2 plus 3 is 5"
        adder.add(2, 3) == 5

        and: "3 plus 2 is also 5"//and块是then块的补充
        adder.add(3, 2) == 5
        and: "3 plus 2 is also 5"//and块是then块的补充
        adder.add(3, 2) == 6
        and: "3 plus 2 is also 5"//and块是then块的补充
        adder.add(3, 2) == 7
    }

}
