package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CheckinsImgSysAddAction extends StandardAddAction {

//    @Override
//    protected void before(Arg arg) {
//        this.actionContext.setAttribute("not_validate",true);
//        super.before(arg);
//
//    }

    @Override
    protected void doSaveData() {
        ActionContextExt actionContextExt = ActionContextExt.of(actionContext.getUser());
        actionContextExt.set("not_validate", true);
        objectData = serviceFacade.saveObjectData(actionContext.getUser(), objectData, actionContextExt.getContext());
    }

}

