package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.CheckinsUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;

import java.util.List;
import java.util.stream.Collectors;

public class MileageStatisticsDetailRelatedListController extends StandardRelatedListController {

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        List<ObjectDataDocument> dataDocumentList = result.getDataList();
        dataDocumentList = CheckinsUtils.formatDataList(dataDocumentList);
        if (null != dataDocumentList)
            result.setDataList(dataDocumentList);

        result.getButtonInfo().setButtons(result.getButtonInfo().getButtons().stream().filter(o->(!"Abolish_button_default".equals(o.get("api_name")))&&(!"custom".equals(o.get("action_type")))).collect(Collectors.toList()));
        return result;
    }
}
