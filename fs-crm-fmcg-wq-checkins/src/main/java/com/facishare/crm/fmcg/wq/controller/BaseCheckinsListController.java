package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.CheckinsUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import org.apache.commons.collections.CollectionUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class BaseCheckinsListController extends FmcgStandardListController {
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ObjectDescribeDocument objectDescribeDocument = result.getObjectDescribe();
        objectDescribeDocument = CheckinsUtils.clearObjectDescribeDocument(objectDescribeDocument);
        if (null != objectDescribeDocument)
            result.setObjectDescribe(objectDescribeDocument);
        //清除外勤图片对象的name
        List<ObjectDataDocument> dataDocumentList = result.getDataList();
//        log.info("BaseCheckinsListController size {}",result.getDataList().size());
        dataDocumentList = CheckinsUtils.formatDataListName(dataDocumentList);
        if (null != dataDocumentList)
            result.setDataList(dataDocumentList);
        return result;
    }
}
