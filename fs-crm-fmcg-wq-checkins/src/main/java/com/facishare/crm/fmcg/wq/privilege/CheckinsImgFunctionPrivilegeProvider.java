package com.facishare.crm.fmcg.wq.privilege;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.privilege.model.DefaultFunctionPrivilegeProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangsm on 2018/4/24/0024.
 * 初始化方法
 */
@Component
public class CheckinsImgFunctionPrivilegeProvider extends DefaultFunctionPrivilegeProvider {

    List<String> supportActionCodes = Lists.newArrayList();

    {
        for (String actionCode : super.getSupportedActionCodes()) {

            if(!actionCode.equals(ObjectAction.RELATE.getActionCode())&&
                    (!actionCode.equals(ObjectAction.CLONE.getActionCode()))){
                supportActionCodes.add(actionCode);
            }

        }

    }


    @Override
    public String getApiName() {
        return "CheckinsImgObj" ;
    }

    @Override
    public Map<String, List<String>> getCustomInitRoleActionCodes() {
        Map<String, List<String>> actionCodeMap = Maps.newHashMap();
        //外勤角色
        actionCodeMap.put("00000000000000000000000000000026", Collections.unmodifiableList(getSupportedActionCodes()));
        //crm观察者
        actionCodeMap.put("00000000000000000000000000000009", Collections.unmodifiableList(getSupportedActionCodes()));
        //销售人员
        actionCodeMap.put("00000000000000000000000000000015", Collections.unmodifiableList(getSupportedActionCodes()));
        //售后人员
        actionCodeMap.put("00000000000000000000000000000014", Collections.unmodifiableList(getSupportedActionCodes()));
        //服务人员
        actionCodeMap.put("00000000000000000000000000000010", Collections.unmodifiableList(getSupportedActionCodes()));


        return Collections.unmodifiableMap(actionCodeMap);
    }

    @Override
    public  List<String> getSupportedActionCodes() {
        return Collections.unmodifiableList(supportActionCodes);
    }

}
