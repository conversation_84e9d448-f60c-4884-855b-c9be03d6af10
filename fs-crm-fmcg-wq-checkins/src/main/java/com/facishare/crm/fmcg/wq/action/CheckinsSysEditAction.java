package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Slf4j
public class CheckinsSysEditAction extends StandardEditAction {

    private CheckinsProxy checkinsProxy =
            SpringUtil.getContext().getBean(CheckinsProxy.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }
    @Override
    protected void before(Arg arg) {
        RequestContextManager.getContext().setAttribute(ActionContextExt.NOT_VALIDATE, true);
        super.before(arg);
    }
    @Override
    protected void validateLookupData(IObjectData objectData, IObjectDescribe describe) {
        String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
        if (!FmcgGray.Checkins.EA.noValidateCoustomerIdLookup.gray(ea)) {
            super.validateLookupData(objectData, describe);
        }
    }
    @Override
    protected boolean needTriggerApprovalFlow() {
        log.debug("CheckinsSysEditAction needTriggerApprovalFlow is start!");
        ObjectDataDocument objectDataDocument = arg.getObjectData();
        if (!Objects.isNull(objectDataDocument)) {
            IObjectData iObjectData = objectDataDocument.toObjectData();
            if (!Objects.isNull(iObjectData.get("check_id"))) {
                String checkinsId = iObjectData.get("check_id").toString();
                if (StringUtils.isNotEmpty(checkinsId)) {
                    String outTenantId = iObjectData.get("out_tenant_id", String.class);
                    String enterpriseAccount = iObjectData.get("enterprise_account", String.class);
                    long checkinTime = checkinsProxy.getCheckinsById(actionContext.getTenantId(),enterpriseAccount, outTenantId, checkinsId);
                    if (checkinTime != -1) {
                        if (checkinTime == 0) {
                           boolean result = super.needTriggerApprovalFlow();
                            log.debug("CheckinsSysEditAction needTriggerApprovalFlow  supper,result {}",result);
                           return result;
                        }
                    }
                }
            }
        }
        return false;
    }
    //编辑的时候没有startCallBack回调 ，需要在这里处理逻辑
    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        if (isEditApprovalFlowStartSuccess()) {
            ObjectDataDocument objectDataDocument = result.getObjectData();
            if (!Objects.isNull(objectDataDocument)) {
                IObjectData iObjectData = objectDataDocument.toObjectData();
                String checkId = iObjectData.get("check_id", String.class);
                String outTenantId = iObjectData.get("out_tenant_id", String.class);
                String enterpriseAccount = iObjectData.get("enterprise_account", String.class);
                log.info("CheckinsSysEditAction checkId:{},outTenantId:{},enterpriseAccount:{}", checkId, outTenantId, enterpriseAccount);
                checkinsProxy.handleCheckinStatus(actionContext.getTenantId(),enterpriseAccount, outTenantId, checkId);
            }

        }
        return result;
    }
}
