package com.facishare.crm.fmcg.wq.button;

import com.facishare.crm.fmcg.wq.CheckinsDefaultObject;
import com.facishare.crm.fmcg.wq.util.ButtonUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.ArrayList;
import java.util.List;

public abstract class AbstractCheckinSpecialButtonProvider implements SpecialButtonProvider {
    @Override
    public String getApiName() {
        return CheckinsDefaultObject.Checkins.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        List<IButton> buttons = new ArrayList<>(1);
        buttons.add(ButtonUtils.buildButton(ObjectAction.JUMP_SHOP));
        buttons.add(ButtonUtils.buildButton(ObjectAction.CREATE_OUT_PLAN));
        return buttons;
    }

}
