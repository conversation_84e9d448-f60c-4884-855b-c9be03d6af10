package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.predef.action.StandardFlowStartCallbackAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


/**
 * Created by hejy on 2018/5/17.
 * 触发了审批流的回调
 * 审批开始
 */
@Slf4j
public class CheckinsFlowStartCallbackAction extends StandardFlowStartCallbackAction {
    private CheckinsProxy checkinsProxy =
            SpringUtil.getContext().getBean(CheckinsProxy.class);


    @Override
    protected void updateDataLifeStatus() {
        ObjectLifeStatus lifeStatus = ObjectDataExt.of(this.objectData).getLifeStatus();
        //如果是这种触发的走特殊处理 其他情况用supper方法
        if (ObjectLifeStatus.NORMAL == lifeStatus && this.triggerType == ApprovalFlowTriggerType.CREATE && ApprovalFlowStartResult.SUCCESS == this.triggerResult){
            List<String> toUpdateFieldList = modifyObjectDataWhenStartApprovalFlow(this.triggerType, this.triggerResult);
            if (CollectionUtils.notEmpty(toUpdateFieldList)) {
                this.serviceFacade.batchUpdateByFields(this.actionContext.getUser(), Lists.newArrayList(new IObjectData[]{this.objectData}), toUpdateFieldList);
            }
        }else{
            super.updateDataLifeStatus();
        }
    }
    public List<String> modifyObjectDataWhenStartApprovalFlow(ApprovalFlowTriggerType type, ApprovalFlowStartResult result) {
        if (!ObjectLifeStatus.INEFFECTIVE.startCallBack(objectData, type, result)) {
            return Lists.newArrayList();
        }

        List<String> fieldsToUpdate = Lists.newArrayList(ObjectLifeStatus.LIFE_STATUS_API_NAME);
        if (ApprovalFlowTriggerType.INVALID.equals(type)) {
            fieldsToUpdate.add(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME);
        }
        return fieldsToUpdate;
    }
    @Override
    protected Result after(Arg arg, Result result) {
        log.debug("CheckinsFlowStartCallbackAction is start!");
        Result result1 = super.after(arg, result);

        if (String.valueOf(ApprovalFlowTriggerType.CREATE.getTriggerTypeCode()).equals(arg.getTriggerType())
            ||String.valueOf(ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode()).equals(arg.getTriggerType())
        ) {
            if(ApprovalFlowStartResult.of(arg.getCode()).equals(ApprovalFlowStartResult.SUCCESS)){
                IObjectData newObjectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getDataId(), objectDescribe.getApiName());
                String checkId = newObjectData.get("check_id", String.class);
                String outTenantId = newObjectData.get("out_tenant_id",String.class);
                String enterpriseAccount = newObjectData.get("enterprise_account",String.class);
                log.info("CheckinsFlowStartCallbackAction checkId:{},outTenantId:{},enterpriseAccount:{}", checkId,outTenantId,enterpriseAccount);
                checkinsProxy.handleCheckinStatus(actionContext.getTenantId(),enterpriseAccount,outTenantId,checkId);

            }
        }
        return result1;
    }


}
