package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class CheckinsSaveCheckAction extends StandardAddAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }
    @Override
    protected Result doAct(Arg arg) {
        Result result = new Result();
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        return result;
    }

    @Override
    protected void finallyDo() {

    }
}
