package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.ui.layout.IButton;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * Created by zhangsm on 2018/4/9/0009.
 */
@Slf4j
public class CheckinsListHeaderController extends BaseCheckinsListHeaderController {

    public static final Set<String> CHECKINS_BUTTON_SET = Sets.newHashSet();

    static {
        CHECKINS_BUTTON_SET.add("Export_button_default");//导出
        CHECKINS_BUTTON_SET.add("ExportFile_button_default");//导出图片/附件
        CHECKINS_BUTTON_SET.add(ObjectAction.CREATE_OUT_PLAN.getButtonApiName());//新建互联计划
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);

        //灰度过滤掉高级外勤对象列表页默认的批量操作按钮，放开自定义批量操作按钮
        boolean removeCheckinsObjDefaultBatchButtons = FmcgGray.Checkins.EI.removeCheckinsObjDefaultBatchButtonsForEIs.gray(objectDescribeExt.getTenantId());
        boolean removeCheckinsObjDefaultBatchButtonsGray = FmcgGray.Checkins.EI.removeCheckinsObjDefaultBatchButtonsForEIs.fsGray(objectDescribeExt.getTenantId());
        //高级外勤批量操作外露指定按钮
        boolean showCheckinsObjAppointedButtons = FmcgGray.Checkins.EI.showCheckinsObjAppointedButtons.gray(objectDescribeExt.getTenantId());
        boolean showCheckinsObjAppointedButtonsGray = FmcgGray.Checkins.EI.showCheckinsObjAppointedButtons.fsGray(objectDescribeExt.getTenantId());
        if (CollectionUtils.notEmpty(result.getButtons())) {
            result.getButtons().removeIf(buttonDocument -> {
                if ((removeCheckinsObjDefaultBatchButtons || removeCheckinsObjDefaultBatchButtonsGray) && (showCheckinsObjAppointedButtons || showCheckinsObjAppointedButtonsGray)) {
                    return !buttonDocument.containsValue(IButton.ACTION_TYPE_CUSTOM) && !CHECKINS_BUTTON_SET.contains(buttonDocument.toLayoutButton().getName());
                } else if (removeCheckinsObjDefaultBatchButtons || removeCheckinsObjDefaultBatchButtonsGray) {
                    return !buttonDocument.containsValue(IButton.ACTION_TYPE_CUSTOM);
                } else if (showCheckinsObjAppointedButtons || showCheckinsObjAppointedButtonsGray) {
                    return !CHECKINS_BUTTON_SET.contains(buttonDocument.toLayoutButton().getName());
                } else {
                    return true;
                }
            });
            //互联计划 移动端先移除 判空
            if(controllerContext.getRequestContext().getClientInfo() != null && !controllerContext.getRequestContext().getClientInfo().startsWith("WEB")){
                result.getLayout().toLayout().getButtons().removeIf(
                        buttonDocument -> {
                            return ObjectAction.CREATE_OUT_PLAN.getButtonApiName().equals(buttonDocument.getName());
                        }
                );
            }
        }

        return result;
    }
}
