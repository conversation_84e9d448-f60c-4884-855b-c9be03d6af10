package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.exception.CheckinsErrorCode;
import com.facishare.crm.fmcg.wq.exception.CheckinsException;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Slf4j
public class CheckinsObjExceptionAction extends BaseObjectSaveAction<BaseObjectSaveAction.Arg> {

    @Override
    protected void before(Arg arg) {
        throw new CheckinsException(CheckinsErrorCode.GO_TO_WAIQIN.getMessage(), CheckinsErrorCode.GO_TO_WAIQIN);
    }


    @Override
    protected String getIRule() {
        return null;
    }

    @Override
    protected ObjectAction getObjectAction() {
        return null;
    }

    @Override
    protected void checkUniquenessRule() {

    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected Result doAct(Arg arg) {
        return null;
    }
}
