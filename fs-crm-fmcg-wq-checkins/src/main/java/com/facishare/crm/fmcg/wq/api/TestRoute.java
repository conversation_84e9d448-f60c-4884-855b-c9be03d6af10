package com.facishare.crm.fmcg.wq.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.api.model.BaseResult;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 用来验证 路由是否正确的请求
 * @author: zhangsm
 * @create: 2023-01-11 19:11
 **/
public interface TestRoute {
    @Data
    @ToString
    class Arg implements Serializable {
        String ea;
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper=true)
    class Result extends BaseResult {
        String env;
        String appName;
    }
}
