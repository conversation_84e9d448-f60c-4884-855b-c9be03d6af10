package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.CheckinsUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public class BaseCheckinsRelatedListController extends StandardRelatedListController {
    @Override
    protected Result after(Arg arg, Result result) {
//        log.info("BaseCheckinsRelatedListController -- arg{}",arg);
        result = super.after(arg, result);
        ObjectDescribeDocument objectDescribeDocument = result.getObjectDescribe();
//        if(Objects.nonNull(objectDescribeDocument)){
//            IObjectDescribe iObjectDescribe = objectDescribeDocument.toObjectDescribe();
//            iObjectDescribe.set("actions", Lists.newArrayList());
//            result.setObjectDescribe(ObjectDescribeDocument.of(iObjectDescribe));
//        }
        List<ObjectDataDocument> dataDocumentList = result.getDataList();
        dataDocumentList = CheckinsUtils.formatDataListName(dataDocumentList);
        if (null != dataDocumentList)
            result.setDataList(dataDocumentList);
        return result;
    }
}
