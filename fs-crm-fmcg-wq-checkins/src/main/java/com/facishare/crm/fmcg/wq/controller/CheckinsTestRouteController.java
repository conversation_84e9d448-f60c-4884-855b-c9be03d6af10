package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.api.TestRoute;
import com.facishare.paas.appframework.core.model.PreDefineController;

import java.util.List;

/**
 * Created by zhangsm on 2018/4/8/0008.
 */
public class CheckinsTestRouteController extends PreDefineController<TestRoute.Arg,TestRoute.Result> {

    @Override
    protected void before(TestRoute.Arg arg) {

    }

    @Override
    protected TestRoute.Result after(TestRoute.Arg arg, TestRoute.Result result) {
        return result;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected TestRoute.Result doService(TestRoute.Arg arg) {
        log.info("testRoute from : {},env {}",arg.getEa(),System.getProperty("process.profile"));
        TestRoute.Result result = new TestRoute.Result();
        result.setEnv(System.getProperty("process.profile"));
        result.setAppName(System.getProperty("app.name"));
        return result;
    }

}
