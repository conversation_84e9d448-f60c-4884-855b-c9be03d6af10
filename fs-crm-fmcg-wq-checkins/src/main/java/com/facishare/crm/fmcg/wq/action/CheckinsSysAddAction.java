package com.facishare.crm.fmcg.wq.action;

import com.facishare.appserver.checkins.model.enums.CustomGray;
import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhangsm on 2018/4/9/0009.
 */
@Slf4j
public class CheckinsSysAddAction extends StandardAddAction {

    private CheckinsProxy checkinsProxy =
            SpringUtil.getContext().getBean(CheckinsProxy.class);

    public final static String customerIdApiName = "customer_id";

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected void doSaveData() {
        ActionContextExt actionContextExt = ActionContextExt.of(actionContext.getUser());
        actionContextExt.set("not_validate", true);
        objectData = serviceFacade.saveObjectData(actionContext.getUser(), objectData, actionContextExt.getContext());
    }
    @Override
    protected void validateLookupData(IObjectData objectData, IObjectDescribe describe){
        String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
        if (!FmcgGray.Checkins.EA.noValidateCoustomerIdLookup.gray(ea)){
            super.validateLookupData(objectData,describe);
        }
    }


    @Override
    protected void triggerApprovalFlow(){
        ObjectDataDocument objectDataDocument = arg.getObjectData();
        if (!Objects.isNull(objectDataDocument)){
            IObjectData iObjectData = objectDataDocument.toObjectData();
            if (!Objects.isNull(iObjectData.get("check_id"))){
                String checkinsId = iObjectData.get("check_id").toString();
                if(StringUtils.isNotEmpty(checkinsId)){
                    String outTenantId = iObjectData.get("out_tenant_id",String.class);
                    String enterpriseAccount = iObjectData.get("enterprise_account",String.class);
                    long checkinTime= checkinsProxy.getCheckinsById(actionContext.getTenantId(),enterpriseAccount,outTenantId,checkinsId);
                    if(checkinTime!=-1) {
                        if(checkinTime==0){
                            super.triggerApprovalFlow();
                        }else{
                            ArrayList fieldsToUpdate = Lists.newArrayList(new String[]{"life_status"});
                            iObjectData.set("life_status", "normal");
                            serviceFacade.batchUpdateByFields(this.actionContext.getUser(), Lists.newArrayList(iObjectData), fieldsToUpdate);
                        }
                    }else if(GrayRelease.isAllow("checkin-server-v2", "noCheckinsUpdateLifeStatus", actionContext.getTenantId())){
                       iObjectData.set("life_status", "normal");
                       serviceFacade.batchUpdateByFields(this.actionContext.getUser(), Lists.newArrayList(iObjectData), Lists.newArrayList(new String[]{"life_status"}));
                    }



                }
            }
        }
    }
}
