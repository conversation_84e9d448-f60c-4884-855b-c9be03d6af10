package com.facishare.crm.fmcg.wq;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

/**
 * Created by zhangsm on 2018/4/8/0008.
 */
@Slf4j
public enum CheckinsDefaultObject implements PreDefineObject {
    CheckinsImg( "CheckinsImgObj"),
    Checkins( "CheckinsObj"),
    CheckinsImgDetail( "CheckinsImgDetailObj"),
    AIMainObj( "AIMainObj"),
    RemakeMonitoringRecord("RemakeMonitoringRecordObj"),
    MileageStatistics("MileageStatisticsObj"),
    MileageStatisticsDetail("MileageStatisticsDetailObj");
    private final String apiName;

    private static final String PACKAGE_NAME = CheckinsDefaultObject.class.getPackage().getName();

    CheckinsDefaultObject(String apiName) {
        this.apiName = apiName;
    }

    public static CheckinsDefaultObject getEnum(String apiName) {
        List<CheckinsDefaultObject> list = Arrays.asList(CheckinsDefaultObject.values());
        return list.stream().filter(m -> m.getApiName().equalsIgnoreCase(apiName)).findAny().orElse(null);
    }

    public static void init() {
        for (CheckinsDefaultObject object : CheckinsDefaultObject.values()) {
            log.info("init {}", object.toString());
            PreDefineObjectRegistry.register(object);
        }
    }

    @Override
    public String getApiName() {
        return this.apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }
}
