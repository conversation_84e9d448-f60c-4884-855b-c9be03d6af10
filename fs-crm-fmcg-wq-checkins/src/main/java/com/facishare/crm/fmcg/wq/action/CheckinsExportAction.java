package com.facishare.crm.fmcg.wq.action;

import com.facishare.appserver.utils.DateUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.ListIterator;
import java.util.Objects;


@Slf4j
public class CheckinsExportAction extends StandardExportAction {

    @Override
    protected void fillDataList(User user, IObjectDescribe describe, List<IFieldDescribe> fields, List<IObjectData> dataList) {
        parseDuration(dataList);
        super.fillDataList(user, describe, fields, dataList);
//        Map<String, IFieldDescribe> fieldDescribeMap= describe.getFieldDescribeMap();
//        IFieldDescribe durationField= fieldDescribeMap.get("duration");
//        if(Objects.nonNull(durationField)){
//            durationField.setType("text");
//            parseDuration(dataList);
//        }


    }
    @Override
    protected List<IFieldDescribe> findFields(String describeApiName, String recordType) {
        List<IFieldDescribe> fields = super.findFields(describeApiName, recordType);
        // 创建虚拟字段，apiname要和原number字段一直，类型为text
        IFieldDescribe virtualNumberField = new TextFieldDescribe();
        virtualNumberField.set(IFieldDescribe.API_NAME, "duration");
        virtualNumberField.set(IFieldDescribe.LABEL, "外勤时长(新)"); //ignoreI18n
        // 替换原number字段
        ListIterator<IFieldDescribe> lit = fields.listIterator();
        while (lit.hasNext()) {
            IFieldDescribe field = lit.next();
            if (Objects.equals("duration",field.getApiName())) {
                lit.set(virtualNumberField);
                break;
            }
        }
        return fields;
    }

    private void parseDuration(List<IObjectData> data) {
        if (CollectionUtils.isNotEmpty(data)) {
            for (IObjectData iObjectData : data) {
                if (Objects.nonNull(iObjectData.get("check_out_time")) && Objects.nonNull(iObjectData.get("checkins_time"))) {
                    long check_out_time = (long) iObjectData.get("check_out_time");
                    long checkins_time = (long) iObjectData.get("checkins_time");
                    String time = "";
                    if (check_out_time != 0 && checkins_time != 0) {
                        long duration = check_out_time - checkins_time;
//                        log.info("check_out_time is :{},checkins_time is :{},duration:{}", check_out_time, checkins_time, duration);
                        if (duration >= DateUtils.ONE_DAY) {
                            time = duration / DateUtils.ONE_DAY + "天" + duration % DateUtils.ONE_DAY / DateUtils.ONE_HOUR + "小时" + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR / DateUtils.ONE_MINUTE + "分" + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND+ "秒"; //ignoreI18n
                        } else if (duration >= DateUtils.ONE_HOUR) {
                            time = duration / DateUtils.ONE_HOUR + "小时" + duration % DateUtils.ONE_HOUR / DateUtils.ONE_MINUTE + "分" + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND+ "秒"; //ignoreI18n
                        } else if (duration >= DateUtils.ONE_MINUTE) {
                            time = duration / DateUtils.ONE_MINUTE + "分" + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND+ "秒"; //ignoreI18n
                        } else if (duration >= DateUtils.ONE_SECOND){
                            time = duration / DateUtils.ONE_SECOND + "秒"; //ignoreI18n
                        }else {
                            time = "0秒"; //ignoreI18n
                        }
                        iObjectData.set("duration", time);
                        iObjectData.set("duration_formula", time);
                    }else{
                        iObjectData.set("duration", "--");
                    }

                }else{
                    iObjectData.set("duration", "--");
                }
            }

        }
    }
}
