package com.facishare.crm.fmcg.wq.action;

import com.facishare.appserver.checkins.api.model.BaseResult;
import com.facishare.appserver.checkins.api.model.SaveJumpShop;
import com.facishare.appserver.checkins.api.model.common.ObjectInfo;
import com.facishare.appserver.utils.AccountUtils;
import com.facishare.crm.fmcg.wq.exception.CheckinsException;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.crm.fmcg.wq.util.LogMapUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class CheckinsJumpShopAction  extends AbstractStandardAction<CheckinsJumpShopAction.Arg,CheckinsJumpShopAction.Result> {

    private CheckinsProxy checkinsProxy =
            SpringUtil.getContext().getBean(CheckinsProxy.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    protected Map<String, Object> getArgs() {
        return CollectionUtils.nullToEmpty((this.arg).getArgs());
    }
    @Override
    protected Result doAct(Arg arg) {
        Result result= new Result();
        SaveJumpShop saveJumpShop = new SaveJumpShop();
        saveJumpShop.setCheckId(arg.getObjectDataId());
        String ea = serviceFacade.getEAByEI(actionContext.getTenantId());
        saveJumpShop.setEa(ea);
        saveJumpShop.setFieldData(arg.getArgs());
        saveJumpShop.setRouteId(arg.getRouteId());
        saveJumpShop.setCheckTypeId(arg.getCheckTypeId());
        saveJumpShop.setObjectInfo(arg.getObjectInfo());
        saveJumpShop.setSceneId(arg.getSceneId());
        saveJumpShop.setUserId(actionContext.getUser().getUserIdInt());
        BaseResult res = checkinsProxy.saveJumpInfo(actionContext.getTenantId(),saveJumpShop);
        if(res.getErrorCode() != 0){
            throw new CheckinsException(res.getMessage(),res.getErrorCode());
        }
        result.setHasReturnValue(false);
        handleLogMap(ea,arg.getObjectDataId());
        return result;
    }

    private void handleLogMap(String ea,String checkId) {
        try {
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("secondMethod", "jumpShopAction");
            dataMap.put("biz.checkId", checkId);
            dataMap.put("ea", ea);
            dataMap.put("user", AccountUtils.getUserAccount(ea,actionContext.getUser().getUserIdInt()));
            LogMapUtils.sendPointLog(actionContext, dataMap);
        }catch (Exception e){
            log.error("handleLogMap error:",e);
        }
    }

    @Data
    public static class Arg {
        private String objectDataId;
        private ObjectDataDocument args;
        private String routeId;
        private String sceneId;
        private String checkTypeId;
        private ObjectInfo objectInfo;
    }

    @Data
    public static class Result {
        private boolean hasReturnValue;
    }
}
