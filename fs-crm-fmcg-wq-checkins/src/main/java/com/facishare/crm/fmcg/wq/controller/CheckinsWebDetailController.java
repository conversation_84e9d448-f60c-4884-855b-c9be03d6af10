package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.CheckinsUtils;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

@Slf4j
public class CheckinsWebDetailController extends StandardWebDetailController {
    private CheckinsProxy checkinsProxy =
            SpringUtil.getContext().getBean(CheckinsProxy.class);


    static String NEW_DETAIL_CheckinsInfo_COMPONENT_JSON = "{\n" +
            "  \"buttons\": [],\n" +
            "  \"api_name\": \"CheckinsInfo\",\n" +
            "  \"is_hidden\": false,\n" +
            "  \"header\": \"外勤信息\",\n" +
            "  \"type\": \"group\",\n" +
            "  \"order\": 2\n" +
            "}";

    static String NEW_DETAIL_CheckinsAction_COMPONENT_JSON = "{\"buttons\":[],\"api_name\":\"CheckinsAction\",\"is_hidden\":false,\"header\":\"外勤动作\",\"type\":\"group\",\"order\":3,\"icon\":\"waiqindongzuo\"}"; //ignoreI18n

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("CheckinsWebDetailController");
        LayoutDocument layoutDocument = result.getLayout();

        if (null != layoutDocument) {
            String tenantId = this.controllerContext.getTenantId();
            String ea = serviceFacade.getEAByEI(tenantId);
            if (StringUtils.isEmpty(arg.getLayoutAgentType()) || "web".equals(arg.getLayoutAgentType()) || "mobile".equals(arg.getLayoutAgentType())) {
                log.info("CheckinsWebDetailController agentType:{},layoutType:{}", arg.getLayoutAgentType(), arg.getLayoutType());
                layoutDocument = CheckinsUtils.filterButtons(layoutDocument, arg.getLayoutAgentType(), tenantId);
            }
            ILayout layout = layoutDocument.toLayout();

            try {
                //增加逻辑,如果外勤数据未找到则隐藏这俩页签
                long checkinsTime = checkinsProxy.getCheckinsById(tenantId, ea, data.get("out_tenant_id",String.class), result.getData().getId());
                if (checkinsTime != -1L){
                    String info = I18N.text("oaappsrv.waiqin.msg.checkinsinformation");
                    IComponent component = ComponentFactory.newInstance(StringUtils.isNotEmpty(info) ? NEW_DETAIL_CheckinsInfo_COMPONENT_JSON.replace("外勤信息", info) : NEW_DETAIL_CheckinsInfo_COMPONENT_JSON); //ignoreI18n
                    String action = I18N.text("oaappsrv.waiqin.msg.checkinsaction");
                    IComponent actionComponent = ComponentFactory.newInstance(StringUtils.isNotEmpty(action) ? NEW_DETAIL_CheckinsAction_COMPONENT_JSON.replace("外勤动作", action) : NEW_DETAIL_CheckinsAction_COMPONENT_JSON); //ignoreI18n
                    if (StringUtils.isEmpty(arg.getLayoutAgentType()) || "web".equals(arg.getLayoutAgentType())) {
                        WebDetailLayout.of(layout).addComponents(Lists.newArrayList(actionComponent, component), 0);
                    } else {
                        WebDetailLayout.of(layout).addComponents(Lists.newArrayList(actionComponent), 0);
                    }
                    layout.setDefaultComponent("CheckinsAction");
                }
            } catch (Exception e) {
                log.error("CheckinsWebDetailController addComponents error", e);
            }
            result.setLayout(LayoutDocument.of(layout));
        }

        ObjectDescribeDocument objectDescribeDocument = result.getDescribe();
        objectDescribeDocument = CheckinsUtils.clearObjectDescribeDocument(objectDescribeDocument);
        if (null != objectDescribeDocument)
            result.setDescribe(objectDescribeDocument);
        ObjectDataDocument dataDocument = result.getData();
        dataDocument = CheckinsUtils.formatDetailName(dataDocument);
        if (null != dataDocument)
            result.setData(dataDocument);
        return super.after(arg, result);
    }
}
