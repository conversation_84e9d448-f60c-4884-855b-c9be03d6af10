package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.paas.appframework.core.predef.action.StandardUnlockAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;


@Slf4j
public class CheckinsUnlockAction extends StandardUnlockAction {

    private CheckinsProxy checkinsProxy =
            SpringUtil.getContext().getBean(CheckinsProxy.class);
    @Override
    protected boolean skipCheckButtonConditions() {
        return true;
    }

    /**
     * 跳过按钮的前置执行动作
     *
     * @return
     */
    @Override
    protected boolean skipPreFunction() {
        return true;
    }

    /**
     * 跳过按钮后动作
     *
     * @return
     */
    @Override
    protected boolean skipPostFunction() {
        return true;
    }

    @Override
    protected void before(Arg arg) {
    super.before(arg);
    }

    @Override
    protected Result doAct(Arg arg) {
        return super.doAct(arg);
    }

    @Override
    protected Result after(Arg arg,Result result) {
       result = super.after(arg, result);
        if (CollectionUtils.isNotEmpty(allObjectDataList)) {
            for (IObjectData iObjectData : allObjectDataList) {
                if (!Objects.isNull(iObjectData.get("check_id"))) {
                    String checkinsId = iObjectData.get("check_id").toString();
                    if (StringUtils.isNotEmpty(checkinsId)) {
                        String outTenantId = iObjectData.get("out_tenant_id", String.class);
                        String enterpriseAccount = iObjectData.get("enterprise_account", String.class);
                        checkinsProxy.updateCheckinsLockStatus(actionContext.getTenantId(),enterpriseAccount, outTenantId, checkinsId, "0");
                    }
                }
            }
        }
        return result;
    }
}
