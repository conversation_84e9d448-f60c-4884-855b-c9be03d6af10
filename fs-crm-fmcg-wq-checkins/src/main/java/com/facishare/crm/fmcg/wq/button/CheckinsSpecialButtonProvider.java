package com.facishare.crm.fmcg.wq.button;

import com.facishare.crm.fmcg.wq.CheckinsDefaultObject;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public class CheckinsSpecialButtonProvider extends AbstractCheckinSpecialButtonProvider{
    @Override
    public String getApiName() {
        return CheckinsDefaultObject.Checkins.getApiName();
    }

    @Override
    public List<IButton> getSpecialButtons() {
        return super.getSpecialButtons();
    }
}
