package com.facishare.crm.fmcg.wq;

import com.facishare.appserver.checkins.model.enums.ObjApiName;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.FmcgGray;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by zhangsm on 2018/4/11/0011.
 */
@Slf4j
public class CheckinsUtils {
    //listheader 过滤按钮
    public static List<ButtonDocument>  clearButtonDocument(List<ButtonDocument> buttons, String apiName){
        if(CollectionUtils.isNotEmpty(buttons)){
            if(ObjApiName.CheckinsObj.toString().equals(apiName)){
                List<ButtonDocument> newButtons = Lists.newArrayList();
                //高级外勤表头按钮list 只保留导出按钮
                for(ButtonDocument button:buttons) {
                    if("Export_button_default".equals(button.get("api_name"))) {
                        newButtons.add(button);
                    }
                }
                return newButtons;
            }
        }
        return buttons;
    }


    public static LayoutDocument clearLayoutDocument(LayoutDocument layoutDocument) {
        if (Objects.nonNull(layoutDocument)) {
            ILayout layout = layoutDocument.toLayout();
//            layout.setButtons(Lists.newArrayList());
            String apiName = layout.getRefObjectApiName();
            //放开导出
            List<IButton> buttons=Lists.newArrayList();
            if (ObjApiName.CheckinsObj.toString().equals(apiName)){
                layout.getButtons().forEach(o->{
                    if("Export_button_default".equals(o.getName())){
                        buttons.add(o);
                    }
                    if("Add_button_default".equals(o.getName())){
                        buttons.add(o);
                    }
                    if (ObjectAction.CREATE_OUT_PLAN.getButtonApiName().equals(o.getName())){
                        buttons.add(o);
                    }
                });
            }
            layout.setButtons(buttons);
            //放开详情页的tab
//            List<IComponent> iComponentList = null;
//            try {
//                iComponentList = layout.getComponents();
//                iComponentList.stream().forEach(o -> {
//                            if (!"detailInfo".equals(o.getName())||(!"relatedObject".equals(o.getName()))) {
//                                o.set("child_components", Lists.newArrayList());
//                            }
//                        }
//                );
//                layout.setComponents(iComponentList);
//            } catch (Exception e) {
//                log.error("", e);
//            }

            //构造两个tab页
//            com.facishare.paas.appframework.metadata.layout.WebDetailLayout#addComponents
//            try {
//                IComponent component = ComponentFactory.newInstance(NEW_DETAIL_CheckinsInfo_COMPONENT_JSON);
//                IComponent actionComponent = ComponentFactory.newInstance(NEW_DETAIL_CheckinsAction_COMPONENT_JSON);
//                WebDetailLayout.of(layout).addComponents(Lists.newArrayList(actionComponent,component),0);
//
//                layout.setDefaultComponent("CheckinsAction");
////                WebDetailLayout.of(layout).addComponents(iComponentList);
////                iComponentList.add(component);
////                LayoutUtils.addComponentByComponentJsonStr(layout,NEW_DETAIL_CheckinsInfo_COMPONENT_JSON);
////                layout.setComponents(iComponentList);
//            } catch (Exception e) {
//                log.error("", e);
//            }
            return LayoutDocument.of(layout);
        }
        return null;
    }
    public static final Set<String> CHECKINS_SUPPORT_BUTTONS = Sets.newHashSet(ObjectAction.PRINT.getButtonApiName(),ObjectAction.SEND_MAIL.getButtonApiName());

    public static LayoutDocument filterButtons(LayoutDocument layoutDocument, String layoutAgentType, String tenantId) {
        if (Objects.nonNull(layoutDocument)) {
            ILayout layout = layoutDocument.toLayout();
            String apiName = layout.getRefObjectApiName();
            List<IButton> buttons = Lists.newArrayList();
            boolean isGray = FmcgGray.Checkins.EI.showCheckinsWebDetailCustomButtons.gray(tenantId);
            boolean gray = FmcgGray.Checkins.EI.showCheckinsWebDetailCustomButtons.fsGray(tenantId);
            if (StringUtils.isEmpty(layoutAgentType) || "web".equals(layoutAgentType)) {
                try {
                    if (ObjApiName.CheckinsObj.toString().equals(apiName)) {
                        layout.getComponents().forEach(component -> {
                            if ("head_info".equals(component.getName())) {
                                component.getButtons().forEach(o -> {

                                    if (CHECKINS_SUPPORT_BUTTONS.contains(o.getName())) {
                                        buttons.add(o);
                                    }else if (IButton.ACTION_TYPE_CUSTOM.equals(o.getActionType()) && (isGray || gray)) {
                                        buttons.add(o);
                                    }
                                });
                                component.setButtons(buttons);
                            }
                        });
                    }
                } catch (MetadataServiceException e) {
                    log.error("filterButtons layout.getComponents error.", e);
                    return layoutDocument;
                }
            } else if ("mobile".equals(layoutAgentType)) {
                //过滤掉不需要的按钮
                if (ObjApiName.CheckinsObj.toString().equals(apiName)) {
                    layout.getButtons().forEach(o -> {
                        if ("SaleRecord_button_default".equals(o.getName())) {
                            buttons.add(o);
                        }
                        if ("Discuss_button_default".equals(o.getName())) {
                            buttons.add(o);
                        }
                        if ("Remind_button_default".equals(o.getName())) {
                            buttons.add(o);
                        }
                        if (isGray || gray) {
                            if (IButton.ACTION_TYPE_CUSTOM.equals(o.getActionType())) {
                                buttons.add(o);
                            }
                        }
                    });
                }
                layout.setButtons(buttons);
            }
            return LayoutDocument.of(layout);
        }
        return null;
    }


    public static ObjectDescribeDocument clearObjectDescribeDocument(ObjectDescribeDocument objectDescribeDocument) {
//        if (Objects.nonNull(objectDescribeDocument)) {
//            IObjectDescribe iObjectDescribe = objectDescribeDocument.toObjectDescribe();
//            iObjectDescribe.set("actions", Lists.newArrayList());
//            return ObjectDescribeDocument.of(iObjectDescribe);
//        }
        return objectDescribeDocument;
    }

    public static List<Map<String, Object>> clearRefObjects(List<Map<String, Object>> refObjects) {
        if (CollectionUtils.isNotEmpty(refObjects)) {
            refObjects.stream().forEach(o -> {
                if (null != o.get("describe")) {
//                    ObjectDescribeDocument tempObjDoc = new ObjectDescribeDocument(o);
//                    IObjectDescribe tempIObj = tempObjDoc.toObjectDescribe();
//                    tempIObj.set("actions", Lists.newArrayList());
//                    o.put("describe", ObjectDescribeExt.of(tempIObj).toMap());
                }
                if (null != o.get("layout")) {
                    LayoutDocument layoutDocument1 = new LayoutDocument(o);
                    ILayout layout = layoutDocument1.toLayout();
                    layout.setButtons(Lists.newArrayList());
                    List<IComponent> iComponentList = null;
//                    try {
//                        iComponentList = layout.getComponents();
//                        iComponentList.stream().forEach(e -> {
//                                    if (!"detailInfo".equals(e.getName())) {
//                                        e.set("child_components", Lists.newArrayList());
//                                    }
//                                }
//
//                        );
//                        layout.setComponents(iComponentList);
//                    } catch (Exception e) {
//                        log.error("", e);
//                    }
                    o.put("layout", LayoutExt.of(layout).toMap());
                }
            });
        }
        return refObjects;
    }

    public static List<DocumentBaseEntity> hideField4Checkins(List<DocumentBaseEntity> fieldList,String ea) {
        List<String> hideKeyList = Lists.newArrayList("action_id", "life_status","lock_status", "status",
                "select_ids", "rule_id", "relation_obj_id", "relation_obj_api_name", "feed_id", "signer", "enterprise_account",
                "client_version", "check_id", "check_type_id", "check_action_list", "unique_action_id", "last_modified_by", "checkin_id", "address_city"
                , "address_country", "address_country", "address_street", "address_street", "circle_ids", "employee_ids", "crm_child_object", "address_street_num"
                , "address_province");//主属性显示2018-10-10,去掉image_path(19-10-17)
        if (CollectionUtils.isNotEmpty(fieldList)) {
            if (GrayRelease.isAllow("checkin-server-v2", "showLifeStatus", ea)) {
                hideKeyList.remove("life_status");
            }
            fieldList = fieldList.stream().filter(o -> {
                if (hideKeyList.contains(o.get("field_name"))) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            return fieldList;
        }
        return null;
    }

    public static List<Map<String, Object>> hideField4CheckinsV2(List<Map<String, Object>> fieldList,String ea) {
        List<String> hideKeyList = Lists.newArrayList("action_id","life_status", "lock_status", "status",
                "select_ids", "rule_id", "relation_obj_id", "relation_obj_api_name", "feed_id", "signer", "enterprise_account",
                "client_version", "check_id", "check_type_id", "check_action_list", "unique_action_id", "last_modified_by", "checkin_id", "address_city"
                , "address_country", "address_country", "address_street", "address_street", "circle_ids", "employee_ids", "crm_child_object", "address_street_num"
                , "address_province");//主属性显示2018-10-10
        if (CollectionUtils.isNotEmpty(fieldList)) {
            if (GrayRelease.isAllow("checkin-server-v2", "showLifeStatus", ea)) {
                hideKeyList.remove("life_status");
            }
            fieldList = fieldList.stream().filter(o -> {
//                log.info("fieldList {}", o.get("field_name"));
                if (hideKeyList.contains(o.get("field_name"))) {
//                    log.info("fieldList contains {}", o.get("field_name"));
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            return fieldList;
        }
        return null;
    }


    public static List<String> hideField4CheckinsV3(List<String> fieldList,String ea) {
        List<String> hideKeyList = Lists.newArrayList("action_id", "life_status","lock_status", "status",
                "select_ids", "rule_id", "relation_obj_id", "relation_obj_api_name", "feed_id", "signer", "enterprise_account",
                "client_version", "check_id", "check_type_id", "check_action_list", "unique_action_id", "last_modified_by", "checkin_id", "address_city"
                , "address_country", "address_country", "address_street", "address_street", "circle_ids", "employee_ids", "crm_child_object", "address_street_num"
                , "address_province");//主属性显示2018-10-10,生命状态 2024-05-07
        if (CollectionUtils.isNotEmpty(fieldList)) {
            if (GrayRelease.isAllow("checkin-server-v2", "showLifeStatus", ea)) {
                hideKeyList.remove("life_status");
            }
            fieldList = fieldList.stream().filter(o -> {
                if (hideKeyList.contains(o)) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            return fieldList;
        }
        return null;
    }

    /**
     * 用来格式化图片对象列表页 高级外勤对像的name
     *
     * @param dataDocumentList
     * @return
     */
    public static List<ObjectDataDocument> formatDataListName(List<ObjectDataDocument> dataDocumentList) {
        log.info("formatDataListName entry");
        String CheckinsObjName = "object_checkins_id__r";

        String day = I18N.text("oaappsrv.waiqin.msg.day");
        String hour = I18N.text("oaappsrv.waiqin.msg.hour");
        String minute = I18N.text("oaappsrv.waiqin.msg.minute");
        String second = I18N.text("oaappsrv.waiqin.msg.second");

        if (CollectionUtils.isNotEmpty(dataDocumentList)) {
            for (ObjectDataDocument objectDataDocument : dataDocumentList) {
//                log.info("formatDataListName check_out_time is :{},checkins_time is :{}",objectDataDocument.get("check_out_time"),objectDataDocument.get("checkins_time"));
                if (Objects.nonNull(objectDataDocument.get("check_out_time")) && Objects.nonNull(objectDataDocument.get("checkins_time"))) {
                    long check_out_time = (long) objectDataDocument.get("check_out_time");
                    long checkins_time = (long) objectDataDocument.get("checkins_time");
                    String time = "";
                    if (check_out_time != 0 && checkins_time != 0) {
                        long duration = check_out_time - checkins_time;
//                        log.info("formatDataListName check_out_time is :{},checkins_time is :{},duration:{}", check_out_time, checkins_time, duration);
                        if (duration >= DateUtils.ONE_DAY) {
                            time = duration / DateUtils.ONE_DAY + day + duration % DateUtils.ONE_DAY / DateUtils.ONE_HOUR + hour + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR / DateUtils.ONE_MINUTE + minute + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND+ second;
                        } else if (duration >= DateUtils.ONE_HOUR) {
                            time = duration / DateUtils.ONE_HOUR + hour + duration % DateUtils.ONE_HOUR / DateUtils.ONE_MINUTE + minute + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND+ second;
                        } else if (duration >= DateUtils.ONE_MINUTE) {
                            time = duration / DateUtils.ONE_MINUTE + minute + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND+ second;
                        } else if (duration >= DateUtils.ONE_SECOND){
                            time = duration / DateUtils.ONE_SECOND + second;
                        }else {
                            time = "0"+second;
                        }
                        objectDataDocument.put("duration_formula", time);
                        objectDataDocument.put("duration", time);
                    }else{
                        objectDataDocument.put("duration", "--");
                    }
                }else{
                    objectDataDocument.put("duration", "--");
                }
//                if (objectDataDocument.keySet().contains(CheckinsObjName)) {
//                    objectDataDocument.put(CheckinsObjName, "高级外勤对象");
//                }
            }

        }
        return dataDocumentList;
    }



    public static ObjectDataDocument formatDetailName(ObjectDataDocument dataDocument) {

        String day = I18N.text("oaappsrv.waiqin.msg.day");
        String hour = I18N.text("oaappsrv.waiqin.msg.hour");
        String minute = I18N.text("oaappsrv.waiqin.msg.minute");
        String second = I18N.text("oaappsrv.waiqin.msg.second");

        if (Objects.nonNull(dataDocument.get("check_out_time")) && Objects.nonNull(dataDocument.get("checkins_time"))) {
            long check_out_time = (long) dataDocument.get("check_out_time");
            long checkins_time = (long) dataDocument.get("checkins_time");
            String time = "";
            if (check_out_time != 0 && checkins_time != 0) {
                long duration = check_out_time - checkins_time;
//                log.info("check_out_time is :{},checkins_time is :{},duration:{}", check_out_time, checkins_time, duration);
                if (duration >= DateUtils.ONE_DAY) {
                    time = duration / DateUtils.ONE_DAY + day + duration % DateUtils.ONE_DAY / DateUtils.ONE_HOUR + hour + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR / DateUtils.ONE_MINUTE + minute + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND+ second;
                } else if (duration >= DateUtils.ONE_HOUR) {
                    time = duration / DateUtils.ONE_HOUR + hour + duration % DateUtils.ONE_HOUR / DateUtils.ONE_MINUTE + minute + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND+ second;
                } else if (duration >= DateUtils.ONE_MINUTE) {
                    time = duration / DateUtils.ONE_MINUTE + minute + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR % DateUtils.ONE_MINUTE / DateUtils.ONE_SECOND+ second;
                } else if (duration >= DateUtils.ONE_SECOND){
                    time = duration / DateUtils.ONE_SECOND + second;
                }else {
                    time = "0"+second;
                }
                dataDocument.put("duration_formula", time);
                dataDocument.put("duration", time);
            }else{
                dataDocument.put("duration", "--");
            }
        }else{
            dataDocument.put("duration", "--");
        }
//        String CheckinsObjKey = "check_type_id";
//        String CheckinsImgObjKey = "action_id";
//        if (Objects.nonNull(dataDocument)) {
//            if (dataDocument.keySet().contains(CheckinsObjKey)) {
//                //高级外勤详情
//                Object object = dataDocument.get("content_text");
//                dataDocument.put("name", Objects.nonNull(object)?(String)object:"高级外勤对象");
//
//
////                Object object = dataDocument.get("create_time");
////                long createTime = Objects.nonNull(object)?(long)object:0;
////                if (createTime == 0){
////                    dataDocument.put("name","高级外勤对象");
////                }else {
////                    dataDocument.put("name","高级外勤对象_" + DateUtils.getStringFromTime(createTime,DateUtils.DateFormat));
////                }
//            } else if (dataDocument.keySet().contains(CheckinsImgObjKey)) {
//                //高级外勤图片详情
//                dataDocument.put("object_checkins_id__r", "高级外勤对象");
//                String actionName = dataDocument.get("action_name") == null ? "外勤图片" : (String) dataDocument.get("action_name");
//                dataDocument.put("name", actionName);
////                String checkinId = (String) dataDocument.get("checkin_id");
////                String actionId = (String) dataDocument.get("action_id");
////                if (Strings.isNullOrEmpty(checkinId)){
////                    dataDocument.put("object_checkins_id__r","高级外勤对象");
////                }else {
////                    dataDocument.put("object_checkins_id__r","高级外勤对象--" + DateUtils.getStringFromDate(new ObjectId(checkinId).getDate(),DateUtils.DateFormat));
////                }
////                String actionName = dataDocument.get("action_name") == null? "外勤图片":(String) dataDocument.get("action_name");
////                if (Strings.isNullOrEmpty(actionId)){
////                    dataDocument.put("name",actionName);
////                }else {
////                    dataDocument.put("name",actionName+"--" + DateUtils.getStringFromDate(new ObjectId(actionId).getDate(),DateUtils.DateFormat));
////                }
//            }
//            return dataDocument;
//        }


        return dataDocument;
    }


    public static List<ObjectDataDocument> formatDataList(List<ObjectDataDocument> dataDocumentList) {
        log.info("formatDataList entry");
        if (CollectionUtils.isNotEmpty(dataDocumentList)) {
            for (ObjectDataDocument objectDataDocument : dataDocumentList) {//直接使用在途时长会报错字符串不能转long
                if (Objects.nonNull(objectDataDocument.get("start_time")) && Objects.nonNull(objectDataDocument.get("arrival_time"))) {
                    long start_time = (long) objectDataDocument.get("start_time");
                    long arrival_time = (long) objectDataDocument.get("arrival_time");
                    if (start_time != 0 && arrival_time !=0) {
                        objectDataDocument.put("transit_time", getDuration(arrival_time,start_time));
                    }else{
                        objectDataDocument.put("transit_time", "--");
                    }
                }else{
                    objectDataDocument.put("transit_time", "--");
                }
            }
        }
        return dataDocumentList;
    }

    public static ObjectDataDocument formatDetailData(ObjectDataDocument objectDataDocument) {
        if (Objects.nonNull(objectDataDocument.get("start_time")) && Objects.nonNull(objectDataDocument.get("arrival_time"))) {
            long start_time = (long) objectDataDocument.get("start_time");
            long arrival_time = (long) objectDataDocument.get("arrival_time");
            if (start_time != 0 && arrival_time !=0) {
                objectDataDocument.put("transit_time", getDuration(arrival_time,start_time));
            }else{
                objectDataDocument.put("transit_time", "--");
            }
        }else{
            objectDataDocument.put("transit_time", "--");
        }
        return objectDataDocument;
    }

    protected static String getDuration(long checkoutTime,long checkinsTime) {

        String day = I18N.text("oaappsrv.waiqin.msg.day");
        String hour = I18N.text("oaappsrv.waiqin.msg.hour");
        String minute = I18N.text("oaappsrv.waiqin.msg.minute");
        String second = I18N.text("oaappsrv.waiqin.msg.second");
        String time = "";
        if (checkinsTime != 0 && checkinsTime !=0) {
            long duration = (checkoutTime / DateUtils.ONE_MINUTE - checkinsTime / DateUtils.ONE_MINUTE) * DateUtils.ONE_MINUTE;
            if (duration >= DateUtils.ONE_DAY) {
                time = duration / DateUtils.ONE_DAY + day + duration % DateUtils.ONE_DAY / DateUtils.ONE_HOUR + hour + duration % DateUtils.ONE_DAY % DateUtils.ONE_HOUR / DateUtils.ONE_MINUTE + minute;
            } else if (duration >= DateUtils.ONE_HOUR) {
                time = duration / DateUtils.ONE_HOUR + hour + duration % DateUtils.ONE_HOUR / DateUtils.ONE_MINUTE + minute;
            } else if (duration >= DateUtils.ONE_MINUTE) {
                time = duration / DateUtils.ONE_MINUTE + minute;
            } else {
                time = "0"+minute;
            }
        }
        return time;
    }


    public static void parseTransitTime(List<IObjectData> data) {
        if (CollectionUtils.isNotEmpty(data)) {
            for (IObjectData iObjectData : data) {
                if (Objects.nonNull(iObjectData.get("start_time")) && Objects.nonNull(iObjectData.get("arrival_time"))) {
                    long start_time = (long) iObjectData.get("start_time");
                    long arrival_time = (long) iObjectData.get("arrival_time");
                    if (arrival_time != 0 && start_time != 0) {
                        iObjectData.set("transit_time", getDuration(arrival_time,start_time));
                    }else{
                        iObjectData.set("transit_time", "--");
                    }
                }else{
                    iObjectData.set("transit_time", "--");
                }
            }

        }
    }
}
