package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.List;

public class RemakeMonitoringRecordWebDetailController extends StandardWebDetailController {
    @Override
    protected Result after(Arg arg, Result result) {
        log.info("RemakeMonitoringRecordWebDetailController");
        super.after(arg, result);
        LayoutExt layoutExt = LayoutExt.of(result.getLayout().toLayout());

        layoutExt.getHeadInfoComponent().ifPresent(x -> {
            List<IButton> buttonList = x.getButtons();
            buttonList.removeIf(o -> "Abolish".equals(o.getAction()));
            x.setButtons(buttonList);
        });


        result.setLayout(LayoutDocument.of(layoutExt));
        return result;
    }
}
