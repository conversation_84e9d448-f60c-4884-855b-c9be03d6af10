package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.CheckinsUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;

import java.util.List;

public class MileageStatisticsDetailListController extends StandardListController {

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        List<ObjectDataDocument> dataDocumentList = result.getDataList();
        dataDocumentList = CheckinsUtils.formatDataList(dataDocumentList);
        if (null != dataDocumentList)
            result.setDataList(dataDocumentList);
        return result;
    }

}
