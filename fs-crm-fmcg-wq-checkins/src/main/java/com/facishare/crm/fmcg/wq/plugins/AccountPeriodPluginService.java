package com.facishare.crm.fmcg.wq.plugins;


import com.facishare.appserver.checkins.model.enums.ObjApiName;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.domain.AddActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.EditActionDomainPlugin;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/12/14/ 10:55
 **/
@Slf4j
@SuppressWarnings("Duplicates")
@Service
@ServiceModule("account_period_save_validate")
public class AccountPeriodPluginService {

    @Autowired
    protected ServiceFacade serviceFacade;
    @ServiceMethod("add_before")
    public AddActionDomainPlugin.Result addBefore(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        try {
            IObjectData currentPurchaseObj = arg.getObjectData().toObjectData();
            if (Objects.nonNull(currentPurchaseObj.get("fixed_payment_date"))) {
                int value = Integer.parseInt(arg.getObjectData().get("fixed_payment_date").toString());
                if (value < 1 || value > 31) {
                    throw new ValidateException("[固定账期日]需要在1-31之间"); //ignoreI18n
                }
            } else if ("option2".equals(currentPurchaseObj.get("accounting_period_type"))) {
                throw new ValidateException("[固定账期日]必填"); //ignoreI18n
            }
            if (Objects.nonNull(currentPurchaseObj.get("payment_days"))) {
                int value = Integer.parseInt(currentPurchaseObj.get("payment_days").toString());
                if (value < 0) {
                    throw new ValidateException("[限期天数]需要大于0"); //ignoreI18n
                }
            } else if ("option3".equals(currentPurchaseObj.get("accounting_period_type"))) {
                throw new ValidateException("[限期天数]必填"); //ignoreI18n
            }
        }catch (Exception e){
            log.info("account_period_save_validate add_before is error",e);
            throw e;
        }
        return new AddActionDomainPlugin.Result();
    }

    @ServiceMethod("edit_before")
    public EditActionDomainPlugin.Result editBefore(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {

        try {
            IObjectData currentPurchaseObj = arg.getObjectData().toObjectData();

            if (Objects.nonNull(currentPurchaseObj.get("fixed_payment_date"))) {
                int value = Integer.parseInt(currentPurchaseObj.get("fixed_payment_date").toString());
                if (value < 1 || value > 31) {
                    throw new ValidateException("[固定账期日]需要在1-31之间"); //ignoreI18n
                }
            } else if ("option2".equals(currentPurchaseObj.get("accounting_period_type"))) {
                throw new ValidateException("[固定账期日]必填"); //ignoreI18n
            }
            if (Objects.nonNull(currentPurchaseObj.get("payment_days"))) {
                int value = Integer.parseInt(currentPurchaseObj.get("payment_days").toString());
                if (value < 0) {
                    throw new ValidateException("[限期天数]需要大于0"); //ignoreI18n
                }
            } else if ("option3".equals(currentPurchaseObj.get("accounting_period_type"))) {
                throw new ValidateException("[限期天数]必填"); //ignoreI18n
            }
        }catch (Exception e){
            log.info("account_period_save_validate edit_before is error",e);
            throw e;
        }
        return new EditActionDomainPlugin.Result();
    }
}
