package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.common.storage.mysql.dao.BaseDao;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.plugin.APLControllerPlugin;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.describe.WhatFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.LIST_SELECT_PARTLY_GRAY_EI;

public class CheckinsRelatedListWithAuthController extends StandardRelatedListController {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }
    @Override
    protected void before(Arg arg) {
        JSONObject searchJson = JSON.parseObject(arg.getSearchQueryInfo());
        Integer dataAuthUserId = searchJson.getInteger("dataAuthUserId");
        RequestContext requestContext = controllerContext.getRequestContext();
        if (dataAuthUserId != null){
            Field field = null;
            try {
                /**
                 *   public User(String tenantId, String userId) {
                 *         this.tenantId = tenantId;
                 *         this.userId = userId;
                 *     }
                 *
                 *     public User(String tenantId, String userId, String outUserId, String outTenantId) {
                 *         this(tenantId, userId, outUserId, outTenantId, null);
                 *     }
                 */

                field = RequestContext.class.getDeclaredField("user");
                field.setAccessible(true);
                String dataAuthUserIdStr = String.valueOf(dataAuthUserId);
                if (dataAuthUserId < 0){
                    throw new ValidateException("参数错误"); //ignoreI18n
                }else if (dataAuthUserId > 3000000){
                    //去掉场景字段     "search_template_id": "650c125913968d3fd0a6b995",
                    //    "search_template_type": "default",
                    arg.setSearchTemplateId(null);
                    arg.setSearchTemplateType(null);

                    //查外部企业
                    List<IObjectData> publicEmployeeObj = serviceFacade.findObjectDataByIdsIgnoreAll(controllerContext.getTenantId(), Lists.newArrayList(dataAuthUserIdStr), "PublicEmployeeObj");
                    if (CollectionUtils.notEmpty(publicEmployeeObj)){
                        String outTenantId = publicEmployeeObj.get(0).get("outer_tenant_id", String.class);
                        if (StringUtils.isNotBlank(outTenantId)){
                            field.set(requestContext, new User(controllerContext.getTenantId(),"", dataAuthUserIdStr,outTenantId));
                        }else{
                            throw new ValidateException("外部企业用户不存在"); //ignoreI18n
                        }
                    }
                }else {
                    field.set(requestContext, new User(controllerContext.getTenantId(), dataAuthUserIdStr));
                }
            } catch (Exception e) {
                throw new ValidateException(e.getMessage());
            }

        }else {
            throw new ValidateException("参数错误"); //ignoreI18n
        }
        controllerContext = new ControllerContext(requestContext,arg.getObjectApiName(),controllerContext.getMethodName());
        super.before(arg);
    }

    @Override
    protected QueryResult<IObjectData> getQueryResult(SearchTemplateQuery query) {
        return super.getQueryResult(query);
    }
}
