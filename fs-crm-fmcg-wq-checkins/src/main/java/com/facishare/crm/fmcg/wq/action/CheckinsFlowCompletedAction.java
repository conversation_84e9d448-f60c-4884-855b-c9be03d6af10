package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;

/**
 * 审批同意或者拒绝的回调
 */
@Slf4j
public class CheckinsFlowCompletedAction extends StandardFlowCompletedAction {


    private CheckinsProxy checkinsProxy =
            SpringUtil.getContext().getBean(CheckinsProxy.class);


    @Override
    protected Result after(Arg arg, Result result) {
        log.debug("CheckinsFlowCompletedAction is start!");
        Result result1 = super.after(arg, result);
        IObjectData newObjectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getDataId(), objectDescribe.getApiName());
        String checkId = newObjectData.get("check_id", String.class);
        String outTenantId = newObjectData.get("out_tenant_id",String.class);
        String enterpriseAccount = newObjectData.get("enterprise_account",String.class);
        long checkinTime = checkinsProxy.getCheckinsById(actionContext.getTenantId(),enterpriseAccount,outTenantId,checkId);
        if (checkinTime!=-1) {
            if (checkinTime == 0 || checkinTime == 1) {//未签到说明是计划触发的
                boolean needHandlePlanRepeater = true;
                if (Objects.nonNull(arg.getCallbackData())){
                    if (arg.getCallbackData() instanceof Map){
                        Map<String,Object> callBackMap = (Map<String,Object>)arg.getCallbackData();
                        needHandlePlanRepeater = (Boolean) callBackMap.getOrDefault("handlePlanRepeater",true);
                        log.debug("CheckinsFlowCompletedAction handlePlanRepeater is {} ",needHandlePlanRepeater);
                    }
                }
                checkinsProxy.handleCheckinFinishStatus(actionContext.getTenantId(),enterpriseAccount, outTenantId, checkId, arg.getTriggerType(), arg.isPass()?"true":"false",needHandlePlanRepeater?"true":"false");
            }
        }
        return result1;
    }
}
