package com.facishare.crm.fmcg.wq.controller;

import com.facishare.appserver.checkins.api.model.GetJumpInfoResult;
import com.facishare.appserver.checkins.api.model.SaveJumpShop;
import com.facishare.appserver.checkins.model.enums.ObjApiName;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;


import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class CheckinsJumpShopDetailController extends PreDefineController<CheckinsJumpShopDetailController.Arg, CheckinsJumpShopDetailController.Result> {


    private CheckinsProxy checkinsProxy =
            SpringUtil.getContext().getBean(CheckinsProxy.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected Result doService(Arg arg) {
        Result result = new Result();

        if("detail".equals(arg.getPage())) {
            SaveJumpShop saveJumpShop = new SaveJumpShop();
            saveJumpShop.setCheckId(arg.getDataId());
            saveJumpShop.setEa(serviceFacade.getEAByEI(controllerContext.getTenantId()));
            GetJumpInfoResult res = checkinsProxy.getJumpShop(controllerContext.getTenantId(), saveJumpShop);
            result.setObjectData(ObjectDataDocument.of(res.getData()));
        }else {
            result.setObjectData(new ObjectDataDocument());
        }

        IUdefButton button = serviceFacade.findButtonByApiName(User.systemUser(controllerContext.getTenantId()), ObjectAction.JUMP_SHOP.getButtonApiName(), ObjApiName.CheckinsObj.toString());
        IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), ObjApiName.CheckinsObj.toString());
        if(CollectionUtils.notEmpty(button.getParamForm())){
            boolean hasAddress = false;
            for (int i = button.getParamForm().size() - 1; i >= 0; i--) {
                Map map = button.getParamForm().get(i);
                String apiName = map.get("api_name").toString().substring(5);
                if("jump_shop_time".equals(apiName)){
                    button.getParamForm().remove(i);
                    continue;
                }
                Map fieldDescribe = getFieldDescribe(describe,apiName);
                if(Objects.nonNull(fieldDescribe)){
                    Object required = map.get("is_required");
                    map.putAll(fieldDescribe);
                    map.put("is_required",required);
                }

                if("jump_shop_address".equals(apiName)){
                    hasAddress = true;
                    map.put("is_required",true);
                }

            }
            if (!hasAddress) {
                Map<String,Object> param = Maps.newHashMap();
                Map fieldDescribe = getFieldDescribe(describe,"jump_shop_address");
                param.putAll(fieldDescribe);
                param.put("is_required",true);
                param.put("object_api_name","CheckinsObj");
                button.getParamForm().add(0,param);
            }
        }else{
            Map<String,Object> param = Maps.newHashMap();
            Map fieldDescribe = getFieldDescribe(describe,"jump_shop_address");
            param.putAll(fieldDescribe);
            param.put("is_required",true);
            param.put("object_api_name","CheckinsObj");
            button.setParamForm(Lists.newArrayList(param));
        }
        result.setButton( ButtonDocument.of(button));

        return result;
    }
    public Map getFieldDescribe(IObjectDescribe describe,String fieldApiName) {
        Map fields = (Map)describe.get("fields", Map.class);
        if (fields == null) {
            return null;
        } else {
            Map fieldDoc = (Map)fields.get(fieldApiName);
            return fieldDoc;
        }
    }
    @Data
    public static class Arg {
        private String dataId;

        private String page;
    }

    @Data
    public static class Result {
        private ButtonDocument button;

        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
