package com.facishare.crm.fmcg.wq.controller;

import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.api.TestRoute;
import com.facishare.crm.fmcg.wq.proxy.CheckinsProxy;
import com.facishare.crm.fmcg.wq.service.PreRoleService;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.List;
/**
 * 预置角色controller
 */

public class CheckinsPreRoleController extends PreDefineController<TestRoute.Arg,TestRoute.Result> {
    PreRoleService preRoleService = SpringUtil.getContext().getBean(PreRoleService.class);

    @Override
    protected void before(TestRoute.Arg arg) {
    super.before(arg);
    }

    @Override
    protected TestRoute.Result after(TestRoute.Arg arg, TestRoute.Result result) {
        return result;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("sfaVisit_fmcg_limit");
    }

    @Override
    protected TestRoute.Result doService(TestRoute.Arg arg) {
        preRoleService.preSFAVisitRole(controllerContext.getTenantId());
        preRoleService.preRoleGroup(controllerContext.getTenantId());
        return result;
    }

}
