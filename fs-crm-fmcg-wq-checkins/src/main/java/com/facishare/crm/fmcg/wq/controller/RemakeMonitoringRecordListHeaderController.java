package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.CheckinsUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


public class RemakeMonitoringRecordListHeaderController extends StandardListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        log.info("RemakeMonitoringRecordListHeaderController");
        List<String> removeButtons= Lists.newArrayList("Add","Import");
        result.setButtons(result.getButtons().stream().filter(o->!"Abolish_button_default".equals(o.get("api_name"))).collect(Collectors.toList()));
        LayoutExt layoutExt = LayoutExt.of(result.getLayout().toLayout());
        layoutExt.getButtons().removeIf(o -> removeButtons.contains(o.getAction()));
        result.setLayout(LayoutDocument.of(layoutExt));

        List list = (List)result.getLayout().get("buttons");
        if(CollectionUtils.isNotEmpty(list)){
            Iterator iterator = list.iterator();
            while (iterator.hasNext()){
                JSONObject jsonObject = (JSONObject) iterator.next();
                if(Objects.nonNull(jsonObject) && removeButtons.contains(jsonObject.getString("action"))){
                    iterator.remove();
                }
            }
        }

        result.getTemplates().forEach(o->{
            List<Map<String, Object>> field_list= (List<Map<String, Object>>)o.get("field_list");
            field_list = field_list.stream().filter(f -> !"action_id".equals(f.get("field_name"))).collect(Collectors.toList());
            o.put("field_list",field_list);
        });

        if(CollectionUtils.isNotEmpty(result.getVisibleFieldsWidth())) {
            List<DocumentBaseEntity> newVisibleFieldsWidth = result.getVisibleFieldsWidth().stream().filter(f -> !"action_id".equals(f.get("field_name"))).collect(Collectors.toList());
            result.setVisibleFieldsWidth(newVisibleFieldsWidth);
        }
        if(CollectionUtils.isNotEmpty(result.getVisibleFields())) {
            List<String> newVisibleFields = result.getVisibleFields().stream().filter(f -> !"action_id".equals(f)).collect(Collectors.toList());
            result.setVisibleFields(newVisibleFields);
        }
        log.info("RemakeMonitoringRecordListHeaderController,button:{}", JSON.toJSONString(layoutExt.getButtons()));
        return result;
    }
}
