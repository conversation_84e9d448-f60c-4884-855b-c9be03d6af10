package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class MileageStatisticsListHeaderController extends StandardListHeaderController {
    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        log.info("MileageStatisticsListHeaderController");
        List<String> removeButtons= Lists.newArrayList("Add","Import");
        result.setButtons(result.getButtons().stream().filter(o->(!"Abolish_button_default".equals(o.get("api_name")))&&(!"custom".equals(o.get("action_type")))).collect(Collectors.toList()));
        LayoutExt layoutExt = LayoutExt.of(result.getLayout().toLayout());
        layoutExt.getButtons().removeIf(o -> removeButtons.contains(o.getAction()));
        result.setLayout(LayoutDocument.of(layoutExt));

        List list = (List)result.getLayout().get("buttons");
        if(CollectionUtils.isNotEmpty(list)){
            Iterator iterator = list.iterator();
            while (iterator.hasNext()){
                JSONObject jsonObject = (JSONObject) iterator.next();
                if(Objects.nonNull(jsonObject) && removeButtons.contains(jsonObject.getString("action"))){
                    iterator.remove();
                }
            }
        }

        log.info("MileageStatisticsListHeaderController,button:{}", JSON.toJSONString(layoutExt.getButtons()));
        return result;
    }
}
