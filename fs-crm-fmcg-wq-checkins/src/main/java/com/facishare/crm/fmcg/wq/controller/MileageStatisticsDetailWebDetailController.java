package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.CheckinsUtils;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardWebDetailController;
import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.List;

public class MileageStatisticsDetailWebDetailController extends StandardWebDetailController {

    @Override
    protected Result after(Arg arg, Result result) {
        result = super.after(arg, result);
        ObjectDataDocument dataDocument = result.getData();
        dataDocument = CheckinsUtils.formatDetailData(dataDocument);
        if (null != dataDocument)
            result.setData(dataDocument);

        LayoutExt layoutExt = LayoutExt.of(result.getLayout().toLayout());

        layoutExt.getHeadInfoComponent().ifPresent(x -> {
            List<IButton> buttonList = x.getButtons();
            buttonList.removeIf(o -> "Abolish".equals(o.getAction())||"custom".equals(o.getActionType()));
            x.setButtons(buttonList);
        });


        result.setLayout(LayoutDocument.of(layoutExt));
        return result;
    }
}
