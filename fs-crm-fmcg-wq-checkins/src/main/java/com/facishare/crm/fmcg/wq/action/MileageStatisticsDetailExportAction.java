package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.CheckinsUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardExportAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.TextFieldDescribe;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.ListIterator;
import java.util.Objects;

@Slf4j
public class MileageStatisticsDetailExportAction extends StandardExportAction {
    @Override
    protected void fillDataList(User user, IObjectDescribe describe, List<IFieldDescribe> fields, List<IObjectData> dataList) {
        CheckinsUtils.parseTransitTime(dataList);
        super.fillDataList(user, describe, fields, dataList);
    }

    @Override
    protected List<IFieldDescribe> findFields(String describeApiName, String recordType) {
        List<IFieldDescribe> fields = super.findFields(describeApiName, recordType);
        // 创建虚拟字段，apiname要和原number字段一直，类型为text---否则会报错 number类型无法转化为"0分"
        IFieldDescribe virtualNumberField = new TextFieldDescribe();
        virtualNumberField.set(IFieldDescribe.API_NAME, "transit_time");
        virtualNumberField.set(IFieldDescribe.LABEL, "在途时长"); //ignoreI18n
        // 替换原number字段
        ListIterator<IFieldDescribe> lit = fields.listIterator();
        while (lit.hasNext()) {
            IFieldDescribe field = lit.next();
            if (Objects.equals("transit_time",field.getApiName())) {
                lit.set(virtualNumberField);
                break;
            }
        }
        return fields;
    }
}
