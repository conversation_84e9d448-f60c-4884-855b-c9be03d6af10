package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 无身份请求
 * identity
 */
@Slf4j
public class CheckinsAddNoIdentityController extends PreDefineController<CheckinsAddNoIdentityController.Arg, BaseObjectSaveAction.Result> {


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected BaseObjectSaveAction.Result doService(Arg arg) {
        //action
        ActionContext actionContext = new ActionContext(RequestContext.builder()
                .tenantId(arg.getNoIdentityEi())
                .user(User.systemUser(arg.getNoIdentityEi())).build(),
                arg.getNoIdentityBindApiName(), "Add");
        return serviceFacade.triggerAction(actionContext, arg, BaseObjectSaveAction.Result.class);
    }


    @Data
    public static class Arg extends BaseObjectSaveAction.Arg {
        private String noIdentityBindApiName;
        private String noIdentityEi;
    }

}
