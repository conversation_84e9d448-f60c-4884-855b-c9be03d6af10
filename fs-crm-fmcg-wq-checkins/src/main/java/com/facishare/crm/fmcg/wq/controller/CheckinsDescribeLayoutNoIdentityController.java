package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.controller.StandardDescribeLayoutController;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Created by zhangsm on 2018/4/9/0009.
 */
@Slf4j
public class CheckinsDescribeLayoutNoIdentityController extends PreDefineController<CheckinsDescribeLayoutNoIdentityController.Arg, StandardDescribeLayoutController.Result> {


    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected StandardDescribeLayoutController.Result doService(Arg arg) {
        //controller
        ControllerContext controllerContext = new ControllerContext(RequestContext.builder()
                .tenantId(arg.getNoIdentityEi())
                .user(User.systemUser(arg.getNoIdentityEi())).build(),
                arg.getNoIdentityBindApiName(), "DescribeLayout");
        return serviceFacade.triggerController(controllerContext, arg, StandardDescribeLayoutController.Result.class);
    }


    @Data
    public static class Arg extends StandardDescribeLayoutController.Arg {
        private String noIdentityBindApiName;
        private String noIdentityEi;
    }

}
