package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.CheckinsUtils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Created by zhangsm on 2018/4/9/0009.
 */
@Slf4j
public class BaseCheckinsListHeaderController extends StandardListHeaderController {

    @Override
    protected List<String> getAuthorizedFields(){
        List<String> fieldList=super.getAuthorizedFields();
        String ea = serviceFacade.getEAByEI(controllerContext.getTenantId());
        fieldList= CheckinsUtils.hideField4CheckinsV3(fieldList,ea);
        return fieldList;
    }

    @Override
    protected Result after(Arg arg, Result result) {
//        log.info("BaseCheckinsListHeaderController -- arg{},result:{}", arg,result);
        //layout去除按钮
        LayoutDocument layoutDocument = result.getLayout();
        layoutDocument = CheckinsUtils.clearLayoutDocument(layoutDocument);
//        log.info("BaseCheckinsListHeaderController -- layoutDocument{}", JSON.toJSON(layoutDocument));
        if (null != layoutDocument)
            result.setLayout(layoutDocument);

        ObjectDescribeDocument objectDescribeDocument = result.getObjectDescribe();
        objectDescribeDocument = CheckinsUtils.clearObjectDescribeDocument(objectDescribeDocument);
        if (null != objectDescribeDocument)
            result.setObjectDescribe(objectDescribeDocument);
        super.after(arg, result);
        List<DocumentBaseEntity> visibleFieldsWidth = result.getVisibleFieldsWidth();
        String ea = serviceFacade.getEAByEI(controllerContext.getTenantId());
        visibleFieldsWidth = CheckinsUtils.hideField4Checkins(visibleFieldsWidth,ea);
        log.info("BaseCheckinsListHeaderController--fields {}",JSON.toJSON(visibleFieldsWidth));
        if (CollectionUtils.notEmpty(visibleFieldsWidth))
            result.setVisibleFieldsWidth(visibleFieldsWidth);

//        //场景中也要去掉多余的字段
        result.getTemplates().forEach(o->{
            List<Map<String, Object>> field_list= (List<Map<String, Object>>)o.get("field_list");
            field_list= CheckinsUtils.hideField4CheckinsV2(field_list,ea);
            o.put("field_list",field_list);
        });
//        //fieldList
//        List<DocumentBaseEntity> fieldList = result.getFieldList();
//        fieldList = com.facishare.crm.fmcg.wq.CheckinsUtils.hideField4Checkins(fieldList);
//        log.info("BaseCheckinsListHeaderController--fields {}",JSON.toJSON(fieldList));
//        if (CollectionUtils.notEmpty(fieldList))
//            result.setFieldList(fieldList);

        return result;
    }
}
