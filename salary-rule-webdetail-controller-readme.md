# SalaryRuleWebDetailController 使用说明

## 功能概述

`SalaryRuleWebDetailController` 是工资规则详情页的控制器，主要功能是在工资规则详情页显示时，自动重写计算公式字段，将配置的工资项按照增减属性连接成易读的公式格式。

## 主要特性

### 1. 自动公式生成
- 根据工资规则中配置的工资项自动生成计算公式
- 按照工资项在规则中的配置顺序排列
- 根据工资项的增减属性添加相应的操作符

### 2. 智能符号处理
- **第一个应发项**：不显示符号，如 `基本工资`
- **第一个扣减项**：显示减号，如 `- 社保扣款`
- **后续应发项**：显示加号，如 `+ 绩效奖金`
- **后续扣减项**：显示减号，如 `- 个税`

### 3. 格式化显示
- 操作符前后都有空格，提高可读性
- 示例：`基本工资 + 绩效奖金 + 岗位津贴 - 社保扣款 - 公积金扣款 - 个税`

## 使用方式

### 1. 部署配置
将 `SalaryRuleWebDetailController.java` 文件放置在正确的包路径下：
```
fs-crm-fmcg-wq-salary/src/main/java/com/facishare/crm/fmcg/wq/controller/
```

### 2. 自动生效
控制器会在工资规则详情页加载时自动执行，无需额外配置。

### 3. 查看效果
1. 打开任意工资规则的详情页
2. 查看"计算公式"字段
3. 该字段会显示根据配置工资项生成的公式

## 示例效果

### 示例1：标准工资结构
**配置的工资项**：
1. 基本工资（应发项）
2. 岗位津贴（应发项）
3. 绩效奖金（应发项）
4. 社保扣款（扣减项）
5. 公积金扣款（扣减项）
6. 个税（扣减项）

**生成的公式**：
```
基本工资 + 岗位津贴 + 绩效奖金 - 社保扣款 - 公积金扣款 - 个税
```

### 示例2：扣减项开头
**配置的工资项**：
1. 社保扣款（扣减项）
2. 基本工资（应发项）
3. 绩效奖金（应发项）

**生成的公式**：
```
- 社保扣款 + 基本工资 + 绩效奖金
```

### 示例3：简单结构
**配置的工资项**：
1. 基本工资（应发项）
2. 社保扣款（扣减项）

**生成的公式**：
```
基本工资 - 社保扣款
```

## 技术实现

### 1. 核心方法
- `buildCalculationFormulaFromSalaryItems()`: 构建计算公式的主方法
- `getSalaryItemIds()`: 获取工资项ID列表，支持多种数据格式
- `buildFormulaString()`: 按顺序构建公式字符串
- `getOperatorByAttribute()`: 根据增减属性获取操作符

### 2. 数据格式兼容
支持多种工资项数据格式：
- `List<String>` 格式
- `String[]` 数组格式  
- `"item1,item2,item3"` 逗号分隔字符串格式

### 3. 异常处理
- 任何异常都不会影响页面正常显示
- 异常情况下只记录日志，不设置计算公式字段
- 工资项不存在时跳过该项，继续处理其他工资项

## 日志记录

### 1. 正常日志
```
SalaryRuleWebDetailController after method called
工资规则计算公式已重写: 基本工资 + 绩效奖金 - 社保扣款
```

### 2. 调试日志
```
工资规则未配置工资项，无法构建计算公式
构建的计算公式: 基本工资 + 绩效奖金 - 社保扣款
```

### 3. 警告日志
```
未找到工资项详情，工资项ID列表: [item1, item2]
未找到工资项，ID: item1
工资项名称为空，ID: item1
未知的增减属性: unknown_value
```

### 4. 错误日志
```
重写工资规则计算公式时发生异常
构建计算公式时发生异常
```

## 注意事项

### 1. 性能考虑
- 只在详情页显示时执行，不影响数据存储
- 使用缓存的工资项数据，避免重复查询
- 异常情况下快速失败，不影响页面加载

### 2. 数据一致性
- 公式基于实时的工资项配置生成
- 工资项名称或属性变更会立即反映在公式中
- 不存储生成的公式，每次都重新计算

### 3. 向后兼容
- 不影响现有的工资规则功能
- 只是在显示层面增强用户体验
- 原有的计算公式字段值不会被永久修改

## 扩展说明

### 1. 自定义格式
如需修改公式格式，可以调整 `buildFormulaString()` 方法中的字符串拼接逻辑。

### 2. 多语言支持
操作符（+ -）是通用符号，无需国际化处理。工资项名称会自动使用系统中配置的名称。

### 3. 未来扩展
- 可以考虑添加括号支持复杂公式
- 可以添加公式验证功能
- 可以支持更多的操作符类型

## 依赖关系

### 1. 必需依赖
- `SalaryItemDao`: 查询工资项详情
- `SalaryRuleFields`: 工资规则字段常量
- `SalaryItemFields`: 工资项字段常量

### 2. 框架依赖
- `StandardWebDetailController`: 基础详情页控制器
- `ObjectDataDocument`: 数据文档对象
- `ObjectDataExt`: 数据扩展工具类

## 测试建议

### 1. 功能测试
- 测试不同工资项组合的公式生成
- 测试各种数据格式的兼容性
- 测试异常情况的处理

### 2. 性能测试
- 测试大量工资项时的性能表现
- 测试并发访问时的稳定性

### 3. 兼容性测试
- 测试与现有功能的兼容性
- 测试不同浏览器的显示效果
