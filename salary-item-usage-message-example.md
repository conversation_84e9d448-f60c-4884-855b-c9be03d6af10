# 工资项编辑限制使用信息文案示例

## 功能说明

当工资项被工资规则或员工固定工资表使用时，系统会阻止用户修改关键字段，并返回详细的使用信息文案，告知用户该工资项被哪些对象使用。

## 异常消息格式

### 基本格式
```
工资项已被使用，不允许修改[字段名]。[使用信息文案]
```

### 使用信息文案格式
- **仅被工资规则使用**：`被工资规则使用: 规则名1、规则名2`
- **仅被员工固定工资表使用**：`被员工固定工资表使用: 员工名1的固定工资、员工名2的固定工资`
- **同时被两者使用**：`被工资规则使用: 规则名1、规则名2；被员工固定工资表使用: 员工名1的固定工资、员工名2的固定工资`

## 具体示例

### 示例1：修改增减属性 - 仅被工资规则使用

**场景**：工资项"基本工资"被工资规则"销售人员工资规则"使用，用户尝试修改增减属性

**异常消息**：
```
工资项已被使用，不允许修改增减属性。被工资规则使用: 销售人员工资规则
```

### 示例2：修改计算公式 - 仅被员工固定工资表使用

**场景**：工资项"绩效奖金"被3个员工的固定工资表使用，用户尝试修改计算公式

**异常消息**：
```
工资项已被使用，不允许修改计算公式。被员工固定工资表使用: 张三的固定工资、李四的固定工资、王五的固定工资
```

### 示例3：修改定薪方式 - 同时被多个对象使用

**场景**：工资项"岗位津贴"被2个工资规则和5个员工固定工资表使用，用户尝试修改定薪方式

**异常消息**：
```
工资项已被使用，不允许修改定薪方式。被工资规则使用: 基础工资规则、管理层工资规则；被员工固定工资表使用: 张三的固定工资、李四的固定工资、王五的固定工资、赵六的固定工资、钱七的固定工资
```

### 示例4：修改启用状态 - 被多个工资规则使用

**场景**：工资项"交通补贴"被3个工资规则使用，用户尝试修改启用状态

**异常消息**：
```
工资项已被使用，不允许修改启用状态。被工资规则使用: 销售工资规则、技术工资规则、管理工资规则
```

### 示例5：修改取整方式 - 复杂使用场景

**场景**：工资项"社保扣款"被1个工资规则和3个员工固定工资表使用，用户尝试修改取整方式

**异常消息**：
```
工资项已被使用，不允许修改取整方式。被工资规则使用: 标准扣款规则；被员工固定工资表使用: 张三的固定工资、李四的固定工资、王五的固定工资
```

## 受限制的字段列表

以下字段在工资项被使用时不允许修改：

| 字段名 | 字段显示名 | 说明 |
|--------|------------|------|
| VALUE_TYPE | 取值方式 | 影响工资项的计算逻辑 |
| INCREMENT_DECREMENT_ATTRIB | 增减属性 | 影响工资项在计算中的作用（加项/减项） |
| SALARY_METHOD | 定薪方式 | 影响工资项的适用范围 |
| ENABLED_STATUS | 启用状态 | 影响工资项的可用性 |
| ROUNDING_METHOD | 取整方式 | 影响计算结果的精度 |
| DECIMAL_PLACES | 小数位数 | 影响计算结果的精度 |
| CALCULATION_FORMULA | 计算公式 | 影响工资项的计算逻辑 |

## 允许修改的字段

以下字段在工资项被使用时仍然允许修改：

| 字段名 | 字段显示名 | 说明 |
|--------|------------|------|
| name | 工资项名称 | 仅影响显示，不影响业务逻辑 |
| SALARY_ITEM_DESCRIPTION | 工资项说明 | 仅影响用户理解，不影响计算 |

## 用户体验

### 1. 清晰的错误提示
- 明确告知用户哪个字段不能修改
- 详细说明工资项被哪些对象使用
- 帮助用户理解限制的原因

### 2. 精确的使用信息
- 工资规则使用：显示具体的规则名称
- 员工固定工资表使用：显示使用的记录数量
- 多种使用情况：用分号分隔不同类型的使用信息

### 3. 一致的消息格式
- 所有字段的错误消息格式保持一致
- 使用信息的展示格式统一
- 便于用户理解和记忆

## 技术实现要点

### 1. 使用信息收集
```java
// 查询工资规则使用情况
List<IObjectData> usingSalaryRules = salaryRuleDao.getSalaryRulesBySalaryItemId(tenantId, salaryItemId);

// 查询员工固定工资表使用情况  
List<IObjectData> usingEmployeeFixedSalaryDetails = employeeFixedSalaryDetailDao.getBySalaryItemId(tenantId, salaryItemId);
```

### 2. 文案组装
```java
private String buildUsageMessage(List<IObjectData> usingSalaryRules, List<IObjectData> usingEmployeeFixedSalaryDetails) {
    StringBuilder usingInfo = new StringBuilder();
    
    // 添加工资规则使用信息
    if (CollectionUtils.isNotEmpty(usingSalaryRules)) {
        // 拼接规则名称
        usingInfo.append("被工资规则使用: ").append(规则名称列表);
    }
    
    // 添加员工固定工资表使用信息
    if (CollectionUtils.isNotEmpty(usingEmployeeFixedSalaryDetails)) {
        // 拼接员工固定工资表名称
        StringBuilder usingEmployeeInfo = new StringBuilder();
        for (IObjectData detail : usingEmployeeFixedSalaryDetails) {
            if (usingEmployeeInfo.length() > 0) {
                usingEmployeeInfo.append("、");
            }
            String employeeFixedSalaryName = getEmployeeFixedSalaryName(detail);
            usingEmployeeInfo.append(employeeFixedSalaryName);
        }
        usingInfo.append("被员工固定工资表使用: ").append(usingEmployeeInfo.toString());
    }
    
    return usingInfo.toString();
}
```

### 3. 异常抛出
```java
private void validateFieldNotChanged(ObjectDataDocument newData, IObjectData originalData,
        String fieldName, String fieldDisplayName, String usageMessage) {
    // 检查字段是否被修改
    if (字段值发生变化) {
        throw new ValidateException(String.format("工资项已被使用，不允许修改%s。%s", fieldDisplayName, usageMessage));
    }
}
```

## 日志记录

系统会记录详细的使用信息日志：

```
工资项 [工资项ID] 被以下对象使用: 被工资规则使用: 销售工资规则、技术工资规则；被员工固定工资表使用: 张三的固定工资、李四的固定工资、王五的固定工资
```

## 注意事项

### 1. 性能考虑
- 每次编辑都会查询使用情况
- 对于大量数据可能有性能影响
- 建议在必要时添加缓存机制

### 2. 数据一致性
- 确保使用信息的准确性
- 及时更新使用关系
- 避免显示过期的使用信息

### 3. 用户友好性
- 错误消息要清晰易懂
- 提供足够的上下文信息
- 帮助用户快速定位问题

## 扩展建议

### 1. 增加操作建议
在错误消息中可以添加操作建议：
```
工资项已被使用，不允许修改增减属性。被工资规则使用: 销售工资规则。建议：请先从工资规则中移除该工资项，再进行修改。
```

### 2. 提供快速链接
在错误消息中可以提供快速跳转链接：
```
工资项已被使用，不允许修改计算公式。被工资规则使用: 销售工资规则（点击查看）；被员工固定工资表使用: 张三的固定工资（点击查看）、李四的固定工资（点击查看）。
```

### 3. 支持批量操作提示
当用户尝试批量修改时，提供汇总信息：
```
共选择5个工资项，其中3个已被使用无法修改：基本工资（被2个规则使用）、绩效奖金（被张三的固定工资等3个员工使用）、岗位津贴（被1个规则和李四的固定工资等2个员工使用）。
```
