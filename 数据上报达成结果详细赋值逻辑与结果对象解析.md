# 📊 数据上报达成结果详细赋值逻辑与结果对象解析

## 🎯 系统整体架构

```mermaid
graph TB
    subgraph "用户配置层"
        A1[定义标准对象] --> A2[配置映射规则（已预置默认）]
        A2 --> A3[设置达成方式]
        A3 --> A4[配置三层规则（已预置默认）]
    end

    subgraph "数据处理层"
        B1[数据上报] --> B2[数据映射转换]
        B2 --> B3[三层规则执行]
        B3 --> B4[达成状态判断]
    end

    subgraph "结果输出层"
        C1[项目达成结果] --> C2[形式达成汇总]
        C2 --> C3[最终达成汇总]
        C3 --> C4[业务展示，动作配置陈列分销达成自定义页面按钮]
    end

    A4 --> B2
    B4 --> C1

    style A1 fill:#e3f2fd
    style A2 fill:#e3f2fd
    style A3 fill:#e3f2fd
    style A4 fill:#e3f2fd
    style B1 fill:#f1f8e9
    style B2 fill:#f1f8e9
    style B3 fill:#f1f8e9
    style B4 fill:#f1f8e9
    style C1 fill:#fff3e0
    style C2 fill:#fff3e0
    style C3 fill:#fff3e0
    style C4 fill:#fff3e0
```


## 🏗️ 系统架构概述

数据上报达成评估系统采用**三层处理架构**：
1. **数据映射层**：将不同来源的上报数据转换为统一标准格式
2. **规则执行层**：通过过滤→匹配→达成三步规则链进行评估
3. **结果转换层**：将规则执行结果转换为业务达成对象

## 🔄 核心处理流程详解

### 1️⃣ **数据映射与聚合阶段**

#### **数据映射流程图**

```mermaid
graph TD
    subgraph "原始数据源"
        A1[ShelfReportDetailObj<br/>货架报告明细<br/>product_id, row_numbers, display_format]
        A2[MaterialReportDetailObj<br/>物料报告明细<br/>material_name, quantity, display_format]
        A3[TPMActivityProofProductDetailObj<br/>TPM活动证明产品明细<br/>product_id, display_form_id, ai_number]
        A4[DisplayFormatDetailsObj<br/>陈列形式明细<br/>display_format, layer_count]
    end

    subgraph "映射处理引擎"
        B1[映射配置解析器<br/>读取fieldMappings配置]
        B2[字段转换器<br/>sourceField → targetField]
        B3[关联查找器<br/>通过sourceLookUpApiName查找]
        B4[聚合计算器<br/>DISTINCT_COUNT, SUM等]
    end

    subgraph "标准数据格式"
        C1[ProductItemStandardDetailObj<br/>产品项目标准明细<br/>product_name, 排面数, SKU数量]
        C2[MaterialStandardDetailsObj<br/>物料项目标准明细<br/>material_name, 物料数]
        C3[ProjectStandardsObj<br/>项目标准<br/>层数, 排面数, SKU数量]
        C4[MustDistributeProductsObj<br/>必分销品<br/>product, display_format]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1

    B1 --> B2
    B1 --> B3
    B1 --> B4

    B2 --> C1
    B2 --> C2
    B3 --> C1
    B3 --> C2
    B4 --> C3
    B2 --> C4

    style A1 fill:#e3f2fd
    style A2 fill:#e3f2fd
    style A3 fill:#e3f2fd
    style A4 fill:#e3f2fd
    style B1 fill:#f1f8e9
    style B2 fill:#f1f8e9
    style B3 fill:#f1f8e9
    style B4 fill:#f1f8e9
    style C1 fill:#fff3e0
    style C2 fill:#fff3e0
    style C3 fill:#fff3e0
    style C4 fill:#fff3e0
```

#### **映射转换详细流程图**

```mermaid
graph LR
    subgraph "具体映射示例"
        A[原始数据<br/>product_id: prod_001<br/>row_numbers: 3<br/>display_format: 端架]

        B[映射配置<br/>targetField: product_name<br/>sourceField: product_id<br/>sourceLookUpApiName: ProductObj]

        C[关联查找<br/>通过ProductObj查找<br/>prod_001 → 可口可乐]

        D[字段转换<br/>row_numbers → 排面数<br/>值保持不变: 3]

        E[聚合计算<br/>product_id → SKU数量<br/>DISTINCT_COUNT: 1]

        F[标准数据<br/>product_name: 可口可乐<br/>排面数: 3<br/>SKU数量: 1]
    end

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F

    style A fill:#e3f2fd
    style B fill:#f1f8e9
    style C fill:#fff3e0
    style D fill:#fff3e0
    style E fill:#fff3e0
    style F fill:#e8f5e8
```

#### **核心功能**
将原始报告数据转换为标准化格式，便于后续规则计算。简单来说就是：**从数据上报详情对象映射转换成标准详情对象字段上的格式，key被转换，value不变**。

#### **映射配置结构**
```json
{
  "sourceObjectApiName": "源对象API名",
  "standardObjectApiName": "目标标准对象API名",
  "fieldMappings": [
    {
      "targetField": "目标字段",
      "sourceField": "源字段",
      "sourceLookUpApiName": "关联对象API名(可选)",
      "targetFieldName": "目标字段名称(可选)",
      "aggregationConfig": {"type": "聚合类型(可选)"}
    }
  ]
}
```

#### **实际映射示例**

**示例1：货架报告 → 产品项目标准**
```json
{
  "sourceObjectApiName": "ShelfReportDetailObj",
  "standardObjectApiName": "ProductItemStandardDetailObj",
  "fieldMappings": [
    {
      "targetField": "ProjectStandardsObj.display_format",
      "sourceField": "display_format"
    },
    {
      "targetField": "product_name",
      "sourceField": "product_id"
    },
    {
      "targetFieldName": "排面数",
      "sourceField": "row_numbers"
    },
    {
      "targetFieldName": "SKU数量",
      "aggregationConfig": {"type": "DISTINCT_COUNT"},
      "sourceField": "product_id"
    }
  ]
}
```

**映射说明**：
- `display_format` → `ProjectStandardsObj.display_format`：陈列形式直接映射
- `product_id` → `product_name`：产品ID映射到产品名称字段
- `row_numbers` → `排面数`：排面数直接映射到目标字段名
- `product_id` → `SKU数量`：对产品ID进行去重计数，得到SKU数量

**示例2：TPM活动证明 → 产品项目标准**
```json
{
  "sourceObjectApiName": "TPMActivityProofProductDetailObj",
  "standardObjectApiName": "ProductItemStandardDetailObj",
  "fieldMappings": [
    {
      "targetField": "ProjectStandardsObj.display_format",
      "sourceField": "display_form_id",
      "sourceLookUpApiName": "DisplayFormObj"
    },
    {
      "targetField": "product_name",
      "sourceField": "product_id",
      "sourceLookUpApiName": "ProductObj"
    },
    {
      "targetFieldName": "排面数",
      "sourceField": "ai_number"
    }
  ]
}
```

**映射说明**：
- 使用关联对象查找：`display_form_id` 通过 `DisplayFormObj` 查找到陈列形式
- 产品ID通过 `ProductObj` 查找到产品名称
- AI识别的数量映射到排面数

#### **映射类型说明**

**1. 直接字段映射**：`"targetField": "目标字段", "sourceField": "源字段"`
**2. 关联对象查找**：添加 `"sourceLookUpApiName": "关联对象API名"`
**3. 聚合计算**：添加 `"aggregationConfig": {"type": "聚合类型"}`
**4. 目标字段名映射**：`"targetFieldName": "目标字段名称"`

#### **聚合计算类型**
- **DISTINCT_COUNT**：统计不重复值数量（如SKU数量）
- **SUM**：数值求和（如排面数总计）
- **COUNT**：记录计数
- **AVG**：平均值计算

### 2️⃣ **三层规则执行引擎**

#### **三层规则执行流程图**

```mermaid
graph TD
    A[原始数据<br/>100条陈列记录] --> B{第1层: 过滤规则<br/>_filter}

    B -->|通过| C[过滤后数据<br/>30条记录<br/>符合基础条件]
    B -->|未通过| D[无数据<br/>直接返回未陈列]

    C --> E{第2层: 匹配规则<br/>_match}

    E -->|通过| F[匹配后数据<br/>15条记录<br/>符合业务条件]
    E -->|未通过| G[无匹配数据<br/>返回未达标]

    F --> H{第3层: 达成规则<br/>_achievement}

    H -->|聚合计算| I[分组聚合结果<br/>按配置统计]

    I --> J{达成判断}
    J -->|≥标准| K[达标状态<br/>achieved_results=1]
    J -->|<标准| L[未达标状态<br/>achieved_results=0]
    J -->|部分满足（tpm特有,rio衡量标准）| M[部分达标状态<br/>achieved_results=2]

    D --> N[最终结果<br/>未陈列状态<br/>achieved_results=4]
    G --> O[最终结果<br/>未达标状态<br/>achieved_results=0]
    K --> P[最终结果<br/>达标状态]
    L --> Q[最终结果<br/>未达标状态]
    M --> R[最终结果<br/>部分达标状态]

    style A fill:#e3f2fd
    style B fill:#f1f8e9
    style C fill:#f1f8e9
    style E fill:#fff3e0
    style F fill:#fff3e0
    style H fill:#fce4ec
    style I fill:#fce4ec
    style J fill:#f3e5f5
    style K fill:#e8f5e8
    style L fill:#ffebee
    style M fill:#fff8e1
    style N fill:#f5f5f5
    style O fill:#ffebee
    style P fill:#e8f5e8
    style Q fill:#ffebee
    style R fill:#fff8e1
```

#### **数据漏斗效应图**

```mermaid
graph TD
    subgraph "数据量变化"
        A1[原始数据: 100条] --> A2[过滤后: 30条]
        A2 --> A3[匹配后: 15条]
        A3 --> A4[聚合后: 5组]
        A4 --> A5[最终结果: 1个状态]
    end

    subgraph "规则作用"
        B1[过滤规则<br/>筛选基础条件<br/>如: 陈列形式=端架]
        B2[匹配规则<br/>匹配业务条件<br/>如: 产品=可口可乐]
        B3[达成规则<br/>聚合计算判断<br/>如: 排面数≥3]
    end

    A1 -.-> B1
    A2 -.-> B2
    A3 -.-> B3

    style A1 fill:#e3f2fd
    style A2 fill:#f1f8e9
    style A3 fill:#fff3e0
    style A4 fill:#fce4ec
    style A5 fill:#e8f5e8
    style B1 fill:#f1f8e9
    style B2 fill:#fff3e0
    style B3 fill:#fce4ec
```

#### **规则执行流程详解**

三层规则按顺序执行，每一层都会对数据进行筛选和处理：

```java
// 原始数据：所有上报的陈列数据
List<IObjectData> originalDataList = getAllReportData();

// 第1层：过滤规则 (filterRules)
List<IObjectData> filteredDataList = new ArrayList<>();
if (filterTemplate != null) {
    filteredDataList = applyFilterRule(filterTemplate, originalDataList);
    // 用途：筛选出符合基本条件的数据，如特定陈列形式、特定门店等
}

// 第2层：匹配规则 (matchRules)
List<IObjectData> matchedDataList = new ArrayList<>();
if (matchTemplate != null && !filteredDataList.isEmpty()) {
    matchedDataList = applyFilterRule(matchTemplate, filteredDataList);
    // 用途：在过滤后的数据中，进一步匹配业务条件，如产品类型、位置要求等
}

// 第3层：达成规则 (achievementRules)
Map<String, GroupAchievementResult> groupResults = new HashMap<>();
boolean isOverallAchieved = false;
if (achievementTemplate != null && !matchedDataList.isEmpty()) {
    groupResults = applyAchievementRule(aggFieldMappings,
            achievementTemplate, matchedDataList, groupByFields);
    isOverallAchieved = groupResults.isOverallSuccess();
    // 用途：对匹配后的数据进行聚合计算，判断是否达成标准要求
}
```

#### **三层规则详细说明**

##### **第1层：过滤规则（Filter Rules）**

**作用**：从所有上报数据中筛选出符合基本条件的数据
**规则名称格式**：`{标准对象名}_filter`
**数据流向**：`原始数据` → `过滤后数据`

**过滤条件示例**：
- 陈列形式过滤：只处理"端架"陈列形式的数据
- 门店过滤：只处理特定门店的数据
- 时间过滤：只处理特定时间范围的数据
- 状态过滤：只处理已审核的数据

**过滤后数据用途**：
- 作为第2层匹配规则的输入数据
- 如果没有第2层规则，直接作为第3层达成规则的输入
- 用于统计"有陈列数据"的基础判断

##### **第2层：匹配规则（Match Rules）**

**作用**：在过滤后的数据中，进一步匹配具体的业务条件
**规则名称格式**：`{标准对象名}_match`
**数据流向**：`过滤后数据` → `匹配后数据`

**匹配条件示例**：
- 产品匹配：匹配特定的产品或产品分类
- 位置匹配：匹配特定的陈列位置或层级
- 物料匹配：匹配特定的物料类型
- 组合匹配：同时满足多个条件的数据

**匹配后数据用途**：
- 作为第3层达成规则的输入数据
- 用于计算实际陈列的产品、物料等信息
- 用于生成"实际产品"、"实际物料"等字段的值

##### **第3层：达成规则（Achievement Rules）**

**作用**：对匹配后的数据进行聚合计算，判断是否达成标准要求
**规则名称格式**：`{标准对象名}_achievement`
**数据流向**：`匹配后数据` → `聚合结果` → `达成判断`

**达成计算示例**：
- 数量达成：统计排面数、SKU数量等是否达到标准
- 覆盖达成：统计产品覆盖率、物料覆盖率等
- 层级达成：统计各层级的陈列情况
- 组合达成：综合多个指标的达成情况

**聚合结果用途**：
- 生成最终的达成状态（达标/未达标/部分达标/未陈列）
- 计算实际数量、要求数量、差异数量等字段
- 生成达成项总结、未达成项总结等分析信息

#### **数据在各层的变化示例**

**原始数据**（100条陈列记录）：
```json
[
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 2, "layer": 1},
  {"display_format": "货架", "product_id": "prod_002", "row_numbers": 1, "layer": 2},
  {"display_format": "端架", "product_id": "prod_003", "row_numbers": 4, "layer": 1},
  // ... 更多数据
]
```

**第1层过滤后**（30条记录）：
```json
// 过滤条件：display_format = "端架"
[
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 2, "layer": 1},
  {"display_format": "端架", "product_id": "prod_003", "row_numbers": 4, "layer": 1},
  // ... 只保留端架陈列的数据
]
```

**第2层匹配后**（15条记录）：
```json
// 匹配条件：product_id in ["prod_001", "prod_003", "prod_005"]
[
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 2, "layer": 1},
  {"display_format": "端架", "product_id": "prod_003", "row_numbers": 4, "layer": 1},
  // ... 只保留指定产品的数据
]
```

**第3层聚合后**：
```json
// 聚合计算：按product_id分组，统计总排面数
{
  "prod_001": {"total_rows": 6, "achieved": false}, // 要求≥8排面
  "prod_003": {"total_rows": 12, "achieved": true}   // 要求≥8排面
}
// 最终结果：部分达标（2个产品中1个达标）
```

#### **分组聚合处理**

在第3层达成规则中，系统会根据配置进行分组聚合：

**分组策略**：
- **层级分组**：当标准要求为"任意层级"时，按`layer`字段分组计算
- **产品分组**：按`product_id`分组，统计每个产品的达成情况
- **物料分组**：按`material_id`分组，统计每个物料的达成情况
- **默认分组**：无特殊要求时，所有数据作为一组处理

**聚合计算**：
- **SUM**：求和计算（如总排面数）
- **DISTINCT_COUNT**：去重计数（如SKU数量）
- **COUNT**：记录计数（如陈列次数）
- **MAX/MIN**：最大/最小值（如最高层级）

### 3️⃣ **单项达成状态判断逻辑**

#### **达成状态判断流程图**

```mermaid
graph TD
    A[规则执行结果] --> B{规则是否成功?}

    B -->|是| C[达标状态 achieved_results=1]
    B -->|否| D{是否有陈列数据?}

    D -->|否| E[未陈列状态 achieved_results=4]
    D -->|是| F{是否有子条件?}

    F -->|是| G{过滤数据是否非空?}
    F -->|否| H[未达标状态 achieved_results=0]

    G -->|是| I[部分达标状态 achieved_results=2]
    G -->|否| H

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#f5f5f5
    style F fill:#fff3e0
    style G fill:#fff3e0
    style H fill:#ffebee
    style I fill:#fff8e1
```

#### **状态码含义图**

```mermaid
graph LR
    subgraph "达成状态编码"
        A1[0 - 未达标<br/>🔴 有陈列但未达成标准]
        A2[1 - 达标<br/>🟢 完全满足标准要求]
        A3[2 - 部分达标<br/>🟡 部分满足标准要求]
        A4[4 - 未陈列<br/>⚪ 没有相关陈列数据]
        A5[5 - 待审核<br/>🔵 TPM活动证明待审核]
    end

    subgraph "业务含义"
        B1[规则执行失败<br/>且有陈列数据]
        B2[规则执行成功<br/>达成所有要求]
        B3[有子条件且<br/>有过滤数据]
        B4[没有过滤到<br/>任何陈列数据]
        B5[TPM系统判断<br/>状态为待审核]
    end

    A1 -.-> B1
    A2 -.-> B2
    A3 -.-> B3
    A4 -.-> B4
    A5 -.-> B5

    style A1 fill:#ffebee
    style A2 fill:#e8f5e8
    style A3 fill:#fff8e1
    style A4 fill:#f5f5f5
    style A5 fill:#e3f2fd
    style B1 fill:#ffebee
    style B2 fill:#e8f5e8
    style B3 fill:#fff8e1
    style B4 fill:#f5f5f5
    style B5 fill:#e3f2fd
```

#### **单项目达成判断代码**
```java
if (ruleExecutionResult.isSuccess()) {
    achievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS,
        DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_1); // 达标
} else if (!ruleExecutionResult.hasDisplay()) {
    achievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS,
        DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_4); // 未陈列
} else if (ruleExecutionResult.hasSubConditions() &&
           CollectionUtils.isNotEmpty(ruleExecutionResult.getFilteredDataList())) {
    achievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS,
        DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_2); // 部分达标
} else {
    achievement.set(DisplayProjectAchievementFields.ACHIEVED_RESULTS,
        DisplayProjectAchievementFields.ACHIEVED_RESULTS_Options_0); // 未达标
}
```

### 4️⃣ **整体达成状态聚合逻辑**

#### **聚合流程图**

```mermaid
graph TD
    subgraph "单项达成结果"
        A1[产品项目达成结果<br/>DisplayProjectAchievementObj]
        A2[物料项目达成结果<br/>DisplayProjectAchievementObj]
        A3[整体项目达成结果<br/>DisplayProjectAchievementObj]
        A4[分销产品达成结果<br/>DistributionProductsAchievedObj]
    end

    subgraph "中间聚合层"
        B1[陈列形式达成汇总<br/>SummaryDisplayAchievementObj]
        B2[产品组达成统计]
        B3[物料组达成统计]
        B4[项目组达成统计]
    end

    subgraph "最终聚合结果"
        C1[总体达成汇总<br/>DisplayDistrAchSummaryObj]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A1 --> B2
    A2 --> B3
    A3 --> B4
    A4 --> B2

    B1 --> C1
    B2 --> C1
    B3 --> C1
    B4 --> C1

    style A1 fill:#e8f5e8
    style A2 fill:#fff3e0
    style A3 fill:#f3e5f5
    style A4 fill:#fce4ec
    style B1 fill:#e3f2fd
    style B2 fill:#e3f2fd
    style B3 fill:#e3f2fd
    style B4 fill:#e3f2fd
    style C1 fill:#ffebee
```

#### **聚合计算逻辑**

**第一层聚合：按陈列形式聚合**
```java
// 统计每个陈列形式的达成情况
Map<String, List<DisplayProjectAchievementObj>> displayFormGroups =
    groupByDisplayForm(projectAchievements);

for (String displayForm : displayFormGroups.keySet()) {
    List<DisplayProjectAchievementObj> projects = displayFormGroups.get(displayForm);

    // 统计该陈列形式下的达成情况
    int totalProjects = projects.size();
    int achievedProjects = countAchievedProjects(projects);

    // 生成陈列形式达成汇总
    SummaryDisplayAchievementObj summary = createSummary(displayForm,
        totalProjects, achievedProjects);
}
```

**第二层聚合：按达成方式计算最终状态**
```java
// 根据SuccessfulStoreRangeObj.calculation_rules_attainment计算
String calculationMethod = successfulStoreRange.getCalculationRulesAttainment();

if ("all_attainment".equals(calculationMethod)) {
    // 所有陈列形式都必须达标
    boolean allDisplayFormsAchieved = summaryDisplayAchievements.stream()
        .allMatch(summary -> isAchieved(summary.getAchievedResults()));

    if (allDisplayFormsAchieved) {
        finalStatus = ACHIEVEMENT_STATUS_Options_1; // 全部达标
    } else if (hasAnyAchieved(summaryDisplayAchievements)) {
        finalStatus = ACHIEVEMENT_STATUS_Options_2; // 部分达标
    } else {
        finalStatus = ACHIEVEMENT_STATUS_Options_3; // 均不达标
    }

} else if ("all_display_attainment".equals(calculationMethod)) {
    // 所有已陈列的形式都必须达标
    List<SummaryDisplayAchievementObj> displayedForms = summaryDisplayAchievements.stream()
        .filter(summary -> !isNotDisplayed(summary.getAchievedResults()))
        .collect(Collectors.toList());

    boolean allDisplayedFormsAchieved = displayedForms.stream()
        .allMatch(summary -> isAchieved(summary.getAchievedResults()));

    // 计算最终状态...
}
```

#### **聚合状态判断流程图**

```mermaid
graph TD
    A[收集所有单项达成结果] --> B{获取达成计算方式<br/>calculation_rules_attainment}

    B -->|all_attainment| C{所有陈列形式都达标?}
    B -->|all_display_attainment| D{所有已陈列形式都达标?}

    C -->|是| E[全部达标<br/>achievement_status=1]
    C -->|否| F{有任何形式达标?}

    D -->|是| E
    D -->|否| G{有任何已陈列形式达标?}

    F -->|是| H[部分达标<br/>achievement_status=2]
    F -->|否| I[均不达标<br/>achievement_status=3]

    G -->|是| H
    G -->|否| I

    E --> J[生成DisplayDistrAchSummaryObj]
    H --> J
    I --> J

    style A fill:#e3f2fd
    style B fill:#fff3e0
    style C fill:#fff3e0
    style D fill:#fff3e0
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style G fill:#fff3e0
    style H fill:#fff8e1
    style I fill:#ffebee
    style J fill:#f3e5f5
```

#### **达成项总结和未达成项总结的生成**

**达成项总结生成逻辑**：
```java
List<String> achievedResults = new ArrayList<>();

// 统计产品组达成情况
int productGroupsAchieved = countAchievedProductGroups();
int totalProductGroups = getTotalProductGroups();
if (productGroupsAchieved >= totalProductGroups) {
    achievedResults.add(ACHIEVED_RESULTS_Options_5); // 产品项目达标
}

// 统计物料组达成情况
int materialGroupsAchieved = countAchievedMaterialGroups();
int totalMaterialGroups = getTotalMaterialGroups();
if (materialGroupsAchieved >= totalMaterialGroups) {
    achievedResults.add(ACHIEVED_RESULTS_Options_6); // 物料项目达标
}

// 统计项目组达成情况
int projectGroupsAchieved = countAchievedProjectGroups();
int totalProjectGroups = getTotalProjectGroups();
if (projectGroupsAchieved >= totalProjectGroups) {
    achievedResults.add(ACHIEVED_RESULTS_Options_3); // 项目全部达标
} else if (projectGroupsAchieved > 0) {
    achievedResults.add(ACHIEVED_RESULTS_Options_4); // 项目部分达标
}

// 统计分销产品达成情况
int distributionProductsAchieved = countAchievedDistributionProducts();
int totalDistributionProducts = getTotalDistributionProducts();
if (distributionProductsAchieved >= totalDistributionProducts) {
    achievedResults.add(ACHIEVED_RESULTS_Options_7); // 产品SKU种类达标
}
```

**未达成项总结生成逻辑**：
```java
List<String> summaryIssues = new ArrayList<>();

// 检查陈列形式缺失
List<String> requiredDisplayForms = getRequiredDisplayForms();
List<String> actualDisplayForms = getActualDisplayForms();
if (!actualDisplayForms.containsAll(requiredDisplayForms)) {
    summaryIssues.add(SUMMARY_ISSUES_Options_1); // 陈列形式缺失/陈列形式未达标
}

// 检查产品项目未达标
if (productGroupsAchieved < totalProductGroups) {
    summaryIssues.add(SUMMARY_ISSUES_Options_5); // 产品不足
}

// 检查物料项目未达标
if (materialGroupsAchieved < totalMaterialGroups) {
    summaryIssues.add(SUMMARY_ISSUES_Options_3); // 物料不足
}

// 检查项目组未达标
if (projectGroupsAchieved < totalProjectGroups) {
    summaryIssues.add(SUMMARY_ISSUES_Options_4); // 整体项目数不足
}

// 检查分销产品未达标
if (distributionProductsAchieved < totalDistributionProducts) {
    summaryIssues.add(SUMMARY_ISSUES_Options_2); // 产品SKU种类不足
}
```

## 📋 标准对象体系详解

### 🔸 **标准定义对象**

#### **SuccessfulStoreRangeObj（铺货标准定义）**

**对象中文名**：铺货标准定义
**对象API名**：`SuccessfulStoreRangeObj`
**对象说明**：定义整体的铺货标准规则和计算方式

**关键字段**：
- `calculation_rules_attainment` - 达标计算方式（影响最终汇总结果）
- `standard_setting_method` - 标准设置方式
- `display_type` - 陈列形式
- `state` - 是否启用

**达标计算方式选项（影响最终汇总结果的计算）**：
- `all_attainment` - 所有项目、产品、物料达标则达标
- `all_display_attainment` - 所有已陈列项目、产品、物料达标则达标

**说明**：此达标计算方式决定了DisplayDistrAchSummaryObj的最终达成状态计算逻辑：



**达成状态判断**：
- `all_attainment`：所有陈列形式都必须达标，或所有项目都必须达标
- `all_display_attainment`：所有已陈列的陈列形式都必须达标，或所有已陈列的项目都必须达标

### 🔸 **达成父条件逻辑**

在达成结果计算中，系统支持父子条件的层级判断：

**父条件机制**：
- 通过`condition_parent_id`字段建立父子关系
- 分为两种父条件类型：**达成结果父条件** 和 **标准父条件**

**达成结果父条件**：
- 当达成结果有父条件时，只取父条件的结果作为总结果
- 子条件不参与最终结果计算，仅作为明细记录
- 用于汇总多个子项目的达成情况

**标准父条件**：
- 当标准定义有父子关系时，父标准作为子标准的达成数量标准
- 父标准定义了子标准需要达成的最低数量要求
- 子标准的达成数量必须满足父标准的要求才算整体达成

**应用场景**：
- **达成结果父条件**：多个产品项目的汇总结果，只显示父级汇总状态
- **标准父条件**：父标准定义"至少3个产品达标"，子标准定义具体产品要求

**计算逻辑**：
1. **达成结果父条件**：直接使用父条件的达成结果，忽略子条件结果
2. **标准父条件**：统计子标准的达成数量，与父标准要求的数量进行比较

## 📊 标准对象组成图

### 🔸 **整体标准架构**

```mermaid
graph TD
    A[SuccessfulStoreRangeObj<br/>铺货标准定义<br/>📋 必填字段:<br/>• calculation_rules_attainment<br/>• standard_setting_method<br/>• display_type] --> B[ProjectStandardsObj<br/>铺货项目标准<br/>📋 必填字段:<br/>• setting_type<br/>• display_level<br/>• quantity]

    A --> C[ProductItemStandardObj<br/>产品陈列标准<br/>📋 必填字段:<br/>• ways_achieve_standard<br/>• display_format<br/>• state]

    A --> D[MaterialStandardRequiremObj<br/>物料陈列标准<br/>📋 必填字段:<br/>• ways_achieve_standard<br/>• display_format<br/>• state]

    A --> E[DisplayTypeStandardsObj<br/>陈列形式标准<br/>📋 必填字段:<br/>• display_form<br/>• define_layer<br/>• successful_store]

    A --> F[MustDistributeProductsObj<br/>必分销品<br/>📋 必填字段:<br/>• product/product_multiple<br/>• display_format<br/>• min_report_product]

    C --> G[ProductItemStandardDetailObj<br/>产品项目标准<br/>📋 必填字段:<br/>• product_item<br/>• product_name<br/>• display_project<br/>• project_standards]

    D --> H[MaterialStandardDetailsObj<br/>物料项目标准<br/>📋 必填字段:<br/>• material_standard<br/>• material_name<br/>• display_project<br/>• material_quantity]

    B --> I[DisplayProjectsObj<br/>陈列项目<br/>📋 必填字段:<br/>• unit<br/>• state<br/>• display_describe]

    G --> I
    H --> I

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#f1f8e9
    style G fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#f3e5f5
```

上图展示了完整的标准对象组成架构，包括：

**顶层标准定义**：
- `SuccessfulStoreRangeObj` 作为根节点，定义整体的达标计算方式

**五大标准类型**：
1. **ProjectStandardsObj** - 铺货项目标准（整体陈列要求）
2. **ProductItemStandardObj** - 产品陈列标准（产品相关要求）
3. **MaterialStandardRequiremObj** - 物料陈列标准（物料相关要求）
4. **DisplayTypeStandardsObj** - 陈列形式标准（形式定义）
5. **MustDistributeProductsObj** - 必分销品（分销要求）

**明细标准对象**：
- `ProductItemStandardDetailObj` - 产品项目标准明细
- `MaterialStandardDetailsObj` - 物料项目标准明细
- `DisplayProjectsObj` - 陈列项目定义（被多个标准引用）

### 🔸 **达成方式影响范围**

```mermaid
graph TD
    A[SuccessfulStoreRangeObj<br/>calculation_rules_attainment<br/>🎯 影响范围: 最终汇总结果] --> A1[all_attainment<br/>所有项目、产品、物料达标则达标]
    A --> A2[all_display_attainment<br/>所有已陈列项目、产品、物料达标则达标]

    B[ProductItemStandardObj<br/>ways_achieve_standard<br/>🎯 影响范围: 产品标准组内判断] --> B1[选项1: 全都达标<br/>组内所有项目都必须达成]
    B --> B2[选项2: 任一达标<br/>组内任意一个项目达成即可]

    C[MaterialStandardRequiremObj<br/>ways_achieve_standard<br/>🎯 影响范围: 物料标准组内判断] --> C1[选项1: 全都达标<br/>组内所有项目都必须达成]
    C --> C2[选项2: 任一达标<br/>组内任意一个项目达成即可]

    A1 --> D[DisplayDistrAchSummaryObj<br/>最终达成状态]
    A2 --> D

    B1 --> E[产品标准组达成结果]
    B2 --> E

    C1 --> F[物料标准组达成结果]
    C2 --> F

    E --> D
    F --> D

    style A fill:#e1f5fe
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#ffebee
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style A1 fill:#e3f2fd
    style A2 fill:#e3f2fd
    style B1 fill:#f1f8e9
    style B2 fill:#f1f8e9
    style C1 fill:#fef7e0
    style C2 fill:#fef7e0
```

上图展示了不同层级的达成方式设置及其影响范围：

**三个层级的达成方式**：
1. **总体层级**：`SuccessfulStoreRangeObj.calculation_rules_attainment` 影响最终汇总结果
2. **产品组层级**：`ProductItemStandardObj.ways_achieve_standard` 影响产品标准组内判断
3. **物料组层级**：`MaterialStandardRequiremObj.ways_achieve_standard` 影响物料标准组内判断

**计算流程**：
- 各标准组内部先根据组内达成方式计算组达成结果
- 最终汇总时根据总体达成方式计算DisplayDistrAchSummaryObj的达成状态

### 🔸 **父条件逻辑流程**

```mermaid
graph TD
    A[开始评估] --> B{是否有父条件?<br/>condition_parent_id}

    B -->|无父条件| C[直接评估当前条件]
    B -->|有父条件| D{父条件类型?}

    D -->|达成结果父条件| E[只取父条件结果<br/>作为总结果]
    D -->|标准父条件| F[父标准定义达成数量要求<br/>统计子标准达成数量]

    C --> G[根据规则计算达成结果]

    F --> H{子标准达成数量<br/>≥ 父标准要求数量?}
    H -->|是| I[标准组达标]
    H -->|否| J[标准组未达标]

    E --> K[输出父条件结果]
    G --> L[输出当前条件结果]
    I --> M[输出标准组达成结果]
    J --> M

    style A fill:#e8f5e8
    style B fill:#fff3e0
    style C fill:#e3f2fd
    style D fill:#fff3e0
    style E fill:#ffebee
    style F fill:#fef7e0
    style G fill:#f1f8e9
    style H fill:#fff3e0
    style I fill:#e8f5e8
    style J fill:#ffebee
    style K fill:#ffebee
    style L fill:#e3f2fd
    style M fill:#fef7e0
```

上图展示了修正后的父子条件评估逻辑：

**两种父条件类型**：

1. **达成结果父条件**：
   - 当达成结果有父条件时，只取父条件的结果作为总结果
   - 子条件不参与最终结果计算，仅作为明细记录
   - 用于汇总多个子项目的达成情况

2. **标准父条件**：
   - 父标准定义子标准需要达成的最低数量要求
   - 统计子标准的达成数量，与父标准要求进行比较
   - 子标准达成数量 ≥ 父标准要求数量时，整个标准组才算达标

**应用价值**：
- **结果汇总**：避免重复计算，直接使用父级汇总结果
- **数量控制**：通过父标准控制子标准的达成要求
- **逻辑清晰**：明确区分结果汇总和标准控制两种场景

#### **ProjectStandardsObj（铺货项目标准）**

**对象中文名**：铺货项目标准
**对象API名**：`ProjectStandardsObj`
**对象说明**：定义整体陈列项目的标准要求

**关键字段**：
- `setting_type` - 设置类型（0-整体陈列标准，1-产品陈列标准，2-物料陈列标准）
- `display_level` - 层级要求
- `quantity` - 最低标准值
- `display_project` - 陈列项目

#### **ProductItemStandardObj（产品陈列标准）**

**对象中文名**：产品陈列标准
**对象API名**：`ProductItemStandardObj`
**对象说明**：定义产品陈列的标准要求

**关键字段**：
- `ways_achieve_standard` - 达标方式（影响产品标准组内判断）
- `display_format` - 适用陈列形式
- `state` - 是否启用

**达标方式选项（影响产品标准组内判断）**：
- `1` - 所有规则产品项目均达标（组内所有项目都必须达成）
- `2` - 满足一条规则即达标（组内任意一个项目达成即可）

**说明**：此达标方式决定了产品标准组内多个ProductItemStandardDetailObj项目之间的逻辑关系。当设置为"全都达标"时，该产品标准组下的所有产品项目都必须达成才算该组达标；当设置为"任一达标"时，只要有一个产品项目达成即可算该组达标。

#### **ProductItemStandardDetailObj（产品项目标准）**

**对象中文名**：产品项目标准
**对象API名**：`ProductItemStandardDetailObj`
**对象说明**：产品陈列标准的具体项目明细

**关键字段**：
- `product_item` - 关联产品陈列标准
- `product_name` - 产品名称
- `display_project` - 陈列项目
- `project_standards` - 最低标准值

#### **MaterialStandardRequiremObj（物料陈列标准）**

**对象中文名**：物料陈列标准
**对象API名**：`MaterialStandardRequiremObj`
**对象说明**：定义物料陈列的标准要求

**关键字段**：
- `ways_achieve_standard` - 达标方式（影响物料标准组内判断）
- `display_format` - 适用陈列形式
- `state` - 是否启用

**达标方式选项（影响物料标准组内判断）**：
- `1` - 所有规则产品项目均达标（组内所有项目都必须达成）
- `2` - 满足一条规则即达标（组内任意一个项目达成即可）

**说明**：此达标方式决定了物料标准组内多个MaterialStandardDetailsObj项目之间的逻辑关系。当设置为"全都达标"时，该物料标准组下的所有物料项目都必须达成才算该组达标；当设置为"任一达标"时，只要有一个物料项目达成即可算该组达标。

#### **MaterialStandardDetailsObj（物料项目标准）**

**对象中文名**：物料项目标准
**对象API名**：`MaterialStandardDetailsObj`
**对象说明**：物料陈列标准的具体项目明细

**关键字段**：
- `material_standard` - 关联物料陈列标准
- `material_name` - 物料名称
- `display_project` - 陈列项目
- `material_quantity` - 最低标准值

#### **DisplayTypeStandardsObj（陈列形式标准）**

**对象中文名**：陈列形式标准
**对象API名**：`DisplayTypeStandardsObj`
**对象说明**：定义陈列形式的标准要求

**关键字段**：
- `display_form` - 陈列形式
- `define_layer` - 是否按层定义
- `successful_store` - 陈列标准

#### **MustDistributeProductsObj（必分销品）**

**对象中文名**：必分销品
**对象API名**：`MustDistributeProductsObj`
**对象说明**：定义必须分销的产品标准

**关键字段**：
- `product` - 产品名称（单选）
- `product_multiple` - 产品名称（多选）
- `display_format` - 陈列形式
- `min_report_product` - 最低上报必销品品项数

#### **DisplayProjectsObj（陈列项目）**

**对象中文名**：陈列项目
**对象API名**：`DisplayProjectsObj`
**对象说明**：定义可用的陈列项目类型

**关键字段**：
- `unit` - 项目单位（层/个）
- `state` - 是否启用
- `display_describe` - 描述



## 📋 结果对象体系详解

### 🔸 **结果对象关系图**

```mermaid
graph TD
    subgraph "结果容器"
        A[AchievementResult<br/>总结果容器]
    end

    subgraph "项目达成结果"
        B1[totalProjectAchievements<br/>所有项目达成结果]
        B2[productProjectAchievements<br/>产品项目达成结果]
        B3[materialProjectAchievements<br/>物料项目达成结果]
        B4[overallProjectAchievements<br/>整体项目达成结果]
    end

    subgraph "汇总达成结果"
        C1[summaryDisplayAchievements<br/>陈列形式汇总达成结果]
        C2[distributionProductsAchievements<br/>分销产品达成结果]
        C3[displayDistrAchSummary<br/>总体达成汇总]
    end

    subgraph "具体对象类型"
        D1[DisplayProjectAchievementObj<br/>陈列项目达成]
        D2[SummaryDisplayAchievementObj<br/>陈列形式达成总结]
        D3[DistributionProductsAchievedObj<br/>分销达成情况]
        D4[DisplayDistrAchSummaryObj<br/>陈列铺货达成汇总]
    end

    A --> B1
    A --> B2
    A --> B3
    A --> B4
    A --> C1
    A --> C2
    A --> C3

    B1 --> D1
    B2 --> D1
    B3 --> D1
    B4 --> D1
    C1 --> D2
    C2 --> D3
    C3 --> D4

    style A fill:#e3f2fd
    style B1 fill:#f1f8e9
    style B2 fill:#f1f8e9
    style B3 fill:#f1f8e9
    style B4 fill:#f1f8e9
    style C1 fill:#fff3e0
    style C2 fill:#fff3e0
    style C3 fill:#fff3e0
    style D1 fill:#fce4ec
    style D2 fill:#fce4ec
    style D3 fill:#fce4ec
    style D4 fill:#fce4ec
```

### 🔸 **AchievementResult（总结果容器）**

```java
public class AchievementResult {
    // 所有项目达成结果的总集合
    private List<IObjectData> totalProjectAchievements = new ArrayList<>();

    // 产品项目达成结果
    private List<IObjectData> productProjectAchievements = new ArrayList<>();

    // 物料项目达成结果
    private List<IObjectData> materialProjectAchievements = new ArrayList<>();

    // 整体项目达成结果
    private List<IObjectData> overallProjectAchievements = new ArrayList<>();

    // 陈列形式汇总达成结果
    private List<IObjectData> summaryDisplayAchievements = new ArrayList<>();

    // 分销产品达成结果
    private List<IObjectData> distributionProductsAchievements = new ArrayList<>();

    // 总体达成汇总（最终结果）
    private IObjectData displayDistrAchSummary;
}
```

### 🔸 **DisplayProjectAchievementObj（陈列项目达成）**

**对象中文名**：陈列项目达成
**对象API名**：`DisplayProjectAchievementObj`
**对象说明**：记录单个项目的详细达成情况

#### **完整字段列表**

| 字段中文名 | 字段API名 | 数据类型 | 含义说明 |
|-----------|-----------|----------|----------|
| 缺少产品 | `absence_products` | 多选查找 | 未陈列的产品列表 |
| 条件父ID | `condition_parent_id` | 文本 | 父条件关联ID |
| 达成项 | `achievement_summary` | 多选项 | 已达成的项目类型 |
| 设置类型 | `setting_type` | 选择项 | 项目类型分类 |
| 关联规则组对象名称 | `rule_group_apiname` | 文本 | 规则组对象API名 |
| 差异值 | `difference_quantity` | 数字 | 差异值（实际-要求） |
| 要求物料分类 | `required_materials_classification` | 文本 | 标准要求的物料分类 |
| 项目名称 | `project_name` | 文本 | 陈列项目的名称 |
| 缺少物料分类 | `absence_materials_classification` | 文本 | 未使用的物料分类 |
| 达成结果 | `achieved_results` | 选择项 | 项目达成状态 |
| 关联报告 | `related_report` | 查找关联 | 关联的报告ID |
| 未达成项 | `summary_issues` | 多选项 | 未达成的问题类型 |
| 实际产品 | `actual_products` | 多选查找 | 实际陈列的产品列表 |
| 要求产品分类 | `required_products_classification` | 多选查找 | 标准要求的产品分类 |
| 实际产品分类 | `actual_products_classification` | 文本 | 实际陈列的产品分类 |
| 实际物料 | `actual_materials` | 多选查找 | 实际使用的物料列表 |
| 关联物料陈列标准 | `material_standards_id` | 查找关联 | 物料标准ID |
| 陈列形式 | `related_display_achievement` | 查找关联 | 关联的陈列形式ID |
| 层级 | `level` | 选择项 | 陈列层级要求 |
| 缺少产品分类 | `absence_products_classification` | 文本 | 未陈列的产品分类 |
| 关联规则组数据 | `rule_group_id` | 文本 | 关联的规则组ID |
| 要求项目数量 | `required_project_quantity` | 数字 | 标准要求的数量 |
| 要求物料 | `required_materials` | 多选查找 | 标准要求的物料列表 |
| 缺少物料 | `absence_materials` | 多选查找 | 未使用的物料列表 |
| 关联产品陈列标准 | `product_standards_id` | 查找关联 | 产品标准ID |
| 要求产品 | `required_products` | 多选查找 | 标准要求的产品列表 |
| 实际物料分类 | `actual_materials_classification` | 文本 | 实际使用的物料分类 |
| 实际项目数量 | `actual_project_quantity` | 数字 | 实际达成的数量 |
| 达标方式 | `ways_achieve_standard` | 选择项 | 达成判断方式 |
| 不达标项目数量 | `unmet_project_count__c` | 数字 | 未达标项目统计 |

#### **选择项字段的选项值**

**达成结果（achieved_results）选项**：
- `0` - 未达标（规则执行失败且有陈列时赋值）
- `1` - 达标（规则执行成功时赋值）
- `2` - 部分达标（有子条件且有过滤数据时赋值）
- `4` - 未陈列（没有过滤到陈列数据时赋值）
- `5` - 待审核（TPM活动证明系统判断状态为待审核时赋值）

**设置类型（setting_type）选项**：
- `1` - 整体陈列标准（ProjectStandardsFields对象时赋值）
- `2` - 产品陈列标准（ProductItemStandardDetailFields对象时赋值）
- `3` - 物料陈列标准（MaterialStandardDetailsFields对象时赋值）

**层级（level）选项**：
- `-1` - 每层（标准数据中层级为任意层级时赋值，用于分组聚合）
- `0` - 所有层级
- `1` - 第1层（从标准数据或报告数据中获取，当层级不为任意层级时赋值）
- `2` - 第2层（从标准数据或报告数据中获取，当层级不为任意层级时赋值）
- `3` - 第3层（从标准数据或报告数据中获取，当层级不为任意层级时赋值）
- `4` - 第4层（从标准数据或报告数据中获取，当层级不为任意层级时赋值）
- `5` - 第5层（从标准数据或报告数据中获取，当层级不为任意层级时赋值）
- `6` - 第6层（从标准数据或报告数据中获取，当层级不为任意层级时赋值）
- `7` - 第7层（从标准数据或报告数据中获取，当层级不为任意层级时赋值）

**达标方式（ways_achieve_standard）选项**：
- `1` - 所有规则产品项目均达标（从标准数据ACHIEVEMENT_WAY字段获取，用于达成率计算和分组判断）
- `2` - 满足一条规则即达标（从标准数据ACHIEVEMENT_WAY字段获取，用于达成率计算和分组判断，在TPM活动证明中也会赋值）

**达成项总结（achievement_summary）选项**：
- `1` - 达标（规则执行成功时赋值）
- `2` - 部分达标
- `3` - 物料达标（物料项目达成时赋值）
- `4` - 产品达标（产品项目达成时赋值）
- `5` - SKU数达标
- `6` - 排面数达标
- `7` - 层数达标（层级为任意层级且规则执行成功时赋值）

**未达成项（summary_issues）选项**：
- `1` - SKU数不足
- `2` - 层数不足（层级为任意层级且规则执行失败时赋值）
- `3` - 排面数不足
- `4` - 物料不足（物料项目未达成时赋值）
- `5` - 物料种类不足

### 🔸 **SummaryDisplayAchievementObj（陈列形式达成总结）**

**对象中文名**：陈列形式达成总结
**对象API名**：`SummaryDisplayAchievementObj`
**对象说明**：记录单个陈列形式的整体达成情况

#### **完整字段列表**

| 字段中文名 | 字段API名 | 数据类型 | 含义说明 |
|-----------|-----------|----------|----------|
| 陈列形式 | `related_display_achievement` | 查找关联 | 关联的陈列形式ID |
| 达成项总结 | `achievement_summary` | 多选项 | 该陈列形式已达成的项目 |
| 要求位置 | `required_location` | 选择项 | 标准要求的陈列位置 |
| 陈列照 | `display_photo` | 图片 | 实际陈列的照片 |
| 实际位置 | `actual_location` | 选择项 | 实际陈列的位置 |
| 未达成项总结 | `summary_issues` | 多选项 | 该陈列形式的问题 |
| 是否按层 | `define_layer` | 选择项 | 是否按层级定义 |
| 规则编码 | `rule_group_id` | 文本 | 关联的规则组ID |
| 达成状态 | `achieved_results` | 选择项 | 陈列形式达成状态 |
| 关联报告 | `related_report` | 查找关联 | 关联的报告ID |

#### **选择项字段的选项值**

**要求位置（required_location）选项**：
- `1` - 主通道（从DisplayTypeStandardsFields.DISPLAY_POSITION获取赋值）
- `2` - 门店显眼位（从DisplayTypeStandardsFields.DISPLAY_POSITION获取赋值）
- `3` - 入口处（从DisplayTypeStandardsFields.DISPLAY_POSITION获取赋值）
- `4` - 端头货架（从DisplayTypeStandardsFields.DISPLAY_POSITION获取赋值）
- `5` - 堆头陈列区（从DisplayTypeStandardsFields.DISPLAY_POSITION获取赋值）
- `6` - 收银台附近（从DisplayTypeStandardsFields.DISPLAY_POSITION获取赋值）
- `7` - 冰柜和冷藏区（从DisplayTypeStandardsFields.DISPLAY_POSITION获取赋值）
- `8` - 竞品旁边（从DisplayTypeStandardsFields.DISPLAY_POSITION获取赋值）

**实际位置（actual_location）选项**：
- `1` - 主通道
- `2` - 门店显眼位
- `3` - 入口处
- `4` - 端头货架
- `5` - 堆头陈列区
- `6` - 收银台附近
- `7` - 冰柜和冷藏区
- `8` - 竞品旁边

**是否按层（define_layer）选项**：
- `0` - 否（从DisplayTypeStandardsFields.DEFINE_LAYER获取赋值）
- `1` - 是（从DisplayTypeStandardsFields.DEFINE_LAYER获取赋值）

**达成状态（achieved_results）选项**：
- `1` - 达标（系统判断状态为通过时赋值）
- `2` - 部分达标（系统判断状态为部分通过时赋值）
- `3` - 未达标（系统判断状态为失败时赋值）
- `4` - 未陈列（系统判断状态为空或未陈列时赋值）
- `5` - 待审（系统判断状态为待审核时赋值）

**达成项总结（achievement_summary）选项**：
- `1` - 项目全部达标（项目组达成数 >= 项目组总数时赋值）
- `2` - 项目部分达标（项目组达成数 > 0且 < 项目组总数时赋值）
- `3` - 物料项目达标（物料组达成数 >= 物料组总数时赋值）
- `4` - 产品项目达标（产品组达成数 >= 产品组总数时赋值）
- `5` - 产品SKU种类达标
- `6` - 物料SKU种类达标
- `7` - 产品排面数达标
- `8` - 产品层数达标

**未达成项总结（summary_issues）选项**：
- `1` - 产品项目未达标（产品组达成数 < 产品组总数时赋值）
- `2` - 产品SKU种类不足
- `3` - 物料SKU种类不足（物料组达成数 < 物料组总数时赋值）
- `4` - 产品排面数不足
- `5` - 产品层数不足
- `6` - 产品组数不足
- `7` - 部分层未达标

### 🔸 **DisplayDistrAchSummaryObj（陈列铺货达成汇总）**

**对象中文名**：陈列铺货达成汇总
**对象API名**：`DisplayDistrAchSummaryObj`
**对象说明**：记录整体的最终达成汇总结果

#### **完整字段列表**

| 字段中文名 | 字段API名 | 数据类型 | 含义说明 |
|-----------|-----------|----------|----------|
| 数据来源单据对象 | `related_api_names` | 多选项 | 数据来源的对象API名列表 |
| 报告名称 | `report_title` | 文本 | 报告的标题 |
| 外勤ID | `check_id` | 查找关联 | 关联的外勤检查ID |
| 要求陈列形式 | `required_display_forms` | 多选查找 | 标准要求的陈列形式列表 |
| 数据来源单据数据 | `related_object_data` | 长文本 | 来源单据的数据内容 |
| 达成项总结 | `achieved_results` | 多选项 | 总体已达成的内容 |
| 数据来源单据 | `related_object` | 查找关联 | 来源单据ID |
| 标准要求类型 | `required_standard_types` | 多选项 | 包含的标准类型 |
| 缺少陈列形式 | `absence_display_forms` | 多选查找 | 未陈列的形式列表 |
| 实际陈列形式 | `actual_display_forms` | 多选查找 | 实际有陈列的形式列表 |
| 日期 | `standard_achievement_date` | 日期时间 | 达成评估的时间戳 |
| 门店名称 | `store_name` | 文本 | 门店名称 |
| 未达成项总结 | `summary_issues` | 多选项 | 总体存在的问题 |
| 达成状态 | `achievement_status` | 选择项 | 总体达成状态 |
| 陈列标准设置方式 | `methods_display_standards` | 选择项 | 陈列标准的设置方式 |
| 关联业务单据数据 | `business_documents_id` | 查找关联 | 业务单据ID |
| 分销标准设置方式 | `methods_distribution_standards` | 选择项 | 分销标准的设置方式 |
| 关联业务单据对象名 | `business_documents_apiname` | 文本 | 业务单据对象API名 |
| 记录类型 | `record_type` | 选择项 | 记录类型标识 |
| 规则组API名 | `rule_group_apiName` | 文本 | 规则组对象API名 |
| 规则组ID | `rule_group_id` | 文本 | 规则组ID |

#### **选择项字段的选项值**

**标准要求类型（required_standard_types）选项**：
- `1` - 产品要求
- `2` - 品类要求
- `3` - 有物料要求
- `4` - 有物料分类要求
- `5` - 有整体要求

**达成状态（achievement_status）选项**：
- `1` - 全部达标（所有陈列形式都达标或所有项目都达标时赋值）
- `2` - 部分达标（部分陈列形式达标或部分项目达标时赋值）
- `3` - 均不达标（所有陈列形式都未达标或所有项目都未达标时赋值）

**陈列标准设置方式（methods_display_standards）选项**：
- `1` - 项目标准
- `2` - 陈列形式+项目标准

**分销标准设置方式（methods_distribution_standards）选项**：
- `1` - 按门店
- `2` - 按门店产品
- `3` - 按陈列形式+产品

**记录类型（record_type）选项**：
- `default__c` - 陈列报告（在查询和过滤时使用）
- `distribution_report__c` - 分销报告（在查询和过滤时使用）
- `activity_evidence_report__c` - 活动证明报告（在查询和过滤时使用）

**达成项总结（achieved_results）选项**：
- `1` - 陈列形式全部达标
- `2` - 陈列形式部分达标
- `3` - 项目全部达标（项目组达成数 >= 项目组总数时赋值）
- `4` - 项目部分达标（项目组达成数 > 0且 < 项目组总数时赋值）
- `5` - 产品项目达标（产品组达成数 >= 产品组总数时赋值）
- `6` - 物料项目达标（物料组达成数 >= 物料组总数时赋值）
- `7` - 产品SKU种类达标（分销产品达成数 >= 分销产品总数时赋值）
- `8` - 物料SKU种类达标
- `9` - 产品层数达标

**未达成项总结（summary_issues）选项**：
- `1` - 陈列形式缺失/陈列形式未达标（存在未陈列的陈列形式时赋值）
- `2` - 产品SKU种类不足（分销产品达成数 < 分销产品总数时赋值）
- `3` - 物料不足（物料组达成数 < 物料组总数时赋值）
- `4` - 整体项目数不足（项目组达成数 < 项目组总数时赋值）
- `5` - 产品不足（产品组达成数 < 产品组总数时赋值）
- `6` - 产品组数不足
- `7` - 部分层未达标

### 🔸 **DistributionProductsAchievedObj（分销达成情况）**

**对象中文名**：分销达成情况
**对象API名**：`DistributionProductsAchievedObj`
**对象说明**：记录必分销产品的达成情况

#### **完整字段列表**

| 字段中文名 | 字段API名 | 数据类型 | 含义说明 |
|-----------|-----------|----------|----------|
| 要求产品范围 | `required_standard` | 多选查找 | 要求的产品范围 |
| 实际产品范围 | `actual_standard` | 多选查找 | 实际分销的产品范围 |
| 达成SKU数量 | `quantity_products_achieved` | 数字 | 达成的SKU数量 |
| 陈列形式 | `display_format` | 查找关联 | 分销要求的陈列形式 |
| 关联的标准 | `related_standard` | 查找关联 | 关联的标准ID |
| 产品分类 | `product_category` | 查找关联 | 单选产品分类 |
| 产品分类（多选） | `product_categories` | 多选查找 | 多选产品分类列表 |
| 关联报告 | `related_report` | 查找关联 | 关联的报告ID |
| 标准值 | `standard_value` | 数字 | 要求分销数量 |
| 分销状态 | `status` | 选择项 | 分销达成状态 |

#### **选择项字段的选项值**

**分销状态（status）选项**：
- `0` - 未分销（规则执行失败时赋值）
- `1` - 已分销（规则执行成功时赋值）

### 🔸 **MustDistributeProductsObj（必分销品）**

**对象中文名**：必分销品
**对象API名**：`MustDistributeProductsObj`
**对象说明**：定义必须分销的产品标准

#### **完整字段列表**

| 字段中文名 | 字段API名 | 数据类型 | 含义说明 |
|-----------|-----------|----------|----------|
| 产品名称 | `product` | 查找关联 | 单选产品 |
| 陈列场景 | `display_scene` | 选择项 | 陈列场景类型 |
| 产品名称（多选） | `product_multiple` | 多选查找 | 多选产品列表 |
| 陈列形式 | `display_format` | 查找关联 | 要求的陈列形式 |
| 成功门店项目标准 | `successful_store` | 查找关联 | 关联的成功门店标准 |
| 产品分类 | `product_category` | 查找关联 | 单选产品分类 |
| 产品分类（多选） | `product_categories` | 多选查找 | 多选产品分类 |
| 最低上报必销品品项数 | `min_report_product` | 数字 | 最低上报产品数量 |

#### **选择项字段的选项值**





## 🎨 达成状态值详解

### **基础达成状态编码（实际使用）**

| 状态代码 | 状态名称 | 适用对象 | 判断条件 | 显示建议 |
|---------|---------|---------|---------|---------|
| `1` | **达标** | 所有对象 | 规则执行成功 | 🟢 绿色 |
| `2` | **部分达标** | 项目/汇总对象 | 有子条件且有过滤数据 | 🟡 黄色 |
| `0` | **未达标** | 项目对象 | 规则执行失败且有陈列 | 🔴 红色 |
| `3` | **未达标** | 汇总对象 | 所有项目都未达标 | 🔴 红色 |
| `4` | **未陈列** | 项目/形式对象 | 没有过滤到陈列数据 | ⚪ 灰色 |
| `5` | **待审核** | 形式对象 | 系统判断状态为待审核 | 🔵 蓝色 |





## 🔍 实际应用示例

### **示例1：端架产品陈列达成评估**

**业务场景**：评估端架陈列中可口可乐产品的排面数是否达标

**原始上报数据**（50条陈列记录）：
```json
[
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 3, "layer": 1, "store_id": "store_A"},
  {"display_format": "端架", "product_id": "prod_002", "row_numbers": 2, "layer": 1, "store_id": "store_A"},
  {"display_format": "货架", "product_id": "prod_001", "row_numbers": 4, "layer": 2, "store_id": "store_A"},
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 2, "layer": 2, "store_id": "store_A"},
  {"display_format": "端架", "product_id": "prod_003", "row_numbers": 1, "layer": 1, "store_id": "store_A"},
  // ... 更多数据
]
```

**标准配置**：
- 陈列形式：端架
- 目标产品：可口可乐（prod_001）
- 排面数要求：≥ 8排面
- 达成方式：全都达标

**三层规则执行详解**：

#### **第1层：过滤规则（ProductItemStandardDetailObj_filter）**
**过滤条件**：`display_format = "端架"`
**过滤前**：50条记录
**过滤后**：15条记录
```json
[
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 3, "layer": 1},
  {"display_format": "端架", "product_id": "prod_002", "row_numbers": 2, "layer": 1},
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 2, "layer": 2},
  {"display_format": "端架", "product_id": "prod_003", "row_numbers": 1, "layer": 1},
  // ... 只保留端架陈列数据
]
```
**用途**：筛选出端架陈列的所有数据，排除货架、堆头等其他陈列形式

#### **第2层：匹配规则（ProductItemStandardDetailObj_match）**
**匹配条件**：`product_id = "prod_001"`
**匹配前**：15条记录
**匹配后**：6条记录
```json
[
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 3, "layer": 1},
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 2, "layer": 2},
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 1, "layer": 1},
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 2, "layer": 1},
  // ... 只保留可口可乐产品数据
]
```
**用途**：在端架陈列数据中，进一步筛选出可口可乐产品的陈列记录

#### **第3层：达成规则（ProductItemStandardDetailObj_achievement）**
**聚合配置**：
- 分组字段：`product_id`
- 聚合字段：`row_numbers`
- 聚合方式：`SUM`

**聚合计算**：
```json
{
  "prod_001": {
    "total_row_numbers": 8,  // 3+2+1+2 = 8排面
    "record_count": 4,
    "required_quantity": 8,
    "achieved": true         // 8 ≥ 8，达标
  }
}
```

**达成判断**：
- 实际排面数：8
- 要求排面数：8
- 判断结果：✅ 达标

#### **数据映射转换**：
```json
// 映射配置应用
{
  "product_id": "prod_001" → "product_name": "可口可乐",
  "sum(row_numbers)": 8 → "排面数": 8,
  "count(distinct product_id)": 1 → "SKU数量": 1
}
```

#### **最终结果生成**：
```json
// DisplayProjectAchievementObj
{
  "achieved_results": "1",                    // 达标
  "project_name": "端架产品排面数项目",
  "setting_type": "2",                        // 产品陈列标准
  "required_project_quantity": 8,
  "actual_project_quantity": 8,
  "difference_quantity": 0,
  "required_products": ["prod_001"],
  "actual_products": ["prod_001"],
  "absence_products": [],
  "achievement_summary": ["1", "4"],          // 达标 + 产品达标
  "summary_issues": []                        // 无问题
}
```

### **示例2：物料陈列未达标评估**

**业务场景**：评估端架陈列中促销物料的使用情况

**原始上报数据**（30条物料记录）：
```json
[
  {"display_format": "端架", "material_id": "mat_001", "quantity": 2, "material_type": "促销牌"},
  {"display_format": "端架", "material_id": "mat_002", "quantity": 1, "material_type": "价格牌"},
  {"display_format": "货架", "material_id": "mat_001", "quantity": 3, "material_type": "促销牌"},
  {"display_format": "端架", "material_id": "mat_003", "quantity": 0, "material_type": "展示架"},
  // ... 更多数据
]
```

**标准配置**：
- 陈列形式：端架
- 目标物料：促销牌（mat_001）
- 数量要求：≥ 5个
- 达成方式：全都达标

**三层规则执行详解**：

#### **第1层：过滤规则（MaterialStandardDetailsObj_filter）**
**过滤条件**：`display_format = "端架"`
**过滤后**：12条记录（只保留端架陈列的物料数据）

#### **第2层：匹配规则（MaterialStandardDetailsObj_match）**
**匹配条件**：`material_id = "mat_001" AND material_type = "促销牌"`
**匹配后**：3条记录
```json
[
  {"display_format": "端架", "material_id": "mat_001", "quantity": 2, "material_type": "促销牌"},
  {"display_format": "端架", "material_id": "mat_001", "quantity": 1, "material_type": "促销牌"},
  {"display_format": "端架", "material_id": "mat_001", "quantity": 1, "material_type": "促销牌"}
]
```

#### **第3层：达成规则（MaterialStandardDetailsObj_achievement）**
**聚合计算**：
```json
{
  "mat_001": {
    "total_quantity": 4,     // 2+1+1 = 4个
    "required_quantity": 5,
    "achieved": false        // 4 < 5，未达标
  }
}
```

**最终结果**：
```json
// DisplayProjectAchievementObj
{
  "achieved_results": "0",                    // 未达标
  "project_name": "端架物料数量项目",
  "setting_type": "3",                        // 物料陈列标准
  "required_project_quantity": 5,
  "actual_project_quantity": 4,
  "difference_quantity": -1,
  "required_materials": ["mat_001"],
  "actual_materials": ["mat_001"],
  "absence_materials": [],
  "achievement_summary": [],                  // 无达成项
  "summary_issues": ["4"]                     // 物料不足
}
```

### **示例3：未陈列情况评估**

**业务场景**：评估堆头陈列中雪碧产品的陈列情况

**原始上报数据**（20条陈列记录）：
```json
[
  {"display_format": "端架", "product_id": "prod_001", "row_numbers": 3},
  {"display_format": "货架", "product_id": "prod_002", "row_numbers": 2},
  {"display_format": "端架", "product_id": "prod_003", "row_numbers": 1},
  // ... 没有堆头陈列数据
]
```

**标准配置**：
- 陈列形式：堆头
- 目标产品：雪碧（prod_004）
- 排面数要求：≥ 6排面

**三层规则执行详解**：

#### **第1层：过滤规则（ProductItemStandardDetailObj_filter）**
**过滤条件**：`display_format = "堆头"`
**过滤结果**：0条记录（没有堆头陈列数据）

#### **第2层：匹配规则（跳过）**
**原因**：第1层过滤后没有数据，直接跳过第2层和第3层

#### **第3层：达成规则（跳过）**
**原因**：没有匹配数据，无法进行聚合计算

**达成判断逻辑**：
```java
if (!ruleExecutionResult.hasDisplay()) {
    // 没有过滤到任何陈列数据
    achievement.set(ACHIEVED_RESULTS, ACHIEVED_RESULTS_Options_4); // 未陈列
}
```

**最终结果**：
```json
// DisplayProjectAchievementObj
{
  "achieved_results": "4",                    // 未陈列
  "project_name": "堆头产品排面数项目",
  "setting_type": "2",                        // 产品陈列标准
  "required_project_quantity": 6,
  "actual_project_quantity": 0,
  "difference_quantity": -6,
  "required_products": ["prod_004"],
  "actual_products": [],
  "absence_products": ["prod_004"],
  "achievement_summary": [],                  // 无达成项
  "summary_issues": []                        // 未陈列不算问题
}
```

### **示例4：三层规则执行对比总结**

| 示例 | 第1层过滤结果 | 第2层匹配结果 | 第3层聚合结果 | 最终状态 | 状态代码 |
|------|-------------|-------------|-------------|---------|---------|
| **端架可口可乐** | 15条→端架数据 | 6条→可口可乐数据 | 8排面≥8要求 | 达标 | `1` |
| **端架促销物料** | 12条→端架数据 | 3条→促销牌数据 | 4个<5要求 | 未达标 | `0` |
| **堆头雪碧** | 0条→无堆头数据 | 跳过 | 跳过 | 未陈列 | `4` |

**关键规律**：
1. **第1层过滤**：决定是否有基础陈列数据，影响"未陈列"状态
2. **第2层匹配**：决定是否有目标业务数据，影响实际计算基础
3. **第3层聚合**：决定最终达成结果，影响"达标/未达标"状态
4. **数据传递**：每层的输出都是下一层的输入，形成数据漏斗效应

## 💡 关键特性说明

### **1. 灰度功能控制**
系统支持通过灰度开关调整判断逻辑：
- 当启用`isPartialDisplayedNotAchieved`时，"部分达标"和"未陈列"都会被判定为"未达标"

### **2. 层级化评估**
系统采用三层评估体系：
- **项目级**：单个项目的达成情况（DisplayProjectAchievementObj）
- **陈列形式级**：单个陈列形式的达成情况（SummaryDisplayAchievementObj）
- **总体级**：整体的达成情况（DisplayDistrAchSummaryObj）

### **3. 动态阈值设置**
不同的业务场景可以设置不同的达成标准：
- 数量阈值（如：排面数 ≥ 3）
- 比例阈值（如：达成率 ≥ 80%）
- 存在性要求（如：必须有指定产品）

### **4. 分组聚合支持**
- 支持按层级分组计算（当层级设置为"任意层级"时）
- 支持多维度聚合（DISTINCT_COUNT、SUM等）
- 支持复杂的条件组合判断

## 🎯 使用建议

1. **查看达成状态**：优先关注状态代码和颜色标识
2. **分析未达成原因**：查看"未达成项总结"了解具体问题
3. **制定改进计划**：根据问题总结针对性改进
4. **监控达成趋势**：定期查看达成率变化情况

这套达成结果赋值逻辑确保了评估的客观性和一致性，帮助使用人员准确了解陈列执行情况并及时调整策略。
