<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-crm-fmcg-wq</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>fs-crm-fmcg-wq-common</artifactId>
    <packaging>jar</packaging>

    <properties>
<!--        <maven.compiler.source>${java.version}</maven.compiler.source>-->
<!--        <maven.compiler.target>${jdk.version}</maven.compiler.target>-->
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-notice-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
<!--        Maven: com.facishare:fs-organization-paas-api:3.0.0-SNAPSHOT-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-paas-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mongo-spring-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare.appserver</groupId>
            <artifactId>fs-appserver-common-tools</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-timezone-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.16</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>morphia-logging-slf4j</artifactId>
                    <groupId>org.mongodb.morphia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>morphia</artifactId>
                    <groupId>org.mongodb.morphia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>druid</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-pod-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-eservice-common</artifactId>
                    <groupId>com.facishare.open</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-web</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.fxiaoke</groupId>-->
<!--            <artifactId>fs-paas-gnomon-api</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata-restdriver</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-fcp</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-fsi-proxy</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-privilege-temp</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>javassist-3.14.0-GA</artifactId>
                    <groupId>org.ow2.util.bundles</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-fsi-proxy</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-appserver-common-tools</artifactId>
                    <groupId>com.facishare.appserver</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-qixin-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mq</artifactId>
<!--            <version>1.0.3-SNAPSHOT</version>-->
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-enterprise-id-account-converter</artifactId>
<!--            <version>1.0-SNAPSHOT</version>-->
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.facishare</groupId>-->
<!--            <artifactId>fs-union-account-outapi</artifactId>-->
<!--            <version>1.0.3-SNAPSHOT</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>fs-metadata-api</artifactId>-->
<!--                    <groupId>com.facishare</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>easyexcel</artifactId>-->
<!--                    <groupId>com.alibaba</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.facishare.open</groupId>-->
<!--            <artifactId>fs-wechat-union-core-api</artifactId>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>mongo-spring-support</artifactId>-->
<!--                    <groupId>com.github.colin-lee</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>mongo-java-driver</artifactId>-->
<!--                    <groupId>org.mongodb</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>ehcache</artifactId>-->
<!--                    <groupId>org.ehcache</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>commons-pool</artifactId>-->
<!--                    <groupId>commons-pool</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>morphia</artifactId>-->
<!--                    <groupId>org.mongodb.morphia</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>fs-qixin-api</artifactId>-->
<!--                    <groupId>com.facishare</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-api</artifactId>
            <version>0.1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.appserver</groupId>
            <artifactId>checkins-v2-api</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.facishare.open</groupId>-->
<!--            <artifactId>fs-wechat-proxy-core-api</artifactId>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>resteasy-client</artifactId>-->
<!--                    <groupId>org.jboss.resteasy</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>xstream</artifactId>-->
<!--                    <groupId>com.thoughtworks.xstream</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>poi-ooxml</artifactId>-->
<!--                    <groupId>org.apache.poi</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        暂时强制指定 后续走父 pom-->
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
<!--            <version>1.4.20</version>-->
        </dependency>
        <!--灰度接入包-->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>gray-release</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.jetbrains.kotlin</groupId>-->
<!--            <artifactId>kotlin-runtime</artifactId>-->
<!--            <version>1.0.6</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>2.3.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.promeg</groupId>
            <artifactId>tinypinyin</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-coordination</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-fsi-proxy</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.facishare</groupId>-->
<!--            <artifactId>fs-rocketmq-support</artifactId>-->
<!--&lt;!&ndash;            <version>1.0.0-SNAPSHOT</version>&ndash;&gt;-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.facishare.open</groupId>-->
<!--            <artifactId>fs-wechat-dubbo-rest-outer-api</artifactId>-->
<!--            <version>2.0.0-SNAPSHOT</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>fs-dubbo-rest-plugin</artifactId>-->
<!--                    <groupId>com.facishare</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.fxiaoke.fmcg</groupId>
            <artifactId>fs-fmcg-framework-metadata</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-uc-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.appserver</groupId>
            <artifactId>checkins-office-v2-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.appserver</groupId>
            <artifactId>checkins-model-db</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-pod-client</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>fs-druid</artifactId>-->
<!--            <version>1.2.15-SNAPSHOT</version>-->
<!--        </dependency>-->
    </dependencies>

</project>