package com.facishare.crm.fmcg.wq.constants;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CheckinStatus枚举测试类
 */
@DisplayName("CheckinStatus枚举测试")
class CheckinStatusTest {

    @ParameterizedTest
    @MethodSource("provideStatusValues")
    @DisplayName("测试CheckinStatus枚举的intValue方法")
    void testIntValue(CheckinStatus status, int expectedValue) {
        assertEquals(expectedValue, status.intValue(), 
                status + "的intValue应返回" + expectedValue);
    }

    // 提供测试数据
    private static Stream<Arguments> provideStatusValues() {
        return Stream.of(
                Arguments.of(CheckinStatus.InitSuccess, 0),
                Arguments.of(CheckinStatus.MongoSuccess, 1),
                Arguments.of(CheckinStatus.PicSuccess, 2),
                Arguments.of(CheckinStatus.FeedSuccess, 3),
                Arguments.of(CheckinStatus.Finish, 4),
                Arguments.of(CheckinStatus.Delete, -1),
                Arguments.of(CheckinStatus.TriggerWorkflow, -2),
                Arguments.of(CheckinStatus.WorkflowReject, -3)
        );
    }

    @Test
    @DisplayName("测试CheckinStatus枚举的所有值")
    void testAllValues() {
        CheckinStatus[] values = CheckinStatus.values();
        assertEquals(8, values.length, "CheckinStatus应有8个枚举值");
        
        // 验证所有枚举值都存在
        assertNotNull(CheckinStatus.valueOf("InitSuccess"));
        assertNotNull(CheckinStatus.valueOf("MongoSuccess"));
        assertNotNull(CheckinStatus.valueOf("PicSuccess"));
        assertNotNull(CheckinStatus.valueOf("FeedSuccess"));
        assertNotNull(CheckinStatus.valueOf("Finish"));
        assertNotNull(CheckinStatus.valueOf("Delete"));
        assertNotNull(CheckinStatus.valueOf("TriggerWorkflow"));
        assertNotNull(CheckinStatus.valueOf("WorkflowReject"));
    }
}
