package com.facishare.crm.fmcg.wq.util;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * RedisUtils测试类
 */
public class RedisUtilsTest {

    @Test
    public void testConstants() {
        // 测试RedisUtils常量
        assertEquals("fs-crm-fmcg-channel-data-", RedisUtils.CHANNEL_DATA_PREFIX);
        assertEquals("fs-crm-fmcg-wq-office_export_template_by_account_", RedisUtils.EXPORT_TEMPLATE_REDIS_KEY);
        assertEquals("fs-crm-fmcg-wq-office_import_by_account_", RedisUtils.IMPORT_BY_ACCOUNT_REDIS_KEY);
    }

    @Test
    public void testGetKey() {
        // 通过反射测试私有方法getKey
        RedisUtils redisUtils = new RedisUtils();
        String tenantId = "tenant123";
        String dealerId = "dealer456";

        // 测试生成的Redis键格式
        try {
            java.lang.reflect.Method method = RedisUtils.class.getDeclaredMethod("getKey", String.class, String.class,
                    String.class);
            method.setAccessible(true);
            String result = (String) method.invoke(redisUtils, RedisUtils.CHANNEL_DATA_PREFIX, tenantId, dealerId);
            assertEquals(RedisUtils.CHANNEL_DATA_PREFIX + tenantId + "_" + dealerId, result);
        } catch (Exception e) {
            fail("反射调用getKey方法失败: " + e.getMessage());
        }
    }

    @Test
    public void testGetKeyForScheduleImpOrExp() {
        // 测试getKeyForScheduleImpOrExp方法
        RedisUtils redisUtils = new RedisUtils();
        String key = "test_key_";
        String tenantId = "tenant123";
        String ruleId = "rule456";

        String result = redisUtils.getKeyForScheduleImpOrExp(key, tenantId, ruleId);
        assertEquals(key + tenantId + "_" + ruleId, result);
    }
}
