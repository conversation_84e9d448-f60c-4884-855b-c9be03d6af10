package com.facishare.crm.fmcg.wq.constants;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * SystemConstants接口测试类
 */
public class SystemConstantsTest {

    @Test
    public void testConstants() {
        // 测试SystemConstants常量值
        assertEquals("AccountObj", SystemConstants.AccountApiName);
        assertEquals("PaymentObj", SystemConstants.PaymentApiName);
        assertEquals("RefundObj", SystemConstants.RefundApiName);
        assertEquals("OrderPaymentObj", SystemConstants.OrderPaymentApiname);
        assertEquals("SalesOrderObj", SystemConstants.SalesOrderApiName);
        assertEquals("object_describe_api_name", SystemConstants.ObjectDescribeApiName);
        assertEquals("object_describe_id", SystemConstants.ObjectDescribeId);
    }

    @Test
    public void testRelevantTeam() {
        // 测试RelevantTeam枚举值
        assertNotNull(SystemConstants.RelevantTeam.TeamMemberRole);
        assertNotNull(SystemConstants.RelevantTeam.TeamMemberPermissionType);
        assertNotNull(SystemConstants.RelevantTeam.TeamMemberEmployee);
    }

    @Test
    public void testField() {
        // 测试Field枚举值
        assertNotNull(SystemConstants.Field.Id);
        assertNotNull(SystemConstants.Field.LifeStatusBeforeInvalid);
        assertNotNull(SystemConstants.Field.LifeStatus);
        assertNotNull(SystemConstants.Field.isDeleted);
        assertNotNull(SystemConstants.Field.LockStatus);
    }

    @Test
    public void testActionCode() {
        // 测试ActionCode枚举值
        assertNotNull(SystemConstants.ActionCode.Abolish);
        assertNotNull(SystemConstants.ActionCode.Edit);
        assertNotNull(SystemConstants.ActionCode.ChangeOwner);
        assertNotNull(SystemConstants.ActionCode.AddTeamMember);
        assertNotNull(SystemConstants.ActionCode.EditTeamMember);
    }

    @Test
    public void testLifeStatus() {
        // 测试LifeStatus枚举值
        assertNotNull(SystemConstants.LifeStatus.Ineffective);
        assertNotNull(SystemConstants.LifeStatus.UnderReview);
        assertNotNull(SystemConstants.LifeStatus.Normal);
        assertNotNull(SystemConstants.LifeStatus.InChange);
        assertNotNull(SystemConstants.LifeStatus.Invalid);
    }
}
