package com.facishare.crm.fmcg.wq;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FmcgGray测试类
 */
@DisplayName("FmcgGray接口测试")
class FmcgGrayTest {

    @Test
    @DisplayName("测试Checkins.EI枚举值")
    void testCheckinsEIEnumValues() {
        // 验证枚举值存在
        assertNotNull(FmcgGray.Checkins.EI.removeCheckinsObjDefaultBatchButtonsForEIs);
        assertNotNull(FmcgGray.Checkins.EI.showCheckinsObjAppointedButtons);
        assertNotNull(FmcgGray.Checkins.EI.showCheckinsWebDetailCustomButtons);
        assertNotNull(FmcgGray.Checkins.EI.syncAchievementResultsAllCompleteTime);
        assertNotNull(FmcgGray.Checkins.EI.iOSIntegral);
    }

    @Test
    @DisplayName("测试Checkins.EA枚举值")
    void testCheckinsEAEnumValues() {
        // 验证枚举值存在
        assertNotNull(FmcgGray.Checkins.EA.isYLProductCollection);
        assertNotNull(FmcgGray.Checkins.EA.areaManage);
        assertNotNull(FmcgGray.Checkins.EA.noValidateCoustomerIdLookup);
    }

    @Test
    @DisplayName("测试Supply.EI枚举值")
    void testSupplyEIEnumValues() {
        // 验证枚举值存在
        assertNotNull(FmcgGray.Supply.EI.closedSyncAvailablePriceBookObj);
    }
}
