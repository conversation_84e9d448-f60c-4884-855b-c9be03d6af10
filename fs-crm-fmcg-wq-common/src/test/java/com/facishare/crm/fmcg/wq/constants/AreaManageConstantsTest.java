package com.facishare.crm.fmcg.wq.constants;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * AreaManageConstants接口测试类
 */
@DisplayName("AreaManageConstants接口测试")
class AreaManageConstantsTest {

    @Test
    @DisplayName("测试AreaManageConstants常量值")
    void testConstants() {
        assertEquals("business_group", AreaManageConstants.BUSINESS_GROUP_PRESET);
        assertEquals("service_center__c", AreaManageConstants.SALES_DEPARTMENT__C);
        assertEquals("belong_area", AreaManageConstants.BELONG_AREA_ACCOUNT);
        assertEquals("sales_department", AreaManageConstants.SALES_DEPARTMENT);
        assertEquals("business_group", AreaManageConstants.BUSINESS_GROUP_FIX);
        assertEquals("service_center", AreaManageConstants.SERVICE_CENTER);
    }
}
