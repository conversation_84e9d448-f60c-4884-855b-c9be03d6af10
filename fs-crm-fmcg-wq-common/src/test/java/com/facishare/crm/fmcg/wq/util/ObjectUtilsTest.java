package com.facishare.crm.fmcg.wq.util;

import com.facishare.paas.appframework.common.util.ObjectAction;
import org.junit.Test;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * ObjectUtils测试类
 */
public class ObjectUtilsTest {

    @Test
    public void testButtonList() {
        // 验证buttonList包含预期的按钮
        List<ObjectAction> expectedButtons = Arrays.asList(
                ObjectAction.AREA_BATCH_ADD,
                ObjectAction.AREA_BATCH_EDIT,
                ObjectAction.AREA_MERGE);

        assertEquals(expectedButtons.size(), ObjectUtils.buttonList.size());

        for (ObjectAction button : expectedButtons) {
            assertTrue("按钮列表应该包含 " + button.name(),
                    ObjectUtils.buttonList.contains(button));
        }
    }

    @Test
    public void testFixFields() {
        // 验证fixFields包含预期的字段
        List<String> expectedFields = Arrays.asList(
                "belong_area", "name", "location", "owner",
                "customer_label", "account_level", "last_visit_closed_time",
                "visit_frequency", "route_ids");

        assertEquals(expectedFields.size(), ObjectUtils.fixFields.size());

        for (String field : expectedFields) {
            assertTrue("固定字段列表应该包含 " + field,
                    ObjectUtils.fixFields.contains(field));
        }
    }

    @Test
    public void testCustomerLevelConstant() {
        assertEquals("customer_label", ObjectUtils.CUSTOMER_LEVEL);
    }

    @Test
    public void testParseEmployeeValue() {
        // 测试parseEmployeeValue方法
        com.facishare.paas.metadata.api.IObjectData mockObjectData = mock(
                com.facishare.paas.metadata.api.IObjectData.class);

        // 测试String类型的owner
        when(mockObjectData.get("owner")).thenReturn("123");
        Optional<String> result1 = ObjectUtils.parseEmployeeValue(mockObjectData, "owner");
        assertTrue(result1.isPresent());
        assertEquals("123", result1.get());

        // 测试List类型的owner
        when(mockObjectData.get("owner")).thenReturn(Arrays.asList("456"));
        Optional<String> result2 = ObjectUtils.parseEmployeeValue(mockObjectData, "owner");
        assertTrue(result2.isPresent());
        assertEquals("456", result2.get());

        // 测试Integer类型的owner
        when(mockObjectData.get("owner")).thenReturn(789);
        Optional<String> result3 = ObjectUtils.parseEmployeeValue(mockObjectData, "owner");
        assertTrue(result3.isPresent());
        assertEquals("789", result3.get());

        // 测试null值
        when(mockObjectData.get("owner")).thenReturn(null);
        Optional<String> result4 = ObjectUtils.parseEmployeeValue(mockObjectData, "owner");
        assertFalse(result4.isPresent());
    }
}
