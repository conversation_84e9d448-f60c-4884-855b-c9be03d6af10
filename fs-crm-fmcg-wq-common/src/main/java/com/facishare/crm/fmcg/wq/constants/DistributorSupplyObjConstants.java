package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 下级配送商
 * @author: zhangsm
 * @create: 2021-04-25 21:52
 **/
public interface DistributorSupplyObjConstants {
    String API_NAME = "DistributorSupplyObj";
    String DISPLAY_NAME = "下级配送商"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field {
        thisDealerId("store","客户名称"), //ignoreI18n
        upDealerId("up_dealer","上级经销商"), //ignoreI18n
        specialSupply("special_supply","特例供货"), //ignoreI18n
        upSupplyId("up_level_dealer","上级供货关系"); //ignoreI18n

        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
}
