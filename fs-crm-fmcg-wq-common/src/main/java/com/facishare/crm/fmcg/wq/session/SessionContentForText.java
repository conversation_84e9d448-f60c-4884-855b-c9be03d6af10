package com.facishare.crm.fmcg.wq.session;

import com.facishare.appserver.checkins.model.db.Checkins;
import com.facishare.appserver.checkins.model.pb.data.GetPlanInfoArgs;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/12/27/ 10:46
 **/
public class SessionContentForText extends SessionContent{
    public SessionContentForText(String pushTitle,String title,String first){
        this.title = title;
        this.subtitle = getSubtitle();
        this.first = first;
        this.link = getLink();
        this.pushTitle = pushTitle;
        this.content = getSessionContent();
    }


    protected String getSubtitle(){ return ""; }

    protected String getLink() {
        return "";
    }

    private List<Map<String, StringBuilder>> getSessionContent() {
        return Lists.newArrayList();
    }

}
