package com.facishare.crm.fmcg.wq.constants;

public abstract class TPMActivityProofFields {

	private TPMActivityProofFields(){};

	public static final String DISPLAY_NAME = "活动举证"; //ignoreI18n

	public static final String API_NAME = "TPMActivityProofObj";

	 //门店
	public static final String STORE_ID = "store_id";

	 //AI识别状态
	public static final String AI_IDENTIFY_STATUS = "ai_identify_status";

		 //识别中
		public static final String AI_IDENTIFY_STATUS_Options_identifying = "identifying";

		 //已识别
		public static final String AI_IDENTIFY_STATUS_Options_identified = "identified";

		 //识别失败
		public static final String AI_IDENTIFY_STATUS_Options_identify_failed = "identify_failed";

	 //举证照片
	public static final String PROOF_IMAGES = "proof_images";

	 //系统判定结果
	public static final String SYSTEM_JUDGMENT_STATUS = "system_judgment_status";

		 //未陈列
		public static final String SYSTEM_JUDGMENT_STATUS_Options_not_display = "not_display";

		 //达标
		public static final String SYSTEM_JUDGMENT_STATUS_Options_pass = "pass";

		 //不达标
		public static final String SYSTEM_JUDGMENT_STATUS_Options_fail = "fail";

		 //部分达标
		public static final String SYSTEM_JUDGMENT_STATUS_Options_partial_pass = "partial_pass";

	 //举证申报费用(元)
	public static final String ACTUAL_TOTAL = "actual_total";

	 //启用AI
	public static final String OPEN_AI = "open_ai";

		 //是
		public static final String OPEN_AI_Options_true = "true";

		 //否
		public static final String OPEN_AI_Options_false = "false";

	 //备注
	public static final String REMARK = "remark";

	 //检核状态
	public static final String AUDIT_STATUS = "audit_status";

		 //未检核
		public static final String AUDIT_STATUS_Options_schedule = "schedule";

		 //合格
		public static final String AUDIT_STATUS_Options_pass = "pass";

		 //不合格
		public static final String AUDIT_STATUS_Options_reject = "reject";

		 //其他
		public static final String AUDIT_STATUS_Options_other = "other";

	 //活动费用核销
	public static final String DEALER_ACTIVITY_COST_ID = "dealer_activity_cost_id";

	 //费用折算比例
	public static final String COST_CONVERSION_RATIO = "cost_conversion_ratio";

	 //单店申报费用(元)
	public static final String TOTAL = "total";

	 //外勤动作编号
	public static final String ACTION_ID = "action_id";

	 //活动方案
	public static final String ACTIVITY_ID = "activity_id";

	 //活动协议
	public static final String ACTIVITY_AGREEMENT_ID = "activity_agreement_id";

	 //活动举证阶段明细
	public static final String PROOF_TIME_PERIOD_DETAIL_ID = "proof_time_period_detail_id";

	 //外勤编号
	public static final String VISIT_ID = "visit_id";

	 //抽检状态
	public static final String RANDOM_AUDIT_STATUS = "random_audit_status";

		 //已抽检
		public static final String RANDOM_AUDIT_STATUS_Options_checked = "checked";

		 //未抽检
		public static final String RANDOM_AUDIT_STATUS_Options_unchecked = "unchecked";

		 //其他
		public static final String RANDOM_AUDIT_STATUS_Options_other = "other";

	 //经销商
	public static final String DEALER_ID = "dealer_id";

}
