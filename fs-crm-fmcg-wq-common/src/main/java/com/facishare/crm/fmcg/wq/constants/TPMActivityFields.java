package com.facishare.crm.fmcg.wq.constants;

public abstract class TPMActivityFields {

	private TPMActivityFields(){};

	public static final String DISPLAY_NAME = "活动申请"; //ignoreI18n

	public static final String API_NAME = "TPMActivityObj";

	 //活动方案金额(元)
	public static final String ACTIVITY_AMOUNT = "activity_amount";

	 //结束日期
	public static final String END_DATE = "end_date";

	 //参与活动的客户类型
	public static final String CUSTOMER_TYPE = "customer_type";

		 //门店
		public static final String CUSTOMER_TYPE_Options_store = "store";

		 //经销商
		public static final String CUSTOMER_TYPE_Options_dealer = "dealer";

		 //经销商和门店
		public static final String CUSTOMER_TYPE_Options_dealerAndStore = "dealerAndStore";

		 //品牌商
		public static final String CUSTOMER_TYPE_Options_brand = "brand";

		 //其他
		public static final String CUSTOMER_TYPE_Options_other = "other";

	 //举证阶段范围
	public static final String PROOF_PERIOD = "proof_period";

	 //活动编号
	public static final String CODE = "code";

	 //门店范围
	public static final String STORE_RANGE = "store_range";

	 //参与部门(多选)
	public static final String MULTI_DEPARTMENT_RANGE = "multi_department_range";

	 //开始日期
	public static final String BEGIN_DATE = "begin_date";

	 //活动已入账费用(元)
	public static final String RECORDED_AMOUNT = "recorded_amount";

	 //活动实际金额(元) 
	public static final String ACTIVITY_ACTUAL_AMOUNT = "activity_actual_amount";

	 //活动说明
	public static final String DESCRIPTION = "description";

	 //剩余核销费用(元)
	public static final String REMAINING_WRITE_OFF_AMOUNT = "remaining_write_off_amount";

	 //结案状态
	public static final String CLOSED_STATUS = "closed_status";

		 //已结案
		public static final String CLOSED_STATUS_Options_closed = "closed";

		 //未结案
		public static final String CLOSED_STATUS_Options_unclosed = "unclosed";

	 //活动附件
	public static final String ATTACHMENT = "attachment";

	 //活动类型
	public static final String ACTIVITY_TYPE = "activity_type";

		 //活动类型
		public static final String ACTIVITY_TYPE_Options_64425bb99dedae0001aec3e8 = "64425bb99dedae0001aec3e8";

		 //陈列类活动
		public static final String ACTIVITY_TYPE_Options_643f93119dedae00019a6d0a = "643f93119dedae00019a6d0a";

		 //直播类活动
		public static final String ACTIVITY_TYPE_Options_643f93119dedae00019a6d0b = "643f93119dedae00019a6d0b";

		 //AI识别
		public static final String ACTIVITY_TYPE_Options_67dce38089e24a0001297a56 = "67dce38089e24a0001297a56";

	 //活动经销商
	public static final String DEALER_ID = "dealer_id";

	 //客户地址
	public static final String ACCOUNT_ADDRESS_ID = "account_address_id";

	 //收货人
	public static final String CONSIGNEE = "consignee";

	 //联系地址
	public static final String ADDRESS = "address";

	 //结案时间
	public static final String CLOSE_TIME = "close_time";

	 //产品范围
	public static final String PRODUCT_RANGE = "product_range";

	 //方案名称
	public static final String ACTIVITY_UNIFIED_CASE_ID = "activity_unified_case_id";

	 //核销次数
	public static final String MAX_WRITE_OFF_COUNT = "max_write_off_count";

		 //一次核销
		public static final String MAX_WRITE_OFF_COUNT_Options_once = "once";

		 //多次核销
		public static final String MAX_WRITE_OFF_COUNT_Options_multi = "multi";

		 //其他
		public static final String MAX_WRITE_OFF_COUNT_Options_other = "other";

	 //最晚可发起核销时间
	public static final String LAST_WRITE_OFF_DATE = "last_write_off_date";

	 //申请日期
	public static final String APPLICATION_DATE = "application_date";

	 //联系电话
	public static final String PHONE_NUMBER = "phone_number";

	 //备注
	public static final String REMARKS = "remarks";

	 //活动状态
	public static final String ACTIVITY_STATUS = "activity_status";

		 //审批中
		public static final String ACTIVITY_STATUS_Options_approval = "approval";

		 //未生效
		public static final String ACTIVITY_STATUS_Options_ineffective = "ineffective";

		 //未开始
		public static final String ACTIVITY_STATUS_Options_schedule = "schedule";

		 //进行中
		public static final String ACTIVITY_STATUS_Options_in_progress = "in_progress";

		 //已结束
		public static final String ACTIVITY_STATUS_Options_end = "end";

		 //已关闭
		public static final String ACTIVITY_STATUS_Options_closed = "closed";

		 //其他
		public static final String ACTIVITY_STATUS_Options_other = "other";

	 //是否启用自动结案
	public static final String IS_AUTO_CLOSE = "is_auto_close";

		 //是
		public static final String IS_AUTO_CLOSE_Options_true = "true";

		 //否
		public static final String IS_AUTO_CLOSE_Options_false = "false";

}
