package com.facishare.crm.fmcg.wq.constants;

public abstract class ShelfReportFields {

	private ShelfReportFields(){};

	public static final String DISPLAY_NAME = "陈列上报"; //ignoreI18n

	public static final String API_NAME = "ShelfReportObj";

	 //查找关联
	public static final String FIELD_2L5UZ = "field_2l5uZ";

	 //图片
	public static final String PATH = "path";

	 //AI数据
	public static final String AI_DATA = "AI_data";

	 //最后一次
	public static final String FIELD_LOOKUP_OBJECT_T4YVV = "field_lookup_object_t4YVV";

	 //ai图片
	public static final String AI_PATHS = "ai_paths";

	 //AI图片
	public static final String AI_PATH = "AI_path";

	 //ai数据
	public static final String AI_DATAS = "ai_datas";

	 //客户
	public static final String CUSTOMER_ID = "customer_id";

}
