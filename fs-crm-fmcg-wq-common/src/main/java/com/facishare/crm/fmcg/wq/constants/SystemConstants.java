package com.facishare.crm.fmcg.wq.constants;

import lombok.Getter;

public interface SystemConstants {
    String AccountApiName = "AccountObj";
    String PaymentApiName = "PaymentObj";
    String RefundApiName = "RefundObj";
    String OrderPaymentApiname = "OrderPaymentObj";
    String SalesOrderApiName = "SalesOrderObj";

    String ObjectDescribeApiName = "object_describe_api_name";
    String ObjectDescribeId = "object_describe_id";

    enum RelevantTeam {
        TeamMemberRole("teamMemberRole", "成员角色"), //ignoreI18n

        TeamMemberPermissionType("teamMemberPermissionType", "成员权限类型"), //ignoreI18n

        TeamMemberEmployee("teamMemberEmployee", "成员员工"), //ignoreI18n

        ;

        public final String apiName;
        public final String label;

        RelevantTeam(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }
    }

    enum TeamMemberRole {
        Owner("1", "负责人"), //ignoreI18n

        Normal("2", "普通成员"),; //ignoreI18n

        public String value;
        public String label;

        TeamMemberRole(String value, String label) {
            this.value = value;
            this.value = label;
        }
    }

    enum Field {
        Id("_id", "主键id"), //查询的时候主键用这个<br> //ignoreI18n

        LifeStatusBeforeInvalid("life_status_before_invalid", "作废前生命状态"), //ignoreI18n

        LifeStatus("life_status", "生命状态"), //ignoreI18n
        isDeleted("is_deleted", "删除状态"), //ignoreI18n

        LockStatus("lock_status", "锁定状态"), //ignoreI18n

        LockRule("lock_rule", "锁定规则"), //ignoreI18n

        LockUser("lock_user", "加锁人"), //ignoreI18n

        RelevantTeam("relevant_team", "相关团队"), //ignoreI18n

        Owner("owner", "负责人"), //ignoreI18n

        OwnerDepartment("owner_department", "负责人所在部门"), //ignoreI18n

        RecordType("record_type", "业务类型"), //ignoreI18n

        TennantID("tenant_id", "企业"), //ignoreI18n

        ExtendObjDataId("extend_obj_data_id", "extend_obj_data_id"),

        CreateBy("created_by", "创建人"), //ignoreI18n

        LastModifiedBy("last_modified_by", "最后修改人"), //ignoreI18n

        CreateTime("create_time", "创建时间"), //ignoreI18n

        LastModifiedTime("last_modified_time", "最后修改时间"), //ignoreI18n

        HelpText("help_text", "帮助信息") //ignoreI18n

        ;
        @Getter
        public final String apiName;
        @Getter
        public final String label;

        Field(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }
    }

    enum LayoutType {
        Detail("detail"),

        List("list"),

        ;

        public final String layoutType;

        LayoutType(String layoutType) {
            this.layoutType = layoutType;
        }
    }

    enum RenderType {

        AutoNumber("auto_number"),

        MasterDetail("master_detail"),

        Currency("currency"),

        SelectOne("select_one"),

        SelectMany("select_many"),

        Number("number"),

        Employee("employee"),

        RecordType("record_type"),

        ObjectReference("object_reference"),

        DateTime("date_time"),

        Date("date"),

        Text("text"),

        LongText("long_text"),

        FileAttachment("file_attachment"),

        ;

        public final String renderType;

        RenderType(String renderType) {
            this.renderType = renderType;
        }

    }

    enum ActionCode {
        Abolish("Abolish"),

        Edit("Edit"),

        ChangeOwner("ChangeOwner", "java_spring", "ChangeOwnerCustomAction", "更换负责人"), //ignoreI18n

        AddTeamMember("AddTeamMember", "java_spring", "AddTeamMemberCustomAction", "添加团队成员"), //ignoreI18n

        EditTeamMember("EditTeamMember", "java_spring", "EditTeamMemberCustomAction", "编辑团队成员"), //ignoreI18n

        DeleteTeamMember("DeleteTeamMember", "java_spring", "DeleteTeamMemberCustomAction", "删除团队成员"), //ignoreI18n

        Lock("Lock", "java_spring", "LockCustomAction", "锁定"), //ignoreI18n

        Unlock("Unlock", "java_spring", "UnLockCustomAction", "解锁"), //ignoreI18n

        ;

        final String actionCode;
        String sourceType;
        String actionClass;
        String label;

        ActionCode(String actionCode) {
            this.actionCode = actionCode;
        }

        ActionCode(String actionCode, String sourceType, String actionClass, String label) {
            this.actionCode = actionCode;
            this.sourceType = sourceType;
            this.actionClass = actionClass;
            this.label = label;
        }
    }
    enum LifeStatus {
        Ineffective("ineffective", "未生效"), //ignoreI18n

        UnderReview("under_review", "审核中"), //ignoreI18n

        Normal("normal", "正常"), //ignoreI18n

        InChange("in_change", "变更中"), //ignoreI18n

        Invalid("invalid", "作废"); //ignoreI18n
        @Getter
        public final String value;
        @Getter
        public final String label;

        LifeStatus(String value, String label) {
            this.value = value;
            this.label = label;
        }
    }

    enum LockStatus {
        Locked("1", "锁定"), //ignoreI18n

        UnLock("0", "未锁定"), //ignoreI18n

        ;

        public final String value;
        public final String label;

        LockStatus(String value, String label) {
            this.label = label;
            this.value = value;
        }
    }

    enum ImportType {
        Insert(0, "新建"), Edit(1, "编辑"); //ignoreI18n
        public final int value;
        public final String lable;

        ImportType(int value, String lable) {
            this.lable = lable;
            this.value = value;
        }
    }
}
