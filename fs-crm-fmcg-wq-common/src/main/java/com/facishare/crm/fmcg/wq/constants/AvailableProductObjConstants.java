package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 可售产品
 * @author: zhangsm
 * @create: 2021-04-25 21:52
 **/
public interface AvailableProductObjConstants {
    String API_NAME = "AvailableProductObj";
    String DISPLAY_NAME = "可售产品"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field{
        //id
        availableRangeId("available_range_id","可售范围id"), //ignoreI18n
        productId("product_id","产品名称"), //ignoreI18n
        //单选
        applyRange("apply_range","适用范围"); //ignoreI18n

        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
    @AllArgsConstructor
    @Getter
    enum Value{
        ALL("全部"), //ignoreI18n
        FIXED("指定产品"), //ignoreI18n
        PRICE_BOOK("跟随适用价目表范围"), //ignoreI18n
        CONDITION("符合指定条件"); //ignoreI18n

        /**
         *
         */
        private final String disName;
       public static Value of(String value){
            for (Value fValue : values()) {
                if (fValue.name().equals(value)){
                    return fValue;
                }
            }
            return null;
        }
    }
}
