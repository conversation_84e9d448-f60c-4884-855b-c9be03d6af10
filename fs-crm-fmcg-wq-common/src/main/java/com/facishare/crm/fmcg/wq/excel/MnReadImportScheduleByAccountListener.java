package com.facishare.crm.fmcg.wq.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellData;
import com.facishare.crm.fmcg.wq.constants.OfficeShiftConstants;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/07/24/ 18:28
 **/
@Getter
public class MnReadImportScheduleByAccountListener extends ReadImportScheduleByAccountListener {
    private List<Map<Integer, String>> dataMapList = Lists.newArrayList();
    private List<List<String>> dataList = Lists.newArrayList();
    private List<String> headList = Lists.newArrayList();

    @Override
    public void invokeHead(Map<Integer, CellData> headMap, AnalysisContext context){

        headList.addAll(headMap.values().stream().map(CellData::getStringValue).collect(Collectors.toList()));
    }
    @Override
    public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
        dataMapList.add(rowData);
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 数据解析完成后的操作
        convertHeadList();
        int lineSize = headList.size();
        for (int j = 1; j < dataMapList.size(); j++) {
            List<String> subDataList = Lists.newArrayList();
            for (int i = 0; i < lineSize; i++) {
                subDataList.add(dataMapList.get(j).getOrDefault(i,""));
            }
            dataList.add(subDataList);
        }
        dataMapList = null;
    }

    private void convertHeadList(){
        if(headList.isEmpty() || headList.size() <= 2){
            throw new ValidateException("表头不合法,请参考导入模版"); //ignoreI18n
        }
        if(!OfficeShiftConstants.HEAD_ACCOUNT_NAME.equals(headList.get(0))){
            throw new ValidateException("表头不合法,请参考导入模版"); //ignoreI18n
        }
        if(!OfficeShiftConstants.HEAD_ACCOUNT_NO.equals(headList.get(1))){
            throw new ValidateException("表头不合法,请参考导入模版"); //ignoreI18n
        }
        if(!OfficeShiftConstants.HEAD_CLASSES.equals(headList.get(2))){
            throw new ValidateException("表头不合法,请参考导入模版"); //ignoreI18n
        }
        String datePattern = "(\\d{1,4})年(\\d{1,2})月(\\d{1,2})日"; //ignoreI18n
        Pattern pattern = Pattern.compile(datePattern);
        String nowDateStr = LocalDate.now().toString();
        for (int i = 3; i < headList.size(); i++) {
            // 判断日期
            Matcher matcher = pattern.matcher(headList.get(i));
            if (matcher.find()) {
                try {
                    int year = Integer.parseInt(matcher.group(1));
                    int month = Integer.parseInt(matcher.group(2));
                    int day = Integer.parseInt(matcher.group(3));
                    String dateStr = LocalDate.of(year, month, day).toString();
                    if(nowDateStr.compareTo(dateStr) > 0){
                        throw new ValidateException("只能导入未来数据"); //ignoreI18n
                    }
                    headList.set(i,dateStr);
                }catch (Exception e){
                    throw new ValidateException("表头日期不合法,请参考导入模版"); //ignoreI18n
                }
            } else {
                throw new ValidateException("表头日期不合法,请参考导入模版"); //ignoreI18n
            }
        }
    }

}