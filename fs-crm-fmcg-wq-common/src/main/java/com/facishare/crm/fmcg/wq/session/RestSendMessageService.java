package com.facishare.crm.fmcg.wq.session;

import com.alibaba.fastjson.JSON;
import com.facishare.restful.client.exception.FRestClientException;
import com.fxiaoke.api.MessageServiceV2;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.model.MessageResponse;
import com.fxiaoke.model.message.SendTextCardMessageArg;
import com.fxiaoke.enterpriserelation2.service.UnionMessageService;
import com.fxiaoke.enterpriserelation2.arg.UnionMessageSendArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.github.autoconf.ConfigFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.google.gson.Gson;
import okhttp3.MediaType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.*;

@Slf4j
@Service
public class RestSendMessageService {

    public static final String PMM_APPID = "FMCGPMM";
    @Resource(name = "httpSupport")
    private OkHttpSupport client;
    @Autowired
    public MessageServiceV2 messageServiceV2;

    @Autowired
    public UnionMessageService unionMessageService;
   //url
    private static  String SEND_INTERCONNECT_NOTICE_URL = "http://172.17.4.230:30926/open/material/wx/link/outapi/notice/sendNotice";

    static {
        ConfigFactory.getInstance().getConfig("CheckInService", config -> {
            SEND_INTERCONNECT_NOTICE_URL = config.get("sendInterconnectNoticeUrl", SEND_INTERCONNECT_NOTICE_URL);

        });
    }

    private static final MediaType mediaTypeJson = MediaType.parse("application/json; charset=UTF-8");

    /**
     * 发送互联通知公告
     * {"isFixedTime":2,"noticeCategoryId":null,"userVisibleRangeType":1,"targets":{"departments":[],"employees":[]},"downstreamAccepterVO":{"outerTenantIds":[],"outerTgroupIds":[],"outerUids":["E.84788.300404414"],"outerRoleIds":[],"stopOuterUids":[],"outerDepartmentIds":[],"outerTreeDepartmentIds":[],"publicTenantIds":[],"publicTgroupIds":[],"parentDeptListIds":[]},"imageTextParams":[{"title":"爱上对方水电费","content":"https://www.ceshi112.com","summary":"","coverImageUrl":"","type":"URL","isCoverImageInText":false,"fileAttachments":[]}]}
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @param outerUids 外部用户ID列表，格式如：E.84788.300404414
     * @return 是否发送成功
     */
    public boolean sendInterconnectNotice(String tenantId, String userId, String title, String content, List<String> outerUids) {
        if (StringUtils.isBlank(tenantId) || StringUtils.isBlank(userId) || StringUtils.isBlank(title) 
                || StringUtils.isBlank(content) || CollectionUtils.isEmpty(outerUids)) {
            log.warn("发送互联通知公告参数无效，tenantId: {}, userId: {}, title: {}, outerUids: {}", 
                    tenantId, userId, title, outerUids);
            return false;
        }
        
        try {
            // 构建请求URL

            // 构建请求体
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("isFixedTime", 2);
            requestBody.put("noticeCategoryId", null);
            requestBody.put("userVisibleRangeType", 1);

            Map<String, Object> targets = new HashMap<>();
            targets.put("departments", Collections.emptyList());
            targets.put("employees", Collections.emptyList());
            requestBody.put("targets", targets);

            Map<String, Object> downstreamAccepterVO = new HashMap<>();
            downstreamAccepterVO.put("outerTenantIds", Collections.emptyList());
            downstreamAccepterVO.put("outerTgroupIds", Collections.emptyList());
            downstreamAccepterVO.put("outerUids", outerUids);
            downstreamAccepterVO.put("outerRoleIds", Collections.emptyList());
            downstreamAccepterVO.put("stopOuterUids", Collections.emptyList());
            downstreamAccepterVO.put("outerDepartmentIds", Collections.emptyList());
            downstreamAccepterVO.put("outerTreeDepartmentIds", Collections.emptyList());
            downstreamAccepterVO.put("publicTenantIds", Collections.emptyList());
            downstreamAccepterVO.put("publicTgroupIds", Collections.emptyList());
            downstreamAccepterVO.put("parentDeptListIds", Collections.emptyList());
            requestBody.put("downstreamAccepterVO", downstreamAccepterVO);

            List<Map<String, Object>> imageTextParams = new ArrayList<>();
            Map<String, Object> imageTextParam = new HashMap<>();
            imageTextParam.put("title", title);
            imageTextParam.put("content", content);
            imageTextParam.put("summary", "");
            imageTextParam.put("coverImageUrl", "");
            imageTextParam.put("type", "URL");
            imageTextParam.put("isCoverImageInText", false);
            imageTextParam.put("fileAttachments", Collections.emptyList());
            imageTextParams.add(imageTextParam);
            requestBody.put("imageTextParams", imageTextParams);
            
            // 构建请求
            okhttp3.Request request = new okhttp3.Request.Builder()
                .url(SEND_INTERCONNECT_NOTICE_URL)
                .header("accept", "application/json, text/javascript, */*; q=0.01")
                .header("accept-language", "zh-CN,zh-TW;0.9,en;0.8")
                .header("Content-Type", "application/json; charset=UTF-8")
                .header("x-tenant-id", tenantId)
                .header("x-user-id", userId)
                .post(okhttp3.RequestBody.create(mediaTypeJson, new Gson().toJson(requestBody)))
                .build();
            
            // 发送请求
            String responseString = (String) client.syncExecute(request, new SyncCallback() {
                @Override
                public String response(okhttp3.Response response) throws IOException {
                    return Objects.requireNonNull(response.body()).string();
                }
            });
            
            // 解析响应
            Map<String, Object> responseMap = JSON.parseObject(responseString, Map.class);
            boolean success = responseMap != null && "0".equals(String.valueOf(responseMap.get("errCode")));
            
            if (success) {
                log.info("发送互联通知公告成功，tenantId: {}, title: {}, outerUids: {}", tenantId, title, outerUids);
            } else {
                log.error("发送互联通知公告失败，tenantId: {}, title: {}, outerUids: {}, response: {}", 
                        tenantId, title, outerUids, responseString);
            }
            
            return success;
        } catch (Exception e) {
            log.error("发送互联通知公告异常，tenantId: {}, title: {}, outerUids: {}", tenantId, title, outerUids, e);
            return false;
        }
    }

    /**
     * 带重试机制的发送互联通知公告
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @param outerUids 外部用户ID列表，格式如：E.84788.300404414
     * @return 是否发送成功
     */
    public boolean sendInterconnectNoticeWithRetry(String tenantId, String userId, String title, 
                                                 String content, List<String> outerUids) {
        int retryTime = 3; // 默认重试3次
        for (int i = 0; i < retryTime; i++) {
            try {
                boolean result = sendInterconnectNotice(tenantId, userId, title, content, outerUids);
                if (result) {
                    log.info("sendInterconnectNoticeWithRetry success，tenantId: {}, outerUids: {}", 
                            tenantId, outerUids);
                    return true;
                } else {
                    log.warn("sendInterconnectNoticeWithRetry failed，tenantId: {}, outerUids: {}", 
                            tenantId, outerUids);
                    if (i == retryTime - 1) {
                        return false;
                    }
                }
            } catch (Exception e) {
                if (i == retryTime - 1) {
                    log.error("sendInterconnectNoticeWithRetry error，tenantId: {}, outerUids: {}", 
                            tenantId, outerUids, e);
                    return false;
                } else {
                    log.warn("sendInterconnectNoticeWithRetry error，第{}次重试，tenantId: {}, outerUids: {}", 
                            i + 1, tenantId, outerUids, e);
                }
            }
        }
        return false;
    }

    public void sendTextCardMessage(SendTextCardMessageArg arg) {
        try {
            MessageResponse result = messageServiceV2.sendTextCardMessage(arg);
            if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                log.info("message send success");
            } else {
                log.error("message send failed");
            }
        } catch (FRestClientException ex) {
            log.error("FRestClientException -", ex);
        }
    }

    /**
     * 带重试机制的发送文本卡片消息
     *
     * @param arg 消息参数
     * @param employeeId 员工ID
     * @param salaryDataId 工资条ID
     * @return 是否发送成功
     */
    public boolean sendTextCardMessageWithRetry(SendTextCardMessageArg arg, String employeeId) {
        int retryTime = 3; // 默认重试3次
        for (int i = 0; i < retryTime; i++) {
            try {
                MessageResponse result = messageServiceV2.sendTextCardMessage(arg);
                if (result.getCode() == MessageResponse.SUCCESS_CODE) {
                    log.info("sendTextCardMessageWithRetry success，employeeId: {}", employeeId);
                    return true;
                } else {
                    log.warn("sendTextCardMessageWithRetry failed，employeeId: {}", employeeId);
                    if (i == retryTime - 1) {
                        return false;
                    }
                }
            } catch (Exception e) {
                if (i == retryTime - 1) {
                    log.error("sendTextCardMessageWithRetry error，employeeId: {}", employeeId, e);
                    return false;
                } else {
                    log.warn("sendTextCardMessageWithRetry error，第{}次重试，employeeId: {}", i + 1, employeeId, e);
                }
            }
        }
        return false;
    }

    /**
     * 带重试机制的发送Union消息
     *
     * @param headerObj 头部对象
     * @param unionMessageSendArg Union消息参数
     * @param employeeExternalId 外部员工ID
     * @param salaryDataId 工资条ID
     * @return 是否发送成功
     */
    public boolean sendUnionMessageWithRetry(HeaderObj headerObj, UnionMessageSendArg unionMessageSendArg,
                                           String employeeExternalId) {
        int retryTime = 3; // 默认重试3次
        for (int i = 0; i < retryTime; i++) {
            try {
                RestResult<Void> result = unionMessageService.sendMessage(headerObj, unionMessageSendArg);
                if (result != null && result.isSuccess()) {
                    log.info("sendUnionMessageWithRetry success，employeeExternalId: {}",
                        employeeExternalId);
                    return true;
                }
            } catch (Exception e) {
                if (i == retryTime - 1) {
                    log.error("sendUnionMessageWithRetry error，employeeExternalId: {}",
                        employeeExternalId, e);
                    return false;
                } else {
                    log.warn("sendUnionMessageWithRetry error，第{}次重试，employeeExternalId: {}",
                        i + 1, employeeExternalId, e);
                }
            }
        }
        return false;
    }

}
