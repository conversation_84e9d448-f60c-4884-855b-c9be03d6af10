package com.facishare.crm.fmcg.wq.constants;

public abstract class PublicEmployeeFields {

	private PublicEmployeeFields(){};

	public static final String DISPLAY_NAME = "互联用户"; //ignoreI18n

	public static final String API_NAME = "PublicEmployeeObj";

	 //是否管理者
	public static final String DATA_MANAGER = "data_manager";

	 //停用时间
	public static final String STOP_TIME = "stop_time";

	 //性别
	public static final String GENDER = "gender";

	 //登录邮箱
	public static final String LOGIN_EMAIL = "login_email";

	 //关联联系人
	public static final String CONTRACT_ID = "contract_id";

	 //下游语言
	public static final String ER_LANGUAGE = "er_language";

		 //简体中文
//		public static final String ER_LANGUAGE_Options_zh-CN = "zh-CN";
//
//		 //英文
//		public static final String ER_LANGUAGE_Options_en = "en";
//
//		 //繁体中文
//		public static final String ER_LANGUAGE_Options_zh-TW = "zh-TW";
//
//		 //日文
//		public static final String ER_LANGUAGE_Options_ja-JP = "ja-JP";

	 //互联状态
	public static final String TYPE = "type";

		 //正常
		public static final String TYPE_Options_1 = "1";

		 //停用
		public static final String TYPE_Options_0 = "0";

	 //头像
	public static final String PROFILE_IMAGE = "profile_image";

	 //关联互联企业
	public static final String OUTER_TENANT_ID = "outer_tenant_id";

	 //是否主负责人
	public static final String RELATION_OWNER = "relation_owner";

	 //部门
	public static final String DEPARTMENT = "department";

	 //职位
	public static final String JOB_TITLE = "job_title";

	 //邮箱
	public static final String EMAIL = "email";

	 //名称拼音
	public static final String NAME_SPELL = "name_spell";

	 //手机号
	public static final String MOBILE = "mobile";

	 //头像鉴权token
	public static final String PROFILE_IMAGE_TOKEN = "profile_image_token";

	 //下游时区
	public static final String ER_TIME_ZONE = "er_time_zone";

//		 //(GMT+14:00) 莱恩群岛时间 (Pacific/Kiritimati)
//		public static final String ER_TIME_ZONE_Options_Pacific/Kiritimati = "Pacific/Kiritimati";
//
//		 //(GMT+13:45) 查坦夏令时间 (Pacific/Chatham)
//		public static final String ER_TIME_ZONE_Options_Pacific/Chatham = "Pacific/Chatham";
//
//		 //(GMT+13:00) 新西兰夏令时间 (Pacific/Auckland)
//		public static final String ER_TIME_ZONE_Options_Pacific/Auckland = "Pacific/Auckland";
//
//		 //(GMT+13:00) 菲尼克斯群岛时间 (Pacific/Enderbury)
//		public static final String ER_TIME_ZONE_Options_Pacific/Enderbury = "Pacific/Enderbury";
//
//		 //(GMT+13:00) 汤加标准时间 (Pacific/Tongatapu)
//		public static final String ER_TIME_ZONE_Options_Pacific/Tongatapu = "Pacific/Tongatapu";
//
//		 //(GMT+12:00) 彼得罗巴甫洛夫斯克-堪察加标准时间 (Asia/Kamchatka)
//		public static final String ER_TIME_ZONE_Options_Asia/Kamchatka = "Asia/Kamchatka";
//
//		 //(GMT+12:00) 斐济标准时间 (Pacific/Fiji)
//		public static final String ER_TIME_ZONE_Options_Pacific/Fiji = "Pacific/Fiji";
//
//		 //(GMT+12:00) 诺福克岛时间 (Pacific/Norfolk)
//		public static final String ER_TIME_ZONE_Options_Pacific/Norfolk = "Pacific/Norfolk";
//
//		 //(GMT+11:00) 豪勋爵岛夏令时间 (Australia/Lord_Howe)
//		public static final String ER_TIME_ZONE_Options_Australia/Lord_Howe = "Australia/Lord_Howe";
//
//		 //(GMT+11:00) 澳大利亚东部夏令时间 (Australia/Sydney)
//		public static final String ER_TIME_ZONE_Options_Australia/Sydney = "Australia/Sydney";
//
//		 //(GMT+11:00) 所罗门群岛时间 (Pacific/Guadalcanal)
//		public static final String ER_TIME_ZONE_Options_Pacific/Guadalcanal = "Pacific/Guadalcanal";
//
//		 //(GMT+10:30) 澳大利亚中部夏令时间 (Australia/Adelaide)
//		public static final String ER_TIME_ZONE_Options_Australia/Adelaide = "Australia/Adelaide";
//
//		 //(GMT+10:00) 澳大利亚东部标准时间 (Australia/Brisbane)
//		public static final String ER_TIME_ZONE_Options_Australia/Brisbane = "Australia/Brisbane";
//
//		 //(GMT+09:30) 澳大利亚中部标准时间 (Australia/Darwin)
//		public static final String ER_TIME_ZONE_Options_Australia/Darwin = "Australia/Darwin";
//
//		 //(GMT+09:00) 韩国标准时间 (Asia/Seoul)
//		public static final String ER_TIME_ZONE_Options_Asia/Seoul = "Asia/Seoul";
//
//		 //(GMT+09:00) 日本标准时间 (Asia/Tokyo)
//		public static final String ER_TIME_ZONE_Options_Asia/Tokyo = "Asia/Tokyo";
//
//		 //(GMT+08:00) 香港标准时间 (Asia/Hong_Kong)
//		public static final String ER_TIME_ZONE_Options_Asia/Hong_Kong = "Asia/Hong_Kong";
//
//		 //(GMT+08:00) 马来西亚时间 (Asia/Kuala_Lumpur)
//		public static final String ER_TIME_ZONE_Options_Asia/Kuala_Lumpur = "Asia/Kuala_Lumpur";
//
//		 //(GMT+08:00) 菲律宾标准时间 (Asia/Manila)
//		public static final String ER_TIME_ZONE_Options_Asia/Manila = "Asia/Manila";
//
//		 //(GMT+08:00) 中国标准时间 (Asia/Shanghai)
//		public static final String ER_TIME_ZONE_Options_Asia/Shanghai = "Asia/Shanghai";
//
//		 //(GMT+08:00) 新加坡标准时间 (Asia/Singapore)
//		public static final String ER_TIME_ZONE_Options_Asia/Singapore = "Asia/Singapore";
//
//		 //(GMT+08:00) 台北标准时间 (Asia/Taipei)
//		public static final String ER_TIME_ZONE_Options_Asia/Taipei = "Asia/Taipei";
//
//		 //(GMT+08:00) 澳大利亚西部标准时间 (Australia/Perth)
//		public static final String ER_TIME_ZONE_Options_Australia/Perth = "Australia/Perth";
//
//		 //(GMT+07:00) 印度支那时间 (Asia/Bangkok)
//		public static final String ER_TIME_ZONE_Options_Asia/Bangkok = "Asia/Bangkok";
//
//		 //(GMT+07:00) 印度支那时间 (Asia/Ho_Chi_Minh)
//		public static final String ER_TIME_ZONE_Options_Asia/Ho_Chi_Minh = "Asia/Ho_Chi_Minh";
//
//		 //(GMT+07:00) 印度尼西亚西部时间 (Asia/Jakarta)
//		public static final String ER_TIME_ZONE_Options_Asia/Jakarta = "Asia/Jakarta";
//
//		 //(GMT+06:30) 缅甸时间 (Asia/Rangoon)
//		public static final String ER_TIME_ZONE_Options_Asia/Rangoon = "Asia/Rangoon";
//
//		 //(GMT+06:00) 孟加拉标准时间 (Asia/Dhaka)
//		public static final String ER_TIME_ZONE_Options_Asia/Dhaka = "Asia/Dhaka";
//
//		 //(GMT+05:45) 尼泊尔时间 (Asia/Kathmandu)
//		public static final String ER_TIME_ZONE_Options_Asia/Kathmandu = "Asia/Kathmandu";
//
//		 //(GMT+05:30) 印度时间 (Asia/Colombo)
//		public static final String ER_TIME_ZONE_Options_Asia/Colombo = "Asia/Colombo";
//
//		 //(GMT+05:30) 印度时间 (Asia/Kolkata)
//		public static final String ER_TIME_ZONE_Options_Asia/Kolkata = "Asia/Kolkata";
//
//		 //(GMT+05:00) 巴基斯坦标准时间 (Asia/Karachi)
//		public static final String ER_TIME_ZONE_Options_Asia/Karachi = "Asia/Karachi";
//
//		 //(GMT+05:00) 乌兹别克斯坦标准时间 (Asia/Tashkent)
//		public static final String ER_TIME_ZONE_Options_Asia/Tashkent = "Asia/Tashkent";
//
//		 //(GMT+05:00) 叶卡捷琳堡标准时间 (Asia/Yekaterinburg)
//		public static final String ER_TIME_ZONE_Options_Asia/Yekaterinburg = "Asia/Yekaterinburg";
//
//		 //(GMT+04:30) 阿富汗时间 (Asia/Kabul)
//		public static final String ER_TIME_ZONE_Options_Asia/Kabul = "Asia/Kabul";
//
//		 //(GMT+04:00) 阿塞拜疆标准时间 (Asia/Baku)
//		public static final String ER_TIME_ZONE_Options_Asia/Baku = "Asia/Baku";
//
//		 //(GMT+04:00) 海湾标准时间 (Asia/Dubai)
//		public static final String ER_TIME_ZONE_Options_Asia/Dubai = "Asia/Dubai";
//
//		 //(GMT+04:00) 格鲁吉亚标准时间 (Asia/Tbilisi)
//		public static final String ER_TIME_ZONE_Options_Asia/Tbilisi = "Asia/Tbilisi";
//
//		 //(GMT+04:00) 亚美尼亚标准时间 (Asia/Yerevan)
//		public static final String ER_TIME_ZONE_Options_Asia/Yerevan = "Asia/Yerevan";
//
//		 //(GMT+03:30) 伊朗标准时间 (Asia/Tehran)
//		public static final String ER_TIME_ZONE_Options_Asia/Tehran = "Asia/Tehran";
//
//		 //GMT+03:00) 东部非洲时间 (Africa/Nairobi)
//		public static final String ER_TIME_ZONE_Options_Africa/Nairobi = "Africa/Nairobi";
//
//		 //(GMT+03:00) 阿拉伯标准时间 (Asia/Baghdad)
//		public static final String ER_TIME_ZONE_Options_Asia/Baghdad = "Asia/Baghdad";
//
//		 //(GMT+03:00) 阿拉伯标准时间 (Asia/Kuwait)
//		public static final String ER_TIME_ZONE_Options_Asia/Kuwait = "Asia/Kuwait";
//
//		 //(GMT+03:00) 阿拉伯标准时间 (Asia/Riyadh)
//		public static final String ER_TIME_ZONE_Options_Asia/Riyadh = "Asia/Riyadh";
//
//		 //(GMT+03:00) 东欧标准时间 (Europe/Istanbul)
//		public static final String ER_TIME_ZONE_Options_Europe/Istanbul = "Europe/Istanbul";
//
//		 //(GMT+03:00) 莫斯科标准时间 (Europe/Minsk)
//		public static final String ER_TIME_ZONE_Options_Europe/Minsk = "Europe/Minsk";
//
//		 //(GMT+03:00) 莫斯科标准时间 (Europe/Moscow)
//		public static final String ER_TIME_ZONE_Options_Europe/Moscow = "Europe/Moscow";
//
//		 //GMT+02:00) 东欧标准时间 (Africa/Cairo)
//		public static final String ER_TIME_ZONE_Options_Africa/Cairo = "Africa/Cairo";
//
//		 //GMT+02:00) 南非标准时间 (Africa/Johannesburg)
//		public static final String ER_TIME_ZONE_Options_Africa/Johannesburg = "Africa/Johannesburg";
//
//		 //(GMT+02:00) 东欧标准时间 (Asia/Beirut)
//		public static final String ER_TIME_ZONE_Options_Asia/Beirut = "Asia/Beirut";
//
//		 //(GMT+02:00) 以色列标准时间 (Asia/Jerusalem)
//		public static final String ER_TIME_ZONE_Options_Asia/Jerusalem = "Asia/Jerusalem";
//
//		 //(GMT+02:00) 东欧标准时间 (Europe/Athens)
//		public static final String ER_TIME_ZONE_Options_Europe/Athens = "Europe/Athens";
//
//		 //(GMT+02:00) 东欧标准时间 (Europe/Bucharest)
//		public static final String ER_TIME_ZONE_Options_Europe/Bucharest = "Europe/Bucharest";
//
//		 //(GMT+02:00) 东欧标准时间 (Europe/Helsinki)
//		public static final String ER_TIME_ZONE_Options_Europe/Helsinki = "Europe/Helsinki";
//
//		 //(GMT+01:00) 中欧标准时间 (Africa/Algiers)
//		public static final String ER_TIME_ZONE_Options_Africa/Algiers = "Africa/Algiers";
//
//		 //(GMT+01:00) 西欧夏令时间 (Africa/Casablanca)
//		public static final String ER_TIME_ZONE_Options_Africa/Casablanca = "Africa/Casablanca";
//
//		 //(GMT+01:00) 中欧标准时间 (Europe/Amsterdam)
//		public static final String ER_TIME_ZONE_Options_Europe/Amsterdam = "Europe/Amsterdam";
//
//		 //(GMT+01:00) 中欧标准时间 (Europe/Berlin)
//		public static final String ER_TIME_ZONE_Options_Europe/Berlin = "Europe/Berlin";
//
//		 //(GMT+01:00) 中欧标准时间 (Europe/Brussels)
//		public static final String ER_TIME_ZONE_Options_Europe/Brussels = "Europe/Brussels";
//
//		 //(GMT+01:00) 中欧标准时间 (Europe/Paris)
//		public static final String ER_TIME_ZONE_Options_Europe/Paris = "Europe/Paris";
//
//		 //(GMT+01:00) 中欧标准时间 (Europe/Prague)
//		public static final String ER_TIME_ZONE_Options_Europe/Prague = "Europe/Prague";
//
//		 //(GMT+01:00) 中欧标准时间 (Europe/Rome)
//		public static final String ER_TIME_ZONE_Options_Europe/Rome = "Europe/Rome";
//
//		 //(GMT+00:00) 格林尼治标准时间 (Europe/Dublin)
//		public static final String ER_TIME_ZONE_Options_Europe/Dublin = "Europe/Dublin";
//
//		 //(GMT+00:00) 西欧标准时间 (Europe/Lisbon)
//		public static final String ER_TIME_ZONE_Options_Europe/Lisbon = "Europe/Lisbon";
//
//		 //(GMT+00:00) 格林尼治标准时间 (Europe/London)
//		public static final String ER_TIME_ZONE_Options_Europe/London = "Europe/London";
//
//		 //(GMT+00:00) 格林尼治标准时间 (GMT)
//		public static final String ER_TIME_ZONE_Options_GMT = "GMT";
//
//		 //(GMT-01:00) 格陵兰岛东部标准时间 (America/Scoresbysund)
//		public static final String ER_TIME_ZONE_Options_America/Scoresbysund = "America/Scoresbysund";
//
//		 //(GMT-01:00) 亚速尔群岛标准时间 (Atlantic/Azores)
//		public static final String ER_TIME_ZONE_Options_Atlantic/Azores = "Atlantic/Azores";
//
//		 //(GMT-01:00) 佛得角标准时间 (Atlantic/Cape_Verde)
//		public static final String ER_TIME_ZONE_Options_Atlantic/Cape_Verde = "Atlantic/Cape_Verde";
//
//		 //(GMT-02:00) 南乔治亚岛时间 (Atlantic/South_Georgia)
//		public static final String ER_TIME_ZONE_Options_Atlantic/South_Georgia = "Atlantic/South_Georgia";
//
//		 //GMT-03:00) 阿根廷标准时间 (America/Argentina/Buenos_Aires)
//		public static final String ER_TIME_ZONE_Options_America/Argentina/Buenos_Aires = "America/Argentina/Buenos_Aires";
//
//		 //(GMT-03:00) 智利夏令时间 (America/Santiago)
//		public static final String ER_TIME_ZONE_Options_America/Santiago = "America/Santiago";
//
//		 //(GMT-03:00) 巴西利亚标准时间 (America/Sao_Paulo)
//		public static final String ER_TIME_ZONE_Options_America/Sao_Paulo = "America/Sao_Paulo";
//
//		 //(GMT-03:30) 纽芬兰标准时间 (America/St_Johns)
//		public static final String ER_TIME_ZONE_Options_America/St_Johns = "America/St_Johns";
//
//		 //GMT-04:00) 委内瑞拉时间 (America/Caracas)
//		public static final String ER_TIME_ZONE_Options_America/Caracas = "America/Caracas";
//
//		 //(GMT-04:00) 大西洋标准时间 (America/Halifax)
//		public static final String ER_TIME_ZONE_Options_America/Halifax = "America/Halifax";
//
//		 //(GMT-04:00) 大西洋标准时间 (America/Puerto_Rico)
//		public static final String ER_TIME_ZONE_Options_America/Puerto_Rico = "America/Puerto_Rico";
//
//		 //(GMT-04:00) 大西洋标准时间 (Atlantic/Bermuda)
//		public static final String ER_TIME_ZONE_Options_Atlantic/Bermuda = "Atlantic/Bermuda";
//
//		 //GMT-05:00) 哥伦比亚标准时间 (America/Bogota)
//		public static final String ER_TIME_ZONE_Options_America/Bogota = "America/Bogota";
//
//		 //(GMT-05:00) 北美东部标准时间 (America/Indiana/Indianapolis)
//		public static final String ER_TIME_ZONE_Options_America/Indiana/Indianapolis = "America/Indiana/Indianapolis";
//
//		 //(GMT-05:00) 秘鲁标准时间 (America/Lima)
//		public static final String ER_TIME_ZONE_Options_America/Lima = "America/Lima";
//
//		 //(GMT-05:00) 北美东部标准时间 (America/New_York)
//		public static final String ER_TIME_ZONE_Options_America/New_York = "America/New_York";
//
//		 //(GMT-05:00) 北美东部标准时间 (America/Panama)
//		public static final String ER_TIME_ZONE_Options_America/Panama = "America/Panama";
//
//		 //GMT-06:00) 北美中部标准时间 (America/Chicago)
//		public static final String ER_TIME_ZONE_Options_America/Chicago = "America/Chicago";
//
//		 //(GMT-06:00) 北美中部标准时间 (America/El_Salvador)
//		public static final String ER_TIME_ZONE_Options_America/El_Salvador = "America/El_Salvador";
//
//		 //(GMT-06:00) 北美中部标准时间 (America/Mexico_City)
//		public static final String ER_TIME_ZONE_Options_America/Mexico_City = "America/Mexico_City";
//
//		 //GMT-07:00) 北美山区标准时间 (America/Denver)
//		public static final String ER_TIME_ZONE_Options_America/Denver = "America/Denver";
//
//		 //(GMT-07:00) 墨西哥太平洋标准时间 (America/Mazatlan)
//		public static final String ER_TIME_ZONE_Options_America/Mazatlan = "America/Mazatlan";
//
//		 //(GMT-07:00) 北美山区标准时间 (America/Phoenix)
//		public static final String ER_TIME_ZONE_Options_America/Phoenix = "America/Phoenix";
//
//		 //(GMT-08:00) 北美太平洋标准时间 (America/Los_Angeles)
//		public static final String ER_TIME_ZONE_Options_America/Los_Angeles = "America/Los_Angeles";
//
//		 //(GMT-08:00) 北美太平洋标准时间 (America/Tijuana)
//		public static final String ER_TIME_ZONE_Options_America/Tijuana = "America/Tijuana";
//
//		 //(GMT-08:00) 皮特凯恩时间 (Pacific/Pitcairn)
//		public static final String ER_TIME_ZONE_Options_Pacific/Pitcairn = "Pacific/Pitcairn";
//
//		 //GMT-09:00) 阿拉斯加标准时间 (America/Anchorage)
//		public static final String ER_TIME_ZONE_Options_America/Anchorage = "America/Anchorage";
//
//		 //(GMT-09:00) 甘比尔时间 (Pacific/Gambier)
//		public static final String ER_TIME_ZONE_Options_Pacific/Gambier = "Pacific/Gambier";
//
//		 //(GMT-09:30) 马克萨斯群岛时间 (Pacific/Marquesas)
//		public static final String ER_TIME_ZONE_Options_Pacific/Marquesas = "Pacific/Marquesas";
//
//		 //(GMT-10:00) 夏威夷-阿留申标准时间 (America/Adak)
//		public static final String ER_TIME_ZONE_Options_America/Adak = "America/Adak";
//
//		 //(GMT-10:00) 夏威夷-阿留申标准时间 (Pacific/Honolulu)
//		public static final String ER_TIME_ZONE_Options_Pacific/Honolulu = "Pacific/Honolulu";
//
//		 //(GMT-11:00) 纽埃时间 (Pacific/Niue)
//		public static final String ER_TIME_ZONE_Options_Pacific/Niue = "Pacific/Niue";
//
//		 //(GMT-11:00) 萨摩亚标准时间 (Pacific/Pago_Pago)
//		public static final String ER_TIME_ZONE_Options_Pacific/Pago_Pago = "Pacific/Pago_Pago";

	 //启用时间
	public static final String START_TIME = "start_time";

	 //注册来源
	public static final String REGISTER_TYPE = "register_type";

		 //管理员添加
		public static final String REGISTER_TYPE_Options_1 = "1";

		 //自注册
		public static final String REGISTER_TYPE_Options_2 = "2";

	 //配额过期导致下游企业失效
	public static final String QUOTA_EXPIRED = "quota_expired";

	 //下游币种
	public static final String ER_MC_CURRENCY = "er_mc_currency";

		 //欧元
		public static final String ER_MC_CURRENCY_Options_EUR = "EUR";

		 //IRR - 伊朗里亚尔
		public static final String ER_MC_CURRENCY_Options_IRR = "IRR";

		 //TWD - 台币
		public static final String ER_MC_CURRENCY_Options_TWD = "TWD";

		 //阿富汗的阿富汗尼（新）
		public static final String ER_MC_CURRENCY_Options_AFN = "AFN";

		 //日元
		public static final String ER_MC_CURRENCY_Options_JPY = "JPY";

		 //港币
		public static final String ER_MC_CURRENCY_Options_HKD = "HKD";

		 //美元
		public static final String ER_MC_CURRENCY_Options_USD = "USD";

		 //人民币
		public static final String ER_MC_CURRENCY_Options_CNY = "CNY";

		 //AED - 阿联酋迪拉姆
		public static final String ER_MC_CURRENCY_Options_AED = "AED";

		 //ALL - 阿尔巴尼亚列克
		public static final String ER_MC_CURRENCY_Options_ALL = "ALL";

		 //AMD - 亚美尼亚打兰
		public static final String ER_MC_CURRENCY_Options_AMD = "AMD";

		 //ANG - 荷属安地列斯盾
		public static final String ER_MC_CURRENCY_Options_ANG = "ANG";

		 //AOA - 安哥拉宽扎
		public static final String ER_MC_CURRENCY_Options_AOA = "AOA";

		 //ARS - 阿根廷比索
		public static final String ER_MC_CURRENCY_Options_ARS = "ARS";

		 //AUD - 澳元
		public static final String ER_MC_CURRENCY_Options_AUD = "AUD";

		 //AWG - 阿鲁巴岛弗罗林
		public static final String ER_MC_CURRENCY_Options_AWG = "AWG";

		 //AZN - 阿塞拜疆马纳特
		public static final String ER_MC_CURRENCY_Options_AZN = "AZN";

		 //BAM - 自由兑换马克
		public static final String ER_MC_CURRENCY_Options_BAM = "BAM";

		 //BBD - 巴巴多斯元
		public static final String ER_MC_CURRENCY_Options_BBD = "BBD";

		 //BDT - 孟加拉国塔卡
		public static final String ER_MC_CURRENCY_Options_BDT = "BDT";

		 //BGN - 保加利亚列弗
		public static final String ER_MC_CURRENCY_Options_BGN = "BGN";

		 //BHD - 巴林第纳尔
		public static final String ER_MC_CURRENCY_Options_BHD = "BHD";

		 //BIF - 布隆迪法郎
		public static final String ER_MC_CURRENCY_Options_BIF = "BIF";

		 //BMD - 百慕大元
		public static final String ER_MC_CURRENCY_Options_BMD = "BMD";

		 //BND - 文莱元
		public static final String ER_MC_CURRENCY_Options_BND = "BND";

		 //BOB - 玻利维亚的玻利维亚诺
		public static final String ER_MC_CURRENCY_Options_BOB = "BOB";

		 //BOV - 玻利维亚姆夫多尔
		public static final String ER_MC_CURRENCY_Options_BOV = "BOV";

		 //BRB - 巴西克鲁塞罗（旧）
		public static final String ER_MC_CURRENCY_Options_BRB = "BRB";

		 //BRL - 巴西币
		public static final String ER_MC_CURRENCY_Options_BRL = "BRL";

		 //BSD - 巴哈马元
		public static final String ER_MC_CURRENCY_Options_BSD = "BSD";

		 //BTN - 不丹卢比
		public static final String ER_MC_CURRENCY_Options_BTN = "BTN";

		 //BWP - 博茨瓦纳普拉
		public static final String ER_MC_CURRENCY_Options_BWP = "BWP";

		 //BYN - 白俄罗斯卢布
		public static final String ER_MC_CURRENCY_Options_BYN = "BYN";

		 //BYR - 白俄罗斯卢布
		public static final String ER_MC_CURRENCY_Options_BYR = "BYR";

		 //BZD - 伯利兹元
		public static final String ER_MC_CURRENCY_Options_BZD = "BZD";

		 //CAD - 加拿大元
		public static final String ER_MC_CURRENCY_Options_CAD = "CAD";

		 //CDF - 刚果法郎
		public static final String ER_MC_CURRENCY_Options_CDF = "CDF";

		 //CHF - 瑞士法郎
		public static final String ER_MC_CURRENCY_Options_CHF = "CHF";

		 //CLF - 智利货币
		public static final String ER_MC_CURRENCY_Options_CLF = "CLF";

		 //CLP - 智利比索
		public static final String ER_MC_CURRENCY_Options_CLP = "CLP";

		 //COP - 哥伦比亚比索
		public static final String ER_MC_CURRENCY_Options_COP = "COP";

		 //CRC - 哥斯达黎加科郎
		public static final String ER_MC_CURRENCY_Options_CRC = "CRC";

		 //CUC - 古巴比索自由兑换
		public static final String ER_MC_CURRENCY_Options_CUC = "CUC";

		 //CUP - 古巴比索
		public static final String ER_MC_CURRENCY_Options_CUP = "CUP";

		 //CVE - 佛得角埃斯库多
		public static final String ER_MC_CURRENCY_Options_CVE = "CVE";

		 //CZK - 捷克克朗
		public static final String ER_MC_CURRENCY_Options_CZK = "CZK";

		 //DJF - 吉布提法郎
		public static final String ER_MC_CURRENCY_Options_DJF = "DJF";

		 //DKK - 丹麦克埃
		public static final String ER_MC_CURRENCY_Options_DKK = "DKK";

		 //DOP - 多米尼加比索
		public static final String ER_MC_CURRENCY_Options_DOP = "DOP";

		 //DZD - 阿尔及利亚第纳尔
		public static final String ER_MC_CURRENCY_Options_DZD = "DZD";

		 //EEK - 爱沙尼亚克朗
		public static final String ER_MC_CURRENCY_Options_EEK = "EEK";

		 //EGP - 埃及镑
		public static final String ER_MC_CURRENCY_Options_EGP = "EGP";

		 //ERN - 厄立特里亚纳克法
		public static final String ER_MC_CURRENCY_Options_ERN = "ERN";

		 //ETB - 埃塞俄比亚比尔
		public static final String ER_MC_CURRENCY_Options_ETB = "ETB";

		 //FJD - 斐济元
		public static final String ER_MC_CURRENCY_Options_FJD = "FJD";

		 //FKP - 福克兰群岛镑
		public static final String ER_MC_CURRENCY_Options_FKP = "FKP";

		 //GBP - 英镑
		public static final String ER_MC_CURRENCY_Options_GBP = "GBP";

		 //GEL - 乔治亚拉里
		public static final String ER_MC_CURRENCY_Options_GEL = "GEL";

		 //GHS - 加纳塞地
		public static final String ER_MC_CURRENCY_Options_GHS = "GHS";

		 //GIP - 直布罗陀镑
		public static final String ER_MC_CURRENCY_Options_GIP = "GIP";

		 //GMD - 冈比亚达拉西
		public static final String ER_MC_CURRENCY_Options_GMD = "GMD";

		 //GNF - 几内亚法郎
		public static final String ER_MC_CURRENCY_Options_GNF = "GNF";

		 //GTQ - 危地马拉格查尔
		public static final String ER_MC_CURRENCY_Options_GTQ = "GTQ";

		 //GYD - 圭亚那元
		public static final String ER_MC_CURRENCY_Options_GYD = "GYD";

		 //HNL - 洪都拉斯伦比拉
		public static final String ER_MC_CURRENCY_Options_HNL = "HNL";

		 //HRD - 克罗地亚第纳尔（旧）
		public static final String ER_MC_CURRENCY_Options_HRD = "HRD";

		 //HRK - 库纳
		public static final String ER_MC_CURRENCY_Options_HRK = "HRK";

		 //HTG - 海地古德
		public static final String ER_MC_CURRENCY_Options_HTG = "HTG";

		 //HUF - 匈牙利福林
		public static final String ER_MC_CURRENCY_Options_HUF = "HUF";

		 //IDR - 印度尼西亚卢比
		public static final String ER_MC_CURRENCY_Options_IDR = "IDR";

		 //ILS - 以色列谢克尔
		public static final String ER_MC_CURRENCY_Options_ILS = "ILS";

		 //INR - 印度卢比
		public static final String ER_MC_CURRENCY_Options_INR = "INR";

		 //IQD - 伊拉克第纳尔
		public static final String ER_MC_CURRENCY_Options_IQD = "IQD";

		 //ISK - 冰岛克朗
		public static final String ER_MC_CURRENCY_Options_ISK = "ISK";

		 //JMD - 牙买加元
		public static final String ER_MC_CURRENCY_Options_JMD = "JMD";

		 //JOD - 约旦第纳尔
		public static final String ER_MC_CURRENCY_Options_JOD = "JOD";

		 //KES - 肯尼亚先令
		public static final String ER_MC_CURRENCY_Options_KES = "KES";

		 //KGS - 吉尔吉斯斯坦索姆
		public static final String ER_MC_CURRENCY_Options_KGS = "KGS";

		 //KHR - 柬埔寨里耳
		public static final String ER_MC_CURRENCY_Options_KHR = "KHR";

		 //KMF - 科摩罗法郎
		public static final String ER_MC_CURRENCY_Options_KMF = "KMF";

		 //KPW - 朝鲜元
		public static final String ER_MC_CURRENCY_Options_KPW = "KPW";

		 //KRW - 韩元
		public static final String ER_MC_CURRENCY_Options_KRW = "KRW";

		 //KWD - 科威特第纳尔
		public static final String ER_MC_CURRENCY_Options_KWD = "KWD";

		 //KYD - 开曼群岛元
		public static final String ER_MC_CURRENCY_Options_KYD = "KYD";

		 //KZT - 哈萨克斯坦腾格
		public static final String ER_MC_CURRENCY_Options_KZT = "KZT";

		 //LAK - 老挝基普
		public static final String ER_MC_CURRENCY_Options_LAK = "LAK";

		 //LBP - 黎巴嫩磅
		public static final String ER_MC_CURRENCY_Options_LBP = "LBP";

		 //LKR - 斯里兰卡卢比
		public static final String ER_MC_CURRENCY_Options_LKR = "LKR";

		 //LRD - 利比里亚元
		public static final String ER_MC_CURRENCY_Options_LRD = "LRD";

		 //LSL - 莱索托洛提
		public static final String ER_MC_CURRENCY_Options_LSL = "LSL";

		 //LYD - 利比亚第纳尔
		public static final String ER_MC_CURRENCY_Options_LYD = "LYD";

		 //MAD - 摩洛哥迪拉姆
		public static final String ER_MC_CURRENCY_Options_MAD = "MAD";

		 //MDL - 摩尔多瓦列伊
		public static final String ER_MC_CURRENCY_Options_MDL = "MDL";

		 //MGA - 马达加斯加阿里亚里
		public static final String ER_MC_CURRENCY_Options_MGA = "MGA";

		 //MKD - 马其顿第纳尔
		public static final String ER_MC_CURRENCY_Options_MKD = "MKD";

		 //MMK - 缅甸元
		public static final String ER_MC_CURRENCY_Options_MMK = "MMK";

		 //MNT - 蒙古图格里克
		public static final String ER_MC_CURRENCY_Options_MNT = "MNT";

		 //MOP - 澳门元
		public static final String ER_MC_CURRENCY_Options_MOP = "MOP";

		 //MRO - 毛里塔尼亚乌吉亚
		public static final String ER_MC_CURRENCY_Options_MRO = "MRO";

		 //MRU - 毛里塔尼亚乌吉亚
		public static final String ER_MC_CURRENCY_Options_MRU = "MRU";

		 //MUR - 毛里求斯卢比
		public static final String ER_MC_CURRENCY_Options_MUR = "MUR";

		 //MVR - 马尔代夫卢非亚
		public static final String ER_MC_CURRENCY_Options_MVR = "MVR";

		 //MWK - 马拉维克瓦查
		public static final String ER_MC_CURRENCY_Options_MWK = "MWK";

		 //MXN - 墨西哥比索
		public static final String ER_MC_CURRENCY_Options_MXN = "MXN";

		 //MXV - 墨西哥转换单位 (UDI)
		public static final String ER_MC_CURRENCY_Options_MXV = "MXV";

		 //MYR - 马来西亚林吉特
		public static final String ER_MC_CURRENCY_Options_MYR = "MYR";

		 //MZN - 莫桑比克新美提卡
		public static final String ER_MC_CURRENCY_Options_MZN = "MZN";

		 //NAD - 纳米比亚元
		public static final String ER_MC_CURRENCY_Options_NAD = "NAD";

		 //NGN - 尼日利亚奈拉
		public static final String ER_MC_CURRENCY_Options_NGN = "NGN";

		 //NIO - 尼加拉瓜科多巴
		public static final String ER_MC_CURRENCY_Options_NIO = "NIO";

		 //NOK - 挪威克郎
		public static final String ER_MC_CURRENCY_Options_NOK = "NOK";

		 //NPR - 尼泊尔卢比
		public static final String ER_MC_CURRENCY_Options_NPR = "NPR";

		 //NZD - 新西兰元
		public static final String ER_MC_CURRENCY_Options_NZD = "NZD";

		 //OMR - 阿曼里亚尔
		public static final String ER_MC_CURRENCY_Options_OMR = "OMR";

		 //PAB - 巴拿马巴波亚
		public static final String ER_MC_CURRENCY_Options_PAB = "PAB";

		 //PEN - 秘鲁索尔
		public static final String ER_MC_CURRENCY_Options_PEN = "PEN";

		 //PGK - 巴布亚新几内亚基那
		public static final String ER_MC_CURRENCY_Options_PGK = "PGK";

		 //PHP - 菲律宾比索
		public static final String ER_MC_CURRENCY_Options_PHP = "PHP";

		 //PKR - 巴基斯坦卢比
		public static final String ER_MC_CURRENCY_Options_PKR = "PKR";

		 //PLN - 波兰兹罗提
		public static final String ER_MC_CURRENCY_Options_PLN = "PLN";

		 //PYG - 巴拉圭瓜拉尼
		public static final String ER_MC_CURRENCY_Options_PYG = "PYG";

		 //QAR - 卡塔尔里亚尔
		public static final String ER_MC_CURRENCY_Options_QAR = "QAR";

		 //RON - 罗马尼亚列伊
		public static final String ER_MC_CURRENCY_Options_RON = "RON";

		 //RSD - 塞尔维亚第纳尔
		public static final String ER_MC_CURRENCY_Options_RSD = "RSD";

		 //RUB - 俄罗斯卢布
		public static final String ER_MC_CURRENCY_Options_RUB = "RUB";

		 //RWF - 卢旺达法郎
		public static final String ER_MC_CURRENCY_Options_RWF = "RWF";

		 //SAR - 沙特阿拉伯里亚尔
		public static final String ER_MC_CURRENCY_Options_SAR = "SAR";

		 //SBD - 所罗门群岛元
		public static final String ER_MC_CURRENCY_Options_SBD = "SBD";

		 //SCR - 塞舌尔卢比
		public static final String ER_MC_CURRENCY_Options_SCR = "SCR";

		 //SDG - 苏丹镑
		public static final String ER_MC_CURRENCY_Options_SDG = "SDG";

		 //SEK - 瑞典克朗
		public static final String ER_MC_CURRENCY_Options_SEK = "SEK";

		 //SGD - 新加坡元
		public static final String ER_MC_CURRENCY_Options_SGD = "SGD";

		 //SHP - 圣海伦斯磅
		public static final String ER_MC_CURRENCY_Options_SHP = "SHP";

		 //SLL - 塞拉利昂的利昂
		public static final String ER_MC_CURRENCY_Options_SLL = "SLL";

		 //SOS - 索马里先令
		public static final String ER_MC_CURRENCY_Options_SOS = "SOS";

		 //SRD - 苏利南元
		public static final String ER_MC_CURRENCY_Options_SRD = "SRD";

		 //SSP - 南苏丹镑
		public static final String ER_MC_CURRENCY_Options_SSP = "SSP";

		 //STD - 圣多美和普林西比多布拉
		public static final String ER_MC_CURRENCY_Options_STD = "STD";

		 //STN - 圣多美和普林西比多布拉
		public static final String ER_MC_CURRENCY_Options_STN = "STN";

		 //SYP - 叙利亚磅
		public static final String ER_MC_CURRENCY_Options_SYP = "SYP";

		 //SZL - 斯威士兰里兰吉尼
		public static final String ER_MC_CURRENCY_Options_SZL = "SZL";

		 //THB - 泰铢
		public static final String ER_MC_CURRENCY_Options_THB = "THB";

		 //TJS - 塔吉克斯坦索莫尼
		public static final String ER_MC_CURRENCY_Options_TJS = "TJS";

		 //TMT - 土库曼斯坦新马纳特
		public static final String ER_MC_CURRENCY_Options_TMT = "TMT";

		 //TND - 突尼斯第纳尔
		public static final String ER_MC_CURRENCY_Options_TND = "TND";

		 //TOP - 汤加潘加
		public static final String ER_MC_CURRENCY_Options_TOP = "TOP";

		 //TRY - 土耳其新里拉
		public static final String ER_MC_CURRENCY_Options_TRY = "TRY";

		 //TTD - 特立尼达和多巴哥元
		public static final String ER_MC_CURRENCY_Options_TTD = "TTD";

		 //TZS - 坦桑尼亚先令
		public static final String ER_MC_CURRENCY_Options_TZS = "TZS";

		 //UAH - 乌克兰格里夫纳
		public static final String ER_MC_CURRENCY_Options_UAH = "UAH";

		 //UGX - 乌干达先令
		public static final String ER_MC_CURRENCY_Options_UGX = "UGX";

		 //UYU - 乌拉圭比索
		public static final String ER_MC_CURRENCY_Options_UYU = "UYU";

		 //UZS - 乌兹别克斯坦苏姆
		public static final String ER_MC_CURRENCY_Options_UZS = "UZS";

		 //VEF - Venezuelan Bolivar Fuerte
		public static final String ER_MC_CURRENCY_Options_VEF = "VEF";

		 //VES - Venezuelan Bolívar Soberano
		public static final String ER_MC_CURRENCY_Options_VES = "VES";

		 //VND - 越南盾
		public static final String ER_MC_CURRENCY_Options_VND = "VND";

		 //VUV - 瓦努阿图瓦图
		public static final String ER_MC_CURRENCY_Options_VUV = "VUV";

		 //WST - 萨摩亚塔拉
		public static final String ER_MC_CURRENCY_Options_WST = "WST";

		 //XAF - 非洲金融共同体法郎 (BEAC)
		public static final String ER_MC_CURRENCY_Options_XAF = "XAF";

		 //XCD - 东加勒比元
		public static final String ER_MC_CURRENCY_Options_XCD = "XCD";

		 //XOF - 非洲金融共同体法郎 (BCEAO)
		public static final String ER_MC_CURRENCY_Options_XOF = "XOF";

		 //XPF - 太平洋法郎
		public static final String ER_MC_CURRENCY_Options_XPF = "XPF";

		 //YER - 也门里亚尔
		public static final String ER_MC_CURRENCY_Options_YER = "YER";

		 //ZAR - 南非兰特
		public static final String ER_MC_CURRENCY_Options_ZAR = "ZAR";

		 //ZMW - 赞比亚克瓦查（新）
		public static final String ER_MC_CURRENCY_Options_ZMW = "ZMW";

		 //ZWL - 津巴布韦元
		public static final String ER_MC_CURRENCY_Options_ZWL = "ZWL";

	 //下游员工id
	public static final String EMPLOYEE_ID = "employee_id";

	 //激活状态
	public static final String ACTIVE_STATE = "active_state";

		 //未激活
		public static final String ACTIVE_STATE_Options_0 = "0";

		 //已激活
		public static final String ACTIVE_STATE_Options_1 = "1";

	 //外部人员id
	public static final String OUTER_UID = "outer_uid";

	 //名称排序
	public static final String NAME_ORDER = "name_order";

}