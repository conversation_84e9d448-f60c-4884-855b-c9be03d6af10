package com.facishare.crm.fmcg.wq.util;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

/**
 * 字段验证工具类
 * 提供字段显示名称获取、字段值比较、异常信息格式化等功能
 * 用于统一处理字段相关的验证逻辑，避免硬编码字段名称
 * 
 * <AUTHOR>
 */
@Slf4j
public class FieldValidationUtil {

    /**
     * 获取字段的显示名称
     * 从字段描述中动态获取标签信息
     *
     * @param objectDescribe 对象描述
     * @param fieldName 字段名
     * @return 显示名称，如果获取失败则返回字段名
     */
    public static String getFieldDisplayName(IObjectDescribe objectDescribe, String fieldName) {
        if (objectDescribe == null || StringUtils.isBlank(fieldName)) {
            return fieldName;
        }

        try {
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(fieldName);
            if (fieldDescribe != null) {
                String label = fieldDescribe.getLabel();
                if (StringUtils.isNotBlank(label)) {
                    return label;
                }
            }
        } catch (Exception e) {
            log.warn("获取字段显示名称失败，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }

        // 如果获取失败，返回字段名
        return fieldName;
    }

    /**
     * 比较两个字段值是否相等
     * 空字符串视作null，两边都为空视作相等
     * 支持数组/集合比较，元素相同但顺序不同视作相等
     *
     * @param value1 第一个值
     * @param value2 第二个值
     * @return 是否相等
     */
    public static boolean isFieldValueEqual(Object value1, Object value2) {
        // 处理null值比较
        if (value1 == null && value2 == null) {
            return true;
        }
        if (value1 == null || value2 == null) {
            // 检查是否为空字符串（视作null）
            String normalizedValue1 = normalizeEmptyValue(value1);
            String normalizedValue2 = normalizeEmptyValue(value2);
            return normalizedValue1 == null && normalizedValue2 == null;
        }

        // 如果都是数组或集合类型，进行集合比较
        boolean value1isArrayOrCollection = isArrayOrCollection(value1);
        boolean value2isArrayOrCollection = isArrayOrCollection(value2);
        if (value1isArrayOrCollection && value2isArrayOrCollection) {
            return isArrayOrCollectionEqual(value1, value2);
        }

        // 如果一个是数组/集合，一个不是，则不相等
        if (value1isArrayOrCollection || value2isArrayOrCollection) {
            return false;
        }

        // 标准化值：将空字符串视作null
        String normalizedValue1 = normalizeEmptyValue(value1);
        String normalizedValue2 = normalizeEmptyValue(value2);

        // 使用StringUtils.equals进行比较，能正确处理null值
        return StringUtils.equals(normalizedValue1, normalizedValue2);
    }

    /**
     * 验证字段是否被修改
     * 空字符串视作null，两边都为空不算改变
     *
     * @param objectDescribe 对象描述
     * @param fieldName 字段名
     * @param newValue 新值
     * @param originalValue 原始值
     * @param reason 不允许修改的原因
     * @throws com.facishare.paas.appframework.core.exception.ValidateException 如果字段被修改
     */
    public static void validateFieldNotChanged(IObjectDescribe objectDescribe, String fieldName, 
            Object newValue, Object originalValue, String reason) {
        if (!isFieldValueEqual(newValue, originalValue)) {
            String fieldDisplayName = getFieldDisplayName(objectDescribe, fieldName);
            String message = createFieldNotEditableMessage(fieldDisplayName, reason);
            throw new com.facishare.paas.appframework.core.exception.ValidateException(message);
        }
    }

    /**
     * 格式化异常信息，使用字段显示名称
     * 
     * @param objectDescribe 对象描述
     * @param template 异常信息模板，使用 %s 占位符
     * @param fieldNames 字段名数组
     * @return 格式化后的异常信息
     */
    public static String formatExceptionMessage(IObjectDescribe objectDescribe, String template, String... fieldNames) {
        if (fieldNames == null || fieldNames.length == 0) {
            return template;
        }

        String[] displayNames = new String[fieldNames.length];
        for (int i = 0; i < fieldNames.length; i++) {
            displayNames[i] = getFieldDisplayName(objectDescribe, fieldNames[i]);
        }

        return String.format(template, (Object[]) displayNames);
    }

    /**
     * 创建字段不允许修改的异常信息
     * 
     * @param fieldDisplayName 字段显示名称
     * @param reason 不允许修改的原因
     * @return 异常信息
     */
    public static String createFieldNotEditableMessage(String fieldDisplayName, String reason) {
        if (StringUtils.isNotBlank(reason)) {
            return String.format("不允许修改%s。%s", fieldDisplayName, reason); // ignoreI18n
        } else {
            return String.format("不允许修改%s", fieldDisplayName); // ignoreI18n
        }
    }

    /**
     * 创建字段必填的异常信息
     * 
     * @param objectDescribe 对象描述
     * @param fieldName 字段名
     * @param condition 必填条件描述
     * @return 异常信息
     */
    public static String createFieldRequiredMessage(IObjectDescribe objectDescribe, String fieldName, String condition) {
        String fieldDisplayName = getFieldDisplayName(objectDescribe, fieldName);
        if (StringUtils.isNotBlank(condition)) {
            return String.format("%s时，%s为必填字段", condition, fieldDisplayName); // ignoreI18n
        } else {
            return String.format("%s为必填字段", fieldDisplayName); // ignoreI18n
        }
    }

    /**
     * 创建字段值不一致的异常信息
     * 
     * @param objectDescribe 对象描述
     * @param fieldName1 第一个字段名
     * @param fieldName2 第二个字段名（可选）
     * @return 异常信息
     */
    public static String createFieldMismatchMessage(IObjectDescribe objectDescribe, String fieldName1, String fieldName2) {
        String fieldDisplayName1 = getFieldDisplayName(objectDescribe, fieldName1);
        if (StringUtils.isNotBlank(fieldName2)) {
            String fieldDisplayName2 = getFieldDisplayName(objectDescribe, fieldName2);
            return String.format("%s与%s不一致", fieldDisplayName1, fieldDisplayName2); // ignoreI18n
        } else {
            return String.format("%s不一致", fieldDisplayName1); // ignoreI18n
        }
    }

    /**
     * 标准化空值：将空字符串视作null
     * 
     * @param value 原始值
     * @return 标准化后的值，空字符串返回null，其他值转为字符串
     */
    private static String normalizeEmptyValue(Object value) {
        if (value == null) {
            return null;
        }
        
        String stringValue = value.toString();
        // 空字符串视作null
        if (StringUtils.isBlank(stringValue)) {
            return null;
        }
        
        return stringValue;
    }

    /**
     * 判断对象是否为数组或集合类型
     *
     * @param value 要判断的对象
     * @return 是否为数组或集合
     */
    private static boolean isArrayOrCollection(Object value) {
        if (value == null) {
            return false;
        }
        return value.getClass().isArray() || value instanceof java.util.Collection;
    }

    /**
     * 比较两个数组或集合是否相等（元素相同，不考虑顺序）
     *
     * @param value1 第一个数组或集合
     * @param value2 第二个数组或集合
     * @return 是否相等
     */
    private static boolean isArrayOrCollectionEqual(Object value1, Object value2) {
        java.util.Set<String> set1 = convertToStringSet(value1);
        java.util.Set<String> set2 = convertToStringSet(value2);

        return set1.equals(set2);
    }

    /**
     * 将数组或集合转换为字符串Set
     *
     * @param value 数组或集合对象
     * @return 字符串Set
     */
    private static java.util.Set<String> convertToStringSet(Object value) {
        java.util.Set<String> result = new java.util.HashSet<>();

        if (value == null) {
            return result;
        }

        if (value.getClass().isArray()) {
            // 处理数组
            Object[] array = (Object[]) value;
            for (Object item : array) {
                if (item != null) {
                    String normalizedItem = normalizeEmptyValue(item);
                    if (normalizedItem != null) {
                        result.add(normalizedItem);
                    }
                }
            }
        } else if (value instanceof java.util.Collection) {
            // 处理集合
            java.util.Collection<?> collection = (java.util.Collection<?>) value;
            for (Object item : collection) {
                if (item != null) {
                    String normalizedItem = normalizeEmptyValue(item);
                    if (normalizedItem != null) {
                        result.add(normalizedItem);
                    }
                }
            }
        }

        return result;
    }
}
