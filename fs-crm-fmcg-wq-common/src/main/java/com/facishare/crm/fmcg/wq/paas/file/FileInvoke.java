package com.facishare.crm.fmcg.wq.paas.file;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileDownload;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/12/25/ 20:15
 **/
@Slf4j
@Service
public class FileInvoke {

    @Autowired
    private NFileStorageService nFileStorageService;

    public byte[] nDownloadTempFile(String userAccount, String tempFileName) {
        String[] split = userAccount.split("\\.");
        String ea = split[1];
        String userId = split[2];
        NTempFileDownload.Arg arg = new NTempFileDownload.Arg();
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        arg.setTempFileName(tempFileName);
        return nFileStorageService.nTempFileDownload(arg, ea).getData();
    }

    public List<List<Object>> getExcelDataList(InputStream inputStream, List<List<String>> headList) {
        List<Object> list = EasyExcel.read(inputStream).head(headList).autoCloseStream(true).sheet().doReadSync();
        List<List<Object>> result = Lists.newArrayList();
        for (Object obj : list) {
            List<Object> data = (List<Object>) obj;
            result.add(data);
        }
        return result;
    }

}
