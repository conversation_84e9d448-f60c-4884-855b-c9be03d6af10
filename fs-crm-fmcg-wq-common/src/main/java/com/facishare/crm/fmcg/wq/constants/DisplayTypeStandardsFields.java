package com.facishare.crm.fmcg.wq.constants;

public abstract class DisplayTypeStandardsFields {

	private DisplayTypeStandardsFields(){};

	public static final String DISPLAY_NAME = "陈列形式标准"; //ignoreI18n

	public static final String API_NAME = "DisplayTypeStandardsObj";

	 //陈列形式
	public static final String DISPLAY_FORM = "display_form";

	 //示意图
	public static final String STANDARD_SCHEMATIC_DIAGRAM = "standard_schematic_diagram";

	 //陈列位置
	public static final String DISPLAY_POSITION = "display_position";

		 //主通道
		public static final String DISPLAY_POSITION_Options_1 = "1";

		 //门店显眼位
		public static final String DISPLAY_POSITION_Options_2 = "2";

		 //入口处
		public static final String DISPLAY_POSITION_Options_3 = "3";

		 //端头货架
		public static final String DISPLAY_POSITION_Options_4 = "4";

		 //堆头陈列区
		public static final String DISPLAY_POSITION_Options_5 = "5";

		 //收银台附近
		public static final String DISPLAY_POSITION_Options_6 = "6";

		 //冰柜和冷藏区
		public static final String DISPLAY_POSITION_Options_7 = "7";

		 //竞品旁边
		public static final String DISPLAY_POSITION_Options_8 = "8";

		 //其他
		public static final String DISPLAY_POSITION_Options_other = "other";

	 //标准说明
	public static final String EXPLANATION_PROJECT_STANDA = "explanation_project_standa";

	 //陈列标准
	public static final String SUCCESSFUL_STORE = "successful_store";

	 //是否按层定义
	public static final String DEFINE_LAYER = "define_layer";

		 //否
		public static final String DEFINE_LAYER_Options_0 = "0";

		 //是
		public static final String DEFINE_LAYER_Options_1 = "1";

		 //其他
		public static final String DEFINE_LAYER_Options_other = "other";

}
