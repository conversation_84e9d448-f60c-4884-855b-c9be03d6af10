package com.facishare.crm.fmcg.wq.exception;

import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ErrorCode;

/**
 * Created by zhangsm on 2018/4/9/0009.
 */
public class CheckinsException extends AppBusinessException {
    public CheckinsException(String message, ErrorCode errorCode) {
        super(message, errorCode);
    }
    public CheckinsException(CheckinsErrorCode checkinsErrorCode){
        super(checkinsErrorCode.getMessage(),checkinsErrorCode);
    }
    public CheckinsException(CheckinsErrorCode checkinsErrorCode,String[] datas){
        super(checkinsErrorCode.getMessage(datas),checkinsErrorCode);
    }
    public CheckinsException(String message,int code) {
        super(message, code);
    }

}
