package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.predef.action.StandardEditAction;

import java.util.Optional;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-06-25 10:57
 **/
public class FmcgSkipPermissionEditAction extends StandardEditAction {
    String checkinsFlag = null;
    @Override
    protected void before(Arg arg) {
        checkinsFlag = Optional.ofNullable(arg.getObjectData().get("checkins_flag")).orElse("").toString();
        if (checkinsFlag.equals("1")) {
            this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
            this.actionContext.setAttribute("triggerFlow",Boolean.FALSE);
        }else if (checkinsFlag.equals("2")){
            this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
        }
        super.before(arg);
    }
}
