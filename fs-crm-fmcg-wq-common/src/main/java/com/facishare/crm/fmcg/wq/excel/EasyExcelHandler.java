package com.facishare.crm.fmcg.wq.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.facishare.crm.fmcg.wq.excel.style.FmcgAutoMatchColumnWidthAndRowHeightStyleStrategy;
import com.facishare.crm.fmcg.wq.excel.style.PullDownCellWriteHandler;
import com.facishare.crm.fmcg.wq.session.FmcgPushSession;
import com.facishare.crm.fmcg.wq.session.SessionContentForExport;
import com.facishare.fsi.proxy.model.global.config.GetEnterpriseConfigByEA;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.qixin.api.model.AuthInfo;
import com.facishare.qixin.api.model.EmployeeId;
//import com.facishare.svclib.fsi.service.FsiBuilder;
//import com.facishare.svclib.fsi.service.FsiUrlPath;
//import com.facishare.svclib.fsi.service.Global.Config.GetEnterpriseConfigByEA;
//import com.facishare.svclib.fsi.service.N.NStorage.NUploadFileDirect;
import com.fxiaoke.common.release.GrayRelease;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.github.autoconf.api.IConfigFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/12/20/ 18:07
 **/
@Slf4j
@Component
public class EasyExcelHandler {

    @Autowired
    private NFileStorageService nFileStorageService;

    @Autowired
    private FmcgPushSession fmcgPushSession;

    private String enterpriseConfigURL;

    @PostConstruct
    private void init() {
//        IConfigFactory factory = ConfigFactory.getInstance();
//        String path = GetEnterpriseConfigByEA.class.getAnnotation(FsiUrlPath.class).value();
//        IChangeableConfig props = factory.getConfig("Checkin-third-config",
//                config -> enterpriseConfigURL = config.get(path));
//
//        enterpriseConfigURL = props.get(path);
    }

    private static <T> List<List<String>> getExcelHeader(Class<T> clazz) {
        List<Field> list = Lists.newArrayList();
        list.addAll(Arrays.asList(clazz.getDeclaredFields()));
        list.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        return list.stream()
                .filter(field -> Objects.nonNull(field.getAnnotation(ExcelProperty.class)))
                .sorted(Comparator.comparing(field -> field.getAnnotation(ExcelProperty.class).index()))
                .map(field -> Lists.newArrayList(field.getName())).collect(Collectors.toList());
    }

    public WriteCellStyle getHeadWriteCellStyle() {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        //背景颜色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        //字体样式
        WriteFont font = new WriteFont();
        //字体
        font.setFontName("宋体"); //ignoreI18n
        //大小
        font.setFontHeightInPoints((short)10);
        //字体颜色
        font.setColor(IndexedColors.WHITE1.getIndex());
        //是否加粗
        font.setBold(true);
        //设置字体风格
        headWriteCellStyle.setWriteFont(font);
        headWriteCellStyle.setWrapped(false);
        return headWriteCellStyle;
    }

    public void sendFile(byte[] fileByte,String ea,int userId,String fileName,String appId){
        if(Objects.nonNull(fileByte)) {
            try {
                AuthInfo auth = new AuthInfo();
                auth.setAppId(appId);
                auth.setEmployeeId(EmployeeId.build(ea, 0));
                long size = fileByte.length;
                String path = uploadFileN(ea, userId, fileByte, "xlsx");
                log.info("exportFile fileName:{},size:{}, path:{}", fileName, size, path);
                SessionContentForExport export = new SessionContentForExport(path, size, fileName + ".xlsx");
                fmcgPushSession.sendSessionForExport(Lists.newArrayList(userId), ea, auth,export);
            }catch (Exception e){
                log.error("sendFile error",e);
                throw e;
            }
        }
    }
    public String uploadFileN(String enterpriseAccount, Integer employeeId, byte[] fileByte, String fileExtName) {
//        if (GrayRelease.isAllow("checkin-server-v2", "fileOldFsi", enterpriseAccount)) {
//            NUploadFileDirect fsi = FsiBuilder.newBuilder()
//                    .setFsiHost((config, EA) -> getEnterpriseConfigByEA(EA)).isFinalHost(false)
//                    .build(NUploadFileDirect.class);
//
//            NUploadFileDirect.Args arg = fsi.args();
//            arg.sourceUser = ("E." + employeeId);
//            arg.data = (fileByte);
//            arg.fileSecurityGroup = ("");
//            arg.fileExt = (fileExtName);
//            arg.EA = (enterpriseAccount);
//            try {
//                return fsi.post(enterpriseAccount, arg).finalNPath;
//            } catch (Exception e) {
//                log.error("uploadFileN: old arg:[{},{},{}] error:[{}]", enterpriseAccount, employeeId, fileExtName, e.getMessage());
//                throw e;
//            }
//        }else{
            try {
                com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect.Arg arg = new com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect.Arg();
                arg.setSourceUser("E." + employeeId);
                arg.setData(fileByte);
                arg.setFileSecurityGroup("");
                arg.setFileExt(fileExtName);
                arg.setEa(enterpriseAccount);
                arg.setExpireDay(7);
                com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect.Result result = nFileStorageService.nUploadFileDirect(arg,enterpriseAccount);
                return result.getFinalNPath();
            } catch (Exception e) {
                log.error("uploadFileN: arg:[{},{},{}] error:[{}]", enterpriseAccount, employeeId, fileExtName, e.getMessage());
                throw e;
            }
//        }
    }

//    public String getEnterpriseConfigByEA(String enterpriseAccount) {
//        GetEnterpriseConfigByEA fsi = FsiBuilder.newBuilder()
//                .setFsiHost((config, ea) -> enterpriseConfigURL)
//                .build(GetEnterpriseConfigByEA.class);
//
//        GetEnterpriseConfigByEA.Args arg = fsi.args();
//
//        arg.EnterpriseAccount = enterpriseAccount;
//
//        try {
//            return fsi.post(enterpriseAccount, arg).EnterpriseConfig.FspV5Address;
//        } catch (Exception e) {
//            log.error("[error] 获取企业ip失败：" + enterpriseAccount, e);
//            throw e;
//        }
//    }

    public void pushFile(String ea,int userId,List<List<String>> dataList,List<List<String>> headList,String title){
        pushFile(ea,userId,dataList,headList,title,null,0,0,0,0);
    }
    public void pushFile(String ea,int userId,List<List<String>> dataList,List<List<String>> headList,String title,
                         List<String> dropdownList,int firstRow,int lastRow,int firstCol,int lastCol){
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        //表头样式
        WriteCellStyle headWriteCellStyle = getHeadWriteCellStyle();
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcel.write(os).head(headList).registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, new WriteCellStyle()))
                    .registerWriteHandler(new PullDownCellWriteHandler(dropdownList,firstRow,lastRow,firstCol,lastCol))
                    .registerWriteHandler(new FmcgAutoMatchColumnWidthAndRowHeightStyleStrategy((short)20,(short)14)).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(title).build();
            excelWriter.write(dataList,writeSheet);
        }catch (Exception e){
            log.info("pushFile data is error",e);
            // 发生异常时，将os重置，避免发送损坏的数据
            os.reset();
        }finally {
            if (excelWriter != null) {
                try {
                    excelWriter.finish();
                } catch (Exception e) {
                    log.error("pushFile excelWriter finish error", e);
                    // 即使finish失败，也要确保os被正确处理
                    os.reset();
                }
            }
        }
        // 只有当os中有数据时才发送文件
        if (os.size() > 0) {
            sendFile(os.toByteArray(),ea,userId,title,"");
        }
        try {
            os.close();
        } catch (Exception e) {
            log.error("pushFile close os error ",e);
        }
    }

}
