package com.facishare.crm.fmcg.wq.session;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.qixin.api.constant.MessageType;
import com.facishare.qixin.api.constant.OSS1SubCategory;
import com.facishare.qixin.api.model.AuthInfo;
import com.facishare.qixin.api.model.EmployeeId;
import com.facishare.qixin.api.model.LastItem;
import com.facishare.qixin.api.model.message.content.Document;
import com.facishare.qixin.api.model.message.content.OTTemplateMessage;
import com.facishare.qixin.api.model.pushSession.arg.ExternalMessage;
import com.facishare.qixin.api.model.pushSession.arg.PushOSS1SessionArg;
import com.facishare.qixin.api.model.session.PushItem;
import com.facishare.qixin.api.service.PushSessionService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/12/20/ 19:32
 **/
@Slf4j
@Service
public class FmcgPushSession {


    @Autowired
    public PushSessionService pushSessionService;

    public void doSendSession(String ea, List<Integer> receivers, SessionContent sessionContent, OSS1SubCategory oss1SubCategory) {
        if (CollectionUtils.isEmpty(receivers)) {
            return;
        }

        AuthInfo auth = new AuthInfo();
        auth.setAppId("");
        auth.setEmployeeId(EmployeeId.build(ea, 0));

        ExternalMessage externalMessage = new ExternalMessage();
        externalMessage.setMessgeType(MessageType.TEMPLATE);
        // 标题
        OTTemplateMessage otTemplateMessageContent = new OTTemplateMessage();
        OTTemplateMessage.Title title = new OTTemplateMessage.Title();
        title.content = sessionContent.title;
        title.color = "#333333";
        // 副标题
        title.time = sessionContent.subtitle;
        otTemplateMessageContent.title = title;

        OTTemplateMessage.Frist first = new OTTemplateMessage.Frist();
        first.content = sessionContent.first;
        otTemplateMessageContent.first = first;

        List<OTTemplateMessage.Info> infos = Lists.newArrayList();
        for (Map<String, StringBuilder> contentLine : sessionContent.content) {
            OTTemplateMessage.Info info = new OTTemplateMessage.Info();
            Object label = contentLine.get("label");
            Object value = contentLine.get("value");
            if (Objects.nonNull(label)) {
                info.label = label.toString();
            }
            if (Objects.nonNull(value)) {
                info.value = value.toString();
            }
            infos.add(info);
        }
        otTemplateMessageContent.infos = infos;

        // 详情点击
        OTTemplateMessage.Button button = new OTTemplateMessage.Button();
        button.url = sessionContent.link;
        button.title = StringUtils.isEmpty(sessionContent.buttonTitle) ? "查看详情" : sessionContent.buttonTitle; //ignoreI18n
        otTemplateMessageContent.button = button;

        // 通知栏提醒
        PushItem pushItem = new PushItem();
        pushItem.setPushMessage(sessionContent.pushTitle);
        pushItem.setIsWithSound(true);

        // 更改通知栏最后提示
        LastItem lastItem = new LastItem();
        lastItem.setLastTime(System.currentTimeMillis());
        lastItem.setSummary(sessionContent.title);

        externalMessage.setOtTemplateMessageContent(otTemplateMessageContent);
        sendSession(receivers, externalMessage, oss1SubCategory,auth,pushItem, lastItem, ea);


    }

    public void sendSessionForExport(List<Integer> receivers, String ea,AuthInfo auth, SessionContent sessionContent) {
        log.info("sendSessionForExport start, ea={}, auth={}", ea, auth.toString());

        ExternalMessage externalMessage = getExternalMessage(sessionContent);

        // 通知栏提醒
        PushItem pushItem = new PushItem();
        pushItem.setPushMessage(sessionContent.pushTitle);
        pushItem.setIsWithSound(true);

        LastItem lastItem = new LastItem();
        lastItem.setLastTime(System.currentTimeMillis());
        lastItem.setSummary("文件"); //ignoreI18n
        // 文件通知
        sendSession(receivers, externalMessage, OSS1SubCategory.WJTZ,auth, pushItem, lastItem,
                ea);
    }
    @NotNull
    private static ExternalMessage getExternalMessage(SessionContent sessionContent) {
        SessionContentForExport sessionContent0;
        if (sessionContent instanceof SessionContentForExport) {
            sessionContent0 = (SessionContentForExport) sessionContent;
        } else {
            throw new ValidateException("消息类型不匹配"); //ignoreI18n
        }


        ExternalMessage externalMessage = new ExternalMessage();

        Document document = new Document();

        document.setName(sessionContent0.fileName);
        document.setSize((int)sessionContent0.size);
        // 0不可预览 1可预览
        document.setPrv(sessionContent0.prv);
        document.setFile(sessionContent0.link);
        // 文档类型
        externalMessage.setMessgeType(MessageType.DOCUMENT);
        externalMessage.setDocumentContent(document);
        return externalMessage;
    }

    /**
     * 发送session，异步导出推送消息使用
     */
    private void sendSession(List<Integer> employeeIds, ExternalMessage externalMessage, OSS1SubCategory oss1SubCategory,
                            AuthInfo auth, PushItem pushItem, LastItem lastItem, String ea) {
        PushOSS1SessionArg arg = new PushOSS1SessionArg();

        arg.setNotReadCount(0);
        if (Objects.nonNull(pushItem)) {
            // 通知栏
            arg.setPushItem(pushItem); 
        }
        if (Objects.nonNull(lastItem)) {
            // 更改最后通知提示
            arg.setLastItem(lastItem); 
        }

        arg.setAuthInfo(auth);
        // 接受者
        arg.setEmployeeIds(employeeIds);
        // session类型
        arg.setOss1SubCategory(oss1SubCategory);
        // 内容
        arg.setExternalMessage(externalMessage); 
        arg.setEnterpriseAccount(ea);
        try {
            log.info("send message start. receivers {}, message type {}, ea {}, args-{}", employeeIds,
                    oss1SubCategory.getValue(), ea, new Gson().toJson(arg));
            pushSessionService.pushOSS1Session(arg);
            log.info("send message success. receivers {}, message type {}, ea {}", employeeIds, oss1SubCategory.getValue(), ea);
        }catch (Exception e){
            log.error("send message error", e);
        }
    }

}
