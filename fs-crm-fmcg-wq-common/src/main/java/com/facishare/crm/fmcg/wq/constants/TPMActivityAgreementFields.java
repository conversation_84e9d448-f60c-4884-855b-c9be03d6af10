package com.facishare.crm.fmcg.wq.constants;

public abstract class TPMActivityAgreementFields {

	private TPMActivityAgreementFields(){};

	public static final String DISPLAY_NAME = "活动协议"; //ignoreI18n

	public static final String API_NAME = "TPMActivityAgreementObj";

	 //结束日期
	public static final String END_DATE = "end_date";

	 //门店
	public static final String STORE_ID = "store_id";

	 //协议图片
	public static final String AGREEMENT_IMAGES = "agreement_images";

	 //协议编号
	public static final String CODE = "code";

	 //开始日期
	public static final String BEGIN_DATE = "begin_date";

	 //备注
	public static final String DESCRIPTION = "description";

	 //协议费用(元)
	public static final String TOTAL = "total";

	 //协议费用(元)
	public static final String ACTUAL_TOTAL_AMOUNT = "actual_total_amount";

	 //店主签名
	public static final String STORE_OWNER_SIGNATURE = "store_owner_signature";

	 //动作ID
	public static final String ACTION_ID = "action_id";

	 //活动方案
	public static final String ACTIVITY_ID = "activity_id";

	 //协议状态
	public static final String AGREEMENT_STATUS = "agreement_status";

		 //未生效
		public static final String AGREEMENT_STATUS_Options_schedule = "schedule";

		 //已生效
		public static final String AGREEMENT_STATUS_Options_in_progress = "in_progress";

		 //已过期
		public static final String AGREEMENT_STATUS_Options_end = "end";

		 //其他
		public static final String AGREEMENT_STATUS_Options_other = "other";

	 //外勤ID
	public static final String VISIT_ID = "visit_id";

	 //经销商
	public static final String DEALER_ID = "dealer_id";

}
