package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 特例供货
 * @author: zhangsm
 * @create: 2021-04-25 21:52
 **/
public interface SpecialSupplyObjConstants {
    String API_NAME = "SpecialSupplyObj";
    String DISPLAY_NAME = "特例供货"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field{
        shopId("supply_object_name","名称"), //ignoreI18n
        productId("product","产品"), //ignoreI18n
        dealerId("supplier","上级供货商"), //ignoreI18n
        dealerSupplyId("dealer_supply","关联供货关系"), //ignoreI18n
        type("type","类型"); //ignoreI18n

        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
}
