package com.facishare.crm.fmcg.wq.constants;

public abstract class DisplayFormFields {

	private DisplayFormFields(){};

	public static final String DISPLAY_NAME = "陈列形式"; //ignoreI18n

	public static final String API_NAME = "DisplayFormObj";

	 //长(m)
	public static final String DISPLAY_LENGTH = "display_length";

	 //陈列位置
	public static final String DISPLAY_POSITION = "display_position";

		 //主通
		public static final String DISPLAY_POSITION_Options_1 = "1";

		 //门店显眼
		public static final String DISPLAY_POSITION_Options_2 = "2";

		 //入口
		public static final String DISPLAY_POSITION_Options_3 = "3";

		 //端头货
		public static final String DISPLAY_POSITION_Options_4 = "4";

		 //堆头陈列
		public static final String DISPLAY_POSITION_Options_5 = "5";

		 //收银台附
		public static final String DISPLAY_POSITION_Options_6 = "6";

		 //冰柜和冷藏
		public static final String DISPLAY_POSITION_Options_7 = "7";

		 //竞品旁
		public static final String DISPLAY_POSITION_Options_8 = "8";

		 //电梯上下
		public static final String DISPLAY_POSITION_Options_9 = "9";

		 //其他
		public static final String DISPLAY_POSITION_Options_other = "other";

	 //层数
	public static final String NUMBER_LAYERS = "number_layers";

		 //1层
		public static final String NUMBER_LAYERS_Options_1 = "1";

		 //2层
		public static final String NUMBER_LAYERS_Options_2 = "2";

		 //3层
		public static final String NUMBER_LAYERS_Options_3 = "3";

		 //4层
		public static final String NUMBER_LAYERS_Options_4 = "4";

		 //5层
		public static final String NUMBER_LAYERS_Options_5 = "5";

		 //6层
		public static final String NUMBER_LAYERS_Options_6 = "6";

		 //7层
		public static final String NUMBER_LAYERS_Options_7 = "7";

		 //其他
		public static final String NUMBER_LAYERS_Options_other = "other";

	 //关联陈列项目
	public static final String DISPLAY_PROJECT = "display_project";

	 //标准效果图
	public static final String STANDARD_RENDERING = "standard_rendering";

	 //高(m)
	public static final String DISPLAY_HEIGHT = "display_height";

	 //是否启用
	public static final String STATE = "state";

		 //是
		public static final String STATE_Options_true = "true";

		 //否
		public static final String STATE_Options_false = "false";

	 //陈列方式
	public static final String DISPLAY_FORMS = "display_forms";

		 //货架陈
		public static final String DISPLAY_FORMS_Options_1 = "1";

		 //地推陈
		public static final String DISPLAY_FORMS_Options_2 = "2";

		 //冰柜陈
		public static final String DISPLAY_FORMS_Options_3 = "3";

		 //收银台陈
		public static final String DISPLAY_FORMS_Options_4 = "4";

		 //其他
		public static final String DISPLAY_FORMS_Options_other = "other";

	 //描述
	public static final String DISPLAY_DESCRIBE = "display_describe";

	 //适用场景
	public static final String APPLICABLE_SCENARIOS = "applicable_scenarios";

		 //新品推
		public static final String APPLICABLE_SCENARIOS_Options_1 = "1";

		 //日常销
		public static final String APPLICABLE_SCENARIOS_Options_2 = "2";

		 //促销活
		public static final String APPLICABLE_SCENARIOS_Options_3 = "3";

		 //其他
		public static final String APPLICABLE_SCENARIOS_Options_other = "other";

	 //宽(m)
	public static final String DISPLAY_WIDTH = "display_width";

	 //所属分类
	public static final String DISPLAY_TYPES = "display_types";

		 //主陈
		public static final String DISPLAY_TYPES_Options_1 = "1";

		 //二陈
		public static final String DISPLAY_TYPES_Options_2 = "2";

		 //多点陈
		public static final String DISPLAY_TYPES_Options_3 = "3";

		 //其他
		public static final String DISPLAY_TYPES_Options_other = "other";

}
