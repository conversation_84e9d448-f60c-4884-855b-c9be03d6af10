package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;

import java.util.Optional;

public class FmcgSkipPermissionIncrementUpdateAction extends StandardIncrementUpdateAction {
    String checkinsFlag = null;
    @Override
    protected void before(Arg arg) {
        checkinsFlag = Optional.ofNullable(arg.getData().get("checkins_flag")).orElse("").toString();
        if (checkinsFlag.equals("1")) {
            this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
            this.actionContext.setAttribute("triggerFlow",Boolean.FALSE);
            arg.getData().remove("checkins_flag");
        }else if (checkinsFlag.equals("2")){
            this.actionContext.setAttribute("skipBaseValidate", Boolean.TRUE);
            arg.getData().remove("checkins_flag");
        }
        super.before(arg);
    }
}
