package com.facishare.crm.fmcg.wq.dao;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-19 15:30
 **/
public interface BaseDaoInterface {
    String rFormat = "%s__r";
    /**
     * 查询数据 不包含作废
     * @param tenantId
     * @param apiName
     * @param id
     * @return
     */
    IObjectData getById(String tenantId, String apiName, String id);

    /**
     * 查询数据 不包含作废
     * @param tenantId
     * @param apiName
     * @param id
     * @return
     */
    List<IObjectData> getByIds(String tenantId, String apiName, List<String> id);
    /**
     * 查询主从数据
     * @param user 用户信息
     * @param mainObjectApiName 主对象API名称
     * @param mainObjectId 主对象ID
     * @return 主从数据
     */
    Map<String, Map<String, IObjectData>> getMainAndDetailObjectData(User user, String mainObjectApiName, String mainObjectId);
    /**
     * 查询数据 包含作废
     * @param tenantId
     * @param apiName
     * @param ids
     * @return
     */
    List<IObjectData> findByIdsIncludeInvalid(String tenantId, String apiName, List<String> ids);

    /**
     * 循环查符合范围所有数据
     *
     * @param tenantId
     * @param searchQuery
     * @param apiName
     * @return
     */
    List<IObjectData> getAllIObjectDataListByQuery(User user, SearchQuery searchQuery, String apiName);

    /**
     * 循环查符合范围所有数据，指定返回字段
     *
     * @param tenantId
     * @param searchQuery
     * @param apiName
     * @return
     */
    List<IObjectData> getAllIObjectDataListByQueryWithFields(User user, SearchQuery searchQuery, String apiName, List<String> fields);

    /**
     * 根据条件查所有数据
     * @param tenantId
     * @param searchQuery
     * @param apiName
     * @param isQueryAll
     * @return
     */
    QueryResult<IObjectData> getAllQueryDataListByQueryWithFields(User user, SearchQuery searchQuery, String apiName, List<String> fields);
    /**
     * 根据条件查数据，可以指定是否查所有
     * @param tenantId
     * @param searchQuery
     * @param apiName
     * @param isQueryAll
     * @return
     */
    QueryResult<IObjectData> getQueryDataListByQuery(User user, SearchQuery searchQuery, String apiName, boolean isQueryAll);

    List<IObjectData> getAllIObjectDataListByQueryWithDb(User user, SearchQuery searchQuery, String apiName);
    /**
     * 系统单条保存
     * @param tenantId
     * @param data
     * @return
     */
    IObjectData save(User user, IObjectData data);

    /**
     * 系统批量保存 默认触发工作流
     * @param tenantId
     * @param addList
     * @return
     */
    List<IObjectData> batchSave(User user, Collection<IObjectData> addList);

    /**
     * 单一对象批量保存 默认触发工作流
     * @param objectDataList
     * @param user
     * @return
     */
    List<IObjectData> batchSaveOfSingleApiName(List<IObjectData> objectDataList, User user);
    /**
     * 系统修改
     * @param tenantId
     * @param data
     * @return
     */
    IObjectData update(User user, IObjectData data);

    /**
     * 跳过权限 会有修改人逻辑
     * @param tenantId
     * @param userId
     * @param data
     * @return
     */
    IObjectData update(String tenantId, String userId, IObjectData data);

    /**
     * 默认不触发工作流  更新操作 没有修改记录
     * @param objectDataList
     * @param user
     * @return
     */
     List<IObjectData> batchUpdate(List<IObjectData> objectDataList, User user);

    /**
     * rest批量更新带修改记录的接口
     * @param tenantId
     * @param userId
     * @param apiName
     * @param data
     */
    void batchUpdateDataByProxy(int tenantId, int userId, String apiName, List<JSONObject> data);

    /**
     * 获取并作废删除
     * @param tenantId
     * @param searchQueryBuilder
     * @param apiName
     * @return
     */
    Map<String,IObjectData> batchInvalidAndDelByQuery(User user, SearchQuery.SearchQueryBuilder searchQueryBuilder, String apiName);
    /**
     * 删除 查找关联的相关数据
     *
     * @param tenantId
     * @param lookupDataIds 需要删除的查找关联数据id
     * @param apiName       要删除的对象 apiname
     * @param lookUpFields  要删除的对象打的查找关联字段
     */
    void delLookUp(User user, List<String> lookupDataIds, String apiName, Set<String> lookUpFields);

    /**
     * 批量作废并删除 支持多种对象放在一起
     * @param user
     * @param delList
     */
    void batchInvalidAndDel(User user, Collection<IObjectData> delList);
    /**
     * 单一对象的批量作废和删除
     * @param objectDataList
     * @param user
     */
    void batchInvalidAndDelOfSingleApiName(List<IObjectData> objectDataList, User user);
    /**
     * 作废 数据
     */
    void invalidData(User user, SearchQuery.SearchQueryBuilder searchQueryBuilder, String apiName);

    /**
     * 删除作废的数据
     * @param apiName
     * @param ids
     * @param user
     */
    void delData(String apiName, List<String> ids, User user);
    void delData(List<IObjectData> delDatas,User user);

    /**
     * 恢复数据
     * @param apiName
     * @param ids
     * @param user
     */
    void bulkRecover(String apiName, List<String> ids, User user);

     IObjectData createBaseObjectData(String tenantId, String owner, String apiName);
}
