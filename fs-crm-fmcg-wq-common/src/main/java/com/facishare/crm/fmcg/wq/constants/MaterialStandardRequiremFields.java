package com.facishare.crm.fmcg.wq.constants;

public abstract class MaterialStandardRequiremFields {

	private MaterialStandardRequiremFields(){};

	public static final String DISPLAY_NAME = "物料陈列标准"; //ignoreI18n

	public static final String API_NAME = "MaterialStandardRequiremObj";

	 //描述
	public static final String STANDARD_DESCRIPTION = "standard_description";

	 //适用陈列形式
	public static final String DISPLAY_FORMAT = "display_format";

	 //是否启用
	public static final String STATE = "state";

		 //是
		public static final String STATE_Options_true = "true";

		 //否
		public static final String STATE_Options_false = "false";

	 //达标方式
	public static final String WAYS_ACHIEVE_STANDARD = "ways_achieve_standard";

		 //所有规则产品项目均达标
		public static final String WAYS_ACHIEVE_STANDARD_Options_1 = "1";

		 //满足一条规则即达标
		public static final String WAYS_ACHIEVE_STANDARD_Options_2 = "2";

		 //其他
		public static final String WAYS_ACHIEVE_STANDARD_Options_other = "other";

}
