package com.facishare.crm.fmcg.wq.constants;

public abstract class SuccessfulStoreRangeFields {

	private SuccessfulStoreRangeFields(){};

	public static final String DISPLAY_NAME = "铺货标准定义"; //ignoreI18n

	public static final String API_NAME = "SuccessfulStoreRangeObj";

	 //日期范围
	public static final String DATE_RANGE = "date_range";

	 //结束日期
	public static final String END_DATE = "end_date";











	 //必分销SKU数
	public static final String MUST_DISTRIBUTE_SKU = "must_distribute_sku";

	 //陈列形式
	public static final String DISPLAY_TYPE = "display_type";

	 //达标计算方式
	public static final String CALCULATION_RULES_ATTAINMENT = "calculation_rules_attainment";

		 //所有项目、产品、物料达标则达标
		public static final String CALCULATION_RULES_ATTAINMENT_Options_all_attainment = "all_attainment";
		// 所有已陈列项目、产品、物料达标则达标
		public static final String CALCULATION_RULES_ATTAINMENT_Options_all_display_attainment = "all_diplay";

		 //其他
		public static final String CALCULATION_RULES_ATTAINMENT_Options_other = "other";

	 //铺货规则说明
	public static final String RULE_DESCRIPTION = "rule_description";















	 //是否启用
	public static final String STATE = "state";

		 //yes
		public static final String STATE_Options_true = "true";

		 //no
		public static final String STATE_Options_false = "false";

	 //标准设置方式
	public static final String STANDARD_SETTING_METHOD = "standard_setting_method";

		 //店内项目标准
		public static final String STANDARD_SETTING_METHOD_Options_1 = "1";

		 //店内陈列形式及项目标准
		public static final String STANDARD_SETTING_METHOD_Options_2 = "2";

		 //其他
		public static final String STANDARD_SETTING_METHOD_Options_other = "other";





	 //状态
	public static final String DISPLAY_STATUS = "display_status";

		 //未生效
		public static final String DISPLAY_STATUS_Options_0 = "0";

		 //生效中
		public static final String DISPLAY_STATUS_Options_1 = "1";

		 //已到期
		public static final String DISPLAY_STATUS_Options_2 = "2";

		 //其他
		public static final String DISPLAY_STATUS_Options_other = "other";

	 //铺货标准示意图
	public static final String SKETCH_MAP = "sketch_map";

	 //永久有效
	public static final String VALIDITY_PERIOD_STANDARDS = "validity_period_standards";

		 //永久有效
		public static final String VALIDITY_PERIOD_STANDARDS_Options_1 = "1";

		 //固定有效期
		public static final String VALIDITY_PERIOD_STANDARDS_Options_2 = "2";

		 //其他
		public static final String VALIDITY_PERIOD_STANDARDS_Options_other = "other";

	 //开始日期
	public static final String START_DATE = "start_date";

	//铺货标准
	//distribution_standards__c
	public static final String RECORDETYPE_DISTRIBUTION_STANDARDS = "distribution_standards__c";
	//default__c 陈列标准
	public static final String RECORDETYPE_DISPLAY_STANDARDS = "default__c";

}
