package com.facishare.crm.fmcg.wq.constants;

public abstract class TPMActivityItemFields {

	private TPMActivityItemFields(){};

	public static final String DISPLAY_NAME = "活动项目管理"; //ignoreI18n

	public static final String API_NAME = "TPMActivityItemObj";

	 //计费模式
	public static final String CALCULATE_PATTERN = "calculate_pattern";

		 //单价模式
		public static final String CALCULATE_PATTERN_Options_1 = "1";

		 //总价模式
		public static final String CALCULATE_PATTERN_Options_2 = "2";

		 //其他
		public static final String CALCULATE_PATTERN_Options_other = "other";

	 //陈列项目
	public static final String DISPLAY_PROJECTS_ID = "display_projects_id";

	 //活动项目单位
	public static final String UNIT = "unit";

		 //米
		public static final String UNIT_Options_meter = "meter";

		 //箱
		public static final String UNIT_Options_box = "box";

		 //场
		public static final String UNIT_Options_chang = "chang";

		 //个
		public static final String UNIT_Options_ge = "ge";

		 //台
		public static final String UNIT_Options_tai = "tai";

		 //幅
		public static final String UNIT_Options_fu = "fu";

		 //其他
		public static final String UNIT_Options_other = "other";

	 //项目编号
	public static final String CODE = "code";

	 //数量标准
	public static final String AMOUNT_STANDARD = "amount_standard";

	 //是否启用
	public static final String IS_ACTIVATED = "is_activated";

		 //是
		public static final String IS_ACTIVATED_Options_true = "true";

		 //否
		public static final String IS_ACTIVATED_Options_false = "false";

	 //费用标准(元)
	public static final String COST_STANDARD = "cost_standard";

	 //数量校验
	public static final String AMOUNT_STANDARD_CHECK = "amount_standard_check";

		 //是
		public static final String AMOUNT_STANDARD_CHECK_Options_true = "true";

		 //否
		public static final String AMOUNT_STANDARD_CHECK_Options_false = "false";

	 //效果图
	public static final String STANDARD_DISPLAY_IMAGES = "standard_display_images";

	 //备注
	public static final String REMARK = "remark";

}
