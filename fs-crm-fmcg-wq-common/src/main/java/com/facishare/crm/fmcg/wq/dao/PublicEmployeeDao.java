package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-20 17:00
 **/
@Slf4j
@Component
public class PublicEmployeeDao extends AbstractDao {


    public IObjectData getByOutUserId(String tenantId, String outerUserId) {
           List<IObjectData> iObjectDataList = getAllIObjectDataListByQuery(User.systemUser(tenantId), SearchQuery.builder()
                .eq("outer_uid", outerUserId)
                .build(), "PublicEmployeeObj");
        if (iObjectDataList.size() > 1) {
            log.error("outerUserId:{} 查询到多个互联用户", outerUserId);
        }
        return iObjectDataList.isEmpty() ? null : iObjectDataList.get(0);
    }
}

