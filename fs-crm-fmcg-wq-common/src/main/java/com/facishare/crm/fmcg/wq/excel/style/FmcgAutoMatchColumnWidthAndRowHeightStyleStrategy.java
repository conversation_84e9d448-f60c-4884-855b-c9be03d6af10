package com.facishare.crm.fmcg.wq.excel.style;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/12/27/ 11:22
 **/
public class FmcgAutoMatchColumnWidthAndRowHeightStyleStrategy extends AbstractColumnWidthStyleStrategy {

    private static final int MAX_COLUMN_WIDTH = 70;
    private Short headRowHeight;
    private Short contentRowHeight;
    private Map<Integer, Map<Integer, Integer>> CACHE = new HashMap<Integer, Map<Integer, Integer>>(8);
    public FmcgAutoMatchColumnWidthAndRowHeightStyleStrategy(short headRowHeight, short contentRowHeight){
        this.headRowHeight = headRowHeight;
        this.contentRowHeight = contentRowHeight;
    }
    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, CellData cellData, Cell cell, Head head,
                                  int relativeRowIndex, boolean isHead) {
        if (!isHead && cellData == null) {
            return;
        }
        Map<Integer, Integer> maxColumnWidthMap = CACHE.computeIfAbsent(writeSheetHolder.getSheetNo(), k -> new HashMap<Integer, Integer>(16));
        int columnWidth = dataLength(cellData, cell, isHead) + 2;
        if (columnWidth < 0) {
            return;
        }
        if (columnWidth > MAX_COLUMN_WIDTH) {
            columnWidth = MAX_COLUMN_WIDTH;
        }
        Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
        if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
            maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
            writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
        }
        //设置行高
        setColumnHeight(isHead,cell.getRow(),cell);
    }

    private void setColumnHeight(boolean isHead, Row row, Cell cell) {
        if (isHead){
            setHeadColumnHeight(row);
        }else{
            setContentColumnHeight(row,cell);
        }
    }

    protected void setHeadColumnHeight(Row row) {
        if (headRowHeight != null && row.getHeightInPoints() < headRowHeight) {
            row.setHeightInPoints(headRowHeight);
        }
    }
    protected void setContentColumnHeight(Row row, Cell cell) {
        int rowHeightCount = 1;
        if (cell.getCellTypeEnum().equals(CellType.STRING)) {
            String cellStringCellValue = cell.getStringCellValue();
            String[] strings = cellStringCellValue.split("\n");
            rowHeightCount = strings.length;
        }
        if (rowHeightCount > 1){
            cell.setCellValue(new XSSFRichTextString(cell.getStringCellValue()) {
            });
        }
        int rowHeight = contentRowHeight * rowHeightCount;
        if (row.getHeightInPoints() < rowHeight) {
            row.setHeightInPoints(rowHeight);
        }
    }
    private Integer dataLength(CellData cellData, Cell cell, boolean isHead) {
        try {
            if (isHead) {
                return cell.getStringCellValue().getBytes("GBK").length;
            }
            switch (cellData.getType()) {
                case STRING:
                    String stringValue = cellData.getStringValue();
                    String[] strings = stringValue.split("\n");
                    int length = 0;
                    for (String str : strings) {
                        try {
                            length = Integer.max(str.getBytes("GBK").length,length);
                        } catch (UnsupportedEncodingException e) {
                            //e.printStackTrace();
                        }
                    }
                    return length;
                case BOOLEAN:
                    return cellData.getBooleanValue().toString().getBytes().length;
                case NUMBER:
                    return cellData.getNumberValue().toString().getBytes().length;
                default:
                    return -1;
            }
        } catch (UnsupportedEncodingException e) {
            return cellData.getStringValue().getBytes().length;
        }
    }
}
