package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface PriceBookObjConstants {
    String API_NAME = "PriceBookObj";
    String DISPLAY_NAME = "价目表"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field{
        //id
        isStandard("is_standard","是否标准价目表") ; //ignoreI18n

        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
}
