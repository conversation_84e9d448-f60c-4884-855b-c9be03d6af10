package com.facishare.crm.fmcg.wq.dao;

import com.facishare.crm.fmcg.wq.constants.PersonnelFields;
import com.facishare.crm.fmcg.wq.constants.PublicEmployeeFields;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.organization.paas.model.PaaSResult;
import com.facishare.organization.paas.model.permission.QueryUserRoleCodesByUsers;
import com.facishare.organization.paas.service.PaaSPermissionService;
import com.facishare.organization.paas.util.PaasArgumentUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-20 17:00
 **/
@Slf4j
@Component
public class EmployeeDao extends AbstractDao {
    @Autowired
    private PaaSPermissionService paaSPermissionService;

    /**
     * 通过用户ids 获取id名字
     */
    public Map<Integer, String> getUserNameByIds(String tenantId, Set<Integer> ids) {
        Map<Integer, String> idNameMap = Maps.newHashMap();
        if (null == ids || ids.isEmpty()) {
            return null;
        }
        if (ids.removeIf(o -> o == -10000)) {
            idNameMap.put(-10000, "系统"); // ignoreI18n
        }
        Map<Boolean, List<Integer>> idsMap = ids.stream()
                .collect(Collectors.groupingBy(id -> id.intValue() > 100000000));
        idsMap.forEach((isOuter, v) -> {
            if (isOuter) {
                List<IObjectData> outerEmployeeObjs = getOuterEmployeeObjs(tenantId, v.stream().map(Integer::valueOf).collect(Collectors.toList()),
                        Lists.newArrayList("outer_uid", "name"));
                // 获取外部员工
                idNameMap.putAll(outerEmployeeObjs.stream().collect(Collectors
                        .toMap(o -> Integer.valueOf(o.get("outer_uid").toString()), o -> o.get("name").toString())));
            } else {
                List<IObjectData> innerEmployeeObjs = getInnerEmployeeObjs(tenantId, v.stream().map(Integer::valueOf).collect(Collectors.toList()),
                        Lists.newArrayList("user_id", "name"));
                // 获取内部员工
                idNameMap.putAll(innerEmployeeObjs.stream().collect(Collectors
                        .toMap(o -> Integer.valueOf(o.get("user_id").toString()), o -> o.get("name").toString())));
            }
        });
        return idNameMap;
    }

    /**
     * 通过ids 取名字 String
     */
    public Map<String, String> getUserNameByStringIds(String tenantId, Collection<String> ids) {
        Map<String, String> idNameMap = Maps.newHashMap();
        if (null == ids || ids.isEmpty()) {
            return null;
        }
        if (ids.removeIf(o -> o == "-10000")) {
            idNameMap.put("-10000", "系统"); // ignoreI18n
        }
        Map<Boolean, List<String>> idsMap = ids.stream()
                .collect(Collectors.groupingBy(id -> Integer.valueOf(id) >100000000));
        idsMap.forEach((isOuter, v) -> {
            if (isOuter) {
                List<IObjectData> outerEmployeeObjs = getOuterEmployeeObjs(tenantId, v.stream().map(Integer::valueOf).collect(Collectors.toList()),
                        Lists.newArrayList("outer_uid", "name"));
                // 获取外部员工
                idNameMap.putAll(outerEmployeeObjs.stream().collect(Collectors
                        .toMap(o -> o.get("outer_uid").toString(), o -> o.get("name").toString())));
            } else {
                List<IObjectData> innerEmployeeObjs = getInnerEmployeeObjs(tenantId, v.stream().map(Integer::valueOf).collect(Collectors.toList()),
                        Lists.newArrayList("user_id", "name"));
                // 获取内部员工
                idNameMap.putAll(innerEmployeeObjs.stream().collect(Collectors
                        .toMap(o -> o.get("user_id").toString(), o -> o.get("name").toString())));
            }
        });
        //missed 补 E.tenantId.id
        ids.stream().filter(o -> !idNameMap.containsKey(o)).forEach(o -> idNameMap.put(o, "E." + tenantId + "." + o));
        return idNameMap;
    }


    private List<IObjectData> getInnerEmployeeObjs(String tenantId, List<Integer> outerUserIds, List<String> fields) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in("user_id", outerUserIds)
                .build(), "PersonnelObj", fields);
    }

    private List<IObjectData> getOuterEmployeeObjs(String tenantId, List<Integer> userIds, List<String> fields) {
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in("outer_uid", userIds)
                .build(), "PublicEmployeeObj", fields);
    }

    /**
     * 通过EmployeeDao获取内部员工信息
     * 
     * @param tenantId   租户ID
     * @param employeeId 员工ID
     * @return 内部员工对象
     */
    public IObjectData getInternalEmployeeById(String tenantId, String employeeId) {
        try {
            List<IObjectData> personnelObjs = getAllIObjectDataListByQueryWithFields(
                    User.systemUser(tenantId),
                    SearchQuery.builder()
                            .eq(PersonnelFields.USER_ID, employeeId)
                            .build(),
                    PersonnelFields.API_NAME,
                    Lists.newArrayList(PersonnelFields.MAIN_DEPARTMENT, PersonnelFields.VICE_DEPARTMENTS,
                            PersonnelFields.POSITION));
            return personnelObjs.isEmpty() ? null : personnelObjs.get(0);
        } catch (Exception e) {
            log.error("获取内部员工 {} 信息时发生异常", employeeId, e);
            return null;
        }
    }

    /**
     * 通过EmployeeDao获取外部员工信息
     * 
     * @param tenantId   租户ID
     * @param employeeId 外部员工ID
     * @return 外部员工对象
     */
    public IObjectData getExternalEmployeeById(String tenantId, String employeeId) {
        try {
            List<IObjectData> publicEmployeeObjs = getAllIObjectDataListByQueryWithFields(
                    User.systemUser(tenantId),
                    SearchQuery.builder()
                            .eq(PublicEmployeeFields.OUTER_UID, employeeId)
                            .build(),
                    PublicEmployeeFields.API_NAME,
                    Lists.newArrayList(PublicEmployeeFields.DEPARTMENT, PublicEmployeeFields.OUTER_TENANT_ID));
            return publicEmployeeObjs.isEmpty() ? null : publicEmployeeObjs.get(0);
        } catch (Exception e) {
            log.error("获取外部员工 {} 信息时发生异常", employeeId, e);
            return null;
        }
    }

    /**
     * 获取员工的所有部门信息
     * 
     * @param tenantId   租户ID
     * @param employeeId 员工ID
     * @param isExternal 是否为外部员工
     * @return 部门ID列表
     */
    public List<String> getEmployeeDepartments(String tenantId, String employeeId, boolean isExternal) {
        List<String> departmentIds = Lists.newArrayList();

        try {
            if (!isExternal) {
                // 内部员工：通过EmployeeDao查询PersonnelObj获取部门信息
                IObjectData personnelObj = getInternalEmployeeById(tenantId, employeeId);
                if (personnelObj != null) {
                    // 获取主属部门
                    Object mainDepartment = personnelObj.get(PersonnelFields.MAIN_DEPARTMENT);
                    if (mainDepartment instanceof List) {
                        List<?> mainDeptList = (List<?>) mainDepartment;
                        for (Object dept : mainDeptList) {
                            if (dept != null) {
                                departmentIds.add(dept.toString());
                            }
                        }
                    }

                    // 获取附属部门
                    Object viceDepartments = personnelObj.get(PersonnelFields.VICE_DEPARTMENTS);
                    if (viceDepartments instanceof List) {
                        List<?> viceDeptList = (List<?>) viceDepartments;
                        for (Object dept : viceDeptList) {
                            if (dept != null) {
                                departmentIds.add(dept.toString());
                            }
                        }
                    }
                }
            } else {
                // // 外部员工：通过EmployeeDao查询PublicEmployeeObj获取部门信息
                // IObjectData publicEmployeeObj = getExternalEmployeeById(tenantId,
                // employeeId);
                // if (publicEmployeeObj != null) {
                // // 获取部门信息
                // Object department = publicEmployeeObj.get(PublicEmployeeFields.DEPARTMENT);
                // if (department != null) {
                // departmentIds.add(department.toString());
                // }
                // }
            }
        } catch (Exception e) {
            log.error("获取员工 {} 的部门信息时发生异常", employeeId, e);
        }

        return departmentIds;
    }

    /**
     * 获取员工的角色信息（支持内部和外部员工）
     * 
     * @param tenantId   租户ID
     * @param employeeId 员工ID
     * @param isExternal 是否为外部员工
     * @return 角色ID列表
     */
    public List<String> getEmployeeRoles(String tenantId, String employeeId, String outTenantId, boolean isExternal) {
        List<String> roleIds = Lists.newArrayList();
        try {
            // 使用 paaSPermissionService.queryUserRoleCodesByUsers 查询用户角色
            List<String> userIds = Lists.newArrayList(employeeId);

            QueryUserRoleCodesByUsers.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(
                    QueryUserRoleCodesByUsers.Argument.class,
                    Integer.valueOf(tenantId),
                    -10000,
                    "CRM");
            argument.setUsers(userIds);

            PaaSResult<Map<String, List<String>>> result = paaSPermissionService.queryUserRoleCodesByUsers(argument);

            if (result != null && result.getResult() != null && !result.getResult().isEmpty()) {
                List<String> roleCodes = result.getResult().get(employeeId);
                if (roleCodes != null && !roleCodes.isEmpty()) {
                    roleIds.addAll(roleCodes);
                }
            }

            log.debug("员工 {} 的角色列表: {}", employeeId, roleIds);
        } catch (Exception e) {
            log.error("获取员工 {} 角色时发生异常", employeeId, e);
            throw new ValidateException("获取员工角色失败"); // ignoreI18n
        }
        return roleIds;
    }

    /**
     * 批量获取员工信息（包括内部和外部员工）
     *
     * @param tenantId    租户ID
     * @param employeeIds 员工ID列表（字符串格式）
     * @return 员工对象列表
     */
    public List<IObjectData> getbyIds(String tenantId, List<String> employeeIds) {
        if (employeeIds == null || employeeIds.isEmpty()) {
            return Lists.newArrayList();
        }

        List<IObjectData> result = Lists.newArrayList();

        // 分离内部员工和外部员工ID
        List<Integer> internalEmployeeIds = Lists.newArrayList();
        List<Integer> externalEmployeeIds = Lists.newArrayList();

        for (String employeeId : employeeIds) {
            try {
                Integer id = Integer.valueOf(employeeId);
                if (id > 100000000) {
                    externalEmployeeIds.add(id);
                } else {
                    internalEmployeeIds.add(id);
                }
            } catch (NumberFormatException e) {
                log.warn("无效的员工ID格式: {}", employeeId);
            }
        }

        // 获取内部员工信息
        if (!internalEmployeeIds.isEmpty()) {
            List<IObjectData> internalEmployees = getInnerEmployeeObjs(tenantId, internalEmployeeIds,
                    Lists.newArrayList("user_id", "name", "main_department", "vice_departments"));
            result.addAll(internalEmployees);
        }

        // 获取外部员工信息
        if (!externalEmployeeIds.isEmpty()) {
            List<IObjectData> externalEmployees = getOuterEmployeeObjs(tenantId, externalEmployeeIds,
                    Lists.newArrayList("outer_uid", "name", "department", "outer_tenant_id"));
            result.addAll(externalEmployees);
        }

        return result;
    }

    public boolean isExternalEmployee(String employeeId) {
        try {
            Integer id = Integer.valueOf(employeeId);
            return id > 100000000;
        } catch (NumberFormatException e) {
            log.warn("无效的员工ID格式: {}", employeeId);
            return false;
        }
    }
}
