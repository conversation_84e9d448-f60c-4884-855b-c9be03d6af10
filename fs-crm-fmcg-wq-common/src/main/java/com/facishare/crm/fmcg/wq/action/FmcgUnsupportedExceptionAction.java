package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.exception.CheckinsErrorCode;
import com.facishare.crm.fmcg.wq.exception.CheckinsException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2024-11-01 11:55
 **/
public class FmcgUnsupportedExceptionAction extends StandardAddAction {
    @Override
    protected void before(Arg arg) {
        throw new CheckinsException(CheckinsErrorCode.UNSUPPORTED_OPERATION.getMessage(), CheckinsErrorCode.UNSUPPORTED_OPERATION);
    }
}
