package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-06-20 14:25
 **/
public interface PromoterFields {
    String API_NAME = "PromoterObj";
    String PROFILE_IMAGE = "profile_image";

    String IN_DATE = "in_date";

    String ENTERPRISE_RELATION_ID = "enterprise_relation_id";

    String PROMOTER_TYPE = "promoter_type";

    String ACTIVITY_ID = "activity_id";

    String MOBILE = "mobile";
    /**
     *        //离职
     *         resign("0"),
     *         //在职
     *         normal("1"),
     *         //未入职
     *         noEntry("2");
     */
    String IO_STATUS = "io_status";
    /**
     * 促销员审核状态
     //已通过
     agree("true"),
     //待审核
     wait("false"),
     //未通过
     disagree("disagree");
     */
    String REVIEW_STATUS = "review_status";

    String PUBLIC_EMPLOYEE_ID = "public_employee_id";

    String ACTIVITY_STATUS = "activity_status";

    /**
     * 负责的门店
     */
    String RESPONSIBLE_ACCOUNTS = "responsible_accounts";


    @AllArgsConstructor
    @Getter
    enum ReviewStatus {
        //已通过
        agree("true"),
        //待审核
        wait("false"),
        //未通过
        disagree("disagree");
        private final String value;
        @Override
        public String toString(){
            return this.value;
//            throw new RuntimeException("com.facishare.crm.fmcg.wq.constants.PromoterFields.ReviewStatus.toString not support");
        }
    }

}

