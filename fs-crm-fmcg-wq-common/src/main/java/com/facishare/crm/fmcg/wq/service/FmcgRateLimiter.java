package com.facishare.crm.fmcg.wq.service;

import com.github.autoconf.ConfigFactory;
import com.google.common.util.concurrent.RateLimiter;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 限流
 * @author: zhangsm
 * @create: 2024-11-25 19:28
 **/
@Component
@Slf4j
@Data
public class FmcgRateLimiter {
    private RateLimiter podTriggerWorkFlowLimiter;
    @PostConstruct
    void init() {
        ConfigFactory.getInstance().getConfig("checkin-custom-config",
                config -> {
                    try {
                        podTriggerWorkFlowLimiter = RateLimiter.create(config.getDouble("podTriggerWorkFlowLimiter", 10));
                    } catch (Exception e) {
                        log.warn("cannot reload {} and exception: {}", config.getName(), e);
                    }
                }
        );
    }



}
