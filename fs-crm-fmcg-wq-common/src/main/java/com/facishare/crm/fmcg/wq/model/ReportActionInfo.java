package com.facishare.crm.fmcg.wq.model;

import com.facishare.appserver.checkins.api.model.BaseArgs;
import com.facishare.appserver.checkins.api.model.BaseResult;
import com.facishare.appserver.checkins.model.common.JumpActionButton;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/21/ 14:57
 **/
public interface ReportActionInfo {
    @Data
    class Arg extends BaseArgs {
        String tenantId;

        private String actionId;
        private String sourceActionId;
        private String checkinId;
    }
    @Data
    class Result extends BaseResult {
        private String reportApiName;
        private String reportDataId;
        private String actionId;
        // 配置的自定义页面的Id
        private String appTemplateId;
        private String sourceActionId;
        private List<JumpActionButton> showButton;
        private int btnDisplay;
    }
}
