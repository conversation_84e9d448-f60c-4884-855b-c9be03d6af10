package com.facishare.crm.fmcg.wq.constants;

public abstract class EmployeeFixedSalaryFields {

	private EmployeeFixedSalaryFields(){};

	public static final String DISPLAY_NAME = "员工固定工资表"; //ignoreI18n

	public static final String API_NAME = "EmployeeFixedSalaryObj";

	 //互联企业
	public static final String CONNECTED_COMPANY = "connected_company";

	 //工资规则
	public static final String SALARY_RULE = "salary_rule";
	public static final String SALARY_RULE_NAME = "salary_rule__r";

	 //手机号(外部)
	public static final String MOBILE_NUMBER_EXTERNAL = "mobile_number_external";

	 //员工(外部)
	public static final String EMPLOYEE_EXTERNAL = "employee_external";
	public static final String EMPLOYEE_EXTERNAL_NAME = "employee_external__r";

	 //手机号(内部)
	public static final String PHONE_NUMBER = "phone_number";

	 //员工(内部)
	public static final String EMPLOYEE = "employee";
	public static final String EMPLOYEE_NAME = "employee__r";

	 //主属部门(内部)
	public static final String MAIN_DEPARTMENT = "main_department";

	 //互联角色
	public static final String CONNECTED_ROLE = "connected_role";

	//虚拟字段：互联角色显示
	public static final String CONNECTED_ROLE_DISPLAY = "connected_role_display";

	 //定薪方式
	public static final String SALARY_METHOD = "salary_method";

		 //日薪
		public static final String SALARY_METHOD_Options_1 = "1";

		 //周薪
		public static final String SALARY_METHOD_Options_2 = "2";

		 //月薪
		public static final String SALARY_METHOD_Options_3 = "3";

    //员工类型 内部人员 default__c 外部人员 external__c
    public static final String RECORDTYPE_INTERNAL = "default__c";

    public static final String RECORDTYPE_EXTERNAL = "record_external__c";
}