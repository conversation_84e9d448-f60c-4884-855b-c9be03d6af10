package com.facishare.crm.fmcg.wq.constants;

public abstract class ProductItemStandardDetailFields {
	public static final String MAST_ID = "product_item";
	private ProductItemStandardDetailFields(){};

	public static final String DISPLAY_NAME = "产品项目标准"; //ignoreI18n

	public static final String API_NAME = "ProductItemStandardDetailObj";

	 //产品分类
	public static final String PRODUCT_CATEGORIZATION = "product_categorization";
	public static final String PRODUCT_CATEGORIZATION__R = "product_categorization__r";
	 //单位
	public static final String UNIT = "unit";

	 //陈列项目
	public static final String DISPLAY_PROJECT = "display_project";

	 //关联产品陈列标准
	public static final String PRODUCT_ITEM = "product_item";

	 //描述
	public static final String STANDARD_DESCRIPTION = "standard_description";

	 //最低标准值
	public static final String PROJECT_STANDARDS = "project_standards";

	 //产品名称
	public static final String PRODUCT_NAME = "product_name";
	public static final String PRODUCT_NAME__R = "product_name__r";
	//达成子条件s
	public static final String SUB_CONDITIONS = "sub_conditions";



}
