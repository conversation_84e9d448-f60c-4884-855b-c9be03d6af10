package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.model.FmcgPreActionArgs;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.appframework.core.predef.action.BaseObjectApprovalAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.facishare.paas.I18N;
import org.checkerframework.checker.units.qual.A;

import java.util.List;
import java.util.Map;

/**
 * fmcg 预置 按钮action
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-10-18 18:26
 **/
public abstract class FmcgAbstractStandardAction<A extends FmcgPreActionArgs, R> extends BaseObjectApprovalAction<A, R> {
    /**
     * db数据 优先查数据copy
     */
    IObjectData dbData;
    /**
     * 主数据 可能是变更的数据
     */
    IObjectData objectData;

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getOldObjectData() {
        return dbData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(getObjectAction().getActionCode());
    }

    @Override
    protected void before(A arg) {
        super.before(arg);
        if (CollectionUtils.empty(dataList)) {
            throw new ValidateException("data not find");//ignoreI18n
        }
        dbData = ObjectDataExt.of(dataList.get(0)).copy();
        objectData = ObjectDataExt.of(dataList.get(0)).copy();
    }

    @Override
    protected List<String> getDataPrivilegeIds(A arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected String getButtonApiName() {
        return getObjectAction().getButtonApiName();
    }
    protected abstract ObjectAction getObjectAction();
    /**
     * 修改记录
     */
    protected void recodeLog() {
        //主对象修改记录
        Map<String, Object> updateFields = ObjectDataExt.of(dbData).diff(objectData, objectDescribe);
        if (CollectionUtils.notEmpty(updateFields)) {
            serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, this.objectDescribe, objectData, updateFields, dbData);
        }
    }


}
