package com.facishare.crm.fmcg.wq.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.crm.fmcg.wq.constants.UserScheduleDetailFields;
import com.facishare.crm.fmcg.wq.constants.UserScheduleFields;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.Data;
import lombok.SneakyThrows;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 配置文件生成
 * @author: zhangsm
 * @create: 2023-12-18 17:44
 **/
public class ConfigUtils {

    public static void main(String[] args) {
        printFieldConfig();
    }

    /**
     * fs-paas-filter-config
     * 配置对象支持的操作 ui事件
     */

    static final List<String> skipKeys = Lists.newArrayList("API_NAME", "ORIGIN_SOURCE");

    /**
     * fs-paas-config-fmcg
     * 配置字段支持的操作
     */

    @SneakyThrows
    static void printFieldConfig() {
        List<Class> configClasses = Lists.newArrayList(AccountShiftDetailFields.class);
        // API_NAME ORIGIN_SOURCE key跳过
        JSONObject result = new JSONObject();
        for (Class configClass : configClasses) {
            //jsonobj
            JSONObject jsonObject = new JSONObject();

            Field[] fields = configClass.getDeclaredFields();
            for (Field field : fields) {
                // = API_NAME 加到jsonobj里
                if (field.getName().equals("API_NAME")) {
                    result.put(field.get(null).toString(), jsonObject);
                }
                if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) && !skipKeys.contains(field.getName())) {
                    String key = field.getName();
                    Object value = field.get(null);
                    jsonObject.put(value.toString(), JSONArray.parse("[{\"tenants\":[],\"data\":{\"add\":1,\"edit\":1,\"enable\":1,\"display\":1,\"remove\":1,\"attrs\":{\"is_required\":1,\"wheres\":1,\"label\":1,\"target_related_list_label\":1,\"related_wheres\":1}},\"group\":\"default\"}]"));
                }
            }
        }
        System.out.println(result.toJSONString());
    }

   @SneakyThrows
   public static List<String> getFields(Class clazz)  {
        List<String> result = new ArrayList<>();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) && !skipKeys.contains(field.getName())) {
                result.add(field.get(null).toString());
            }
        }
        return result;
    }
}
