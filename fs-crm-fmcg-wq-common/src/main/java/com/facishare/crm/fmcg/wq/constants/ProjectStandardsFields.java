package com.facishare.crm.fmcg.wq.constants;

public abstract class ProjectStandardsFields {

	private ProjectStandardsFields(){};

	public static final String DISPLAY_NAME = "铺货项目标准"; //ignoreI18n

	public static final String API_NAME = "ProjectStandardsObj";

	 //最低标准
	public static final String QUANTITY = "quantity";

	 //标准说明
	public static final String EXPLANATION_PROJECT = "explanation_project";

	 //设置类型
	public static final String SETTING_TYPE = "setting_type";

		 //整体陈列标准
		public static final String SETTING_TYPE_Options_0 = "0";

		 //产品陈列标准
		public static final String SETTING_TYPE_Options_1 = "1";

		 //物料陈列标准
		public static final String SETTING_TYPE_Options_2 = "2";

		 //其他
		public static final String SETTING_TYPE_Options_other = "other";

	 //关联陈列形式
	public static final String DISPLAY_TYPE_STANDARD = "display_type_standard";

	 //项目类型
	public static final String TYPE = "type";

	 //产品陈列标准
	public static final String PRODUCT_DISPLAY_STANDARDS = "product_display_standards";

	 //层级
	public static final String DISPLAY_LEVEL = "display_level";

		 //所有层
		public static final String DISPLAY_LEVEL_Options_0 = "0";

		 //第1层
		public static final String DISPLAY_LEVEL_Options_1 = "1";

		 //第2层
		public static final String DISPLAY_LEVEL_Options_2 = "2";

		 //第3层
		public static final String DISPLAY_LEVEL_Options_3 = "3";

		 //第4层
		public static final String DISPLAY_LEVEL_Options_4 = "4";

		 //第5层
		public static final String DISPLAY_LEVEL_Options_5 = "5";

		 //第6层
		public static final String DISPLAY_LEVEL_Options_6 = "6";

		 //第7层
		public static final String DISPLAY_LEVEL_Options_7 = "7";

		 //其他
		public static final String DISPLAY_LEVEL_Options_other = "other";

	 //项目名称
	public static final String TPM_PROJECT = "tpm_project";

	 //规则名称
	public static final String DISPLAY_RULE_NAME = "display_rule_name";

	 //陈列项目
	public static final String DISPLAY_PROJECT = "display_project";

	 //陈列形式
	public static final String DISPLAY_FORMAT = "display_format";

	 //成功门店项目标准
	public static final String SUCCESSFUL_STORE = "successful_store";

	 //物料陈列标准
	public static final String MATERIAL_DISPLAY_STANDARDS = "material_display_standards";

}
