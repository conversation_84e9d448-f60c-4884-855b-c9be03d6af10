package com.facishare.crm.fmcg.wq.service;

import com.facishare.crm.fmcg.wq.model.RestResult;
import lombok.Data;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: fs-appserver-checkins-office-v2
 * @description:
 * @author: zhangsm
 * @create: 2021-06-22 11:46
 **/
@ControllerAdvice
public class ExceptionHandlerController {


    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public RestResult<List<ErrorMsg>> exception(MethodArgumentNotValidException e) {
        RestResult result = new RestResult();
        BindingResult bindingResult = e.getBindingResult();
        List<ObjectError> allErrors = bindingResult.getAllErrors();
        List<ErrorMsg> errorMsgs = new ArrayList<>();

        allErrors.forEach(objectError -> {
            ErrorMsg errorMsg = new ErrorMsg();
            FieldError fieldError = (FieldError)objectError;
            errorMsg.setField(fieldError.getField());
            errorMsg.setObjectName(fieldError.getObjectName());
            errorMsg.setMessage(fieldError.getDefaultMessage());
            errorMsgs.add(errorMsg);
        });
        result.setCode(-1);
        result.setData(errorMsgs);
        result.setMessage(errorMsgs.stream().map(o->o.getMessage()).collect(Collectors.joining(",")));
        return result;
    }
    @Data
    static class ErrorMsg{
        String field;
        String objectName;
        String message;
    }
}
