package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 经营产品明细
 * @author: zhangsm
 * @create: 2021-04-25 21:52
 **/
public interface CoveredProductObjConstants {
    String API_NAME = "CoveredProductObj";
    String DISPLAY_NAME = "经营产品明细"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field{
        productId("relation_product","产品名称"), //ignoreI18n
        supplyType("field_Y29k1__c","产品分类"), //ignoreI18n
        businessScopeId("belong_product_group","经营范围"), //ignoreI18n
        storeCount("field_x7Qj1__c","产品条形码"), //ignoreI18n
        belongGroup("belong_product_group","产品分组"); //ignoreI18n

        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
}
