package com.facishare.crm.fmcg.wq.util;

import com.facishare.appserver.utils.AccountUtils;
import com.facishare.appserver.utils.LogUtils;
import com.facishare.paas.appframework.core.model.ActionContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class LogMapUtils {
    public static void sendPointLog(ActionContext context, Map<String,Object> data) {
        Map<String, Object> maps = new HashMap();
        maps.put("source", context.getRequestSource());
        if(Objects.nonNull(context.getEa())) {
            maps.put("ea", context.getEa());
            maps.put("user", AccountUtils.getUserAccount(context.getEa(), context.getUser().getUserIdInt()));
        }
        maps.put("userId", context.getUser().getUserIdInt());
        maps.put("postId", context.getPostId());
        maps.put("clientVersion", context.getClientInfo());
        maps.put("code", 0);
        maps.put("method", "commonMethod");
        maps.put("_dataVersion", "1.2");
        maps.putAll(data);
        LogUtils.sendPointLog(maps);
    }

}
