package com.facishare.crm.fmcg.wq.constants;

public abstract class TPMActivityAgreementDetailFields {

	private TPMActivityAgreementDetailFields(){};

	public static final String DISPLAY_NAME = "活动协议项目"; //ignoreI18n

	public static final String API_NAME = "TPMActivityAgreementDetailObj";

	 //计费方式
	public static final String CALCULATE_PATTERN = "calculate_pattern";

	 //兑付方式
	public static final String PAYMENT_MODE = "payment_mode";

	 //编号
	public static final String CODE = "code";

	 //数量标准
	public static final String AGREEMENT_AMOUNT_STANDARD = "agreement_amount_standard";

	 //兑付产品数量
	public static final String PAYMENT_PRODUCT_AMOUNT = "payment_product_amount";

	 //物料规则
	public static final String MATERIAL_STANDARD_REQUIREM_ID = "material_standard_requirem_id";

	 //产品要求说明
	public static final String PRODUCT_STANDARD_DESCRIPTION = "product_standard_description";

	 //效果图
	public static final String STANDARD_DISPLAY_IMAGES = "standard_display_images";

	 //费用项目
	public static final String ACTIVITY_ITEM_ID = "activity_item_id";

	 //是否上报项目数量
	public static final String IS_REPORT_ITEM_QUANTITY = "is_report_item_quantity";

	 //项目类型
	public static final String TYPE = "type";

	 //陈列形式
	public static final String DISPLAY_FORM_ID = "display_form_id";

	 //产品规则
	public static final String PRODUCT_ITEM_STANDARD_ID = "product_item_standard_id";

	 //费用标准(元)
	public static final String AGREEMENT_COST_STANDARD = "agreement_cost_standard";

	 //兑付产品
	public static final String PAYMENT_PRODUCT = "payment_product";

	 //单项总费用(元)
	public static final String SUBTOTAL = "subtotal";

	 //数量校验
	public static final String AMOUNT_STANDARD_CHECK = "amount_standard_check";

	 //活动协议
	public static final String ACTIVITY_AGREEMENT_ID = "activity_agreement_id";

	 //物料要求说明
	public static final String MATERIAL_STANDARD_DESCRIPTION = "material_standard_description";

	 //活动项目
	public static final String ACTIVITY_DETAIL_ID = "activity_detail_id";

	 //兑付产品单位
	public static final String PAYMENT_PRODUCT_UNIT = "payment_product_unit";

}
