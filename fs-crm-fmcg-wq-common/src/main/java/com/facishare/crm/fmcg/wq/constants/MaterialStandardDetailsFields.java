package com.facishare.crm.fmcg.wq.constants;

public abstract class MaterialStandardDetailsFields {
	public static final String MASTER_ID = "material_standard";

	private MaterialStandardDetailsFields(){};

	public static final String DISPLAY_NAME = "物料项目标准"; //ignoreI18n

	public static final String API_NAME = "MaterialStandardDetailsObj";

	 //物料分类
	public static final String MATERIAL_CATEGORY = "material_category";
    public static final String MATERIAL_CATEGORY__R = "material_category__r";

		 //示例选项
		public static final String MATERIAL_CATEGORY_Options_option1 = "option1";

		 //其他
		public static final String MATERIAL_CATEGORY_Options_other = "other";

	 //单位
	public static final String UNIT = "unit";

	 //项目名称
	public static final String DISPLAY_PROJECT = "display_project";

	 //关联物料标准
	public static final String MATERIAL_STANDARD = "material_standard";

	 //描述
	public static final String STANDARD_DESCRIPTION = "standard_description";

	 //物料名称
	public static final String MATERIAL_NAME__R = "material_name__r";

	public static final String MATERIAL_NAMES = "material_name";

	 //最低标准值
	public static final String MATERIAL_QUANTITY = "material_quantity";

}
