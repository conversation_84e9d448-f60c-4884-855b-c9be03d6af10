package com.facishare.crm.fmcg.wq.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.formula.functions.T;

import java.io.Serializable;
import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2025-03-19 15:33
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RestResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private int code;
    private String message;
    private List<String> errors;
    private T data;

    private String uuId;

    private Object extData;
}
