package com.facishare.crm.fmcg.wq.dao;
import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.checkins.model.enums.CustomGray;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.service.FmcgRateLimiter;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ControllerContext;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.BaseListController;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataBatchIncrementUpdate;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.AsyncTaskExecutor;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-19 14:58
 **/
@Slf4j
public abstract class AbstractDao implements BaseDaoInterface {

    @Resource
    protected PaasDataProxy paasDataProxy;
    @Autowired
    protected InfraServiceFacade infraServiceFacade;
    @Autowired
    ServiceFacade serviceFacade;
    @Resource
    AsyncTaskExecutor fmcgSyncTaskExecutor;
    @Resource
    AsyncTaskExecutor fmcgThreadPoolExecutor;
    @Autowired
    private ObjectDataServiceImpl objectDataService;
    @Autowired
    private FmcgRateLimiter fmcgRateLimiter;
    @Override
    public void batchInvalidAndDel(User user, Collection<IObjectData> delList) {
        delList.parallelStream().collect(Collectors.groupingBy(o -> o.getDescribeApiName())).forEach((k, v) -> {
            try {
                batchInvalidAndDelOfSingleApiName(v, user);
            } catch (Exception e) {
                log.info("batchDel error tenantId {},delList apiName {} size {}", user, k, v.size());
            }
        });

    }

    /**
     * 分組執行的邏輯
     *
     * @param list
     * @param action
     */
    private void partListAction(List<IObjectData> list, Consumer<List<IObjectData>> action) {
        if (CollectionUtils.isNotEmpty(list)) {
            int size = ConfigFactory.getConfig("CheckInService").getInt("partListSize", 1000);
            if (list.size() > size) {
                action.accept(list);
            } else {

                Lists.partition(list, size).stream().forEach(tList -> {
                    action.accept(tList);
                });
            }
        }
    }
    @Override
    public IObjectData createBaseObjectData(String tenantId, String owner, String apiName) {
        IObjectData iObjectData = new ObjectData();
        iObjectData.setDescribeApiName(apiName);
        iObjectData.setTenantId(tenantId);
        iObjectData.setOrderBy(-10000);
        iObjectData.setRecordType("default__c");
        if (StringUtils.isNotBlank(owner)){
            if (Long.valueOf(owner) > 100000000L){
                iObjectData.setOutOwner(Lists.newArrayList(owner));
                iObjectData.setOwner(Lists.newArrayList("-10000"));
            }else{
                iObjectData.setOwner(Lists.newArrayList(owner));
            }
        }else {
            iObjectData.setOwner(Lists.newArrayList("-10000"));
        }
        iObjectData.setId(new ObjectId().toString());
        return iObjectData;
    }
    @Override
    public List<IObjectData> batchSave(User user, Collection<IObjectData> addList) {
        List<IObjectData> result = new ArrayList<>();
        addList.parallelStream().collect(Collectors.groupingBy(o -> o.getDescribeApiName())).forEach((k, v) -> {
            result.addAll(batchSaveOfSingleApiName(v, user));
        });
        return result;
    }

    /**
     * 分组批量保存
     *
     * @param objectDataList
     * @param user
     */
    @Override
    public List<IObjectData> batchSaveOfSingleApiName(List<IObjectData> objectDataList, User user) {
        List<IObjectData> result = Lists.newArrayList();
        partListAction(objectDataList, list -> result.addAll(serviceFacade.bulkSaveObjectData(list, user)));
        //工作流消息
        ParallelUtils.createBackgroundTask().submit(() -> {
            for (IObjectData objectData : result) {
                fmcgRateLimiter.getPodTriggerWorkFlowLimiter().acquire();
                infraServiceFacade.startWorkFlow(objectData.getId(), objectData.getDescribeApiName(), WorkflowProducer.TRIGGER_START, user, null, true);
            }
        }).run();
        return result;
    }

    /**
     * 删除 查找关联的相关数据
     *
     * @param tenantId
     * @param lookupDataIds 需要删除的查找关联数据id
     * @param apiName       要删除的对象 apiname
     * @param lookUpFields  要删除的对象打的查找关联字段
     */
    @Override
    public void delLookUp(User user, List<String> lookupDataIds, String apiName, Set<String> lookUpFields) {
        if (CollectionUtils.isEmpty(lookUpFields)) {
            return;
        }
        List<SearchQuery.SearchQueryBuilder> searchQueryList = lookUpFields.stream().map(o -> SearchQuery.builder().in(o, lookupDataIds)).collect(Collectors.toList());
        SearchQuery searchQuery = null;
        if (searchQueryList != null) {
            if (searchQueryList.size() > 1) {
                SearchQuery.SearchQueryBuilder temp = null;
                for (int i = 0; i < searchQueryList.size(); i++) {
                    if (i == 0) {
                        temp = searchQueryList.get(i);
                    } else {
                        temp.addOrWheres(searchQueryList.get(i));
                    }

                }
                searchQuery = temp.build();
            } else {
                searchQuery = searchQueryList.get(0).build();
            }
        }
        if (searchQuery == null) {
            return;
        }
        //清空 走es
        searchQuery.getSearchTemplateQuery().setSearchSource(null);
        List<IObjectData> data = getAllIObjectDataListByQuery(user, searchQuery, apiName);
        if (CollectionUtils.isNotEmpty(data)) {
            batchInvalidAndDelOfSingleApiName(data, user);
        }
    }



    /**
     * 排序 都是id 倒叙
     *
     * @param tenantId
     * @param searchQuery
     * @param apiName
     * @param fields
     * @return
     */
    @Override
    public QueryResult<IObjectData> getAllQueryDataListByQueryWithFields(User user, SearchQuery searchQuery, String apiName, List<String> fields) {
        int limit = ConfigFactory.getConfig("CheckInService").getInt("fmcgLimit", 200);
        // 最一次并发查询的次数。超过这个次数，开始新的并发，且条件拼上id
        int maxParallelCount = ConfigFactory.getConfig("CheckInService").getInt("maxParallelCount", 50);
        if (CollectionUtils.isNotEmpty(fields)) {
            List<String> showFields = Lists.newArrayList();
            showFields.addAll(fields);
            showFields.add(BaseField.describeApiName.getApiName());
            showFields.add(BaseField.tenantId.getApiName());
            showFields.add(BaseField.id.getApiName());
            showFields.add(BaseField.name.getApiName());
            showFields.add(BaseField.lifeStatus.getApiName());
            showFields.add(BaseField.recordType.getApiName());
            showFields.add(BaseField.owner.getApiName());
            showFields.add(BaseField.createTime.getApiName());
            showFields.add(BaseField.lastModifiedTime.getApiName());
            fields = showFields.stream().distinct().collect(Collectors.toList());
        }
        List<String> finalFields = fields;
        searchQuery.getSearchTemplateQuery().setLimit(limit);
        //查一次 如果后续还有 再多线程调用
        QueryResult<IObjectData> queryResult = getData(user, searchQuery, apiName, fields, limit, 0);
        if (queryResult.getTotalNumber() == null || queryResult.getTotalNumber() == queryResult.getData().size()) {
            return queryResult;
        }
        //返回的list
        QueryResult<IObjectData> result = new QueryResult<>();
        result.setData(ListUtils.synchronizedList(Lists.newArrayList()));
        result.getData().addAll(queryResult.getData());
        result.setTotalNumber(getTotal(user, searchQuery, apiName));

        List<Integer> offsets = Lists.newArrayList();
        int times = result.getTotalNumber() / limit;
        if (CustomGray.EA.fmcgOldQueryGray.gray("ss")) {
            //从1开始跳过0 0 已经查完了
            for (int i = 1; i <= times; i++) {
                offsets.add(i * limit);
            }
            //批量查询 复用线程池
            baseParallel(offsets, offset -> {
                QueryResult<IObjectData> tempResult = getDataIgnoreAll(user, searchQuery, apiName, finalFields, limit, offset);
                if (CollectionUtils.isNotEmpty(tempResult.getData())) {
                    result.getData().addAll(ListUtils.synchronizedList(tempResult.getData()));
                }
                log.debug("parallelGetDatasByQueryWithFields tenantId {} apiName {} limit {}", user.getTenantId(), apiName, limit);
            });
        } else {
            //再次指定排序
            ArrayList<OrderBy> orders = Lists.newArrayList();
            OrderBy orderBy = new OrderBy();
            orderBy.setFieldName("_id");
            orderBy.setIsAsc(false);
            orders.add(orderBy);
            searchQuery.getSearchTemplateQuery().resetOrder(orders);
            //批量查询 复用线程池
            Consumer<Integer> queryConsumer = offset -> {
                QueryResult<IObjectData> tempResult = getDataIgnoreAll(user, searchQuery, apiName, finalFields, limit, offset);
                if (CollectionUtils.isNotEmpty(tempResult.getData())) {
                    result.getData().addAll(ListUtils.synchronizedList(tempResult.getData()));
                }
                log.debug("parallelGetDatasByQueryWithFields tenantId {} apiName {} limit {}", user.getTenantId(), apiName, limit);
            };
            //从1开始跳过0 0 已经查完了
            for (int i = 1; i <= times; i++) {
                if (i % maxParallelCount == 0) {
                    //查 以前的
                    baseParallel(offsets, queryConsumer);
                    //倒序
                    result.getData().sort(Comparator.comparing(IObjectData::getId).reversed());
                    searchQuery.getSearchTemplateQuery().getFilters().removeIf(o -> o.getFieldName().equals("_id") && o.getOperator().equals(Operator.LT));
                    searchQuery.getSearchTemplateQuery().addFilters(Lists.newArrayList(SearchQuery.filter("_id", Operator.LT, result.getData().get(result.getData().size() - 1).getId())));
                    offsets.clear();
                }
                offsets.add((i % maxParallelCount) * limit);
            }
            if (CollectionUtils.isNotEmpty(offsets)) {
                baseParallel(offsets, queryConsumer);
            }
        }
        return result;
    }

    /**
     * 分页查询  重写了下  返回total
     *
     * @param tenantId
     * @param searchQuery
     * @param apiName
     * @param fields
     * @param limit
     * @param offset
     * @return
     */
    private QueryResult<IObjectData> getData(User user, SearchQuery searchQuery, String apiName, List<String> fields, int limit, int offset) {
        SearchQuery tempSearchQuery = SearchQuery.builder().copyOf(searchQuery).build();
        tempSearchQuery.getSearchTemplateQuery().setOffset(offset);
        tempSearchQuery.getSearchTemplateQuery().setLimit(limit);
        ActionContextExt context = ActionContextExt.of(user);
        context.setDoCalculate(false);
        context.disableDeepQuote().skipRelevantTeam();
        tempSearchQuery.getSearchTemplateQuery().setPermissionType(0);
        tempSearchQuery.getSearchTemplateQuery().setNeedReturnCountNum(true);
        tempSearchQuery.getSearchTemplateQuery().setNeedReturnQuote(false);
        if (CollectionUtils.isEmpty(fields)) {
            return serviceFacade.findBySearchQuery(context.getContext(), apiName, tempSearchQuery.getSearchTemplateQuery());
        } else {
            return serviceFacade.findBySearchTemplateQueryWithFields(context.getContext(), apiName, tempSearchQuery.getSearchTemplateQuery(), fields);
        }
    }

    /**
     * 获取 total
     *
     * @param tenantId
     * @param searchQuery
     * @param apiName
     * @return
     */
    public Integer getTotal(User user, SearchQuery searchQuery, String apiName) {
        SearchQuery tempSearchQuery = SearchQuery.builder().copyOf(searchQuery).build();
        tempSearchQuery.getSearchTemplateQuery().setFindExplicitTotalNum(Boolean.TRUE);
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, apiName, tempSearchQuery.getSearchTemplateQuery());
        return queryResult.getTotalNumber();
    }

    /**
     * 并发查询的逻辑
     *
     * @param datas
     * @param action
     * @param <T>
     */
    private <T> void baseParallel(List<T> datas, Consumer<? super T> action) {
        CompletableFuture.allOf(datas.stream().map(o -> CompletableFuture.supplyAsync(() -> {
                    ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
                    if (contextClassLoader == null) {
                        log.info("contextClassLoader is null {}", Thread.currentThread().getStackTrace());
                        Thread.currentThread().setContextClassLoader(ClassLoader.getSystemClassLoader());
                    }
                    action.accept(o);
                    return Boolean.TRUE;
                }, fmcgSyncTaskExecutor)

        ).toArray(CompletableFuture[]::new)).join();
    }

    /**
     * 忽略全部
     *
     * @param tenantId
     * @param searchQuery
     * @param apiName
     * @param fields
     * @param limit
     * @param offset
     * @return
     */
    private QueryResult<IObjectData> getDataIgnoreAll(User user, SearchQuery searchQuery, String apiName, List<String> fields, int limit, int offset) {
        SearchQuery tempSearchQuery = SearchQuery.builder().copyOf(searchQuery).build();
        tempSearchQuery.getSearchTemplateQuery().setOffset(offset);
        tempSearchQuery.getSearchTemplateQuery().setLimit(limit);

        if (CollectionUtils.isEmpty(fields)) {
            return serviceFacade.findBySearchQueryIgnoreAll(user, apiName, tempSearchQuery.getSearchTemplateQuery());
        } else {
            return serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, apiName, tempSearchQuery.getSearchTemplateQuery(), fields);
        }
    }

    @Override
    public IObjectData getById(String tenantId, String apiName, String id) {
        if (StringUtils.isBlank(id)){
            return null;
        }
        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(tenantId, Lists.newArrayList(id), apiName);
        if (CollectionUtils.isEmpty(objectDataByIds)){
            return null;
        }
        return objectDataByIds.get(0);
    }

    @Override
    public List<IObjectData> getByIds(String tenantId, String apiName, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Lists.newArrayList();
        }
        return serviceFacade.findObjectDataByIds(tenantId, ids, apiName);

    }
    
    /**
     * 查一个数据的主从数据 根据主对象id
     * 
     * @param user              用户信息
     * @param mainObjectApiName 主对象API名称
     * @param mainObjectId      主对象ID
     * @return 所有数据 主从数据都包含
     */
    @Override
    public Map<String/* apiName */, Map<String/* _id */, IObjectData>> getMainAndDetailObjectData(User user,
            String mainObjectApiName, String mainObjectId) {
        Map<String, Map<String, IObjectData>> result = new HashMap<>();
        IObjectData mainObject = getById(user.getTenantId(), mainObjectApiName, mainObjectId);
        if (mainObject == null) {
            return result;
        }
        Map<String, IObjectData> map = new HashMap<>();
        map.put(mainObject.getId(), mainObject);
        result.put(mainObjectApiName, map);
        List<IObjectData> details = serviceFacade.findDetailObjectDataListIgnoreFormula(mainObject, user);
        for (IObjectData detail : details) {
            String detailApiName = detail.getDescribeApiName();
            Map<String, IObjectData> detailMap = result.getOrDefault(detailApiName, new HashMap<>());
            detailMap.put(detail.getId(), detail);
            result.put(detailApiName, detailMap);
        }
        return result;
    }
        
        

    @Override
    public List<IObjectData> findByIdsIncludeInvalid(String tenantId, String objectDescribeAPIName, List<String> ids) {
        User user = User.systemUser(tenantId);
        IActionContext actionContext = ActionContextExt.of(user).getContext();
        return serviceFacade.findByIdsIncludeInvalid(ids, tenantId, objectDescribeAPIName, actionContext);
    }
    @Override
    public List<IObjectData> getAllIObjectDataListByQuery(User user, SearchQuery searchQuery, String apiName) {
        return getAllQueryDataListByQueryWithFields(user, searchQuery, apiName, null).getData();
    }

    @Override
    public QueryResult<IObjectData> getQueryDataListByQuery(User user, SearchQuery searchQuery, String apiName, boolean isQueryAll) {
        if (isQueryAll) {
            return getAllQueryDataListByQueryWithFields(user, searchQuery, apiName, null);
        }
        int limit = isQueryAll ? 100 : searchQuery.getSearchTemplateQuery().getLimit();
        searchQuery.getSearchTemplateQuery().setLimit(limit);
//        searchQuery.getSearchTemplateQuery().setFindExplicitTotalNum(Boolean.TRUE);
        if (isQueryAll) {
            searchQuery.getSearchTemplateQuery().setOffset(0);
        }
        QueryResult<IObjectData> data = new QueryResult<>();

//        log.info("getQueryDataListByQuery ProductFilter server searchQuery :{}",JSON.toJSON(searchQuery));
        QueryResult<IObjectData> queryResult = serviceFacade.findBySearchQuery(user, apiName, searchQuery.getSearchTemplateQuery());
        data.setTotalNumber(queryResult.getTotalNumber());
        data.getData().addAll(queryResult.getData());
        if (isQueryAll && queryResult.getTotalNumber() > searchQuery.getSearchTemplateQuery().getLimit()) {
            for (int i = limit; i < queryResult.getTotalNumber(); i += limit) {
                SearchQuery tempSearchQuery = SearchQuery.builder().copyOf(searchQuery).build();
                tempSearchQuery.getSearchTemplateQuery().setOffset(i);
                tempSearchQuery.getSearchTemplateQuery().setLimit(limit);
                data.getData().addAll(serviceFacade.findBySearchQuery(user, apiName, tempSearchQuery.getSearchTemplateQuery()).getData());
            }

        }

        return data;
    }
    @Override
    public List<IObjectData> getAllIObjectDataListByQueryWithFields(User user, SearchQuery searchQuery, String apiName, List<String> fields) {
        return getAllQueryDataListByQueryWithFields(user, searchQuery, apiName, fields).getData();
    }

    @Override
    public IObjectData save(User user, IObjectData data) {
        return serviceFacade.saveObjectData(user, data);
    }

    @Override
    public IObjectData update(User user, IObjectData data) {
        return serviceFacade.updateObjectData(user, data);
    }

    @Override
    public IObjectData update(String tenantId, String userId, IObjectData data) {
        if (StringUtils.isBlank(userId) || "0".equals(userId)) {
            userId = "-10000";
        }
        User user = User.builder().tenantId(tenantId).userId(userId).build();
        IActionContext context = ActionContextExt.of(user, RequestContextManager.getContext()).allowUpdateInvalid(true).setNotValidate(true).getContext();
        context.setChildObjValidateSkip(true);
        context.setPrivilegeCheck(false);
        return serviceFacade.updateObjectData(user, data, context);
    }
    @Override
    public Map<String,IObjectData> batchInvalidAndDelByQuery(User user, SearchQuery.SearchQueryBuilder searchQueryBuilder, String apiName) {
        //查询
        List<String> fields = Lists.newArrayList(BaseField.describeApiName.getApiName(), BaseField.tenantId.getApiName(), BaseField.id.getApiName());
        Map<String,IObjectData> delDataMap = new HashMap<>();
        List<IObjectData> tempData = null;
        SearchTemplateQuery searchTemplateQuery = searchQueryBuilder.build().getSearchTemplateQuery();
        do {
            tempData = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, apiName, searchTemplateQuery, fields).getData();
            //删除
            if (CollectionUtils.isNotEmpty(tempData)) {
                delDataMap.putAll(tempData.stream().collect(Collectors.toMap(IObjectData::getId, o -> o)));
                batchInvalidAndDelOfSingleApiName(tempData, user);
            }
        } while (CollectionUtils.isNotEmpty(tempData));
        return delDataMap;
    }

    /**
     * 分组批量作废
     *
     * @param objectDataList
     * @param user
     */
    @Override
    public void batchInvalidAndDelOfSingleApiName(List<IObjectData> objectDataList, User user) {
        partListAction(objectDataList, list -> serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(list, user));
    }

    @Override
    public void invalidData(User user, SearchQuery.SearchQueryBuilder searchQueryBuilder, String apiName) {
        List<String> fields = Lists.newArrayList(BaseField.describeApiName.getApiName(), BaseField.tenantId.getApiName(), BaseField.id.getApiName());
        List<IObjectData> tempData = null;
        Set<String> ids = new HashSet<>();
        String lastId = null;
        SearchTemplateQuery searchTemplateQuery = searchQueryBuilder.build().getSearchTemplateQuery();
        do {
            tempData = serviceFacade.findBySearchQueryWithFieldsIgnoreAll(user, apiName, searchTemplateQuery, fields).getData();
            //删除
            if (CollectionUtils.isNotEmpty(tempData)) {
                ids.addAll(tempData.stream().map(o -> o.getId()).collect(Collectors.toSet()));
                partListAction(tempData, t -> serviceFacade.bulkInvalid(t, user));
            }
        } while (CollectionUtils.isNotEmpty(tempData));
    }
    @Override
    public void delData(String apiName, List<String> ids, User user) {
        //查询
        if (CollectionUtils.isNotEmpty(ids)) {
            List<IObjectData> objectDataByIdsIncludeDeletedIgnoreAll = serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreAll(user, ids, apiName);
            serviceFacade.bulkDelete(objectDataByIdsIncludeDeletedIgnoreAll, user);
        }
    }
    @Override
    public void delData(List<IObjectData> delDatas,User user){
        serviceFacade.bulkDelete(delDatas, user);
    }
    @Override
    public void bulkRecover(String apiName, List<String> ids, User user) {
        //查询
        if (CollectionUtils.isNotEmpty(ids)) {
            List<IObjectData> objectDataByIdsIncludeDeletedIgnoreAll = serviceFacade.findObjectDataByIdsIncludeDeletedIgnoreAll(user, ids, apiName);
            serviceFacade.bulkRecover(objectDataByIdsIncludeDeletedIgnoreAll, user);
        }
    }

    /**
     * 分组批量更新
     *
     * @param objectDataList
     * @param user
     */
    @Override
    public List<IObjectData> batchUpdate(List<IObjectData> objectDataList, User user) {
        List<IObjectData> result = Lists.newArrayList();
        partListAction(objectDataList, list -> result.addAll(serviceFacade.batchUpdate(list, user)));
        return result;
    }

    /**
     * update接口  调用通用的  不使用元数据的  否则需要单独处理修改记录
     */
    @Override
    public void batchUpdateDataByProxy(int tenantId, int userId, String apiName, List<JSONObject> data) {
        PaasDataBatchIncrementUpdate.Arg arg = new PaasDataBatchIncrementUpdate.Arg();
        arg.addAll(data);
        PaasDataBatchIncrementUpdate.Result result = paasDataProxy.batchIncrementUpdate(tenantId, userId, apiName, arg);
    }
    @Override
    public List<IObjectData> getAllIObjectDataListByQueryWithDb(User user, SearchQuery searchQuery, String apiName) {
//        searchQuery.getSearchTemplateQuery().setSearchSource("db");
        return getAllIObjectDataListByQuery(user, searchQuery, apiName);
    }

    @SneakyThrows
    public List<IObjectData> getDataWithAuth(User user , String apiName, SearchQuery searchQuery){
//        ActionContextExt context = ActionContextExt.of(user);
//        context.setDoCalculate(false);
//        context.disableDeepQuote().setSkipRelevantTeam(false);
        RequestContext context = RequestContext.builder()
                .tenantId(user.getTenantId()).user(user).build();
        ControllerContext controllerContext = new ControllerContext(context, "CheckinsObj", "RelatedListWithAuth");
        StandardRelatedListController.Arg arg = new StandardRelatedListController.Arg();
        arg.setObjectApiName(apiName);
        String jsonString = searchQuery.getSearchTemplateQuery().toJsonString();
        //字符串换掉最后的字符} 改成 \"dataAuthUserId\":1013}
        jsonString = jsonString.substring(0,jsonString.length()-1) + ",\"dataAuthUserId\":"+user.getUserId()+"}";
        arg.setSearchQueryInfo(jsonString);
        arg.setIncludeAssociated(false);
        arg.setIncludeOrgInfo(false);
        arg.setIgnoreSceneFilter(false);
        arg.setIncludeDescribe(false);
        arg.setIncludeLayout(false);
        arg.setIgnoreSceneRecordType(false);
        arg.setOrdered(false);
        arg.setFindExplicitTotalNum(false);
        arg.setUntranslation(false);
        arg.setSearchRichTextExtra(false);
        arg.setSerializeEmpty(false);
        arg.setExtractExtendInfo(false);
        arg.setEncryptMaskFieldsWithDescribe(false);
        BaseListController.Result result = serviceFacade.triggerController(controllerContext, arg, BaseListController.Result.class);
        return result.getDataList().stream().map(o->o.toObjectData()).collect(Collectors.toList());
    }

    /**
     * 通过下游ea获取上游的互联企业
     */
    public IObjectData getEaRelaObjByDownEa(String upEi,String downEa){
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(20);
        query.setOffset(0);
        Filter keywordFilter = new Filter();
        keywordFilter.setFieldName("enterprise_account");
        keywordFilter.setOperator(Operator.EQ);
        keywordFilter.setFieldValues(Lists.newArrayList(downEa));
        query.setFilters(Lists.newArrayList(keywordFilter));

        List<IObjectData> detailData = serviceFacade.findBySearchQuery(User.systemUser(upEi), "EnterpriseRelationObj", query).getData();
        if (detailData.size() > 1){
            for (int i = 0; i < detailData.size(); i++) {
                log.info("getEaRelaObjByDownEi upEi:{},downEa:{},name:{}",upEi,downEa,detailData.get(i).getName());
            }
        }
        return !detailData.isEmpty() ? detailData.get(0) : null;
    }
    
    /**
     * 补充
     * serviceFacade.fillObjectDataWithRefObject(describe, resultList, user);
     * serviceFacade.fillSelectLabelInfo(describe, resultList);
     */
   public List<IObjectData> fillRefObject(User user, String apiName, List<IObjectData> resultList){
        IObjectDescribe describe = serviceFacade.findObject(user.getTenantId(),apiName);
        serviceFacade.fillObjectDataWithRefObject(describe, resultList, user);
        serviceFacade.fillSelectLabelInfo(describe, resultList);
        return resultList;
    }

}
