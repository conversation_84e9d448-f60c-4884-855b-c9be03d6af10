package com.facishare.crm.fmcg.wq.controller;

import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.IdempotentService;
import com.facishare.idempotent.IdempotentStore;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 幂等controller
 * @author: zhangsm
 * @create: 2024-03-19 17:23
 **/
@Idempotent
public abstract class FmcgIdempotentPreDefineController<A, R> extends PreDefineController<A, R> {

    abstract  R doServiceIdempotent(A a);

    /**
     * 不允许重写 ，重写 doServiceIdempotent
     * @param a
     * @return
     */
    @Override
    public final R doService(A a) {
        Idempotent idempotent = getIdempotent();
        if (idempotent != null) {
            result = doIdempotentAct(arg, idempotent);
            stopWatch.lap("doIdempotentAct");
        } else {
            result = this.doServiceIdempotent(arg);
            stopWatch.lap("doService");
        }
        return result;
    }
    private R doIdempotentAct(A arg, Idempotent idempotent) {
        IdempotentService idempotentService = serviceFacade.getBean(IdempotentService.class);
        IdempotentStore idempotentStore = serviceFacade.getBean(IdempotentStore.class);
        if (idempotentService != null) {
            String key = getIdempotentKey(arg);
            R r = idempotentService.doWithIdempotent(key, () -> this.doServiceIdempotent(arg), idempotent, null,
                    new MetaDataBusinessException(I18N.text(I18NKey.DO_NOT_SUBMIT_REPEATEDLY)));
            idempotentStore.delete(key);
            return r;
        }
        return this.doService(arg);
    }

    /**
     * 唯一键
     * @param arg
     * @return
     */
    protected abstract String getIdempotentKey(A arg);

    private Idempotent getIdempotent() {
        return this.getClass().getAnnotation(Idempotent.class);
    }
}
