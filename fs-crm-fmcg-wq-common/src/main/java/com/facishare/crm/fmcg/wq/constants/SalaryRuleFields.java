package com.facishare.crm.fmcg.wq.constants;

public abstract class Salary<PERSON>uleFields {

	private SalaryRuleFields(){};

	public static final String DISPLAY_NAME = "工资规则"; //ignoreI18n

	public static final String API_NAME = "SalaryRuleObj";

	 //发放单自动创建时机
	public static final String DISTRIBUTION_TIME = "distribution_time";

		 //次日
		public static final String DISTRIBUTION_TIME_Options_1 = "1";

		 //次周
		public static final String DISTRIBUTION_TIME_Options_2 = "2";

		 //次月
		public static final String DISTRIBUTION_TIME_Options_3 = "3";

	 //适用企业(外部)
	public static final String APPLICABLE_OUT_TENANT = "applicable_out_tenant";

	 //工资项
	public static final String SALARY_ITEM = "salary_item";

	 //计算公式
	public static final String CALCULATION_FORMULA = "calculation_formula";

	 //适用部门(内部)
	public static final String APPLICABLE_DEPARTMENT_INTE = "applicable_department_inte";

	 //定薪方式
	public static final String SALARY_METHOD = "salary_method";

		 //月薪
		public static final String SALARY_METHOD_Options_3 = "3";

		 //周薪
		public static final String SALARY_METHOD_Options_2 = "2";

		 //日薪
		public static final String SALARY_METHOD_Options_1 = "1";

	 //自动创建工资发放单
	public static final String AUTO_CREATED_PAYROLLVOUCHE = "auto_created_payrollvouche";

		 //是
		public static final String AUTO_CREATED_PAYROLLVOUCHE_Options_true = "true";

		 //否
		public static final String AUTO_CREATED_PAYROLLVOUCHE_Options_false = "false";

	 //适用角色(内部)
	public static final String APPLICABLE_ROLE_INTE = "applicable_role_inte";

		 //示例选项
		public static final String APPLICABLE_ROLE_INTE_Options_option1 = "option1";

	 //生效日期
	public static final String EFFECTIVE_DATE = "effective_date";

	 //适用角色(外部)
	public static final String APPLICABLE_OUT_ROLE = "applicable_out_role";

		 //示例选项
		public static final String APPLICABLE_OUT_ROLE_Options_option1 = "option1";

	 //工资条表头
	public static final String SALARY_STATEMENT_HEADER = "salary_statement_header";

	 //次周创建发放单时机
	public static final String NEXT_WEEK_DISTRIBUTION_OP = "next_week_distribution_op";

		 //周一
		public static final String NEXT_WEEK_DISTRIBUTION_OP_Options_1 = "1";

		 //周二
		public static final String NEXT_WEEK_DISTRIBUTION_OP_Options_2 = "2";

		 //周三
		public static final String NEXT_WEEK_DISTRIBUTION_OP_Options_3 = "3";

		 //周四
		public static final String NEXT_WEEK_DISTRIBUTION_OP_Options_4 = "4";

		 //周五
		public static final String NEXT_WEEK_DISTRIBUTION_OP_Options_5 = "5";

		 //周六
		public static final String NEXT_WEEK_DISTRIBUTION_OP_Options_6 = "6";

		 //周日
		public static final String NEXT_WEEK_DISTRIBUTION_OP_Options_0 = "0";

	 //适用人员(外部)
	public static final String APPLICABLE_OUT_USER = "applicable_out_user";

	 //适用人员(内部)
	public static final String APPLICABLE_PERSON_INTE = "applicable_person_inte";

	 //次月创建发放单时机
	public static final String NEXT_MONTH_DISTRIBUTION_OP = "next_month_distribution_op";

		 //1
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_1 = "1";

		 //2
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_2 = "2";

		 //3
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_3 = "3";

		 //4
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_4 = "4";

		 //5
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_5 = "5";

		 //6
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_6 = "6";

		 //7
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_7 = "7";

		 //8
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_8 = "8";

		 //9
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_9 = "9";

		 //10
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_10 = "10";

		 //11
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_11 = "11";

		 //12
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_12 = "12";

		 //13
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_13 = "13";

		 //14
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_14 = "14";

		 //15
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_15 = "15";

		 //16
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_16 = "16";

		 //17
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_17 = "17";

		 //18
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_18 = "18";

		 //19
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_19 = "19";

		 //20
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_20 = "20";

		 //21
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_21 = "21";

		 //22
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_22 = "22";

		 //23
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_23 = "23";

		 //24
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_24 = "24";

		 //25
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_25 = "25";

		 //26
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_26 = "26";

		 //27
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_27 = "27";

		 //28
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_28 = "28";

		 //29
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_29 = "29";

		 //30
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_30 = "30";

		 //31
		public static final String NEXT_MONTH_DISTRIBUTION_OP_Options_31 = "31";

	 //发放周期
	public static final String DISTRIBUTION_CYCLE = "distribution_cycle";

		 //按日发放
		public static final String DISTRIBUTION_CYCLE_Options_1 = "1";

		 //按周发放
		public static final String DISTRIBUTION_CYCLE_Options_2 = "2";

		 //按月发放
		public static final String DISTRIBUTION_CYCLE_Options_3 = "3";

	//业务类型 - 内部规则
	public static final String RECORD_TYPE_INTERNAL = "default__c";

	//业务类型 - 外部规则
	public static final String RECORD_TYPE_EXTERNAL = "record_external__c";

	//虚拟字段：适用角色（内部）显示
	public static final String APPLICABLE_ROLE_INTE_DISPLAY = "applicable_role_inte_display";

	//虚拟字段：适用角色（外部）显示
	public static final String APPLICABLE_OUT_ROLE_DISPLAY = "applicable_out_role_display";

	//强制不适用人员
	public static final String NON_APPLICABLE_PERSONNEL = "non_applicable_personnel";
	//强制适用人员 mandatory_applicable_personnel
	public static final String MANDATORY_APPLICABLE_PERSONNEL = "mandatory_applicable_personnel";

}
