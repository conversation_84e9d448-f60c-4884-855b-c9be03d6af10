package com.facishare.crm.fmcg.wq.service;
import com.facishare.paas.auth.common.exception.AuthException;
import com.google.common.collect.Lists;
import com.facishare.paas.auth.model.AuthContext;

import com.facishare.paas.auth.model.params.request.AddRoleGroupArg;
import com.facishare.paas.auth.model.params.request.CreateRoleArg;
import com.fxiaoke.paas.auth.factory.RoleClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2024-09-04 15:31
 **/
@Component
@Slf4j
public class PreRoleService {
    @Autowired
    private RoleClient roleClient;

    /**
     * SFA拜访应用许可
     */
    private static  final String SFA_VISIT_PARA_KEY = "user_license_advanced_outwork_limit";
    private static  final String SFA_VISIT_ROLE_CODE = "sfaVisitUserLimitRole";
    private static  final String FMCG_ROLE_GROUP_CODE = "fmcgRoleGroup";
    /**
     * 预置角色
     */
    public void preSFAVisitRole(String tenantId) {
        CreateRoleArg createRoleArg = new CreateRoleArg();
        createRoleArg.setRoleCode(SFA_VISIT_ROLE_CODE);
        createRoleArg.setRoleName("SFA拜访应用许可"); //ignoreI18n
        createRoleArg.setDescription("SFA拜访应用许可"); //ignoreI18n
        createRoleArg.setRoleType(1);
        createRoleArg.setLicenseCode(SFA_VISIT_PARA_KEY);
        createRoleArg.setAuthContext(AuthContext.builder().tenantId(tenantId).userId("-10000").appId("CRM").build());
        try{
            roleClient.createRole(createRoleArg);
        }catch (AuthException e){
            log.info("createRole error tenatId {} ,roleId {}",tenantId,SFA_VISIT_ROLE_CODE,e);
        }
    }
    /**
     * 创建角色分组
     */
    public void preRoleGroup(String tenantId) {
        AddRoleGroupArg addRoleGroupArg = new AddRoleGroupArg();
        addRoleGroupArg.setId(FMCG_ROLE_GROUP_CODE);
        addRoleGroupArg.setRoleGroupName("快消应用许可"); //ignoreI18n
        addRoleGroupArg.setRoleGroupDescription("快消应用许可"); //ignoreI18n
        addRoleGroupArg.setRoleCodes(Lists.newArrayList(SFA_VISIT_ROLE_CODE));
        addRoleGroupArg.setRoleGroupType(0);
        addRoleGroupArg.setAuthContext(AuthContext.builder().tenantId(tenantId).userId("-10000").appId("CRM").build());
        try{
            roleClient.addRoleGroup(addRoleGroupArg);
        }catch (AuthException e){
            log.info("addRoleGroup error tenatId {} ,roleId {}",tenantId,FMCG_ROLE_GROUP_CODE,e);
        }
    }
}
