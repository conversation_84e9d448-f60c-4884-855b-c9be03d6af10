package com.facishare.crm.fmcg.wq.constants;

public abstract class DisplayFormatDetailsFields {

	private DisplayFormatDetailsFields(){};

	public static final String DISPLAY_NAME = "陈列形式上报明细"; //ignoreI18n

	public static final String API_NAME = "DisplayFormatDetailsObj";

	 //陈列上报
	public static final String DISPLAY_REPORT = "display_report";

	 //ai层数
	public static final String AI_LAYER_COUNT = "ai_layer_count";

	 //数字
	public static final String FIELD_EXF1F = "field_eXf1F";

	 //地堆最大可视面
	public static final String MAX_VISIBLE_FACE_STOCKPILE = "max_visible_face_stockpile";

	 //陈列形式
	public static final String DISPLAY_FORMAT = "display_format";

	 //宽度
	public static final String WIDTH = "width";

	 //照片
	public static final String PHOTO = "photo";

	 //层数
	public static final String LAYER_COUNT = "layer_count";

	 //测试数字
	public static final String FIELD_6I2CW = "field_6i2Cw";

	 //陈列分类
	public static final String DISPLAY_CATEGORY = "display_category";

	 //割箱组数
	public static final String SCISSOR_GROUP_COUNT = "scissor_group_count";

}
