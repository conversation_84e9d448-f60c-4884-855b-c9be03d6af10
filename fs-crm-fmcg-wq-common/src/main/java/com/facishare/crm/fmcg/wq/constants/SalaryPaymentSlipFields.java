package com.facishare.crm.fmcg.wq.constants;

public abstract class SalaryPaymentSlipFields {

	private SalaryPaymentSlipFields(){};

	public static final String DISPLAY_NAME = "工资发放单"; //ignoreI18n

	public static final String API_NAME = "SalaryPaymentSlipObj";

	 //结束时间
	public static final String END_DATE = "end_date";

	 //发放状态
	public static final String PAY_STATUS = "pay_status";

		 //生成异常
		public static final String PAY_STATUS_Options_ERROR = "-1";

		 //正在生成
		public static final String PAY_STATUS_Options_0 = "0";

		 //未发放
		public static final String PAY_STATUS_Options_1 = "1";

		 //发放中
		public static final String PAY_STATUS_Options_2 = "2";

		 //发放异常
		public static final String PAY_STATUS_Options_3 = "3";

		 //已发放
		public static final String PAY_STATUS_Options_4 = "4";

	 //发放说明
	public static final String PAY_DESCRIPTION = "pay_description";



	 //工资规则
	public static final String SALARY_RULE = "salary_rule";

	 //发薪期间
	public static final String PAY_PERIOD = "pay_period";

	 //开始时间
	public static final String START_DATE = "start_date";

	//工资条表头
	public static final String SALARY_STATEMENT_HEADER = "salary_statement_header";

	//业务类型 - 内部规则
	public static final String RECORD_TYPE_INTERNAL = "default__c";

	//业务类型 - 外部规则
	public static final String RECORD_TYPE_EXTERNAL = "record_outer__c";

}