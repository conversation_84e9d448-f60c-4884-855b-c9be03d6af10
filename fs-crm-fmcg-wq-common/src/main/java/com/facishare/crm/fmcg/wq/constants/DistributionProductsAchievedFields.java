package com.facishare.crm.fmcg.wq.constants;

public abstract class DistributionProductsAchievedFields {

    private DistributionProductsAchievedFields() {
    };

    public static final String DISPLAY_NAME = "分销达成情况"; //ignoreI18n

    public static final String API_NAME = "DistributionProductsAchievedObj";

    // 要求要产品范围
    public static final String REQUIRED_STANDARD = "required_standard";
    public static final String REQUIRED_STANDARD_NAME = "required_standard__r";
    // 实际产品范围
    public static final String ACTUAL_STANDARD = "actual_standard";
    public static final String ACTUAL_STANDARD_NAME = "actual_standard__r";

    // 达成SKU数量
    public static final String QUANTITY_PRODUCTS_ACHIEVED = "quantity_products_achieved";

    // 陈列形式
    public static final String DISPLAY_FORMAT = "display_format";
    public static final String DISPLAY_FORMAT_NAME = "display_format__r";

    // 关联的标准
    public static final String RELATED_STANDARD = "related_standard";

    // 产品分类
    public static final String PRODUCT_CATEGORY = "product_category";
    public static final String PRODUCT_CATEGORY_NAME = "product_category__r";

    // 产品分类(多选)
    public static final String PRODUCT_CATEGORIES = "product_categories";
    public static final String PRODUCT_CATEGORIES_NAME = "product_categories__r";

    // 关联报告
    public static final String RELATED_REPORT = "related_report";
	 //标准值
	public static final String STANDARD_VALUE = "standard_value";

    // 分销状态
    public static final String STATUS = "status";
    public static final String STATUS_NAME = "status__r";

    // 已分销
    public static final String STATUS_Options_1 = "1";

    // 未分销
    public static final String STATUS_Options_0 = "0";

    // 其他
    public static final String STATUS_Options_other = "other";

}
