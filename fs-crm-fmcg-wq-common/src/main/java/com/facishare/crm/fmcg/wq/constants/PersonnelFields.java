package com.facishare.crm.fmcg.wq.constants;

public abstract class PersonnelFields {

	private PersonnelFields(){};

	public static final String DISPLAY_NAME = "人员"; //ignoreI18n

	public static final String API_NAME = "PersonnelObj";

	 //生日
	public static final String BIRTHDAY = "birthday";

	 //汇报对象
	public static final String LEADER = "leader";

	 //停用时间
	public static final String STOP_TIME = "stop_time";

	 //登陆方式
	public static final String LOGIN_TYPE = "login_type";

		 //手机号
		public static final String LOGIN_TYPE_Options_phone = "phone";

		 //邮箱
		public static final String LOGIN_TYPE_Options_email = "email";

	 //登录账号
	public static final String USER_NAME = "user_name";

	 //办公电话
	public static final String WORK_PHONE = "work_phone";

	 //msn
	public static final String MSN = "msn";

	 //基本信息描述
	public static final String DESCRIPTION = "description";

	 //语言
	public static final String LANGUAGE = "language";

		 //简体中文
		public static final String LANGUAGE_Options_zh = "zh-CN";

		 //英文
		public static final String LANGUAGE_Options_en = "en";

		 //繁体中文
		public static final String LANGUAGE_Options_tw = "zh-TW";

		 //日语
		public static final String LANGUAGE_Options_ja = "ja-JP";

	 //qq号码
	public static final String QQ_ACCOUNT = "qq_account";

	 //头像
	public static final String PROFILE_IMAGE = "profile_image";

	 //密码
	public static final String PASSWORD = "password";

	 //就业日期
	public static final String DATE_OF_FIRST_PLOYMENT = "date_of_first_ployment";

	 //微信号码
	public static final String WEIXIN_ACCOUNT = "weixin_account";

	 //员工创建时间
	public static final String EMPLOYEE_CREATE_TIME = "employee_create_time";

	 //邮箱
	public static final String EMAIL = "email";

	 //附属部门
	public static final String VICE_DEPARTMENTS = "vice_departments";

	 //单点登录账号
	public static final String SAML2_NAME_ID = "saml2_name_id";

	 //拼音
	public static final String NAME_SPELL = "name_spell";

	 //激活
	public static final String IS_ACTIVE = "is_active";

		 //是
		public static final String IS_ACTIVE_Options_true = "true";

		 //否
		public static final String IS_ACTIVE_Options_false = "false";

	 //性别
	public static final String SEX = "sex";

		 //男
		public static final String SEX_Options_M = "M";

		 //女
		public static final String SEX_Options_F = "F";

	 //心情
	public static final String WORKING_STATES = "working_states";

	 //主属部门
	public static final String MAIN_DEPARTMENT = "main_department";

	 //禁止登录
	public static final String IS_PAUSE_LOGIN = "is_pause_login";

		 //是
		public static final String IS_PAUSE_LOGIN_Options_true = "true";

		 //否
		public static final String IS_PAUSE_LOGIN_Options_false = "false";

	 //姓名
	public static final String FULL_NAME = "full_name";

	 //手机
	public static final String PHONE = "phone";

	 //员工ID
	public static final String USER_ID = "user_id";

	 //办公电话扩展分机号
	public static final String EXTENSION_NUMBER = "extension_number";

	 //入职日期
	public static final String DATE_OF_JOINING = "date_of_joining";

	 //员工编号
	public static final String EMPLOYEE_NUMBER = "employee_number";

	 //职位
	public static final String POSITION = "position";

	 //员工状态
	public static final String STATUS = "status";

		 //启用
		public static final String STATUS_Options_0 = "0";

		 //停用
		public static final String STATUS_Options_1 = "1";

}