package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 基础字段apiname
 * @author: zhangsm
 * @create: 2021-04-22 10:29
 **/
@AllArgsConstructor
@Getter
public enum BaseField{
    id("_id"),
    dataOwnDepartment("data_own_department"),
    recordType("record_type"),
    owner("owner"),
    tenantId("tenant_id"),
    describeApiName("object_describe_api_name"),
    name("name"),
    createTime("create_time"),
    lastModifiedTime("last_modified_time"),
    lifeStatus("life_status"),
    ;
    /**
     * apiName
     */
    private final String apiName;
}
