package com.facishare.crm.fmcg.wq.constants;

public abstract class TPMActivityProofProductDetailFields {

	private TPMActivityProofProductDetailFields(){};

	public static final String DISPLAY_NAME = "活动举证产品明细"; //ignoreI18n

	public static final String API_NAME = "TPMActivityProofProductDetailObj";

	 //AI识别数量
	public static final String AI_NUMBER = "ai_number";

	 //数量标准
	public static final String NUMBER_STANDARD = "number_standard";

	 //产品
	public static final String PRODUCT_ID = "product_id";

	 //项目
	public static final String ACTIVITY_ITEM_ID = "activity_item_id";

	 //举证陈列图片
	public static final String ACTIVITY_PROOF_DISPLAY_IMG_ID = "activity_proof_display_img_id";

	 //活动举证
	public static final String ACTIVITY_PROOF_ID = "activity_proof_id";

	 //陈列形式
	public static final String DISPLAY_FORM_ID = "display_form_id";

	 //产品分类
	public static final String PRODUCT_CATEGORY_ID = "product_category_id";

}
