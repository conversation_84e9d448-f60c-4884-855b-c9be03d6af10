package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import org.apache.commons.collections.CollectionUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-10-31 18:06
 **/
public class FmcgStandardListController extends StandardListController {
    @Override
    protected Result after(Arg arg, Result result) {
        //过滤字段 支持不过滤 __o__r__l
        if (CollectionUtils.isNotEmpty(arg.getFieldProjection()) && CollectionUtils.isNotEmpty(result.getDataList())) {
            //过滤字段 重写set
            Set<String> fieldProjectionSet = arg.getFieldProjection().stream().collect(Collectors.toSet());
            //add fieldExtName __o__r__l 等
            List<IFieldDescribe> fieldByApiNames = objectDescribe.getFieldByApiNames(arg.getFieldProjection());
            for (IFieldDescribe fieldByApiName : fieldByApiNames) {
                FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldByApiName);
                String fieldExtendName = fieldDescribeExt.getFieldExtendName();
                if (null != fieldExtendName){
                    fieldProjectionSet.add(fieldExtendName);
                }
            }
            List<String> fieldProjection = arg.getFieldProjection();
            for (ObjectDataDocument objectDataDocument : result.getDataList()) {
                Iterator<String> iterator = objectDataDocument.keySet().iterator();
                iterator.forEachRemaining((a) -> {
                    if (!fieldProjectionSet.contains(a)) {
                        iterator.remove();
                    }
                });
            }
        }
        return result;
    }
}
