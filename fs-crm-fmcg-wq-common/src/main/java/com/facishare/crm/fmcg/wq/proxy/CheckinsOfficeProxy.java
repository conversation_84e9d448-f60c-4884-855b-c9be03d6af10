package com.facishare.crm.fmcg.wq.proxy;

import com.alibaba.fastjson.JSONObject;
import com.facishare.appserver.checkinsoffice.api.model.AccountRangeOnRule;
import com.facishare.appserver.checkinsoffice.api.model.ScheduleDetail;
import com.facishare.crm.fmcg.wq.model.RestResult;
import com.facishare.rest.core.annotation.*;

import java.util.Map;


@RestResource(value="CHECKINSOFFICE",desc="考勤回调方法",contentType = "application/json;charset=utf-8")
public interface CheckinsOfficeProxy {

    @POST(value="/schedule/crmCallBack/validateScheduleInfo",desc = "校验班次是否符合要求")
    String validateScheduleInfo(@QueryParam("eId") String eId, @QueryParam("outerUserId") String outerUserId,@QueryParam("upstreamOwnerId") String upstreamOwnerId ,@Body ScheduleDetail scheduleDetail);

    @POST(value="/schedule/crmCallBack/saveSchedule",desc = "保存排班")
    String saveSchedule(@QueryParam("eId") String eId, @QueryParam("outerUserId") String outerUserId, @Body ScheduleDetail scheduleDetail);

    @POST(value="/schedule/crmCallBack/isUpdateSchedule",desc = "是否更新排班")
    boolean isUpdateSchedule(@QueryParam("eId") String eId, @QueryParam("outerUserId") String outerUserId, @Body ScheduleDetail scheduleDetail);

    @POST(value="/schedule/crmCallBack/getAccountRangeOnRule",desc = "获取客户设置的客户范围")
    AccountRangeOnRule.Result getAccountByRuleId(@HeaderParam("eId") String eId, @Body AccountRangeOnRule.Args args);

    @POST(value="/schedule/crmCallBack/queryMonthStatFields",desc = "获取月度考勤表统计信息")
    RestResult<Map<Integer, Map<String, Object>>> queryMonthStatFields(@HeaderParam("x-fs-ei") String eId, @Body JSONObject args);
}
