package com.facishare.crm.fmcg.wq.util;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.metadata.impl.ui.layout.Button;
import com.facishare.paas.metadata.ui.layout.IButton;
import de.lab4inf.math.util.Strings;

public class ButtonUtils {

    public static IButton buildButton(ObjectAction objectAction) {
        IButton button = new Button();
        button.setAction(objectAction.getActionCode());
        button.setLabel(objectAction.getActionLabel());
        button.set("isActive", true);
        if(Strings.isNullOrEmpty(objectAction.getButtonApiName())){
            button.setName(objectAction.getActionCode() + "_button_" + IButton.ACTION_TYPE_DEFAULT);
        }else {
            button.setName(objectAction.getButtonApiName());
        }
        button.setActionType(IButton.ACTION_TYPE_DEFAULT);
        return button;
    }

}
