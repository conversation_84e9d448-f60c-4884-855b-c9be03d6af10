package com.facishare.crm.fmcg.wq.constants;

public abstract class DisplayDistrAchSummaryFields {

	private DisplayDistrAchSummaryFields(){};

	public static final String DISPLAY_NAME = "陈列铺货达成汇总"; //ignoreI18n

	public static final String API_NAME = "DisplayDistrAchSummaryObj";

	 //数据来源单据对象
	public static final String RELATED_API_NAMES = "related_api_names";

	 //报告名称
	public static final String REPORT_TITLE = "report_title";

	 //外勤id
	public static final String CHECK_ID = "check_id";

	 //要求陈列形式
	public static final String REQUIRED_DISPLAY_FORMS = "required_display_forms";

	 //数据来源单据数据
	public static final String RELATED_OBJECT_DATA = "related_object_data";

	 //达成项总结
	public static final String ACHIEVED_RESULTS = "achieved_results";

		 //陈列形式全部达标
		public static final String ACHIEVED_RESULTS_Options_1 = "1";

		 //陈列形式部分达标
		public static final String ACHIEVED_RESULTS_Options_2 = "2";

		 //项目全部达标
		public static final String ACHIEVED_RESULTS_Options_3 = "3";

		 //项目部分达标
		public static final String ACHIEVED_RESULTS_Options_4 = "4";

		 //产品项目达标
		public static final String ACHIEVED_RESULTS_Options_5 = "5";

		 //物料项目达标
		public static final String ACHIEVED_RESULTS_Options_6 = "6";

		 //产品SKU种类达标
		public static final String ACHIEVED_RESULTS_Options_7 = "7";

		 //物料SKU种类达标
		public static final String ACHIEVED_RESULTS_Options_8 = "8";

		 //产品层数达标
		public static final String ACHIEVED_RESULTS_Options_9 = "9";

		 //其他
		public static final String ACHIEVED_RESULTS_Options_other = "other";

	 //数据来源单据
	public static final String RELATED_OBJECT = "related_object";

	 //标准要求类型
	public static final String REQUIRED_STANDARD_TYPES = "required_standard_types";
		//有整体要求
		public static final String REQUIRED_STANDARD_TYPES_Options_5 = "5";
		 //有物料分类要求
		public static final String REQUIRED_STANDARD_TYPES_Options_4 = "4";

		 //有物料要求
		public static final String REQUIRED_STANDARD_TYPES_Options_3 = "3";

		 //品类要求
		public static final String REQUIRED_STANDARD_TYPES_Options_2 = "2";

		 //产品要求
		public static final String REQUIRED_STANDARD_TYPES_Options_1 = "1";

		 //其他
		public static final String REQUIRED_STANDARD_TYPES_Options_other = "other";

	 //缺少陈列形式
	public static final String ABSENCE_DISPLAY_FORMS = "absence_display_forms";

	 //实际陈列形式
	public static final String ACTUAL_DISPLAY_FORMS = "actual_display_forms";

	 //日期
	public static final String STANDARD_ACHIEVEMENT_DATE = "standard_achievement_date";

	 //门店名称
	public static final String STORE_NAME = "store_name";

	 //未达成项总结
	public static final String SUMMARY_ISSUES = "summary_issues";

		 //陈列形式缺失 陈列形式未达标
		public static final String SUMMARY_ISSUES_Options_1 = "1";

		 //产品SKU种类不足
		public static final String SUMMARY_ISSUES_Options_2 = "2";

		 //物料不足
		public static final String SUMMARY_ISSUES_Options_3 = "3";

		 //整体项目数不足
		public static final String SUMMARY_ISSUES_Options_4 = "4";

		 //产品不足
		public static final String SUMMARY_ISSUES_Options_5 = "5";

		 //产品组数不足
		public static final String SUMMARY_ISSUES_Options_6 = "6";

		 //部分层未达标
		public static final String SUMMARY_ISSUES_Options_7 = "7";

		 //其他
		public static final String SUMMARY_ISSUES_Options_other = "other";

	 //达成状态
	public static final String ACHIEVEMENT_STATUS = "achievement_status";

		 //均不达标
		public static final String ACHIEVEMENT_STATUS_Options_3 = "3";

		 //部分达标
		public static final String ACHIEVEMENT_STATUS_Options_2 = "2";

		 //全部达标
		public static final String ACHIEVEMENT_STATUS_Options_1 = "1";

		 //其他
		public static final String ACHIEVEMENT_STATUS_Options_other = "other";

	 //陈列标准设置方式
	public static final String METHODS_DISPLAY_STANDARDS = "methods_display_standards";

		 //陈列形式+项目标准
		public static final String METHODS_DISPLAY_STANDARDS_Options_2 = "2";

		 //项目标准
		public static final String METHODS_DISPLAY_STANDARDS_Options_1 = "1";

		 //其他
		public static final String METHODS_DISPLAY_STANDARDS_Options_other = "other";

	 //关联业务单据数据
	public static final String BUSINESS_DOCUMENTS_ID = "business_documents_id";

	 //分销标准设置方式
	public static final String METHODS_DISTRIBUTION_STANDARDS = "methods_distribution_standards";

		 //按陈列形式+产品
		public static final String METHODS_DISTRIBUTION_STANDARDS_Options_3 = "3";

		 //按门店产品
		public static final String METHODS_DISTRIBUTION_STANDARDS_Options_2 = "2";

		 //按门店
		public static final String METHODS_DISTRIBUTION_STANDARDS_Options_1 = "1";

		 //其他
		public static final String METHODS_DISTRIBUTION_STANDARDS_Options_other = "other";

	 //关联业务单据对象名
	public static final String BUSINESS_DOCUMENTS_APINAME = "business_documents_apiname";

	public static final String RECORD_TYPE = "record_type";
		public static final String RECORD_DISPLAY = "default__c";
		public static final String RECORD_DISTRIBUTION = "distribution_report__c";
		public static final String RECORD_ACTIVITY = "activity_evidence_report__c";
        
    public static final String RULE_GROUP_API_NAME = "rule_group_apiName";
    public static final String RULE_GROUP_ID = "rule_group_id";

}
