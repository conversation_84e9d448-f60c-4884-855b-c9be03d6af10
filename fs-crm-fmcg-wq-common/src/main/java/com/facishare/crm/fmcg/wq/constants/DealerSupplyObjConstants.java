package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 供货关系管理
 * @author: zhangsm
 * @create: 2021-04-25 21:52
 **/
public interface DealerSupplyObjConstants {
    String API_NAME = "DealerSupplyObj";
    String DISPLAY_NAME = "供货关系设置"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field{
        dealerId("delear","配送商"), //ignoreI18n
        supplyType("supply_type","配送商类型"), //ignoreI18n
        ownDepartment("data_own_department","归属部门"), //ignoreI18n
        storeCount("store_quantity","供货门店数"); //ignoreI18n


        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
}
