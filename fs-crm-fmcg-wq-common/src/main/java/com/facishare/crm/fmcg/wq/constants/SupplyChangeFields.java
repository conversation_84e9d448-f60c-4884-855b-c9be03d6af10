package com.facishare.crm.fmcg.wq.constants;

public interface SupplyChangeFields {


	String API_NAME = "SupplyChangeObj";
	String DISPLAY_NAME = "供货关系变更"; //ignoreI18n
	/**
	 * 操作码详见 SupplyOperateEnum

	 */
	String OPERATE = "supply_operate";
	/**
	 *                 {
	 *                     "label": "执行失败",
	 *                     "value": "2"
	 *                 },
	 *                 {
	 *                     "label": "执行成功",
	 *                     "value": "1"
	 *                 },
	 *                 {
	 *                     "label": "未执行",
	 *                     "value": "0"
	 *                 },
	 * 执行状态
	 * 0 未执行
	 * 1 执行成功
	 * 2 执行失败
	 */
	String EXECUTE_STATUS = "execute_status";

	String IN_UP_CUSTOMER = "in_up_customer";
	String OUT_UP_CUSTOMER = "out_up_customer";
	String IN_UP_SUPPLY = "in_up_supply";
	String OUT_UP_SUPPLY = "out_up_supply";
	String FAIL_MESSAGE = "fail_message";
	String ARGS = "args";

}