package com.facishare.crm.fmcg.wq.constants;

public abstract class ShelfReportDetailFields {

	private ShelfReportDetailFields(){};

	public static final String DISPLAY_NAME = "陈列上报明细"; //ignoreI18n

	public static final String API_NAME = "ShelfReportDetailObj";

	 //整箱数
	public static final String FULL_BOX = "full_box";

	 //堆头数
	public static final String STACK = "stack";

	 //协访人
	public static final String ASSISTANT = "assistant";

	 //多行文本测试
	public static final String FIELD_32PXR = "field_32pxR";

	 //测试布局取值
	public static final String FIELD_G8528 = "field_g8528";

	 //图片
	public static final String FIELD_4YNU1 = "field_4Ynu1";

	 //AI排面数
	public static final String AI_ROW_NUMBER = "ai_row_number";

	 //产品规格
	public static final String PRODUCT_SPEC = "product_spec";

	 //单行文本
	public static final String FIELD_KE2V2 = "field_ke2v2";

	 //排面数
	public static final String ROW_NUMBERS = "row_numbers";

	 //规格ID
	public static final String PRODUCT_UNIQUE_ID = "product_unique_id";

	 //陈列上报
	public static final String REPORT_ID = "report_id";

	 //产品
	public static final String PRODUCT_ID = "product_id";

}
