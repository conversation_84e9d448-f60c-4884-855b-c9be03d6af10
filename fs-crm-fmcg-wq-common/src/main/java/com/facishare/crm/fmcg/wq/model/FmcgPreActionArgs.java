package com.facishare.crm.fmcg.wq.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-10-18 18:34
 **/
@Data
public abstract class FmcgPreActionArgs  implements Serializable {
    String objectDataId;

    /**
     * 是否跳过触发审批流程
     */
    private boolean skipTriggerApprovalFlow = false;

    /**
     * 获取数据ID（兼容方法）
     */
    public String getDataId() {
        return objectDataId;
    }
}
