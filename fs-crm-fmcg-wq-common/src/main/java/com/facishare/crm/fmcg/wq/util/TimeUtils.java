package com.facishare.crm.fmcg.wq.util;


import com.facishare.paas.I18N;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2024-03-19 16:27
 **/
public class TimeUtils {
    private TimeUtils(){

    }
    public static String convertTimestampToHoursAndMinutes(Long timestamp) {
        String day = I18N.text("oaappsrv.waiqin.msg.day");
        String hour = I18N.text("oaappsrv.waiqin.msg.hour");
        String minute = I18N.text("oaappsrv.waiqin.msg.minute");
        String second = I18N.text("oaappsrv.waiqin.msg.second");
        if (timestamp == null || timestamp.longValue() == 0L){
            return "";
        }
        // 假设时间戳是以毫秒为单位
        long totalMinutes = timestamp / 60000; // 将毫秒转成分钟
        long hours = totalMinutes / 60; // 获取小时数
        long minutes = totalMinutes % 60; // 获取剩余分钟数

        StringBuilder sb = new StringBuilder();
        if (hours > 0) {
            sb.append(hours).append(hour);
        }
        if (minutes > 0) {
            sb.append(minutes).append(minute);
        }

        // 如果转换结果为空，即小时和分钟都为0，返回"0分钟"
        if (sb.length() == 0) {
            sb.append("0").append(minute);
        }

        return sb.toString();
    }
}
