package com.facishare.crm.fmcg.wq.util;

import com.github.jedis.support.MergeJedisCmd;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @program: fs-crm-fmcg
 * @description: redis工具
 * @author: zhangsm
 * @create: 2021-05-28 16:28
 **/
@Component
public class RedisUtils {
    @Resource
    private MergeJedisCmd redisCmd;
    public final static String SYNC_AVARANGE_PREFIX = "fs-crm-fmcg-jmlmc-avarange-";
    public final static String SHOP_QUERY_PREFIX = "fs-crm-fmcg-yl-shop-query-";
    public final static String SYNC_COUNT_PREFIX = "fs-crm-fmcg-yl-sync-count-";
    public final static String TRANSFER_PREFIX = "fs-crm-fmcg-yl-transfer-";

    public final static String CHANNEL_DATA_PREFIX = "fs-crm-fmcg-channel-data-";
    public final static String EXPORT_TEMPLATE_REDIS_KEY = "fs-crm-fmcg-wq-office_export_template_by_account_";
    public final static String IMPORT_BY_ACCOUNT_REDIS_KEY = "fs-crm-fmcg-wq-office_import_by_account_";

    public boolean syncAvaRangeDoing(String tenantId,String dealerSupplyId){
        return redisCmd.exists(getKey(SYNC_AVARANGE_PREFIX,tenantId, dealerSupplyId));
    }
    public void syncAvaRangeRemove(String tenantId,String dealerSupplyId){
        redisCmd.del(getKey(SYNC_AVARANGE_PREFIX,tenantId, dealerSupplyId));
    }
    public void lockTransfer(String tenantId,String dealerSupplyId,int transferType) {
        redisCmd.setex(getKey(TRANSFER_PREFIX, tenantId, dealerSupplyId + transferType), 6000L, "1");
    }
    public void unlockTransfer(String tenantId,String dealerSupplyId,int transferType) {
        redisCmd.del(getKey(TRANSFER_PREFIX, tenantId, dealerSupplyId + transferType));
    }
    public boolean checkTransfer(String tenantId,String dealerSupplyId,int transferType) {
        return redisCmd.exists(getKey(TRANSFER_PREFIX, tenantId, dealerSupplyId + transferType));
    }
    /**
     * 验证调用次数 10分钟 调用3次 增加 其他文案 跳出逻辑
     * @param tenantId
     * @param shopId
     */
    public Long checkCount(String tenantId,String shopId){
        String cacheKey = SYNC_COUNT_PREFIX +tenantId+"-"+shopId;
        return redisCmd.incr(cacheKey);
    }
    public void addCount(String tenantId,String shopId){
        String cacheKey = SYNC_COUNT_PREFIX +tenantId+"-"+shopId;
        Long incr = redisCmd.incr(cacheKey);
        redisCmd.expire(cacheKey,6000L);
    }

    public void syncAvaRangeAdd(String tenantId,String dealerSupplyId){
        redisCmd.setex(getKey(SYNC_AVARANGE_PREFIX,tenantId, dealerSupplyId),6000L,"1");
    }
    public String getShopQueryResultByRedis(String tenantId,String supplyAccountId) {
        return redisCmd.get(getKey(SHOP_QUERY_PREFIX, tenantId, supplyAccountId));
    }
    public void  addShopRedis(String tenantId,String supplyAccountId,String result) {
        //5分钟缓存
        redisCmd.setex(getKey(SHOP_QUERY_PREFIX, tenantId, supplyAccountId), 300L, result);
    }
    public void removeShopRedis(String tenantId,String supplyAccountId) {
        redisCmd.del(getKey(SHOP_QUERY_PREFIX, tenantId, supplyAccountId));
    }

    public void setChannelData(String tenantId,String result) {
        redisCmd.setex(getKey(CHANNEL_DATA_PREFIX, tenantId, "fix"), 60*60*5L, result);
    }
    public String getChannelData(String tenantId) {
        return redisCmd.get(getKey(CHANNEL_DATA_PREFIX, tenantId, "fix"));
    }
    public void clearChannelData(String tenantId) {
        redisCmd.del(getKey(CHANNEL_DATA_PREFIX, tenantId, "fix"));
    }

    private String getKey(String prefix,String tenantId, String dealerSupplyId) {
        return prefix + tenantId + "_" + dealerSupplyId;
    }

    public boolean checkKeyIsExist(String redisKey){
        return StringUtils.isNotBlank(redisCmd.get(redisKey));
    }

    public void setCacheTime(String redisKey,String redisValue,long seconds){
        redisCmd.setex(redisKey,seconds,redisValue);
    }

    public void clearByKey(String redisKey){
        redisCmd.del(redisKey);
    }

    public String getKeyForScheduleImpOrExp(String key,String tenantId,String ruleId){
        return key + tenantId + "_" + ruleId;
    }

}
