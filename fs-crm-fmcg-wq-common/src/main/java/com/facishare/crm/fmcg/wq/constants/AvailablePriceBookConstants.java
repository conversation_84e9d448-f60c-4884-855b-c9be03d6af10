package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface AvailablePriceBookConstants {
    String API_NAME = "AvailablePriceBookObj";
    String DISPLAY_NAME = "可售价目表"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field{

        priceBookId("price_book_id","价目表名称"), //ignoreI18n
        availableRangeId("available_range_id","可售范围"); //ignoreI18n


        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
}
