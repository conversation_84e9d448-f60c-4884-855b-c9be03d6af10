package com.facishare.crm.fmcg.wq.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @program: fs-crm-fmcg
 * @description: 供货门店
 * @author: zhangsm
 * @create: 2021-04-25 21:52
 **/
public interface SupplyStoreObjConstants {
    String API_NAME = "SupplyStoreObj";
    String DISPLAY_NAME = "供货门店"; //ignoreI18n
    @AllArgsConstructor
    @Getter
    enum Field {
        thisDealerId("store","客户名称"), //ignoreI18n
        upDealerId("supply_id","关联供货商"), //ignoreI18n
        upSupplyId("supply_dealer","关联供货关系"), //ignoreI18n
        specialSupply("special_supply","是否特例供货"); //ignoreI18n

        /**
         * apiName
         */
        private final String apiName;


        /**
         * 标签名
         */
        private final String label;
    }
}
