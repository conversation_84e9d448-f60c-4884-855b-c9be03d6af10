<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:META-INF/fs-spring-dubbo-rest-client-eisupport-rest.xml"/>
    <bean id="fmcgQiXinDubboRest" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
        <property name="configName" value="fs-qixin-rest-client"/>
    </bean>

    <bean class="com.facishare.dubbo.plugin.client.EISupportDubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.qixin.api.service.PushSessionService"/>
        <property name="serverHostProfile" ref="fmcgQiXinDubboRest"/>
    </bean>
</beans>