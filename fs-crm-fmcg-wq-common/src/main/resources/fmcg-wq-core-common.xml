<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.1.xsd http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
<!--    <context:component-scan-->
<!--            base-package="com.facishare.paas.appframework,com.facishare.crm,com.fxiaoke.transaction.tcc"/>-->
<!--    <context:annotation-config/>-->
    <import resource="classpath:fmcg-framework-http.xml"/>
    <import resource="classpath:checkins-office-rest-client.xml"/>
    <!--考勤dubborest-->
    <import resource="classpath:spring/checkins-v2-rest-client.xml"/>
    <!--企信dubborest-->
<!--    <import resource="classpath:fmcg-qixin-dubborest.xml"/>-->
    <import resource="classpath:spring/fs-qixin-rest-client-remote.xml"/>
    <!--文件系统-->
    <import resource="classpath:META-INF/spring/fs-fsi-proxy-service.xml" />
    <import resource="classpath:spring/fs-paas-proxy.xml"/>


    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-paas-appframework-rest" init-method="init"/>

    <!-- 考勤服务代理方法-->
    <bean id="checkinsOfficeProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.fmcg.wq.proxy.CheckinsOfficeProxy">
        <property name="factory" ref="restServiceProxyFactory">
        </property>
    </bean>
    <!--外勤代理方法-->
    <bean id="checkinsProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.fmcg.wq.proxy.CheckinsProxy">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
    <bean id="redisCmd" class="com.github.jedis.support.JedisFactoryBean"
          p:configName="checkins-v2-redis"/>


    <!--发消息使用spring多线程-->
    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="5"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="20"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1000"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>
    <bean id="fmcgThreadPoolExecutor" class="com.github.trace.executor.MonitorAsyncTaskExecutor">
        <constructor-arg name="executor" ref="taskExecutor"/>
    </bean>

    <!--实时性较高的 操作时间短 用的 防止被堵-->
    <bean id="syncTaskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="10"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="30"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1000"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>
    <bean id="fmcgSyncTaskExecutor" class="com.github.trace.executor.MonitorAsyncTaskExecutor">
        <constructor-arg name="executor" ref="syncTaskExecutor"/>
    </bean>

<!--    crm新消息服务-->
    <bean id="messageServiceV2" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.fxiaoke.api.MessageServiceV2"/>
    </bean>
</beans>