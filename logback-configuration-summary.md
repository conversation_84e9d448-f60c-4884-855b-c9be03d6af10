# FMCG-WQ Logback 配置修改总结

## 📋 修改概述

为 `fmcg-wq` 模块配置了专门的错误日志，将 ERROR 级别的日志输出到 `service-error.log` 文件，并设置了7天回滚压缩。

## 🔧 具体修改内容

### 1. 新增 ServiceErrorLog Appender

在 `fs-crm-fmcg-wq-web/src/main/resources/logback.xml` 中添加了专门的错误日志 appender：

```xml
<!-- FMCG-WQ 服务错误日志 -->
<appender name="ServiceErrorLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
        <level>ERROR</level>
    </filter>
    <file>${catalina.home}/logs/service-error.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
        <fileNamePattern>${catalina.home}/logs/service-error.%d{yyyy-MM-dd}.log.zip</fileNamePattern>
        <maxHistory>7</maxHistory>
        <maxFileSize>1GB</maxFileSize>
        <totalSizeCap>5GB</totalSizeCap>
        <cleanHistoryOnStart>true</cleanHistoryOnStart>
    </rollingPolicy>
    <encoder>
        <pattern>${defaultPattern}</pattern>
    </encoder>
</appender>
```

### 2. 新增 FMCG-WQ 专门的 Logger 配置

为 `com.facishare.crm.fmcg.wq` 包配置了专门的日志记录器：

```xml
<!-- FMCG-WQ 专门的错误日志配置 -->
<logger name="com.facishare.crm.fmcg.wq" level="INFO" additivity="false">
    <appender-ref ref="ServiceLog"/>
    <appender-ref ref="ServiceErrorLog"/>
</logger>
```

## 📊 配置特性

### ServiceErrorLog Appender 特性

| 配置项 | 值 | 说明 |
|--------|-----|------|
| **日志文件** | `${catalina.home}/logs/service-error.log` | 错误日志文件路径 |
| **日志级别** | `ERROR` | 只记录 ERROR 级别的日志 |
| **回滚策略** | `TimeBasedRollingPolicy` | 基于时间的回滚策略 |
| **文件名模式** | `service-error.%d{yyyy-MM-dd}.log.zip` | 按日期回滚并压缩 |
| **保留天数** | `7天` | 保留最近7天的日志文件 |
| **单文件大小** | `1GB` | 单个日志文件最大1GB |
| **总大小限制** | `5GB` | 所有日志文件总大小限制5GB |
| **启动清理** | `true` | 启动时清理过期日志 |
| **日志格式** | `${defaultPattern}` | 使用默认的日志格式 |

### Logger 配置特性

| 配置项 | 值 | 说明 |
|--------|-----|------|
| **包名** | `com.facishare.crm.fmcg.wq` | 针对 fmcg-wq 包 |
| **日志级别** | `INFO` | 记录 INFO 及以上级别的日志 |
| **继承性** | `false` | 不继承父 logger 的 appender |
| **输出目标** | `ServiceLog` + `ServiceErrorLog` | 同时输出到服务日志和错误日志 |

## 🎯 实现效果

### 1. 日志分离
- **普通日志**: 所有 INFO 及以上级别的日志继续输出到 `service.log`
- **错误日志**: ERROR 级别的日志额外输出到 `service-error.log`

### 2. 日志文件结构
```
${catalina.home}/logs/
├── service.log                    # 所有服务日志（包含 fmcg-wq）
├── service-error.log              # fmcg-wq 错误日志（仅 ERROR 级别）
├── service-error.2024-01-01.log.zip  # 历史错误日志（压缩）
├── service-error.2024-01-02.log.zip
└── ...
```

### 3. 日志内容示例

#### service.log 中的 fmcg-wq 日志
```
14:30:25.123 [http-nio-8080-exec-1] INFO  SalaryServiceImpl traceId123 user456 工资条生成成功，ID: salary123
14:30:26.456 [http-nio-8080-exec-2] WARN  SalaryServiceImpl traceId124 user456 工资明细计算警告，明细ID: detail456
14:30:27.789 [http-nio-8080-exec-3] ERROR SalaryServiceImpl traceId125 user456 工资明细计算异常，设置为异常状态，明细ID: detail789
```

#### service-error.log 中的 fmcg-wq 错误日志
```
14:30:27.789 [http-nio-8080-exec-3] ERROR SalaryServiceImpl traceId125 user456 工资明细计算异常，设置为异常状态，明细ID: detail789
    at com.facishare.crm.fmcg.wq.service.impl.SalaryServiceImpl.calculateSalaryItemAmount(SalaryServiceImpl.java:3515)
    at com.facishare.crm.fmcg.wq.service.impl.SalaryServiceImpl.processSingleSalaryDetail(SalaryServiceImpl.java:4064)
    ...
```

## 🔍 日志格式说明

使用的是 `${defaultPattern}` 格式，包含以下信息：
- **时间戳**: `HH:mm:ss.SSS` 格式
- **线程名**: `[thread]`
- **日志级别**: `%-5level`
- **Logger名**: `%logger{12}`
- **TraceId**: `%X{traceId}`
- **UserId**: `%X{userId}`
- **日志消息**: `%msg`
- **异常堆栈**: `%rEx{full, ...}` (过滤了无用的框架堆栈)

## 🚀 使用建议

### 1. 错误监控
- 可以专门监控 `service-error.log` 文件来快速发现 fmcg-wq 模块的错误
- 设置日志告警，当错误日志文件有新内容时及时通知

### 2. 问题排查
- 使用 TraceId 关联普通日志和错误日志
- 错误日志文件更小，查找错误更快速

### 3. 日志分析
- 可以单独分析 fmcg-wq 模块的错误趋势
- 便于统计错误率和错误类型

## 📈 性能影响

### 1. 磁盘空间
- 错误日志通常较少，额外的磁盘占用很小
- 7天回滚 + 5GB 总大小限制，控制了磁盘使用

### 2. 写入性能
- 同步写入两个文件，性能影响微乎其微
- ERROR 级别日志频率较低，不会造成性能瓶颈

### 3. 日志处理
- 错误日志文件更小，处理和传输更快
- 便于日志收集系统单独处理错误日志

## ✅ 验证方法

### 1. 启动验证
启动应用后检查日志文件是否正常创建：
```bash
ls -la ${catalina.home}/logs/service-error.log
```

### 2. 功能验证
触发一个错误场景，检查错误日志是否正确记录：
```bash
tail -f ${catalina.home}/logs/service-error.log
```

### 3. 回滚验证
等待一天后检查是否正确回滚：
```bash
ls -la ${catalina.home}/logs/service-error.*.log.zip
```

## 🎉 总结

通过这次配置修改，我们成功实现了：

1. ✅ **专门的错误日志**: fmcg-wq 模块的 ERROR 日志单独输出到 `service-error.log`
2. ✅ **7天回滚压缩**: 自动按天回滚并压缩历史日志文件
3. ✅ **大小控制**: 单文件1GB，总大小5GB的限制
4. ✅ **格式统一**: 使用与其他日志相同的格式，便于分析
5. ✅ **性能优化**: 最小的性能影响，最大的便利性

这个配置将大大提高 fmcg-wq 模块错误日志的可管理性和可分析性！🚀
