#!/bin/bash

# 容器OOM排查脚本集合
# 使用方法: ./oom-check-scripts.sh [function_name] [parameters]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 1. 检查Docker容器内存使用情况
check_docker_memory() {
    local container_id=$1
    if [ -z "$container_id" ]; then
        print_error "请提供容器ID"
        echo "使用方法: check_docker_memory <container_id>"
        return 1
    fi
    
    print_info "检查Docker容器 $container_id 的内存使用情况..."
    
    # 检查容器是否存在
    if ! docker ps -a --format "table {{.ID}}" | grep -q "$container_id"; then
        print_error "容器 $container_id 不存在"
        return 1
    fi
    
    echo "=== 容器基本信息 ==="
    docker inspect "$container_id" | jq '.[] | {
        Name: .Name,
        State: .State.Status,
        Memory: .HostConfig.Memory,
        MemorySwap: .HostConfig.MemorySwap,
        OomKillDisable: .HostConfig.OomKillDisable
    }'
    
    echo -e "\n=== 容器资源使用情况 ==="
    docker stats "$container_id" --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"
    
    echo -e "\n=== 容器内存限制信息 ==="
    docker exec "$container_id" sh -c '
        if [ -f /sys/fs/cgroup/memory/memory.limit_in_bytes ]; then
            echo "Memory Limit: $(cat /sys/fs/cgroup/memory/memory.limit_in_bytes | numfmt --to=iec)"
            echo "Memory Usage: $(cat /sys/fs/cgroup/memory/memory.usage_in_bytes | numfmt --to=iec)"
            echo "Memory Max Usage: $(cat /sys/fs/cgroup/memory/memory.max_usage_in_bytes | numfmt --to=iec)"
        elif [ -f /sys/fs/cgroup/memory.max ]; then
            echo "Memory Limit: $(cat /sys/fs/cgroup/memory.max)"
            echo "Memory Usage: $(cat /sys/fs/cgroup/memory.current | numfmt --to=iec)"
        fi
    ' 2>/dev/null || print_warning "无法获取cgroup内存信息"
    
    echo -e "\n=== 容器OOM事件 ==="
    docker exec "$container_id" dmesg 2>/dev/null | grep -i "killed process\|out of memory\|oom" | tail -10 || print_warning "无法获取OOM事件信息"
}

# 2. 检查Kubernetes Pod内存使用情况
check_k8s_memory() {
    local pod_name=$1
    local namespace=${2:-default}
    
    if [ -z "$pod_name" ]; then
        print_error "请提供Pod名称"
        echo "使用方法: check_k8s_memory <pod_name> [namespace]"
        return 1
    fi
    
    print_info "检查Kubernetes Pod $pod_name (namespace: $namespace) 的内存使用情况..."
    
    # 检查Pod是否存在
    if ! kubectl get pod "$pod_name" -n "$namespace" >/dev/null 2>&1; then
        print_error "Pod $pod_name 在命名空间 $namespace 中不存在"
        return 1
    fi
    
    echo "=== Pod基本信息 ==="
    kubectl get pod "$pod_name" -n "$namespace" -o wide
    
    echo -e "\n=== Pod资源使用情况 ==="
    kubectl top pod "$pod_name" -n "$namespace" --containers 2>/dev/null || print_warning "无法获取资源使用情况，请确保metrics-server已安装"
    
    echo -e "\n=== Pod资源限制 ==="
    kubectl get pod "$pod_name" -n "$namespace" -o jsonpath='{.spec.containers[*].resources}' | jq .
    
    echo -e "\n=== Pod事件 ==="
    kubectl get events --field-selector involvedObject.name="$pod_name" -n "$namespace" --sort-by='.lastTimestamp' | tail -10
    
    echo -e "\n=== Pod日志(最后50行) ==="
    kubectl logs "$pod_name" -n "$namespace" --tail=50 | grep -i "outofmemory\|oom\|memory" || print_info "日志中未发现内存相关错误"
}

# 3. 检查Java进程内存使用情况
check_java_memory() {
    local pid=$1
    
    if [ -z "$pid" ]; then
        print_error "请提供Java进程PID"
        echo "使用方法: check_java_memory <pid>"
        echo "可用的Java进程:"
        jps -v 2>/dev/null || ps aux | grep java | grep -v grep
        return 1
    fi
    
    # 检查进程是否存在
    if ! ps -p "$pid" >/dev/null 2>&1; then
        print_error "进程 $pid 不存在"
        return 1
    fi
    
    # 检查是否为Java进程
    if ! ps -p "$pid" -o cmd= | grep -q java; then
        print_error "进程 $pid 不是Java进程"
        return 1
    fi
    
    print_info "检查Java进程 $pid 的内存使用情况..."
    
    echo "=== Java进程基本信息 ==="
    ps -p "$pid" -o pid,ppid,cmd,%mem,%cpu,etime
    
    echo -e "\n=== JVM启动参数 ==="
    ps -p "$pid" -o cmd= | tr ' ' '\n' | grep -E '^-X|^-XX' | sort
    
    echo -e "\n=== JVM内存使用情况 ==="
    jstat -gc "$pid" 2>/dev/null || print_warning "无法获取GC信息，请确保jstat可用"
    
    echo -e "\n=== JVM内存容量 ==="
    jstat -gccapacity "$pid" 2>/dev/null || print_warning "无法获取内存容量信息"
    
    echo -e "\n=== 堆内存对象统计(Top 20) ==="
    jmap -histo "$pid" 2>/dev/null | head -25 || print_warning "无法获取堆内存统计信息"
    
    echo -e "\n=== 线程堆栈信息 ==="
    local thread_count=$(jstack "$pid" 2>/dev/null | grep -c "^\".*\" #" || echo "无法获取")
    echo "活跃线程数: $thread_count"
}

# 4. 系统内存检查
check_system_memory() {
    print_info "检查系统内存使用情况..."
    
    echo "=== 系统内存概览 ==="
    free -h
    
    echo -e "\n=== 内存详细信息 ==="
    cat /proc/meminfo | grep -E "MemTotal|MemFree|MemAvailable|Buffers|Cached|SwapTotal|SwapFree"
    
    echo -e "\n=== 内存使用最多的进程(Top 10) ==="
    ps aux --sort=-%mem | head -11
    
    echo -e "\n=== 系统负载 ==="
    uptime
    
    echo -e "\n=== 磁盘使用情况 ==="
    df -h
    
    echo -e "\n=== OOM Killer日志 ==="
    dmesg | grep -i "killed process\|out of memory\|oom" | tail -10 || print_info "未发现OOM事件"
}

# 5. 生成内存报告
generate_memory_report() {
    local output_file=${1:-"memory_report_$(date +%Y%m%d_%H%M%S).txt"}
    
    print_info "生成内存使用报告到文件: $output_file"
    
    {
        echo "=== 内存使用报告 ==="
        echo "生成时间: $(date)"
        echo "主机名: $(hostname)"
        echo ""
        
        echo "=== 系统信息 ==="
        uname -a
        echo ""
        
        check_system_memory
        echo ""
        
        echo "=== Docker容器信息 ==="
        if command -v docker >/dev/null 2>&1; then
            docker ps --format "table {{.ID}}\t{{.Names}}\t{{.Status}}\t{{.Image}}"
            echo ""
            docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
        else
            echo "Docker未安装或不可用"
        fi
        echo ""
        
        echo "=== Kubernetes Pod信息 ==="
        if command -v kubectl >/dev/null 2>&1; then
            kubectl get pods --all-namespaces -o wide 2>/dev/null || echo "无法连接到Kubernetes集群"
        else
            echo "kubectl未安装或不可用"
        fi
        echo ""
        
        echo "=== Java进程信息 ==="
        if command -v jps >/dev/null 2>&1; then
            jps -v
        else
            ps aux | grep java | grep -v grep
        fi
        
    } > "$output_file"
    
    print_success "报告已生成: $output_file"
}

# 6. 实时监控内存使用
monitor_memory() {
    local interval=${1:-5}
    local duration=${2:-60}
    
    print_info "开始监控内存使用情况 (间隔: ${interval}秒, 持续: ${duration}秒)"
    print_info "按 Ctrl+C 停止监控"
    
    local count=0
    local max_count=$((duration / interval))
    
    while [ $count -lt $max_count ]; do
        clear
        echo "=== 内存监控 ($(date)) ==="
        echo "监控时间: $((count * interval))/${duration}秒"
        echo ""
        
        free -h
        echo ""
        
        echo "内存使用最多的进程:"
        ps aux --sort=-%mem | head -6
        echo ""
        
        if command -v docker >/dev/null 2>&1; then
            echo "Docker容器内存使用:"
            docker stats --no-stream --format "table {{.Container}}\t{{.MemUsage}}\t{{.MemPerc}}" 2>/dev/null | head -6
        fi
        
        sleep "$interval"
        count=$((count + 1))
    done
    
    print_success "监控完成"
}

# 主函数
main() {
    case "$1" in
        "check_docker")
            check_docker_memory "$2"
            ;;
        "check_k8s")
            check_k8s_memory "$2" "$3"
            ;;
        "check_java")
            check_java_memory "$2"
            ;;
        "check_system")
            check_system_memory
            ;;
        "report")
            generate_memory_report "$2"
            ;;
        "monitor")
            monitor_memory "$2" "$3"
            ;;
        *)
            echo "容器OOM排查脚本"
            echo ""
            echo "使用方法:"
            echo "  $0 check_docker <container_id>              - 检查Docker容器内存"
            echo "  $0 check_k8s <pod_name> [namespace]         - 检查K8s Pod内存"
            echo "  $0 check_java <pid>                         - 检查Java进程内存"
            echo "  $0 check_system                             - 检查系统内存"
            echo "  $0 report [output_file]                     - 生成内存报告"
            echo "  $0 monitor [interval] [duration]            - 实时监控内存"
            echo ""
            echo "示例:"
            echo "  $0 check_docker abc123"
            echo "  $0 check_k8s my-app default"
            echo "  $0 check_java 12345"
            echo "  $0 monitor 5 300"
            ;;
    esac
}

# 执行主函数
main "$@"
