package com.facishare.crm.fmcg.wq;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.junit.Test;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-09-11 18:04
 **/
public class Mytest {

    @Test
    public void pwd(){
//        System.out.println(PasswordUtil.decode("0C1A654782903C404FCDAEE0D604232AC2A1A35B616D4742A65519A9DC7BB9FD"));
//        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
//        Lists.newArrayList(1,2,3).stream().forEach(o->objectObjectHashMap.put(o,null));
//        System.out.println(objectObjectHashMap);

//        System.out.println(  "www.fxiaoke.com/" + String.format("api/v2/reverse-geocode/decode?latLng=%s&cache=true", URLEncoder.encode(1.515+","+2.648)));
        String ss ="{\"aaa\":\"false\"}";
        T1 t1 = JSON.parseObject(ss, T1.class);
        System.out.println(t1.toString());
        System.out.println("40161413".hashCode()%100);
    }
    @Data
    static class T1{
        Boolean aaa;
    }
}
