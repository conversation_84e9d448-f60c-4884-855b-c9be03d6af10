package com.facishare.crm.fmcg.wq.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.predef.action.StandardFlowStartCallbackAction;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.metadata.api.IObjectData;

public class ShiftsFlowStartCallbackAction extends StandardFlowStartCallbackAction {

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("ShiftsFlowStartCallbackAction is start! arg:{}", JSON.toJSON(arg));
        result = super.after(arg, result);
        if (String.valueOf(ApprovalFlowTriggerType.CREATE.getTriggerTypeCode()).equals(arg.getTriggerType())
                ||String.valueOf(ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode()).equals(arg.getTriggerType())
        ) {
            if(ApprovalFlowStartResult.of(arg.getCode()).equals(ApprovalFlowStartResult.SUCCESS)){
                IObjectData newObjectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getDataId(), objectDescribe.getApiName());
                newObjectData.set("apply_status", "审核中"); //ignoreI18n
                serviceFacade.updateObjectData(actionContext.getUser(), newObjectData);
            }

        }
        return result;
    }
}

