package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.servcie.impl.OfficeShiftServiceImpl;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.functions.utils.Maps;
import lombok.Data;
import org.apache.commons.collections.ListUtils;

import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 终端是用来拉 客户对应门店班次的
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/
public class UserScheduleListFillDataController extends PreDefineController<UserScheduleListFillDataController.Arg, UserScheduleListFillDataController.Result> {

    OfficeShiftService officeShiftService =  SpringUtil.getContext().getBean(OfficeShiftServiceImpl.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected Result doService(Arg arg) {
        Result result = new Result();
        if (arg.getUserScheduleContent() == null){
            return result;
        }
        if (arg.getUserScheduleContent().getDateUserScheduleMap() == null){
            arg.getUserScheduleContent().setDateUserScheduleMap(Maps.newHashMap());
        }
        officeShiftService.checkUserScheduleConflictAndFillData(controllerContext.getTenantId(), arg.getUserScheduleContent(),true,arg.getIsOuter() == 1);
        result.setUserScheduleContent(arg.getUserScheduleContent());
        return result;
    }

    @Data
    public static class Arg {
        private UserScheduleContent userScheduleContent;

        private int isOuter;

    }
    @Data
    public static class Result {
        private UserScheduleContent userScheduleContent;

    }
}
