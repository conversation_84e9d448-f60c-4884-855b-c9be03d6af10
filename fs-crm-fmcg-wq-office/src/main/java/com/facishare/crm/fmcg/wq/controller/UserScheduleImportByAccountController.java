package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.excel.EasyExcel;
import com.facishare.appserver.checkinsoffice.api.model.AccountRangeOnRule;
import com.facishare.appserver.utils.AccountUtils;
import com.facishare.appserver.utils.EnvUtils;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.facishare.crm.fmcg.wq.excel.EasyExcelHandler;
import com.facishare.crm.fmcg.wq.excel.MnReadImportScheduleByAccountListener;
import com.facishare.crm.fmcg.wq.excel.ReadImportScheduleByAccountListener;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.paas.file.FileInvoke;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.session.FmcgPushSession;
import com.facishare.crm.fmcg.wq.session.SessionContentForText;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.qixin.api.constant.OSS1SubCategory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/
@Slf4j
public class UserScheduleImportByAccountController extends PreDefineController<UserScheduleImportByAccountController.Arg, UserScheduleImportByAccountController.Result> {

    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);
    private final FileInvoke fileInvoke = SpringUtil.getContext().getBean(FileInvoke.class);
    private final EasyExcelHandler easyExcelHandler = SpringUtil.getContext().getBean(EasyExcelHandler.class);
    private final FmcgPushSession fmcgPushSession = SpringUtil.getContext().getBean(FmcgPushSession.class);
    private final RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);
    private final EmployeeDao employeeDao = SpringUtil.getContext().getBean(EmployeeDao.class);
    private final AccountShiftDao accountShiftDao =  SpringUtil.getContext().getBean(AccountShiftDao.class);
    private final OfficeShiftService officeShiftService = SpringUtil.getContext().getBean(OfficeShiftService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected Result doService(Arg arg) {
        String tenantId = controllerContext.getTenantId();
        String ea = serviceFacade.getEAByEI(tenantId);
        int userId = controllerContext.getUser().getUserIdInt();
        String importRedisKey = redisUtils.getKeyForScheduleImpOrExp(RedisUtils.IMPORT_BY_ACCOUNT_REDIS_KEY,tenantId,arg.getRuleId());
        redisUtils.clearByKey(importRedisKey);
        if (redisUtils.checkKeyIsExist(importRedisKey)) {
            throw new ValidateException("正在执行导入操作,请于导入完成再操作"); //ignoreI18n
        }
        redisUtils.setCacheTime(importRedisKey,"1",60 * 60);
        String exportRedisKey = redisUtils.getKeyForScheduleImpOrExp(RedisUtils.EXPORT_TEMPLATE_REDIS_KEY,tenantId,arg.getRuleId());
        if(redisUtils.checkKeyIsExist(exportRedisKey)){
            throw new ValidateException("正在获取导入模版,请稍等"); //ignoreI18n
        }
        String userAccount = AccountUtils.getUserAccount(ea,userId);
        AccountRangeOnRule.Result accountRange = null;
        try {
            accountRange = officeShiftService.getAccountRange(tenantId,arg.getRuleId());
            officeShiftService.verifyAccountRange(tenantId,accountRange);
        }catch (ValidateException e){
            redisUtils.clearByKey(importRedisKey);
            throw e;
        }catch (Exception e) {
            redisUtils.clearByKey(importRedisKey);
            throw new ValidateException("系统异常,请稍后重试"); //ignoreI18n
        }

        AccountRangeOnRule.Result finalAccountRange = accountRange;
        fmcgThreadPoolExecutor.execute(()->{
            try{
                boolean isMn = officeShiftService.isMnPmmSchedule(serviceFacade.getEAByEI(tenantId));
                ReadImportScheduleByAccountListener listener = isMn ? new MnReadImportScheduleByAccountListener() :  new ReadImportScheduleByAccountListener();
                try {
                    // 下载文件
                    byte[] dataArr;
                    dataArr = fileInvoke.nDownloadTempFile(userAccount, arg.getNpath());
                    InputStream inputStream = new ByteArrayInputStream(dataArr);
                    EasyExcel.read(inputStream).registerReadListener(listener).autoCloseStream(true).sheet().doReadSync();
                    dataArr = null;
                }catch (ValidateException e){
                    fmcgPushSession.doSendSession(ea,Lists.newArrayList(userId), new SessionContentForText("考勤","自由排班导入失败",e.getMessage()), OSS1SubCategory.WJTZ); //ignoreI18n
                    return;
                }catch (Exception e) {
                    log.error("read is error",e);
                    fmcgPushSession.doSendSession(ea,Lists.newArrayList(userId), new SessionContentForText("考勤","自由排班导入失败","下载文件失败,请稍后重试"), OSS1SubCategory.WJTZ); //ignoreI18n
                    return;
                }
                List<String> headList = listener.getHeadList();
                List<List<String>> errDataList = executeImport(tenantId,finalAccountRange,listener);
                if(errDataList.isEmpty()) {
                    // 发消息 导入完成
                    fmcgPushSession.doSendSession(ea,Lists.newArrayList(userId), new SessionContentForText("考勤","自由排班导入结果","成功"), OSS1SubCategory.WJTZ); //ignoreI18n
                }else{
                    // 导出失败表
                    easyExcelHandler.pushFile(ea, userId, errDataList, headList.stream().map(Lists::newArrayList).collect(Collectors.toList()), "「按门店排班」导入结果表"); //ignoreI18n
                }
            }catch (Exception e){
                log.error("upsertUserSchedule is error",e);
                fmcgPushSession.doSendSession(ea,Lists.newArrayList(userId), new SessionContentForText("考勤","自由排班导入失败","发生未知异常,联系纷享客服"), OSS1SubCategory.WJTZ); //ignoreI18n
            }finally {
                redisUtils.clearByKey(importRedisKey);
            }
        });

        return new Result();
    }

    private List<List<String>> executeImport(String tenantId,AccountRangeOnRule.Result finalAccountRange,ReadImportScheduleByAccountListener listener){

        boolean isMn = officeShiftService.isMnPmmSchedule(serviceFacade.getEAByEI(tenantId));
        List<List<String>> dataList = listener.getDataList();
        List<String> headList = listener.getHeadList();
        Map<Integer,String> indexAndDateStrMap = Maps.newHashMap();
        for (int i = 2; i < headList.size(); i++) {
            indexAndDateStrMap.put(i, headList.get(i));
        }
        headList.add(0,"错误原因"); //ignoreI18n
        // 1.查询客户信息
        Map<String,IObjectData> accIdAndDataMap = officeShiftService.getAccIdAndNameByRuleRange(tenantId,finalAccountRange);
        Map<String,String> accIdAndNameMap = Maps.newHashMap();
//        Map<String,String> accNameAndIdMap = Maps.newHashMap();
        Map<String,String> uniqueFlagAndIdMap = Maps.newHashMap();
        List<String> repeatCustomers = Lists.newArrayList();
        List<String> accIds = Lists.newArrayList();
        for (Map.Entry<String, IObjectData> stringIObjectDataEntry : accIdAndDataMap.entrySet()) {
            accIds.add(stringIObjectDataEntry.getKey());
            accIdAndNameMap.put(stringIObjectDataEntry.getKey(),stringIObjectDataEntry.getValue().getName());
            String uniqueFlagValue = isMn ? (String)stringIObjectDataEntry.getValue().get(AccountObjConstants.Field.accountNo.getApiName()) : stringIObjectDataEntry.getValue().getName();
            if(StringUtils.isBlank(uniqueFlagValue)){
                continue;
            }
            if(uniqueFlagAndIdMap.containsKey(uniqueFlagValue)){
                repeatCustomers.add(stringIObjectDataEntry.getValue().getName());
            }
            uniqueFlagAndIdMap.put(uniqueFlagValue,stringIObjectDataEntry.getKey());
        }

        // 以下构造客户班次及其从对象信息
        Map<String,List<String>> accIdAndShiftDetailDisplayNameMap = Maps.newHashMap();
        Map<String,ObjectDataDocument> shiftIdAndDataMap = Maps.newHashMap();
        Map<String,ObjectDataDocument> shiftDetailIdAndDataMap = Maps.newHashMap();
        // key 为 客户Id_班次明细的display_name
        Map<String,String> shiftDetailDisplayNameAndIdMap = Maps.newHashMap();
        Lists.partition(accIds, 100).forEach(accountIds->{
            // 2.批量查询客户班次
            List<IObjectData> shiftDataList = accountShiftDao.getAccountShiftByAccountId(tenantId,Sets.newHashSet(accountIds));
            Map<String,String> shiftIdAndAccIdMap = Maps.newHashMap();
            Set<String> shiftIds = Sets.newHashSet();
            for (IObjectData iObjectData : shiftDataList) {
                shiftIdAndAccIdMap.put(iObjectData.getId(),iObjectData.get(AccountShiftFields.CUSTOMER).toString());
                shiftIds.add(iObjectData.getId());
                shiftIdAndDataMap.put(iObjectData.getId(), ObjectDataDocument.of(iObjectData));
            }
            Map<String,List<String>> shiftIdAndDetailDisplayNamesMap = Maps.newHashMap();
            // 3.查询客户班次从对象
            List<IObjectData> shiftDetailDataList = accountShiftDao.getAccountShiftDetailByMainIds(tenantId,shiftIds);
            for (IObjectData iObjectData : shiftDetailDataList) {
                String shiftId = iObjectData.get(AccountShiftDetailFields.ACCOUNT_SHIFT).toString();
                String displayName = iObjectData.get(AccountShiftDetailFields.DISPLAY_NAME).toString();
                shiftIdAndDetailDisplayNamesMap.computeIfAbsent(shiftId,k2->Lists.newArrayList()).add(displayName);
                shiftDetailIdAndDataMap.put(iObjectData.getId(),ObjectDataDocument.of(iObjectData));
                shiftDetailDisplayNameAndIdMap.put(shiftIdAndAccIdMap.get(shiftId) + "_" + displayName, iObjectData.getId());
            }
            shiftIdAndAccIdMap.forEach((k,v)->{
                accIdAndShiftDetailDisplayNameMap.put(v,shiftIdAndDetailDisplayNamesMap.get(k));
            });
        });

        // 4.批量查询人员
        Map<Integer,String> userIdNameMap = Maps.newHashMap();
        Lists.partition(arg.getUserIds(),100).forEach(subUserIds->{
            userIdNameMap.putAll(Optional.ofNullable(employeeDao.getUserNameByIds(tenantId,Sets.newHashSet(subUserIds))).orElse(new HashMap<>()));
        });
        Map<String,Integer> userNameAndIdMap = userIdNameMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue,Map.Entry::getKey,(k1,k2)->k1));

        // 5.获取失败的列 并且将其从dataList移除
        List<List<String>> errDataList = getErrorDataList(isMn,dataList,uniqueFlagAndIdMap,accIdAndShiftDetailDisplayNameMap,userNameAndIdMap,repeatCustomers);

        // 6.构造校验冲突参数
        Map<String/*日期*/, Map<Integer/*用户id*/, UserScheduleContent.UserSchedule>> dateUserScheduleMap = getDateUserScheduleMap(isMn,dataList,uniqueFlagAndIdMap,shiftDetailDisplayNameAndIdMap,userNameAndIdMap,indexAndDateStrMap);
        UserScheduleContent newScheduleContent = new UserScheduleContent();
        newScheduleContent.setAccountNameMap(accIdAndNameMap);
        newScheduleContent.setUserNameMap(userIdNameMap);
        newScheduleContent.setExcelAccountIds(accIds);
        newScheduleContent.setExcelUserIds(Lists.newArrayList(userIdNameMap.keySet()));
        newScheduleContent.setAccountShiftDetailMap(shiftDetailIdAndDataMap);
        newScheduleContent.setAccountShiftMap(shiftIdAndDataMap);
        newScheduleContent.setDateUserScheduleMap(dateUserScheduleMap);
        Map<String/*日期*/,Map<Integer/*用户id*/,UserScheduleContent.UserSchedule>> clashResult = officeShiftService.checkUserScheduleConflictAndFillData(tenantId,newScheduleContent,false,arg.getUserIds().stream().anyMatch(o->o.intValue() >*********));

        // 7.遍历数据 将冲突的数据加到errDataList里 并且将其从 dataList 和 dateUserScheduleMap 移除
        errDataList.addAll(removeClashLine(isMn,dataList,clashResult,uniqueFlagAndIdMap,shiftDetailDisplayNameAndIdMap,userNameAndIdMap,indexAndDateStrMap,dateUserScheduleMap));
        newScheduleContent.setDateUserScheduleMap(dateUserScheduleMap);
        // 8.执行插入逻辑
        officeShiftService.upsertUserSchedule(tenantId,newScheduleContent,60 * 30);
        return errDataList;
    }

    private List<List<String>> removeClashLine(boolean isMn,List<List<String>> dataList,Map<String/*日期*/,Map<Integer/*用户id*/,UserScheduleContent.UserSchedule>> clashResult,Map<String,String> uniqueFlagAndIdMap,
                                               Map<String,String> shiftDetailDisplayNameAndIdMap,Map<String,Integer> userNameAndIdMap,Map<Integer,String> indexAndDateStrMap,Map<String/*日期*/, Map<Integer/*用户id*/, UserScheduleContent.UserSchedule>> dateUserScheduleMap){
        List<List<String>> errDataList = Lists.newArrayList();
        if(MapUtils.isEmpty(clashResult)){
            return errDataList;
        }
        // 转换clashResult
        Map<String,Map<String,List<Integer>>> shiftDetailIdAndDateUserIdsMap = Maps.newHashMap();
        clashResult.forEach((key, value) -> {
            value.forEach((userId,schedule)->{
                for (String shiftId : schedule.getAccountShiftDetailIdList()) {
                    shiftDetailIdAndDateUserIdsMap.computeIfAbsent(shiftId,k1->Maps.newHashMap()).computeIfAbsent(key,k2->Lists.newArrayList()).add(userId);
                }
                dateUserScheduleMap.get(key).get(userId).getAccountShiftDetailIdList().removeAll(schedule.getAccountShiftDetailIdList());
            });
        });
        Iterator<List<String>> iterator = dataList.iterator();
        while (iterator.hasNext()) {
            List<String> lineData = iterator.next();
            String accId = uniqueFlagAndIdMap.get(lineData.get(isMn ? 1 : 0));
            String classId = shiftDetailDisplayNameAndIdMap.get(accId + "_" + lineData.get(isMn ? 2 : 1));
            if(!shiftDetailIdAndDateUserIdsMap.containsKey(classId)){
                continue;
            }
            Map<Integer,Set<Integer>> codeAndUserNamesMap = Maps.newHashMap();
            for (int i = isMn ? 3 : 2; i < lineData.size(); i++) {
                // 判断当前日期里面的人员是否为冲突人员
                if(StringUtils.isNotBlank(lineData.get(i))){
                    int userId = userNameAndIdMap.get(lineData.get(i));
                    String dateStr = indexAndDateStrMap.get(i);
                    if(Objects.nonNull(shiftDetailIdAndDateUserIdsMap.get(classId).get(dateStr)) &&
                        shiftDetailIdAndDateUserIdsMap.get(classId).get(dateStr).contains(userId)){
                        int errorCode = clashResult.get(dateStr).get(userId).getErrorReason();
                        codeAndUserNamesMap.computeIfAbsent(errorCode,o->Sets.newHashSet()).add(i);
                    }else{
                        lineData.set(i,"");
                    }
                }
            }
            if(MapUtils.isNotEmpty(codeAndUserNamesMap)){
                StringBuffer stringBuffer = new StringBuffer();
                for (Map.Entry<Integer, Set<Integer>> integerSetEntry : codeAndUserNamesMap.entrySet()) {
                    if(codeAndUserNamesMap.size() > 1) {
                        Set<String> errorNameSet = Sets.newHashSet();
                        stringBuffer.append(UserScheduleContent.getErrorMessage(integerSetEntry.getKey())).append(":");
                        for (Integer index : integerSetEntry.getValue()) {
                            if(!errorNameSet.contains(lineData.get(index))) {
                                errorNameSet.add(lineData.get(index));
                                stringBuffer.append(lineData.get(index)).append(",");
                            }
                        }
                        stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                        stringBuffer.append("\r\n");
                    }else{
                        stringBuffer.append(UserScheduleContent.getErrorMessage(integerSetEntry.getKey()));
                    }
                }
                lineData.add(0,stringBuffer.toString());
                errDataList.add(lineData);
                iterator.remove();
            }
        }
        return errDataList;
    }

    private Map<String/*日期*/, Map<Integer/*用户id*/, UserScheduleContent.UserSchedule>> getDateUserScheduleMap(boolean isMn,List<List<String>> dataList,Map<String,String> uniqueFlagAndIdMap,Map<String,String> shiftDetailDisplayNameAndIdMap,
                                                                                                                 Map<String,Integer> userNameAndIdMap,Map<Integer,String> indexAndDateStrMap){
        Map<String/*日期*/, Map<Integer/*用户id*/, UserScheduleContent.UserSchedule>> dateUserScheduleMap = Maps.newHashMap();
        for (List<String> lineData : dataList) {
            String accId = uniqueFlagAndIdMap.get(lineData.get(isMn ? 1 : 0));
            String classId = shiftDetailDisplayNameAndIdMap.get(accId + "_" + lineData.get(isMn ? 2 : 1));
            for (int i = isMn ? 3 : 2; i < lineData.size(); i++) {
                String userName = lineData.get(i);
                if(StringUtils.isBlank(userName)){
                    continue;
                }
                String dateStr = indexAndDateStrMap.get(i);
                int currentUserId = userNameAndIdMap.get(userName);
                // 获取这行的排班 + 当前人员
                if(Objects.isNull(dateUserScheduleMap.get(dateStr))){
                    dateUserScheduleMap.put(dateStr,Maps.newHashMap());
                }
                UserScheduleContent.UserSchedule userSchedule = dateUserScheduleMap.get(dateStr).getOrDefault(currentUserId,new UserScheduleContent.UserSchedule());
                userSchedule.setUserId(currentUserId);
                userSchedule.setDate(dateStr);
                List<String> shiftDetailIdList = Optional.ofNullable(userSchedule.getAccountShiftDetailIdList()).orElse(Lists.newArrayList());
                if(!shiftDetailIdList.contains(classId)){
                    shiftDetailIdList.add(classId);
                }
                userSchedule.setAccountShiftDetailIdList(shiftDetailIdList);
                dateUserScheduleMap.get(dateStr).put(currentUserId,userSchedule);
            }
        }
        return dateUserScheduleMap;
    }

    private List<List<String>> getErrorDataList(boolean isMn,List<List<String>> dataList, Map<String, String> uniqueFlagAndIdMap,Map<String,List<String>> accIdAndShiftDetailDisplayNameMap,
                                                Map<String,Integer> userNameAndIdMap,List<String> repeatCustomers){
        List<List<String>> errDataList = Lists.newArrayList();
        Iterator<List<String>> iterator = dataList.iterator();
        while (iterator.hasNext()) {
            List<String> lineData = iterator.next();
            if (CollectionUtils.isEmpty(lineData)) {
                iterator.remove();
                continue;
            }
            String accountId = uniqueFlagAndIdMap.get(lineData.get(isMn ? 1 : 0));
            if (StringUtils.isBlank(lineData.get(0)) || StringUtils.isBlank(accountId)) {
                lineData.add(0,"规则设置的客户范围不包含该客户"); //ignoreI18n
                errDataList.add(lineData);
                iterator.remove();
                continue;
            }
            if(repeatCustomers.contains(lineData.get(0))){
                lineData.add(0,"在该规则中存在同名客户,请处理后在进行导入"); //ignoreI18n
                errDataList.add(lineData);
                iterator.remove();
                continue;
            }
            if (StringUtils.isBlank(lineData.get(isMn ? 2 : 1)) || !Optional.ofNullable(accIdAndShiftDetailDisplayNameMap.get(accountId)).orElse(Lists.newArrayList()).contains(lineData.get(isMn ? 2 : 1))) {
                lineData.add(0,"客户没有对应班次"); //ignoreI18n
                errDataList.add(lineData);
                iterator.remove();
                continue;
            }
            Set<String> errorUserNameSet = Sets.newHashSet();
            for (int i = isMn ? 3 : 2; i < lineData.size(); i++) {
                String userName = lineData.get(i);
                if (StringUtils.isNotBlank(userName)) {
                    if (!userNameAndIdMap.containsKey(userName)) {
                        errorUserNameSet.add(userName);
                        break;
                    }
                }
            }
            if(!errorUserNameSet.isEmpty()){
                List<String> newLineData = Lists.newArrayList();
                StringBuffer errUser = new StringBuffer();
                for (int i = 0; i < lineData.size(); i++) {
                    String newLineDatum = lineData.get(i);
                    if(i < (isMn ? 3 : 2)){
                        newLineData.add(newLineDatum);
                    }else {
                        if (StringUtils.isNotBlank(newLineDatum) && errorUserNameSet.contains(newLineDatum)) {
                            if (!errUser.toString().contains(newLineDatum)) {
                                errUser.append(newLineDatum).append(",");
                            }
                            lineData.set(i,"");
                            newLineData.add(newLineDatum);
                        } else {
                            newLineData.add("");
                        }
                    }
                }
                errUser.deleteCharAt(errUser.length() - 1);
                newLineData.add(0,"该规则不适用于人员【" + errUser + "】"); //ignoreI18n
                errDataList.add(newLineData);
                iterator.remove();
            }
        }
        return errDataList;
    }

    @Data
    public static class Arg {
        /**
         * npath
         */
        private String npath;

        private String ruleId;

        public int isOuter;

        public List<Integer> userIds;

    }
    public static class Result {

    }
}
