package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;


@Slf4j
public class UserScheduleDetailInvalidAction extends StandardInvalidAction {
    @Override
    protected void before(Arg arg) {
        //不支持客户班次从对象直接编辑
        throw new ValidateException("不支持从对象直接操作"); //ignoreI18n
    }
}
