package com.facishare.crm.fmcg.wq.controller;

import com.facishare.paas.appframework.core.predef.controller.StandardImportObjectController;
import com.facishare.paas.appframework.metadata.importobject.ImportType;

/**
 *
 */
public class AccountShiftImportObjectController extends StandardImportObjectController {
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        after.getImportObject().setSupportType(ImportType.UNSUPPORT_UPDATE_IMPORT);
        return after;
    }
}