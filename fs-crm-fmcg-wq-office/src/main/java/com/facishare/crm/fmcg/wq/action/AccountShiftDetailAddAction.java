package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import org.apache.commons.collections.ListUtils;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-23 10:13
 **/
public class AccountShiftDetailAddAction extends StandardAddAction {
    @Override
    protected void before(Arg arg) {
        //不支持客户班次从对象直接编辑
        throw new ValidateException("不支持客户班次从对象直接操作"); //ignoreI18n
    }

}
