package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class UserScheduleBulkInvalidAction extends StandardBulkInvalidAction {
    @Override
    protected void before(Arg arg) {
        //不支持作废
        throw new ValidateException("不允许作废用户排班数据,删除用户排班可以通过编辑删除用户排班详情对象实现"); //ignoreI18n
    }
}
