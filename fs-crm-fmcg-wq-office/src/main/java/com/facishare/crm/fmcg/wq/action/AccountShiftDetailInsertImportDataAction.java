package com.facishare.crm.fmcg.wq.action;

import cn.hutool.core.convert.Convert;
import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.crm.fmcg.wq.constants.AttendanceCorrectFields;
import com.facishare.crm.fmcg.wq.constants.OfficeShiftConstants;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 客户班次新建导入
 * 真正导入数据的业务处理逻辑，输入的是从excel中读出的一批数据行，返回有错误的行号和对应的业务校验失败的原因。
 * <p>
 * 注意这个action要注意异常处理，按规则返回结果，否则在导入的结果中无法正确
 * <p>
 * 整个的处理过程分为字段转换和初始化->校验字段值→对合法的数据做业务的默认值处理→写入数据库 共4个大的阶段
 * <p>
 * 这几个阶段也对应了4个业务的扩展点
 * <p>
 * 1 com.facishare.paas.appframework.core.predef.action.BaseImportDataAction#customInit（）
 * <p>
 * 对应数据字段的转换和初始化
 * <p>
 * 2 com.facishare.paas.appframework.core.predef.action.BaseImportDataAction#customValidate（）
 * <p>
 * 对应数据校验
 * <p>
 * 3 com.facishare.paas.appframework.core.predef.action.BaseImportDataAction#customDefaultValue（）
 * <p>
 * 对应业务默认值赋值
 * <p>
 * 4 com.facishare.paas.appframework.core.predef.action.BaseImportDataAction#importData（）
 * <p>
 * 对应写入数据
 * <p>
 * 除了这些大阶段的扩展点，还有每个阶段一些功能的扩展，这个可以看代码按需使用
 * @author: zhangsm
 * @create: 2024-02-20 14:37
 **/
public class AccountShiftDetailInsertImportDataAction extends StandardInsertImportDataAction {

    Map<String,List<IObjectData>> dbDetailMap;
    List<IObjectData> dbUpdateNumDetailList = Lists.newArrayList();
    AccountShiftDao accountShiftDao = SpringUtil.getContext().getBean(AccountShiftDao.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected void customInit(List<ImportData> dataList) {
        //开始时间 结束时间 必填
        objectDescribeExt.getFieldDescribeSilently(AccountShiftDetailFields.START_TIME).ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(true));
        objectDescribeExt.getFieldDescribeSilently(AccountShiftDetailFields.END_TIME).ifPresent(iFieldDescribe -> iFieldDescribe.setRequired(true));
        super.customInit(dataList);
    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        //查询已有数据
        dbDetailMap = accountShiftDao.getAccountShiftDetailByMainIds(actionContext.getTenantId(),dataList.stream().map(data -> data.getData().get(AccountShiftDetailFields.ACCOUNT_SHIFT, String.class)).collect(Collectors.toSet()))
                .stream().collect(Collectors.groupingBy(i->i.get(AccountShiftDetailFields.ACCOUNT_SHIFT, String.class)));
        //开始时间＜结束时间
        validateTime(dataList);
        //客户班次详情从对象最大5个
        validateDetailMaxLimit(dataList);
    }

    private void validateTime(List<ImportData> dataList) {
        //开始时间＜结束时间
        List<ImportError> errorList = Lists.newArrayList();
        dataList.forEach(data -> {
            if (StringUtils.isBlank(data.getErrorMessage())){
                Long startTime = Convert.toLong(data.getData().get(AccountShiftDetailFields.START_TIME));
                Long endTime = Convert.toLong(data.getData().get(AccountShiftDetailFields.END_TIME));
                if (startTime != 0 && endTime != 0 && startTime > endTime) {
                    errorList.add(new ImportError(data.getRowNo(), "开始时间不能大于结束时间")); //ignoreI18n
                }
            }
        });
        mergeErrorList(errorList);

    }

    /**
     * 按照主对象ID过滤数据超过最大班次5个
     */
    protected void validateDetailMaxLimit(List<ImportData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<ImportError> errorList = Lists.newArrayList();
        //dataList按照主对象ID分组
        dataList.stream().collect(Collectors.groupingBy(data -> data.getData().get(AccountShiftDetailFields.ACCOUNT_SHIFT, String.class))).forEach((mainId, accountDetails) -> {
            //已有数据量
            int dbDetailSize = dbDetailMap.getOrDefault(mainId, ListUtils.EMPTY_LIST).size();
            //已有数据+导入数据 >
            if(dbDetailSize + accountDetails.size() > OfficeShiftConstants.MAX_ACCOUNT_SHIFT_DETAIL){
                accountDetails.forEach(data -> errorList.add(new ImportError(data.getRowNo(), MessageFormat.format("每个门店最多设置{0}个班次", OfficeShiftConstants.MAX_ACCOUNT_SHIFT_DETAIL)))); //ignoreI18n
            }
        });
        mergeErrorList(errorList);
    }

    @Override
    protected void customDefaultValue(List<IObjectData> validList) {
        super.customDefaultValue(validList);
        //按照住对象id 分组 重新编号
        validList.stream().collect(Collectors.groupingBy(data -> data.get(AccountShiftDetailFields.ACCOUNT_SHIFT, String.class))).forEach((mainId, accountDetails) -> {
            //已有数据list
            List<IObjectData> dbDetailList = dbDetailMap.getOrDefault(mainId, ListUtils.EMPTY_LIST);
            Set<String> existIds = dbDetailList.stream().map(i -> i.getId()).collect(Collectors.toSet());
            //构造新的list db数据按照编号插入到新数据里面，重新编号保证db数据编号不变
            List<IObjectData> allDetailList = Lists.newArrayList();
            allDetailList.addAll(dbDetailList);
            allDetailList.addAll(accountDetails);
            //重新编号
            for (int i = 0; i < allDetailList.size(); i++) {
                IObjectData iObjectData = allDetailList.get(i);
                //是db数据
                if (existIds.contains(iObjectData.getId())){
                    int newNo = i + 1;
                    //旧编号和新编号不一样
                    if (iObjectData.get(AccountShiftDetailFields.SHIFT_NO, Integer.class) != newNo){
                        iObjectData.set(AccountShiftDetailFields.SHIFT_NO, newNo);
                        dbUpdateNumDetailList.add(iObjectData);
                    }
                }else{
                    //是新数据
                    iObjectData.set(AccountShiftDetailFields.SHIFT_NO, i + 1);
                }

            }
        });
    }

    @Override
    protected List<IObjectData> importData(List<IObjectData> validList) {
        //validList 按照主id 分组批量保存处理

        List<IObjectData> iObjectData = super.importData(validList);
        //更新编号
        if (!CollectionUtils.empty(dbUpdateNumDetailList)){
            accountShiftDao.batchUpdate(dbUpdateNumDetailList,actionContext.getUser());
        }
        return iObjectData;
    }
}
