package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.IdempotentRequestContext;
import com.facishare.idempotent.RequestId;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 客户视图排班表批量添加接口
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/
public class UserScheduleBatchAddByAccountController extends FmcgIdempotentPreDefineController<UserScheduleBatchAddByAccountController.Arg, UserScheduleBatchAddByAccountController.Result> {
    OfficeShiftService officeShiftService = SpringUtil.getContext().getBean(OfficeShiftService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doServiceIdempotent(Arg arg) {
        List<UserScheduleContent.ShiftSchedule> shiftScheduleList = arg.getShiftScheduleList();
        String todayStr = LocalDate.now().toString();
        for (UserScheduleContent.ShiftSchedule shiftSchedule : shiftScheduleList) {
            //日期必填
            if (StringUtils.isBlank(shiftSchedule.getDate())) {
                throw new ValidateException("排班日期必填"); //ignoreI18n
            }
            //客户班次详情id 必填
            if (StringUtils.isBlank(shiftSchedule.getAccountShiftDetailId())) {
                throw new ValidateException("班次详情id必填"); //ignoreI18n
            }
            if (shiftSchedule.getDate().compareTo(todayStr) < 0) {
                throw new ValidateException("排班日期必须是今日之后"); //ignoreI18n
            }
        }

        UserScheduleContent newScheduleContent = new UserScheduleContent();
        //Map<String/*日期*/, Map<String/*客户班次详情id*/, ShiftShcedule>> 二级的map
        Map<String, Map<String, UserScheduleContent.ShiftSchedule>> shiftScheduleMap = shiftScheduleList.stream().collect(Collectors.groupingBy(UserScheduleContent.ShiftSchedule::getDate, Collectors.toMap(UserScheduleContent.ShiftSchedule::getAccountShiftDetailId, o -> o, (o, o2) -> o)));
        newScheduleContent.setDateShiftDetailScheduleMap(shiftScheduleMap);
        //check
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> stringMapMap = officeShiftService.checkUserScheduleConflictAndFillData(controllerContext.getTenantId(), newScheduleContent,true
        ,arg.getIsOuter() == 1);
        if (!stringMapMap.isEmpty()) {
            //本次变更会导致 xxx日期，xxx,xxx用户排班冲突
            StringBuilder sb = new StringBuilder();
            sb.append("操作失败 : "); //ignoreI18n
            //按照错误类型重新聚合
            stringMapMap.entrySet().stream().flatMap(o -> o.getValue().entrySet().stream()).collect(Collectors.groupingBy(o -> o.getValue().getErrorReason())).entrySet().stream().forEach(o -> {
                //按照日期聚合
                o.getValue().stream().collect(Collectors.groupingBy(o1 -> o1.getValue().getDate())).entrySet().stream().forEach(o2 -> {
                    sb.append(o2.getKey()).append(":");
                    sb.append(o2.getValue().stream().map(o1 ->newScheduleContent.getUserNameMap().getOrDefault(o1.getKey(), o1.getKey().toString())).collect(Collectors.joining(",")));
                });
                sb.append(UserScheduleContent.getErrorMessage(o.getKey())).append(";");
            });
            throw new ValidateException(sb.toString());
        }
        //upsert
        officeShiftService.upsertUserSchedule(controllerContext.getTenantId(), newScheduleContent, 10);
        return new Result(newScheduleContent);
    }

    @Override
    protected String getIdempotentKey(Arg arg) {
        return controllerContext.getTenantId() + "UserScheduleBatchAdd" + JSON.toJSONString(arg).hashCode();
    }

    @Data
    public static class Arg {
        /**
         * 用户排班信息
         */
//        private List<UserScheduleContent.UserSchedule> userScheduleSimpleList;

        private List<UserScheduleContent.ShiftSchedule> shiftScheduleList;

        int isOuter;


    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private UserScheduleContent userScheduleContent;

    }
}
