package com.facishare.crm.fmcg.wq.action;
import com.alibaba.fastjson.JSON;
import com.facishare.appserver.checkinsoffice.api.model.NumInfo;
import com.facishare.appserver.checkinsoffice.api.model.ScheduleDate;
import com.facishare.appserver.checkinsoffice.api.model.ScheduleDetail;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.proxy.CheckinsOfficeProxy;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Slf4j
public class ShiftsAddAction extends StandardAddAction {

    private CheckinsOfficeProxy checkinsOfficeProxy = SpringUtil.getContext().getBean(CheckinsOfficeProxy.class);


    @Override
    protected void before(Arg arg) {
        validateDetailData(arg);
        super.before(arg);
    }

    private void validateDetailData(Arg arg) {

        //User{tenantId=79525, userId=null, outUserId=300097585, outTenantId=300015919, upstreamOwnerId=1000}
        String eId= actionContext.getTenantId();
        Integer outUserId;
        if(Objects.nonNull(actionContext.getUser().getOutUserId())) {
            outUserId= Integer.valueOf(actionContext.getUser().getOutUserId());
        }else{
            outUserId= Integer.valueOf(actionContext.getUser().getUserId());
        }
        String upstreamOwnerId=actionContext.getUpstreamOwnerId();
        ScheduleDetail scheduleDetail=new ScheduleDetail();
        Long dateTs = Long.parseLong(arg.getObjectData().get("apply_date").toString());
        String dateStr= DateUtils.getStringFromTime(dateTs,"yyyy-MM-dd");

        //校验不能重复
        if(!CollectionUtils.isEmpty(arg.getDetails())) {
            if (!CollectionUtils.isEmpty(arg.getDetails().get("ShiftAdjustmentObj"))) {
                if(arg.getDetails().get("ShiftAdjustmentObj").size()>10){
                    throw new ValidateException("每天的班次不能超过10个"); //ignoreI18n
                }
                List<ObjectDataDocument> shiftsAdjustDetails= arg.getDetails().get("ShiftAdjustmentObj");
                if(!CollectionUtils.isEmpty(shiftsAdjustDetails)){
                    ScheduleDate scheduleDate=new ScheduleDate();
                    scheduleDate.setDateTs(dateTs);
                    scheduleDate.setDateStr(dateStr);
                    List<Integer> allNums=Lists.newArrayList();
                    for(ObjectDataDocument o:shiftsAdjustDetails) {
                        if(Objects.isNull(o.get("nums"))){
                            throw new ValidateException("调班不能为空"); //ignoreI18n
                        }
                        Integer num = Integer.valueOf(o.get("nums").toString());
                        if(allNums.contains(num)){
                            throw new ValidateException("提交的班次有重复,请重复选择"); //ignoreI18n
                        }
                        allNums.add(num);

                        NumInfo numInfo = new NumInfo();
                        numInfo.setNum(num);
                        if(Objects.nonNull(o.get("customer_id"))) {
                            numInfo.setCustomerId(o.get("customer_id").toString());
                            if(Objects.nonNull(o.get("customer_id__r"))) {
                                numInfo.setCustomerName(o.get("customer_id__r").toString());
                            }else{
                                List<IObjectData> customInfo = serviceFacade.findObjectDataByIds(actionContext.getUser().getTenantId(), Lists.newArrayList(o.get("customer_id").toString()), "AccountObj");
                                if(CollectionUtils.isEmpty(customInfo)){
                                    log.error("customer_id__r and customInfo is null");
                                }else {
                                    numInfo.setCustomerName(customInfo.get(0).getName());
                                }
                            }
                        }
                        if (CollectionUtils.isEmpty(scheduleDate.getNums())) {
                            scheduleDate.setNums(Lists.newArrayList(num));
                            scheduleDate.setNumInfos(Lists.newArrayList(numInfo));
                        } else {
                            scheduleDate.getNums().add(num);
                            scheduleDate.getNumInfos().add(numInfo);
                        }
                    }
                    if(!CollectionUtils.isEmpty(scheduleDate.getNumInfos())){
                        //正序排序
                        Collections.sort(scheduleDate.getNumInfos(), new Comparator<NumInfo>()
                        {
                            public int compare(NumInfo a1, NumInfo a2)
                            {
                                return a1.getNum() - a2.getNum();
                            }
                        });
                        Collections.sort(scheduleDate.getNums());//正序排序
                    }
                    scheduleDetail.setScheduleDates(Lists.newArrayList(scheduleDate));
                    scheduleDetail.setUserId(outUserId);
                    log.info(JSON.toJSONString(scheduleDetail));
                    String resultStr=checkinsOfficeProxy.validateScheduleInfo(eId,outUserId.toString(),upstreamOwnerId,scheduleDetail);
                    if(Strings.isNotEmpty(resultStr)){
                        throw new ValidateException(resultStr);
                    }
                }else{
                    throw new ValidateException("调班不能为空"); //ignoreI18n
                }
            }
        }else{
            throw new ValidateException("调班不能为空"); //ignoreI18n
        }

    }

}


