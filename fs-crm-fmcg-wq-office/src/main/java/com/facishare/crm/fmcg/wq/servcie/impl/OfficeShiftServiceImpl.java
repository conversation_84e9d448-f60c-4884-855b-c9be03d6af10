package com.facishare.crm.fmcg.wq.servcie.impl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.appserver.checkins.api.service.ObjUpdateService;
import com.facishare.appserver.checkins.model.enums.ObjApiName;
import com.facishare.appserver.checkinsoffice.api.model.AccountRangeOnRule;
import com.facishare.appserver.checkinsoffice.api.model.PaasIdAndName;
import com.facishare.appserver.checkinsoffice.api.model.UserSchedule;
import com.facishare.appserver.checkinsoffice.api.service.CheckinsOfficeV2Service;
import com.facishare.appserver.utils.EnvUtils;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.proxy.CheckinsOfficeProxy;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.Wheres;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Maps;

import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.facishare.crm.fmcg.wq.dao.UserScheduleDao;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.SetUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.collections.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-14 19:01
 *
 * @eo.api-type http
 * @eo.groupName 默认分组
 * @eo.path*/
@Slf4j
@Component
public class OfficeShiftServiceImpl implements OfficeShiftService {

    @Autowired
    AccountShiftDao accountShiftDao;
    @Autowired
    UserScheduleDao userScheduleDao;
    @Autowired
    EmployeeDao employeeDao;
    @Autowired
    CheckinsOfficeV2Service checkinsOfficeV2Service;
    @Autowired
    BaseDao baseDao;
    @Autowired
    EIEAConverter eieaConverter;
    @Autowired
    ObjUpdateService objUpdateService;

    @Autowired
    private CheckinsOfficeProxy checkinsOfficeProxy;

    /**
     * @eo.name syncUserScheduleToStat
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param tenantId
     * @param newScheduleContent
     * @return void
     */
    @Override
    public void syncUserScheduleToStat(String tenantId, UserScheduleContent newScheduleContent) {
        checkinsOfficeV2Service.syncStatEntities(eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId)), convertUserSchedule(newScheduleContent,Boolean.TRUE));
    }
    private List<UserSchedule> convertUserSchedule(UserScheduleContent userScheduleContent,boolean filterChange) {
        List<UserSchedule> userSchedulelist = Lists.newArrayList();
        if (MapUtils.isEmpty(userScheduleContent.getDateUserScheduleMap())){
            return userSchedulelist;
        }
        userScheduleContent.getDateUserScheduleMap().forEach((date, value) -> value.forEach((userId, contentUserSchedule) -> {
            if (!filterChange || contentUserSchedule.isChange()){
                UserSchedule userSchedule = new UserSchedule();
                userSchedule.setDate(date);
                userSchedule.setUserId(userId);
                userSchedule.setScheduleDetailList(Lists.newArrayList());
                for (String accountDetailId : contentUserSchedule.getAccountShiftDetailIdList()) {
                    UserSchedule.UserScheduleDetail userScheduleDetail = new UserSchedule.UserScheduleDetail();
                    userScheduleDetail.setAccountShiftDetailId(accountDetailId);
                    IObjectData iObjectData = userScheduleContent.getAccountShiftDetailMap().get(accountDetailId).toObjectData();
                    userScheduleDetail.setAccountShiftDetailName(iObjectData.get("name", String.class));
                    //获取班次详情对象
                    String accountShiftId = iObjectData.get("account_shift", String.class);
                    IObjectData accountShift = userScheduleContent.getAccountShiftMap().get(accountShiftId).toObjectData();
                    String accountId = accountShift.get("customer", String.class);
                    userScheduleDetail.setAccountId(accountId);
                    userScheduleDetail.setAccountName(userScheduleContent.getAccountNameMap().get(accountId));
                    userScheduleDetail.setStartTime(iObjectData.get("start_time", Long.class));
                    userScheduleDetail.setAccountShiftNo(iObjectData.get("shift_no",Integer.class,0));
                    userScheduleDetail.setEndTime(iObjectData.get("end_time", Long.class));
                    userSchedule.getScheduleDetailList().add(userScheduleDetail);
                }
                userSchedulelist.add(userSchedule);
            }
        }));
        return userSchedulelist;
    }


    /**
     * @eo.name checkUserScheduleConflictAndFillData
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param tenantId
     * @param newScheduleContent
     * @param throwException
     * @param isOuter
     * @return Map
     */
    @Override
    public Map<String/*日期*/,Map<Integer/*用户id*/,UserScheduleContent.UserSchedule>> checkUserScheduleConflictAndFillData(String tenantId, UserScheduleContent newScheduleContent,boolean throwException,boolean isOuter) {
        Map<String/*日期*/,Map<Integer/*用户id*/,UserScheduleContent.UserSchedule>> conflictUserSchedule = Maps.newHashMap();
        //填充数据
        fillDateShiftDetailMap(tenantId, newScheduleContent,isOuter);
        //check 日期 只能修改今日以及今日以后得数据
        if (throwException){
            newScheduleContent.getDateUserScheduleMap().keySet().forEach(date -> {
                if (date.compareTo(DateUtils.getStringFromTime(System.currentTimeMillis(),DateUtils.DateFormat)) < 0) {
                    throw new ValidateException("只能修改今日以及今日以后的排班数据"); //ignoreI18n
                }
                //客户班次详情id重复
                newScheduleContent.getDateUserScheduleMap().get(date).values().forEach(userSchedule -> {
                    if (CollectionUtils.isNotEmpty(userSchedule.getAccountShiftDetailIdList())) {
                        //判断list 有重复数据
                        Set<String> accountShiftDetailIdSet = Sets.newHashSet(userSchedule.getAccountShiftDetailIdList());
                        if (accountShiftDetailIdSet.size() != userSchedule.getAccountShiftDetailIdList().size()) {
                            throw new ValidateException("排班客户班次重复"); //ignoreI18n
                        }
                        //大于最大值
                        if (accountShiftDetailIdSet.size() > OfficeShiftConstants.MAX_USER_SCHEDULE_DETAIL){
                            throw new ValidateException(MessageFormat.format("用户排班超过最大数据{0}个", OfficeShiftConstants.MAX_USER_SCHEDULE_DETAIL)); //ignoreI18n
                        }
                    }
                });
            });
        }
        //填充用户名
        fillEmployeeName(tenantId,newScheduleContent);
        //填充客户班次
        fillAccountShiftDetail(tenantId, newScheduleContent,true);
        //用户班次时间有交集判断
        newScheduleContent.getDateUserScheduleMap().entrySet().forEach(entry-> {
            String date = entry.getKey();
            Map<Integer,UserScheduleContent.UserSchedule> userScheduleMap = entry.getValue();
            Set<Integer> userIds = userScheduleMap.keySet();
            if (!throwException && date.compareTo(DateUtils.getStringFromTime(System.currentTimeMillis(),DateUtils.DateFormat)) < 0) {
                userScheduleMap.values().forEach(userSchedule -> {
                    userSchedule.setErrorReason(UserScheduleContent.SCHEDULE_DATE_ERROR);
                    conflictUserSchedule.computeIfAbsent(date, o->Maps.newHashMap()).computeIfAbsent(userSchedule.getUserId(), o->userSchedule);
                });
                return;
            }
            //查规则
            Map<Integer, Long> ruleMinScheduleTime = checkinsOfficeV2Service.getRuleMinScheduleTime(eieaConverter.enterpriseIdToAccount(Integer.valueOf(tenantId)), Lists.newArrayList(userIds));
            Iterator<Map.Entry<Integer, UserScheduleContent.UserSchedule>> iterator = userScheduleMap.entrySet().iterator();
            while (iterator.hasNext()){
                Map.Entry<Integer, UserScheduleContent.UserSchedule> next = iterator.next();
                UserScheduleContent.UserSchedule userSchedule = next.getValue();
                if (CollectionUtils.isNotEmpty(userSchedule.getAccountShiftDetailIdList())){
                    //构建时间组数据
                    List<Long[]> timeSlots = Lists.newArrayList();
                    for (String accountShiftDetailId : userSchedule.getAccountShiftDetailIdList()) {
                        IObjectData accountShiftDetailObj = newScheduleContent.getAccountShiftDetailMap().get(accountShiftDetailId).toObjectData();
                        Long startTime = accountShiftDetailObj.get(AccountShiftDetailFields.START_TIME, Long.class);
                        Long endTime = accountShiftDetailObj.get(AccountShiftDetailFields.END_TIME, Long.class);
                        if (startTime > endTime){
                            //次日打卡
                            endTime += DateUtils.ONE_DAY;
                        }
                        timeSlots.add(new Long[]{startTime, endTime});
                    }
                    if (!throwException){
                        if (CollectionUtils.isNotEmpty(userSchedule.getAccountShiftDetailIdList())) {
                            //判断list 有重复数据
                            Set<String> accountShiftDetailIdSet = Sets.newHashSet(userSchedule.getAccountShiftDetailIdList());
                            if (accountShiftDetailIdSet.size() != userSchedule.getAccountShiftDetailIdList().size()) {
                                userSchedule.setErrorReason(UserScheduleContent.SCHEDULE_ACCOUNT_SHIFT_DETAIL_REPEAT);
                                conflictUserSchedule.computeIfAbsent(date, o->Maps.newHashMap()).computeIfAbsent(userSchedule.getUserId(), o->userSchedule);
                                continue;
                            }
                            //大于最大值
                            if (accountShiftDetailIdSet.size() > OfficeShiftConstants.MAX_USER_SCHEDULE_DETAIL){
                                userSchedule.setErrorReason(UserScheduleContent.SCHEDULE_ACCOUNT_SHIFT_DETAIL_MAX);
                                conflictUserSchedule.computeIfAbsent(date, o->Maps.newHashMap()).computeIfAbsent(userSchedule.getUserId(), o->userSchedule);
                                continue;
                            }
                        }
                    }
                    //判断超过24h
                    if (checkTimeSlotsOver24h(timeSlots)) {
                        userSchedule.setErrorReason(UserScheduleContent.SCHEDULE_OVER_24H);
                        conflictUserSchedule.computeIfAbsent(date, o->Maps.newHashMap()).computeIfAbsent(userSchedule.getUserId(), o->userSchedule);
                        continue;
                    }
                    //取所有的判断交集
                    if (checkTimeSlotsConflict(timeSlots)) {
                        userSchedule.setErrorReason(UserScheduleContent.SCHEDULE_TIME_CONFLICT);
                        conflictUserSchedule.computeIfAbsent(date, o->Maps.newHashMap()).computeIfAbsent(userSchedule.getUserId(),o-> userSchedule);
                        continue;
                    }


                    //判断 工作时长是否满足最小工作时长 这个排班时长不足的验证必须放到最后 因为 有种检测会过滤掉这种错误
                    if (checkRuleMinTimeInsufficient(timeSlots, ruleMinScheduleTime.getOrDefault(userSchedule.getUserId(),DateUtils.ONE_DAY))) {
                        userSchedule.setErrorReason(UserScheduleContent.SCHEDULE_TIME_INSUFFICIENT);
                        conflictUserSchedule.computeIfAbsent(date, o->Maps.newHashMap()).computeIfAbsent(userSchedule.getUserId(), o->userSchedule);
                        continue;
                    }
                }
            }
        });
        newScheduleContent.setDateShiftDetailScheduleMap(convertDateShiftDetailScheduleMap(newScheduleContent));
        return conflictUserSchedule;

    }

    /**
     * 数据转化 填充
     * @param tenantId
     * @param newScheduleContent
     * @eo.name 数据转化 填充
     * @eo.url null
     * @eo.method get
     * @eo.request-type formdata
     * @param isOuter
     */
    @Override
    public void fillDateShiftDetailMap(String tenantId, UserScheduleContent newScheduleContent,boolean isOuter) {
        //填充数据
        if (MapUtils.isEmpty(newScheduleContent.getDateUserScheduleMap()) && MapUtils.isNotEmpty(newScheduleContent.getDateShiftDetailScheduleMap())) {
            Map<String/*日期*/, Map<Integer/*用户id*/, UserScheduleContent.UserSchedule>> dateUserScheduleMap = Maps.newConcurrentMap();
            newScheduleContent.setDateUserScheduleMap(dateUserScheduleMap);
            //转数据
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            newScheduleContent.getDateShiftDetailScheduleMap().forEach((date, shiftScheduleMap) -> parallelTask.submit(() -> {
                try {
                    if (MapUtils.isNotEmpty(shiftScheduleMap)) {
                        //聚合门店班次详情的id
                        List<String> accountShiftDetailIds = shiftScheduleMap.values().stream().map(o -> o.getAccountShiftDetailId()).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                        //按照人员id 构造map
                        Map<Integer, List<String>> argUserIdAccountShiftDetailIdMap = Maps.newHashMap();
                        shiftScheduleMap.forEach((accountShiftDetailId, shiftSchedule) -> {
                            for (UserScheduleContent.ShiftScheduleUser userId : shiftSchedule.getUserIds()) {
                                argUserIdAccountShiftDetailIdMap.computeIfAbsent(userId.getUserId(), o -> Lists.newArrayList()).add(accountShiftDetailId);
                            }
                        });
                        //查询参数人的班次
                        List<IObjectData> partUserScheduleList = userScheduleDao.getUserScheduleByDateAndUserIds(tenantId, date, argUserIdAccountShiftDetailIdMap.keySet());
                        //查询现有的用户班次详情
                        List<IObjectData> partUserScheduleDetailList = userScheduleDao.getUserScheduleDetailByDateAndShiftDetailIds(tenantId,date, accountShiftDetailIds);
                        //根据所有的用户班次主对象查询所有的详情对象
                        List<String> mainIds = partUserScheduleDetailList.stream().map(o -> o.get(UserScheduleDetailFields.USER_SCHEDULE, String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                        mainIds = ListUtils.sum(mainIds,partUserScheduleList.stream().map(o -> o.getId()).collect(Collectors.toList()));
                        //按照人员id分组
                        Map<Integer, List<IObjectData>> dbUserScheduleMap = userScheduleDao.getByIds(tenantId,UserScheduleFields.API_NAME,mainIds).stream().collect(Collectors.groupingBy(o -> o.get(UserScheduleFields.USER_ID, Integer.class)));
                        //按照主id 分组
                        Map<String, List<String/*accountDetailId*/>> dbUserScheduleDetailMap = userScheduleDao.getUserScheduleDetailByMainIds(tenantId, mainIds).stream().collect(Collectors.groupingBy(o -> o.get(UserScheduleDetailFields.USER_SCHEDULE, String.class), Collectors.mapping(o -> o.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL,String.class), Collectors.toList())));
                        //聚合参数的所有人员 和 dbUserShcduleMap的所有人员
                        Set<Integer> allUserIds = Sets.newHashSet(dbUserScheduleMap.keySet());
                        allUserIds.addAll(argUserIdAccountShiftDetailIdMap.keySet());
                        //循环构造以人员为key的map数据
                        Map<Integer/*用户id*/, UserScheduleContent.UserSchedule> userScheduleMap = Maps.newHashMap();
                        //构造userScheduleMap
                        for (Integer userId : allUserIds) {
                            //增加内外部隔离
                            if (isOuter && userId.intValue() < *********){
                                continue;
                            }
                            if (!isOuter && userId.intValue() > *********){
                                continue;
                            }
                            UserScheduleContent.UserSchedule userSchedule = new UserScheduleContent.UserSchedule();
                            userSchedule.setUserId(userId);
                            userSchedule.setDate(date);
                            List<String> accountShiftDetailIdList = Lists.newArrayList();
                            if (CollectionUtils.isNotEmpty(dbUserScheduleMap.get(userId))){
                                for (IObjectData objectData : dbUserScheduleMap.get(userId)) {
                                    if (CollectionUtils.isNotEmpty(dbUserScheduleDetailMap.get(objectData.getId()))) {
                                        accountShiftDetailIdList.addAll(dbUserScheduleDetailMap.get(objectData.getId()));
                                    }
                                }
                            }
                            accountShiftDetailIdList.removeAll(shiftScheduleMap.keySet());
                            if (CollectionUtils.isNotEmpty(argUserIdAccountShiftDetailIdMap.get(userId))) {
                                accountShiftDetailIdList.addAll(argUserIdAccountShiftDetailIdMap.get(userId));
                            }
                            userSchedule.setAccountShiftDetailIdList(accountShiftDetailIdList.stream().distinct().collect(Collectors.toList()));
                            userScheduleMap.put(userId, userSchedule);
                        }
                        dateUserScheduleMap.put(date, userScheduleMap);
                    }
                } catch (Exception e) {
                    log.error("fillDateShiftDetailMap error! tenantId:{},shiftScheduleMap:{}", tenantId, shiftScheduleMap, e);
                }
            }));
            try {
                //最大600s
                boolean success = parallelTask.await(5L, TimeUnit.SECONDS);
                if (!success) {
                    log.error("fillDateShiftDetailMap failed! ei:{}", tenantId);
                    throw new ValidateException("获取用户全量排班"); //ignoreI18n
                }
            } catch (TimeoutException e) {
                log.error("fillDateShiftDetailMap time out! ei:{}", tenantId, e);
                throw new ValidateException("获取用户全量排班"); //ignoreI18n
            }

        }
    }

    private boolean checkRuleMinTimeInsufficient(List<Long[]> timeSlots, Long minTime) {
        //循环算每个数据的时间 累加在一起 小于 minTime返回 true
        long sum = 0;
        for (Long[] timeSlot : timeSlots) {
            sum += timeSlot[1] - timeSlot[0];
        }
        return sum < minTime;
    }

    /**
     * 判断时间冲突
     * @param timeSlots
     * @return
     * @eo.name 判断时间冲突
     * @eo.url null
     * @eo.method get
     * @eo.request-type formdata
     */
    public static boolean checkTimeSlotsConflict(List<Long[]> timeSlots) {
        for (int i = 0; i < timeSlots.size(); i++) {
            for (int j = i + 1; j < timeSlots.size(); j++) {
                if (timeSlots.get(i)[1] >= timeSlots.get(j)[0] &&
                        timeSlots.get(j)[1] >= timeSlots.get(i)[0]) {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * 判断时间跨度超过24h
     * @eo.name 判断时间跨度超过24h
     * @eo.url null
     * @eo.method get
     * @eo.request-type formdata
     * @param timeSlots
     */
    public static boolean checkTimeSlotsOver24h(List<Long[]> timeSlots) {
      //用时间组结束时间的最大值减去开始时间的最小值 超过24h的ms值 返回 true
        long max = timeSlots.stream().mapToLong(timeSlot -> timeSlot[1]).max().getAsLong();
        long min = timeSlots.stream().mapToLong(timeSlot -> timeSlot[0]).min().getAsLong();
        return max - min >= DateUtils.ONE_DAY;

    }
    /**
     * @eo.name upsertUserSchedule
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param tenantId
     * @param newScheduleContent
     * @param timeoutSeconds
     * @return void
     */
    @Override
    public void upsertUserSchedule(String tenantId, UserScheduleContent newScheduleContent, long timeoutSeconds) {
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> argDateUserScheduleMap = newScheduleContent.getDateUserScheduleMap();
        //循环处理，效率问题后续会并发处理 根据人查已有数据
        //新增 主数据
        ConcurrentLinkedQueue<IObjectData> allAddMainDataList = new ConcurrentLinkedQueue<>();
        //新增 从数据
        ConcurrentLinkedQueue<IObjectData> allAddSubDataList = new ConcurrentLinkedQueue<>();
        //移除的主数据 因为主数据移除了，从数据也会被级联删除，当用户排班的list里没有存任何数据的时候，会删除主数据
        ConcurrentLinkedQueue<IObjectData> allRemoveMainDataList = new ConcurrentLinkedQueue<>();
        //移除的从数据，更新的时候 会先删除从数据，再新增从数据
        ConcurrentLinkedQueue<IObjectData> allRemoveSubDataList = new ConcurrentLinkedQueue<>();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        argDateUserScheduleMap.keySet().forEach(date -> parallelTask.submit(() -> {
            try {
                //新增 主数据
                List<IObjectData> addMainDataList = Lists.newArrayList();
                //新增 从数据
                List<IObjectData> addSubDataList = Lists.newArrayList();
                //移除的主数据 因为主数据移除了，从数据也会被级联删除，当用户排班的list里没有存任何数据的时候，会删除主数据
                List<IObjectData> removeMainDataList = Lists.newArrayList();
                //移除的从数据，更新的时候 会先删除从数据，再新增从数据
                List<IObjectData> removeSubDataList = Lists.newArrayList();
                Map<Integer, List<UserScheduleContent.UserSchedule>> existUserScheduleMap = Maps.newHashMap();
                Set<Integer> userIds = argDateUserScheduleMap.get(date).keySet();
                //获取用户排班主对象
                List<IObjectData> existMainList = userScheduleDao.getMainListByDateAndUserIds(tenantId, date, date, userIds);
                //获取用户排班从对象
                List<IObjectData> existSubList = userScheduleDao.getUserScheduleDetailByMainIds(tenantId, existMainList.stream().map(userScheduleObj -> userScheduleObj.getId()).collect(Collectors.toList()));
                //根据mainid 分组
                Map<String, List<IObjectData>> existSubMap = existSubList.stream().collect(Collectors.groupingBy(userScheduleObj -> userScheduleObj.get(UserScheduleDetailFields.USER_SCHEDULE, String.class)));
                //循环构造UserSchedule
                for (IObjectData mainObj : existMainList) {
                    UserScheduleContent.UserSchedule userSchedule = new UserScheduleContent.UserSchedule();
                    userSchedule.setMainId(mainObj.getId());
                    userSchedule.setMainData(ObjectDataDocument.of(mainObj));
                    userSchedule.setDate(date);
                    userSchedule.setUserId(mainObj.get(UserScheduleFields.USER_ID, Integer.class));
                    List<IObjectData> details = Optional.ofNullable(existSubMap.get(mainObj.getId())).orElse(Lists.newArrayList());
                    userSchedule.setUserScheduleDetailList(details.stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
                    userSchedule.setAccountShiftDetailIdList(details.stream().map(userScheduleDetailObj -> userScheduleDetailObj.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
                    existUserScheduleMap.computeIfAbsent(userSchedule.getUserId(), o -> Lists.newArrayList()).add(userSchedule);
                }

                //merge 数据 判断增删
                Map<Integer, UserScheduleContent.UserSchedule> argUserScheduleMap = argDateUserScheduleMap.get(date);
                for (Map.Entry<Integer, UserScheduleContent.UserSchedule> argUserScheduleEntry : argUserScheduleMap.entrySet()) {
                    Integer userId = argUserScheduleEntry.getKey();
                    UserScheduleContent.UserSchedule argUserSchedule = argUserScheduleEntry.getValue();
                    //有错误的过滤掉
                    if (argUserSchedule.getErrorReason() != 0) {
                        continue;
                    }
                    //已有数据
                    if (existUserScheduleMap != null) {
                        List<UserScheduleContent.UserSchedule> existUserScheduleList = existUserScheduleMap.get(userId);
                        if (existUserScheduleList != null) {
                            //通过已有数据，更新的逻辑
                            //已有数据
                            if (existUserScheduleList.size() > 1) {
                                //移除 多余的数据 只保留一个
                                UserScheduleContent.UserSchedule remove = existUserScheduleList.remove(0);
                                removeMainDataList.addAll(Lists.newArrayList(remove.getMainData().toObjectData()));
                                log.error("userSchedule exist more than one,tenantId:{},userId:{},date:{}", tenantId, userId, date);
                            }
                            UserScheduleContent.UserSchedule existUserSchedule = existUserScheduleList.get(0);
                            //判断是否变更
                            if (CollectionUtils.isEqualCollection(existUserSchedule.getAccountShiftDetailIdList(), argUserSchedule.getAccountShiftDetailIdList())) {
                                //没有变更
                                continue;
                            }
                            //变更了
                            argUserSchedule.setChange(true);
                            argUserSchedule.setMainData(existUserSchedule.getMainData());
                            argUserSchedule.setMainId(existUserSchedule.getMainId());
                            List<IObjectData> userScheduleDetailList = Lists.newArrayList();
                            //新增子对象数据 过滤掉已有的数据
                            {
                                List<IObjectData> addSubDataListByDate = argUserSchedule.getAccountShiftDetailIdList().stream().filter(accountShiftDetailId -> !existUserSchedule.getAccountShiftDetailIdList().contains(accountShiftDetailId))
                                        .distinct()//去重
                                        .map(accountShiftDetailId -> {
                                            IObjectData accountShiftDetail = newScheduleContent.getAccountShiftDetailMap().get(accountShiftDetailId).toObjectData();
                                            IObjectData accountShift = newScheduleContent.getAccountShiftMap().get(accountShiftDetail.get(AccountShiftDetailFields.ACCOUNT_SHIFT, String.class)).toObjectData();
                                            IObjectData userScheduleDetailObj = userScheduleDao.convertUserScheduleDetailObj(tenantId, argUserSchedule.getUserId(), argUserSchedule.getDate(), existUserSchedule.getMainId(),
                                                    accountShiftDetail, accountShift);
                                            return userScheduleDetailObj;
                                        }).collect(Collectors.toList());
                                userScheduleDetailList.addAll(addSubDataListByDate);
                                addSubDataList.addAll(addSubDataListByDate);
                            }

                            //删除子对象数据
                            {
                                for (ObjectDataDocument objectDataDocument : existUserSchedule.getUserScheduleDetailList()) {
                                    IObjectData iObjectData = objectDataDocument.toObjectData();
                                    //判断 从参数里删除，如果没有删除，就是需要删除的数据
                                    if (!argUserSchedule.getAccountShiftDetailIdList().removeIf(o -> iObjectData.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, String.class).equals(o))) {
                                        removeSubDataList.add(iObjectData);
                                    } else {
                                        userScheduleDetailList.add(iObjectData);
                                    }
                                }
                            }
                            //重写参数数据
                            argUserSchedule.setUserScheduleDetailList(userScheduleDetailList.stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
                            argUserSchedule.setAccountShiftDetailIdList(userScheduleDetailList.stream().map(userScheduleDetailObj -> userScheduleDetailObj.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
                        } else {
                            //完全新增主从数据
                            addUserScheduleObj(tenantId, argUserSchedule, newScheduleContent.getAccountShiftDetailMap(), newScheduleContent.getAccountShiftMap(), addMainDataList, addSubDataList);
                        }
                    } else {
                        //完全新增主从数据
                        addUserScheduleObj(tenantId, argUserSchedule, newScheduleContent.getAccountShiftDetailMap(), newScheduleContent.getAccountShiftMap(), addMainDataList, addSubDataList);
                    }
                }

                //保证两边统一，先同步stat表，再创建paas对象
                //重新构造 newScheduleContent 只保留当前日期的
                UserScheduleContent syncDataContent = new UserScheduleContent();
                syncDataContent.setDateUserScheduleMap(Maps.newHashMap());
                syncDataContent.getDateUserScheduleMap().put(date, argUserScheduleMap);
                syncDataContent.setAccountShiftDetailMap(newScheduleContent.getAccountShiftDetailMap());
                syncDataContent.setAccountShiftMap(newScheduleContent.getAccountShiftMap());
                syncDataContent.setAccountNameMap(newScheduleContent.getAccountNameMap());
                syncDataContent.setUserNameMap(newScheduleContent.getUserNameMap());
                syncDataContent.setExcelAccountIds(newScheduleContent.getExcelAccountIds());
                syncDataContent.setExcelUserIds(newScheduleContent.getExcelUserIds());
                syncDataContent.setAccountAndShiftDetailIdsMap(newScheduleContent.getAccountAndShiftDetailIdsMap());

                //同步stat表
                syncUserScheduleToStat(tenantId, syncDataContent);
                //删除数据
                allRemoveMainDataList.addAll(removeMainDataList);
                allRemoveSubDataList.addAll(removeSubDataList);
                //创建数据
                allAddMainDataList.addAll(addMainDataList);
                allAddSubDataList.addAll(addSubDataList);
            } catch (Exception e) {
                log.error("getExistUserScheduleListByUserAndDate error,tenantId:{},userIds:{}", tenantId, date, e);
                //TODO 还原数据
                throw e;
            }
        }));
        //保存
        try {
            //最大30 min
            boolean success = parallelTask.await(Long.min(1800, timeoutSeconds), TimeUnit.SECONDS);
            if (!success) {
                log.error("upsertUserSchedule failed! ei:{}", tenantId);
                throw new ValidateException("更新排班出错，请重试或联系纷享客服！"); //ignoreI18n
            }
        } catch (TimeoutException e) {
            log.error("upsertUserSchedule time out! ei:{}", tenantId, e);
            throw new ValidateException("更新用户排班超时,请重试！"); //ignoreI18n
        }finally {
            //删除数据
            if (!CollectionUtils.isEmpty(allRemoveSubDataList)) {
                userScheduleDao.batchInvalidAndDel(User.systemUser(tenantId), allRemoveSubDataList);
            }
            if (!CollectionUtils.isEmpty(allRemoveMainDataList)) {
                userScheduleDao.batchInvalidAndDel(User.systemUser(tenantId), allRemoveMainDataList);
            }
            //创建数据
            if (!CollectionUtils.isEmpty(allAddMainDataList)) {
                userScheduleDao.batchSave(User.systemUser(tenantId), allAddMainDataList);
            }
            if (!CollectionUtils.isEmpty(allAddSubDataList)) {
                userScheduleDao.batchSave(User.systemUser(tenantId), allAddSubDataList);
            }
        }
    }

    /**
     * @eo.name getUserScheduleMapByAccountDetail
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param tenantId
     * @param accountShiftDetailIds
     * @param date
     * @param isOuter
     * @return Map
     */
    @Override
    public Map<Integer,List<IObjectData>> getUserScheduleMapByAccountDetail(String tenantId, List<String> accountShiftDetailIds, String date, boolean isOuter) {
            if (CollectionUtils.isEmpty(accountShiftDetailIds)){
                return MapUtils.EMPTY_MAP;
            }
            //查询现有的用户班次详情
            List<IObjectData> partUserScheduleDetailList = userScheduleDao.getUserScheduleDetailByDateAndShiftDetailIds(tenantId,date, accountShiftDetailIds);
            //根据所有的用户班次主对象查询所有的详情对象
            List<String> mainIds = partUserScheduleDetailList.stream().map(o -> o.get(UserScheduleDetailFields.USER_SCHEDULE, String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            //按照人员id分组
            return userScheduleDao.getByIds(tenantId,UserScheduleFields.API_NAME,mainIds).stream()
                    .filter(o->{
                        if (isOuter){
                            return o.get(UserScheduleFields.USER_ID, Integer.class) > *********;
                        }else{
                            return o.get(UserScheduleFields.USER_ID, Integer.class) < *********;
                        }
                    })
                    .collect(Collectors.groupingBy(o -> o.get(UserScheduleFields.USER_ID, Integer.class)));
    }

    /**
     * @param tenantId
     * @param accountIds
     * @param userIds
     * @param startDate
     * @param endDate
     * @return UserScheduleContent
     */
    @Override
    public UserScheduleContent batchGetUserSchedule(String tenantId, List<String> accountIds, List<Integer> userIds, String startDate, String endDate) {
        return batchGetUserScheduleCommon(tenantId,accountIds, userIds, startDate, endDate,true);
    }

    /**
     * 根据门店和门店班次详情id 查询数据
     *
     * @param tenantId
     * @param accountIds
     * @param startDate
     * @param endDate
     * @param isOuter
     * @return
     */
    @NotNull
    private Set<Integer> getScheduleUserIdsByAccount(String tenantId, List<String> accountIds, String startDate, String endDate, boolean isOuter) {
       return getScheduleUserIdsByAccount(tenantId, accountIds, startDate, endDate, null, isOuter);
    }
    @NotNull
    private Set<Integer> getScheduleUserIdsByAccount(String tenantId, List<String> accountIds, String startDate, String endDate,Set<String> noUserScheduleDetailIds, boolean isOuter) {
        Set<Integer> userIdsByAccount = SetUtils.EMPTY_SET;
        if (CollectionUtils.isNotEmpty(accountIds)) {
            HashSet<String> accountIdSet = Sets.newHashSet(accountIds);
            //部分用户班次从对象
            List<IObjectData> partUserScheduleDetailList = userScheduleDao.getUserScheduleDetailListByDateAndAccountIds(tenantId, startDate, endDate, accountIdSet,noUserScheduleDetailIds);
            //用户班次主对象
            List<IObjectData> userScheduleList = userScheduleDao.getByIds(tenantId, UserScheduleFields.API_NAME, partUserScheduleDetailList.stream().map(o -> o.get(UserScheduleDetailFields.USER_SCHEDULE, String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
            //所有的用户id
            userIdsByAccount = userScheduleList.stream().map(o -> o.get(UserScheduleFields.USER_ID, Integer.class)).filter(o->{
                if (isOuter){
                    return o.intValue() > *********;
                }else{
                    return o.intValue() < *********;
                }
            }).collect(Collectors.toSet());
        }
        return userIdsByAccount;
    }

//    /**
//     *
//     * @param tenantId
//     * @param accountShiftDetailIds
//     * @param startDate
//     * @param endDate
//     * @param noUserScheduleDetailIds
//     * @param isOuter
//     * @return
//     */
//    private Set<Integer> getScheduleUserIdsByAccountShiftDetailIds(String tenantId, List<String> accountShiftDetailIds, String startDate, String endDate, Set<String> noUserScheduleDetailIds, boolean isOuter) {
//        Set<Integer> userIdsByAccount = SetUtils.EMPTY_SET;
//        if (CollectionUtils.isNotEmpty(accountShiftDetailIds)) {
//            //部分用户班次从对象
//            List<IObjectData> partUserScheduleDetailList = userScheduleDao.getUserScheduleDetailByDateAndShiftDetailIds(tenantId, startDate, endDate, accountShiftDetailIds,noUserScheduleDetailIds);
//            //用户班次主对象
//            List<IObjectData> userScheduleList = userScheduleDao.getByIds(tenantId, UserScheduleFields.API_NAME, partUserScheduleDetailList.stream().map(o -> o.get(UserScheduleDetailFields.USER_SCHEDULE, String.class)).distinct().collect(Collectors.toList()));
//            //所有的用户id
//            userIdsByAccount = userScheduleList.stream().map(o -> o.get(UserScheduleFields.USER_ID, Integer.class)).filter(o->{
//                if (isOuter){
//                    return o.intValue() > *********;
//                }else{
//                    return o.intValue() < *********;
//                }
//            }).collect(Collectors.toSet());
//        }
//        return userIdsByAccount;
//    }

//    @Override
//    public UserScheduleContent batchGetUserScheduleByAccount(String tenantId, List<String> accountIds, String startDate, String endDate) {
//       return batchGetUserScheduleCommon(tenantId,accountIds, null, startDate, endDate,true);
//    }

//    @Override
//    public UserScheduleContent batchGetUserScheduleByUser(String tenantId, List<Integer> userIds, String startDate, String endDate) {
//        return batchGetUserScheduleByUserCommon(tenantId, userIds, null, startDate, endDate,true);
//    }

    /**
     * @eo.name batchGetUserScheduleIgnoreOther
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param tenantId
     * @param accountIds
     * @param userIds
     * @param startDate
     * @param endDate
     * @return UserScheduleContent
     */
    @Override
    public UserScheduleContent batchGetUserScheduleIgnoreOther(String tenantId, List<String> accountIds, List<Integer> userIds, String startDate, String endDate) {
        return batchGetUserScheduleCommon(tenantId, accountIds, userIds, startDate, endDate,false);
    }

    @NotNull
    private UserScheduleContent batchGetUserScheduleCommon(String tenantId, List<String> accountIds, List<Integer> userIds, String startDate, String endDate,boolean fillOther) {
        //处理参数 内外部过滤，还有客户转用户
        UserScheduleContent userScheduleContent = new UserScheduleContent();
        if (CollectionUtils.isEmpty(accountIds) && CollectionUtils.isEmpty(userIds)) {
            return userScheduleContent;
        }
        Set<Integer> userIdsByAccount = null;
        if (CollectionUtils.isNotEmpty(accountIds)) {
            boolean isOuter;
            if (CollectionUtils.isNotEmpty(userIds)){
                isOuter = userIds.stream().anyMatch(o->o.intValue() >*********);
            } else {
                isOuter = false;
            }
            userIdsByAccount = getScheduleUserIdsByAccount(tenantId, accountIds, startDate, endDate,isOuter);
        }
        if (CollectionUtils.isNotEmpty(userIds)) {
            if (CollectionUtils.isNotEmpty(accountIds)) {
                if (CollectionUtils.isNotEmpty(userIdsByAccount)) {
                    userIds = userIds.stream().distinct().filter(userIdsByAccount::contains).sorted().collect(Collectors.toList());
                }else{
                    return userScheduleContent;
                }
            }
        }else if (CollectionUtils.isNotEmpty(accountIds)) {
            if (CollectionUtils.isNotEmpty(userIdsByAccount)) {
                userIds = userIdsByAccount.stream().distinct().sorted().collect(Collectors.toList());
            } else {
                return userScheduleContent;
            }
        }
        //真正 获取的方法
        return batchGetUserScheduleByUserCommon(tenantId, userIds,accountIds, startDate, endDate, fillOther);
    }

    /**
     * 真正逻辑
     * todo： 后续可以优化下 区分下客户和 用户的  是数据不完整
     * @param tenantId
     * @param userIds
     * @param argAccountIds 用来判断 是否是取过交集，不用向下再查了
     * @param startDate
     * @param endDate
     * @param fillOther
     * @return
     */
    @NotNull
    private UserScheduleContent batchGetUserScheduleByUserCommon(String tenantId, List<Integer> argUserIds, List<String> argAccountIds, String startDate, String endDate, boolean fillOther) {
        UserScheduleContent userScheduleContent = new UserScheduleContent();
        if(CollectionUtils.isEmpty(argUserIds)){
            return userScheduleContent;
        }
        //换参数了 不修改原始值
        List<Integer> userIds = Lists.newArrayList(argUserIds);
        List<String> accountIds = argAccountIds == null ? Lists.newArrayList() : Lists.newArrayList(argAccountIds);

        userScheduleContent.setExcelAccountIds(accountIds);
        userScheduleContent.setExcelUserIds(userIds);
        //查用户排班 UserScheduleObj
        List<IObjectData> userSheculeObjList = userScheduleDao.getMainListByDateAndUserIds(tenantId, startDate, endDate, Sets.newHashSet(userIds));
        if (CollectionUtils.isEmpty(userSheculeObjList)){
            if (CollectionUtils.isNotEmpty(userIds)){
                fillEmployeeName(tenantId,userScheduleContent);
            }
            return userScheduleContent;
        }

        //根据用户排班id 查所有的子对象 UserScheduleDetailObj
        List<IObjectData> userScheduleDetailByMainIds = userScheduleDao.getUserScheduleDetailByMainIds(tenantId, userSheculeObjList.stream().map(userScheduleObj -> userScheduleObj.getId()).collect(Collectors.toList()));
        //最后需要留下完整数据的客户ids
        List<String> accountWithCompleteDataIds = userScheduleDetailByMainIds.stream().map(o -> o.get(UserScheduleDetailFields.ACCOUNT, String.class)).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(argAccountIds)){
            //门店模式 需要 补充其他的用户数据
            boolean isOuter;
            if (CollectionUtils.isNotEmpty(userIds)){
                isOuter = userIds.stream().anyMatch(o->o.intValue() >*********);
            } else {
                isOuter = false;
            }
            //其他的用户id
            Set<Integer> otherUserIds = getScheduleUserIdsByAccount(tenantId, accountWithCompleteDataIds
                    , startDate, endDate, userScheduleDetailByMainIds.stream().map(o -> o.getId()).collect(Collectors.toSet()),isOuter);
            if (CollectionUtils.isNotEmpty(otherUserIds)){
                userScheduleContent.getExcelUserIds().addAll(otherUserIds);
                List<IObjectData> otherMainListByDateAndUserIds = userScheduleDao.getMainListByDateAndUserIds(tenantId, startDate, endDate, otherUserIds);
                userSheculeObjList.addAll(otherMainListByDateAndUserIds);
                userScheduleDetailByMainIds.addAll(userScheduleDao.getUserScheduleDetailByMainIds(tenantId, otherMainListByDateAndUserIds.stream().map(IObjectData::getId).collect(Collectors.toList())));
            }
        }
        //userScheduleDetailByMainIds 按照mainId 聚合
        userScheduleContent.setDateUserScheduleMap(convretDateUserScheduleMap(userSheculeObjList, userScheduleDetailByMainIds,Boolean.FALSE));
        if (fillOther){
            //补客户排班数据
            fillAccountShiftDetail(tenantId,userScheduleContent,false);
            //补用户数据
            fillEmployeeName(tenantId,userScheduleContent);
        }
        //转换门店班次为主的数据
        userScheduleContent.setDateShiftDetailScheduleMap(convertDateShiftDetailScheduleMap(userScheduleContent));
        //保留有完整用户数据的，参数的就行，去掉多余的用户id
        if (CollectionUtils.isNotEmpty(userScheduleContent.getExcelUserIds()) && CollectionUtils.isNotEmpty(argUserIds)) {
            userScheduleContent.getExcelUserIds().removeIf(o->!argUserIds.contains(o));
        }
        if (CollectionUtils.isNotEmpty(argAccountIds)) {
            //客户参数不为空
            userScheduleContent.getExcelAccountIds().removeIf(o -> !argAccountIds.contains(o));
        } else if (CollectionUtils.isNotEmpty(accountWithCompleteDataIds)) {
            //客户参数为空
            userScheduleContent.getExcelAccountIds().removeIf(o -> !accountWithCompleteDataIds.contains(o));
        }
        return userScheduleContent;
    }

//    @Override
//    public UserScheduleContent batchGetUserScheduleByUserIgnoreOther(String tenantId, List<Integer> userIds, String startDate, String endDate) {
//        return batchGetUserScheduleByUserCommon(tenantId, userIds, null, startDate, endDate,false);
//    }

    private Map<String, Map<String, UserScheduleContent.ShiftSchedule>> convertDateShiftDetailScheduleMap(UserScheduleContent userScheduleContent) {
        Map<String, Map<String, UserScheduleContent.ShiftSchedule>> dateShiftDetailScheduleMap = Maps.newHashMap();
        if (MapUtils.isEmpty(userScheduleContent.getDateUserScheduleMap())){
            return dateShiftDetailScheduleMap;
        }
        userScheduleContent.getDateUserScheduleMap().forEach((date, value) -> {
            Map<String, UserScheduleContent.ShiftSchedule> shiftScheduleMap = Maps.newHashMap();
            value.forEach((userId, userSchedule) -> {
                userSchedule.getAccountShiftDetailIdList().forEach(accountShiftDetailId -> {
                    UserScheduleContent.ShiftSchedule shiftSchedule = shiftScheduleMap.computeIfAbsent(accountShiftDetailId, o -> {
                        UserScheduleContent.ShiftSchedule temp = new UserScheduleContent.ShiftSchedule();
                        temp.setDate(date);
                        temp.setAccountShiftDetailId(accountShiftDetailId);
                        temp.setUserIds(Lists.newArrayList());
                        return temp;
                    });
                    shiftSchedule.getUserIds().add(UserScheduleContent.ShiftScheduleUser.builder().userId(userId).errorReason(userSchedule.getErrorReason()).build());
                });
            });
            dateShiftDetailScheduleMap.put(date, shiftScheduleMap);
        });
        userScheduleContent.setDateShiftDetailScheduleMap(dateShiftDetailScheduleMap);
        return dateShiftDetailScheduleMap;
    }

    /**
     * @eo.name convretDateUserScheduleMap
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param userSheculeObjList
     * @param userScheduleDetailByMainIds
     * @param isChange
     * @return Map
     */
    @Override
    public  Map<String/*日期*/, Map<Integer/*用户id*/, UserScheduleContent.UserSchedule>> convretDateUserScheduleMap(List<IObjectData> userSheculeObjList,List<IObjectData> userScheduleDetailByMainIds,boolean isChange) {
        Map<String, List<IObjectData>> mainUserScheduleDetailsMap = userScheduleDetailByMainIds.stream().collect(Collectors.groupingBy(o -> o.get(UserScheduleDetailFields.USER_SCHEDULE, String.class)));
        Map<String/*日期*/, Map<Integer/*用户id*/, UserScheduleContent.UserSchedule>> dateUserScheduleMap = Maps.newHashMap();
        userSheculeObjList.forEach(userScheduleObj -> {
            UserScheduleContent.UserSchedule userSchedule = new UserScheduleContent.UserSchedule();
            userSchedule.setMainId(userScheduleObj.getId());
            userSchedule.setMainData(ObjectDataDocument.of(userScheduleObj));
            userSchedule.setDate(DateUtils.getStringFromTime(userScheduleObj.get(UserScheduleFields.DATE, Long.class),DateUtils.DateFormat));
            userSchedule.setUserId(userScheduleObj.get(UserScheduleFields.USER_ID, Integer.class));
            List<IObjectData> userScheduleDetails = mainUserScheduleDetailsMap.get(userScheduleObj.getId());
            if (CollectionUtils.isNotEmpty(userScheduleDetails)){
                userSchedule.setAccountShiftDetailIdList(userScheduleDetails.stream().map(userScheduleDetailObj -> userScheduleDetailObj.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
                userSchedule.setUserScheduleDetailList(userScheduleDetails.stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
            }else{
                userSchedule.setUserScheduleDetailList(Lists.newArrayList());
                userSchedule.setAccountShiftDetailIdList(Lists.newArrayList());
            }
            userSchedule.setChange(isChange);
            dateUserScheduleMap.computeIfAbsent(userSchedule.getDate(),o->Maps.newHashMap()).computeIfAbsent(userSchedule.getUserId(), o->userSchedule);
        });
        return dateUserScheduleMap;
    }


    /**
     * 完全新增的主从对象
     *
     * @param newScheduleContent
     * @param userId
     * @param date
     * @param tenantId
     * @param argUserSchedule
     * @param accountShiftMap
     * @param addMainDataList
     * @param addSubDataList
     */
    private void addUserScheduleObj(String tenantId, UserScheduleContent.UserSchedule argUserSchedule, Map<String, ObjectDataDocument> accountShiftDetailObjMap, Map<String, ObjectDataDocument> accountShiftMap, List<IObjectData> addMainDataList, List<IObjectData> addSubDataList) {
        argUserSchedule.setChange(true);
        //构建主对象数据
        IObjectData userScheduleObj = userScheduleDao.createBaseObjectData(tenantId, String.valueOf(argUserSchedule.getUserId()), UserScheduleFields.API_NAME);
        userScheduleObj.set(UserScheduleFields.USER_ID, argUserSchedule.getUserId());
        userScheduleObj.set(UserScheduleFields.DATE, DateUtils.getDateTimeFromString(argUserSchedule.getDate(),DateUtils.DateFormat));
        //填充参数数据
        argUserSchedule.setMainId(userScheduleObj.getId());
        argUserSchedule.setMainData(ObjectDataDocument.of(userScheduleObj));
        //新增主对象数据
        addMainDataList.add(userScheduleObj);


        //构建从对象数据
        List<IObjectData> addSubDataListByDate = argUserSchedule.getAccountShiftDetailIdList().stream()
                .distinct()//去重
                .map(accountShiftDetailId -> {
            IObjectData accountShiftDetail = accountShiftDetailObjMap.get(accountShiftDetailId).toObjectData();
            IObjectData accountShift = accountShiftMap.get(accountShiftDetail.get(AccountShiftDetailFields.ACCOUNT_SHIFT, String.class)).toObjectData();
            IObjectData userScheduleDetailObj = userScheduleDao.convertUserScheduleDetailObj(tenantId, argUserSchedule.getUserId(), argUserSchedule.getDate(), userScheduleObj.getId(), accountShiftDetail, accountShift);
            return userScheduleDetailObj;
        }).collect(Collectors.toList());
        //填充参数数据
        argUserSchedule.setUserScheduleDetailList(addSubDataListByDate.stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
        argUserSchedule.setAccountShiftDetailIdList(addSubDataListByDate.stream().map(userScheduleDetailObj -> userScheduleDetailObj.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        //新增从对象数据
        addSubDataList.addAll(addSubDataListByDate);
    }

    /**
     * 不存在的客户班次会报错
     * 修改成， 不存在的客户班次直接过滤没
     * @param tenantId
     * @param newScheduleContent
     * @param onlyFuture 仅未来数据的话，查不到会报错
     * @eo.name 不存在的客户班次会报错修改成， 不存在的客户班次直接过滤没
     * @eo.url null
     * @eo.method get
     * @eo.request-type formdata
     */
    @Override
    public void fillAccountShiftDetail(String tenantId, UserScheduleContent newScheduleContent,boolean onlyFuture) {
        String todayStr = LocalDate.now().toString();
        {
            //accountIds 需要解析的客户数据
            Set<String> fillAccountIds = Sets.newHashSet();
            Set<String> existAccountIds = newScheduleContent.getAccountAndShiftDetailIdsMap().keySet();
            if (CollectionUtils.isNotEmpty(newScheduleContent.getExcelAccountIds())) {
                fillAccountIds.addAll(newScheduleContent.getExcelAccountIds().stream().filter(o -> !existAccountIds.contains(o)).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(fillAccountIds)) {
                //客户班次主
                List<IObjectData> accountShiftList = accountShiftDao.getAccountShiftByAccountId(tenantId, fillAccountIds);
                //客户班次详情
                List<IObjectData> accountShiftDetailList = accountShiftDao.getAccountShiftDetailByMainIds(tenantId, accountShiftList.stream().map(IObjectData::getId).collect(Collectors.toSet()));
                fillAccountShiftByAccountShiftArg(newScheduleContent, accountShiftList, accountShiftDetailList);
            }
        }
        {

            //用户排班表里的需要解析的数据  只返回已经排班的未来和当天的数据，当天的不管了
            Set<String> accountShiftDetailIds = newScheduleContent.getDateUserScheduleMap().values().stream().flatMap(userScheduleMap -> userScheduleMap.values().stream()).filter(o->!onlyFuture || o.getDate().compareTo(todayStr) >= 0 ).flatMap(userSchedule -> userSchedule.getAccountShiftDetailIdList().stream()).collect(Collectors.toSet());
            accountShiftDetailIds.removeAll(newScheduleContent.getAccountShiftDetailMap().keySet());
            if (CollectionUtils.isNotEmpty(accountShiftDetailIds)) {
                //客户班次详情
                List<IObjectData> accountShiftDetailsByIds = accountShiftDao.getAccountShiftDetailsByIds(tenantId, accountShiftDetailIds);
                //客户班次主
                Set<String> accountShiftIds = accountShiftDetailsByIds.stream().map(accountShiftDetailObj -> accountShiftDetailObj.get(AccountShiftDetailFields.ACCOUNT_SHIFT, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                //客户班次详情
                List<IObjectData> accountShiftList = accountShiftDao.getAccountShiftByIds(tenantId, accountShiftIds);
                List<IObjectData> accountShiftDetailList = accountShiftDao.getAccountShiftDetailByMainIds(tenantId, accountShiftIds);
                //map  accountShiftId and accountId
                fillAccountShiftByAccountShiftArg(newScheduleContent, accountShiftList, accountShiftDetailList);
            }
        }
        //过滤掉所有的没查到的门店班次详情id
        newScheduleContent.getDateUserScheduleMap().values().forEach(userScheduleMap -> userScheduleMap.values().forEach(userSchedule -> {
            userSchedule.getAccountShiftDetailIdList().removeIf(c -> !newScheduleContent.getAccountShiftDetailMap().keySet().contains(c));
        }));
        //聚合所有的客户id
        List<String> accountIds = newScheduleContent.getAccountShiftMap().values().stream().map(accountShiftObj -> (String) accountShiftObj.get(AccountShiftFields.CUSTOMER)).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newScheduleContent.getExcelAccountIds())){
            newScheduleContent.setExcelAccountIds(accountIds);
        }else{
            //list 取差集
            newScheduleContent.getExcelAccountIds().addAll(CollectionUtils.removeAll(accountIds,newScheduleContent.getExcelAccountIds()));
        }
        fillAccountName(tenantId, newScheduleContent);
        //accountIds 和 nameMap 不一致 报错
        if (onlyFuture && newScheduleContent.getAccountNameMap().size() < accountIds.size()) {
            throw new ValidateException("用户排班数据中包含已经删除的客户数据 : "+ JSON.toJSONString(CollectionUtils.removeAll(accountIds, newScheduleContent.getAccountNameMap().keySet()))); //ignoreI18n
        }
    }

    private void fillAccountName(String tenantId, UserScheduleContent newScheduleContent) {

        //填充客户名称
        Collection<String> findAccountIds = CollectionUtils.removeAll(newScheduleContent.getExcelAccountIds(), newScheduleContent.getAccountNameMap().keySet());
        if(findAccountIds.isEmpty()){
            return;
        }
        baseDao.getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder().in("_id", findAccountIds).build(), "AccountObj", Lists.newArrayList("name"))
                .forEach(accountObj -> newScheduleContent.getAccountNameMap().put(accountObj.getId(), accountObj.get("name", String.class)));
    }

    private  void fillAccountShiftByAccountShiftArg(UserScheduleContent newScheduleContent, List<IObjectData> accountShiftList, List<IObjectData> accountShiftDetailList) {
        //map  accountShiftId and accountId
        Map<String, String> mainAndAccountIdMap = accountShiftList.stream().collect(Collectors.toMap(k -> k.getId(), v -> v.get(AccountShiftFields.CUSTOMER, String.class), (v1, v2) -> v1));
        newScheduleContent.getAccountShiftDetailMap().putAll(accountShiftDetailList.stream().collect(Collectors.toMap(accountShiftDetailObj -> accountShiftDetailObj.getId(), accountShiftDetailObj -> ObjectDataDocument.of(accountShiftDetailObj))));
        newScheduleContent.getAccountShiftMap().putAll(accountShiftList.stream().collect(Collectors.toMap(accountShiftObj -> accountShiftObj.getId(), accountShiftObj -> ObjectDataDocument.of(accountShiftObj))));
        //生成map按照accountShiftId 聚合成Map<String/*accountShiftId*/,list/*accountShiftDetailIds*/>为value key为accountId
        newScheduleContent.getAccountAndShiftDetailIdsMap().putAll(accountShiftDetailList.stream().collect(Collectors.groupingBy(accountShiftDetailObj -> mainAndAccountIdMap.get(accountShiftDetailObj.get(AccountShiftDetailFields.ACCOUNT_SHIFT, String.class))
                , Collectors.mapping(accountShiftDetailObj -> accountShiftDetailObj, Collectors.groupingBy(k -> k.get(AccountShiftDetailFields.ACCOUNT_SHIFT, String.class), Collectors.mapping(accountShiftDetailObj -> accountShiftDetailObj.getId(), Collectors.toList()))))));
    }



    /**
     * @eo.name fillEmployeeName
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param tenantId
     * @param newScheduleContent
     * @return void
     */
    @Override
    public void fillEmployeeName(String tenantId, UserScheduleContent newScheduleContent) {
        //跟fillAccountShiftDetail 逻辑大概一致
        //需要解析的客户数据
        {
            //fillUserIds 需要解析的客户数据
            Set<Integer> fillUserIds = Sets.newHashSet();
            Set<Integer> existUserIds = newScheduleContent.getUserNameMap().keySet();
            if (CollectionUtils.isNotEmpty(newScheduleContent.getExcelUserIds())) {
                fillUserIds.addAll(newScheduleContent.getExcelUserIds().stream().filter(o -> !existUserIds.contains(o)).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(fillUserIds)) {
                Map<Integer, String> userNameByIds = employeeDao.getUserNameByIds(tenantId, fillUserIds);
                newScheduleContent.getUserNameMap().putAll(userNameByIds);
                if (userNameByIds.size() < fillUserIds.size()) {
                    throw new ValidateException("用户排班数据中包含已经删除的用户数据 : "+ JSON.toJSONString(CollectionUtils.removeAll(fillUserIds,userNameByIds.keySet()))); //ignoreI18n
                }
            }
        }
        {
            //用户排班表里的需要解析的数据
            Set<Integer> userIds = Optional.ofNullable(newScheduleContent.getDateUserScheduleMap()).orElse(Maps.newHashMap()).values().stream().flatMap(userScheduleMap -> userScheduleMap.keySet().stream()).collect(Collectors.toSet());
            userIds.removeAll(newScheduleContent.getUserNameMap().keySet());
            if (CollectionUtils.isNotEmpty(userIds)) {
                //查Employee
                Map<Integer, String> userNameByIds = employeeDao.getUserNameByIds(tenantId, userIds);
                newScheduleContent.getUserNameMap().putAll(userNameByIds);
                if (userNameByIds.size() < userIds.size()) {
                    throw new ValidateException("用户排班数据中包含已经删除的用户数据 : "+ JSON.toJSONString(CollectionUtils.removeAll(userIds,userNameByIds.keySet()))); //ignoreI18n
                }
            }
        }
        //聚合所有的人员id
        List<Integer> userIds = newScheduleContent.getUserNameMap().keySet().stream().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newScheduleContent.getExcelUserIds())){
            newScheduleContent.setExcelUserIds(userIds);
        }else{
            newScheduleContent.getExcelUserIds().addAll(CollectionUtils.removeAll(userIds,newScheduleContent.getExcelUserIds()));
        }

    }

    /**
     * @eo.name syncUserSchedulesByAccountDetailIds
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param user
     * @param accountDetails
     * @return void
     */
    @Override
    public void syncUserSchedulesByAccountDetailIds(User user, List<String> accountDetails,int delFlag) {
        //删除当天 和当天之后的排班
        SearchQuery.SearchQueryBuilder builder = SearchQuery.builder();
        if (CollectionUtils.isNotEmpty(accountDetails)) {
            builder.in(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, accountDetails);
        } else {
            return;
        }
        Map<String, IObjectData> userDetails = null;
        if (delFlag == 1) {
            userDetails = accountShiftDao.batchInvalidAndDelByQuery(user, builder
                    .gte(UserScheduleDetailFields.SCHEDULE_DATE, LocalDate.now().atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli()), UserScheduleDetailFields.API_NAME);
        } else {
            userDetails = accountShiftDao.getAllIObjectDataListByQuery(user, builder
                    .gte(UserScheduleDetailFields.SCHEDULE_DATE, LocalDate.now().atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli()).build(), UserScheduleDetailFields.API_NAME).stream().collect(Collectors.toMap(IObjectData::getId, o -> o, (v1, v2) -> v1));
        }


        //查用户排班主数据
        List<String> userScheduleIds = userDetails.values().stream().map(userScheduleDetailObj -> userScheduleDetailObj.get(UserScheduleDetailFields.USER_SCHEDULE, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<IObjectData> userScheduleMainList = userScheduleDao.getByIds(user.getTenantId(), UserScheduleFields.API_NAME, userScheduleIds);
        //查从数据
        List<IObjectData> userScheduleDetails = userScheduleDao.getUserScheduleDetailByMainIds(user.getTenantId(), userScheduleIds);
        UserScheduleContent userScheduleContent = new UserScheduleContent();
        userScheduleContent.setDateUserScheduleMap(convretDateUserScheduleMap(userScheduleMainList, userScheduleDetails, Boolean.TRUE));
        fillAccountShiftDetail(user.getTenantId(), userScheduleContent, Boolean.FALSE);
        //同步到stat表
        syncUserScheduleToStat(user.getTenantId(), userScheduleContent);
    }

    /**
     * @eo.name checkUserScheduleConflictByUpdadteShiftDetail
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param user
     * @param updateTimeAccountShiftDetails
     * @return Pair
     */
    @Override
    public Pair<Map<String, Map<Integer, UserScheduleContent.UserSchedule>>,UserScheduleContent> checkUserScheduleConflictByUpdadteShiftDetail(User user, List<IObjectData> updateTimeAccountShiftDetails) {
        SearchQuery.SearchQueryBuilder builder = SearchQuery.builder();
        if (CollectionUtils.isNotEmpty(updateTimeAccountShiftDetails)) {
            builder.in(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, updateTimeAccountShiftDetails.stream().map(IObjectData::getId).collect(Collectors.toList()));
        }else{
            return null;
        }
        List<IObjectData> allIObjectDataListByQuery = accountShiftDao.getAllIObjectDataListByQuery(user, builder
                .gte(UserScheduleDetailFields.SCHEDULE_DATE, LocalDate.now().atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli()).build(), UserScheduleDetailFields.API_NAME);
        //查用户排班主数据
        List<String> userScheduleIds = allIObjectDataListByQuery.stream().map(userScheduleDetailObj -> userScheduleDetailObj.get(UserScheduleDetailFields.USER_SCHEDULE, String.class)).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userScheduleIds)){
            return null;
        }

        List<IObjectData> userScheduleMainList = userScheduleDao.getByIds(user.getTenantId(),UserScheduleFields.API_NAME, userScheduleIds);
        //查从数据
        List<IObjectData> userScheduleDetails = userScheduleDao.getUserScheduleDetailByMainIds(user.getTenantId(), userScheduleIds);
        UserScheduleContent userScheduleContent = new UserScheduleContent();
        userScheduleContent.setAccountShiftDetailMap(updateTimeAccountShiftDetails.stream().collect(Collectors.toMap(IObjectData::getId,accountShiftDetailObj -> ObjectDataDocument.of(accountShiftDetailObj))));
        userScheduleContent.setDateUserScheduleMap(convretDateUserScheduleMap(userScheduleMainList, userScheduleDetails,Boolean.TRUE));
        //验证方法
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> conflictData = checkUserScheduleConflictAndFillData(user.getTenantId(), userScheduleContent,Boolean.FALSE,userScheduleMainList.get(0).get(UserScheduleFields.USER_ID, Integer.class).intValue() > *********);
        return Pair.of(conflictData,userScheduleContent);
    }

    /**
     * @eo.name getAccountRange
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param tenantId
     * @param ruleId
     * @return Result
     */
    @Override
    public AccountRangeOnRule.Result getAccountRange(String tenantId, String ruleId){
        AccountRangeOnRule.Args args = new AccountRangeOnRule.Args();
        args.setRuleId(ruleId);
        return checkinsOfficeProxy.getAccountByRuleId(tenantId,args);
    }

    /**
     * @eo.name verifyAccountRange
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param tenantId
     * @param accountRange
     * @return void
     */
    @Override
    public void verifyAccountRange(String tenantId,AccountRangeOnRule.Result accountRange){
        if(Objects.isNull(accountRange.getCustomerRangeType())
                || (accountRange.getCustomerRangeType() == 1) && StringUtils.isBlank(accountRange.getCustomerRangeWhere())
                || (accountRange.getCustomerRangeType() == 2) && CollectionUtils.isEmpty(accountRange.getCustomerRangeInfos())){
            throw new ValidateException("请先设置客户的范围"); //ignoreI18n
        }
        if(accountRange.getCustomerRangeType() == 2 && accountRange.getCustomerRangeInfos().size() > OfficeShiftConstants.MAX_ACCOUNT_ON_RULE){
            throw new ValidateException("请缩小客户的范围"); //ignoreI18n
        }
        if(accountRange.getCustomerRangeType() == 1){
            List<Wheres> wheres = JSONObject.parseObject(accountRange.getCustomerRangeWhere(),new TypeReference<List<Wheres>>(){});
            SearchQuery searchQuery = SearchQuery.builder().build();
            searchQuery.getSearchTemplateQuery().setLimit(1);
            searchQuery.getSearchTemplateQuery().setWheres(wheres);
            int total = employeeDao.getTotal(User.systemUser(tenantId),searchQuery, AccountObjConstants.API_NAME);
            if(total > OfficeShiftConstants.MAX_ACCOUNT_ON_RULE){
                throw new ValidateException("请缩小客户的范围"); //ignoreI18n
            }
            if(total == 0){
                throw new ValidateException("该范围下没有客户,请重新设置"); //ignoreI18n
            }
        }
    }

    /**
     * @eo.name getAccIdAndNameByRuleRange
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param tenantId
     * @param range
     * @return Map
     */
    @Override
    public Map<String, IObjectData> getAccIdAndNameByRuleRange(String tenantId,AccountRangeOnRule.Result range) {
        Map<String, IObjectData> accIdAndDataMap = Maps.newHashMap();
        // 1.批量查询客户
        if (range.getCustomerRangeType() == 1) {
            List<Wheres> wheres = JSONObject.parseObject(range.getCustomerRangeWhere(), new TypeReference<List<Wheres>>() {
            });
            SearchQuery searchQuery = SearchQuery.builder().build();
            searchQuery.getSearchTemplateQuery().setWheres(wheres);
            QueryResult<IObjectData> queryResult = employeeDao.getAllQueryDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, AccountObjConstants.API_NAME, null);
            accIdAndDataMap.putAll(queryResult.getData().stream().collect(Collectors.toMap(IObjectData::getId, k2->k2, (k1, k2) -> k1)));
        } else {
            accIdAndDataMap = baseDao.getByIds(tenantId,ObjApiName.AccountObj.toString(),range.getCustomerRangeInfos().stream().map(PaasIdAndName::getId).filter(Objects::nonNull).collect(Collectors.toList())).stream().collect(Collectors.toMap(IObjectData::getId, k2->k2, (k1, k2) -> k1));
        }
        return accIdAndDataMap;
    }

    @Override
    public Map<String, IObjectData> getAccDataByRuleRange(String tenantId,AccountRangeOnRule.Result range) {
        Map<String, IObjectData> accDataMap = Maps.newHashMap();
        // 1.批量查询客户
        if (range.getCustomerRangeType() == 1) {
            List<Wheres> wheres = JSONObject.parseObject(range.getCustomerRangeWhere(), new TypeReference<List<Wheres>>() {
            });
            SearchQuery searchQuery = SearchQuery.builder().build();
            searchQuery.getSearchTemplateQuery().setWheres(wheres);
            QueryResult<IObjectData> queryResult = employeeDao.getAllQueryDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, AccountObjConstants.API_NAME, null);
            accDataMap.putAll(queryResult.getData().stream().collect(Collectors.toMap(IObjectData::getId, k2->k2, (k1, k2) -> k1)));
        } else {
            List<String> accIdList = range.getCustomerRangeInfos().stream().map(PaasIdAndName::getId).collect(Collectors.toList());
            accDataMap = userScheduleDao.getByIds(tenantId, ObjApiName.AccountObj.toString(),accIdList).stream().collect(Collectors.toMap(DBRecord::getId, k2->k2,(k1, k2)->k1));
        }
        return accDataMap;
    }

    /**
     * @eo.name getRuleSearchQuery
     * @eo.url
     * @eo.method get
     * @eo.request-type formdata
     * @param tenantId
     * @param ruleId
     * @return SearchQuery
     */
    @Override
    public SearchQuery getRuleSearchQuery(String tenantId, String ruleId) {
        AccountRangeOnRule.Result accountRange = getAccountRange(tenantId, ruleId);
        verifyAccountRange(tenantId, accountRange);
        if (accountRange.getCustomerRangeType() == 1) {
            List<Wheres> wheres = JSONObject.parseObject(accountRange.getCustomerRangeWhere(), new TypeReference<List<Wheres>>() {
            });
            SearchQuery searchQuery = SearchQuery.builder().build();
            searchQuery.getSearchTemplateQuery().setWheres(wheres);
            return searchQuery;
        } else {
            return SearchQuery.builder().in("_id", accountRange.getCustomerRangeInfos().stream().map(PaasIdAndName::getId).collect(Collectors.toList())).build();
        }
    }


    @Override
    public boolean isMnPmmSchedule(String ea) {
        return EnvUtils.isMN() || GrayRelease.isAllow("checkin-server-v2", "mnPmmSchedule", ea);
    }
}
