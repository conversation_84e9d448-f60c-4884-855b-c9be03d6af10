package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;


@Slf4j
public class AccountShiftInvalidAction extends StandardInvalidAction {
    AccountShiftDao accountShiftDao = SpringUtil.getContext().getBean(AccountShiftDao.class);
    OfficeShiftService officeShiftService = SpringUtil.getContext().getBean(OfficeShiftService.class);

    @Override
    protected boolean needTriggerApprovalFlow() {
        return false;
    }

    @Override
    protected boolean needTriggerWorkFlow() {
        return false;
    }
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        //直接调删除，不允许恢复
        accountShiftDao.delData(objectDataList,actionContext.getUser());
        List<IObjectData> details = detailObjectData.get(AccountShiftDetailFields.API_NAME);
        if(CollectionUtils.isNotEmpty(details)){
            officeShiftService.syncUserSchedulesByAccountDetailIds(actionContext.getUser(), details.stream().map(o->o.getId()).collect(Collectors.toList()),1);
        }
        return after;
    }
}
