package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportDataAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 客户班次新建导入
 * 真正导入数据的业务处理逻辑，输入的是从excel中读出的一批数据行，返回有错误的行号和对应的业务校验失败的原因。
 * <p>
 * 注意这个action要注意异常处理，按规则返回结果，否则在导入的结果中无法正确
 * <p>
 * 整个的处理过程分为字段转换和初始化->校验字段值→对合法的数据做业务的默认值处理→写入数据库 共4个大的阶段
 * <p>
 * 这几个阶段也对应了4个业务的扩展点
 * <p>
 * 1 com.facishare.paas.appframework.core.predef.action.BaseImportDataAction#customInit（）
 * <p>
 * 对应数据字段的转换和初始化
 * <p>
 * 2 com.facishare.paas.appframework.core.predef.action.BaseImportDataAction#customValidate（）
 * <p>
 * 对应数据校验
 * <p>
 * 3 com.facishare.paas.appframework.core.predef.action.BaseImportDataAction#customDefaultValue（）
 * <p>
 * 对应业务默认值赋值
 * <p>
 * 4 com.facishare.paas.appframework.core.predef.action.BaseImportDataAction#importData（）
 * <p>
 * 对应写入数据
 * <p>
 * 除了这些大阶段的扩展点，还有每个阶段一些功能的扩展，这个可以看代码按需使用
 * @author: zhangsm
 * @create: 2024-02-20 14:37
 **/
public class AccountShiftInsertImportDataAction extends StandardInsertImportDataAction {
    AccountShiftDao accountShiftDao = SpringUtil.getContext().getBean(AccountShiftDao.class);
    String customerFieldLabel = "客户"; //ignoreI18n

    @Override
    protected void customInit(List<ImportData> dataList) {
        //设置客户字段为唯一
        objectDescribeExt.getFieldDescribeSilently(AccountShiftFields.CUSTOMER).ifPresent(iFieldDescribe ->
        {
            iFieldDescribe.setUnique(true);
            customerFieldLabel = iFieldDescribe.getLabel();
        });
        super.customInit(dataList);

    }

    @Override
    protected void customValidate(List<ImportData> dataList) {
        super.customValidate(dataList);
        //唯一验证，同一个客户只有一个客户班次主对象
        validateCustomerUnique(dataList);
    }

    private void validateCustomerUnique(List<ImportData> dataList) {
        //查询已有数据
        Set<String> customerSet = dataList.stream().map(data -> data.getData().get(AccountShiftFields.CUSTOMER, String.class)).collect(Collectors.toSet());
        List<IObjectData> dbList = accountShiftDao.getAccountShiftByAccountId(actionContext.getTenantId(), customerSet);
        if (CollectionUtils.isNotEmpty(dbList)) {
            List<ImportError> errorList = Lists.newArrayList();
            Map<String, IObjectData> dbMap = dbList.stream().collect(Collectors.toMap(i -> i.get(AccountShiftFields.CUSTOMER, String.class), i -> i, (o, n) -> o));
            for (ImportData importData : dataList) {
                String customer = importData.getData().get(AccountShiftFields.CUSTOMER, String.class);
                if (dbMap.containsKey(customer)) {
                    errorList.add(new ImportError(importData.getRowNo(), String.format("%s在系统中已存在", customerFieldLabel))); //ignoreI18n
                }
            }
            mergeErrorList(errorList);
        }
        //单次表格内容重复验证
        {
            List<ImportError> errorList = Lists.newArrayList();
            Map<String, List<ImportData>> customerMap = dataList.stream().collect(Collectors.groupingBy(i -> i.getData().get(AccountShiftFields.CUSTOMER, String.class)));
            for (Map.Entry<String, List<ImportData>> entry : customerMap.entrySet()) {
                if (entry.getValue().size() > 1) {
                    errorList.add(new ImportError(entry.getValue().get(0).getRowNo(), String.format("%s在表格中重复", customerFieldLabel))); //ignoreI18n
                }
            }
            mergeErrorList(errorList);
        }
    }
}
