package com.facishare.crm.fmcg.wq.model;

import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Sets;
import com.facishare.appserver.checkinsoffice.api.model.UserSchedule;
import com.facishare.crm.fmcg.wq.constants.OfficeShiftConstants;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.functions.utils.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.matheclipse.core.expression.S;

import java.io.Serializable;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-18 15:02
 **/
@Data
public class UserScheduleContent implements Serializable {
    /**
     * 超过24h班次
     */
    public static final Integer SCHEDULE_OVER_24H = 1;
    /**
     * 班次时间冲突
     */
    public static final Integer SCHEDULE_TIME_CONFLICT = 2;
    /**
     * 规则排班时长不足
     */
    public static final Integer SCHEDULE_TIME_INSUFFICIENT = 3;
    /**
     * 日期错误  只能修改今日以及今日以后的排班数据
     */
    public static final Integer SCHEDULE_DATE_ERROR = 4;
    /**
     * 重复排班 排班客户班次重复
     */
    public static final Integer SCHEDULE_ACCOUNT_SHIFT_DETAIL_REPEAT = 5;
    /**
     * 超过最大客户班次数量
     */
    public static final Integer SCHEDULE_ACCOUNT_SHIFT_DETAIL_MAX = 6;

    public static String getErrorMessage(int errorCode) {
        if (errorCode == 0) {
            return "成功"; //ignoreI18n
        }
        if (errorCode == SCHEDULE_OVER_24H) {
            return "超过24h班次"; //ignoreI18n
        }
        if (errorCode == SCHEDULE_TIME_CONFLICT) {
            return "班次时间冲突"; //ignoreI18n
        }
        if (errorCode == SCHEDULE_TIME_INSUFFICIENT) {
            return "规则排班时长不足"; //ignoreI18n
        }
        if (errorCode == SCHEDULE_DATE_ERROR) {
            return "只能修改今日以及今日以后的排班数据"; //ignoreI18n
        }
        if (errorCode == SCHEDULE_ACCOUNT_SHIFT_DETAIL_REPEAT) {
            return "排班客户班次重复"; //ignoreI18n
        }
        if (errorCode == SCHEDULE_ACCOUNT_SHIFT_DETAIL_MAX) {
            return MessageFormat.format("用户排班超过最大数据{0}个", OfficeShiftConstants.MAX_USER_SCHEDULE_DETAIL); //ignoreI18n
        }

        return "未知错误"; //ignoreI18n
    }

    /**
     * 客户id列表 表格顺序  客户模式导入导出会有
     */
    private List<String> excelAccountIds = Lists.newArrayList();
    /**
     * 用户idd列表 表格顺序  人员模式导入导出会有
     */
    private List<Integer> excelUserIds = Lists.newArrayList();
    /**
     * 用户id名称映射
     */
    private Map<Integer, String> userNameMap = Maps.newHashMap();
    /**
     * 客户id名称映射
     */
    private Map<String, String> accountNameMap = Maps.newHashMap();
    ;
    /**
     * 用户排班
     */
    private Map<String/*日期*/, Map<Integer/*用户id*/, UserSchedule>> dateUserScheduleMap = Maps.newHashMap();
    ;
    /**
     * 客户班次映射
     */
    private Map<String/*客户id*/, Map<String/*客户班次主对象idd*/, List<String>/*客户班次详情id*/>> accountAndShiftDetailIdsMap = Maps.newHashMap();
    /**
     * 客户班次主对象
     */
    private Map<String/*客户班次主对象id*/, ObjectDataDocument/*客户班次主对象*/> accountShiftMap = Maps.newHashMap();
    /**
     * 客户班次详情映射
     */
    private Map<String/* 客户班次详情id*/, ObjectDataDocument/*客户班次详情*/> accountShiftDetailMap = Maps.newHashMap();

    /**
     * 客户班次用户排班
     */
    private Map<String/*日期*/, Map<String/*班次详情id*/, ShiftSchedule>> dateShiftDetailScheduleMap = Maps.newHashMap();

    /**
     * 门店视图结构
     */
    @Data
    public static class ShiftSchedule {
        /**
         * 用户排班，客户班次详情Id列表
         */
        private String accountShiftDetailId;
        /**
         * 排班日期
         */
        private String date;
        /**
         * 用户id
         */
        private List<ShiftScheduleUser> userIds;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ShiftScheduleUser {
        /**
         * 用户id
         */
        private Integer userId;
        /**
         * 错误原因
         */
        private int errorReason;
    }

    @Data
    public static class UserSchedule {
        /**
         * 用户id
         */
        private Integer userId;
        /**
         * 排班日期
         */
        private String date;
        /**
         * 用户排班，客户班次详情Id列表
         */
        private List<String> accountShiftDetailIdList;


        //-----------------------下面参数后台使用，不需要给前端-----------------------

        /**
         * 用户排班主对象 dataId
         */
        private String mainId;
        /**
         * 用户排班主对象
         */
        private ObjectDataDocument mainData;
        /**
         * 用户排班，客户班次详情Id列表
         */
        private List<ObjectDataDocument> userScheduleDetailList;
        /**
         * 是否变更了数据 需要同步stat表
         */
        private boolean change;
        /**
         * 错误原因
         */
        private int errorReason;
        /**
         * 编辑的数据
         */
        private boolean edit;
    }

}
