package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.esotericsoftware.minlog.Log;
import com.facishare.appserver.checkins.model.enums.ObjApiName;
import com.facishare.appserver.checkinsoffice.api.model.AccountRangeOnRule;
import com.facishare.appserver.checkinsoffice.api.model.PaasIdAndName;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.appserver.utils.EnvUtils;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.facishare.crm.fmcg.wq.dao.UserScheduleDao;
import com.facishare.crm.fmcg.wq.excel.EasyExcelHandler;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.session.FmcgPushSession;
import com.facishare.crm.fmcg.wq.session.SessionContentForText;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.qixin.api.constant.OSS1SubCategory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/03/18/ 16:17
 **/
@Slf4j
public class UserScheduleExportController extends PreDefineController<UserScheduleExportController.Arg, UserScheduleExportController.Result> {

    EmployeeDao employeeDao = SpringUtil.getContext().getBean(EmployeeDao.class);
    OfficeShiftService officeShiftService = SpringUtil.getContext().getBean(OfficeShiftService.class);
    UserScheduleDao userScheduleDao = SpringUtil.getContext().getBean(UserScheduleDao.class);
    private final EasyExcelHandler easyExcelHandler = SpringUtil.getContext().getBean(EasyExcelHandler.class);
    private final FmcgPushSession fmcgPushSession = SpringUtil.getContext().getBean(FmcgPushSession.class);
    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected UserScheduleExportController.Result doService(UserScheduleExportController.Arg arg) {
        if(StringUtils.isBlank(arg.getRuleId()) || CollectionUtils.isEmpty(arg.getUserIds()) || StringUtils.isBlank(arg.getStartDate()) ||
                StringUtils.isBlank(arg.getEndDate()) || !DateUtils.isValidDate(arg.getStartDate(),DateUtils.DateFormat) || !DateUtils.isValidDate(arg.getEndDate(),DateUtils.DateFormat)){
            throw new ValidateException("参数错误"); //ignoreI18n
        }
        int userId = controllerContext.getUser().getUserIdInt();
        String tenantId = controllerContext.getTenantId();
        String ea = serviceFacade.getEAByEI(tenantId);
        boolean isMn = officeShiftService.isMnPmmSchedule(ea);
        fmcgThreadPoolExecutor.execute(()->{
            try {
                List<List<String>> dataList = getExportData(isMn);
                List<List<String>> headList = getHead(isMn,arg.getType());
                easyExcelHandler.pushFile(ea, userId, dataList, headList, arg.getType() == 1 ? "按人员排班导出表" : "按门店排班导出表"); //ignoreI18n
            }catch (Exception e) {
                fmcgPushSession.doSendSession(ea, Lists.newArrayList(userId), new SessionContentForText("考勤", arg.getType() == 1 ? "按人员排班导出失败" : "按门店排班导出失败", "成功"), OSS1SubCategory.WJTZ); //ignoreI18n
            }
        });
        return new UserScheduleExportController.Result();
    }

    private List<List<String>> getHead(boolean isMn, int type){
        List<List<String>> headList = Lists.newArrayList();
        if(type == 1){
            headList.add(Lists.newArrayList("人员")); //ignoreI18n
            headList.add(Lists.newArrayList("门店")); //ignoreI18n
            if(isMn) {
                headList.add(Lists.newArrayList("客户编码")); //ignoreI18n
            }
            headList.add(Lists.newArrayList("排班日期")); //ignoreI18n
            headList.add(Lists.newArrayList("班次")); //ignoreI18n
            headList.add(Lists.newArrayList("班次时间")); //ignoreI18n
        }else{
            headList.add(Lists.newArrayList("门店")); //ignoreI18n
            if(isMn) {
                headList.add(Lists.newArrayList("客户编码")); //ignoreI18n
            }
            headList.add(Lists.newArrayList("班次")); //ignoreI18n
            headList.add(Lists.newArrayList("班次时间")); //ignoreI18n
            headList.add(Lists.newArrayList("排班日期")); //ignoreI18n
            headList.add(Lists.newArrayList("人员")); //ignoreI18n
        }
        return headList;
    }

    private List<List<String>> getExportData(boolean isMn){
        try {
            String tenantId = controllerContext.getTenantId();
//            String ea = serviceFacade.getEAByEI(tenantId);
            // 查询规则中的客户
            Map<String, IObjectData> accountDataMap = getAccountData(tenantId,arg.getAccountIds());
            Map<Integer, String> userIdAndNameMap = employeeDao.getUserNameByIds(tenantId, Sets.newHashSet(arg.getUserIds()));
            if (MapUtils.isEmpty(accountDataMap)) {
                return ListUtils.EMPTY_LIST;
            }
            List<IObjectData> mainUserSchedules = userScheduleDao.getMainListByDateAndUserIds(tenantId, arg.getStartDate(), arg.getEndDate(), Sets.newHashSet(arg.getUserIds()));
            Map<String, Integer> userScheduleIdAndUserIdMap = Maps.newHashMap();
            List<String> userScheduleIds = Lists.newArrayList();
            for (IObjectData mainUserSchedule : mainUserSchedules) {
                userScheduleIdAndUserIdMap.put(mainUserSchedule.getId(), Integer.valueOf(mainUserSchedule.get(UserScheduleFields.USER_ID).toString()));
                userScheduleIds.add(mainUserSchedule.getId());
            }
            List<IObjectData> userScheduleDetails = userScheduleDao.getUserScheduleDetailByMainIds(tenantId, userScheduleIds);
            Set<String> userAccShiftIdSet = userScheduleDetails.stream().map(o -> o.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL).toString()).collect(Collectors.toSet());
            Map<String, IObjectData> accShiftDetailInfoMap = userScheduleDao.getByIds(tenantId, AccountShiftDetailFields.API_NAME, Lists.newArrayList(userAccShiftIdSet)).stream().collect(Collectors.toMap(DBRecord::getId, k2 -> k2, (k1, k2) -> k1));

            if (arg.getType() == 1) {
                return getDataByUser(isMn,accountDataMap, userIdAndNameMap, userScheduleDetails, userScheduleIdAndUserIdMap, accShiftDetailInfoMap);
            } else {
                return getDataByAccount(isMn,accountDataMap, userIdAndNameMap, userScheduleDetails, userScheduleIdAndUserIdMap, accShiftDetailInfoMap);
            }
        }catch (Exception e){
            log.error("getExportData is error",e);
            throw new ValidateException("导出失败"); //ignoreI18n
        }
    }


    private List<List<String>> getDataByUser(boolean isMn,Map<String, IObjectData> accountDataMap,Map<Integer,String> userIdAndNameMap,List<IObjectData> userScheduleDetails,
                                             Map<String,Integer> userScheduleIdAndUserIdMap,Map<String,IObjectData> accShiftDetailInfoMap){
        List<List<String>> dataList = Lists.newArrayList();

        for (IObjectData userScheduleDetail : userScheduleDetails) {
            Object accId = userScheduleDetail.get(UserScheduleDetailFields.ACCOUNT);
            if(Objects.isNull(accId) || !accountDataMap.containsKey(accId.toString())){
                continue;
            }
            List<String> datas = Lists.newArrayList();
            Integer userId = userScheduleIdAndUserIdMap.get(userScheduleDetail.get(UserScheduleDetailFields.USER_SCHEDULE).toString());
            datas.add(userIdAndNameMap.get(userId));
            datas.add(accountDataMap.get(accId.toString()).getName());
            if(isMn) {
                datas.add((String) accountDataMap.get(accId.toString()).get("account_no__c"));
            }
            datas.add(DateUtils.getStringFromTime(Long.parseLong(userScheduleDetail.get(UserScheduleDetailFields.SCHEDULE_DATE).toString()),DateUtils.DateFormat));
            IObjectData accShiftDetail = accShiftDetailInfoMap.get(userScheduleDetail.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL).toString());
            if(Objects.isNull(accShiftDetail)){
                continue;
            }
            datas.add(accShiftDetail.getName());
            datas.add(DateUtils.getStringFromTime(Long.valueOf(accShiftDetail.get(AccountShiftDetailFields.START_TIME).toString()),DateUtils.TimeNoSecondFormat)
                    + "-" + DateUtils.getStringFromTime(Long.valueOf(accShiftDetail.get(AccountShiftDetailFields.END_TIME).toString()),DateUtils.TimeNoSecondFormat));
            dataList.add(datas);
        }
        dataList.sort(new Comparator<List<String>>() {
            @Override
            public int compare(List<String> o1, List<String> o2) {
                if (StringUtils.equals(o1.get(0), (o2.get(0)))) {
                    if (StringUtils.equals(o1.get(1), (o2.get(1)))) {
                        if (StringUtils.equals(o1.get(2), (o2.get(2)))) {
                            return o1.get(4).compareTo(o2.get(4));
                        } else {
                            return o1.get(2).compareTo(o2.get(2));
                        }
                    } else {
                        return o1.get(1).compareTo(o2.get(1));
                    }
                } else {
                    return o1.get(0).compareTo(o2.get(0));
                }
            }
        });
        return dataList;
    }

    private List<List<String>> getDataByAccount(boolean isMn,Map<String, IObjectData> accountDataMap,Map<Integer,String> userIdAndNameMap,List<IObjectData> userScheduleDetails,
                                             Map<String,Integer> userScheduleIdAndUserIdMap,Map<String,IObjectData> accShiftDetailInfoMap){
        List<List<String>> dataList = Lists.newArrayList();
        for (IObjectData userScheduleDetail : userScheduleDetails) {
            Object accId = userScheduleDetail.get(UserScheduleDetailFields.ACCOUNT);
            if(Objects.isNull(accId) || !accountDataMap.containsKey(accId.toString())){
                continue;
            }
            List<String> datas = Lists.newArrayList();
            datas.add(accountDataMap.get(accId.toString()).getName());
            if(isMn) {
                datas.add((String) accountDataMap.get(accId.toString()).get("account_no__c"));
            }
            IObjectData accShiftDetail = accShiftDetailInfoMap.get(userScheduleDetail.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL).toString());
            if(Objects.isNull(accShiftDetail)){
                continue;
            }
            datas.add(accShiftDetail.getName());
            datas.add(DateUtils.getStringFromTime(Long.valueOf(accShiftDetail.get(AccountShiftDetailFields.START_TIME).toString()), DateUtils.TimeNoSecondFormat)
                    + "-" + DateUtils.getStringFromTime(Long.valueOf(accShiftDetail.get(AccountShiftDetailFields.END_TIME).toString()), DateUtils.TimeNoSecondFormat));
            Integer userId = userScheduleIdAndUserIdMap.get(userScheduleDetail.get(UserScheduleDetailFields.USER_SCHEDULE).toString());
            datas.add(DateUtils.getStringFromTime(Long.parseLong(userScheduleDetail.get(UserScheduleDetailFields.SCHEDULE_DATE).toString()),DateUtils.DateFormat));
            datas.add(userIdAndNameMap.get(userId));
            dataList.add(datas);
        }
        dataList.sort(new Comparator<List<String>>() {
            @Override
            public int compare(List<String> o1, List<String> o2) {
                if (StringUtils.equals(o1.get(0), (o2.get(0)))) {
                    if (StringUtils.equals(o1.get(3), (o2.get(3)))) {
                        return o1.get(2).compareTo(o2.get(2));
                    } else {
                        return o1.get(3).compareTo(o2.get(3));
                    }
                } else {
                    return o1.get(0).compareTo(o2.get(0));
                }
            }
        });
        return dataList;
    }
    private Map<String,IObjectData> getAccountData(String tenantId,List<String> accountIds){
        if(CollectionUtils.isNotEmpty(accountIds)){
            return userScheduleDao.getByIds(tenantId, ObjApiName.AccountObj.toString(),accountIds).stream().collect(Collectors.toMap(DBRecord::getId, k2->k2,(k1, k2)->k1));
        }
        AccountRangeOnRule.Result accountRange = officeShiftService.getAccountRange(tenantId, arg.getRuleId());
        if(accountRange.getCustomerRangeType() == 2){
            List<String> accIds = accountRange.getCustomerRangeInfos().stream().map(PaasIdAndName::getId).collect(Collectors.toList());
            return userScheduleDao.getByIds(tenantId, ObjApiName.AccountObj.toString(),accIds).stream().collect(Collectors.toMap(DBRecord::getId,  k2->k2,(k1, k2)->k1));
        }
        if(accountRange.getCustomerRangeType() == 1){
            List<Wheres> wheres = JSONObject.parseObject(accountRange.getCustomerRangeWhere(),new TypeReference<List<Wheres>>(){});
            SearchQuery searchQuery = SearchQuery.builder().build();
            searchQuery.getSearchTemplateQuery().setWheres(wheres);
            List<String> fieldList = Lists.newArrayList();
            if(officeShiftService.isMnPmmSchedule(serviceFacade.getEAByEI(tenantId))){
                fieldList.addAll(Arrays.asList("_id","name","account_no__c"));
            }else{
                fieldList.addAll(Arrays.asList("_id","name"));
            }
            QueryResult<IObjectData> queryResult = userScheduleDao.getAllQueryDataListByQueryWithFields(User.systemUser(tenantId), searchQuery, ObjApiName.AccountObj.toString(),fieldList);
            return queryResult.getData().stream().collect(Collectors.toMap(DBRecord::getId,  k2->k2,(k1, k2)->k1));
        }
        return null;
    }

    @Data
    public static class Arg {

        private List<Integer> userIds;
        private List<String> accountIds;

        private String ruleId;

        /**
         * 1 按照人员 2 按照门店
         */
        private int type;

        private String startDate;

        private String endDate;

    }
    @Data
    public static class Result {

    }
}
