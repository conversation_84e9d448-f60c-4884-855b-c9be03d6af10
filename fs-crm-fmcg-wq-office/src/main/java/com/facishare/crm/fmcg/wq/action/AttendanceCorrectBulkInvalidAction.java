package com.facishare.crm.fmcg.wq.action;

import cn.hutool.core.convert.Convert;
import com.facishare.appserver.checkinsoffice.api.service.CheckinsOfficeV2Service;
import com.facishare.crm.fmcg.wq.constants.AttendanceCorrectDetailFields;
import com.facishare.crm.fmcg.wq.constants.AttendanceCorrectFields;
import com.facishare.crm.fmcg.wq.servcie.AttendanceCorrecctService;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class AttendanceCorrectBulkInvalidAction extends StandardBulkInvalidAction {

    AttendanceCorrecctService attendanceCorrecctService = SpringUtil.getContext().getBean(AttendanceCorrecctService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);

        for (String dataId : arg.getDataIds()) {
            if (!isApprovalFlowStartSuccessOrAsynchronous(dataId)) {
                IObjectData newObjectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), dataId, objectDescribe.getApiName());
                String id = newObjectData.getId();
                String tenantId = newObjectData.getTenantId();
                String ea = serviceFacade.getEAByEI(tenantId);
                //修正日期
                long correctDate = Convert.toDate(newObjectData.get(AttendanceCorrectFields.ATTENDANCE_DATE)).getTime();

                attendanceCorrecctService.saveCorrectTime(ea, attendanceCorrecctService.getCorrerctOwner(newObjectData), correctDate, getPostObjectDetails().get(AttendanceCorrectDetailFields.API_NAME), CheckinsOfficeV2Service.CorrectOperateFlag.DELETE);
                //直接删除掉 不进回收站
                serviceFacade.delete(dataId, objectDescribe.getApiName(), actionContext.getUser());
            }
        }
        return after;
    }
}
