package com.facishare.crm.fmcg.wq.servcie;

import com.facishare.appserver.checkinsoffice.api.model.AccountRangeOnRule;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.common.Pair;

import java.util.List;
import java.util.Map;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-14 19:00
 **/
public interface OfficeShiftService {

    /**
     * 同步用户排班到考勤stat表
     */
    void syncUserScheduleToStat(String tenantId, UserScheduleContent newScheduleContent);

    /**
     * 验证排班表冲突
     * @param userScheduleDataList 目标数据，用户排班主从对象。全量数据
     * @return 返回 验证不通过的数据
     */
    Map<String/*日期*/,Map<Integer/*用户id*/,UserScheduleContent.UserSchedule>> checkUserScheduleConflictAndFillData(String tenantId, UserScheduleContent newScheduleContent,boolean throwException,boolean isOuter);
    void fillDateShiftDetailMap(String tenantId, UserScheduleContent newScheduleContent,boolean isOuter);
    /**
     * 验证成功后upsert排班对象
     * 有删除有更新
     * @param tenantId
     * @param newScheduleContent
     * @param timeoutSeconds 超时时间 导出传大一点，最大值 目前是10分钟
     * @return 返回 验证不通过的数据
     */
    void upsertUserSchedule(String tenantId, UserScheduleContent newScheduleContent,long timeoutSeconds);

    /**
     * db 现有的
     * @return
     */
    Map<Integer,List<IObjectData>> getUserScheduleMapByAccountDetail(String tenantId, List<String> accountDetailIds, String date, boolean isOuter);

    /**
     * accountIds 和 userIds 是并集查询的数据 确保数据准确性
     * @param tenantId
     * @param accountIds
     * @param userIds
     * @param startDate
     * @param endDate
     * @return
     */
    UserScheduleContent batchGetUserSchedule(String tenantId, List<String> accountIds, List<Integer> userIds, String startDate, String endDate);
//    @Deprecated
//    UserScheduleContent batchGetUserScheduleByAccount(String tenantId,List<String> accountIds, String startDate, String endDate);
//    /**
//     * 按照日期拉取排班表，最大限制31天 500人
//     */
//    @Deprecated
//    UserScheduleContent batchGetUserScheduleByUser(String tenantId,List<Integer> userIds, String startDate, String endDate);
    UserScheduleContent batchGetUserScheduleIgnoreOther(String tenantId, List<String> accountIds, List<Integer> userIds, String startDate, String endDate);
//    @Deprecated
//    UserScheduleContent batchGetUserScheduleByUserIgnoreOther(String tenantId,List<Integer> userIds, String startDate, String endDate);
    Map<String/*日期*/, Map<Integer/*用户id*/, UserScheduleContent.UserSchedule>> convretDateUserScheduleMap(List<IObjectData> userSheculeObjList,List<IObjectData> userScheduleDetailByMainIds,boolean isChange);

    /**
     * 根据UserScheduleContent 里的数据 填充客户相关数据
     */
    void fillAccountShiftDetail(String tenantId,UserScheduleContent newScheduleContent,boolean onlyFuture);

    void fillEmployeeName(String tenantId,UserScheduleContent newScheduleContent);

    /**
     * 根据 班次详情对象id 重新从db同步用户排班
     * @param tenantId
     * @param accountDetails
     * @param delFlag 1 是 需要删除的 其他是更新
     */
    void syncUserSchedulesByAccountDetailIds(User user, List<String> accountDetailIds ,int delFlag);


    /**
     * 根据 已经更新的班次详情对象数据，验证新的排班组合是否满足
     * @param user
     * @param updateTimeAccountShiftDetails
     */
    Pair<Map<String, Map<Integer, UserScheduleContent.UserSchedule>>,UserScheduleContent> checkUserScheduleConflictByUpdadteShiftDetail(User user, List<IObjectData> updateTimeAccountShiftDetails);

    AccountRangeOnRule.Result getAccountRange(String tenantId, String ruleId);
    void verifyAccountRange(String tenantId,AccountRangeOnRule.Result accountRange);

    Map<String, IObjectData> getAccIdAndNameByRuleRange(String tenantId,AccountRangeOnRule.Result range);
    Map<String, IObjectData> getAccDataByRuleRange(String tenantId,AccountRangeOnRule.Result range);

    SearchQuery getRuleSearchQuery(String tenantId, String ruleId);


    boolean isMnPmmSchedule(String ea);

}
