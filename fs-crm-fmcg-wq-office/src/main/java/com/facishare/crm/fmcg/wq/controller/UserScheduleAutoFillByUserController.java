package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.crm.fmcg.wq.dao.BaseDao;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.servcie.impl.OfficeShiftServiceImpl;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/

public class UserScheduleAutoFillByUserController extends FmcgIdempotentPreDefineController<UserScheduleAutoFillByUserController.Arg, UserScheduleAutoFillByUserController.Result> {

    OfficeShiftService officeShiftService = SpringUtil.getContext().getBean(OfficeShiftServiceImpl.class);
    AccountShiftDao accountShiftDao = SpringUtil.getContext().getBean(AccountShiftDao.class);
    BaseDao baseDao = SpringUtil.getContext().getBean(BaseDao.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected Result doServiceIdempotent(Arg arg) {
        Result result = new Result();
        //如果 sourceEndDate sourceStartDate间隔天数 比 endDate startDate间隔天数小则报错
        //计算间隔天数
        LocalDate startDate = LocalDate.parse(arg.getStartDate());
        LocalDate endDate = LocalDate.parse(arg.getEndDate());
        //开始时间小于结束时间
        if (startDate.isAfter(endDate)) {
            throw new ValidateException("开始时间大于结束时间"); //ignoreI18n
        }
        if (ChronoUnit.DAYS.between(startDate, endDate) > 31) {
            throw new ValidateException("超过最大间隔31天"); //ignoreI18n
        }
        //调用批量拉取排班表 controller
        String tenantId = controllerContext.getTenantId();
        UserScheduleContent userScheduleContent = officeShiftService.batchGetUserSchedule(tenantId,arg.getAccountIds(), arg.getUserIds(), arg.getStartDate(), arg.getEndDate());
        //如果结束时间早于今天 直接返回
        if (endDate.isBefore(LocalDate.now())) {
            result.setUserScheduleContent(userScheduleContent);
            return result;
        }
        //开始时间和今天最大值
        LocalDate tempStartDate = startDate.isAfter(LocalDate.now()) ? startDate : LocalDate.now();

        Map<Integer, String> userAuthAccountMap = Maps.newConcurrentMap();


        //并发按照人员权限去查客户数据 limit 2
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        SearchQuery searchQuery = officeShiftService.getRuleSearchQuery(tenantId, arg.getRuleId());
        searchQuery.getSearchTemplateQuery().setLimit(2);
        arg.getUserIds().forEach(userId -> parallelTask.submit(() -> {
            try {
                //获取权限的客户
                User build = User.builder().tenantId(tenantId).userId(String.valueOf(userId)).build();
                build.setIsCrmAdmin(Optional.of(false));
                List<IObjectData> authAccountList = accountShiftDao.getDataWithAuth(build, AccountObjConstants.API_NAME, searchQuery);
                if (CollectionUtils.isNotEmpty(authAccountList) && authAccountList.size() == 1) {
                    userAuthAccountMap.put(userId, authAccountList.get(0).getId());
                }

            } catch (Exception e) {
                log.error("get authData error,tenantId:{},userId:{}", tenantId, userId, e);
                throw e;
            }
        }));
        try {
            //最大600s
            boolean success = parallelTask.await(10L, TimeUnit.SECONDS);
            if (!success) {
                log.error("getAuthData failed! ei:{}", tenantId);
                throw new ValidateException("查询人员权限数据错误"); //ignoreI18n
            }
        } catch (TimeoutException e) {
            log.error("getAuthData time out! ei:{}", tenantId, e);
            throw new ValidateException("查询人员权限数据超时,请重试！"); //ignoreI18n
        }

        //查门店班次详情
        if (userScheduleContent.getExcelAccountIds() == null) {
            userScheduleContent.setExcelAccountIds(Lists.newArrayList());
        }
        userScheduleContent.getExcelAccountIds().addAll(userAuthAccountMap.values());
        officeShiftService.fillAccountShiftDetail(tenantId, userScheduleContent, false);
        //可以处理的数据
        Map<Integer, String> userIdAndDetailIdMap = Maps.newConcurrentMap();
        for (Map.Entry<Integer, String> integerStringEntry : userAuthAccountMap.entrySet()) {
            Integer userId = integerStringEntry.getKey();
            String authAccountId = integerStringEntry.getValue();
            if (MapUtils.isNotEmpty(userScheduleContent.getAccountAndShiftDetailIdsMap().get(authAccountId))) {
                List<String> values = userScheduleContent.getAccountAndShiftDetailIdsMap().get(authAccountId).values().iterator().next();
                if (CollectionUtils.isNotEmpty(values) && values.size() == 1) {
                    userIdAndDetailIdMap.put(userId, values.get(0));
                }
            }
        }
        if (MapUtils.isEmpty(userIdAndDetailIdMap)) {
            throw new ValidateException("当前排班人员/门店没有符合自动排班条件的数据，请手动排班"); //ignoreI18n
        }
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> dbDateUserScheduleMap = userScheduleContent.getDateUserScheduleMap();
//        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> newDateUserScheduleMap = userScheduleContent.getDateUserScheduleMap();
        //日期循环 tempStartDate 到endDate
        while (!tempStartDate.isAfter(endDate) && MapUtils.isNotEmpty(userIdAndDetailIdMap)) {
            String tempStartDateStr = tempStartDate.toString();
            //获取当天的排班数据
            Map<Integer, UserScheduleContent.UserSchedule> userScheduleMap = dbDateUserScheduleMap.get(tempStartDateStr);
            if (userScheduleMap == null) {
                userScheduleMap = Maps.newHashMap();
                userScheduleContent.getDateUserScheduleMap().put(tempStartDateStr, userScheduleMap);
            }
            for (Map.Entry<Integer, String> integerStringEntry : userIdAndDetailIdMap.entrySet()) {
                Integer userId = integerStringEntry.getKey();
                if (!userScheduleMap.containsKey(userId) || CollectionUtils.isEmpty(userScheduleMap.get(userId).getAccountShiftDetailIdList())) {
                    UserScheduleContent.UserSchedule userSchedule = new UserScheduleContent.UserSchedule();
                    userSchedule.setUserId(userId);
                    userSchedule.setDate(tempStartDateStr);
                    userSchedule.setAccountShiftDetailIdList(Lists.newArrayList(userIdAndDetailIdMap.get(userId)));
//                    userSchedule.setEdit(true);
                    userScheduleMap.put(userId, userSchedule);
                }
            }
            tempStartDate = tempStartDate.plusDays(1);
        }
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> stringMapMap = officeShiftService.checkUserScheduleConflictAndFillData(controllerContext.getTenantId(), userScheduleContent, false
        ,arg.getUserIds().stream().anyMatch(o->o.intValue() >*********));
        if (!stringMapMap.isEmpty()) {
            //本次变更会导致 xxx日期，xxx,xxx用户排班冲突
            StringBuilder sb = new StringBuilder();
            sb.append("操作失败 : "); //ignoreI18n
            int beforeLength = sb.length();
            //按照错误类型重新聚合
            stringMapMap.entrySet().stream().flatMap(o -> o.getValue().entrySet().stream()).collect(Collectors.groupingBy(o -> o.getValue().getErrorReason())).entrySet().stream().forEach(o -> {
                //过滤掉日期异常
                if (o.getKey() == UserScheduleContent.SCHEDULE_DATE_ERROR) {
                    return;
                }
                //按照日期聚合
                o.getValue().stream().collect(Collectors.groupingBy(o1 -> o1.getValue().getDate())).entrySet().stream().forEach(o2 -> {
                    sb.append(o2.getKey()).append(":");
                    sb.append(o2.getValue().stream().map(o1 -> userScheduleContent.getUserNameMap().getOrDefault(o1.getKey(), o1.getKey().toString())).collect(Collectors.joining(",")));
                });
                sb.append(UserScheduleContent.getErrorMessage(o.getKey())).append(";");
            });
            if (sb.length() != beforeLength){
                throw new ValidateException(sb.toString());
            }
        }
        //upsert
        officeShiftService.upsertUserSchedule(controllerContext.getTenantId(), userScheduleContent, 10);
        if (CollectionUtils.isNotEmpty(userScheduleContent.getExcelUserIds()) && CollectionUtils.isNotEmpty(arg.getUserIds())) {
            userScheduleContent.getExcelUserIds().removeIf(o->!arg.getUserIds().contains(o));
        }
        return new Result(userScheduleContent);
    }


    @Override
    protected String getIdempotentKey(Arg arg) {
        return controllerContext.getTenantId() + controllerContext.getMethodName() + JSON.toJSONString(arg).hashCode();
    }

    @Data
    public static class Arg {
        /**
         * accountIds
         */
        private List<String> accountIds;
        /**
         * userIds
         */
        private List<Integer> userIds;
        /**
         * 开始日期
         */
        private String startDate;
        /**
         * 结束日期
         */
        private String endDate;
        /**
         * 规则id 查考勤范围
         */
        private String ruleId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Result {
        private UserScheduleContent userScheduleContent;

    }
}
