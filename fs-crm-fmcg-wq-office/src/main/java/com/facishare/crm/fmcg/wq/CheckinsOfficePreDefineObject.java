package com.facishare.crm.fmcg.wq;

import com.facishare.paas.appframework.core.model.ActionClassInfo;
import com.facishare.paas.appframework.core.model.ControllerClassInfo;
import com.facishare.paas.appframework.core.model.PreDefineObject;
import com.facishare.paas.appframework.core.model.PreDefineObjectRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;

public enum CheckinsOfficePreDefineObject implements PreDefineObject {

    Shifts("ShiftsObj"),
    AttendanceCorrect("AttendanceCorrectObj"),//考勤修正
    AttendanceCorrectDetail("AttendanceCorrectDetailObj"),//考勤修正详情
    ShiftAdjustment("ShiftAdjustmentObj"),
    UserSchedule("UserScheduleObj"),
    UserScheduleDetail("UserScheduleDetailObj"),
    AccountShift("AccountShiftObj"),
    AccountShiftDetail("AccountShiftDetailObj"),

        ;

    private static final Logger logger = LoggerFactory.getLogger(CheckinsOfficePreDefineObject.class);

    private final String apiName;

    private static final String PACKAGE_NAME = CheckinsOfficePreDefineObject.class.getPackage().getName();

    CheckinsOfficePreDefineObject(String apiName) {
        this.apiName = apiName;
    }

    public static CheckinsOfficePreDefineObject getEnum(String apiName) {
        List<CheckinsOfficePreDefineObject> list = Arrays.asList(CheckinsOfficePreDefineObject.values());
        return list.stream().filter(m -> m.getApiName().equalsIgnoreCase(apiName)).findAny().orElse(null);
    }

    public static void init() {
        for (CheckinsOfficePreDefineObject object : CheckinsOfficePreDefineObject.values()) {
            logger.info("init {}", object.toString());
            PreDefineObjectRegistry.register(object);
        }
    }

    @Override
    public String getApiName() {
        return this.apiName;
    }

    @Override
    public String getPackageName() {
        return PACKAGE_NAME;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String className = PACKAGE_NAME + ".action." + this + actionCode + "Action";
        return new ActionClassInfo(className);
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String className = PACKAGE_NAME + ".controller." + this + methodName + "Controller";
        return new ControllerClassInfo(className);
    }

}
