package com.facishare.crm.fmcg.wq.controller;

import cn.hutool.core.collection.CollUtil;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.servcie.impl.OfficeShiftServiceImpl;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 客户视图排班表列表查询接口
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/
public class UserScheduleListByAccountController extends PreDefineController<UserScheduleListByAccountController.Arg, UserScheduleListByAccountController.Result> {

    OfficeShiftService officeShiftService =  SpringUtil.getContext().getBean(OfficeShiftServiceImpl.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected Result doService(Arg arg) {
        Result result = new Result();
        LocalDate startDate = LocalDate.parse(arg.getStartDate());
        LocalDate endDate = LocalDate.parse(arg.getEndDate());
        if (startDate.isAfter(endDate)) {
            throw new ValidateException("开始时间大于结束时间"); //ignoreI18n
        }
        if (ChronoUnit.DAYS.between(startDate, endDate) > 31) {
            throw new ValidateException("超过最大间隔31天"); //ignoreI18n
        }
        UserScheduleContent userScheduleContent = officeShiftService.batchGetUserSchedule(controllerContext.getTenantId(), arg.getAccountIds(),arg.getUserIds(),arg.getStartDate(),arg.getEndDate());
        if (arg.getUserSelected() != 1 && CollectionUtils.isNotEmpty(arg.getAccountIds())){
            userScheduleContent.setExcelAccountIds(ListUtils.sum(userScheduleContent.getExcelAccountIds(), arg.getAccountIds()));
            //筛选器没选择人的话  相当与取单条件 需要补齐客户
            officeShiftService.fillAccountShiftDetail(controllerContext.getTenantId(),userScheduleContent,false);
            userScheduleContent.setExcelUserIds(arg.getUserIds());
        }
        result.setUserScheduleContent(userScheduleContent);
        return result;
    }

    @Data
    public static class Arg {
        /**
         * userIds
         */
        private List<Integer> userIds;

        private List<String> accountIds;

        private String startDate;

        private String endDate;

        /**
         * 已经筛选了用户
         */
        private int userSelected;

    }
    @Data
    public static class Result {
        private UserScheduleContent userScheduleContent;

    }
}
