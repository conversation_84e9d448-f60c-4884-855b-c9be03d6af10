package com.facishare.crm.fmcg.wq.action;

import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.crm.fmcg.wq.constants.UserScheduleDetailFields;
import com.facishare.crm.fmcg.wq.constants.UserScheduleFields;
import com.facishare.crm.fmcg.wq.dao.UserScheduleDao;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-23 10:13
 **/
public class UserScheduleEditAction extends StandardEditAction {
    UserScheduleDao userScheduleDao = SpringUtil.getContext().getBean(UserScheduleDao.class);
    OfficeShiftService officeShiftService = SpringUtil.getContext().getBean(OfficeShiftService.class);
    UserScheduleContent newScheduleContent;
    @Override
    protected void before(Arg arg) {
        //判断排班日期必须是今日之后
        ObjectDataExt objectDataExt = ObjectDataExt.of(arg.getObjectData());
        //自动赋值 如果userId 为空则取owner
//        if (!ObjectUtils.parseEmployeeValue(objectDataExt, UserScheduleFields.USER_ID).isPresent()) {
        int userId;
        Optional<String> outOwnerIdOp = objectDataExt.getOutOwnerId();
        if (outOwnerIdOp.isPresent()) {
            userId = Integer.valueOf(outOwnerIdOp.get());
            arg.getObjectData().put(UserScheduleFields.USER_ID, userId);
            //清空 owner
            objectDataExt.setOwner(Lists.newArrayList("-10000"));
        } else {
            userId = objectDataExt.getOwnerIdInt();
            arg.getObjectData().put(UserScheduleFields.USER_ID, userId);
        }
//        }
        if (arg.getObjectData().get(UserScheduleFields.DATE) == null) {
            throw new ValidateException("排班日期必填"); //ignoreI18n
        }
        Long date = objectDataExt.get(UserScheduleFields.DATE, Long.class);
        if (date < LocalDate.now().atStartOfDay(ZoneId.of("Asia/Shanghai")).toInstant().toEpochMilli()) {
            throw new ValidateException("排班日期必须是今日之后"); //ignoreI18n
        }
        //排班人员不能编辑
        if (updatedFieldMap.keySet().contains(UserScheduleFields.USER_ID)){
            throw new ValidateException("排班人员不能编辑"); //ignoreI18n
        }
        if (updatedFieldMap.keySet().contains(UserScheduleFields.DATE)){
            throw new ValidateException("排班日期不能编辑"); //ignoreI18n
        }


        //唯一键验证 一个用户一个日期只能有一条数据
        String dateStr = DateUtils.getStringFromTime(date, DateUtils.DateFormat);
        List<IObjectData> mainListByDateAndUserIds = userScheduleDao.getMainListByDateAndUserIds(actionContext.getTenantId(), dateStr, dateStr, Sets.newHashSet(objectDataExt.get(UserScheduleFields.USER_ID, Integer.class)));
        if (CollectionUtils.isNotEmpty(mainListByDateAndUserIds) && mainListByDateAndUserIds.stream().anyMatch(o -> !StringUtils.equals(o.getId(), arg.getObjectData().getId()))) {
            throw new ValidateException("同一用户相同日期只能有一条排班数据"); //ignoreI18n
        }
        newScheduleContent = new UserScheduleContent();
        //从对象补数据
        List<IObjectData> userScheduleDetails = arg.getDetails().getOrDefault(UserScheduleDetailFields.API_NAME, Lists.newArrayList()).stream().map(o->o.toObjectData()).collect(Collectors.toList());
        userScheduleDetails.forEach(o -> {
            o.set(UserScheduleDetailFields.USER_SCHEDULE, arg.getObjectData().getId());
        });
        newScheduleContent.setDateUserScheduleMap(officeShiftService.convretDateUserScheduleMap(Lists.newArrayList(arg.getObjectData().toObjectData()), userScheduleDetails, true));
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> stringMapMap = officeShiftService.checkUserScheduleConflictAndFillData(actionContext.getTenantId(), newScheduleContent, true,userId > 100000000);
        if (!stringMapMap.isEmpty()) {
            //本次变更会导致 xxx日期，xxx,xxx用户排班冲突
            StringBuilder sb = new StringBuilder();
            sb.append("操作失败 : "); //ignoreI18n
            //按照错误类型重新聚合
            stringMapMap.entrySet().stream().flatMap(o -> o.getValue().entrySet().stream()).collect(Collectors.groupingBy(o -> o.getValue().getErrorReason())).entrySet().stream().forEach(o -> {
                //按照日期聚合
                o.getValue().stream().collect(Collectors.groupingBy(o1 -> o1.getValue().getDate())).entrySet().stream().forEach(o2 -> {
                    sb.append(o2.getKey()).append(":");
                    sb.append(o2.getValue().stream().map(o1 ->newScheduleContent.getUserNameMap().getOrDefault(o1.getKey(), o1.getKey().toString())).collect(Collectors.joining(",")));
                });
                sb.append(UserScheduleContent.getErrorMessage(o.getKey())).append(";");
            });
            throw new ValidateException(sb.toString());
        }
        //补充从对象数据
        for (IObjectData userScheduleDetail : userScheduleDetails) {
            //客户排班详情id
            String accountShiftDetailId = userScheduleDetail.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, String.class);
            //客户排班详情对象
            IObjectData accountShiftDetailObj = newScheduleContent.getAccountShiftDetailMap().get( accountShiftDetailId).toObjectData();
            //客户班次id
            String accountShiftId = accountShiftDetailObj.get(AccountShiftDetailFields.ACCOUNT_SHIFT, String.class);
            //客户班次对象
            IObjectData accountShiftObj = newScheduleContent.getAccountShiftMap().get(accountShiftId).toObjectData();
            //客户id
            String accountId = accountShiftObj.get(AccountShiftFields.CUSTOMER, String.class);
            //userScheduleDetail 值重写
            userScheduleDetail.set(UserScheduleDetailFields.ACCOUNT_SHIFT, accountShiftId);
            userScheduleDetail.set(UserScheduleDetailFields.ACCOUNT, accountId);
            userScheduleDetail.set(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, accountShiftDetailId);
            //时间快照 重写
            userScheduleDetail.set(UserScheduleDetailFields.CURRENT_SCHEDULE_TIME, accountShiftDetailObj.get("display_name",String.class));
            //时长
            userScheduleDetail.set(UserScheduleDetailFields.CURRENT_DURATION, accountShiftDetailObj.get(AccountShiftDetailFields.DURATION, Long.class));
        }
        // 调整到最后 因为上面有初始化值的逻辑
        super.before(arg);
    }



    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        //同步stat表
        officeShiftService.syncUserScheduleToStat(actionContext.getTenantId(), newScheduleContent);
        return after;
    }
}
