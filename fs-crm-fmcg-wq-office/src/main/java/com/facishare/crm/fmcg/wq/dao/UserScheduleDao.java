package com.facishare.crm.fmcg.wq.dao;

import com.alibaba.druid.util.StringUtils;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import static com.facishare.crm.fmcg.wq.util.TimeUtils.convertTimestampToHoursAndMinutes;

/**
 * @program: fs-crm-fmcg-wq
 * @description:用户排班对象操作
 * @author: zhangsm
 * @create: 2023-12-19 19:08
 **/
@Component
public class UserScheduleDao extends AbstractDao {
    final static List<String> userScheduleShowFields = ConfigUtils.getFields(UserScheduleFields.class);
    final static List<String> userScheduleDetailShowFields = ConfigUtils.getFields(UserScheduleDetailFields.class);

    public List<IObjectData> getMainListByDateAndUserIds(String tenantId, String startDate, String endDate, Set<Integer> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return ListUtils.EMPTY_LIST;
        }
        //根据条件查询UserScheduleObj
        long startDateTime = DateUtils.getDateTimeFromString(startDate, DateUtils.DateFormat);
        long endDateTime = DateUtils.getDateTimeFromString(endDate, DateUtils.DateFormat) + DateUtils.ONE_DAY - 1;
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .betweenDate(UserScheduleFields.DATE, startDateTime, endDateTime)
                .in(UserScheduleFields.USER_ID, userIds)
                .build(), UserScheduleFields.API_NAME, userScheduleShowFields);
    }

    public List<IObjectData> getUserScheduleDetailListByDateAndAccountIds(String tenantId, String startDate, String endDate, Set<String> accountIdSet, Set<String> noUserScheduleDetailIds) {
        if (CollectionUtils.isEmpty(accountIdSet)) {
            return ListUtils.EMPTY_LIST;
        }
        //根据条件查询UserScheduleDetailObj
        long startDateTime = DateUtils.getDateTimeFromString(startDate, DateUtils.DateFormat);
        long endDateTime = DateUtils.getDateTimeFromString(endDate, DateUtils.DateFormat) + DateUtils.ONE_DAY - 1;
        SearchQuery.SearchQueryBuilder in = SearchQuery.builder()
                .betweenDate(UserScheduleDetailFields.SCHEDULE_DATE, startDateTime, endDateTime)
                .exist(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL)
                .in(UserScheduleDetailFields.ACCOUNT, accountIdSet);
        if (CollectionUtils.isNotEmpty(noUserScheduleDetailIds)) {
            in.nin(BaseField.id.getApiName(), noUserScheduleDetailIds);

        }
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), in
                .build(), UserScheduleDetailFields.API_NAME, userScheduleDetailShowFields);
    }

    public List<IObjectData> getUserScheduleDetailByMainIds(String tenantId, List<String> mainIds) {
        if (CollectionUtils.isEmpty(mainIds)) {
            return ListUtils.EMPTY_LIST;
        }
        //根据条件查询UserScheduleDetailObj
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(UserScheduleDetailFields.USER_SCHEDULE, mainIds)
                .exist(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL)
                .build(), UserScheduleDetailFields.API_NAME, userScheduleDetailShowFields);
    }

    @NotNull
    public IObjectData convertUserScheduleDetailObj(String tenantId, Integer userId, String date, String
            userScheduleMainId, IObjectData accountShiftDetailObj, IObjectData accountShift) {
        IObjectData userScheduleDetailObj = createBaseObjectData(tenantId, String.valueOf(userId), UserScheduleDetailFields.API_NAME);
        userScheduleDetailObj.set(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, accountShiftDetailObj.getId());
        userScheduleDetailObj.set(UserScheduleDetailFields.USER_SCHEDULE, userScheduleMainId);
        userScheduleDetailObj.set(UserScheduleDetailFields.SCHEDULE_DATE, date);
        userScheduleDetailObj.set(UserScheduleDetailFields.ACCOUNT_SHIFT, accountShiftDetailObj.get(UserScheduleDetailFields.ACCOUNT_SHIFT, String.class));
        userScheduleDetailObj.set(UserScheduleDetailFields.ACCOUNT, accountShift.get(AccountShiftFields.CUSTOMER, String.class));
        //班次时间
        userScheduleDetailObj.set(UserScheduleDetailFields.CURRENT_SCHEDULE_TIME, accountShiftDetailObj.get("display_name", String.class));
        //时长
        userScheduleDetailObj.set(UserScheduleDetailFields.CURRENT_DURATION, accountShiftDetailObj.get(AccountShiftDetailFields.DURATION, Long.class));
        return userScheduleDetailObj;
    }

    /**
     * 通过 门店班次详情ids 获取用户班次详情list
     * @param tenantId 租户id
     * @param accountShiftDetailIds 门店班次详情ids
     * @param date 日期
     * @return
     */
    public List<IObjectData> getUserScheduleDetailByDateAndShiftDetailIds(String tenantId,String date, List<String> accountShiftDetailIds) {
        if (CollectionUtils.isEmpty(accountShiftDetailIds)) {
            return ListUtils.EMPTY_LIST;
        }
        accountShiftDetailIds.removeIf(StringUtils::isEmpty);
        if (CollectionUtils.isEmpty(accountShiftDetailIds)) {
            return ListUtils.EMPTY_LIST;
        }
        //根据条件查询UserScheduleDetailObj
        long startDateTime = DateUtils.getDateTimeFromString(date, DateUtils.DateFormat);
        long endDateTime = DateUtils.getDateTimeFromString(date, DateUtils.DateFormat) + DateUtils.ONE_DAY - 1;
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .betweenDate(UserScheduleDetailFields.SCHEDULE_DATE, startDateTime, endDateTime)
                .in(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, accountShiftDetailIds)
                .build(), UserScheduleDetailFields.API_NAME, userScheduleDetailShowFields);
    }

    public List<IObjectData> getUserScheduleDetailByDateAndShiftDetailIds(String tenantId,String startDate,String endDate, List<String> accountShiftDetailIds,Set<String> noUserScheduleDetailIds) {
        if (CollectionUtils.isEmpty(accountShiftDetailIds)) {
            return ListUtils.EMPTY_LIST;
        }
        accountShiftDetailIds.removeIf(StringUtils::isEmpty);
        if (CollectionUtils.isEmpty(accountShiftDetailIds)) {
            return ListUtils.EMPTY_LIST;
        }
        //根据条件查询UserScheduleDetailObj
        long startDateTime = DateUtils.getDateTimeFromString(startDate, DateUtils.DateFormat);
        long endDateTime = DateUtils.getDateTimeFromString(endDate, DateUtils.DateFormat) + DateUtils.ONE_DAY - 1;
        SearchQuery.SearchQueryBuilder in = SearchQuery.builder()
                .betweenDate(UserScheduleDetailFields.SCHEDULE_DATE, startDateTime, endDateTime)
                .in(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL, accountShiftDetailIds);
        if (CollectionUtils.isNotEmpty(noUserScheduleDetailIds)) {
            in.nin(BaseField.id.getApiName(), noUserScheduleDetailIds);

        }
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), in
                .build(), UserScheduleDetailFields.API_NAME, userScheduleDetailShowFields);
    }


    //为班次详情对象的开始时间~结束时间，格式为 09:00~18:00
//    public  String getFormatScheduleTimeStr(IObjectData accountShiftDetailObj) {
//        //对象apiName验证
//        if (!AccountShiftDetailFields.API_NAME.equals(accountShiftDetailObj.getDescribeApiName())) {
//            throw new ValidateException("getFormatScheduleTimeStr 对象apiName错误");
//        }
//        Long endTime = accountShiftDetailObj.get(AccountShiftDetailFields.END_TIME, Long.class);
//        Long startTime = accountShiftDetailObj.get(AccountShiftDetailFields.START_TIME, Long.class);
//        String formatScheduleTime = startTime > endTime ? DateUtils.getStringFromTime(startTime, DateUtils.TimeNoSecondFormat) + "~(次日)" + DateUtils.getStringFromTime(endTime, DateUtils.TimeNoSecondFormat) : DateUtils.getStringFromTime(startTime, DateUtils.TimeNoSecondFormat) + "~" + DateUtils.getStringFromTime(endTime, DateUtils.TimeNoSecondFormat);
//        return formatScheduleTime;
//    }

    public void formatUserScheduleDuration(Collection<IObjectData> accoutShiftDetails){
        //循环 判断apiName 并且 duration 没有值的话 填充
        for (IObjectData accoutShiftDetail : accoutShiftDetails) {
            if (accoutShiftDetail.getDescribeApiName().equals(UserScheduleFields.API_NAME)) {
                if (accoutShiftDetail.get(UserScheduleFields.CURRENT_DURATION_TOTAL) != null) {
                    //结束时间-开始时间
                    String durationStr = convertTimestampToHoursAndMinutes(accoutShiftDetail.get(UserScheduleFields.CURRENT_DURATION_TOTAL, Long.class));
                    accoutShiftDetail.set(UserScheduleFields.CURRENT_DURATION_TOTAL_FORMULA, durationStr);
                    accoutShiftDetail.set(UserScheduleFields.CURRENT_DURATION_TOTAL, durationStr);
                }
            }
        }
    }
    public void formatUserScheduleDetailDuration(Collection<IObjectData> accoutShiftDetails){
        //循环 判断apiName 并且 duration 没有值的话 填充
        for (IObjectData accoutShiftDetail : accoutShiftDetails) {
            if (accoutShiftDetail.getDescribeApiName().equals(UserScheduleDetailFields.API_NAME)) {
                if (accoutShiftDetail.get(UserScheduleDetailFields.CURRENT_DURATION) != null) {
                    //结束时间-开始时间
                    String durationStr = convertTimestampToHoursAndMinutes(accoutShiftDetail.get(UserScheduleDetailFields.CURRENT_DURATION, Long.class));
                    accoutShiftDetail.set(UserScheduleDetailFields.CURRENT_DURATION, durationStr);
                    accoutShiftDetail.set(UserScheduleDetailFields.CURRENT_DURATION_FORMULA, durationStr);
                }
            }
        }
    }


    public List<IObjectData> getUserScheduleByDateAndUserIds(String tenantId, String date, Set<Integer> userIds) {

        if (CollectionUtils.isEmpty(userIds)) {
            return ListUtils.EMPTY_LIST;
        }
        //根据条件查询UserScheduleObj
        long startDateTime = DateUtils.getDateTimeFromString(date, DateUtils.DateFormat);
        long endDateTime = DateUtils.getDateTimeFromString(date, DateUtils.DateFormat) + DateUtils.ONE_DAY - 1;
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .betweenDate(UserScheduleFields.DATE, startDateTime, endDateTime)
                .in(UserScheduleFields.USER_ID, userIds)
                .build(), UserScheduleFields.API_NAME, userScheduleShowFields);
    }
}
