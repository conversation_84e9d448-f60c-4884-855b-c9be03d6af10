package com.facishare.crm.fmcg.wq.dao;

import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.crm.fmcg.wq.util.ConfigUtils;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.collections.ListUtils;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import static com.facishare.crm.fmcg.wq.util.TimeUtils.convertTimestampToHoursAndMinutes;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 客户班次对象操作
 * @author: zhangsm
 * @create: 2023-12-19 19:09
 **/
@Component
public class AccountShiftDao extends AbstractDao {
    final static List<String> accountShiftShowFields = ConfigUtils.getFields(AccountShiftFields.class);
    final static List<String> accountShiftDetailsShowFields = ConfigUtils.getFields(AccountShiftDetailFields.class);


    public List<IObjectData> getAccountShiftByAccountId(String tenantId, Set<String> accountIds) {
        if (accountIds == null || accountIds.isEmpty())
            return ListUtils.EMPTY_LIST;
       return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(AccountShiftFields.CUSTOMER, accountIds)
                .build(), AccountShiftFields.API_NAME,accountShiftShowFields);
    }
    public List<IObjectData> getAccountShiftDetailByMainIds(String tenantId,Set<String> accountShiftIds){
        if (accountShiftIds == null || accountShiftIds.isEmpty())
            return Lists.newArrayList();
        accountShiftIds.remove(null);
        if (accountShiftIds == null || accountShiftIds.isEmpty())
            return ListUtils.EMPTY_LIST;
        //查子对象
        List<IObjectData> allIObjectDataListByQueryWithFields = getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in(AccountShiftDetailFields.ACCOUNT_SHIFT, accountShiftIds)
                .build(), AccountShiftDetailFields.API_NAME, accountShiftDetailsShowFields);
        fillDuration(allIObjectDataListByQueryWithFields);
        return allIObjectDataListByQueryWithFields;
    }


    public List<IObjectData> getAccountShiftDetailsByIds(String tenantId, Set<String> accountShiftDetailIds) {
        if (accountShiftDetailIds == null || accountShiftDetailIds.isEmpty())
            return ListUtils.EMPTY_LIST;
        List<IObjectData> allIObjectDataListByQueryWithFields = getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in("_id", accountShiftDetailIds)
                .build(), AccountShiftDetailFields.API_NAME, accountShiftDetailsShowFields);
        fillDuration(allIObjectDataListByQueryWithFields);
        return allIObjectDataListByQueryWithFields;
    }

    public List<IObjectData> getAccountShiftByIds(String tenantId, Set<String> accountShiftIds) {
        if (accountShiftIds == null || accountShiftIds.isEmpty())
            return ListUtils.EMPTY_LIST;
        return getAllIObjectDataListByQueryWithFields(User.systemUser(tenantId), SearchQuery.builder()
                .in("_id", accountShiftIds)
                .build(), AccountShiftFields.API_NAME,accountShiftShowFields);
    }

    public void fillDuration(Collection<IObjectData> accoutShiftDetails){
        //循环 判断apiName 并且 duration 没有值的话 填充
        for (IObjectData accoutShiftDetail : accoutShiftDetails) {
            if (accoutShiftDetail.getDescribeApiName().equals(AccountShiftDetailFields.API_NAME)) {
                if (accoutShiftDetail.get(AccountShiftDetailFields.DURATION) == null) {
                    //结束时间-开始时间
                    long duration = accoutShiftDetail.get(AccountShiftDetailFields.END_TIME, Long.class) - accoutShiftDetail.get(AccountShiftDetailFields.START_TIME, Long.class);
                    accoutShiftDetail.set(AccountShiftDetailFields.DURATION,duration);
                }
            }
        }
    }

    public void formatDuration(Collection<IObjectData> accoutShiftDetails){
        //循环 判断apiName 并且 duration 没有值的话 填充
        for (IObjectData accoutShiftDetail : accoutShiftDetails) {
            if (accoutShiftDetail.getDescribeApiName().equals(AccountShiftDetailFields.API_NAME)) {
                if (accoutShiftDetail.get(AccountShiftDetailFields.DURATION) != null) {
                    //结束时间-开始时间
                    String durationStr = convertTimestampToHoursAndMinutes(accoutShiftDetail.get(AccountShiftDetailFields.DURATION, Long.class));
                    accoutShiftDetail.set(AccountShiftDetailFields.DURATION_FORMULA, durationStr);
                    accoutShiftDetail.set(AccountShiftDetailFields.DURATION, durationStr);
                }
            }
        }
    }

}
