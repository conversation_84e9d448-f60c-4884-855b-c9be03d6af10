package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.crm.fmcg.wq.dao.UserScheduleDao;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;


@Slf4j
public class UserScheduleInvalidAction extends StandardInvalidAction {
    @Override
    protected void before(Arg arg) {
        //不支持作废
        throw new ValidateException("不允许作废用户排班数据,删除用户排班可以通过编辑删除用户排班详情对象实现"); //ignoreI18n
    }
}
