package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.crm.fmcg.wq.constants.UserScheduleDetailFields;
import com.facishare.crm.fmcg.wq.constants.UserScheduleFields;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.util.ObjectUtils;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.IdempotentRequestContext;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/
public class UserScheduleBatchAddController extends FmcgIdempotentPreDefineController<UserScheduleBatchAddController.Arg, UserScheduleBatchAddController.Result> {
    OfficeShiftService officeShiftService = SpringUtil.getContext().getBean(OfficeShiftService.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }


    @Override
    protected Result doServiceIdempotent(Arg arg) {
        List<UserScheduleContent.UserSchedule> userScheduleSimpleList = arg.getUserScheduleSimpleList();
        String todayStr = LocalDate.now().toString();
        for (UserScheduleContent.UserSchedule userSchedule : userScheduleSimpleList) {
            //日期必填
            if (StringUtils.isBlank(userSchedule.getDate())) {
                throw new ValidateException("排班日期必填"); //ignoreI18n
            }
            //排班人必填
            if (userSchedule.getUserId() == null || userSchedule.getUserId().intValue() == 0) {
                throw new ValidateException("排班人员必填"); //ignoreI18n
            }
            if (userSchedule.getDate().compareTo(todayStr) < 0) {
                throw new ValidateException("排班日期必须是今日之后"); //ignoreI18n
            }
        }

        UserScheduleContent newScheduleContent = new UserScheduleContent();
        //Map<String/*日期*/, Map<Integer/*用户id*/, UserSchedule>> 二级的map
        newScheduleContent.setDateUserScheduleMap(userScheduleSimpleList.stream().collect(Collectors.groupingBy(UserScheduleContent.UserSchedule::getDate, Collectors.toMap(UserScheduleContent.UserSchedule::getUserId, userSchedule -> userSchedule))));
        //check
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> stringMapMap = officeShiftService.checkUserScheduleConflictAndFillData(controllerContext.getTenantId(), newScheduleContent,true,arg.getIsOuter() == 1);
        if (!stringMapMap.isEmpty()) {
            //本次变更会导致 xxx日期，xxx,xxx用户排班冲突
            StringBuilder sb = new StringBuilder();
            sb.append("操作失败 : "); //ignoreI18n
            //按照错误类型重新聚合
            stringMapMap.entrySet().stream().flatMap(o -> o.getValue().entrySet().stream()).collect(Collectors.groupingBy(o -> o.getValue().getErrorReason())).entrySet().stream().forEach(o -> {
                //按照日期聚合
                o.getValue().stream().collect(Collectors.groupingBy(o1 -> o1.getValue().getDate())).entrySet().stream().forEach(o2 -> {
                    sb.append(o2.getKey()).append(":");
                    sb.append(o2.getValue().stream().map(o1 ->newScheduleContent.getUserNameMap().getOrDefault(o1.getKey(), o1.getKey().toString())).collect(Collectors.joining(",")));
                });
                sb.append(UserScheduleContent.getErrorMessage(o.getKey())).append(";");
            });
            throw new ValidateException(sb.toString());
        }
        //upsert
        officeShiftService.upsertUserSchedule(controllerContext.getTenantId(), newScheduleContent, 10);
        return new Result(newScheduleContent);
    }

    @Override
    protected String getIdempotentKey(Arg arg) {
        return controllerContext.getTenantId() + "UserScheduleBatchAdd" + JSON.toJSONString(arg).hashCode();
    }

    @Data
    public static class Arg {
        /**
         * 用户排班信息
         */
        private List<UserScheduleContent.UserSchedule> userScheduleSimpleList;

        private int isOuter;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private UserScheduleContent userScheduleContent;

    }
}
