package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.crm.fmcg.wq.constants.OfficeShiftConstants;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;

import java.text.MessageFormat;
import java.util.List;
import java.util.Set;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-23 10:13
 **/
public class AccountShiftAddAction extends StandardAddAction {
    AccountShiftDao accountShiftDao = SpringUtil.getContext().getBean(AccountShiftDao.class);

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //参数错误
        if (arg.getObjectData().get(AccountShiftFields.CUSTOMER) == null) {
            throw new ValidateException("客户id必填"); //ignoreI18n
        }
        //客户班次详情从对象最大5个
        List<ObjectDataDocument> accountDetails = arg.getDetails().getOrDefault(AccountShiftDetailFields.API_NAME, Lists.newArrayList());
        if (accountDetails.size() > OfficeShiftConstants.MAX_ACCOUNT_SHIFT_DETAIL) {
            throw new ValidateException(MessageFormat.format("每个门店最多设置{0}个班次，请调整后重新提交", OfficeShiftConstants.MAX_ACCOUNT_SHIFT_DETAIL)); //ignoreI18n
        }
//名称不能重复
        Set<String> names = Sets.newHashSet();
        //数据编号重写
        for (int i = 0; i < accountDetails.size(); i++) {
            IObjectData iObjectData = accountDetails.get(i).toObjectData();
            iObjectData.set(AccountShiftDetailFields.SHIFT_NO, i + 1);
            //开始时间 结束时间判空
            if (iObjectData.get(AccountShiftDetailFields.START_TIME) == null) {
                throw new ValidateException("开始时间必填"); //ignoreI18n
            }
            if (iObjectData.get(AccountShiftDetailFields.END_TIME) == null) {
                throw new ValidateException("结束时间必填"); //ignoreI18n
            }
            if(!names.add(iObjectData.getName())){
                throw new ValidateException("班次名称不能重复"); //ignoreI18n
            }
            //时间是否跨24h
            Long startTime = iObjectData.get(AccountShiftDetailFields.START_TIME, Long.class);
            Long endTime = iObjectData.get(AccountShiftDetailFields.END_TIME, Long.class);
            if (startTime > endTime) {
                throw new ValidateException("开始时间不能大于结束时间"); //ignoreI18n
            }
            //重新赋值时长字段
            iObjectData.set(AccountShiftDetailFields.DURATION, endTime - startTime);
        }
        //主对象判断重复，不能有相同客户的数据
        if (CollectionUtils.isNotEmpty(accountShiftDao.getAccountShiftByAccountId(actionContext.getTenantId(), Sets.newHashSet(arg.getObjectData().get(AccountShiftFields.CUSTOMER).toString())))) {
            throw new ValidateException("客户班次已经存在"); //ignoreI18n
        }


    }

}
