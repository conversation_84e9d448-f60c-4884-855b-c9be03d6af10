package com.facishare.crm.fmcg.wq.action;

import cn.hutool.core.convert.Convert;
import com.facishare.appserver.checkinsoffice.api.service.CheckinsOfficeV2Service;
import com.facishare.crm.fmcg.wq.constants.AttendanceCorrectDetailFields;
import com.facishare.crm.fmcg.wq.constants.AttendanceCorrectFields;
import com.facishare.crm.fmcg.wq.servcie.AttendanceCorrecctService;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2023 - 04 - 06  16:28
 **/
@Slf4j
public class AttendanceCorrectFlowCompletedAction extends StandardFlowCompletedAction {

    AttendanceCorrecctService attendanceCorrecctService = SpringUtil.getContext().getBean(AttendanceCorrecctService.class);

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        String tenantId = actionContext.getTenantId();
        String ea = serviceFacade.getEAByEI(tenantId);
        Integer correctOwner = attendanceCorrecctService.getCorrerctOwner(data);
        //修正日期
        long correctDate = Convert.toDate(data.get(AttendanceCorrectFields.ATTENDANCE_DATE)).getTime();

        if (arg.isPass()){
            if (String.valueOf(ApprovalFlowTriggerType.CREATE.getTriggerTypeCode()).equals(arg.getTriggerType()) || String.valueOf(ApprovalFlowTriggerType.UPDATE.getTriggerTypeCode()).equals(arg.getTriggerType())) {
                attendanceCorrecctService.saveCorrectTime(ea, correctOwner, correctDate, getDataDetails().get(AttendanceCorrectDetailFields.API_NAME), CheckinsOfficeV2Service.CorrectOperateFlag.AGREE);
            } else if (String.valueOf(ApprovalFlowTriggerType.INVALID.getTriggerTypeCode()).equals(arg.getTriggerType())) {
                attendanceCorrecctService.saveCorrectTime(ea, correctOwner, correctDate, getDataDetails().get(AttendanceCorrectDetailFields.API_NAME), CheckinsOfficeV2Service.CorrectOperateFlag.DELETE);
                //直接删除掉 不进回收站
                serviceFacade.delete(data.getId(), objectDescribe.getApiName(), actionContext.getUser());
            }
        }else {
            attendanceCorrecctService.saveCorrectTime(ea, correctOwner, correctDate, getDataDetails().get(AttendanceCorrectDetailFields.API_NAME), CheckinsOfficeV2Service.CorrectOperateFlag.DELETE);
        }
        return after;
    }
}
