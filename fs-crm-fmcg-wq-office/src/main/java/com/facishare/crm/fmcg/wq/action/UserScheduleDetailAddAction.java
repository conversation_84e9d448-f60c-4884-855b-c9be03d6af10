package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-23 10:13
 **/
public class UserScheduleDetailAddAction extends StandardAddAction {
    @Override
    protected void before(Arg arg) {
        //不支持客户班次从对象直接编辑
        throw new ValidateException("不支持从对象直接操作"); //ignoreI18n
    }

}
