package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class UserScheduleDetailBulkInvalidAction extends StandardBulkInvalidAction {
    @Override
    protected void before(Arg arg) {
        //不支持客户班次从对象直接编辑
        throw new ValidateException("不支持从对象直接操作"); //ignoreI18n
    }
}
