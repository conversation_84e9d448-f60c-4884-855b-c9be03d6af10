package com.facishare.crm.fmcg.wq.controller;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.servcie.impl.OfficeShiftServiceImpl;
import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.IdempotentRequestContext;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/
public class UserScheduleCopyByUserController extends FmcgIdempotentPreDefineController<UserScheduleCopyByUserController.Arg, UserScheduleCopyByUserController.Result> {

    OfficeShiftService officeShiftService = SpringUtil.getContext().getBean(OfficeShiftServiceImpl.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }


    @Override
    protected Result doServiceIdempotent(Arg arg) {
        Result result = new Result();
        //如果 sourceEndDate sourceStartDate间隔天数 比 endDate startDate间隔天数小则报错
        //计算间隔天数
        LocalDate sourceStartDate = LocalDate.parse(arg.getSourceStartDate());
        LocalDate sourceEndDate = LocalDate.parse(arg.getSourceEndDate());
        LocalDate startDate = LocalDate.parse(arg.getTargetStartDate());
        LocalDate endDate = LocalDate.parse(arg.getTargetEndDate());
        if (startDate.isAfter(endDate)) {
            throw new ValidateException("开始时间大于结束时间"); //ignoreI18n
        }
        if (sourceStartDate.isAfter(sourceEndDate)) {
            throw new ValidateException("开始时间大于结束时间"); //ignoreI18n
        }
        //源日期超过最大间隔31天
        if (ChronoUnit.DAYS.between(sourceStartDate, sourceEndDate) > 31) {
            throw new ValidateException("超过最大间隔31天"); //ignoreI18n
        }
//        if (ChronoUnit.DAYS.between(startDate, endDate) > ChronoUnit.DAYS.between(sourceStartDate, sourceEndDate)) {
//            throw new ValidateException("目标日期间隔天数大于源日期间隔天数");
//        }
        //日期有交集包含相等 报错
        if (!(sourceStartDate.isAfter(endDate) || startDate.isAfter(sourceEndDate))) {
            throw new ValidateException("日期有交集包含相等"); //ignoreI18n
        }
//        //startDate 必须大于等于今天
        LocalDate today = LocalDate.now();
        if (endDate.isBefore(today)) {
            throw new ValidateException("目标结束日期必须大于等于今天"); //ignoreI18n
        }

        //调用批量拉取排班表 controller
        UserScheduleContent userScheduleContent = officeShiftService.batchGetUserScheduleIgnoreOther(controllerContext.getTenantId(),arg.getAccountIds(), arg.getUserIds(), arg.getSourceStartDate(), arg.getSourceEndDate());
        UserScheduleContent dbScheduleContent = officeShiftService.batchGetUserScheduleIgnoreOther(controllerContext.getTenantId(),arg.getAccountIds(), arg.getUserIds(), startDate.isAfter(today) ? startDate.toString() : today.toString(), arg.getTargetEndDate());

        // 目标日期
        //sourceDateUserScheduleMap
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> sourceDateUserScheduleMap = Optional.ofNullable(userScheduleContent.getDateUserScheduleMap()).orElse(Maps.newConcurrentMap());
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> dbDateUserScheduleMap = Optional.ofNullable(dbScheduleContent.getDateUserScheduleMap()).orElse(Maps.newConcurrentMap());
        //targetDateUserScheduleMap
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> targetDateUserScheduleMap = Maps.newConcurrentMap();
        userScheduleContent.setDateUserScheduleMap(targetDateUserScheduleMap);
        LocalDate tempSourceDate = LocalDate.parse(arg.getSourceStartDate());
        LocalDate tempTargetDate = LocalDate.parse(arg.getTargetStartDate());
        while (!tempSourceDate.isAfter(sourceEndDate) || !tempTargetDate.isAfter(endDate)) {
            //执行替换的逻辑
            String tempSourceDateStr = tempSourceDate.toString();
            String tempTargetDateStr = tempTargetDate.toString();
            Map<Integer, UserScheduleContent.UserSchedule> targetUserScheduleMap = Maps.newConcurrentMap();
            //目标数据 需要保存的数据
            Map<Integer, UserScheduleContent.UserSchedule> dbUserScheduleMap = dbDateUserScheduleMap.getOrDefault(tempTargetDateStr, MapUtils.EMPTY_MAP);
            Map<Integer, UserScheduleContent.UserSchedule> sourceUserScheduleMap = sourceDateUserScheduleMap.getOrDefault(tempSourceDateStr, MapUtils.EMPTY_MAP);
            if (!tempTargetDate.isBefore(today)) {
                //人员循环
                for (Integer userId : arg.getUserIds()) {
                    if (Objects.isNull(dbUserScheduleMap.get(userId)) || CollectionUtils.isEmpty(dbUserScheduleMap.get(userId).getAccountShiftDetailIdList())) {
                        //db 无值
                        if (Objects.nonNull(sourceUserScheduleMap.get(userId)) && CollectionUtils.isNotEmpty(sourceUserScheduleMap.get(userId).getAccountShiftDetailIdList())) {
                            //source 有值
                            UserScheduleContent.UserSchedule userSchedule = new UserScheduleContent.UserSchedule();
                            userSchedule.setDate(tempTargetDateStr);
                            userSchedule.setUserId(userId);
                            userSchedule.setAccountShiftDetailIdList(sourceUserScheduleMap.get(userId).getAccountShiftDetailIdList());
                            targetUserScheduleMap.put(userId, userSchedule);
                        }
                    } else {
                        //db 有值
                        if (arg.getIsCover() == 1 ) {
                            //完全覆盖 且 source有值
                            UserScheduleContent.UserSchedule userSchedule = new UserScheduleContent.UserSchedule();
                            userSchedule.setDate(tempTargetDateStr);
                            userSchedule.setUserId(userId);
                            if (Objects.nonNull(sourceUserScheduleMap.get(userId)) && CollectionUtils.isNotEmpty(sourceUserScheduleMap.get(userId).getAccountShiftDetailIdList())){
                                userSchedule.setAccountShiftDetailIdList(sourceUserScheduleMap.get(userId).getAccountShiftDetailIdList());
                            }else{
                                userSchedule.setAccountShiftDetailIdList(Lists.newArrayList());
                            }
                            targetUserScheduleMap.put(userId, userSchedule);
                        } else {
                            //为了前端渲染
                            targetUserScheduleMap.put(userId, dbUserScheduleMap.get(userId));
                        }
                    }
                }
            }
            targetDateUserScheduleMap.put(tempTargetDateStr, targetUserScheduleMap);
            tempSourceDate = tempSourceDate.plusDays(1);
            tempTargetDate = tempTargetDate.plusDays(1); // 递增新的日期
        }
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> stringMapMap = officeShiftService.checkUserScheduleConflictAndFillData(controllerContext.getTenantId(), userScheduleContent, false,arg.getIsOuter() == 1);
        if (!stringMapMap.isEmpty()) {
            //本次变更会导致 xxx日期，xxx,xxx用户排班冲突
            StringBuilder sb = new StringBuilder();
            sb.append("操作失败 : "); //ignoreI18n
            int beforeLength = sb.length();
            //按照错误类型重新聚合
            stringMapMap.entrySet().stream().flatMap(o -> o.getValue().entrySet().stream()).collect(Collectors.groupingBy(o -> o.getValue().getErrorReason())).entrySet().stream().forEach(o -> {
                //过滤掉日期异常
                if (o.getKey() == UserScheduleContent.SCHEDULE_DATE_ERROR) {
                    return;
                }
                //按照日期聚合
                o.getValue().stream().collect(Collectors.groupingBy(o1 -> o1.getValue().getDate())).entrySet().stream().forEach(o2 -> {
                    sb.append(o2.getKey()).append(":");
                    sb.append(o2.getValue().stream().map(o1 -> userScheduleContent.getUserNameMap().getOrDefault(o1.getKey(), o1.getKey().toString())).collect(Collectors.joining(",")));
                });
                sb.append(UserScheduleContent.getErrorMessage(o.getKey())).append(";");
            });
            if (sb.length() != beforeLength){
                throw new ValidateException(sb.toString());
            }
        }
        //upsert
        officeShiftService.upsertUserSchedule(controllerContext.getTenantId(), userScheduleContent, 10);
        if (CollectionUtils.isNotEmpty(userScheduleContent.getExcelUserIds()) && CollectionUtils.isNotEmpty(arg.getUserIds())) {
            userScheduleContent.getExcelUserIds().removeIf(o->!arg.getUserIds().contains(o));
        }
        return new Result(userScheduleContent);
    }

    @Override
    protected String getIdempotentKey(Arg arg) {
        return controllerContext.getTenantId() + controllerContext.getMethodName() + JSON.toJSONString(arg).hashCode();
    }

    @Data
    public static class Arg {
        /**
         * userIds
         */
        private List<Integer> userIds;
        /**
         * accountIds
         */
        private List<String> accountIds;
        /**
         * 目标开始日期
         */
        private String targetStartDate;
        /**
         * 目标结束日期
         */
        private String targetEndDate;
        /**
         * 来源开始日期
         */
        private String sourceStartDate;
        /**
         * 来源结束日期
         */
        private String sourceEndDate;
        /**
         * 是否完全覆盖 1 完全覆盖 0.只覆盖空排班
         */
        private int isCover;

        private int isOuter;
        /**
         * 是否是客户版
         */
        private int isAccountVersion;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Result {
        private UserScheduleContent userScheduleContent;

    }
}
