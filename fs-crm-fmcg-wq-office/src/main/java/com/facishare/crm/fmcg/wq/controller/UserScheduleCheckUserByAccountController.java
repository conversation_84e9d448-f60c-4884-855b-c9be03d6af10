package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.constants.AccountObjConstants;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.servcie.impl.OfficeShiftServiceImpl;
import com.facishare.crm.fmcg.wq.util.SearchQuery;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 客户视图排班表列表查询接口
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/
public class UserScheduleCheckUserByAccountController extends PreDefineController<UserScheduleCheckUserByAccountController.Arg, UserScheduleCheckUserByAccountController.Result> {

    OfficeShiftService officeShiftService =  SpringUtil.getContext().getBean(OfficeShiftServiceImpl.class);
    AccountShiftDao accountShiftDao = SpringUtil.getContext().getBean(AccountShiftDao.class);
    EmployeeDao employeeDao = SpringUtil.getContext().getBean(EmployeeDao.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected Result doService(Arg arg) {
        //验证人是否有客户的权限
        String tenantId = controllerContext.getTenantId();
        //并发按照人员权限去查客户数据 limit 2
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        SearchQuery searchQuery = SearchQuery.builder().eq("_id",arg.getAccountId()).build();
        Map<Integer, Boolean> userAuthMap = Maps.newConcurrentMap();
        arg.getUserIds().forEach(userId -> parallelTask.submit(() -> {
            try {
                //获取权限的客户
                User build = User.builder().tenantId(tenantId).userId(String.valueOf(userId)).build();
                build.setIsCrmAdmin(Optional.of(false));
                List<IObjectData> authAccountList = accountShiftDao.getDataWithAuth(build, AccountObjConstants.API_NAME, searchQuery);
                if (CollectionUtils.isEmpty(authAccountList)) {
                    userAuthMap.put(userId, false);
                }
            } catch (Exception e) {
                log.error("get authData error,tenantId:{},userId:{}", tenantId, userId, e);
                throw e;
            }
        }));
        try {
            //最大600s
            boolean success = parallelTask.await(10L, TimeUnit.SECONDS);
            if (!success) {
                log.error("getAuthData failed! ei:{}", tenantId);
                throw new ValidateException("查询人员权限数据错误"); //ignoreI18n
            }
        } catch (TimeoutException e) {
            log.error("getAuthData time out! ei:{}", tenantId, e);
            throw new ValidateException("查询人员权限数据超时,请重试！"); //ignoreI18n
        }
        if (MapUtils.isNotEmpty(userAuthMap)) {
            Map<Integer, String> userNameByIds = employeeDao.getUserNameByIds(tenantId, userAuthMap.keySet());
            throw new ValidateException("用户" + userAuthMap.entrySet().stream().filter(o -> !o.getValue()) //ignoreI18n
                    .map(p -> userNameByIds.get(p.getKey())).collect(Collectors.joining(","))
                    + "没有权限"); //ignoreI18n
        }
        return new Result();
    }

    @Data
    public static class Arg {
        /**
         * userIds
         */
        private List<Integer> userIds;
        /**
         * 客户id
         */
        private String accountId;
//        /**
//         * 门店班次详情id
//         */
//        private String accountShiftDetailId;
//        /**
//         * 日期
//         */
//        private String dateStr;
//        /**
//         * 已经编辑的数据
//         */
//        private List<UserScheduleContent.ShiftSchedule> shiftScheduleList;

    }
    @Data
    public static class Result {
        private UserScheduleContent userScheduleContent;

    }
}
