package com.facishare.crm.fmcg.wq.action;

import cn.hutool.core.convert.Convert;
import com.facishare.appserver.checkinsoffice.api.service.CheckinsOfficeV2Service;
import com.facishare.crm.fmcg.wq.constants.AttendanceCorrectDetailFields;
import com.facishare.crm.fmcg.wq.constants.AttendanceCorrectFields;
import com.facishare.crm.fmcg.wq.servcie.AttendanceCorrecctService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-08-31 16:22
 **/
public class AttendanceCorrectEditAction extends StandardEditAction {
    AttendanceCorrecctService attendanceCorrecctService = SpringUtil.getContext().getBean(AttendanceCorrecctService.class);
    //修正审批申请人
    Integer correctOwner;

    @Override
    protected void before(Arg arg) {
        throw new ValidateException("不支持编辑操作"); //ignoreI18n

//        super.before(arg);
//        //check 参数
//        List<ObjectDataDocument> attendanceCorrectDetailObj = arg.getDetails().get("AttendanceCorrectDetailObj");
//        if (CollectionUtils.isEmpty(attendanceCorrectDetailObj)) {
//            throw new RuntimeException("修正审批明细为空");
//        }
//        String eId = actionContext.getTenantId();
//        String ea = serviceFacade.getEAByEI(eId);
//        //从 ObjectData 中获取 owner
//        IObjectData objectData = arg.getObjectData().toObjectData();
//        correctOwner = attendanceCorrecctService.getCorrerctOwner(objectData);
//        //修正日期
//        long correctDate = Convert.toDate(objectData.get(AttendanceCorrectFields.ATTENDANCE_DATE)).getTime();
//        //转成IObjectData
//        List<IObjectData> iObjectDataList = attendanceCorrectDetailObj.stream().map(o -> o.toObjectData()).collect(Collectors.toList());
//        attendanceCorrecctService.checkCorrectTime(ea, correctOwner, correctDate, iObjectDataList);
    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        String id = result.getObjectData().getId();
        if (!startApprovalFlowAsynchronous) {
            String tenantId = actionContext.getTenantId();
            String ea = serviceFacade.getEAByEI(tenantId);
            List<ObjectDataDocument> attendanceCorrectDetailObj = arg.getDetails().get(AttendanceCorrectDetailFields.API_NAME);
            List<IObjectData> iObjectDataList = attendanceCorrectDetailObj.stream().map(o -> o.toObjectData()).collect(Collectors.toList());
            //修正日期
            long correctDate = Convert.toDate(objectData.get(AttendanceCorrectFields.ATTENDANCE_DATE)).getTime();
            attendanceCorrecctService.saveCorrectTime(ea, correctOwner, correctDate, iObjectDataList, isApprovalFlowStartSuccess(id) ? CheckinsOfficeV2Service.CorrectOperateFlag.PENDING : CheckinsOfficeV2Service.CorrectOperateFlag.AGREE);
        }
        return after;
    }
}
