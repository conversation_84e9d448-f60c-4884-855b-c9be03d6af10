package com.facishare.crm.fmcg.wq.action;

import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Sets;
import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.crm.fmcg.wq.constants.AccountShiftFields;
import com.facishare.crm.fmcg.wq.constants.OfficeShiftConstants;
import com.facishare.crm.fmcg.wq.dao.UserScheduleDao;
import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.Pair;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-23 10:13
 **/
public class AccountShiftEditAction extends StandardEditAction {
    OfficeShiftService officeShiftService = SpringUtil.getContext().getBean(OfficeShiftService.class);
    UserScheduleDao userScheduleDao = SpringUtil.getContext().getBean(UserScheduleDao.class);
    //班次时间有变动的班次
    List<IObjectData> updateTimeAccountShiftDetails;

    @Override
    protected void before(Arg arg) {
        //客户班次详情从对象最大5个
        List<ObjectDataDocument> argDetails = arg.getDetails().getOrDefault(AccountShiftDetailFields.API_NAME, Lists.newArrayList());
        if (argDetails.size() > OfficeShiftConstants.MAX_ACCOUNT_SHIFT_DETAIL) {
            throw new ValidateException(MessageFormat.format("每个门店最多设置{0}个班次，请调整后重新提交", OfficeShiftConstants.MAX_ACCOUNT_SHIFT_DETAIL)); //ignoreI18n
        }
        //名称不能重复
        Set<String> names = Sets.newHashSet();
        //数据编号重写
        for (int i = 0; i < argDetails.size(); i++) {
            IObjectData iObjectData = argDetails.get(i).toObjectData();
            iObjectData.set(AccountShiftDetailFields.SHIFT_NO, i + 1);
            //开始时间 结束时间判空
            if (iObjectData.get(AccountShiftDetailFields.START_TIME) == null) {
                throw new ValidateException("开始时间必填"); //ignoreI18n
            }
            if(!names.add(iObjectData.getName())){
                throw new ValidateException("班次名称不能重复"); //ignoreI18n
            }
            if (iObjectData.get(AccountShiftDetailFields.END_TIME) == null) {
                throw new ValidateException("结束时间必填"); //ignoreI18n
            }
            //时间是否跨24h
            Long startTime = iObjectData.get(AccountShiftDetailFields.START_TIME, Long.class);
            Long endTime = iObjectData.get(AccountShiftDetailFields.END_TIME, Long.class);
            if (startTime > endTime) {
                throw new ValidateException("开始时间不能大于结束时间"); //ignoreI18n
            }
            //重新赋值时长字段
            iObjectData.set(AccountShiftDetailFields.DURATION, endTime - startTime);
        }
        super.before(arg);
        //不支持编辑客户
        if(updatedFieldMap.containsKey(AccountShiftFields.CUSTOMER)){
            throw new ValidateException("不支持编辑客户"); //ignoreI18n
        }
        //判断已经排班的时候冲突
        if (CollectionUtils.isNotEmpty(detailsToUpdate)) {
            List<IObjectData> accountShiftDetails = detailsToUpdate.stream().filter(detail -> AccountShiftDetailFields.API_NAME.equals(detail.getDescribeApiName())).collect(Collectors.toList());
            List<IObjectData> dbAccountShiftDetails = dbDetailDataMap.get(AccountShiftDetailFields.API_NAME);
            //判断accountShiftDetails 和dbAccountShiftDetails id相同的 AccountShiftDetailFields.END_TIME AccountShiftDetailFields.START_TIME是否有变化
            if (CollectionUtils.isNotEmpty(accountShiftDetails) && CollectionUtils.isNotEmpty(dbAccountShiftDetails)) {
                Map<String, IObjectData> dbAccountShiftDetailMap = dbAccountShiftDetails.stream().collect(Collectors.toMap(o -> o.getId(), o -> o));
                //班次时间有变动的数据
                updateTimeAccountShiftDetails = Lists.newArrayList();
                for (IObjectData accountShiftDetail : accountShiftDetails) {
                    IObjectData dbAccountShiftDetail = dbAccountShiftDetailMap.get(accountShiftDetail.getId());
                    if (dbAccountShiftDetail != null) {
                        Long dbStartTime = dbAccountShiftDetail.get(AccountShiftDetailFields.START_TIME, Long.class);
                        Long dbEndTime = dbAccountShiftDetail.get(AccountShiftDetailFields.END_TIME, Long.class);
                        Long startTime = accountShiftDetail.get(AccountShiftDetailFields.START_TIME, Long.class);
                        Long endTime = accountShiftDetail.get(AccountShiftDetailFields.END_TIME, Long.class);
                        if (dbStartTime != null && dbEndTime != null && startTime != null && endTime != null) {
                            if (dbStartTime.equals(startTime) && dbEndTime.equals(endTime)) {
                                continue;
                            }
                        }
                        updateTimeAccountShiftDetails.add(accountShiftDetail);
                    }
                }
                //根据时间变动的数据查询用户排班表，用户排班详情表
                if (CollectionUtils.isNotEmpty(updateTimeAccountShiftDetails)) {
                    Pair<Map<String, Map<Integer, UserScheduleContent.UserSchedule>>,UserScheduleContent> pair = officeShiftService.checkUserScheduleConflictByUpdadteShiftDetail(actionContext.getUser(), updateTimeAccountShiftDetails);
                    if (pair != null && pair.first != null && !pair.first.isEmpty()) {
                        //本次变更会导致 xxx日期，xxx,xxx用户排班冲突
                        StringBuilder sb = new StringBuilder();
                        sb.append("本次变更会导致 : "); //ignoreI18n
                        //按照错误类型重新聚合
                        pair.first.entrySet().stream().flatMap(o -> o.getValue().entrySet().stream()).collect(Collectors.groupingBy(o -> o.getValue().getErrorReason())).entrySet().stream().forEach(o -> {
                            //按照日期聚合
                            o.getValue().stream().collect(Collectors.groupingBy(o1 -> o1.getValue().getDate())).entrySet().stream().forEach(o2 -> {
                                sb.append(o2.getKey()).append(":");
                                sb.append(o2.getValue().stream().map(o1 -> pair.second.getUserNameMap().getOrDefault(o1.getKey(), o1.getKey().toString())).collect(Collectors.joining(",")));
                            });
//                            if (o.getKey() == UserScheduleContent.SCHEDULE_OVER_24H) {
//                                sb.append("超过24h班次;");
//                            } else if (o.getKey() == UserScheduleContent.SCHEDULE_TIME_CONFLICT) {
//                                sb.append("班次时间冲突;");
//                            } else if (o.getKey() == UserScheduleContent.SCHEDULE_TIME_INSUFFICIENT) {
//                                sb.append("规则排班时长不足;");
//                            }
                            sb.append(UserScheduleContent.getErrorMessage(o.getKey())).append(";");
                        });
                        throw new ValidateException(sb.toString());
                    }
                }
            }
        }


    }

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        if (CollectionUtils.isNotEmpty(detailsToDelete)) {
            List<IObjectData> delAccountShiftDetails = detailsToDelete.stream().filter(detail -> AccountShiftDetailFields.API_NAME.equals(detail.getDescribeApiName())).collect(Collectors.toList());
            officeShiftService.syncUserSchedulesByAccountDetailIds(actionContext.getUser(), delAccountShiftDetails.stream().filter(o-> StringUtils.isNotBlank(o.getId())).map(o -> o.getId()).distinct().collect(Collectors.toList()), 1);
        }
        if (CollectionUtils.isNotEmpty(updateTimeAccountShiftDetails)) {
            officeShiftService.syncUserSchedulesByAccountDetailIds(actionContext.getUser(), updateTimeAccountShiftDetails.stream().map(o -> o.getId()).distinct().collect(Collectors.toList()), 0);
        }

        return after;
    }
}
