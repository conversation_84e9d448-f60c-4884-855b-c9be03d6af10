package com.facishare.crm.fmcg.wq.controller;

import com.facishare.appserver.checkinsoffice.api.model.AccountRangeOnRule;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.appserver.utils.EnvUtils;
import com.facishare.crm.fmcg.wq.constants.*;
import com.facishare.crm.fmcg.wq.dao.AccountShiftDao;
import com.facishare.crm.fmcg.wq.dao.EmployeeDao;
import com.facishare.crm.fmcg.wq.dao.UserScheduleDao;
import com.facishare.crm.fmcg.wq.excel.EasyExcelHandler;
import com.facishare.crm.fmcg.wq.model.excel.OfficeUserExportData;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.util.RedisUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.task.AsyncTaskExecutor;

import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/
public class UserScheduleGetImportTemplateByAccountController extends PreDefineController<UserScheduleGetImportTemplateByAccountController.Arg, UserScheduleGetImportTemplateByAccountController.Result> {

    AsyncTaskExecutor fmcgThreadPoolExecutor =  SpringUtil.getContext().getBean("fmcgThreadPoolExecutor",AsyncTaskExecutor.class);
    private final EasyExcelHandler easyExcelHandler = SpringUtil.getContext().getBean(EasyExcelHandler.class);
    private final EmployeeDao employeeDao = SpringUtil.getContext().getBean(EmployeeDao.class);
    private final UserScheduleDao userScheduleDao =  SpringUtil.getContext().getBean(UserScheduleDao.class);
    private final AccountShiftDao accountShiftDao =  SpringUtil.getContext().getBean(AccountShiftDao.class);
    private final RedisUtils redisUtils = SpringUtil.getContext().getBean(RedisUtils.class);
    private final OfficeShiftService officeShiftService = SpringUtil.getContext().getBean(OfficeShiftService.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected Result doService(Arg arg) {
        Result result = new Result();
        int userId = controllerContext.getUser().getUserIdInt();
        String tenantId = controllerContext.getTenantId();
        String ea = serviceFacade.getEAByEI(tenantId);
        AccountRangeOnRule.Result accountRange = null;
        try{
            // 校验是否
            checkRepeatExport(tenantId,arg.getRuleId());
            setExportRequestCache(tenantId,arg.getRuleId());
            String importRedisKey = redisUtils.getKeyForScheduleImpOrExp(RedisUtils.IMPORT_BY_ACCOUNT_REDIS_KEY,tenantId,arg.getRuleId());
            if (redisUtils.checkKeyIsExist(importRedisKey)) {
                throw new ValidateException("正在执行导入操作,请稍等"); //ignoreI18n
            }
            accountRange = officeShiftService.getAccountRange(tenantId,arg.getRuleId());
            officeShiftService.verifyAccountRange(tenantId,accountRange);
        }catch (ValidateException e){
            clearExportRequest(tenantId,arg.getRuleId());
            throw e;
        }catch (Exception e){
            clearExportRequest(tenantId,arg.getRuleId());
            log.error("getImportByAccount verify is error ei:{}",tenantId,e);
            throw new ValidateException("系统异常,请联系纷享客服"); //ignoreI18n
        }
        AccountRangeOnRule.Result finalAccountRange = accountRange;
        fmcgThreadPoolExecutor.execute(()->{
            try {
                Map<String,IObjectData> accDataMap = officeShiftService.getAccDataByRuleRange(tenantId,finalAccountRange);
                List<String> accountIds = Lists.newArrayList(accDataMap.keySet());
                // 查询客户排班对象
                Map<String, String> accIdAndShiftIdMap = Maps.newHashMap();
                Map<String, List<IObjectData>> shiftIdAndDetailsMap = Maps.newHashMap();
                for (List<String> subAccountIds : Lists.partition(accountIds, 100)) {
                    List<IObjectData> subAccountShiftList = accountShiftDao.getAccountShiftByAccountId(tenantId, Sets.newHashSet(subAccountIds));
                    Set<String> subShiftIds = Sets.newHashSet();
                    for (IObjectData iObjectData : subAccountShiftList) {
                        subShiftIds.add(iObjectData.getId());
                        accIdAndShiftIdMap.put(iObjectData.get(AccountShiftFields.CUSTOMER).toString(), iObjectData.getId());
                    }
                    shiftIdAndDetailsMap.putAll(accountShiftDao.getAccountShiftDetailByMainIds(tenantId, subShiftIds).stream().collect(Collectors.groupingBy(o -> o.get(AccountShiftDetailFields.ACCOUNT_SHIFT).toString())));
                }
                Map<Integer, String> userIdAndNameMap = Maps.newHashMap();
                // Map<排班ID,<日期,List<员工ID>>
                Map<String, Map<Integer, List<String>>> shiftDetialIdAndUserIdDateMap = getShiftDetailAndUserIdDateStr(tenantId, arg.getStartDate(), arg.getEndDate(), userIdAndNameMap);
                List<OfficeUserExportData> dataList = Lists.newArrayList();
                for (Map.Entry<String, String> stringStringEntry : accIdAndShiftIdMap.entrySet()) {
                    String accountId = stringStringEntry.getKey();
                    if (Objects.isNull(accDataMap.get(accountId))) {
                        log.info("getImportByAccount accDataMap is null ei:{},dataId:{}", tenantId, accountId);
                        continue;
                    }
                    String accountName = accDataMap.get(accountId).getName();
                    // 没有查询到客户信息
                    if (StringUtils.isBlank(accountName)) {
                        log.info("getImportByAccount accName is null ei:{},dataId:{}", tenantId, accountId);
                        continue;
                    }
                    String shiftId = stringStringEntry.getValue();
                    List<IObjectData> shiftDetails = shiftIdAndDetailsMap.get(shiftId);
                    // 门店没有设置排班
                    if (CollectionUtils.isEmpty(shiftDetails)) {
                        log.info("getImportByAccount shiftId:{} is not details", shiftId);
                        continue;
                    }
                    shiftDetails.forEach(detail -> {
                        String shiftDetailId = detail.getId();
                        if (MapUtils.isNotEmpty(shiftDetialIdAndUserIdDateMap.get(shiftDetailId))) {
                            Map<Integer, List<String>> dateStrAndUserIdsMap = shiftDetialIdAndUserIdDateMap.get(shiftDetailId);
                            dateStrAndUserIdsMap.forEach((k, v) -> {
                                List<String> userNames = Lists.newArrayList();
                                OfficeUserExportData officeUserExportData = getDefaultOfficeUserData(accDataMap.get(accountId), detail.get(AccountShiftDetailFields.DISPLAY_NAME).toString());
                                officeUserExportData.setUserNames(userNames);
                                dataList.add(officeUserExportData);
                                String userName = userIdAndNameMap.get(k);
                                DateUtils.getDateStream(LocalDate.parse(arg.getStartDate()), LocalDate.parse(arg.getEndDate())).forEach(o -> {
                                    userNames.add(v.contains(o.toString()) ? userName : null);
                                });
                            });
                        } else {
                            dataList.add(getDefaultOfficeUserData(accDataMap.get(accountId), detail.get(AccountShiftDetailFields.DISPLAY_NAME).toString()));
                        }
                    });

                }
                boolean isMn = officeShiftService.isMnPmmSchedule(ea);
                List<List<String>> headList = getHeadList(isMn,arg.getStartDate(), arg.getEndDate());
                int headEndIndex = isMn ? 3 : 2;
                if(userIdAndNameMap.size() > 50) {
                    easyExcelHandler.pushFile(ea, userId, convertDataList(isMn,dataList, headList.size() - headEndIndex), headList, "「按门店排班」导入模板"); //ignoreI18n
                }else{
                    easyExcelHandler.pushFile(ea, userId, convertDataList(isMn,dataList, headList.size() - headEndIndex), headList, "「按门店排班」导入模板", //ignoreI18n
                            new ArrayList<>(userIdAndNameMap.values()), 2,dataList.size() + 1,headEndIndex,headList.size() - 1);
                }
            }catch (Exception e){
                log.error("deal office export template by account is error",e);
            }finally {
                clearExportRequest(tenantId,arg.getRuleId());
            }
        });

        return result;
    }

    private List<List<String>> convertDataList(boolean isMn,List<OfficeUserExportData> dataList,int headSize){
        List<List<String>> exportDataList = Lists.newArrayList();
        for (OfficeUserExportData officeUserExportData : dataList) {
            List<String> objList = Lists.newArrayList();
            objList.add(officeUserExportData.getAccountName());
            if(isMn){
                objList.add(officeUserExportData.getAccountNo());
            }
            objList.add(officeUserExportData.getClasses());
            for (int i = 0; i < headSize; i++) {
                String userName = CollectionUtils.isNotEmpty(officeUserExportData.getUserNames()) && officeUserExportData.getUserNames().size() > i ? officeUserExportData.getUserNames().get(i) : null;
                objList.add(userName);
            }
            exportDataList.add(objList);
        }
        return exportDataList;
    }

    private OfficeUserExportData getDefaultOfficeUserData(IObjectData accData,String classes){
        OfficeUserExportData officeUserExportData = new OfficeUserExportData();
        officeUserExportData.setAccountName(accData.getName());
        officeUserExportData.setAccountNo((String)accData.get("account_no__c"));
        officeUserExportData.setClasses(classes);
        officeUserExportData.setUserNames(null);
        return officeUserExportData;
    }

    private void checkRepeatExport(String tenantId,String ruleId){
        if(redisUtils.checkKeyIsExist(getExportRedisKey(tenantId,ruleId))){
            throw new ValidateException("正在获取导入模版,请稍后在企信查看"); //ignoreI18n
        }
    }

    private String getExportRedisKey(String tenantId,String ruleId){
        return RedisUtils.EXPORT_TEMPLATE_REDIS_KEY + tenantId + "_" + ruleId;
    }

    private void clearExportRequest(String tenantId,String ruleId){
        redisUtils.clearByKey(getExportRedisKey(tenantId, ruleId));
    }

    private void setExportRequestCache(String tenantId,String ruleId){
        redisUtils.setCacheTime(getExportRedisKey(tenantId,ruleId),"1",30);
    }

    private Map<String,Map<Integer,List<String>>> getShiftDetailAndUserIdDateStr(String tenantId,String startDate,String endDate,Map<Integer,String> userIdAndNameMap){
        Map<String,Map<Integer,List<String>>> shiftDetialIdAndUserIdDateMap = Maps.newHashMap();
        for (List<Integer> subUserIds : Lists.partition(arg.getUserIds(), 100)) {
            // 主对象和日期
            List<IObjectData> userSheculeList = userScheduleDao.getMainListByDateAndUserIds(tenantId, startDate, endDate, Sets.newHashSet(subUserIds));
            if(userSheculeList.isEmpty()){
                continue;
            }
            Map<String,IObjectData> mainIdAndDataMap = userSheculeList.stream().collect(Collectors.toMap(DBRecord::getId, k2->k2,(k1, k2)->k1));
            List<String> userSheculeIdList = userSheculeList.stream().map(DBRecord::getId).collect(Collectors.toList());
            List<IObjectData> userScheduleDetailByMainIds = userScheduleDao.getUserScheduleDetailByMainIds(tenantId, userSheculeIdList);
            for (IObjectData detail : userScheduleDetailByMainIds) {
                if(Objects.isNull(detail.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL))){
                    log.info("userScheduleDetail relevance shiftDetail is null ei:{},id:{}",tenantId,detail.getId());
                    continue;
                }
                String shiftDetailId = detail.get(UserScheduleDetailFields.ACCOUNT_SHIFT_DETAIL).toString();
                String mainId = detail.get(UserScheduleDetailFields.USER_SCHEDULE).toString();
                if(Objects.isNull(mainIdAndDataMap.get(mainId).get(UserScheduleFields.USER_ID))){
                    log.info("userScheduleDetail userId is null ei:{},id:{}",tenantId,detail.getId());
                    continue;
                }
                if(Objects.isNull(mainIdAndDataMap.get(mainId).get(UserScheduleFields.DATE))){
                    log.info("userScheduleDetail date is null ei:{},id:{}",tenantId,detail.getId());
                    continue;
                }
                String dateStr = DateUtils.getStringFromTime(Long.parseLong(mainIdAndDataMap.get(mainId).get(UserScheduleFields.DATE).toString()),DateUtils.DateFormat);
                Integer scheduleUserId = Integer.parseInt(mainIdAndDataMap.get(mainId).get(UserScheduleFields.USER_ID).toString());
                shiftDetialIdAndUserIdDateMap.computeIfAbsent(shiftDetailId,k->Maps.newHashMap()).computeIfAbsent(scheduleUserId,k->Lists.newArrayList()).add(dateStr);
            }
        }
        userIdAndNameMap.putAll(Optional.ofNullable(employeeDao.getUserNameByIds(tenantId,Sets.newHashSet(arg.getUserIds()))).orElse(Maps.newHashMap()));
        return shiftDetialIdAndUserIdDateMap;
    }

    private List<List<String>> getHeadList(boolean isMn,String startDate,String endDate){
        List<List<String>> headList = Lists.newArrayList();
        headList.add(Lists.newArrayList("门店")); //ignoreI18n
        if(isMn) {
            headList.add(Lists.newArrayList("客户编码")); //ignoreI18n
        }
        headList.add(Lists.newArrayList("班次")); //ignoreI18n
        DateUtils.getDateStream(LocalDate.parse(startDate), LocalDate.parse(endDate)).forEach(o->{
            String dateStr = buildDateStr(o.getYear(),o.getMonthValue(),o.getDayOfMonth());
            headList.add(Lists.newArrayList(dateStr,o.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.CHINA)));
        });
        return headList;
    }

    private String buildDateStr(int year,int month,int day){
        return year + "年" + month + "月" + day + "日"; //ignoreI18n
    }


    @Data
    public static class Arg {
        private String ruleId;

        private List<Integer> userIds;

        private String startDate;

        private String endDate;

        private int isOuter;

    }
    public static class Result {

    }

    @Data
    public static class UserScheduleInfo{
        public String dateStr;
        public int userId;
    }

}
