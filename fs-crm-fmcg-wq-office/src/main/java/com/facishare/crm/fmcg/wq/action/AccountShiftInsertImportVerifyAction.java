package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportVerifyAction;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 客户班次新建导入 同步校验 表格表头验证
 * 导入校验过程的接口，做是否可以生成导入任务的判断处理
 *
 * 扩展点：
 *
 * 1 com.facishare.paas.appframework.core.predef.action.StandardInsertImportVerifyAction#customVerify（）在通用的处理逻辑后，特殊业务处理
 * @author: zhangsm
 * @create: 2024-02-20 14:37
 **/
public class AccountShiftInsertImportVerifyAction extends StandardInsertImportVerifyAction {


}
