package com.facishare.crm.fmcg.wq.servcie;

import cn.hutool.core.convert.Convert;
import com.beust.jcommander.internal.Lists;
import com.facishare.appserver.checkinsoffice.api.model.CheckCorrectTimeResult;
import com.facishare.appserver.checkinsoffice.api.model.CorrectTime;
import com.facishare.appserver.checkinsoffice.api.service.CheckinsOfficeV2Service;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.constants.AttendanceCorrectDetailFields;
import com.facishare.crm.fmcg.wq.constants.BaseField;
import com.facishare.crm.fmcg.wq.proxy.CheckinsOfficeProxy;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.validator.ValidatorException;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-08-31 15:57
 **/
@Component
@Slf4j
public class AttendanceCorrecctService {
    @Autowired
    private CheckinsOfficeV2Service checkinsOfficeV2Service;

    public Integer getCorrerctOwner(IObjectData objectData){
        List<String> owner = objectData.getOwner();
        // 判空 和-10000 取 outerOwner
        if(CollectionUtils.isEmpty(owner) || StringUtils.isBlank(owner.get(0)) || owner.get(0).equals("-10000")){
            //获取outerOwner
            List<String> outOwner = objectData.getOutOwner();
            if (CollectionUtils.isNotEmpty(owner) && StringUtils.isNotBlank(owner.get(0))){
                owner = outOwner;
            }
        }
        if (CollectionUtils.isEmpty(owner) || StringUtils.isBlank(owner.get(0))){
            throw new ValidateException("修正审批申请人为空"); //ignoreI18n
        }
        return Integer.valueOf(owner.get(0));
    }


    /**
     * 调用 checkinsOfficeProxy.checkCorrectTime
     * @param ea
     * @param userId
     * @param attendanceCorrectDetails
     */
    public void checkCorrectTime(String ea, int userId,long correctDate, List<IObjectData> attendanceCorrectDetails) {
        List<CorrectTime> correctTimeList = convertCorrectTimes(correctDate,attendanceCorrectDetails);
        /**
         * 1.根据 AttendanceCorrectDetailFields 字段获取考勤修正详情对象
         * 2.attendanceCorrectDetails 转换成 CorrectTime
         * 3.调用 checkinsOfficeProxy.checkCorrectTime
         * 4.返回值转换成 CheckCorrectTimeResult
         */
        CheckCorrectTimeResult checkCorrectTimeResult = checkinsOfficeV2Service.checkCorrectTimeV2(ea, userId, correctTimeList, I18N.getContext().getLanguage());
        if (checkCorrectTimeResult == null) {
            throw new ValidateException("checkCorrectTimeResult is null");
        } else if (checkCorrectTimeResult.getCode() != 0) {
            throw new ValidateException(checkCorrectTimeResult.getMessage());
        }
    }

    public void saveCorrectTime(String ea, int userId,long correctDate, List<IObjectData> attendanceCorrectDetails,CheckinsOfficeV2Service.CorrectOperateFlag correctOperateFlag) {
        List<CorrectTime> correctTimeList = convertCorrectTimes(correctDate,attendanceCorrectDetails);
        CheckCorrectTimeResult checkCorrectTimeResult = checkinsOfficeV2Service.saveCorrectTime(ea, userId, correctTimeList, correctOperateFlag);
        if (checkCorrectTimeResult.getCode() != 0){
            throw new ValidateException(checkCorrectTimeResult.getMessage());
        }
    }

    @NotNull
    private static List<CorrectTime> convertCorrectTimes(long correctDate,List<IObjectData> attendanceCorrectDetails) {
        if (attendanceCorrectDetails == null || attendanceCorrectDetails.size() == 0) {
            throw new ValidateException("attendanceCorrectDetails is null");
        }
        List<CorrectTime> correctTimeList = Lists.newArrayList();
        /**
         *      correct_type:迟到1 早退2 签到缺卡3 签退缺卡4 其他other
         *      correctTime.correctType: 1-修正时间异常，2-修正补缺卡
         *      correctTime.checkType: 0-签到，1-签退
         *    String CORRECT_TYPE = "correct_type";
         *
         *     String ATTENDANCE_CORRECT = "attendance_correct";
         *
         *     String CORRECT_TARGET_TIME = "correct_target_time";
         *
         *     String CHECK_ID = "check_id";
         *
         *     String REMARK = "remark";
         *
         *     String CORRECT_TIME = "correct_time";
         *
         *     String CORRECT_REASON = "correct_reason";
         */
        for (IObjectData attendanceCorrectDetail : attendanceCorrectDetails) {
            CorrectTime correctTime = new CorrectTime();
            correctTime.setCorrectMainId(Convert.toStr(attendanceCorrectDetail.get(AttendanceCorrectDetailFields.ATTENDANCE_CORRECT)));
            correctTime.setTargeTime(Convert.toDate(attendanceCorrectDetail.get(AttendanceCorrectDetailFields.CORRECT_TARGET_TIME)).getTime());
            //日期
            correctTime.setTargeDate(correctDate);
            correctTime.setCheckId(Convert.toStr(attendanceCorrectDetail.get(AttendanceCorrectDetailFields.CHECK_ID)));
            correctTime.setCorrectTime(Convert.toDate(attendanceCorrectDetail.get(AttendanceCorrectDetailFields.CORRECT_TIME)).getTime());
            correctTime.setCorrectDataId(Convert.toStr(attendanceCorrectDetail.get(BaseField.id.getApiName())));
            Integer aCorrectType = Convert.toInt(attendanceCorrectDetail.get(AttendanceCorrectDetailFields.CORRECT_TYPE));
            if (aCorrectType == null) {
                throw new ValidateException("correct_type is null");
            } else if (aCorrectType == 1) {
                correctTime.setCorrectType(1);
                correctTime.setCheckType(0);
                //设置原来的异常描述 类似于 09:00 未打卡  09:00 迟到2分钟
                correctTime.setMessage(String.format("%s 迟到",DateUtils.getStringFromTime(correctTime.getTargeTime(), DateUtils.DateTimeNoSecondFormat))); //ignoreI18n
            } else if (aCorrectType == 2) {
                correctTime.setCorrectType(1);
                correctTime.setCheckType(1);
                //设置原来的异常描述 类似于 09:00 未打卡  09:00 迟到2分钟
                    correctTime.setMessage(String.format("%s 早退",DateUtils.getStringFromTime(correctTime.getTargeTime(), DateUtils.DateTimeNoSecondFormat))); //ignoreI18n
            } else if (aCorrectType == 3) {
                correctTime.setCorrectType(2);
                correctTime.setCheckType(0);
                //设置原来的异常描述 类似于 09:00 未打卡  09:00 迟到2分钟
                    correctTime.setMessage(String.format("%s 上班未打卡",DateUtils.getStringFromTime(correctTime.getTargeTime(), DateUtils.DateTimeNoSecondFormat))); //ignoreI18n
            } else if (aCorrectType == 4) {
                correctTime.setCorrectType(2);
                correctTime.setCheckType(1);
                //设置原来的异常描述 类似于 09:00 未打卡  09:00 迟到2分钟
                    correctTime.setMessage(String.format("%s 下班未打卡",DateUtils.getStringFromTime(correctTime.getTargeTime(), DateUtils.DateTimeNoSecondFormat))); //ignoreI18n
            } else {
                throw new ValidateException("correct_type is error");
            }

            correctTimeList.add(correctTime);
        }
        return correctTimeList;
    }

}
