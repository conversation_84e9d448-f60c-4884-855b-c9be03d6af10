package com.facishare.crm.fmcg.wq.action;

import com.alibaba.fastjson.JSON;
import com.facishare.appserver.checkinsoffice.api.model.NumInfo;
import com.facishare.appserver.checkinsoffice.api.model.ScheduleDate;
import com.facishare.appserver.checkinsoffice.api.model.ScheduleDetail;
import com.facishare.appserver.utils.DateUtils;
import com.facishare.crm.fmcg.wq.proxy.CheckinsOfficeProxy;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
public class ShiftsFlowCompletedAction extends StandardFlowCompletedAction {

    private CheckinsOfficeProxy checkinsOfficeProxy =
            SpringUtil.getContext().getBean(CheckinsOfficeProxy.class);

    //保存排班表

    @Override
    protected Result after(Arg arg, Result result) {
        log.info("ShiftsFlowCompletedAction is start!,arg:{}",JSON.toJSON(arg));
        Result result1 = super.after(arg, result);
        IObjectData newObjectData = serviceFacade.findObjectDataIncludeDeleted(actionContext.getUser(), arg.getDataId(), objectDescribe.getApiName());

        if(arg.isPass()) {
            String eId = actionContext.getTenantId();
            Integer outerUserId;
            if(Objects.nonNull(actionContext.getUser().getOutUserId())) {
                outerUserId= Integer.valueOf(actionContext.getUser().getOutUserId());
            }else{
                outerUserId= Integer.valueOf(actionContext.getUser().getUserId());
            }
            Long dateTs = Long.parseLong(newObjectData.get("apply_date").toString());
            String dateStr = DateUtils.getStringFromTime(dateTs, "yyyy-MM-dd");
            ScheduleDetail scheduleDetail = new ScheduleDetail();

            String shiftId=newObjectData.get("_id").toString();
            SearchTemplateQuery query = new SearchTemplateQuery();
            query.setLimit(20);
            query.setOffset(0);
            Filter keywordFilter = new Filter();
            keywordFilter.setFieldName("shift_id");
            keywordFilter.setOperator(Operator.EQ);
            keywordFilter.setFieldValues(Lists.newArrayList(shiftId));
            query.setFilters(Lists.newArrayList(keywordFilter));

            List<IObjectData> detailData = serviceFacade.findBySearchQuery(actionContext.getUser(), "ShiftAdjustmentObj", query).getData();

            //查询客户名称
            Set<String> customerIds=new HashSet<>();
            Map<String,String> customerMap=new HashMap<>();
            log.info("ShiftsFlowCompletedAction is start!,detailData:{}",JSON.toJSON(detailData));
            //构造保存排班的参数
            if (!CollectionUtils.isEmpty(detailData)) {
                detailData.forEach(o->{
                    if(Objects.nonNull(o.get("customer_id"))) {
                        customerIds.add(o.get("customer_id").toString());
                    }
                });
                if(!CollectionUtils.isEmpty(customerIds)){
                    SearchTemplateQuery customerQuery = new SearchTemplateQuery();
                    customerQuery.setLimit(20);
                    customerQuery.setOffset(0);
                    Filter customerFilter = new Filter();
                    customerFilter.setFieldName("shift_id");
                    customerFilter.setOperator(Operator.EQ);
                    customerFilter.setFieldValues(Lists.newArrayList(customerIds));
                    query.setFilters(Lists.newArrayList(customerFilter));

                    List<IObjectData> customerData = serviceFacade.findBySearchQuery(actionContext.getUser(), "AccountObj", query).getData();
                    if(!CollectionUtils.isEmpty(customerData)){
                        customerData.forEach(o->customerMap.put(o.getId(),o.getName()));
                    }
                }
                if (detailData.size() > 10) {
                    throw new ValidateException("每天的班次不能超过10个"); //ignoreI18n
                }
                ScheduleDate scheduleDate = new ScheduleDate();
                scheduleDate.setDateTs(dateTs);
                scheduleDate.setDateStr(dateStr);
                detailData.forEach(o -> {
                    Integer num = Integer.valueOf(o.get("nums").toString());
                    NumInfo numInfo = new NumInfo();
                    numInfo.setNum(num);
                    if(Objects.nonNull(o.get("customer_id"))) {
                        numInfo.setCustomerId(o.get("customer_id").toString());
                        numInfo.setCustomerName(customerMap.get(o.get("customer_id").toString()));
                    }
                    if (CollectionUtils.isEmpty(scheduleDate.getNums())) {
                        scheduleDate.setNums(Lists.newArrayList(num));
                        scheduleDate.setNumInfos(Lists.newArrayList(numInfo));
                    } else {
                        scheduleDate.getNums().add(num);
                        scheduleDate.getNumInfos().add(numInfo);
                    }

                });
                scheduleDetail.setScheduleDates(Lists.newArrayList(scheduleDate));
                scheduleDetail.setUserId(outerUserId);

                //校验时间（判断当前时间是否晚于班次的开始时间）
                if(checkinsOfficeProxy.isUpdateSchedule(eId, outerUserId.toString(), scheduleDetail)){
//                log.info(JSON.toJSONString(scheduleDetail));
                    checkinsOfficeProxy.saveSchedule(eId, outerUserId.toString(), scheduleDetail);
                    newObjectData.set("apply_status", "正常"); //ignoreI18n
                }else{
                    newObjectData.set("apply_status", "已失效"); //ignoreI18n

                }

            }
        }else{
            newObjectData.set("apply_status", "未生效"); //ignoreI18n
        }
        serviceFacade.updateObjectData(actionContext.getUser(), newObjectData);
        return result1;
    }

}
