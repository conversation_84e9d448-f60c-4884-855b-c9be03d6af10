package com.facishare.crm.fmcg.wq.servcie.impl;

import java.util.ArrayList;
import java.util.List;

public class TimeSlotChecker {

    public static void main(String[] args) {
        List<Long[]> timeSlots = new ArrayList<>();
        timeSlots.add(new Long[]{8L, 10L});
        timeSlots.add(new Long[]{11L, 12L});
//        timeSlots.add(new Long[]{12L, 13L});

        if (checkTimeSlots(timeSlots)) {
            System.out.println("时间组没有交叉"); //ignoreI18n
        } else {
            System.out.println("时间组存在交叉"); //ignoreI18n
        }
    }

    static class TimeSlot {
        int startTime;
        int endTime;

        TimeSlot(int startTime, int endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }
    }

    public static boolean checkTimeSlots(List<Long[]> timeSlots) {
        for (int i = 0; i < timeSlots.size(); i++) {
            for (int j = i + 1; j < timeSlots.size(); j++) {
                if (timeSlots.get(i)[1] >= timeSlots.get(j)[0] &&
                        timeSlots.get(j)[1] >= timeSlots.get(i)[0]) {
                    return false;
                }
            }
        }
        return true;
    }
}
