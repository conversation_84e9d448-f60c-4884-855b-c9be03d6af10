package com.facishare.crm.fmcg.wq.action;

import com.facishare.crm.fmcg.wq.constants.AccountShiftDetailFields;
import com.facishare.paas.appframework.core.predef.action.StandardInsertImportTemplateAction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.Iterator;
import java.util.List;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 客户班次新建导入 模板
 * 控制导入模板中包括的列
 *
 * 扩展点：
 *
 * 1 com.facishare.paas.appframework.core.predef.action.BaseImportTemplateAction#customHeader（）：在通用的处理逻辑后，控制模板展示的列
 * @author: zhangsm
 * @create: 2024-02-20 14:37
 **/
public class AccountShiftDetailInsertImportTemplateAction extends StandardInsertImportTemplateAction {
    @Override
    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        super.customHeader(headerFieldList);
        Iterator<IFieldDescribe> iterator = headerFieldList.iterator();

        while(iterator.hasNext()) {
            IFieldDescribe iField = (IFieldDescribe)iterator.next();
            if (iField.getApiName().equals(AccountShiftDetailFields.SHIFT_NO)) {
                iterator.remove();
            }
        }
    }

    @Override
    protected List<IFieldDescribe> sortHeader(List<IFieldDescribe> validFieldList) {
        List<IFieldDescribe> iFieldDescribes = super.sortHeader(validFieldList);
        //如果包含start_time和end_time字段,将end_time字段放在start_time字段之后一位，其他顺序不改变
        if (iFieldDescribes.stream().anyMatch(iFieldDescribe -> iFieldDescribe.getApiName().equals(AccountShiftDetailFields.START_TIME)) &&
                iFieldDescribes.stream().anyMatch(iFieldDescribe -> iFieldDescribe.getApiName().equals(AccountShiftDetailFields.END_TIME))) {
            //移除end_time字段，获取start_time字段的位置，将end_time字段放在start_time字段后面
            IFieldDescribe end_time = iFieldDescribes.stream().filter(iFieldDescribe -> iFieldDescribe.getApiName().equals(AccountShiftDetailFields.END_TIME)).findFirst().get();
            iFieldDescribes.remove(end_time);
            int index = iFieldDescribes.indexOf(iFieldDescribes.stream().filter(iFieldDescribe -> iFieldDescribe.getApiName().equals(AccountShiftDetailFields.START_TIME)).findFirst().get());
            iFieldDescribes.add(index + 1, end_time);
        }

        return iFieldDescribes;
    }
}
