package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-08-31 16:22
 **/
public class AttendanceCorrectDetailEditAction extends StandardEditAction {
    @Override
    protected void before(Arg arg) {
        throw new ValidateException("考勤修正详情,不支持单独编辑"); //ignoreI18n
    }
}
