package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.dao.UserScheduleDao;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2024-03-19 16:18
 **/
public class UserScheduleDetailRelatedListController extends StandardRelatedListController {
    UserScheduleDao userScheduleDao = SpringUtil.getContext().getBean(UserScheduleDao.class);
    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        userScheduleDao.formatUserScheduleDetailDuration(result.getDataList().stream().map(o->o.toObjectData()).collect(Collectors.toList()));
        return after;
    }
}
