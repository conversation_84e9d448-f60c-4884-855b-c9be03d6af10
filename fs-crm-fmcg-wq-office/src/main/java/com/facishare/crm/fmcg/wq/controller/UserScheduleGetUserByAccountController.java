package com.facishare.crm.fmcg.wq.controller;

import com.facishare.crm.fmcg.wq.model.UserScheduleContent;
import com.facishare.crm.fmcg.wq.servcie.OfficeShiftService;
import com.facishare.crm.fmcg.wq.servcie.impl.OfficeShiftServiceImpl;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.SetUtils;
import org.redisson.api.annotation.REntity;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: fs-crm-fmcg-wq
 * @description: 客户视图排班表列表查询接口
 * @author: zhangsm
 * @create: 2023-12-18 16:08
 **/
public class UserScheduleGetUserByAccountController extends PreDefineController<UserScheduleGetUserByAccountController.Arg, UserScheduleGetUserByAccountController.Result> {

    OfficeShiftService officeShiftService =  SpringUtil.getContext().getBean(OfficeShiftServiceImpl.class);
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return ListUtils.EMPTY_LIST;
    }

    @Override
    protected Result doService(Arg arg) {
        //今日以前报错
        LocalDate now = LocalDate.now();
        LocalDate date = LocalDate.parse(arg.getDateStr());
        if (date.isBefore(now)) {
            throw new ValidateException("日期不能早于今天"); //ignoreI18n
        }
        //用户id
        Set<Integer> userIds = Sets.newHashSet(arg.getUserIds());
        boolean isOuter;
        if (CollectionUtils.isNotEmpty(userIds)){
            isOuter = userIds.stream().anyMatch(o->o.intValue() >*********);
        } else {
            throw new ValidateException("参数错误 用户ids为空"); //ignoreI18n
        }
        Set<Integer> shiftDetailUserIds = SetUtils.EMPTY_SET;
        if (CollectionUtils.isNotEmpty(arg.getShiftScheduleList())) {
            //变更中的不为空的话 过滤取当前日期的数据
            List<UserScheduleContent.ShiftSchedule> shiftScheduleList = arg.getShiftScheduleList().stream().filter(shiftSchedule -> shiftSchedule.getDate().equals(arg.getDateStr()) && shiftSchedule.getAccountShiftDetailId().equals(arg.getAccountShiftDetailId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(shiftScheduleList)) {
                shiftDetailUserIds = shiftScheduleList.get(0).getUserIds().stream().map(o->o.getUserId()).collect(Collectors.toSet());
            } else {
                //目前db里的 用户id 和对应的数据
                Map<Integer, List<IObjectData>> dbUserScheduleMapByAccountDetail = officeShiftService.getUserScheduleMapByAccountDetail(controllerContext.getTenantId(), Lists.newArrayList(arg.getAccountShiftDetailId()), arg.getDateStr(),isOuter);
                shiftDetailUserIds = dbUserScheduleMapByAccountDetail.keySet();
            }
        } else {
            //目前db里的 用户id 和对应的数据
            Map<Integer, List<IObjectData>> dbUserScheduleMapByAccountDetail = officeShiftService.getUserScheduleMapByAccountDetail(controllerContext.getTenantId(), Lists.newArrayList(arg.getAccountShiftDetailId()), arg.getDateStr(),isOuter);
            shiftDetailUserIds = dbUserScheduleMapByAccountDetail.keySet();
        }


        userIds.removeAll(shiftDetailUserIds);
        //根据已经变更的数据查询所有的用户
        UserScheduleContent userScheduleContent = new UserScheduleContent();
        if (CollectionUtils.isNotEmpty(arg.getShiftScheduleList())) {
            userScheduleContent.setDateShiftDetailScheduleMap(arg.getShiftScheduleList().stream().filter(p->arg.getDateStr().equals(p.getDate())).collect(Collectors.groupingBy(UserScheduleContent.ShiftSchedule::getDate, Collectors.toMap(UserScheduleContent.ShiftSchedule::getAccountShiftDetailId, o -> o, (o, o2) -> o))));
            officeShiftService.fillDateShiftDetailMap(controllerContext.getTenantId(), userScheduleContent,arg.getUserIds().stream().anyMatch(o->o.intValue() >*********));
        }
        if (userScheduleContent.getDateUserScheduleMap() == null){
            userScheduleContent.setDateUserScheduleMap(Maps.newConcurrentMap());
        }
        UserScheduleContent otherUserScheduleContent;
        Map<Integer, UserScheduleContent.UserSchedule> argUserScheduleMap = userScheduleContent.getDateUserScheduleMap().get(arg.getDateStr()) == null? MapUtils.EMPTY_MAP : userScheduleContent.getDateUserScheduleMap().get(arg.getDateStr());
        if (argUserScheduleMap != null) {
            //查询剩余用户的数据
            List<Integer> otherUserIds = ListUtils.removeAll(userIds, argUserScheduleMap.keySet());
            otherUserScheduleContent = officeShiftService.batchGetUserScheduleIgnoreOther(controllerContext.getTenantId(), null, otherUserIds, arg.getDateStr(), arg.getDateStr());
        }else {
            argUserScheduleMap = MapUtils.EMPTY_MAP;
            otherUserScheduleContent = officeShiftService.batchGetUserScheduleIgnoreOther(controllerContext.getTenantId(), null, Lists.newArrayList(userIds), arg.getDateStr(), arg.getDateStr());
        }
        if (otherUserScheduleContent.getDateUserScheduleMap() == null) {
            otherUserScheduleContent.setDateUserScheduleMap(Maps.newConcurrentMap());
        }
        Map<Integer, UserScheduleContent.UserSchedule> otherUserScheduleMap = otherUserScheduleContent.getDateUserScheduleMap().get(arg.getDateStr()) == null? MapUtils.EMPTY_MAP : otherUserScheduleContent.getDateUserScheduleMap().get(arg.getDateStr());
        Map<Integer,UserScheduleContent.UserSchedule> userScheduleMap = Maps.newHashMapWithExpectedSize(userIds.size());
        //每个里增加一个班次详情
        for (Integer userId : userIds) {
            UserScheduleContent.UserSchedule tempUserSchedule;
            if (argUserScheduleMap.containsKey(userId)) {
                tempUserSchedule = argUserScheduleMap.get(userId);
            } else if (otherUserScheduleMap.containsKey(userId)) {
                tempUserSchedule = otherUserScheduleMap.get(userId);
            } else {
                tempUserSchedule = new UserScheduleContent.UserSchedule();
                tempUserSchedule.setUserId(userId);
                tempUserSchedule.setDate(arg.getDateStr());
            }
            if (tempUserSchedule.getAccountShiftDetailIdList() == null){
                tempUserSchedule.setAccountShiftDetailIdList(Lists.newArrayList(arg.getAccountShiftDetailId()));
            }else{
                tempUserSchedule.getAccountShiftDetailIdList().add(arg.getAccountShiftDetailId());
            }
            userScheduleMap.put(userId, tempUserSchedule);
        }
        userScheduleContent.setDateUserScheduleMap(Maps.newConcurrentMap());
        userScheduleContent.getDateUserScheduleMap().put(arg.getDateStr(),userScheduleMap);
        //check
        Map<String, Map<Integer, UserScheduleContent.UserSchedule>> stringMapMap = officeShiftService.checkUserScheduleConflictAndFillData(controllerContext.getTenantId(), userScheduleContent, false,arg.getUserIds().stream().anyMatch(o->o.intValue() >*********));
        if (!stringMapMap.isEmpty()) {
            Map<Integer,Integer> userIdAndErrorCodeMap = Maps.newConcurrentMap();

            stringMapMap.entrySet().stream().flatMap(o -> o.getValue().entrySet().stream()).collect(Collectors.groupingBy(o -> o.getValue().getErrorReason())).entrySet().stream().forEach(o -> {
                //过滤掉日期异常
                if (o.getKey() == UserScheduleContent.SCHEDULE_TIME_INSUFFICIENT) {
                    return;
                }
                o.getValue().forEach(o1 -> {
                    userIdAndErrorCodeMap.put(o1.getValue().getUserId(),o.getKey());
                });
            });
            if (MapUtils.isNotEmpty(userIdAndErrorCodeMap)){
                log.info("userIdAndErrorCodeMap : {}",userIdAndErrorCodeMap);
            }
            userIds.removeAll(userIdAndErrorCodeMap.keySet());
        }
        // userId 有移除错误的数据 不要直接使用userScheduleContent.getExcelUserIds()
        userIds.addAll(shiftDetailUserIds);
        userScheduleContent.getExcelUserIds().addAll(shiftDetailUserIds);
        userScheduleContent.setExcelUserIds(userScheduleContent.getExcelUserIds().stream().distinct().collect(Collectors.toList()));
        userIds.removeIf( o-> !arg.getUserIds().contains(o));
        officeShiftService.fillEmployeeName(controllerContext.getTenantId(),userScheduleContent);
        userScheduleContent.getUserNameMap().keySet().removeIf(o-> !arg.getUserIds().contains(o));
        result = new Result();
        result.setUserIds(Lists.newArrayList(userIds));
        result.setUserNameMap(userScheduleContent.getUserNameMap());
        return result;
    }

    @Data
    public static class Arg {
        /**
         * 用户ids
         */
        private List<Integer> userIds;
        /**
         * 客户id
         */
        private String accountId;
        /**
         * 门店班次详情id
         */
        private String accountShiftDetailId;
        /**
         * 日期
         */
        private String dateStr;
        /**
         * 已经编辑的数据
         */
        private List<UserScheduleContent.ShiftSchedule> shiftScheduleList;

    }
    @Data
    public static class Result {
        private List<Integer> userIds;
        private Map<Integer, String> userNameMap;

    }
}
