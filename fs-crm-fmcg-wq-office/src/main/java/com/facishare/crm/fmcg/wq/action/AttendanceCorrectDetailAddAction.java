package com.facishare.crm.fmcg.wq.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardAddAction;

/**
 * @program: fs-crm-fmcg-wq
 * @description:
 * @author: zhangsm
 * @create: 2023-08-31 16:22
 **/
public class AttendanceCorrectDetailAddAction extends StandardAddAction {
    @Override
    protected void before(Arg arg) {
        throw new ValidateException("考勤修正详情,不支持单独新增"); //ignoreI18n
    }
}
