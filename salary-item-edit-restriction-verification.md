# 工资项编辑限制功能验证指南

## 功能验证目标

验证当工资项被员工固定工资表选为从对象时，只允许编辑名称和工资项说明字段的限制功能。

## 验证步骤

### 步骤1：准备测试数据

#### 1.1 创建工资项
1. 进入工资项管理页面
2. 创建一个新的工资项，例如：
   - 名称：测试基本工资
   - 工资项说明：用于测试编辑限制的基本工资项
   - 增减属性：应发项
   - 计算类型：固定金额
   - 其他字段按需填写

#### 1.2 创建员工固定工资表
1. 进入员工固定工资管理页面
2. 创建员工固定工资记录
3. 在员工固定工资明细中选择上面创建的工资项

### 步骤2：验证编辑限制

#### 2.1 验证被使用前的编辑权限
1. 在工资项被员工固定工资表使用前
2. 编辑该工资项，尝试修改各种字段
3. **预期结果**：所有字段都可以正常修改和保存

#### 2.2 验证被使用后的编辑限制
1. 确保工资项已被员工固定工资表使用
2. 编辑该工资项，尝试修改以下字段：

**允许修改的字段**：
- ✅ 工资项名称
- ✅ 工资项说明

**应被限制的字段**：
- ❌ 增减属性
- ❌ 计算公式
- ❌ 计算类型
- ❌ 其他业务字段

3. **预期结果**：
   - 允许修改的字段可以正常保存
   - 被限制的字段修改会被自动重置，不会保存

### 步骤3：验证日志记录

#### 3.1 查看应用日志
在编辑工资项时，查看应用日志中的相关记录：

**未被使用时的日志**：
```
工资项 [工资项ID] 未被工资规则或员工固定工资表使用，允许编辑全部字段
```

**被员工固定工资表使用时的日志**：
```
工资项 [工资项ID] 已被 0 个工资规则和 1 个员工固定工资表使用，限制编辑字段
工资项 [工资项ID] 被以下对象使用: 员工固定工资表: 1条记录
工资项 [工资项ID] 字段 [字段名] 被重置，因为工资项已被使用
```

**同时被工资规则和员工固定工资表使用时的日志**：
```
工资项 [工资项ID] 已被 1 个工资规则和 2 个员工固定工资表使用，限制编辑字段
工资项 [工资项ID] 被以下对象使用: 工资规则: [规则名称]；员工固定工资表: 2条记录
```

### 步骤4：边界情况验证

#### 4.1 验证多个员工固定工资表使用
1. 创建多个员工固定工资记录
2. 都使用同一个工资项
3. 编辑该工资项
4. **预期结果**：仍然只能编辑名称和说明

#### 4.2 验证同时被工资规则和员工固定工资表使用
1. 创建工资规则，使用该工资项
2. 同时在员工固定工资表中使用该工资项
3. 编辑该工资项
4. **预期结果**：仍然只能编辑名称和说明

#### 4.3 验证删除使用关系后的恢复
1. 删除员工固定工资明细中对该工资项的引用
2. 确保工资项不再被任何对象使用
3. 编辑该工资项
4. **预期结果**：恢复全部字段的编辑权限

## 验证检查点

### ✅ 功能正确性检查
- [ ] 未被使用的工资项可以编辑所有字段
- [ ] 被员工固定工资表使用的工资项只能编辑名称和说明
- [ ] 被工资规则使用的工资项只能编辑名称和说明
- [ ] 同时被多个对象使用的工资项只能编辑名称和说明
- [ ] 删除使用关系后恢复全部编辑权限

### ✅ 数据一致性检查
- [ ] 允许修改的字段变更能正常保存
- [ ] 被限制的字段修改被正确重置
- [ ] 数据库中的数据与页面显示一致
- [ ] 不会出现数据损坏或不一致

### ✅ 日志记录检查
- [ ] 编辑限制的基本信息被正确记录
- [ ] 使用情况统计准确（工资规则数量、员工固定工资表数量）
- [ ] 字段重置操作被详细记录
- [ ] 日志级别和格式符合规范

### ✅ 性能检查
- [ ] 编辑操作响应时间正常
- [ ] 使用情况查询不会导致明显延迟
- [ ] 大量使用记录时性能可接受
- [ ] 并发编辑时表现稳定

### ✅ 用户体验检查
- [ ] 编辑限制对用户透明，无错误提示
- [ ] 页面操作流畅，无异常卡顿
- [ ] 字段重置后页面显示正确
- [ ] 保存操作反馈及时准确

## 常见问题排查

### 问题1：编辑限制不生效
**可能原因**：
- EmployeeFixedSalaryDetailDao.getBySalaryItemId方法未正确实现
- 查询条件不正确
- 数据库中的关联关系有问题

**排查方法**：
1. 检查数据库中员工固定工资明细表的数据
2. 验证工资项ID的关联关系
3. 查看应用日志中的查询结果

### 问题2：允许编辑的字段也被限制
**可能原因**：
- EDITABLE_FIELDS_WHEN_USED常量配置不正确
- 字段名称不匹配

**排查方法**：
1. 检查常量定义
2. 验证字段名称的一致性
3. 查看字段重置的日志记录

### 问题3：日志记录不完整
**可能原因**：
- 日志级别配置问题
- 日志格式化异常

**排查方法**：
1. 检查日志配置
2. 验证日志输出路径
3. 查看是否有异常堆栈

### 问题4：性能问题
**可能原因**：
- 使用情况查询效率低
- 数据库索引缺失
- 查询结果集过大

**排查方法**：
1. 分析SQL执行计划
2. 检查相关表的索引
3. 监控查询执行时间

## 验证报告模板

### 验证环境
- 测试环境：[环境名称]
- 测试时间：[测试日期]
- 测试人员：[测试人员]
- 版本信息：[代码版本]

### 验证结果
| 验证项目 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|------|------|
| 未被使用时全部可编辑 | 通过 | 通过 | ✅ | |
| 被员工固定工资表使用时限制编辑 | 通过 | 通过 | ✅ | |
| 被工资规则使用时限制编辑 | 通过 | 通过 | ✅ | |
| 同时被多个对象使用时限制编辑 | 通过 | 通过 | ✅ | |
| 删除使用关系后恢复编辑权限 | 通过 | 通过 | ✅ | |
| 日志记录完整准确 | 通过 | 通过 | ✅ | |
| 性能表现良好 | 通过 | 通过 | ✅ | |

### 问题记录
| 问题描述 | 严重程度 | 状态 | 解决方案 |
|---------|---------|------|---------|
| [问题描述] | [高/中/低] | [待解决/已解决] | [解决方案] |

### 验证结论
- [ ] 功能完全符合预期，可以发布
- [ ] 功能基本符合预期，有轻微问题但不影响使用
- [ ] 功能存在问题，需要修复后重新验证
- [ ] 功能严重不符合预期，需要重新开发

### 建议和改进
1. [改进建议1]
2. [改进建议2]
3. [改进建议3]
