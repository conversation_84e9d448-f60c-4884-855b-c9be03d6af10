# 工资时间一致性测试文档

## 测试目的

验证工资发放单、工资条、工资条明细的时间字段是否保持一致，特别是在考虑工资规则生效日期调整后的时间透传。

## 测试场景

### 场景1：生效日期晚于周期开始时间

**测试数据**：
- 周期开始时间：2024-01-01 00:00:00
- 周期结束时间：2024-01-31 23:59:59
- 工资规则生效日期：2024-01-15 00:00:00

**预期结果**：
- 工资发放单开始时间：2024-01-15 00:00:00（调整后）
- 工资发放单结束时间：2024-01-31 23:59:59
- 工资条开始时间：2024-01-15 00:00:00（与发放单一致）
- 工资条结束时间：2024-01-31 23:59:59（与发放单一致）
- 工资条明细开始时间：2024-01-15 00:00:00（与工资条一致）
- 工资条明细结束时间：2024-01-31 23:59:59（与工资条一致）

### 场景2：生效日期早于周期开始时间

**测试数据**：
- 周期开始时间：2024-01-15 00:00:00
- 周期结束时间：2024-01-31 23:59:59
- 工资规则生效日期：2024-01-01 00:00:00

**预期结果**：
- 工资发放单开始时间：2024-01-15 00:00:00（不调整）
- 工资发放单结束时间：2024-01-31 23:59:59
- 工资条开始时间：2024-01-15 00:00:00（与发放单一致）
- 工资条结束时间：2024-01-31 23:59:59（与发放单一致）
- 工资条明细开始时间：2024-01-15 00:00:00（与工资条一致）
- 工资条明细结束时间：2024-01-31 23:59:59（与工资条一致）

### 场景3：没有设置生效日期

**测试数据**：
- 周期开始时间：2024-01-01 00:00:00
- 周期结束时间：2024-01-31 23:59:59
- 工资规则生效日期：null

**预期结果**：
- 工资发放单开始时间：2024-01-01 00:00:00（不调整）
- 工资发放单结束时间：2024-01-31 23:59:59
- 工资条开始时间：2024-01-01 00:00:00（与发放单一致）
- 工资条结束时间：2024-01-31 23:59:59（与发放单一致）
- 工资条明细开始时间：2024-01-01 00:00:00（与工资条一致）
- 工资条明细结束时间：2024-01-31 23:59:59（与工资条一致）

## 测试方法

### 1. 单元测试

```java
@Test
public void testTimeConsistencyWithEffectiveDate() {
    // 准备测试数据
    IObjectData salaryRule = createSalaryRuleWithEffectiveDate("2024-01-15");
    IObjectData employeeFixedSalary = createEmployeeFixedSalary();
    
    long originalStartTime = parseDate("2024-01-01").getTime();
    long endTime = parseDate("2024-01-31").getTime();
    long expectedAdjustedStartTime = parseDate("2024-01-15").getTime();
    
    // 执行测试
    IObjectData salaryPaymentSlip = salaryService.createSalaryPaymentSlip(
        salaryRule, originalStartTime, endTime, null);
    
    IObjectData salaryData = salaryService.generateSalaryData(
        employeeFixedSalary, salaryRule, 
        salaryPaymentSlip.get(SalaryPaymentSlipFields.START_DATE, Long.class),
        salaryPaymentSlip.get(SalaryPaymentSlipFields.END_DATE, Long.class),
        salaryPaymentSlip);
    
    List<IObjectData> salaryDetails = salaryDetailDataDao.getExistSalaryDetailData(
        tenantId, employeeFixedSalary.getId(), 
        salaryPaymentSlip.get(SalaryPaymentSlipFields.START_DATE, Long.class),
        salaryPaymentSlip.get(SalaryPaymentSlipFields.END_DATE, Long.class));
    
    // 验证时间一致性
    assertEquals(expectedAdjustedStartTime, 
        salaryPaymentSlip.get(SalaryPaymentSlipFields.START_DATE, Long.class));
    assertEquals(expectedAdjustedStartTime, 
        salaryData.get(SalaryDataFields.START_DATE, Long.class));
    
    for (IObjectData detail : salaryDetails) {
        assertEquals(expectedAdjustedStartTime, 
            detail.get(SalaryDetailDataFields.START_DATE, Long.class));
        assertEquals(endTime, 
            detail.get(SalaryDetailDataFields.END_DATE, Long.class));
    }
}
```

### 2. 集成测试

```java
@Test
public void testAsyncSalaryGenerationTimeConsistency() {
    // 准备测试数据
    IObjectData salaryRule = createSalaryRuleWithEffectiveDate("2024-01-15");
    List<IObjectData> employees = createEmployeeFixedSalaries();
    
    long originalStartTime = parseDate("2024-01-01").getTime();
    long endTime = parseDate("2024-01-31").getTime();
    
    // 执行异步生成
    salaryService.generateSalaryDataAndPaymentSlip(
        tenantId, salaryRule, employees, originalStartTime, endTime);
    
    // 等待异步处理完成
    Thread.sleep(5000);
    
    // 验证结果
    List<IObjectData> paymentSlips = salaryPaymentSlipDao.getByRangeAndRule(
        tenantId, originalStartTime, endTime, salaryRule.getId());
    
    assertFalse(paymentSlips.isEmpty());
    IObjectData paymentSlip = paymentSlips.get(0);
    
    long adjustedStartTime = paymentSlip.get(SalaryPaymentSlipFields.START_DATE, Long.class);
    assertEquals(parseDate("2024-01-15").getTime(), adjustedStartTime);
    
    // 验证工资条时间
    List<IObjectData> salaryDataList = salaryDataDao.getBySalaryPaymentSlipId(
        tenantId, paymentSlip.getId());
    
    for (IObjectData salaryData : salaryDataList) {
        assertEquals(adjustedStartTime, 
            salaryData.get(SalaryDataFields.START_DATE, Long.class));
        
        // 验证工资条明细时间
        List<IObjectData> details = salaryDetailDataDao.getExistSalaryDetailData(
            tenantId, salaryData.get(SalaryDataFields.EMPLOYEE_FIXED_SALARY, String.class),
            adjustedStartTime, endTime);
        
        for (IObjectData detail : details) {
            assertEquals(adjustedStartTime, 
                detail.get(SalaryDetailDataFields.START_DATE, Long.class));
        }
    }
}
```

### 3. 定时任务测试

```java
@Test
public void testSalaryTaskTimeConsistency() {
    // 准备测试数据
    IObjectData salaryRule = createSalaryRuleWithEffectiveDate("2024-01-15");
    
    SalaryTaskMessage taskMessage = new SalaryTaskMessage();
    taskMessage.setTenantId(tenantId);
    taskMessage.setSalaryRuleId(salaryRule.getId());
    taskMessage.setStartDateStr("2024-01-01");
    taskMessage.setEndDateStr("2024-01-31");
    taskMessage.setFlag(0); // 先计算明细
    
    // 执行flag=0阶段（计算明细）
    salaryService.processSalaryTask(taskMessage);
    
    // 验证明细时间
    List<IObjectData> employees = employeeFixedSalaryDao.getBySalaryRuleId(
        tenantId, salaryRule.getId());
    
    for (IObjectData employee : employees) {
        List<IObjectData> details = salaryDetailDataDao.getExistSalaryDetailData(
            tenantId, employee.getId(), 
            parseDate("2024-01-15").getTime(), // 应该使用调整后的时间
            parseDate("2024-01-31").getTime());
        
        assertFalse(details.isEmpty());
        
        for (IObjectData detail : details) {
            assertEquals(parseDate("2024-01-15").getTime(), 
                detail.get(SalaryDetailDataFields.START_DATE, Long.class));
        }
    }
    
    // 执行flag=1阶段（生成工资条和发放单）
    taskMessage.setFlag(1);
    salaryService.processSalaryTask(taskMessage);
    
    // 验证工资条和发放单时间一致性
    // ... 类似上面的验证逻辑
}
```

## 验证要点

### 1. 时间调整逻辑验证

- ✅ `createSalaryPaymentSlip`方法正确调整开始时间
- ✅ `generateSalaryDataAsync`方法使用工资发放单中的调整时间
- ✅ `generateSalaryDataAndPaymentSlip`方法使用调整后的时间
- ✅ `processSalaryTask`方法在flag=0阶段使用调整后的时间计算明细

### 2. 时间透传验证

- ✅ 工资发放单 → 工资条：时间正确透传
- ✅ 工资条 → 工资条明细：时间正确透传
- ✅ 定时任务 → 工资条明细：时间正确透传

### 3. 边界情况验证

- ✅ 生效日期为null的处理
- ✅ 生效日期早于开始时间的处理
- ✅ 生效日期晚于结束时间的异常处理

## 修改总结

### 已修改的文件

1. **SalaryServiceImpl.java**
   - `processSalaryTask`方法：在flag=0阶段添加生效日期调整逻辑
   - `generateSalaryDataAsync`方法：添加注释说明使用调整后的时间
   - `generateSalaryDataAndPaymentSlip`方法：从工资发放单获取调整后的时间
   - `createSalaryPaymentSlip`方法：增强时间调整日志

2. **SalaryPaymentSlipAddAction.java**
   - `before`方法：添加生效日期调整逻辑
   - 新增`adjustStartTimeByEffectiveDate`方法

### 关键改进

1. **时间一致性**：确保工资发放单、工资条、工资条明细使用相同的调整后时间
2. **完整覆盖**：涵盖前端创建、后端异步生成、定时任务等所有场景
3. **详细日志**：添加时间调整过程的详细日志记录
4. **向后兼容**：不影响现有业务逻辑，只是确保时间一致性

### 预期效果

- 解决工资条明细时间与工资发放单时间不一致的问题
- 确保时间冲突检查使用正确的时间范围
- 提高数据一致性和业务逻辑的准确性
