# SalaryRule角色字段多选功能实现文档

## 功能概述

本实现为SalaryRuleFields中的`APPLICABLE_ROLE_INTE`（适用内部角色）和`APPLICABLE_OUT_ROLE`（适用外部角色）字段提供了多选功能支持。

## 实现组件

### 1. 角色查询服务接口 (RoleQueryService)

**位置**: `fs-crm-fmcg-wq-salary/src/main/java/com/facishare/crm/fmcg/wq/service/RoleQueryService.java`

**功能**:
- 定义角色查询的标准接口
- 提供内部角色和外部角色的查询方法
- 支持字段描述的动态选项设置

### 2. 角色查询服务实现 (RoleQueryServiceImpl)

**位置**: `fs-crm-fmcg-wq-salary/src/main/java/com/facishare/crm/fmcg/wq/service/impl/RoleQueryServiceImpl.java`

**功能**:
- 实现角色查询的具体逻辑
- 集成真实的PaaSPermissionService角色查询API
- 提供预设选项作为API调用失败时的备选方案
- 支持字段描述的多选配置
- 使用反射机制处理角色对象，避免强依赖

**真实角色查询**:
- 调用`paaSPermissionService.roleList()`获取真实角色数据
- 自动过滤已删除的角色（delFlag=true）
- 支持异常处理和降级到预设选项

**内部角色预设选项**（备选方案）:
- 销售人员 (00000000000000000000000000000015)
- 售后人员 (00000000000000000000000000000014)
- 服务人员 (00000000000000000000000000000010)
- 外勤角色 (00000000000000000000000000000026)
- CRM观察者 (00000000000000000000000000000009)

**外部角色查询**:
- 调用`linkAppService.listAllOutRole()`获取真实外部角色数据
- 使用配置化的`checkConnectedAppId`连接应用ID
- 支持异常处理和降级到预设选项

**外部角色预设选项**（备选方案）:
- 外部销售 (EXTERNAL_SALES)
- 外部客服 (EXTERNAL_SERVICE)
- 外部经理 (EXTERNAL_MANAGER)
- 外部操作员 (EXTERNAL_OPERATOR)

### 3. 工资规则列表头控制器 (SalaryRuleListHeaderController)

**位置**: `fs-crm-fmcg-wq-salary/src/main/java/com/facishare/crm/fmcg/wq/controller/SalaryRuleListHeaderController.java`

**功能**:
- 处理工资规则列表页面的字段描述
- 为角色字段动态添加选项
- 支持多选字段的前端展示

### 4. 工资规则详情页控制器 (SalaryRuleWebDetailController)

**位置**: `fs-crm-fmcg-wq-salary/src/main/java/com/facishare/crm/fmcg/wq/controller/SalaryRuleWebDetailController.java`

**功能**:
- 处理工资规则详情页面的字段描述
- 为角色字段动态添加选项
- 重写计算公式字段显示
- 支持多选字段的详情展示

### 5. 测试类 (RoleQueryServiceImplTest)

**位置**: `fs-crm-fmcg-wq-salary/src/test/java/com/facishare/crm/fmcg/wq/service/impl/RoleQueryServiceImplTest.java`

**功能**:
- 验证角色查询服务的功能
- 测试异常处理机制
- 确保代码质量和稳定性

## 使用方式

### 1. 字段配置

在SalaryRuleFields中，以下字段将支持多选：

```java
// 适用内部角色 - 支持多选
public static final String APPLICABLE_ROLE_INTE = "applicable_role_inte";

// 适用外部角色 - 支持多选  
public static final String APPLICABLE_OUT_ROLE = "applicable_out_role";
```

### 2. 数据存储格式

字段数据仍然以array类型存储，例如：
```json
{
  "applicable_role_inte": ["00000000000000000000000000000015", "00000000000000000000000000000014"],
  "applicable_out_role": ["EXTERNAL_SALES", "EXTERNAL_SERVICE"]
}
```

### 3. 前端展示

- **列表页**: 字段将显示为多选下拉框，包含所有可用的角色选项
- **详情页**: 字段将显示为多选下拉框，支持查看和编辑已选择的角色

## 技术特点

### 1. 高可用性
- 异常处理完善，服务异常时返回空列表而不是抛出异常
- 日志记录详细，便于问题排查
- 参数验证严格，防止空指针异常

### 2. 高复用性
- 角色查询逻辑封装在独立的服务中，可被多个控制器复用
- 字段处理逻辑统一，减少代码重复
- 接口设计清晰，便于扩展和维护

### 3. 性能优化
- 角色选项在控制器层动态生成，避免数据库频繁查询
- 使用预设选项减少外部服务调用
- 日志级别合理，避免性能影响

## 扩展说明

### 1. 真实角色数据集成

✅ **已完成**: 当前实现已经集成了真实的角色查询API：

```java
// 已集成PaaSPermissionService
@Autowired
private PaaSPermissionService paaSPermissionService;

// 调用真实的角色查询API
private PaaSResult<RoleListDto.Result> getAllRoleInfoList(int sourceEid) {
    RoleListDto.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(
            RoleListDto.Argument.class, sourceEid, -10000, ROLE_APP_ID);
    return paaSPermissionService.roleList(argument);
}
```

**特点**:
- 自动获取企业的真实角色数据
- 过滤已删除的角色
- API调用失败时自动降级到预设选项
- 使用反射机制避免强类型依赖

**外部角色集成**:
✅ **已完成**: 当前实现已经集成了真实的外部角色查询API：

```java
// 已集成LinkAppService
@Autowired
private LinkAppService linkAppService;

// 配置化的连接应用ID
private static String checkConnectedAppId;
static {
    ConfigFactory.getConfig("checkin-custom-config", config -> {
        checkConnectedAppId = config.get("checkConnectedAppId", "FSAID_11491079");
    });
}

// 调用真实的外部角色查询API
private List<?> listAllOutRole(String ea, int eid) {
    ListAllOutRoleArg arg = new ListAllOutRoleArg();
    arg.setLinkAppId(checkConnectedAppId);
    arg.setUpstreamEa(ea);

    HeaderObj headerObj = HeaderObj.newInstance(eid);
    headerObj.setAppId(checkConnectedAppId);

    RestResult<?> result = linkAppService.listAllOutRole(headerObj, arg);
    return result.isSuccess() ? (List<?>) result.getData() : Lists.newArrayList();
}
```

**特点**:
- 自动获取企业的真实外部角色数据
- 配置化的连接应用ID，支持不同环境
- API调用失败时自动降级到预设选项
- 使用反射机制处理角色对象，避免强类型依赖

### 2. 缓存机制

可以添加缓存来提高性能：

```java
@Cacheable(value = "roleOptions", key = "#tenantId + '_internal'")
public List<Map<String, Object>> getInternalRoleOptions(String tenantId) {
    // 实现逻辑
}
```

### 3. 权限控制

可以根据用户权限过滤可见的角色选项：

```java
public List<Map<String, Object>> getInternalRoleOptions(String tenantId, String userId) {
    // 根据用户权限过滤角色选项
}
```

## 部署说明

1. 将所有新增的Java文件部署到对应的目录
2. 确保Spring容器能够扫描到新增的Service类
3. 重启应用服务
4. 验证工资规则的列表页和详情页角色字段是否正常显示多选选项

## 注意事项

1. **数据兼容性**: 现有的array类型数据格式保持不变，确保向后兼容
2. **性能考虑**: 角色选项在页面加载时动态生成，对于大量角色的情况需要考虑分页或搜索功能
3. **权限安全**: 在生产环境中应该根据用户权限过滤可见的角色选项
4. **错误处理**: 当角色服务不可用时，系统仍能正常运行，只是角色选项为空

## 测试验证

运行测试类验证功能：
```bash
mvn test -Dtest=RoleQueryServiceImplTest -pl fs-crm-fmcg-wq-salary
```

在工资规则的新增/编辑页面验证：
1. 适用内部角色字段显示为多选下拉框
2. 适用外部角色字段显示为多选下拉框  
3. 选项内容符合预期
4. 数据保存和读取正常
