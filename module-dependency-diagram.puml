@startuml fs-crm-fmcg-wq模块依赖关系图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam packageStyle rectangle
skinparam componentStyle rectangle

title fs-crm-fmcg-wq 模块依赖关系图

package "核心框架层" {
  [PaaS应用框架] as PaasFramework
  [元数据服务] as MetadataService
  [权限管理] as PrivilegeService
  [消息队列] as MessageQueue
}

package "基础设施层" {
  [Redis缓存] as Redis
  [PostgreSQL] as PostgreSQL
  [MongoDB] as MongoDB
  [文件存储] as FileStorage
}

package "通用模块 (fs-crm-fmcg-wq-common)" {
  component "基础组件" {
    [BaseDao] as BaseDao
    [BaseDaoInterface] as BaseDaoInterface
    [AbstractDao] as AbstractDao
  }
  
  component "工具类" {
    [SearchQuery] as SearchQuery
    [RedisUtils] as RedisUtils
    [ConfigUtils] as ConfigUtils
    [DateUtils] as DateUtils
  }
  
  component "常量定义" {
    [CommonConstants] as CommonConstants
    [FieldConstants] as FieldConstants
  }
  
  component "服务类" {
    [PreRoleService] as PreRoleService
    [EmployeeDao] as EmployeeDao
  }
}

package "Web应用模块 (fs-crm-fmcg-wq-web)" {
  [Web应用入口] as WebEntry
  [Spring配置] as SpringConfig
  [Web控制器] as WebController
}

package "供货管理模块 (fs-crm-fmcg-wq-supply)" {
  component "控制器层" {
    [DealerSupplyController] as DealerSupplyController
    [DistributorSupplyController] as DistributorSupplyController
    [AreaManageController] as AreaManageController
    [CoveredStoresController] as CoveredStoresController
  }
  
  component "服务层" {
    [SupplyService] as SupplyService
    [SupplyServiceImpl] as SupplyServiceImpl
    [SyncAvailableRangeService] as SyncAvailableRangeService
    [SupplyApproService] as SupplyApproService
    [SalesAreaService] as SalesAreaService
    [ChannelService] as ChannelService
    [PMMService] as PMMService
  }
  
  component "数据访问层" {
    [SupplyDao] as SupplyDao
    [SupplyDaoInterface] as SupplyDaoInterface
    [PublicEmployeeDao] as PublicEmployeeDao
  }
  
  component "业务对象" {
    [MCPreDefineObject] as MCPreDefineObject
    [AreaManageConstants] as AreaManageConstants
    [SupplyConstants] as SupplyConstants
  }
  
  component "Action层" {
    [AreaManageAction] as AreaManageAction
    [PromoterAction] as PromoterAction
    [SupplyChangeAction] as SupplyChangeAction
  }
}

package "办公模块 (fs-crm-fmcg-wq-office)" {
  component "控制器层" {
    [UserScheduleController] as UserScheduleController
    [UserScheduleBatchController] as UserScheduleBatchController
    [UserScheduleImportController] as UserScheduleImportController
  }
  
  component "服务层" {
    [OfficeShiftService] as OfficeShiftService
    [OfficeShiftServiceImpl] as OfficeShiftServiceImpl
  }
  
  component "数据访问层" {
    [AccountShiftDao] as AccountShiftDao
    [UserScheduleDao] as UserScheduleDao
  }
  
  component "业务对象" {
    [CheckinsOfficePreDefineObject] as CheckinsOfficePreDefineObject
    [UserScheduleContent] as UserScheduleContent
  }
  
  component "Action层" {
    [UserScheduleAction] as UserScheduleAction
  }
}

package "薪资模块 (fs-crm-fmcg-wq-salary)" {
  component "API层" {
    [SalaryAPI] as SalaryAPI
  }
  
  component "配置层" {
    [SalaryConfig] as SalaryConfig
  }
  
  component "常量层" {
    [SalaryConstants] as SalaryConstants
  }
  
  component "工具层" {
    [SalaryUtils] as SalaryUtils
  }
  
  component "Action层" {
    [SalaryAction] as SalaryAction
  }
}

package "报表模块 (fs-crm-fmcg-wq-report)" {
  component "服务层" {
    [DistributionReportCollectService] as DistributionReportCollectService
    [DisplayDistrAchSummaryService] as DisplayDistrAchSummaryService
    [DisplayStandardService] as DisplayStandardService
  }
  
  component "数据访问层" {
    [DataReportStandardDao] as DataReportStandardDao
    [DataReportResultDao] as DataReportResultDao
  }
  
  component "业务对象" {
    [ReportDefaultObject] as ReportDefaultObject
    [DisplayReportCollect] as DisplayReportCollect
  }
}

package "签到模块 (fs-crm-fmcg-wq-checkins)" {
  component "业务对象" {
    [CheckinsDefaultObject] as CheckinsDefaultObject
  }
  
  component "数据访问层" {
    [CheckinsDao] as CheckinsDao
  }
  
  component "代理服务" {
    [CheckinsProxy] as CheckinsProxy
  }
}

package "全量模块 (fs-crm-fmcg-wq-all)" {
  [WQInitService] as WQInitService
  [ApplicationContext] as ApplicationContext
}

' 依赖关系定义
WebEntry --> SpringConfig
SpringConfig --> ApplicationContext
ApplicationContext --> WQInitService

WQInitService --> CheckinsDefaultObject
WQInitService --> MCPreDefineObject
WQInitService --> CheckinsOfficePreDefineObject
WQInitService --> ReportDefaultObject

' Web模块依赖
WebController --> SupplyService
WebController --> OfficeShiftService
WebController --> SalaryAPI

' 供货模块内部依赖
DealerSupplyController --> SupplyService
AreaManageController --> SupplyService
SupplyServiceImpl --> SupplyDao
SupplyServiceImpl --> SyncAvailableRangeService
SupplyServiceImpl --> SupplyApproService
SupplyDao --> SupplyDaoInterface
PMMService --> PublicEmployeeDao

' 办公模块内部依赖
UserScheduleController --> OfficeShiftService
OfficeShiftServiceImpl --> AccountShiftDao
OfficeShiftServiceImpl --> UserScheduleDao
OfficeShiftServiceImpl --> EmployeeDao

' 报表模块内部依赖
DistributionReportCollectService --> DataReportStandardDao
DisplayDistrAchSummaryService --> DataReportResultDao

' 通用模块依赖
SupplyDao --> BaseDao
AccountShiftDao --> AbstractDao
UserScheduleDao --> AbstractDao
DataReportStandardDao --> BaseDao

BaseDao --> BaseDaoInterface
AbstractDao --> BaseDaoInterface

' 工具类依赖
SupplyService --> SearchQuery
OfficeShiftService --> SearchQuery
SupplyService --> RedisUtils
BaseDao --> ConfigUtils

' 外部框架依赖
BaseDaoInterface --> MetadataService
SupplyService --> PaasFramework
OfficeShiftService --> PaasFramework
SupplyApproService --> MessageQueue

BaseDao --> PostgreSQL
BaseDao --> MongoDB
RedisUtils --> Redis

' 常量依赖
SupplyService --> SupplyConstants
AreaManageController --> AreaManageConstants
OfficeShiftService --> CommonConstants

note top of WQInitService
  **系统初始化入口**
  负责注册所有预定义对象
  启动各模块服务
end note

note right of BaseDao
  **通用数据访问基类**
  提供标准CRUD操作
  支持多数据源
end note

note bottom of SupplyService
  **核心业务服务**
  供货关系管理
  业务规则验证
end note

note bottom of OfficeShiftService
  **办公排班服务**
  用户排班管理
  冲突检测验证
end note

@enduml
