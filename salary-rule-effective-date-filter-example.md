# 工资规则生效时间过滤功能说明

## 功能概述

在`getSalaryRulesForTenant`方法中增加了按照生效时间过滤的逻辑，只返回生效时间小于当日的工资规则，确保只处理已经生效的规则。

## 主要修改

### 1. 修改的方法

- `SalaryRuleDao.queryRulesByDate()` - 查询应该生成工资发放单的规则
- `SalaryRuleDao.queryRulesByDateForDetail()` - 查询应该生成工资条明细的规则

### 2. 新增的方法

- `SalaryRuleDao.filterByEffectiveDate()` - 根据生效时间过滤工资规则

## 实现逻辑

### 过滤条件

- **生效时间字段**：`EFFECTIVE_DATE`
- **过滤规则**：生效时间 < 当日00:00:00
- **处理逻辑**：只返回已经生效的工资规则

### 时间比较逻辑

```java
// 获取当日的开始时间（00:00:00）
Calendar calendar = Calendar.getInstance();
calendar.setTime(currentDate);
calendar.set(Calendar.HOUR_OF_DAY, 0);
calendar.set(Calendar.MINUTE, 0);
calendar.set(Calendar.SECOND, 0);
calendar.set(Calendar.MILLISECOND, 0);
long currentDayStart = calendar.getTimeInMillis();

// 判断生效时间是否小于当日
if (effectiveDate < currentDayStart) {
    // 包含在结果中
    filteredRules.add(salaryRule);
}
```

## 示例场景

### 场景1：当日为2025-01-15

| 工资规则 | 生效时间 | 是否包含 | 说明 |
|----------|----------|----------|------|
| 规则A | 2025-01-14 23:59:59 | ✅ 是 | 生效时间小于当日 |
| 规则B | 2025-01-15 00:00:00 | ❌ 否 | 生效时间等于当日 |
| 规则C | 2025-01-15 10:30:00 | ❌ 否 | 生效时间大于当日开始 |
| 规则D | 2025-01-10 08:00:00 | ✅ 是 | 生效时间小于当日 |

### 场景2：当日为2025-02-01

| 工资规则 | 生效时间 | 是否包含 | 说明 |
|----------|----------|----------|------|
| 月薪规则1 | 2025-01-31 23:59:59 | ✅ 是 | 上月最后一天生效 |
| 月薪规则2 | 2025-02-01 00:00:00 | ❌ 否 | 当日生效，不包含 |
| 日薪规则 | 2025-01-15 00:00:00 | ✅ 是 | 半月前生效 |

## 代码实现

### 1. queryRulesByDate方法修改

```java
public List<IObjectData> queryRulesByDate(String tenantId, Date date) {
    // ... 原有查询逻辑 ...
    
    // 过滤生效时间：只返回生效时间小于当日的规则
    return filterByEffectiveDate(result, date);
}
```

### 2. queryRulesByDateForDetail方法修改

```java
public List<IObjectData> queryRulesByDateForDetail(String tenantId, Date date) {
    // ... 原有查询逻辑 ...
    
    // 过滤生效时间：只返回生效时间小于当日的规则
    return filterByEffectiveDate(result, date);
}
```

### 3. filterByEffectiveDate方法实现

```java
private List<IObjectData> filterByEffectiveDate(List<IObjectData> salaryRules, Date currentDate) {
    if (CollectionUtils.isEmpty(salaryRules) || currentDate == null) {
        return salaryRules;
    }

    List<IObjectData> filteredRules = new ArrayList<>();
    
    // 获取当日的开始时间（00:00:00）
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(currentDate);
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    long currentDayStart = calendar.getTimeInMillis();

    for (IObjectData salaryRule : salaryRules) {
        try {
            // 获取生效时间
            Object effectiveDateObj = salaryRule.get(SalaryRuleFields.EFFECTIVE_DATE);
            
            if (effectiveDateObj == null) {
                // 如果没有生效时间，跳过该规则
                log.warn("工资规则 {} 没有设置生效时间，跳过处理", salaryRule.getId());
                continue;
            }

            long effectiveDate;
            if (effectiveDateObj instanceof Date) {
                effectiveDate = ((Date) effectiveDateObj).getTime();
            } else if (effectiveDateObj instanceof Long) {
                effectiveDate = (Long) effectiveDateObj;
            } else {
                log.warn("工资规则 {} 的生效时间格式不正确: {}, 跳过处理", 
                        salaryRule.getId(), effectiveDateObj);
                continue;
            }

            // 判断生效时间是否小于当日
            if (effectiveDate < currentDayStart) {
                filteredRules.add(salaryRule);
                log.debug("工资规则 {} 生效时间 {} 小于当日 {}，包含在结果中", 
                        salaryRule.getId(), new Date(effectiveDate), new Date(currentDayStart));
            } else {
                log.debug("工资规则 {} 生效时间 {} 不小于当日 {}，过滤掉", 
                        salaryRule.getId(), new Date(effectiveDate), new Date(currentDayStart));
            }

        } catch (Exception e) {
            log.error("处理工资规则 {} 的生效时间时发生异常，跳过该规则", salaryRule.getId(), e);
        }
    }

    log.info("生效时间过滤完成，原始规则数量: {}, 过滤后规则数量: {}", 
            salaryRules.size(), filteredRules.size());
    return filteredRules;
}
```

## 异常处理

### 1. 空值处理
- 如果工资规则列表为空，直接返回原列表
- 如果当前日期为空，直接返回原列表
- 如果工资规则没有生效时间，跳过该规则并记录警告日志

### 2. 数据类型处理
- 支持`Date`类型的生效时间
- 支持`Long`类型的时间戳
- 对于其他类型，记录警告日志并跳过

### 3. 异常捕获
- 对每个工资规则的处理都进行异常捕获
- 异常情况下跳过该规则，不影响其他规则的处理
- 记录详细的错误日志便于排查

## 日志记录

### 调试日志
```
工资规则 rule123 生效时间 2025-01-14 23:59:59 小于当日 2025-01-15 00:00:00，包含在结果中
工资规则 rule456 生效时间 2025-01-15 10:30:00 不小于当日 2025-01-15 00:00:00，过滤掉
```

### 信息日志
```
生效时间过滤完成，原始规则数量: 5, 过滤后规则数量: 3
```

### 警告日志
```
工资规则 rule789 没有设置生效时间，跳过处理
工资规则 rule101 的生效时间格式不正确: invalid_date, 跳过处理
```

### 错误日志
```
处理工资规则 rule202 的生效时间时发生异常，跳过该规则
```

## 影响范围

### 1. 直接影响
- `PMMSalaryProvider.getSalaryRulesForTenant()` - 定时任务获取工资规则
- 工资条明细生成逻辑
- 工资发放单生成逻辑

### 2. 间接影响
- 薪资计算的准确性提升
- 避免处理未生效的工资规则
- 提高系统的业务逻辑正确性

## 业务价值

### 1. 准确性保障
- 确保只处理已经生效的工资规则
- 避免提前计算未生效规则的薪资
- 保证薪资计算的时间准确性

### 2. 业务合规
- 符合薪资管理的业务规则
- 支持工资规则的分阶段生效
- 提供更灵活的薪资管理能力

### 3. 系统稳定性
- 减少因未生效规则导致的计算错误
- 提供清晰的过滤逻辑和日志记录
- 增强系统的可维护性

## 测试建议

### 1. 单元测试
- 测试不同生效时间的工资规则过滤
- 测试边界条件（当日00:00:00）
- 测试异常情况处理

### 2. 集成测试
- 测试定时任务的完整流程
- 验证过滤后的规则是否正确
- 检查日志记录是否完整

### 3. 业务测试
- 创建不同生效时间的工资规则
- 验证薪资计算结果的正确性
- 确认未生效规则不会被处理

## 注意事项

### 1. 时区处理
- 当前实现使用系统默认时区
- 如需支持多时区，需要额外处理

### 2. 性能考虑
- 过滤逻辑在内存中执行，性能影响较小
- 对于大量工资规则，可考虑在数据库层面过滤

### 3. 向后兼容
- 对于没有生效时间的历史规则，会被跳过
- 建议为历史规则补充生效时间字段

## 扩展建议

### 1. 数据库层面过滤
可以考虑在查询时直接添加生效时间条件：
```java
SearchQuery query = SearchQuery.builder()
    .lt(SalaryRuleFields.EFFECTIVE_DATE, currentDayStart)
    .build();
```

### 2. 配置化过滤规则
可以将过滤逻辑配置化，支持不同的过滤策略：
- 小于当日
- 小于等于当日
- 自定义时间偏移

### 3. 缓存优化
对于频繁查询的工资规则，可以考虑添加缓存机制，提高查询性能。
