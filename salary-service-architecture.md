# 工资服务核心方法架构文档

## 概述

本文档描述了 `SalaryServiceImpl` 中两个核心工资计算方法的逻辑架构和调用关系：
- `generateSalaryData` - 生成单个员工工资条
- `recalculateSalaryDataForPaymentSlip` - 重新计算工资发放单下的所有工资条

## 方法架构图

```mermaid
graph TD
    A[generateSalaryData] --> B[查询已存在工资明细]
    A --> C[检查异常明细]
    A --> D[创建/获取工资条]
    
    C --> E{有异常明细?}
    E -->|是| F[recalculateSalaryDetails 公共方法]
    E -->|否| G[计算工资条总金额]
    
    F --> H[更新hasErrorDetails状态]
    H --> G
    
    G --> I[设置工资条状态]
    I --> J[保存工资条]
    
    K[recalculateSalaryDataForPaymentSlip] --> L[查询工资发放单下所有工资条]
    K --> M[创建缓存Map]
    
    L --> N[遍历每个工资条]
    N --> O[查询工资条明细]
    N --> P[recalculateSalaryDetails 公共方法]
    
    P --> Q[更新工资条金额和状态]
    Q --> R[检查工资条异常状态]
    R --> S[更新全局异常标记]
    
    S --> T[重新检查整体状态]
    T --> U[更新工资发放单状态]
    
    F --> V[recalculateSalaryDetails]
    P --> V
    
    V --> W[处理已修正明细]
    V --> X[重新计算异常明细]
    V --> Y[调用calculateSalaryItemAmount]
    V --> Z[返回总金额和异常状态]
```

## 核心方法详细架构

### 1. generateSalaryData 方法

#### 1.1 方法签名
```java
public IObjectData generateSalaryData(IObjectData employeeFixedSalaryObj, 
                                     IObjectData salaryRuleObj, 
                                     long startTime, 
                                     long endTime, 
                                     boolean recalc)
```

#### 1.2 执行流程

```mermaid
sequenceDiagram
    participant Client as 调用方
    participant GSD as generateSalaryData
    participant Cache as 缓存管理
    participant RSD as recalculateSalaryDetails
    participant DB as 数据库
    
    Client->>GSD: 调用生成工资条
    GSD->>DB: 查询已存在工资明细
    GSD->>GSD: 检查异常明细状态
    
    alt 有异常明细
        GSD->>Cache: 创建工资项缓存
        GSD->>Cache: 创建固定工资明细缓存
        GSD->>RSD: 重新计算异常明细
        RSD->>DB: 更新明细数据
        RSD-->>GSD: 返回总金额和异常状态
        GSD->>GSD: 更新hasErrorDetails
    end
    
    GSD->>DB: 创建或获取工资条
    GSD->>GSD: 计算工资条总金额
    GSD->>GSD: 设置工资条状态
    GSD->>DB: 保存工资条
    GSD-->>Client: 返回工资条对象
```

#### 1.3 关键逻辑点

1. **异常明细检测**
   ```java
   // 检查已存在明细的异常状态
   for (IObjectData detailData : salaryDetailDatas) {
       String distributionStatus = detailData.get(SalaryDetailDataFields.DISTRIBUTION_STATUS, String.class);
       if (SalaryDetailDataFields.PAY_STATUS_Options_ERROR.equals(distributionStatus)) {
           hasErrorDetails = true;
           errorDetailsList.add(detailData);
       }
   }
   ```

2. **异常明细重新计算**
   ```java
   if (hasErrorDetails) {
       // 使用公共方法重新计算异常明细
       Pair<BigDecimal, Boolean> recalculationResult = recalculateSalaryDetails(
           tenantId, errorDetailsList, employeeFixedSalaryObj.getId(), 
           startTime, endTime, salaryItemCache, fixedSalaryDetailCache);
       hasErrorDetails = recalculationResult.getValue();
   }
   ```

3. **工资条状态设置**
   ```java
   if (!isSalaryDataCorrected) {
       if (hasErrorDetails) {
           salaryData.set(SalaryDataFields.PAY_STATUS, SalaryDataFields.PAY_STATUS_Options_ERROR);
       } else {
           salaryData.set(SalaryDataFields.PAY_STATUS, SalaryDataFields.PAY_STATUS_Options_1);
       }
   }
   ```

### 2. recalculateSalaryDataForPaymentSlip 方法

#### 2.1 方法签名
```java
public int recalculateSalaryDataForPaymentSlip(String tenantId, String salaryPaymentSlipId)
```

#### 2.2 执行流程

```mermaid
sequenceDiagram
    participant Client as 调用方
    participant RSDP as recalculateSalaryDataForPaymentSlip
    participant Cache as 缓存管理
    participant RSD as recalculateSalaryDetails
    participant DB as 数据库
    
    Client->>RSDP: 调用重新计算工资发放单
    RSDP->>DB: 查询工资发放单下所有工资条
    RSDP->>Cache: 创建工资项缓存
    RSDP->>Cache: 创建固定工资明细缓存
    
    loop 遍历每个工资条
        RSDP->>DB: 查询工资条明细
        RSDP->>RSD: 重新计算明细
        RSD->>DB: 更新明细数据
        RSD-->>RSDP: 返回总金额和异常状态
        RSDP->>DB: 更新工资条金额和状态
        RSDP->>RSDP: 更新全局异常标记
    end
    
    RSDP->>RSDP: 重新检查整体异常状态
    RSDP->>DB: 更新工资发放单状态
    RSDP-->>Client: 返回更新的工资条数量
```

#### 2.3 关键逻辑点

1. **批量处理工资条**
   ```java
   for (IObjectData salaryData : salaryDataList) {
       List<IObjectData> detailDataList = salaryDetailDataDao.getBySalaryDataId(tenantId, salaryDataId);
       
       // 使用公共方法重新计算明细金额
       Pair<BigDecimal, Boolean> recalculationResult = recalculateSalaryDetails(
           tenantId, detailDataList, employeeFixedSalaryId, 
           startDate, endDate, salaryItemCache, fixedSalaryDetailCache);
   }
   ```

2. **全局异常状态管理**
   ```java
   // 重新检查整个工资发放单的异常状态
   hasErrorDetails = false;
   for (IObjectData salaryData : salaryDataList) {
       String payStatus = salaryData.get(SalaryDataFields.PAY_STATUS, String.class);
       if (SalaryDataFields.PAY_STATUS_Options_ERROR.equals(payStatus)) {
           hasErrorDetails = true;
           break;
       }
   }
   ```

3. **工资发放单状态更新**
   ```java
   if (hasErrorDetails) {
       updateSalaryPaymentSlipStatus(tenantId, salaryPaymentSlipId, 
           SalaryPaymentSlipFields.PAY_STATUS_Options_ERROR, null);
   } else {
       // 恢复为正常状态
   }
   ```

## 公共方法 recalculateSalaryDetails

### 3.1 方法签名
```java
private Pair<BigDecimal, Boolean> recalculateSalaryDetails(String tenantId,
        List<IObjectData> detailDataList, 
        String employeeFixedSalaryId,
        long startTime, 
        long endTime,
        Map<String, IObjectData> salaryItemCache,
        Map<String, List<IObjectData>> fixedSalaryDetailCache)
```

### 3.2 核心逻辑架构

```mermaid
flowchart TD
    A[recalculateSalaryDetails] --> B[初始化总金额和异常标记]
    B --> C[遍历工资明细列表]
    
    C --> D{明细状态检查}
    D -->|已修正| E[保持原值不变]
    D -->|需要计算| F[获取工资项信息]
    
    E --> G[累加到总金额]
    F --> H[从缓存获取工资项]
    H --> I[构建薪资计算上下文]
    I --> J[调用calculateSalaryItemAmount]
    
    J --> K{计算是否成功?}
    K -->|成功| L[更新明细金额和状态]
    K -->|失败| M[设置异常状态]
    
    L --> N[根据增减属性累加金额]
    M --> O[标记hasErrorDetails=true]
    
    N --> P[保存明细到数据库]
    O --> P
    P --> Q[继续下一个明细]
    
    Q --> R{还有明细?}
    R -->|是| C
    R -->|否| S[返回总金额和异常状态]

### 3.3 处理逻辑详解

#### 已修正明细处理
```java
if (SalaryDetailDataFields.DISTRIBUTION_STATUS_Options_4.equals(currentStatus)) {
    // 已修正状态，保持原值不变，直接累加到总金额
    String amountStr = detailData.get(SalaryDetailDataFields.AMOUNT, String.class);
    String incrementDecrementAttrib = detailData.get(SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB, String.class);

    if (StringUtils.isNotBlank(amountStr)) {
        BigDecimal amount = new BigDecimal(amountStr);
        // 根据增减属性判断是加还是减
        if (SalaryDetailDataFields.INCREMENT_DECREMENT_ATTRIB_Options_2.equals(incrementDecrementAttrib)) {
            totalAmount = totalAmount.subtract(amount);
        } else {
            totalAmount = totalAmount.add(amount);
        }
    }
    continue;
}
```

#### 工资项计算处理
```java
// 从缓存中获取工资项信息
IObjectData salaryItem = salaryItemCache.get(salaryItemId);
if (salaryItem == null) {
    salaryItem = salaryItemDao.getById(tenantId, salaryItemId);
    if (salaryItem != null) {
        salaryItemCache.put(salaryItemId, salaryItem);
    }
}

// 调用核心计算方法
Pair<String, BigDecimal> calculationResult = calculateSalaryItemAmount(tenantId, salaryItem,
        salaryContext, null, employeeFixedSalaryId, fixedSalaryDetailCache);
```

## 缓存机制架构

### 4.1 工资项缓存
```java
Map<String, IObjectData> salaryItemCache = new HashMap<>();
```
- **缓存键**: 工资项ID
- **缓存值**: 工资项对象
- **生命周期**: 方法执行期间
- **作用**: 避免重复查询相同工资项

### 4.2 固定工资明细缓存
```java
Map<String, List<IObjectData>> fixedSalaryDetailCache = new HashMap<>();
```
- **缓存键**: 员工固定薪资ID
- **缓存值**: 该员工的所有固定工资明细列表
- **生命周期**: 方法执行期间
- **作用**: 避免重复查询相同员工的固定工资明细

### 4.3 缓存使用流程

```mermaid
sequenceDiagram
    participant Method as 计算方法
    participant Cache as 缓存
    participant DB as 数据库

    Method->>Cache: 查找工资项缓存
    alt 缓存命中
        Cache-->>Method: 返回缓存的工资项
    else 缓存未命中
        Method->>DB: 查询工资项
        DB-->>Method: 返回工资项数据
        Method->>Cache: 将工资项放入缓存
    end

    Method->>Cache: 查找固定工资明细缓存
    alt 缓存命中
        Cache-->>Method: 返回缓存的明细列表
    else 缓存未命中
        Method->>DB: 查询固定工资明细
        DB-->>Method: 返回明细数据
        Method->>Cache: 将明细列表放入缓存
    end
```

## 状态管理架构

### 5.1 工资明细状态
- `DISTRIBUTION_STATUS_Options_0`: 未发放（正常状态）
- `DISTRIBUTION_STATUS_Options_4`: 已修正
- `PAY_STATUS_Options_ERROR`: 生成异常

### 5.2 工资条状态
- `PAY_STATUS_Options_1`: 正常
- `PAY_STATUS_Options_ERROR`: 生成异常

### 5.3 工资发放单状态
- 正常状态: 所有工资条都正常
- 异常状态: 至少有一个工资条异常

### 5.4 状态传播机制

```mermaid
graph TD
    A[工资明细状态] --> B[工资条状态]
    B --> C[工资发放单状态]

    A1[明细计算失败] --> A2[设置明细为异常状态]
    A2 --> B1[工资条包含异常明细]
    B1 --> B2[设置工资条为异常状态]
    B2 --> C1[工资发放单包含异常工资条]
    C1 --> C2[设置工资发放单为异常状态]

    A3[明细计算成功] --> A4[设置明细为正常状态]
    A4 --> B3[工资条所有明细正常]
    B3 --> B4[设置工资条为正常状态]
    B4 --> C3[工资发放单所有工资条正常]
    C3 --> C4[设置工资发放单为正常状态]
```

## 异常处理架构

### 6.1 异常类型
1. **计算异常**: 公式计算失败、数据类型错误等
2. **数据异常**: 工资项不存在、员工信息缺失等
3. **系统异常**: 数据库连接失败、网络异常等

### 6.2 异常处理策略

```mermaid
flowchart TD
    A[异常发生] --> B{异常类型判断}

    B -->|计算异常| C[设置明细为异常状态]
    B -->|数据异常| D[记录警告日志，跳过处理]
    B -->|系统异常| E[记录错误日志，抛出异常]

    C --> F[继续处理下一个明细]
    D --> F
    E --> G[方法执行失败]

    F --> H[统计异常明细数量]
    H --> I[设置相应的状态标记]
```

### 6.3 异常恢复机制
```java
// 如果状态为生成异常，强制重新计算
boolean forceRecalculate = SalaryDetailDataFields.PAY_STATUS_Options_ERROR.equals(currentStatus);
if (forceRecalculate) {
    log.info("发现生成异常的工资明细，强制重新计算，明细ID: {}", detailData.getId());
}
```

## 性能优化架构

### 7.1 缓存优化
- **工资项缓存**: 减少重复查询工资项数据
- **固定工资明细缓存**: 减少重复查询员工固定工资明细
- **批量处理**: 一次性处理多个明细，减少数据库交互次数

### 7.2 查询优化
- **按需查询**: 只查询需要的数据
- **避免重复查询**: 使用缓存机制
- **批量更新**: 批量更新明细数据

### 7.3 性能监控点
```java
// 关键性能指标
- 工资项缓存命中率
- 固定工资明细缓存命中率
- 数据库查询次数
- 方法执行时间
- 异常明细重新计算成功率
```

## 数据流架构

### 8.1 输入数据流
```
员工固定薪资对象 → 工资规则对象 → 时间范围 → 重新计算标记
                ↓
        查询已存在工资明细数据
                ↓
        检查异常明细并分类处理
```

### 8.2 处理数据流
```
异常明细列表 → 缓存机制 → 核心计算方法 → 状态更新 → 数据库保存
                ↓
        总金额计算 → 工资条状态设置 → 工资条保存
```

### 8.3 输出数据流
```
工资条对象 ← 状态信息 ← 总金额信息 ← 明细计算结果
```

## 调用关系总结

### 9.1 方法调用层次
```
generateSalaryData
├── recalculateSalaryDetails (公共方法)
│   ├── calculateSalaryItemAmount
│   └── salaryDetailDataDao.update
└── salaryDataDao.save

recalculateSalaryDataForPaymentSlip
├── recalculateSalaryDetails (公共方法)
│   ├── calculateSalaryItemAmount
│   └── salaryDetailDataDao.update
└── updateSalaryPaymentSlipStatus
```

### 9.2 共享组件
- `recalculateSalaryDetails`: 核心计算逻辑
- `calculateSalaryItemAmount`: 工资项金额计算
- 缓存机制: 工资项缓存和固定工资明细缓存
- 状态管理: 异常状态检查和更新逻辑

### 9.3 差异化处理
- **数据查询方式**: 按员工 vs 按工资发放单
- **处理粒度**: 单个工资条 vs 批量工资条
- **返回值**: 工资条对象 vs 更新数量
- **状态更新范围**: 工资条 vs 工资发放单
```
